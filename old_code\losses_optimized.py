"""
優化的損失函數模組
提供現代深度學習中常用的損失函數和組合策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Optional, Dict, List, Union, Tuple
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class BaseLoss(nn.Module, ABC):
    """損失函數基類"""
    
    def __init__(self, weight: float = 1.0, reduction: str = 'mean'):
        super().__init__()
        self.weight = weight
        self.reduction = reduction
    
    @abstractmethod
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        pass
    
    def get_loss_components(self, inputs: torch.Tensor, targets: torch.Tensor) -> Dict[str, float]:
        """獲取損失組件（安全實現）"""
        try:
            loss = self.forward(inputs, targets)
            return {self.__class__.__name__: loss.item()}
        except Exception as e:
            logger.warning(f"無法計算損失組件 {self.__class__.__name__}: {e}")
            return {self.__class__.__name__: 0.0}


class DiceLoss(BaseLoss):
    """
    改進的Dice損失函數
    支援多類別分割和不同的平滑策略
    """
    
    def __init__(self, 
                 smooth: float = 1.0, 
                 p: int = 2,
                 ignore_index: Optional[int] = None,
                 multiclass: bool = True,
                 weight: Optional[torch.Tensor] = None,
                 reduction: str = 'mean'):
        """
        Args:
            smooth: 平滑因子，避免除以零
            p: Lp範數的p值
            ignore_index: 忽略的類別索引
            multiclass: 是否為多類別問題
            weight: 類別權重
            reduction: 減少方式 ('mean', 'sum', 'none')
        """
        super().__init__(reduction=reduction)
        self.smooth = smooth
        self.p = p
        self.ignore_index = ignore_index
        self.multiclass = multiclass
        self.register_buffer('weight', weight)
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        計算Dice損失
        
        Args:
            inputs: 預測結果 [N, C, H, W] 或 [N, H, W]
            targets: 目標標籤 [N, H, W]
            
        Returns:
            Dice損失值
        """
        if self.multiclass and inputs.dim() == 4:
            return self._multiclass_dice_loss(inputs, targets)
        else:
            return self._binary_dice_loss(inputs, targets)
    
    def _multiclass_dice_loss(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """多類別Dice損失"""
        n_classes = inputs.size(1)
        
        # 將預測轉換為概率
        if inputs.dim() == 4:
            probs = F.softmax(inputs, dim=1)
        else:
            probs = inputs
        
        # 將targets轉換為one-hot編碼
        targets_one_hot = F.one_hot(targets.long(), n_classes).permute(0, 3, 1, 2).float()
        
        # 處理ignore_index
        if self.ignore_index is not None:
            mask = (targets != self.ignore_index).float()
            targets_one_hot = targets_one_hot * mask.unsqueeze(1)
            probs = probs * mask.unsqueeze(1)
        
        # 計算每個類別的Dice係數
        dice_scores = []
        for c in range(n_classes):
            if self.ignore_index is not None and c == self.ignore_index:
                continue
                
            pred_c = probs[:, c]
            target_c = targets_one_hot[:, c]
            
            intersection = (pred_c * target_c).sum(dim=(1, 2))
            union = pred_c.pow(self.p).sum(dim=(1, 2)) + target_c.pow(self.p).sum(dim=(1, 2))
            
            dice = (2 * intersection + self.smooth) / (union + self.smooth)
            dice_scores.append(dice)
        
        dice_scores = torch.stack(dice_scores, dim=1)
        
        # 應用類別權重
        if self.weight is not None:
            weight = self.weight[:dice_scores.size(1)]
            dice_scores = dice_scores * weight.unsqueeze(0)
        
        # 計算平均Dice損失
        if self.reduction == 'mean':
            return 1 - dice_scores.mean()
        elif self.reduction == 'sum':
            return (1 - dice_scores).sum()
        else:
            return 1 - dice_scores
    
    def _binary_dice_loss(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """二值Dice損失"""
        # 確保輸入是正確的形狀
        if inputs.dim() == 4:
            inputs = inputs.squeeze(1)
        if targets.dim() == 4:
            targets = targets.squeeze(1)
        
        # 將預測轉換為概率
        if inputs.dtype != torch.float32:
            inputs = inputs.float()
        if targets.dtype != torch.float32:
            targets = targets.float()
        
        # 展平張量
        inputs_flat = inputs.view(-1)
        targets_flat = targets.view(-1)
        
        # 計算Dice係數
        intersection = (inputs_flat * targets_flat).sum()
        union = inputs_flat.pow(self.p).sum() + targets_flat.pow(self.p).sum()
        
        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        
        return 1 - dice


class FocalLoss(BaseLoss):
    """
    改進的Focal Loss
    用於處理類別不平衡問題
    """
    
    def __init__(self, 
                 alpha: Union[float, torch.Tensor] = 0.25,
                 gamma: float = 2.0,
                 ignore_index: Optional[int] = None,
                 reduction: str = 'mean'):
        """
        Args:
            alpha: 平衡因子
            gamma: 調節因子
            ignore_index: 忽略的類別索引
            reduction: 減少方式
        """
        super().__init__(reduction=reduction)
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
        
        if isinstance(alpha, torch.Tensor):
            self.register_buffer('alpha_tensor', alpha)
        else:
            self.alpha_tensor = None
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        計算Focal Loss
        
        Args:
            inputs: 預測結果 [N, C, H, W]
            targets: 目標標籤 [N, H, W]
            
        Returns:
            Focal Loss值
        """
        # 調整輸入大小
        if inputs.size()[2:] != targets.size()[1:]:
            inputs = F.interpolate(inputs, size=targets.size()[1:], mode='bilinear', align_corners=True)
        
        # 重塑張量
        n, c, h, w = inputs.size()
        inputs = inputs.transpose(1, 2).transpose(2, 3).contiguous().view(-1, c)
        targets = targets.view(-1).long()
        
        # 創建忽略掩碼
        if self.ignore_index is not None:
            valid_mask = targets != self.ignore_index
            inputs = inputs[valid_mask]
            targets = targets[valid_mask]
        
        # 計算交叉熵
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        
        # 計算alpha權重
        if self.alpha_tensor is not None:
            alpha_t = self.alpha_tensor[targets]
        else:
            alpha_t = self.alpha
        
        # 計算Focal Loss
        focal_loss = alpha_t * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class BoundaryAwareLoss(BaseLoss):
    """
    邊界感知損失函數
    增強對物體邊界的關注
    """
    
    def __init__(self, 
                 boundary_weight: float = 1.0,
                 kernel_size: int = 3,
                 reduction: str = 'mean'):
        """
        Args:
            boundary_weight: 邊界損失權重
            kernel_size: 邊界檢測核大小
            reduction: 減少方式
        """
        super().__init__(reduction=reduction)
        self.boundary_weight = boundary_weight
        self.kernel_size = kernel_size
        
        # 創建邊界檢測核
        if kernel_size == 3:
            kernel = torch.tensor([
                [0, 1, 0],
                [1, -4, 1],
                [0, 1, 0]
            ], dtype=torch.float32).reshape(1, 1, 3, 3)
        else:
            # 創建更大的Laplacian核
            kernel = self._create_laplacian_kernel(kernel_size)
        
        self.register_buffer('boundary_kernel', kernel)
    
    def _create_laplacian_kernel(self, size: int) -> torch.Tensor:
        """創建Laplacian邊界檢測核"""
        kernel = torch.zeros(size, size)
        center = size // 2
        kernel[center, center] = -(size * size - 1)
        
        for i in range(size):
            for j in range(size):
                if i != center or j != center:
                    kernel[i, j] = 1
        
        return kernel.reshape(1, 1, size, size).float()
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        計算邊界感知損失
        
        Args:
            inputs: 預測結果 [N, C, H, W]
            targets: 目標標籤 [N, H, W]
            
        Returns:
            邊界感知損失值
        """
        # 獲取預測類別
        if inputs.dim() == 4 and inputs.size(1) > 1:
            _, preds = torch.max(inputs, dim=1)
        else:
            preds = inputs.squeeze(1) if inputs.dim() == 4 else inputs
        
        # 轉換為float並添加通道維度
        preds = preds.float().unsqueeze(1)
        targets = targets.float().unsqueeze(1)
        
        # 確保核在正確的設備上
        if self.boundary_kernel.device != inputs.device:
            self.boundary_kernel = self.boundary_kernel.to(inputs.device)
        
        # 計算邊界
        padding = self.kernel_size // 2
        pred_boundaries = F.conv2d(preds, self.boundary_kernel, padding=padding)
        target_boundaries = F.conv2d(targets, self.boundary_kernel, padding=padding)
        
        # 二值化邊界
        pred_boundaries = (torch.abs(pred_boundaries) > 0.1).float()
        target_boundaries = (torch.abs(target_boundaries) > 0.1).float()
        
        # 計算邊界Dice損失
        intersection = (pred_boundaries * target_boundaries).sum()
        union = pred_boundaries.sum() + target_boundaries.sum()
        
        if union == 0:
            return torch.tensor(0.0, device=inputs.device, requires_grad=True)
        
        dice = (2 * intersection + 1) / (union + 1)
        boundary_loss = 1 - dice
        
        return self.boundary_weight * boundary_loss


class IoULoss(BaseLoss):
    """Intersection over Union損失函數"""
    
    def __init__(self, smooth: float = 1.0, reduction: str = 'mean'):
        super().__init__(reduction=reduction)
        self.smooth = smooth
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        # 處理多類別情況
        if inputs.dim() == 4 and inputs.size(1) > 1:
            inputs = F.softmax(inputs, dim=1)
            targets_one_hot = F.one_hot(targets.long(), inputs.size(1)).permute(0, 3, 1, 2).float()
            
            intersection = (inputs * targets_one_hot).sum(dim=(2, 3))
            union = inputs.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3)) - intersection
            
            iou = (intersection + self.smooth) / (union + self.smooth)
            return 1 - iou.mean()
        else:
            # 二值情況
            inputs = torch.sigmoid(inputs) if inputs.min() < 0 else inputs
            intersection = (inputs * targets).sum()
            union = inputs.sum() + targets.sum() - intersection
            
            iou = (intersection + self.smooth) / (union + self.smooth)
            return 1 - iou


class CombinedLoss(nn.Module):
    """
    組合損失函數框架
    支援多種損失函數的加權組合
    """
    
    def __init__(self, 
                 losses: Dict[str, nn.Module], 
                 weights: Dict[str, float],
                 adaptive_weights: bool = False):
        """
        Args:
            losses: 損失函數字典
            weights: 各損失函數權重
            adaptive_weights: 是否使用自適應權重
        """
        super().__init__()
        self.losses = nn.ModuleDict(losses)
        self.weights = weights
        self.adaptive_weights = adaptive_weights
        
        if adaptive_weights:
            # 初始化自適應權重參數
            self.weight_params = nn.ParameterDict({
                name: nn.Parameter(torch.tensor(weight, dtype=torch.float32))
                for name, weight in weights.items()
            })
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """計算組合損失"""
        total_loss = 0
        
        for name, loss_fn in self.losses.items():
            try:
                loss_value = loss_fn(inputs, targets)
                
                if self.adaptive_weights:
                    weight = torch.sigmoid(self.weight_params[name])
                else:
                    weight = self.weights[name]
                
                weighted_loss = weight * loss_value
                total_loss += weighted_loss
                
            except Exception as e:
                logger.warning(f"計算損失 {name} 時發生錯誤: {e}")
                continue
        
        return total_loss
    
    def get_loss_components(self, inputs: torch.Tensor, targets: torch.Tensor) -> Dict[str, float]:
        """獲取各組件損失值"""
        components = {}
        
        for name, loss_fn in self.losses.items():
            try:
                loss_value = loss_fn(inputs, targets)
                
                if self.adaptive_weights:
                    weight = torch.sigmoid(self.weight_params[name])
                else:
                    weight = self.weights[name]
                
                components[f'{name}_loss'] = loss_value.item()
                components[f'{name}_weighted'] = (weight * loss_value).item()
                
            except Exception as e:
                logger.warning(f"計算損失組件 {name} 時發生錯誤: {e}")
                components[f'{name}_loss'] = 0.0
                components[f'{name}_weighted'] = 0.0
        
        return components


class StagedLoss(nn.Module):
    """
    分階段損失函數
    根據訓練進度調整損失策略
    """
    
    def __init__(self, 
                 loss_configs: List[Dict],
                 stage_epochs: List[int]):
        """
        Args:
            loss_configs: 每個階段的損失配置
            stage_epochs: 各階段的epoch邊界
        """
        super().__init__()
        self.loss_configs = loss_configs
        self.stage_epochs = stage_epochs
        self.current_epoch = 0
        
        # 初始化所有可能的損失函數
        self.losses = nn.ModuleDict()
        for config in loss_configs:
            for loss_name, loss_fn in config.get('losses', {}).items():
                if loss_name not in self.losses:
                    self.losses[loss_name] = loss_fn
    
    def set_epoch(self, epoch: int):
        """設置當前epoch"""
        self.current_epoch = epoch
    
    def get_current_stage(self) -> int:
        """獲取當前訓練階段"""
        for i, epoch_threshold in enumerate(self.stage_epochs):
            if self.current_epoch < epoch_threshold:
                return i
        return len(self.stage_epochs) - 1
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """計算當前階段的損失"""
        stage = self.get_current_stage()
        config = self.loss_configs[stage]
        
        total_loss = 0
        for loss_name, weight in config.get('weights', {}).items():
            if loss_name in self.losses:
                loss_value = self.losses[loss_name](inputs, targets)
                total_loss += weight * loss_value
        
        return total_loss


# 便捷函數
def cross_entropy2d(inputs: torch.Tensor, 
                   targets: torch.Tensor, 
                   weight: Optional[torch.Tensor] = None,
                   ignore_index: int = 255,
                   reduction: str = 'mean') -> torch.Tensor:
    """
    改進的2D交叉熵損失函數
    
    Args:
        inputs: 預測結果 [N, C, H, W]
        targets: 目標標籤 [N, H, W]
        weight: 類別權重
        ignore_index: 忽略的索引
        reduction: 減少方式
    """
    n, c, h, w = inputs.size()
    nt, ht, wt = targets.size()
    
    # 處理尺寸不匹配
    if h != ht or w != wt:
        inputs = F.interpolate(inputs, size=(ht, wt), mode="bilinear", align_corners=True)
    
    # 重塑張量
    inputs = inputs.transpose(1, 2).transpose(2, 3).contiguous().view(-1, c)
    targets = targets.view(-1).long()
    
    # 計算交叉熵
    return F.cross_entropy(inputs, targets, weight=weight, ignore_index=ignore_index, reduction=reduction)


# 損失函數工廠
class LossFactory:
    """損失函數工廠類"""
    
    @staticmethod
    def create_loss(loss_type: str, **kwargs) -> nn.Module:
        """創建損失函數"""
        loss_map = {
            'dice': DiceLoss,
            'focal': FocalLoss,
            'boundary': BoundaryAwareLoss,
            'iou': IoULoss,
            'ce': lambda **k: nn.CrossEntropyLoss(**k),
            'bce': lambda **k: nn.BCEWithLogitsLoss(**k)
        }
        
        if loss_type not in loss_map:
            raise ValueError(f"未知的損失函數類型: {loss_type}")
        
        return loss_map[loss_type](**kwargs)
    
    @staticmethod
    def create_combined_loss(config: Dict) -> CombinedLoss:
        """從配置創建組合損失"""
        losses = {}
        weights = {}
        
        for loss_name, loss_config in config.items():
            loss_type = loss_config.pop('type')
            weight = loss_config.pop('weight', 1.0)
            
            losses[loss_name] = LossFactory.create_loss(loss_type, **loss_config)
            weights[loss_name] = weight
        
        return CombinedLoss(losses, weights)


# 使用示例
if __name__ == "__main__":
    # 創建組合損失
    loss_config = {
        'dice': {'type': 'dice', 'weight': 0.6, 'smooth': 1.0},
        'focal': {'type': 'focal', 'weight': 0.4, 'gamma': 2.0},
        'boundary': {'type': 'boundary', 'weight': 0.2}
    }
    
    combined_loss = LossFactory.create_combined_loss(loss_config)
    
    # 測試損失計算
    inputs = torch.randn(2, 4, 64, 64)  # [N, C, H, W]
    targets = torch.randint(0, 4, (2, 64, 64))  # [N, H, W]
    
    loss = combined_loss(inputs, targets)
    components = combined_loss.get_loss_components(inputs, targets)
    
    print(f"總損失: {loss.item():.4f}")
    print(f"損失組件: {components}")