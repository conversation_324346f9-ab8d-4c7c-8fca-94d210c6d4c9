#!/usr/bin/env python3
"""
調試GT標註讀取問題
"""

import sys
from pathlib import Path

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

import json
import cv2
import logging

def test_gt_reading():
    """測試GT標註讀取功能"""
    print("🔍 測試GT標註讀取功能")
    print("=" * 50)
    
    if not ENHANCED_YOLO_AVAILABLE:
        print("❌ Enhanced YOLO模組不可用")
        return
    
    try:
        from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
            EnhancedYOLOConfig, 
            create_enhanced_yolo_inference
        )
        
        # 創建基本配置
        config = EnhancedYOLOConfig(
            segmentation_model_path="dummy.pt",  # 使用dummy路徑，僅測試GT讀取
            device="cpu"
        )
        
        # 創建推理器
        inference = create_enhanced_yolo_inference(config)
        
        # 測試1: 檢查現有標註目錄
        test_label_dir = Path(r"D:\image\5_test_image_test\test1\label")
        
        if test_label_dir.exists():
            print(f"✅ 找到標註目錄: {test_label_dir}")
            
            # 查找標註文件
            json_files = list(test_label_dir.glob("*.json"))
            txt_files = list(test_label_dir.glob("*.txt"))
            
            print(f"📁 JSON標註文件: {len(json_files)} 個")
            print(f"📁 TXT標註文件: {len(txt_files)} 個")
            
            # 測試讀取第一個文件
            if json_files:
                test_file = json_files[0]
                print(f"\n🧪 測試讀取JSON文件: {test_file.name}")
                
                # 檢查對應的圖像文件
                image_name = test_file.stem
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
                image_file = None
                
                for ext in image_extensions:
                    potential_image = test_file.parent.parent / "image" / f"{image_name}{ext}"
                    if potential_image.exists():
                        image_file = potential_image
                        break
                
                if image_file:
                    print(f"✅ 找到對應圖像: {image_file}")
                    
                    # 讀取圖像獲取尺寸
                    image = cv2.imread(str(image_file))
                    if image is not None:
                        image_shape = image.shape
                        print(f"📐 圖像尺寸: {image_shape}")
                        
                        # 測試GT載入
                        try:
                            gt_annotations = inference._load_gt_annotations(str(test_file), image_shape)
                            print(f"✅ GT標註載入成功: {len(gt_annotations)} 個標註")
                            
                            # 顯示詳細信息
                            for i, gt in enumerate(gt_annotations[:3]):  # 顯示前3個
                                print(f"   GT {i+1}: {gt['class_name']} (ID: {gt['class_id']}) - bbox: {gt['bbox']}")
                            
                            if len(gt_annotations) > 3:
                                print(f"   ... 還有 {len(gt_annotations) - 3} 個標註")
                            
                        except Exception as e:
                            print(f"❌ GT標註載入失敗: {e}")
                            import traceback
                            traceback.print_exc()
                    else:
                        print(f"❌ 無法讀取圖像: {image_file}")
                else:
                    print(f"❌ 未找到對應圖像文件，檢查的路徑: {test_file.parent.parent / 'image'}")
            
            elif txt_files:
                test_file = txt_files[0]
                print(f"\n🧪 測試讀取TXT文件: {test_file.name}")
                
                # 類似處理TXT文件...
                print("📝 TXT文件內容:")
                with open(test_file, 'r') as f:
                    content = f.read()
                    print(f"   {content[:200]}..." if len(content) > 200 else f"   {content}")
                
        else:
            print(f"❌ 標註目錄不存在: {test_label_dir}")
            print("💡 請創建測試數據或修改test_label_dir路徑")
            
            # 創建示例標註文件
            example_annotation = {
                "version": "5.0.1",
                "flags": {},
                "shapes": [
                    {
                        "label": "crack",
                        "points": [[100, 100], [200, 200]],
                        "group_id": None,
                        "shape_type": "rectangle",
                        "flags": {}
                    }
                ],
                "imagePath": "test.jpg",
                "imageData": None,
                "imageHeight": 480,
                "imageWidth": 640
            }
            
            print("\n📝 示例LabelMe標註格式:")
            print(json.dumps(example_annotation, indent=2))
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_annotation_formats():
    """測試不同標註格式的解析"""
    print("\n🔬 測試標註格式解析")
    print("=" * 30)
    
    # 測試LabelMe格式
    labelme_sample = {
        "shapes": [
            {
                "label": "linear",
                "points": [[50, 60], [150, 160]],
                "shape_type": "rectangle"
            },
            {
                "label": "alligator", 
                "points": [[10, 15, 25, 35, 50], [20, 30, 40, 60, 80]],
                "shape_type": "polygon"
            }
        ],
        "imageHeight": 480,
        "imageWidth": 640
    }
    
    print("✅ LabelMe格式示例解析:")
    for shape in labelme_sample["shapes"]:
        shape_type = shape["shape_type"]
        label = shape["label"]
        points = shape["points"]
        
        if shape_type == "rectangle" and len(points) == 2:
            x1, y1 = points[0]
            x2, y2 = points[1]
            bbox = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
            print(f"   矩形 {label}: bbox = {bbox}")
        elif shape_type == "polygon":
            # 計算多邊形邊界框
            xs = [p[0] for p in points] if isinstance(points[0], list) else points[::2]
            ys = [p[1] for p in points] if isinstance(points[0], list) else points[1::2]
            bbox = [min(xs), min(ys), max(xs), max(ys)]
            print(f"   多邊形 {label}: bbox = {bbox}")
    
    # 測試YOLO格式
    yolo_sample = """0 0.5 0.5 0.2 0.3
1 0.3 0.7 0.1 0.15
2 0.8 0.2 0.05 0.1"""
    
    print("\n✅ YOLO格式示例解析:")
    for line in yolo_sample.strip().split('\n'):
        parts = line.split()
        if len(parts) >= 5:
            class_id = int(parts[0])
            cx, cy, bw, bh = map(float, parts[1:5])
            
            # 假設圖像大小為640x480
            w, h = 640, 480
            x1 = (cx - bw/2) * w
            y1 = (cy - bh/2) * h
            x2 = (cx + bw/2) * w
            y2 = (cy + bh/2) * h
            
            print(f"   類別 {class_id}: bbox = [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")

if __name__ == "__main__":
    test_gt_reading()
    test_annotation_formats()