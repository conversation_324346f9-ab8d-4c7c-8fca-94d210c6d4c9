# 🎯 ROI預覽功能增強說明

## 📋 總覽

已成功修正並增強了ROI預覽功能，讓你能夠清楚地看到ROI區域在哪裡。ROI功能現在完全整合到一般推理模式中，並提供了直觀的視覺化預覽。

## ✅ 完成的修改

### 1. **ROI功能完全整合到一般推理**
- 在 `predict_single_image` 方法中新增ROI檢查和裁切邏輯
- 在推理前自動應用ROI裁切
- 記錄ROI偏移量和裁切圖像信息

### 2. **增強版ROI預覽生成**
- 創建了 `_draw_enhanced_roi_preview()` 函數
- 修正了 `_save_roi_preview()` 函數，不再依賴SAHI啟用
- 提供更清晰、更詳細的ROI區域顯示

### 3. **LabelMe座標轉換**
- 修改LabelMe處理邏輯，使用ROI裁切後的圖像
- 確保座標系統一致性
- 只輸出ROI區域的圖像和對應的JSON

### 4. **自動預覽生成**
- 在一般推理和批次處理中都會自動生成ROI預覽
- 智能判斷何時生成預覽圖

## 🎨 增強版ROI預覽特性

### 視覺化元素
1. **🔴 排除區域標記**
   - 上方區域：紅色半透明
   - 下方區域：藍色半透明
   - 左方區域：黃色半透明
   - 右方區域：青色半透明

2. **🟢 ROI邊界**
   - 粗綠色邊框標記有效區域
   - 動態調整邊框粗細（根據圖像尺寸）

3. **🟡 參考格線**
   - 黃色格線顯示切片劃分
   - 基於SAHI參數顯示

4. **📝 文字標注**
   - ROI尺寸顯示（在ROI區域內）
   - ROI參數顯示（左下角）
   - 標題顯示（頂部中央）

### 自適應功能
- **動態字體大小**：根據圖像尺寸自動調整
- **動態邊框粗細**：根據圖像尺寸自動調整
- **自動佈局**：文字和標注自動定位

## ⚙️ 配置參數

### ROI參數（在 `run_unified_yolo.py` 中）
```python
enable_roi_processing = True  # 啟用ROI處理
roi_top_ratio = 3.0          # 上邊界比例 (1.0=邊緣, 5.0=中心)
roi_bottom_ratio = 2.8       # 下邊界比例
roi_left_ratio = 3.0         # 左邊界比例
roi_right_ratio = 4.0        # 右邊界比例
```

### 預覽控制
```python
enable_roi_preview = True    # 啟用ROI預覽
preview_mode = True         # 預覽模式（處理第一張圖像時生成）
```

## 📊 ROI比例計算邏輯

### 計算公式
```python
crop_factor = (ratio - 1) / 8

# 邊界計算
y1_roi = int(height * crop_factor_top)
y2_roi = int(height - height * crop_factor_bottom)
x1_roi = int(width * crop_factor_left)
x2_roi = int(width - width * crop_factor_right)
```

### 比例對應表
| 比例值 | 裁切因子 | 保留區域 | 說明 |
|--------|----------|----------|------|
| 1.0    | 0.000    | 100%     | 完全保留（邊緣） |
| 2.0    | 0.125    | 87.5%    | 輕微裁切 |
| 3.0    | 0.250    | 75.0%    | 中度裁切 |
| 4.0    | 0.375    | 62.5%    | 較多裁切 |
| 5.0    | 0.500    | 50.0%    | 最大裁切（中心） |

## 🚀 使用方法

### 1. 基本使用
```bash
# 確保ROI參數已在 run_unified_yolo.py 中設定
# 直接運行即可自動生成ROI預覽
python run_unified_yolo.py
```

### 2. 檢查輸出
ROI預覽圖會保存在輸出目錄中：
```
output_path/
└── roi_preview.jpg    # ROI區域預覽圖
```

### 3. 預覽圖說明
- **綠色邊框**：表示實際ROI處理區域
- **彩色半透明區域**：表示會被排除的區域
- **黃色格線**：表示SAHI切片劃分（如果啟用）
- **文字標注**：顯示ROI尺寸和參數

## 🔧 當前ROI設定效果

基於你當前的配置：
```python
roi_top_ratio = 3.0     # 保留75%的上方區域
roi_bottom_ratio = 2.8  # 保留77.5%的下方區域  
roi_left_ratio = 3.0    # 保留75%的左方區域
roi_right_ratio = 4.0   # 保留62.5%的右方區域
```

對於1920x1080的圖像，ROI區域將是：
- **ROI邊界**: (480, 270) → (1200, 837)
- **ROI尺寸**: 720×567 像素
- **保留比例**: 37.5% × 52.5% = 19.7% 的原圖面積

## 📁 輸出檔案

### LabelMe整合
當啟用ROI時，LabelMe輸出會：
1. **只保存ROI裁切後的圖像**
2. **JSON座標對應ROI圖像**（不是原圖座標）
3. **確保圖像和JSON完全匹配**

### 推理結果
- **圖像**: 基於ROI裁切後的圖像進行推理
- **座標**: 相對於ROI圖像的座標系
- **性能**: 更快的推理速度（更小的圖像）

## 🎯 優勢

1. **🎨 清晰可見**: 直觀的視覺化讓你知道ROI區域確切位置
2. **🔧 易於調整**: 通過預覽圖快速驗證ROI設定是否合適
3. **⚡ 提升效率**: ROI裁切減少推理時間和記憶體使用
4. **📊 準確輸出**: LabelMe檔案與實際處理的圖像完全對應

## 💡 使用建議

1. **首次設定**：先運行預覽模式查看ROI區域
2. **參數調整**：根據預覽圖調整ROI比例
3. **驗證效果**：確認ROI區域覆蓋感興趣的道路區域
4. **批次處理**：設定滿意後進行大批量處理

現在你可以清楚地看到ROI區域在哪裡，並且能夠精確控制推理範圍！