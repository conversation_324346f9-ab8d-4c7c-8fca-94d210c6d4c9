"""
統一的util模組初始化文件
提供向後兼容的導入接口，確保重構後的模組可以順利使用
"""

# 重構後的主要模組
from .dataset import (
    YOLODataset, 
    LabelmeDataset, 
    DatasetConfig, 
    ImageProcessor, 
    FontManager,
    convert_labelme_to_yolo,
    denormalize,  # 向後兼容
)

from .checkpoint import (
    CheckpointManager,
    find_latest_checkpoint,
    load_checkpoint,
    create_checkpoint_manager,
    quick_save,
    quick_load,
)

from .losses import (
    DiceLoss,
    FocalLoss,
    BoundaryAwareLoss,
    IoULoss,
    EdgeWeightedLoss,
    StagedRoadDamageLoss,
    SAMLoss,
    CombinedLoss,
    StagedLoss,
    LossFactory,
    cross_entropy2d,
    cross_entropy2d_legacy,  # 向後兼容
)

# 向後兼容的別名
from .dataset import YOLODataset as Dataset_read_YOLODataset
from .dataset import LabelmeDataset as Dataset_read_LabelmeDataset
from .dataset import convert_labelme_to_yolo as Dataset_read_convert_labelme_to_yolo

# 導出所有主要類和函數
__all__ = [
    # Dataset相關
    'YOLODataset', 'LabelmeDataset', 'DatasetConfig', 'ImageProcessor', 
    'FontManager', 'convert_labelme_to_yolo', 'denormalize',
    'Dataset_read_YOLODataset', 'Dataset_read_LabelmeDataset', 'Dataset_read_convert_labelme_to_yolo',
    
    # Checkpoint相關
    'CheckpointManager', 'find_latest_checkpoint', 'load_checkpoint',
    'create_checkpoint_manager', 'quick_save', 'quick_load',
    
    # Loss相關
    'DiceLoss', 'FocalLoss', 'BoundaryAwareLoss', 'IoULoss',
    'EdgeWeightedLoss', 'StagedRoadDamageLoss', 'SAMLoss',
    'CombinedLoss', 'StagedLoss', 'LossFactory',
    'cross_entropy2d', 'cross_entropy2d_legacy',
]