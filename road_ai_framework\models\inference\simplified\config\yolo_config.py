#!/usr/bin/env python3
"""
簡化的YOLO推理配置
將原有的70+參數簡化為15個核心參數
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
import json


@dataclass
class ClassConfig:
    """詳細的類別配置 - 支援每個類別獨立設定"""
    name: str
    conf_threshold: float = 0.5
    sahi_conf_threshold: float = 0.1  # SAHI專用置信度
    enabled: bool = True
    color: Optional[Tuple[int, int, int]] = None  # RGB顏色，None表示自動分配
    display_name: str = ""  # 顯示名稱，空字符串表示使用name


@dataclass
class SimplifiedYOLOConfig:
    """
    簡化的YOLO配置類 - 僅保留15個核心參數
    從原有的70+參數簡化而來，專注於核心功能
    """
    
    # 模型配置 (3個參數)
    detection_model_path: str = ""
    segmentation_model_path: str = ""
    device: str = "auto"
    
    # 推理配置 (4個參數)
    img_size: int = 640
    global_conf: float = 0.25
    iou_threshold: float = 0.45
    max_det: int = 1000
    
    # 精簡SAHI配置 (5個參數)
    enable_sahi: bool = False
    slice_size: int = 512
    overlap_ratio: float = 0.2
    sahi_conf: float = 0.1
    sahi_iou: float = 0.5
    
    # 輸出配置 (3個參數)
    save_visualizations: bool = True
    save_predictions: bool = True
    save_statistics: bool = True
    
    # 詳細類別配置 - 支援每個類別獨立設定
    class_configs: Dict[int, ClassConfig] = field(default_factory=dict)
    
    # 新增詳細輸出配置
    enable_three_view_output: bool = True  # 啟用三視圖輸出 (原圖/GT/pred)
    enable_gt_comparison: bool = False     # 是否啟用GT比較
    gt_format: str = "labelme"             # GT格式: "labelme", "yolo", "coco"
    output_image_quality: int = 95         # 輸出圖像品質 (1-100)
    text_font_size: float = 2.0           # 文字大小
    enable_reports: bool = True           # 啟用報告生成
    
    # 路徑配置
    gt_path: str = ""                     # GT標註路徑
    
    def __post_init__(self):
        """配置初始化後的自動處理"""
        self._validate_config()
        if not self.class_configs:
            self._auto_detect_classes()
        
        # 設定display_name預設值
        for class_config in self.class_configs.values():
            if not class_config.display_name:
                class_config.display_name = class_config.name
    
    def _validate_config(self):
        """驗證配置參數的有效性"""
        if self.img_size <= 0:
            raise ValueError(f"img_size must be positive, got {self.img_size}")
        
        if not (0.0 <= self.global_conf <= 1.0):
            raise ValueError(f"global_conf must be between 0 and 1, got {self.global_conf}")
        
        if not (0.0 <= self.iou_threshold <= 1.0):
            raise ValueError(f"iou_threshold must be between 0 and 1, got {self.iou_threshold}")
        
        if self.max_det <= 0:
            raise ValueError(f"max_det must be positive, got {self.max_det}")
        
        if self.enable_sahi:
            if self.slice_size <= 0:
                raise ValueError(f"slice_size must be positive when SAHI enabled, got {self.slice_size}")
            
            if not (0.0 <= self.overlap_ratio < 1.0):
                raise ValueError(f"overlap_ratio must be between 0 and 1, got {self.overlap_ratio}")
    
    def _auto_detect_classes(self):
        """自動檢測類別配置"""
        # 這個方法會在後續實現中由自動檢測模組調用
        pass
    
    def get_model_path(self) -> str:
        """獲取優先使用的模型路徑"""
        return self.segmentation_model_path or self.detection_model_path
    
    def is_detection_only(self) -> bool:
        """判斷是否僅使用檢測模式"""
        return bool(self.detection_model_path and not self.segmentation_model_path)
    
    def is_segmentation_available(self) -> bool:
        """判斷是否有分割模型可用"""
        return bool(self.segmentation_model_path)
    
    def add_class_config(self, class_id: int, name: str, 
                        conf_threshold: float = 0.5,
                        sahi_conf_threshold: float = 0.1,
                        enabled: bool = True,
                        color: Optional[Tuple[int, int, int]] = None,
                        display_name: str = ""):
        """添加類別配置"""
        if not display_name:
            display_name = name
            
        self.class_configs[class_id] = ClassConfig(
            name=name,
            conf_threshold=conf_threshold,
            sahi_conf_threshold=sahi_conf_threshold,
            enabled=enabled,
            color=color,
            display_name=display_name
        )
    
    def get_class_confidence(self, class_id: int, use_sahi: bool = False) -> float:
        """獲取指定類別的置信度閾值"""
        if class_id in self.class_configs:
            config = self.class_configs[class_id]
            return config.sahi_conf_threshold if use_sahi else config.conf_threshold
        return self.sahi_conf if use_sahi else self.global_conf
    
    def get_enabled_classes(self) -> Dict[int, ClassConfig]:
        """獲取啟用的類別配置"""
        return {k: v for k, v in self.class_configs.items() if v.enabled}
    
    def summary(self) -> str:
        """返回配置摘要"""
        lines = [
            "=== Enhanced YOLO Configuration ===",
            f"模型: {self.get_model_path()}",
            f"設備: {self.device}",
            f"圖像尺寸: {self.img_size}",
            f"全域置信度閾值: {self.global_conf}",
            f"IoU閾值: {self.iou_threshold}",
            f"最大檢測數: {self.max_det}",
            f"SAHI: {'啟用' if self.enable_sahi else '禁用'}",
        ]
        
        if self.enable_sahi:
            lines.extend([
                f"  切片大小: {self.slice_size}",
                f"  重疊比例: {self.overlap_ratio}",
                f"  SAHI置信度: {self.sahi_conf}",
            ])
        
        lines.extend([
            f"類別數量: {len(self.class_configs)}",
            f"三視圖輸出: {'啟用' if self.enable_three_view_output else '禁用'}",
            f"GT比較: {'啟用' if self.enable_gt_comparison else '禁用'}",
            f"報告生成: {'啟用' if self.enable_reports else '禁用'}",
            f"保存可視化: {self.save_visualizations}",
            f"保存預測: {self.save_predictions}",
            f"保存統計: {self.save_statistics}",
        ])
        
        if self.class_configs:
            lines.append("\n=== 類別配置詳情 ===")
            for class_id, config in self.class_configs.items():
                status = "啟用" if config.enabled else "禁用"
                lines.append(f"  {class_id}: {config.display_name} (置信度: {config.conf_threshold}, SAHI: {config.sahi_conf_threshold}) - {status}")
        
        return "\n".join(lines)


# 全域配置常數 - 簡化版本
# 空列表表示從模型自動檢測類別
CLASS_NAMES: List[str] = []

# 空字典表示無標籤別名，僅在標註-模型不匹配時使用
LABEL_ALIASES: Dict[str, str] = {}


def set_global_class_names(class_names: List[str]):
    """設置全域類別名稱"""
    global CLASS_NAMES
    CLASS_NAMES = class_names.copy()


def set_global_label_aliases(aliases: Dict[str, str]):
    """設置全域標籤別名"""
    global LABEL_ALIASES
    LABEL_ALIASES = aliases.copy()


def get_default_config() -> SimplifiedYOLOConfig:
    """獲取默認配置"""
    return SimplifiedYOLOConfig()


def create_detection_config(model_path: str, 
                          confidence: float = 0.5,
                          device: str = "auto") -> SimplifiedYOLOConfig:
    """創建檢測專用配置"""
    return SimplifiedYOLOConfig(
        detection_model_path=model_path,
        global_conf=confidence,
        device=device
    )


def create_segmentation_config(model_path: str,
                             confidence: float = 0.25,
                             device: str = "auto",
                             enable_sahi: bool = False) -> SimplifiedYOLOConfig:
    """創建分割專用配置"""
    return SimplifiedYOLOConfig(
        segmentation_model_path=model_path,
        global_conf=confidence,
        device=device,
        enable_sahi=enable_sahi
    )


def create_sahi_config(model_path: str,
                      slice_size: int = 512,
                      overlap_ratio: float = 0.2,
                      confidence: float = 0.1) -> SimplifiedYOLOConfig:
    """創建SAHI專用配置"""
    return SimplifiedYOLOConfig(
        segmentation_model_path=model_path,
        enable_sahi=True,
        slice_size=slice_size,
        overlap_ratio=overlap_ratio,
        sahi_conf=confidence
    )