#!/usr/bin/env python3
"""
簡化的YOLO推理引擎
專注於核心功能：檢測、分割、SAHI
移除所有過度工程化的功能
"""

import os
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union

import numpy as np
import cv2

# 條件導入
try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False

try:
    from sahi import AutoDetectionModel
    from sahi.predict import get_sliced_prediction
    SAHI_AVAILABLE = True
except ImportError:
    SAHI_AVAILABLE = False

from ..config.yolo_config import SimplifiedYOLOConfig, ClassConfig
from ..config.class_detector import create_auto_class_configs, validate_class_configs


class SimplifiedYOLOInference:
    """
    簡化的YOLO推理類
    移除了過度工程化的功能，專注於核心YOLO推理
    """
    
    def __init__(self, config: SimplifiedYOLOConfig):
        """初始化推理引擎"""
        self.config = config
        self.logger = self._setup_logger()
        
        # 初始化模型
        self.detection_model = None
        self.segmentation_model = None
        self.sahi_model = None
        
        # 初始化類別配置
        self._setup_classes()
        
        # 載入模型
        self._load_models()
        
        # 統計信息
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'total_time': 0.0,
            'class_counts': {}
        }
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger('SimplifiedYOLO')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _setup_classes(self):
        """設置類別配置"""
        if not self.config.class_configs:
            # 自動檢測類別
            model_path = self.config.get_model_path()
            self.config.class_configs = create_auto_class_configs(
                model_path=model_path,
                default_conf=self.config.global_conf
            )
        
        # 驗證類別配置
        if not validate_class_configs(self.config.class_configs):
            raise ValueError("類別配置驗證失敗")
    
    def _load_models(self):
        """載入YOLO模型"""
        if not ULTRALYTICS_AVAILABLE:
            raise ImportError("ultralytics not available. Please install: pip install ultralytics")
        
        # 載入檢測模型
        if self.config.detection_model_path and os.path.exists(self.config.detection_model_path):
            try:
                self.detection_model = YOLO(self.config.detection_model_path)
                self.logger.info(f"✅ 檢測模型載入成功: {self.config.detection_model_path}")
            except Exception as e:
                self.logger.error(f"❌ 檢測模型載入失敗: {e}")
        
        # 載入分割模型
        if self.config.segmentation_model_path and os.path.exists(self.config.segmentation_model_path):
            try:
                self.segmentation_model = YOLO(self.config.segmentation_model_path)
                self.logger.info(f"✅ 分割模型載入成功: {self.config.segmentation_model_path}")
            except Exception as e:
                self.logger.error(f"❌ 分割模型載入失敗: {e}")
        
        # 設置SAHI模型
        if self.config.enable_sahi:
            self._setup_sahi()
        
        # 檢查是否有可用模型
        if not self.detection_model and not self.segmentation_model:
            raise ValueError("沒有可用的模型，請檢查模型路徑")
    
    def _setup_sahi(self):
        """設置SAHI模型"""
        if not SAHI_AVAILABLE:
            self.logger.warning("⚠️ SAHI不可用，跳過SAHI設置")
            self.config.enable_sahi = False
            return
        
        model_path = self.config.get_model_path()
        if not model_path:
            self.logger.warning("⚠️ 沒有模型路徑，無法設置SAHI")
            return
        
        try:
            self.sahi_model = AutoDetectionModel.from_pretrained(
                model_type='ultralytics',
                model_path=model_path,
                confidence_threshold=self.config.sahi_conf,
                device=self.config.device
            )
            self.logger.info("✅ SAHI模型設置成功")
        except Exception as e:
            self.logger.error(f"❌ SAHI模型設置失敗: {e}")
            self.config.enable_sahi = False
    
    def predict_single_image(self, image_path: str, 
                           output_dir: str = None,
                           task_type: str = "auto") -> Dict[str, Any]:
        """
        單張圖像推理
        
        Args:
            image_path: 圖像路徑
            output_dir: 輸出目錄
            task_type: 任務類型 ("detection", "segmentation", "auto")
            
        Returns:
            Dict: 推理結果
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"圖像文件不存在: {image_path}")
        
        start_time = time.time()
        
        # 載入圖像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"無法載入圖像: {image_path}")
        
        # 自動決定任務類型
        if task_type == "auto":
            if self.segmentation_model:
                task_type = "segmentation"
            elif self.detection_model:
                task_type = "detection"
            else:
                raise ValueError("沒有可用的模型")
        
        # 執行推理
        results = {}
        
        if task_type == "detection" and self.detection_model:
            results.update(self._run_detection(image))
        elif task_type == "segmentation" and self.segmentation_model:
            results.update(self._run_segmentation(image))
        
        # SAHI推理（如果啟用）
        if self.config.enable_sahi and self.sahi_model:
            sahi_results = self._run_sahi(image_path)
            results['sahi_predictions'] = sahi_results
        
        # 更新統計
        inference_time = time.time() - start_time
        self._update_stats(results, inference_time)
        
        # 保存結果（如果指定輸出目錄）
        if output_dir:
            self._save_results(image_path, image, results, output_dir)
        
        return results
    
    def _run_detection(self, image: np.ndarray) -> Dict[str, Any]:
        """運行檢測推理"""
        try:
            results = self.detection_model(
                image,
                conf=self.config.global_conf,
                iou=self.config.iou_threshold,
                imgsz=self.config.img_size,
                max_det=self.config.max_det,
                device=self.config.device,
                verbose=False
            )[0]
            
            # 解析結果
            detections = []
            if results.boxes is not None:
                boxes = results.boxes.xyxy.cpu().numpy()
                scores = results.boxes.conf.cpu().numpy()
                class_ids = results.boxes.cls.cpu().numpy().astype(int)
                
                for i, (box, score, cls_id) in enumerate(zip(boxes, scores, class_ids)):
                    if cls_id in self.config.class_configs:
                        class_config = self.config.class_configs[cls_id]
                        if score >= class_config.conf_threshold and class_config.enabled:
                            detections.append({
                                'id': i,
                                'class_id': int(cls_id),
                                'class_name': class_config.name,
                                'confidence': float(score),
                                'bbox': box.tolist(),
                                'area': float((box[2] - box[0]) * (box[3] - box[1]))
                            })
            
            return {'detections': detections}
            
        except Exception as e:
            self.logger.error(f"❌ 檢測推理失敗: {e}")
            return {'detections': []}
    
    def _run_segmentation(self, image: np.ndarray) -> Dict[str, Any]:
        """運行分割推理"""
        try:
            results = self.segmentation_model(
                image,
                conf=self.config.global_conf,
                iou=self.config.iou_threshold,
                imgsz=self.config.img_size,
                max_det=self.config.max_det,
                device=self.config.device,
                verbose=False
            )[0]
            
            # 解析結果
            segments = []
            if results.boxes is not None and results.masks is not None:
                boxes = results.boxes.xyxy.cpu().numpy()
                scores = results.boxes.conf.cpu().numpy()
                class_ids = results.boxes.cls.cpu().numpy().astype(int)
                masks = results.masks.data.cpu().numpy()
                
                for i, (box, score, cls_id, mask) in enumerate(zip(boxes, scores, class_ids, masks)):
                    if cls_id in self.config.class_configs:
                        class_config = self.config.class_configs[cls_id]
                        if score >= class_config.conf_threshold and class_config.enabled:
                            # 調整mask尺寸到原圖大小
                            h, w = image.shape[:2]
                            mask_resized = cv2.resize(mask, (w, h)) > 0.5
                            
                            segments.append({
                                'id': i,
                                'class_id': int(cls_id),
                                'class_name': class_config.name,
                                'confidence': float(score),
                                'bbox': box.tolist(),
                                'mask': mask_resized.astype(np.uint8),
                                'mask_area': float(np.sum(mask_resized))
                            })
            
            return {'segments': segments}
            
        except Exception as e:
            self.logger.error(f"❌ 分割推理失敗: {e}")
            return {'segments': []}
    
    def _run_sahi(self, image_path: str) -> List[Dict[str, Any]]:
        """運行SAHI切片推理"""
        try:
            result = get_sliced_prediction(
                image=image_path,
                detection_model=self.sahi_model,
                slice_height=self.config.slice_size,
                slice_width=self.config.slice_size,
                overlap_height_ratio=self.config.overlap_ratio,
                overlap_width_ratio=self.config.overlap_ratio
            )
            
            # 解析SAHI結果
            sahi_predictions = []
            for detection in result.object_prediction_list:
                bbox = detection.bbox
                class_id = detection.category.id
                confidence = detection.score.value
                class_name = detection.category.name
                
                # 應用類別特定閾值
                if class_id in self.config.class_configs:
                    class_config = self.config.class_configs[class_id]
                    if confidence >= class_config.conf_threshold and class_config.enabled:
                        sahi_predictions.append({
                            'class_id': class_id,
                            'class_name': class_config.name,
                            'confidence': confidence,
                            'bbox': [bbox.minx, bbox.miny, bbox.maxx, bbox.maxy],
                            'source': 'sahi'
                        })
            
            return sahi_predictions
            
        except Exception as e:
            self.logger.error(f"❌ SAHI推理失敗: {e}")
            return []
    
    def _update_stats(self, results: Dict[str, Any], inference_time: float):
        """更新統計信息"""
        self.stats['total_images'] += 1
        self.stats['total_time'] += inference_time
        
        # 統計檢測數量
        for key in ['detections', 'segments']:
            if key in results:
                self.stats['total_detections'] += len(results[key])
                for item in results[key]:
                    class_id = item['class_id']
                    self.stats['class_counts'][class_id] = self.stats['class_counts'].get(class_id, 0) + 1
        
        # 統計SAHI結果
        if 'sahi_predictions' in results:
            self.stats['total_detections'] += len(results['sahi_predictions'])
            for item in results['sahi_predictions']:
                class_id = item['class_id']
                self.stats['class_counts'][class_id] = self.stats['class_counts'].get(class_id, 0) + 1
    
    def _save_results(self, image_path: str, image: np.ndarray, 
                     results: Dict[str, Any], output_dir: str):
        """保存推理結果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        image_name = Path(image_path).stem
        
        # 保存可視化結果
        if self.config.save_visualizations:
            vis_image = self._draw_results(image.copy(), results)
            vis_path = output_path / f"{image_name}_result.jpg"
            cv2.imwrite(str(vis_path), vis_image)
        
        # 保存預測結果
        if self.config.save_predictions:
            pred_path = output_path / f"{image_name}_predictions.json"
            import json
            with open(pred_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    def _draw_results(self, image: np.ndarray, results: Dict[str, Any]) -> np.ndarray:
        """繪製推理結果到圖像上"""
        # 簡化的可視化邏輯
        for key in ['detections', 'segments']:
            if key in results:
                for item in results[key]:
                    bbox = item['bbox']
                    class_name = item['class_name']
                    confidence = item['confidence']
                    
                    # 繪製邊界框
                    x1, y1, x2, y2 = map(int, bbox)
                    cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 繪製標籤
                    label = f"{class_name}: {confidence:.2f}"
                    cv2.putText(image, label, (x1, y1-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    
                    # 繪製mask（如果有）
                    if 'mask' in item:
                        mask = item['mask']
                        colored_mask = np.zeros_like(image)
                        colored_mask[mask > 0] = [0, 255, 0]
                        image = cv2.addWeighted(image, 0.7, colored_mask, 0.3, 0)
        
        return image
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取推理統計信息"""
        stats = self.stats.copy()
        if stats['total_images'] > 0:
            stats['avg_time_per_image'] = stats['total_time'] / stats['total_images']
        else:
            stats['avg_time_per_image'] = 0.0
        return stats
    
    def reset_stats(self):
        """重置統計信息"""
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'total_time': 0.0,
            'class_counts': {}
        }