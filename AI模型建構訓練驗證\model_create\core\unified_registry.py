"""
統一註冊系統 - 自動註冊所有新架構

這個模組會自動註冊所有實現的架構變體，確保它們可以通過統一工廠系統訪問。
包括：
- CSP_IFormer 2024變體
- SegMAN變體
- 混合架構
- 原有架構的向後兼容
"""

import warnings
import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Callable
from functools import partial

from .registry import (
    register_encoder, register_decoder, register_model,
    ENCODER_REGISTRY, DECODER_REGISTRY, MODEL_REGISTRY
)


class UnifiedRegistry:
    """統一註冊系統"""
    
    def __init__(self):
        self.registration_status = {
            'csp_iformer_variants': False,
            'segman_variants': False,
            'original_models': False,
            'hybrid_models': False
        }
        
        # 自動註冊所有架構
        self.register_all_architectures()
    
    def register_all_architectures(self):
        """註冊所有架構"""
        
        print("Starting unified architecture registration...")
        
        # 註冊CSP_IFormer變體
        self._register_csp_iformer_architectures()
        
        # 註冊SegMAN變體
        self._register_segman_architectures()
        
        # 註冊原有模型
        self._register_original_architectures()
        
        # 註冊混合架構
        self._register_hybrid_architectures()
        
        # 註冊便捷創建函數
        self._register_convenience_functions()
        
        print("Architecture registration completed!")
        self._print_registration_summary()
    
    def _register_csp_iformer_architectures(self):
        """註冊CSP_IFormer 2024架構"""
        
        try:
            # 註冊Efficient變體
            self._register_csp_iformer_efficient()
            
            # 註冊Mamba變體
            self._register_csp_iformer_mamba()
            
            # 註冊Enhanced變體
            self._register_csp_iformer_enhanced()
            
            # 註冊原始CSP_IFormer (向後兼容)
            self._register_csp_iformer_original()
            
            self.registration_status['csp_iformer_variants'] = True
            print("✓ CSP_IFormer variants registered successfully")
            
        except Exception as e:
            warnings.warn(f"Failed to register CSP_IFormer variants: {e}")
            print(f"✗ CSP_IFormer variants registration failed: {e}")
    
    def _register_csp_iformer_efficient(self):
        """註冊CSP_IFormer Efficient變體"""
        
        try:
            from ..encoder.VIT.CSP_IFormer_v2024_efficient import (
                csp_iformer_efficient_tiny,
                csp_iformer_efficient_small,
                csp_iformer_efficient_base,
                CSP_IFormer_Efficient
            )
            
            # 創建包裝器類別
            def create_efficient_wrapper(model_fn, size_name):
                @register_encoder(f'csp_iformer_efficient_{size_name}')
                class CSPIFormerEfficientWrapper(nn.Module):
                    def __init__(self, **kwargs):
                        super().__init__()
                        self.model = model_fn(**kwargs)
                        self.size = size_name
                        self.variant = 'efficient'
                        
                        # 設置輸出通道信息
                        if hasattr(self.model, 'embed_dim'):
                            self.embed_dims = [self.model.embed_dim] * 4
                        else:
                            self.embed_dims = [192, 384, 512][{'tiny': 0, 'small': 1, 'base': 2}[size_name]]
                    
                    def forward(self, x):
                        return self.model(x)
                    
                    def forward_features(self, x):
                        if hasattr(self.model, 'forward_features'):
                            return self.model.forward_features(x)
                        else:
                            # 如果沒有forward_features，使用forward並移除分類頭
                            features = self.model(x)
                            return features
                    
                    def get_output_channels(self):
                        return self.embed_dims
                
                return CSPIFormerEfficientWrapper
            
            # 註冊所有尺寸
            create_efficient_wrapper(csp_iformer_efficient_tiny, 'tiny')
            create_efficient_wrapper(csp_iformer_efficient_small, 'small') 
            create_efficient_wrapper(csp_iformer_efficient_base, 'base')
            
        except ImportError as e:
            warnings.warn(f"CSP_IFormer efficient variants not available: {e}")
    
    def _register_csp_iformer_mamba(self):
        """註冊CSP_IFormer Mamba變體"""
        
        try:
            from ..encoder.VIT.CSP_IFormer_v2024_mamba import (
                csp_iformer_mamba_tiny,
                csp_iformer_mamba_small,
                csp_iformer_mamba_base,
                CSP_IFormer_Mamba
            )
            
            def create_mamba_wrapper(model_fn, size_name):
                @register_encoder(f'csp_iformer_mamba_{size_name}')
                class CSPIFormerMambaWrapper(nn.Module):
                    def __init__(self, **kwargs):
                        super().__init__()
                        self.model = model_fn(**kwargs)
                        self.size = size_name
                        self.variant = 'mamba'
                        
                        # Mamba架構的輸出通道
                        embed_dim = getattr(self.model, 'embed_dim', 512)
                        self.embed_dims = [embed_dim//4, embed_dim//2, embed_dim, embed_dim]
                    
                    def forward(self, x):
                        return self.model(x)
                    
                    def forward_features(self, x):
                        if hasattr(self.model, 'forward_features'):
                            return self.model.forward_features(x)
                        else:
                            return self.model(x)
                    
                    def get_output_channels(self):
                        return self.embed_dims
                
                return CSPIFormerMambaWrapper
            
            create_mamba_wrapper(csp_iformer_mamba_tiny, 'tiny')
            create_mamba_wrapper(csp_iformer_mamba_small, 'small')
            create_mamba_wrapper(csp_iformer_mamba_base, 'base')
            
        except ImportError as e:
            warnings.warn(f"CSP_IFormer mamba variants not available: {e}")
    
    def _register_csp_iformer_enhanced(self):
        """註冊CSP_IFormer Enhanced變體"""
        
        try:
            from ..encoder.VIT.CSP_IFormer_v2024_enhanced import (
                csp_iformer_enhanced_small,
                csp_iformer_enhanced_base,
                csp_iformer_enhanced_large,
                CSP_IFormer_Enhanced
            )
            
            def create_enhanced_wrapper(model_fn, size_name):
                @register_encoder(f'csp_iformer_enhanced_{size_name}')
                class CSPIFormerEnhancedWrapper(nn.Module):
                    def __init__(self, **kwargs):
                        super().__init__()
                        self.model = model_fn(**kwargs)
                        self.size = size_name
                        self.variant = 'enhanced'
                        
                        # Enhanced架構的輸出通道
                        embed_dim = getattr(self.model, 'embed_dim', 768)
                        self.embed_dims = [embed_dim//4, embed_dim//2, embed_dim, embed_dim]
                    
                    def forward(self, x):
                        return self.model(x)
                    
                    def forward_features(self, x):
                        if hasattr(self.model, 'forward_features'):
                            return self.model.forward_features(x)
                        else:
                            return self.model(x)
                    
                    def get_output_channels(self):
                        return self.embed_dims
                
                return CSPIFormerEnhancedWrapper
            
            create_enhanced_wrapper(csp_iformer_enhanced_small, 'small')
            create_enhanced_wrapper(csp_iformer_enhanced_base, 'base')
            create_enhanced_wrapper(csp_iformer_enhanced_large, 'large')
            
        except ImportError as e:
            warnings.warn(f"CSP_IFormer enhanced variants not available: {e}")
    
    def _register_csp_iformer_original(self):
        """註冊原始CSP_IFormer (向後兼容)"""
        
        try:
            from ..encoder.VIT.CSP_IFormer_final_SegMode import iformer_small
            
            @register_encoder('csp_iformer_original_small')
            class CSPIFormerOriginalWrapper(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    # 移除不兼容的參數
                    compatible_kwargs = {k: v for k, v in kwargs.items() 
                                       if k in ['size', 'in_channel', 'num_classes']}
                    self.model = iformer_small(**compatible_kwargs)
                    self.size = 'small'
                    self.variant = 'original'
                    self.embed_dims = [96, 192, 320, 384]  # 原始架構的通道數
                
                def forward(self, x):
                    features = self.model(x)
                    if isinstance(features, list):
                        return features[-1]  # 返回最後一層特徵
                    return features
                
                def forward_features(self, x):
                    return self.model(x)
                
                def get_output_channels(self):
                    return self.embed_dims
            
        except ImportError as e:
            warnings.warn(f"Original CSP_IFormer not available: {e}")
    
    def _register_segman_architectures(self):
        """註冊SegMAN架構"""
        
        try:
            from ..full_model.segman.segman_factory import (
                SegMANFactory, create_segman_model
            )
            
            # 註冊SegMAN工廠
            @register_model('segman_factory')
            class SegMANFactoryModel(nn.Module):
                def __init__(self, variant='enhanced', size='small', task='classification', **kwargs):
                    super().__init__()
                    self.factory = SegMANFactory()
                    self.variant = variant
                    self.size = size
                    self.task = task
                    self.kwargs = kwargs
                
                def create_model(self, **override_kwargs):
                    merged_kwargs = {**self.kwargs, **override_kwargs}
                    return create_segman_model(self.variant, self.size, self.task, **merged_kwargs)
            
            # 註冊各種SegMAN變體
            segman_variants = [
                ('mambavision', ['tiny', 'small', 'base']),
                ('efficient', ['nano', 'small', 'medium']),
                ('enhanced', ['small', 'base', 'large'])
            ]
            
            for variant, sizes in segman_variants:
                for size in sizes:
                    self._register_segman_variant(variant, size)
            
            self.registration_status['segman_variants'] = True
            print("✓ SegMAN variants registered successfully")
            
        except ImportError as e:
            warnings.warn(f"SegMAN variants not available: {e}")
            print(f"✗ SegMAN variants registration failed: {e}")
    
    def _register_segman_variant(self, variant: str, size: str):
        """註冊單個SegMAN變體"""
        
        try:
            from ..full_model.segman.segman_factory import create_segman_model
            
            @register_encoder(f'segman_{variant}_{size}')
            class SegMANVariantWrapper(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    # 創建分類模型
                    self.model = create_segman_model(
                        variant=variant,
                        size=size,
                        task='classification',
                        **kwargs
                    )
                    self.variant = variant
                    self.size = size
                    
                    # 設置輸出通道（根據size推斷）
                    size_to_dims = {
                        'nano': [32, 64, 128, 256],
                        'tiny': [64, 128, 256, 512],
                        'small': [96, 192, 384, 768],
                        'medium': [96, 192, 384, 768],
                        'base': [128, 256, 512, 1024],
                        'large': [160, 320, 640, 1280]
                    }
                    self.embed_dims = size_to_dims.get(size, [96, 192, 384, 768])
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    if hasattr(self.model, 'forward_features'):
                        return self.model.forward_features(x)
                    elif hasattr(self.model, 'encoder') and hasattr(self.model.encoder, 'forward_features'):
                        return self.model.encoder.forward_features(x)
                    else:
                        return self.model(x)
                
                def get_output_channels(self):
                    return self.embed_dims
            
        except Exception as e:
            warnings.warn(f"Failed to register SegMAN {variant}_{size}: {e}")
    
    def _register_original_architectures(self):
        """註冊原有架構 (向後兼容)"""
        
        try:
            # 註冊現有的編碼器架構
            self._register_existing_encoders()
            
            self.registration_status['original_models'] = True
            print("✓ Original architectures registered successfully")
            
        except Exception as e:
            warnings.warn(f"Failed to register original architectures: {e}")
            print(f"✗ Original architectures registration failed: {e}")
    
    def _register_existing_encoders(self):
        """註冊現有編碼器"""
        
        # ResNet變體
        try:
            from ..encoder.CNN.resnet import (
                ResNetEncoder, resnet18_encoder, resnet50_encoder
            )
            
            @register_encoder('resnet18')
            class ResNet18Wrapper(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = resnet18_encoder(**kwargs)
                    self.embed_dims = [64, 128, 256, 512]
                
                def forward(self, x):
                    return self.model(x)
                
                def get_output_channels(self):
                    return self.embed_dims
            
            @register_encoder('resnet50')
            class ResNet50Wrapper(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = resnet50_encoder(**kwargs)
                    self.embed_dims = [256, 512, 1024, 2048]
                
                def forward(self, x):
                    return self.model(x)
                
                def get_output_channels(self):
                    return self.embed_dims
                    
        except ImportError:
            warnings.warn("ResNet encoders not available")
        
        # EfficientNet變體
        try:
            from ..encoder.CNN.efficientnet import EfficientNetEncoder
            
            @register_encoder('efficientnet_b0')
            class EfficientNetB0Wrapper(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = EfficientNetEncoder(variant='b0', **kwargs)
                    self.embed_dims = [40, 80, 192, 320]
                
                def forward(self, x):
                    return self.model(x)
                
                def get_output_channels(self):
                    return self.embed_dims
                    
        except ImportError:
            warnings.warn("EfficientNet encoders not available")
    
    def _register_hybrid_architectures(self):
        """註冊混合架構"""
        
        try:
            # CSP_IFormer + SegMAN 混合架構
            @register_model('csp_iformer_segman_hybrid')
            class CSPIFormerSegMANHybrid(nn.Module):
                """CSP_IFormer編碼器 + SegMAN解碼器"""
                
                def __init__(self, 
                           encoder_variant='enhanced',
                           encoder_size='small',
                           decoder_variant='enhanced', 
                           num_classes=5,
                           **kwargs):
                    super().__init__()
                    
                    # 創建CSP_IFormer編碼器
                    encoder_name = f'csp_iformer_{encoder_variant}_{encoder_size}'
                    try:
                        encoder_class = ENCODER_REGISTRY.get(encoder_name)
                        self.encoder = encoder_class(num_classes=0, **kwargs)
                    except KeyError:
                        warnings.warn(f"Encoder {encoder_name} not found, using fallback")
                        # 使用備用編碼器
                        self.encoder = nn.Identity()
                    
                    # 創建SegMAN解碼器
                    try:
                        from ..full_model.segman.segman_factory import SegMANFactory
                        segman_factory = SegMANFactory()
                        
                        # 獲取編碼器輸出通道
                        if hasattr(self.encoder, 'get_output_channels'):
                            in_channels = self.encoder.get_output_channels()
                        else:
                            in_channels = [96, 192, 384, 768]  # 默認
                        
                        self.decoder = segman_factory.create_decoder(
                            variant=decoder_variant,
                            in_channels=in_channels,
                            num_classes=num_classes
                        )
                    except ImportError:
                        warnings.warn("SegMAN decoder not available")
                        # 簡單分類頭作為備用
                        self.decoder = nn.Sequential(
                            nn.AdaptiveAvgPool2d(1),
                            nn.Flatten(),
                            nn.Linear(768, num_classes)
                        )
                
                def forward(self, x):
                    # 編碼器提取特徵
                    if hasattr(self.encoder, 'forward_features'):
                        features = self.encoder.forward_features(x)
                        if isinstance(features, (list, tuple)):
                            features = features
                        else:
                            features = [features] * 4  # 創建多尺度特徵
                    else:
                        features = self.encoder(x)
                        if not isinstance(features, (list, tuple)):
                            features = [features] * 4
                    
                    # 解碼器生成輸出
                    output = self.decoder(features)
                    return output
            
            # Mamba + CSP 混合架構
            @register_model('mamba_csp_hybrid')
            class MambaCSPHybrid(nn.Module):
                """Mamba編碼器 + CSP解碼器"""
                
                def __init__(self, num_classes=5, **kwargs):
                    super().__init__()
                    
                    # 使用Mamba編碼器
                    try:
                        encoder_class = ENCODER_REGISTRY.get('csp_iformer_mamba_small')
                        self.encoder = encoder_class(num_classes=0, **kwargs)
                    except KeyError:
                        warnings.warn("Mamba encoder not found")
                        self.encoder = nn.Identity()
                    
                    # 簡單CSP風格解碼器
                    self.decoder = nn.Sequential(
                        nn.AdaptiveAvgPool2d(1),
                        nn.Flatten(),
                        nn.Linear(512, 256),
                        nn.GELU(),
                        nn.Dropout(0.1),
                        nn.Linear(256, num_classes)
                    )
                
                def forward(self, x):
                    features = self.encoder.forward_features(x) if hasattr(self.encoder, 'forward_features') else self.encoder(x)
                    if isinstance(features, (list, tuple)):
                        features = features[-1]  # 使用最後一層特徵
                    output = self.decoder(features)
                    return output
            
            self.registration_status['hybrid_models'] = True
            print("✓ Hybrid architectures registered successfully")
            
        except Exception as e:
            warnings.warn(f"Failed to register hybrid architectures: {e}")
            print(f"✗ Hybrid architectures registration failed: {e}")
    
    def _register_convenience_functions(self):
        """註冊便捷創建函數"""
        
        @register_model('create_best_model')
        class BestModelCreator(nn.Module):
            """自動選擇最佳模型配置"""
            
            def __init__(self, task='classification', num_classes=1000, efficiency='balanced', **kwargs):
                super().__init__()
                
                # 根據效率需求選擇模型
                if efficiency == 'high':  # 高效率
                    model_name = 'csp_iformer_efficient_small'
                elif efficiency == 'quality':  # 高質量
                    model_name = 'csp_iformer_enhanced_base'
                else:  # 平衡
                    model_name = 'csp_iformer_enhanced_small'
                
                try:
                    model_class = ENCODER_REGISTRY.get(model_name)
                    self.model = model_class(num_classes=num_classes, **kwargs)
                    self.model_name = model_name
                except KeyError:
                    warnings.warn(f"Best model {model_name} not found, using fallback")
                    self.model = nn.Sequential(
                        nn.AdaptiveAvgPool2d(1),
                        nn.Flatten(),
                        nn.Linear(3, num_classes)
                    )
                    self.model_name = "fallback"
            
            def forward(self, x):
                return self.model(x)
    
    def _print_registration_summary(self):
        """打印註冊摘要"""
        
        print("\nRegistration Summary:")
        print("-" * 40)
        
        for component, status in self.registration_status.items():
            status_icon = "✓" if status else "✗"
            print(f"{status_icon} {component}: {'Success' if status else 'Failed'}")
        
        # 統計註冊的組件數量
        total_encoders = len(ENCODER_REGISTRY.list_available())
        total_decoders = len(DECODER_REGISTRY.list_available())
        total_models = len(MODEL_REGISTRY.list_available())
        
        print(f"\nTotal registered components:")
        print(f"  Encoders: {total_encoders}")
        print(f"  Decoders: {total_decoders}")
        print(f"  Models: {total_models}")
        
        success_count = sum(self.registration_status.values())
        total_count = len(self.registration_status)
        success_rate = (success_count / total_count) * 100
        
        print(f"\nOverall success rate: {success_rate:.1f}% ({success_count}/{total_count})")
    
    def get_registration_status(self) -> Dict[str, bool]:
        """獲取註冊狀態"""
        return self.registration_status.copy()
    
    def list_all_components(self) -> Dict[str, list]:
        """列出所有註冊的組件"""
        return {
            'encoders': ENCODER_REGISTRY.list_available(),
            'decoders': DECODER_REGISTRY.list_available(),
            'models': MODEL_REGISTRY.list_available()
        }


# 全域註冊實例
_unified_registry = None

def get_unified_registry() -> UnifiedRegistry:
    """獲取統一註冊系統實例（單例模式）"""
    global _unified_registry
    if _unified_registry is None:
        _unified_registry = UnifiedRegistry()
    return _unified_registry

def ensure_architectures_registered():
    """確保所有架構已註冊"""
    return get_unified_registry()

# 便捷函數
def list_available_architectures() -> Dict[str, list]:
    """列出所有可用架構"""
    registry = get_unified_registry()
    return registry.list_all_components()

def get_architecture_info() -> Dict[str, Any]:
    """獲取架構信息"""
    registry = get_unified_registry()
    components = registry.list_all_components()
    status = registry.get_registration_status()
    
    return {
        'components': components,
        'registration_status': status,
        'summary': {
            'total_encoders': len(components['encoders']),
            'total_decoders': len(components['decoders']),
            'total_models': len(components['models']),
            'success_rate': sum(status.values()) / len(status) * 100
        }
    }


if __name__ == "__main__":
    # 測試統一註冊系統
    print("Testing Unified Registry System")
    print("=" * 50)
    
    registry = get_unified_registry()
    
    # 顯示註冊狀態
    status = registry.get_registration_status()
    print("\nRegistration Status:")
    for component, success in status.items():
        print(f"  {component}: {'✓' if success else '✗'}")
    
    # 顯示組件統計
    components = registry.list_all_components()
    print(f"\nComponent Statistics:")
    print(f"  Encoders: {len(components['encoders'])}")
    print(f"  Decoders: {len(components['decoders'])}")
    print(f"  Models: {len(components['models'])}")
    
    # 顯示部分可用架構
    print(f"\nSample Available Architectures:")
    print(f"  Encoders (first 10): {components['encoders'][:10]}")
    print(f"  Models (first 5): {components['models'][:5]}")
    
    print(f"\n" + "=" * 50)
    print("Registry test completed!")