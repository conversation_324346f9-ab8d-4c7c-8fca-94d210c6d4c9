#!/usr/bin/env python3
"""
🧪 Enhanced Preview Function Test Script
Test the enhanced ROI preview generator functionality
"""

import numpy as np
import cv2
from pathlib import Path


def test_enhanced_preview_generator():
    """Test enhanced ROI preview generator"""
    try:
        print("🧪 Testing Enhanced ROI Preview Generator...")
        
        # Import enhanced preview generator
        from models.inference.roi_preview_generator_enhanced import create_enhanced_roi_preview_generator
        print("✅ Successfully imported enhanced ROI preview generator")
        
        # Create enhanced preview generator instance
        preview_generator = create_enhanced_roi_preview_generator()
        print("✅ Successfully created enhanced preview generator instance")
        
        # Test configuration
        roi_ratios = {
            'top': 3.0,
            'bottom': 2.8,
            'left': 1.3,
            'right': 1.7
        }
        
        slice_config = {
            'height': 320,
            'width': 320,
            'overlap_ratio': 0.25
        }
        
        print("✅ Configuration parameters ready")
        print(f"   ROI Config: {roi_ratios}")
        print(f"   Slice Config: {slice_config}")
        
        # Test coordinate calculation
        test_image_shape = (1080, 1920)  # Full HD image
        roi_coords = preview_generator.calculate_roi_coordinates(test_image_shape, roi_ratios)
        print("✅ ROI coordinate calculation successful")
        print(f"   Result: {roi_coords}")
        
        # Calculate ROI area size
        roi_width = roi_coords['right'] - roi_coords['left']
        roi_height = roi_coords['bottom'] - roi_coords['top']
        print(f"   ROI Size: {roi_width} x {roi_height} px")
        
        # Create test image
        test_image = np.zeros((1080, 1920, 3), dtype=np.uint8)
        # Add some gradient for visual effect
        for i in range(1080):
            for j in range(1920):
                test_image[i, j] = [i//4, j//8, (i+j)//8]
        
        print("✅ Test image created")
        
        # Test enhanced preview generation
        test_output_path = "/tmp/test_enhanced_preview.jpg"
        enhanced_preview = preview_generator.generate_enhanced_preview(
            test_image, roi_ratios, slice_config, test_output_path
        )
        
        if enhanced_preview is not None:
            print("✅ Enhanced preview generation successful")
            print(f"   Preview saved to: {test_output_path}")
            print(f"   Preview shape: {enhanced_preview.shape}")
        else:
            print("❌ Enhanced preview generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_features():
    """Test enhanced preview features"""
    try:
        print("\n🎨 Testing Enhanced Features...")
        
        from models.inference.roi_preview_generator_enhanced import EnhancedROIPreviewGenerator
        
        generator = EnhancedROIPreviewGenerator()
        
        # Test color configuration
        print("✅ Exclude area colors:")
        for direction, color in generator.exclude_colors.items():
            print(f"   {direction}: {color}")
        
        print("✅ Visual settings:")
        print(f"   Exclude alpha: {generator.exclude_alpha}")
        print(f"   Overlap alpha: {generator.overlap_alpha}")
        print(f"   Grid color: {generator.grid_color}")
        print(f"   Overlap color: {generator.overlap_color}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced features test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Enhanced ROI Preview Function Test")
    print("=" * 60)
    
    # Test enhanced preview generator
    if not test_enhanced_preview_generator():
        print("❌ Enhanced preview generator test failed")
        return
    
    # Test enhanced features
    if not test_enhanced_features():
        print("❌ Enhanced features test failed")
        return
    
    print("\n🎉 All enhanced tests passed!")
    print("📷 Enhanced preview functionality is ready")
    print("\n💡 Enhanced Features:")
    print("   ✅ Filled excluded areas (instead of borders)")
    print("   ✅ English annotations and labels")
    print("   ✅ Enhanced overlap visualization")
    print("   ✅ Improved grid display")
    print("   ✅ Slice coordinate labels [Row,Col]")
    print("   ✅ Light green tint for ROI area")
    print("   ✅ Orange filled overlap regions")
    print("\n🚀 Usage:")
    print("   1. Set preview_mode = True")
    print("   2. Run python run_unified_yolo.py")
    print("   3. Enhanced preview will be saved to output_path/preview/")


if __name__ == "__main__":
    main()