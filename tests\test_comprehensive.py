"""
全面的單元測試套件

使用test_image作為測試資料源，測試所有重構後的模組
"""

import unittest
import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List
import numpy as np
import cv2

# 添加專案路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "AI模型建構訓練驗證"))
sys.path.insert(0, str(project_root / "資料前處理"))

from tests.base_test import BaseTestCase, ModelTestMixin, DistributedTestMixin, DataProcessingTestMixin


class ComprehensiveDataProcessingTest(BaseTestCase, DataProcessingTestMixin):
    """全面的資料處理測試"""
    
    def setUp(self):
        """設置測試環境"""
        super().setUp()
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
    
    def test_shared_base_tool(self):
        """測試共享基類工具"""
        try:
            from 資料前處理.shared.base_tool import BaseTool
            from 資料前處理.shared.exceptions import ValidationError
            
            # 創建測試工具類
            class TestTool(BaseTool):
                def validate_inputs(self) -> bool:
                    return self.input_dir is not None
                
                def _execute_main_logic(self, **kwargs):
                    self.stats['processed_files'] = 5
                    self.stats['total_files'] = 5
                    return self.stats
            
            # 測試工具
            tool = TestTool(input_dir=str(self.test_data_dir))
            self.assertIsNotNone(tool)
            
            # 測試執行
            result = tool.run()
            self.assertIn('processed_files', result)
            self.assertEqual(result['processed_files'], 5)
            
        except ImportError as e:
            self.skipTest(f"無法導入BaseTool: {e}")
    
    def test_shared_config_manager(self):
        """測試配置管理器"""
        try:
            from 資料前處理.shared.config_manager import ConfigManager
            
            config_manager = ConfigManager()
            self.assertIsNotNone(config_manager)
            
            # 測試配置設置和獲取
            test_config = {"test_key": "test_value", "nested": {"key": "value"}}
            config_manager.config_data = test_config
            
            self.assertEqual(config_manager.get("test_key"), "test_value")
            self.assertEqual(config_manager.get("nested.key"), "value")
            self.assertIsNone(config_manager.get("nonexistent"))
            
        except ImportError as e:
            self.skipTest(f"無法導入ConfigManager: {e}")
    
    def test_format_detector(self):
        """測試格式檢測器"""
        try:
            from 資料前處理.tools.format_detector import FormatDetector
            
            detector = FormatDetector()
            self.assertIsNotNone(detector)
            
            # 測試使用真實的test_image數據
            for json_file in self.test_data_dir.glob("*.json"):
                try:
                    detected_format = detector.detect_format(json_file)
                    self.assertIn(detected_format, ['labelme', 'coco'])
                    break  # 只測試第一個文件
                except Exception as e:
                    self.logger.warning(f"格式檢測失敗 {json_file}: {e}")
            
        except ImportError as e:
            self.skipTest(f"無法導入FormatDetector: {e}")
    
    def test_annotation_converter_v2_creation(self):
        """測試重構後的標註轉換器創建"""
        try:
            from 資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2
            
            converter = AnnotationConverterV2(
                input_dir=str(self.test_data_dir),
                output_dir=str(self.temp_dir),
                input_format="auto",
                output_format="yolo"
            )
            
            self.assertIsNotNone(converter)
            self.assertEqual(converter.input_format, "auto")
            self.assertEqual(converter.output_format, "yolo")
            
        except ImportError as e:
            self.skipTest(f"無法導入AnnotationConverterV2: {e}")
    
    def test_annotation_converter_compat(self):
        """測試向後兼容適配器"""
        try:
            from 資料前處理.annotation_converter_compat import AnnotationConverter
            
            converter = AnnotationConverter()
            self.assertIsNotNone(converter)
            self.assertIn('total', converter.stats)
            self.assertIn('labelme', converter.supported_formats)
            
        except ImportError as e:
            self.skipTest(f"無法導入向後兼容AnnotationConverter: {e}")
    
    def test_image_processor(self):
        """測試圖像處理器"""
        try:
            from 資料前處理.tools.image_processor import ImageProcessor
            
            processor = ImageProcessor()
            self.assertIsNotNone(processor)
            
            # 使用test_image進行測試
            image, key = self.get_sample_image()
            self.assert_image_valid(image)
            
            # 測試圖像處理能力（如果實現了的話）
            # 這裡可以添加具體的圖像處理測試
            
        except ImportError as e:
            self.skipTest(f"無法導入ImageProcessor: {e}")


class ComprehensiveModelTest(BaseTestCase, ModelTestMixin):
    """全面的模型測試"""
    
    def test_model_factory_creation(self):
        """測試模型工廠創建"""
        try:
            from AI模型建構訓練驗證.model_create.core.model_factory import ModelFactory
            
            factory = ModelFactory()
            self.assertIsNotNone(factory)
            
            # 測試模型創建能力（使用預設配置）
            try:
                # 這裡可以測試具體的模型創建
                pass
            except Exception as e:
                self.logger.warning(f"模型創建測試跳過: {e}")
            
        except ImportError as e:
            self.skipTest(f"無法導入ModelFactory: {e}")
    
    def test_unified_trainer_creation(self):
        """測試統一訓練器創建"""
        try:
            from AI模型建構訓練驗證.model_create.training.trainer import UnifiedTrainer, TrainingConfig
            
            config = TrainingConfig(epochs=5)  # 測試用的短epoch
            self.assertIsNotNone(config)
            self.assertEqual(config.epochs, 5)
            
            # 測試訓練器創建（不進行實際訓練）
            # trainer = UnifiedTrainer(model=None, optimizer=None, config=config)
            # 由於需要真實模型，這裡只測試配置
            
        except ImportError as e:
            self.skipTest(f"無法導入UnifiedTrainer: {e}")
    
    def test_util_modules(self):
        """測試工具模組"""
        try:
            # 測試dataset模組
            from AI模型建構訓練驗證.model_create.util.dataset import DatasetConfig
            
            config = DatasetConfig()
            self.assertIsNotNone(config)
            
            # 測試checkpoint模組
            from AI模型建構訓練驗證.model_create.util.checkpoint import CheckpointManager
            
            # 創建臨時目錄進行測試
            temp_checkpoint_dir = tempfile.mkdtemp()
            self.addCleanup(shutil.rmtree, temp_checkpoint_dir)
            
            manager = CheckpointManager(temp_checkpoint_dir)
            self.assertIsNotNone(manager)
            
            # 測試losses模組
            from AI模型建構訓練驗證.model_create.util.losses import LossFactory
            
            # 測試損失函數工廠
            self.assertIsNotNone(LossFactory)
            
        except ImportError as e:
            self.skipTest(f"無法導入util模組: {e}")


class ComprehensiveDistributedTest(BaseTestCase, DistributedTestMixin):
    """全面的分散式框架測試"""
    
    def setUp(self):
        super().setUp()
        self.setUp_distributed()
    
    def tearDown(self):
        super().tearDown()
        self.tearDown_distributed()
    
    def test_ray_ai_integration_creation(self):
        """測試Ray AI整合模組創建"""
        self.skip_if_no_ray()
        
        try:
            from AI模型建構訓練驗證.model_create.distributed.ray_ai_integration import (
                RayAIIntegrationManager, RayTrainingConfig
            )
            
            # 測試配置創建
            config = RayTrainingConfig(num_workers=1, use_gpu=False)
            self.assertIsNotNone(config)
            self.assertEqual(config.num_workers, 1)
            self.assertFalse(config.use_gpu)
            
            # 測試管理器創建（在CI環境中可能失敗）
            try:
                manager = RayAIIntegrationManager()
                self.assertIsNotNone(manager)
            except Exception as e:
                self.logger.warning(f"Ray管理器創建失敗（可能是環境問題）: {e}")
            
        except ImportError as e:
            self.skipTest(f"無法導入Ray AI整合模組: {e}")
    
    def test_ray_search_space_creation(self):
        """測試Ray搜索空間創建"""
        self.skip_if_no_ray()
        
        try:
            from AI模型建構訓練驗證.model_create.distributed.ray_ai_integration import (
                create_search_space_for_model
            )
            
            # 測試不同模型的搜索空間
            csp_space = create_search_space_for_model("csp_iformer")
            self.assertIsInstance(csp_space, dict)
            self.assertIn("model", csp_space)
            
            mobile_space = create_search_space_for_model("mobilenet")
            self.assertIsInstance(mobile_space, dict)
            self.assertIn("optimizer", mobile_space)
            
            generic_space = create_search_space_for_model("unknown")
            self.assertIsInstance(generic_space, dict)
            
        except ImportError as e:
            self.skipTest(f"無法導入Ray搜索空間創建函數: {e}")


class ComprehensiveIntegrationTest(BaseTestCase):
    """全面的整合測試"""
    
    def setUp(self):
        super().setUp()
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
    
    def test_end_to_end_data_processing(self):
        """端到端資料處理測試"""
        # 1. 測試test_image資料載入
        image, annotation, key = self.get_image_annotation_pair()
        self.assert_image_valid(image)
        self.assert_annotation_valid(annotation)
        
        # 2. 測試基礎工具創建
        try:
            from 資料前處理.shared.base_tool import BaseTool
            
            class MockTool(BaseTool):
                def validate_inputs(self):
                    return True
                
                def _execute_main_logic(self, **kwargs):
                    return {"processed": True}
            
            tool = MockTool(input_dir=str(self.test_data_dir))
            result = tool.run()
            self.assertTrue(result.get("processed"))
            
        except ImportError as e:
            self.logger.warning(f"基礎工具測試跳過: {e}")
        
        # 3. 測試配置管理
        try:
            from 資料前處理.shared.config_manager import ConfigManager
            
            config_manager = ConfigManager()
            config_manager.set("test.value", "test_data")
            self.assertEqual(config_manager.get("test.value"), "test_data")
            
        except ImportError as e:
            self.logger.warning(f"配置管理測試跳過: {e}")
    
    def test_model_training_integration(self):
        """模型訓練整合測試"""
        try:
            # 測試訓練配置創建
            from AI模型建構訓練驗證.model_create.training.trainer import TrainingConfig
            
            config = TrainingConfig(
                epochs=1,  # 最小測試
                enable_mixed_precision=False,  # 關閉以避免環境問題
                gradient_accumulation_steps=1
            )
            
            self.assertIsNotNone(config)
            self.assertEqual(config.epochs, 1)
            
        except ImportError as e:
            self.skipTest(f"無法導入訓練配置: {e}")
    
    def test_distributed_ray_integration(self):
        """分散式Ray整合測試"""
        try:
            from AI模型建構訓練驗證.model_create.distributed import (
                create_ray_training_system,
                RayTrainingConfig
            )
            
            # 測試配置創建
            ray_config = RayTrainingConfig(
                num_workers=1,
                use_gpu=False,  # 測試環境不使用GPU
                storage_path=str(self.temp_dir)
            )
            
            self.assertIsNotNone(ray_config)
            self.assertEqual(ray_config.num_workers, 1)
            
        except ImportError as e:
            self.skipTest(f"無法導入Ray整合模組: {e}")
    
    def test_architecture_consistency(self):
        """架構一致性測試"""
        # 測試各模組間的一致性
        
        # 1. 配置管理一致性
        config_managers = []
        
        try:
            from 資料前處理.shared.config_manager import ConfigManager as DataConfigManager
            config_managers.append(DataConfigManager)
        except ImportError:
            pass
        
        try:
            from AI模型建構訓練驗證.model_create.core.config_manager import ConfigManager as ModelConfigManager
            config_managers.append(ModelConfigManager)
        except ImportError:
            pass
        
        # 確保所有配置管理器都可以創建
        for ConfigManager in config_managers:
            manager = ConfigManager()
            self.assertIsNotNone(manager)
        
        # 2. 基礎類一致性
        try:
            from 資料前處理.shared.base_tool import BaseTool
            
            # 確保BaseTool有必要的方法
            self.assertTrue(hasattr(BaseTool, 'validate_inputs'))
            self.assertTrue(hasattr(BaseTool, 'run'))
            
        except ImportError:
            pass


class TestImageDataTest(BaseTestCase):
    """專門針對test_image資料的測試"""
    
    def test_test_image_data_integrity(self):
        """測試test_image資料完整性"""
        # 檢查圖像和標註配對
        image_files = list(self.test_data_dir.glob("*.jpg"))
        json_files = list(self.test_data_dir.glob("*.json"))
        
        self.assertGreater(len(image_files), 0, "應該有至少一張測試圖像")
        self.assertGreater(len(json_files), 0, "應該有至少一個測試標註")
        
        # 檢查配對
        paired_count = 0
        for img_file in image_files:
            json_file = img_file.with_suffix('.json')
            if json_file.exists():
                paired_count += 1
        
        self.assertGreater(paired_count, 0, "應該有至少一對圖像-標註檔案")
    
    def test_test_image_format_validation(self):
        """測試test_image格式驗證"""
        for i in range(min(3, len(self.test_images))):  # 測試前3張圖像
            image, key = self.get_sample_image(i)
            annotation = self.get_sample_annotation(key)
            
            # 圖像驗證
            self.assert_image_valid(image)
            
            # 標註驗證
            if annotation:
                self.assert_annotation_valid(annotation)
                
                # 檢查標註內容
                self.assertIn('shapes', annotation)
                shapes = annotation['shapes']
                
                for shape in shapes:
                    self.assertIn('label', shape)
                    self.assertIn('points', shape)
                    self.assertIsInstance(shape['points'], list)
                    
                    # 檢查點座標有效性
                    for point in shape['points']:
                        self.assertIsInstance(point, list)
                        self.assertEqual(len(point), 2)
                        self.assertIsInstance(point[0], (int, float))
                        self.assertIsInstance(point[1], (int, float))
    
    def test_test_image_processing_workflow(self):
        """測試使用test_image的處理工作流程"""
        # 模擬完整的處理流程
        image, annotation, key = self.get_image_annotation_pair()
        
        # 1. 圖像預處理
        processed_image = cv2.resize(image, (224, 224))
        self.assertEqual(processed_image.shape[:2], (224, 224))
        
        # 2. 標註處理
        if annotation and 'shapes' in annotation:
            labels = [shape['label'] for shape in annotation['shapes']]
            self.assertIsInstance(labels, list)
        
        # 3. 格式轉換模擬
        temp_output = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, temp_output)
        
        # 儲存處理後的圖像
        output_path = Path(temp_output) / f"processed_{key}.jpg"
        cv2.imwrite(str(output_path), processed_image)
        self.assertTrue(output_path.exists())


def create_comprehensive_test_suite():
    """創建全面測試套件"""
    suite = unittest.TestSuite()
    
    # 添加所有測試類
    test_classes = [
        ComprehensiveDataProcessingTest,
        ComprehensiveModelTest,
        ComprehensiveDistributedTest,
        ComprehensiveIntegrationTest,
        TestImageDataTest
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    return suite


def run_comprehensive_tests():
    """運行全面測試"""
    print("🧪 開始運行全面單元測試...")
    print("=" * 60)
    
    suite = create_comprehensive_test_suite()
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    print(f"運行測試數: {result.testsRun}")
    print(f"失敗測試數: {len(result.failures)}")
    print(f"錯誤測試數: {len(result.errors)}")
    print(f"跳過測試數: {len(result.skipped)}")
    
    if result.failures:
        print("\n❌ 失敗的測試:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n⚠️ 錯誤的測試:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if result.skipped:
        print(f"\n⏭️ 跳過的測試: {len(result.skipped)} 個")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    print(f"\n✅ 成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)