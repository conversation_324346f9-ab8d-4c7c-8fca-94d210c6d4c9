import os
import json
import cv2
import numpy as np
from tqdm import tqdm
from pathlib import Path
import xml.etree.ElementTree as ET
import logging
import sys
logging.StreamHandler(sys.stdout)

def get_polygon_from_box(xmin, ymin, xmax, ymax):
    """
    將 box (xmin, ymin, xmax, ymax) 轉換為四邊形座標列表，
    返回格式: [[xmin, ymin], [xmax, ymin], [xmax, ymax], [xmin, ymax]]
    """
    return [[xmin, ymin], [xmax, ymin], [xmax, ymax], [xmin, ymax]]



class RoadDataProcessor:
    # ============= 1) 英文→中文對照 & 顏色定義 =============
    class_mapping = {
        "expansion_joint": "伸縮縫",
        "joint": "路面接縫",
        "linear_crack": "裂縫",
        "Alligator_crack": "龜裂",
        "potholes": "坑洞",
        "patch": "補綻",
        "manhole_ditch": "人孔蓋或排水溝",
        "deformation": "變形",
        "dirt_spot": "污垢",
        "other": "其它"
    }

    # 每個 key 在 class_mapping 的順序，就是該類別的 ID
    label_id_map = {k: i for i, k in enumerate(class_mapping.keys())}

    label_colors = {
        "expansion_joint": (128, 0, 128),
        "joint": (0, 255, 255),
        "linear_crack": (0, 255, 0),
        "Alligator_crack": (255, 0, 0),
        "potholes": (0, 0, 255),
        "patch": (255, 105, 180),
        "manhole_ditch": (160, 82, 45),  # 棕色系，代表人孔蓋或溝蓋
        "deformation": (0, 0, 139),
        "dirt_spot": (10, 25, 139),
        "other": (10, 250, 19)
    }

    def __init__(self,
                 image_dirs,
                 json_dirs,
                 out_dir,
                 log_path=None,
                 max_count=9999,
                 img_save_quality=95,
                 resize_to=None):
        """
        主要參數說明：
          image_dirs : List[str] ，影像所在資料夾路徑清單
          json_dirs  : List[str] ，JSON 所在資料夾路徑清單（通常與 image_dirs 對應）
          out_dir    : str        ，處理後輸出的根目錄
          log_path   : str or None，若未指定，預設會放在 out_dir 下 "process_log.txt"
          max_count  : int        ，單一資料夾最多處理多少個 JSON 檔
        """
        self.image_dirs = image_dirs
        self.json_dirs = json_dirs
        self.out_dir = out_dir
        self.max_count = max_count
        self.img_save_quality = img_save_quality
        self.resize_to = resize_to


        if log_path is None:
            log_path = os.path.join(out_dir, "process_log.txt")
        self.log_path = log_path

        # 準備輸出目錄
        os.makedirs(self.out_dir, exist_ok=True)

        # 設定 logging
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(self.log_path, mode='w', encoding='utf-8'),
                logging.StreamHandler()
            ],
            force=True  # 這是 Python 3.8+ 才支援的參數
        )


    def run(self):
        """
        主流程：執行資料處理 + 最終標籤統計
        """
        # 1) 處理所有資料夾
        self._process_multiple_datasets()
    
        # 2) 計算最終標籤統計，這裡返回三個統計數據
        segment_count, object_count, multi_label_detail = self._calculate_statistics()
        label_stats_path = os.path.join(self.out_dir, "label_stats.json")
        with open(label_stats_path, "w", encoding="utf-8") as f:
            json.dump({
                "segment_count": segment_count,
                "object_count": object_count,
                "multi_label_detail": multi_label_detail
            }, f, ensure_ascii=False, indent=2)
    
        # 3) 寫 log 結果
        logging.info("\n===== Segment 標籤統計 =====")
        for label, cnt in segment_count.items():
            logging.info(f"  {label}: {cnt} 張")
        logging.info("\n===== Object 標籤統計 =====")
        for label, cnt in object_count.items():
            logging.info(f"  {label}: {cnt} 張")
        if multi_label_detail:
            logging.info("\n===== 多類別影像標籤數量 =====")
            for lbl, cnt in multi_label_detail.items():
                logging.info(f"  {lbl}: {cnt} 次")
    
        logging.info(f"\n✅ label_stats.json 已儲存：{label_stats_path}")
        logging.info("全部流程完成！")

    # ---------------------------------------------
    # 以下皆為此類別的內部方法
    # ---------------------------------------------
    def _process_multiple_datasets(self):
        """
        多組路徑 -> 同時輸出 Segment label + Object detect label + VOC
        並記錄處理結果到 processing_stats.json
        """
        all_stats = []
        for img_dir, js_dir in zip(self.image_dirs, self.json_dirs):
            stat = self._process_dataset(img_dir, js_dir)
            all_stats.append(stat)

        # 彙總
        total_all = sum(s["total_samples"] for s in all_stats)
        total_success = sum(s["processed_success"] for s in all_stats)
        total_skipped = sum(s["skipped_or_failed"] for s in all_stats)

        summary = {
            "total_input_jsons": total_all,
            "total_processed_success": total_success,
            "total_skipped_or_failed": total_skipped,
            "per_folder": all_stats
        }

        summary_path = os.path.join(self.out_dir, "processing_stats.json")
        with open(summary_path, "w", encoding="utf-8") as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logging.info("\n======================================")
        logging.info("📊 處理完成！以下為彙總統計：")
        logging.info(f"  • 總 JSON 數量: {total_all}")
        logging.info(f"  • 成功處理: {total_success}")
        logging.info(f"  • 跳過或失敗: {total_skipped}")
        logging.info(f"詳細紀錄已寫入: {summary_path}")

    def _process_dataset(self, img_dir: str, js_dir: str):
        """
        回傳統計資訊:
          {
            "source": <資料夾名稱>,
            "total_samples": <總 json 數>,
            "processed_success": <成功處理數>,
            "skipped_or_failed": <跳過或失敗數>
          }
        """
        img_dir_path = Path(img_dir)
        js_dir_path = Path(js_dir)

        js_files = list(js_dir_path.glob("*.json"))[:self.max_count]

        total = 0
        success = 0
        skipped = 0

        logging.info(f"\n=== 處理資料夾: {js_dir_path.name} ===")
        for js_file in tqdm(js_files, desc=f"處理 {js_dir_path.name}"):
            total += 1
            try:
                data = json.loads(js_file.read_text(encoding="utf-8"))
            except Exception as e:
                logging.error(f"解析失敗，跳過：{js_file}, {e}")
                skipped += 1
                continue

            base_name = js_file.stem
            # 找對應圖片
            img_name = data.get("imagePath", base_name + ".jpg")
            image_file = img_dir_path / img_name
            if not image_file.exists():
                found = False
                for ext in [".jpeg", ".png", ".bmp"]:
                    alt = img_dir_path / (base_name + ext)
                    if alt.exists():
                        image_file = alt
                        found = True
                        break
                if not found:
                    logging.warning(f"❌ 找不到對應圖片，跳過：{base_name}")
                    skipped += 1
                    continue

            # 嘗試處理
            ret = self._process_image_and_json(image_file, data)
            if not ret:
                skipped += 1
            else:
                success += 1

        logging.info(f"  - 總樣本數: {total}")
        logging.info(f"  - 成功處理: {success}")
        logging.info(f"  - 跳過或失敗: {skipped}")

        return {
            "source": js_dir_path.name,
            "total_samples": total,
            "processed_success": success,
            "skipped_or_failed": skipped
        }

    def _process_image_and_json(self, img_file: Path, json_data: dict) -> bool:
        """
        執行標註轉換：
          - 統計所有標註的標籤，若超過一種則歸類到 multi_label_多類別缺陷 目錄；
          - 如果有 polygon 標註，使用完整流程轉換所有標註（若 rectangle 則轉成四角多邊形）；
          - 如果只有 rectangle 標註，則使用僅 box 的流程處理。
        回傳 True 代表成功處理；False 代表失敗。
        """
        img = self._imdecode_unicode(img_file)
        if img is None:
            logging.warning(f"⚠️ 無法讀取影像：{img_file}")
            return False
    
        original_h, original_w = img.shape[:2]
    
        # ========== 若有設定縮放尺寸，則進行圖片與標註同步縮放 ==========
        if self.resize_to is not None:
            new_w, new_h = self.resize_to
            img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
            scale_x = new_w / original_w
            scale_y = new_h / original_h
    
            # 縮放所有標註點位
            for shape in json_data.get("shapes", []):
                shape["points"] = [
                    [x * scale_x, y * scale_y] for x, y in shape.get("points", [])
                ]
            
        h, w = img.shape[:2]
        shapes = json_data.get("shapes", [])
        if not shapes:
            logging.warning(f"⚠️ {img_file.name} 沒有任何標註")
            return False
    
        # 統計所有標籤，不論標註類型
        all_labels = [s.get("label", "unknown") for s in shapes]
        if len(set(all_labels)) == 1:
            label_dir = self._get_combined_label(all_labels[0])
        else:
            label_dir = "multi_label_多類別缺陷"
    
        # 定義 polygon_shapes 與 rectangle_shapes
        polygon_shapes = [s for s in shapes if s.get("shape_type") == "polygon"]
        rectangle_shapes = [s for s in shapes if s.get("shape_type") == "rectangle"]
    
        # 判斷是否至少有一個 polygon 標註
        any_polygon = any(s.get("shape_type") == "polygon" for s in shapes)
    
        # 當至少有一個 polygon 標註時，以 polygon 流程處理所有標註
        if any_polygon:
            paths = self._make_dirs(Path(self.out_dir), label_dir)
            voc_file = paths["object_voc_format"] / f"{img_file.stem}.xml"
            if voc_file.exists():
                logging.info(f"跳過已處理樣本（VOC 存在）：{img_file.name}")
                return True
    
            out_orig = paths["original"] / img_file.name
            if not out_orig.exists():
                self._imencode_tofile(img, out_orig)
    
            seg_mask = np.zeros((h, w), dtype=np.uint8)
            seg_overlay = img.copy()
            od_overlay = img.copy()
            rect_shapes_converted = []
            yolo_lines = []
            voc_bboxes = []
    
            for shape in shapes:
                label = shape.get("label", "unknown")
                cls_eng = self._parse_label_english_only(label)
                color = self.label_colors.get(cls_eng, (255, 255, 255))
                # 使用 polygon 處理：若原始為 rectangle 則轉換為四角多邊形
                if shape.get("shape_type") == "polygon":
                    pts = np.array(shape.get("points"), dtype=np.int32)
                else:
                    pts_tmp = np.array(shape.get("points"), dtype=np.int32)
                    pts = np.array(get_polygon_from_box(pts_tmp[0][0], pts_tmp[0][1],
                                                         pts_tmp[1][0], pts_tmp[1][1]), dtype=np.int32)
                cv2.fillPoly(seg_mask, [pts], 255)
                cv2.polylines(seg_overlay, [pts], True, color, 8)
                poly_center = pts.mean(axis=0).astype(int)
                cv2.putText(seg_overlay, cls_eng, tuple(poly_center), cv2.FONT_HERSHEY_SIMPLEX, 2, color, 8)
    
                xmin, ymin, xmax, ymax = self._polygon_to_bbox(pts)
                rect_shapes_converted.append({
                    "label": label,
                    "shape_type": "rectangle",
                    "points": [[int(xmin), int(ymin)], [int(xmax), int(ymax)]]
                })
                class_id = self.label_id_map.get(cls_eng, 0)
                yolo_box = self._convert_bbox_to_yolo([xmin, ymin, xmax, ymax], w, h)
                line = f"{class_id} {' '.join(f'{v:.6f}' for v in yolo_box)}"
                yolo_lines.append(line)
    
                cv2.rectangle(od_overlay, (int(xmin), int(ymin)), (int(xmax), int(ymax)), color, 8)
                bx_c = int((xmin + xmax) / 2)
                by_c = int((ymin + ymax) / 2)
                cv2.putText(od_overlay, cls_eng, (bx_c, by_c), cv2.FONT_HERSHEY_SIMPLEX, 2, color, 8)
    
                voc_bboxes.append((cls_eng, int(xmin), int(ymin), int(xmax), int(ymax)))
    
            seg_mask_file = paths["segment_mask_format"] / f"{img_file.stem}_mask.jpg"
            self._imencode_tofile(seg_mask, seg_mask_file)
            seg_overlay_file = paths["segment_overlay_image"] / f"{img_file.stem}_overlay.jpg"
            self._imencode_tofile(seg_overlay, seg_overlay_file)
            seg_json_file = paths["segment_labelme_format"] / f"{img_file.stem}.json"
            if not seg_json_file.exists():
                with seg_json_file.open("w", encoding="utf-8") as fw:
                    json.dump(json_data, fw, ensure_ascii=False, indent=2)
            seg_yolo_lines = self._convert_seg_labelme_to_yolo(json_data, w, h)
            seg_yolo_file = paths["segment_yolo_format"] / f"{img_file.stem}.txt"
            with seg_yolo_file.open("w", encoding="utf-8") as fw:
                fw.write("\n".join(seg_yolo_lines))
    
            od_overlay_file = paths["object_overlay_image"] / f"{img_file.stem}_overlay.jpg"
            self._imencode_tofile(od_overlay, od_overlay_file)
            od_labelme = json_data.copy()
            od_labelme["shapes"] = rect_shapes_converted
            od_json_file = paths["object_labelme_format"] / f"{img_file.stem}.json"
            with od_json_file.open("w", encoding="utf-8") as fw:
                json.dump(od_labelme, fw, ensure_ascii=False, indent=2)
            od_yolo_file = paths["object_yolo_format"] / f"{img_file.stem}.txt"
            with od_yolo_file.open("w", encoding="utf-8") as fw:
                fw.write("\n".join(yolo_lines))
            if voc_bboxes:
                self._create_voc_xml(img_file.stem, w, h, voc_bboxes, voc_file)
            else:
                logging.warning(f"⚠️ {img_file.name} 沒有有效的 VOC 標註，voc_bboxes 為空")
            return True
    
        # 如果只有 rectangle 標註，就只產生 object_detect 的結果（只有 box）
        elif rectangle_shapes:
            logging.info(f"只有box：{img_file.name}")
            # 若只有 box，但標籤不全相同，就採用多標籤文件夾；否則使用單一標籤文件夾
            # 此處直接沿用前面已設定的 label_dir 變數
            paths = self._make_dirs(Path(self.out_dir), label_dir)
        
            voc_file = paths["object_voc_format"] / f"{img_file.stem}.xml"
            if voc_file.exists():
                logging.info(f"跳過已處理樣本（VOC 存在）：{img_file.name}")
                return True
        
            out_orig = paths["original"] / img_file.name
            if not out_orig.exists():
                self._imencode_tofile(img, out_orig)
        
            od_overlay = img.copy()
            rect_shapes_list = []
            yolo_lines = []
            voc_bboxes = []
        
            for shape in rectangle_shapes:
                label = shape["label"]
                pts = np.array(shape["points"], dtype=np.int32)
                xmin, ymin = pts[0]
                xmax, ymax = pts[1]
                cls_eng = self._parse_label_english_only(label)
                color = self.label_colors.get(cls_eng, (255, 255, 255))
        
                rect_shapes_list.append({
                    "label": label,
                    "shape_type": "rectangle",
                    "points": [[int(xmin), int(ymin)], [int(xmax), int(ymax)]]
                })
                class_id = self.label_id_map.get(cls_eng, 0)
                yolo_box = self._convert_bbox_to_yolo([xmin, ymin, xmax, ymax], w, h)
                line = f"{class_id} {' '.join(f'{v:.6f}' for v in yolo_box)}"
                yolo_lines.append(line)
        
                cv2.rectangle(od_overlay, (int(xmin), int(ymin)), (int(xmax), int(ymax)), color, 8)
                bx_c = int((xmin + xmax) / 2)
                by_c = int((ymin + ymax) / 2)
                cv2.putText(od_overlay, cls_eng, (bx_c, by_c), cv2.FONT_HERSHEY_SIMPLEX, 2, color, 8)
                voc_bboxes.append((cls_eng, int(xmin), int(ymin), int(xmax), int(ymax)))
        
            od_overlay_file = paths["object_overlay_image"] / f"{img_file.stem}_overlay.jpg"
            self._imencode_tofile(od_overlay, od_overlay_file)
            od_labelme = json_data.copy()
            od_labelme["shapes"] = rect_shapes_list
            od_json_file = paths["object_labelme_format"] / f"{img_file.stem}.json"
            with od_json_file.open("w", encoding="utf-8") as fw:
                json.dump(od_labelme, fw, ensure_ascii=False, indent=2)
            od_yolo_file = paths["object_yolo_format"] / f"{img_file.stem}.txt"
            with od_yolo_file.open("w", encoding="utf-8") as fw:
                fw.write("\n".join(yolo_lines))
            if voc_bboxes:
                self._create_voc_xml(img_file.stem, w, h, voc_bboxes, voc_file)
            else:
                logging.warning(f"⚠️ {img_file.name} 沒有有效的 VOC 標註，voc_bboxes 為空")
            return True
        
        else:
            logging.warning(f"⚠️ {img_file.name} 沒有 polygon 或 rectangle 標註")
            return False


    def _calculate_statistics(self):
        """
        從輸出資料夾掃描 JSON 來統計標籤數量
        回傳:
           segment_count: {標籤名稱: segment 標籤數量}
           object_count:  {標籤名稱: object 標籤數量}
           multi_label_detail: 多標籤影像中，各標籤出現次數
        """
        segment_count = {}
        object_count = {}
        multi_label_detail = {}
        out_dir_path = Path(self.out_dir)

        for label_dir in out_dir_path.iterdir():
            if not label_dir.is_dir():
                continue

            seg_json_folder = label_dir / "segment_label" / "labelme_format"
            obj_json_folder = label_dir / "object_detect_label" / "labelme_format"

            count_seg = len(list(seg_json_folder.glob("*.json"))) if seg_json_folder.exists() else 0
            count_obj = len(list(obj_json_folder.glob("*.json"))) if obj_json_folder.exists() else 0

            # 如果不是多標籤的群組，就直接記錄 segment 與 object 的數量
            if label_dir.name != "multi_label_多類別缺陷":
                segment_count[label_dir.name] = count_seg
                object_count[label_dir.name] = count_obj
            else:
                # 對於多標籤影像，以 segment 的 JSON 為主來累計每個標籤出現次數
                for json_file in seg_json_folder.glob("*.json"):
                    try:
                        data = json.loads(json_file.read_text(encoding="utf-8"))
                        shapes = data.get("shapes", [])
                        for shape in shapes:
                            lbl = shape.get("label", "unknown")
                            multi_label_detail[lbl] = multi_label_detail.get(lbl, 0) + 1
                    except Exception as e:
                        logging.error(f"統計 {json_file} 時發生錯誤: {e}")

        return segment_count, object_count, multi_label_detail


    def _get_combined_label(self, eng: str) -> str:
        """生成 英文_中文 標籤，例如 Alligator_crack -> Alligator_crack_龜裂"""
        zh = self.class_mapping.get(eng, "unknown")
        return f"{eng}_{zh}"

    def _make_dirs(self, base_dir: Path, label_name: str):
        root = base_dir / label_name
        root.mkdir(parents=True, exist_ok=True)

        paths = {}
        # original
        orig_dir = root / "original"
        orig_dir.mkdir(exist_ok=True)
        paths["original"] = orig_dir

        # segment_label
        seg_root = root / "segment_label"
        for sub in ["mask_format", "overlay_image", "labelme_format", "yolo_format"]:
            p = seg_root / sub
            p.mkdir(parents=True, exist_ok=True)
            paths[f"segment_{sub}"] = p

        # object_detect_label
        obj_root = root / "object_detect_label"
        for sub in ["overlay_image", "labelme_format", "yolo_format", "voc_format"]:
            p = obj_root / sub
            p.mkdir(parents=True, exist_ok=True)
            paths[f"object_{sub}"] = p

        return paths

    def _imdecode_unicode(self, path: Path):
        arr = np.fromfile(str(path), dtype=np.uint8)
        if arr.size == 0:
            return None
        return cv2.imdecode(arr, cv2.IMREAD_COLOR)

    def _imencode_tofile(self, img, out_path: Path):
        out_path.parent.mkdir(parents=True, exist_ok=True)
        encode_params = [int(cv2.IMWRITE_JPEG_QUALITY), self.img_save_quality]
        ret, buf = cv2.imencode(".jpg", img, encode_params)
        if ret:
            buf.tofile(str(out_path.with_suffix(".jpg")))

    def _parse_label_english_only(self, full_label: str) -> str:
        if full_label in self.class_mapping:
            return full_label
        if "_" in full_label:
            no_chinese = full_label.rsplit("_", 1)[0]
            if no_chinese in self.class_mapping:
                return no_chinese
            else:
                fallback = no_chinese.split("_")[0]
                return fallback if fallback in self.class_mapping else "unknown"
        else:
            return full_label if full_label in self.class_mapping else "unknown"

    def _polygon_to_bbox(self, pts: np.ndarray):
        x_min, y_min = pts.min(axis=0)
        x_max, y_max = pts.max(axis=0)
        return float(x_min), float(y_min), float(x_max), float(y_max)

    def _convert_bbox_to_yolo(self, bbox, img_w, img_h):
        xmin, ymin, xmax, ymax = bbox
        cx = ((xmin + xmax) / 2) / img_w
        cy = ((ymin + ymax) / 2) / img_h
        bw = (xmax - xmin) / img_w
        bh = (ymax - ymin) / img_h
        return [cx, cy, bw, bh]

    def _create_voc_xml(self, base_name, w, h, boxes, out_path: Path):
        root = ET.Element("annotation")
        ET.SubElement(root, "folder").text = "images"
        ET.SubElement(root, "filename").text = base_name + ".jpg"
        sz = ET.SubElement(root, "size")
        ET.SubElement(sz, "width").text = str(w)
        ET.SubElement(sz, "height").text = str(h)
        ET.SubElement(sz, "depth").text = "3"
        for cls_name, xmin, ymin, xmax, ymax in boxes:
            obj = ET.SubElement(root, "object")
            ET.SubElement(obj, "name").text = cls_name
            bnd = ET.SubElement(obj, "bndbox")
            ET.SubElement(bnd, "xmin").text = str(int(xmin))
            ET.SubElement(bnd, "ymin").text = str(int(ymin))
            ET.SubElement(bnd, "xmax").text = str(int(xmax))
            ET.SubElement(bnd, "ymax").text = str(int(ymax))
        tree = ET.ElementTree(root)
        tree.write(out_path, encoding='utf-8', xml_declaration=True)

    def _convert_seg_labelme_to_yolo(self, json_data, img_w, img_h):
        lines = []
        shapes = json_data.get("shapes", [])
        for shape in shapes:
            if shape.get("shape_type") != "polygon":
                continue
            label = shape["label"]
            cls_eng = self._parse_label_english_only(label)
            class_id = self.label_id_map.get(cls_eng, 0)
            pts = shape["points"]
            
            # ✅ 處理首尾重複點
            if len(pts) > 2 and pts[0] == pts[-1]:
                pts = pts[:-1]
            
            normalized_points = []
            for pt in pts:
                nx = pt[0] / img_w
                ny = pt[1] / img_h
                normalized_points.append(f"{nx:.6f}")
                normalized_points.append(f"{ny:.6f}")
            line = f"{class_id} " + " ".join(normalized_points)
            lines.append(line)
        return lines


# ============ 主程式入口 ============
if __name__ == "__main__":
    logging.info("看看這行有沒有記錄到檔案？")
    # 1) 設定各資料夾
    image_dirs = [
        r"C:\Users\<USER>\Downloads\hole\labelme\images",
    
    ]
    json_dirs = image_dirs.copy()

    save_dir = r"D:\路面缺陷資料集\全部資料集\資料集_已處理\資料集_已處理_多類別_完整V3_公司1&2"
    
    # 設定是否要縮放圖片大小（None 表示不縮放）
    resize_size = (1200, 900)  # ← 你可以換成 None 表示不縮放
    # 2) 建立物件 & 執行
    processor = RoadDataProcessor(
        image_dirs=image_dirs,
        json_dirs=json_dirs,
        out_dir=save_dir,
        max_count=999999,
        img_save_quality=75,# 75就OK，最低可以設成更15
        resize_to=resize_size 
    )
    processor.run()
    logging.info("程式準備結束...")
    logging.shutdown()
