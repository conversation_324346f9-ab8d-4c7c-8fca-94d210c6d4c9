#!/usr/bin/env python3
"""
簡化的YOLO推理系統 - 主要API介面
這是使用者的主要入口點，提供簡潔易用的API
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from .config.yolo_config import SimplifiedYOLOConfig, set_global_class_names, set_global_label_aliases
from .config.class_detector import print_class_summary
from .core.inference_engine import SimplifiedYOLOInference
from .core.batch_processor import BatchProcessor


class SimplifiedYOLO:
    """
    簡化的YOLO推理系統
    
    提供簡潔的API，專注於核心功能：
    - 單張圖像推理
    - 批次推理
    - 自動類別檢測
    - 基本的SAHI支援
    """
    
    def __init__(self, config: Optional[SimplifiedYOLOConfig] = None):
        """
        初始化簡化YOLO系統
        
        Args:
            config: 配置對象，如果為None則使用預設配置
        """
        self.config = config or SimplifiedYOLOConfig()
        self.inference_engine = SimplifiedYOLOInference(self.config)
        self.batch_processor = BatchProcessor(self.inference_engine)
    
    @classmethod
    def from_model(cls, model_path: str, 
                   confidence: float = 0.25,
                   device: str = "auto",
                   enable_sahi: bool = False) -> 'SimplifiedYOLO':
        """
        從模型文件創建SimplifiedYOLO實例
        
        Args:
            model_path: 模型文件路徑
            confidence: 置信度閾值
            device: 設備 ("auto", "cuda", "cpu")
            enable_sahi: 是否啟用SAHI
            
        Returns:
            SimplifiedYOLO: 配置好的實例
        """
        config = SimplifiedYOLOConfig(
            segmentation_model_path=model_path,
            global_conf=confidence,
            device=device,
            enable_sahi=enable_sahi
        )
        return cls(config)
    
    @classmethod
    def detection_only(cls, model_path: str,
                      confidence: float = 0.5,
                      device: str = "auto") -> 'SimplifiedYOLO':
        """
        創建僅檢測的SimplifiedYOLO實例
        
        Args:
            model_path: 檢測模型路徑
            confidence: 置信度閾值
            device: 設備
            
        Returns:
            SimplifiedYOLO: 檢測專用實例
        """
        config = SimplifiedYOLOConfig(
            detection_model_path=model_path,
            global_conf=confidence,
            device=device
        )
        return cls(config)
    
    @classmethod
    def with_sahi(cls, model_path: str,
                  slice_size: int = 512,
                  overlap_ratio: float = 0.2,
                  confidence: float = 0.1,
                  device: str = "auto") -> 'SimplifiedYOLO':
        """
        創建啟用SAHI的SimplifiedYOLO實例
        
        Args:
            model_path: 模型路徑
            slice_size: 切片大小
            overlap_ratio: 重疊比例
            confidence: 置信度閾值
            device: 設備
            
        Returns:
            SimplifiedYOLO: SAHI專用實例
        """
        config = SimplifiedYOLOConfig(
            segmentation_model_path=model_path,
            enable_sahi=True,
            slice_size=slice_size,
            overlap_ratio=overlap_ratio,
            sahi_conf=confidence,
            global_conf=confidence,
            device=device
        )
        return cls(config)
    
    def predict(self, image_path: str, 
                save_to: Optional[str] = None,
                task: str = "auto") -> Dict[str, Any]:
        """
        對單張圖像進行推理
        
        Args:
            image_path: 圖像路徑
            save_to: 保存目錄（可選）
            task: 任務類型 ("detection", "segmentation", "auto")
            
        Returns:
            Dict: 推理結果
        """
        return self.inference_engine.predict_single_image(
            image_path=image_path,
            output_dir=save_to,
            task_type=task
        )
    
    def predict_batch(self, input_dir: str,
                     output_dir: str,
                     task: str = "auto") -> List[Dict[str, Any]]:
        """
        批次推理目錄中的所有圖像
        
        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            task: 任務類型
            
        Returns:
            List[Dict]: 批次推理結果
        """
        return self.batch_processor.process_directory(
            input_dir=input_dir,
            output_dir=output_dir,
            task_type=task
        )
    
    def predict_with_gt(self, image_path: str,
                       annotation_path: str,
                       output_dir: str,
                       task: str = "auto") -> Dict[str, Any]:
        """
        對帶有ground truth的圖像進行推理
        
        Args:
            image_path: 圖像路徑
            annotation_path: 標註文件路徑
            output_dir: 輸出目錄
            task: 任務類型
            
        Returns:
            Dict: 包含預測和ground truth的結果
        """
        return self.batch_processor.process_single_with_ground_truth(
            image_path=image_path,
            annotation_path=annotation_path,
            output_dir=output_dir,
            task_type=task
        )
    
    def set_confidence(self, confidence: float):
        """設置全域置信度閾值"""
        self.config.global_conf = confidence
        # 更新所有類別的置信度
        for class_config in self.config.class_configs.values():
            class_config.conf_threshold = confidence
    
    def set_class_confidence(self, class_name: str, confidence: float):
        """設置特定類別的置信度閾值"""
        for class_config in self.config.class_configs.values():
            if class_config.name == class_name:
                class_config.conf_threshold = confidence
                return
        print(f"⚠️ 未找到類別: {class_name}")
    
    def enable_sahi(self, slice_size: int = 512, overlap_ratio: float = 0.2):
        """啟用SAHI功能"""
        self.config.enable_sahi = True
        self.config.slice_size = slice_size
        self.config.overlap_ratio = overlap_ratio
        # 重新設置SAHI模型
        self.inference_engine._setup_sahi()
    
    def disable_sahi(self):
        """禁用SAHI功能"""
        self.config.enable_sahi = False
        self.inference_engine.sahi_model = None
    
    def get_classes(self) -> Dict[int, str]:
        """獲取類別映射"""
        return {
            class_id: config.name 
            for class_id, config in self.config.class_configs.items()
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取推理統計信息"""
        return self.inference_engine.get_stats()
    
    def reset_stats(self):
        """重置統計信息"""
        self.inference_engine.reset_stats()
    
    def print_config(self):
        """顯示當前配置"""
        print(self.config.summary())
    
    def print_classes(self):
        """顯示類別配置摘要"""
        print_class_summary(self.config.class_configs)
    
    def save_config(self, config_path: str):
        """保存配置到文件"""
        import json
        config_dict = {
            'detection_model_path': self.config.detection_model_path,
            'segmentation_model_path': self.config.segmentation_model_path,
            'device': self.config.device,
            'img_size': self.config.img_size,
            'global_conf': self.config.global_conf,
            'iou_threshold': self.config.iou_threshold,
            'max_det': self.config.max_det,
            'enable_sahi': self.config.enable_sahi,
            'slice_size': self.config.slice_size,
            'overlap_ratio': self.config.overlap_ratio,
            'sahi_conf': self.config.sahi_conf,
            'save_visualizations': self.config.save_visualizations,
            'save_predictions': self.config.save_predictions,
            'save_statistics': self.config.save_statistics,
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存到: {config_path}")
    
    @classmethod
    def load_config(cls, config_path: str) -> 'SimplifiedYOLO':
        """從文件載入配置"""
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        config = SimplifiedYOLOConfig(**config_dict)
        return cls(config)


# 便利函數
def create_yolo(model_path: str, **kwargs) -> SimplifiedYOLO:
    """
    快速創建YOLO推理實例
    
    Args:
        model_path: 模型路徑
        **kwargs: 其他配置參數
        
    Returns:
        SimplifiedYOLO: 配置好的實例
    """
    return SimplifiedYOLO.from_model(model_path, **kwargs)


def quick_predict(model_path: str, image_path: str, **kwargs) -> Dict[str, Any]:
    """
    快速單張圖像推理
    
    Args:
        model_path: 模型路徑
        image_path: 圖像路徑
        **kwargs: 其他配置參數
        
    Returns:
        Dict: 推理結果
    """
    yolo = SimplifiedYOLO.from_model(model_path, **kwargs)
    return yolo.predict(image_path)


def quick_batch(model_path: str, input_dir: str, output_dir: str, **kwargs) -> List[Dict[str, Any]]:
    """
    快速批次推理
    
    Args:
        model_path: 模型路徑
        input_dir: 輸入目錄
        output_dir: 輸出目錄
        **kwargs: 其他配置參數
        
    Returns:
        List[Dict]: 批次推理結果
    """
    yolo = SimplifiedYOLO.from_model(model_path, **kwargs)
    return yolo.predict_batch(input_dir, output_dir)


# 全域配置函數
def configure_global_classes(class_names: List[str]):
    """配置全域類別名稱"""
    set_global_class_names(class_names)
    print(f"✅ 已設置全域類別: {len(class_names)} 個")


def configure_label_aliases(aliases: Dict[str, str]):
    """配置標籤別名映射"""
    set_global_label_aliases(aliases)
    print(f"✅ 已設置標籤別名: {len(aliases)} 個映射")


# 模組版本信息
__version__ = "1.0.0"
__author__ = "Road AI Framework Team"
__description__ = "Simplified YOLO inference system for road infrastructure detection"