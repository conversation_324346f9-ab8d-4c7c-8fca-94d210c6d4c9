#!/usr/bin/env python3
"""
Enhanced YOLO使用示例
展示如何使用Enhanced YOLO進行道路基礎設施檢測
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

# ===================================================================
# 🔧 類別配置輔助函數
# ===================================================================

def setup_manual_class_configs_detailed():
    """
    方式1: 手動詳細配置 - 完全控制每個類別的所有屬性
    適用場景: 需要精確控制每個類別檢測參數的情況
    """
    from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
        create_manual_class_configs
    )
    
    # 詳細類別定義 - 根據道路基礎設施檢測特性調整
    class_definitions = [
        {
            "name": "linear_crack_裂縫",
            "conf_threshold": 0.25,  # 裂縫容易漏檢，使用較低閾值
            "color": (255, 0, 0),    # 紅色 - 嚴重問題
            "enabled": True,
            "description": "線性裂縫，容易漏檢所以使用低閾值"
        },
        {
            "name": "Alligator_crack_龜裂",
            "conf_threshold": 0.35,  # 龜裂比線性裂縫稍容易檢測
            "color": (255, 165, 0),  # 橙色 - 嚴重損傷
            "enabled": True,
            "description": "龜裂損傷，形狀複雜但特徵明顯"
        },
        {
            "name": "potholes_坑洞", 
            "conf_threshold": 0.7,   # 坑洞誤檢代價高，使用較高閾值
            "color": (220, 20, 60),  # 深紅色 - 最嚴重問題
            "enabled": True,
            "description": "坑洞檢測，誤檢代價高所以使用高閾值"
        },
        {
            "name": "joint_路面接縫",
            "conf_threshold": 0.4,   # 接縫使用中等閾值
            "color": (0, 0, 255),    # 藍色 - 維護類問題
            "enabled": True,
            "description": "路面接縫，正常維護項目"
        },
        {
            "name": "expansion_joint_伸縮縫",
            "conf_threshold": 0.45,  # 伸縮縫較容易識別
            "color": (0, 255, 255),  # 青色 - 功能性結構
            "enabled": True,
            "description": "伸縮縫，功能性道路結構"
        }
    ]
    
    return create_manual_class_configs(class_definitions)


def setup_manual_class_configs_quick():
    """
    方式2: 快速手動配置 - 僅設定類別名稱和置信度
    適用場景: 快速設置，主要關注置信度調整
    """
    from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
        create_quick_class_configs
    )
    
    # 道路基礎設施類別名稱（按檢測難度排序）
    class_names = [
        "expansion_joint_伸縮縫",
        "joint_路面接縫", 
        "linear_crack_裂縫",
        "Alligator_crack_龜裂",
        "potholes_坑洞"
    ]
    
    # 對應的置信度閾值（根據檢測難度和誤檢代價調整）
    conf_thresholds = [
        0.45,  # 伸縮縫：功能結構，中等閾值
        0.4,   # 路面接縫：常見特徵，中等閾值
        0.25,  # 線性裂縫：容易漏檢，低閾值提高敏感度
        0.35,  # 龜裂：複雜形狀，稍低閾值
        0.7    # 坑洞：誤檢代價高，高閾值確保準確性
    ]
    
    return create_quick_class_configs(class_names, conf_thresholds)


def setup_balanced_class_configs():
    """
    方式3: 平衡配置 - 根據檢測難度自動設定
    適用場景: 基於檢測難度的智能配置
    """
    from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
        create_balanced_class_configs
    )
    
    # 道路基礎設施類別
    class_names = [
        "expansion_joint_伸縮縫",
        "joint_路面接縫",
        "linear_crack_裂縫", 
        "Alligator_crack_龜裂",
        "potholes_坑洞"
    ]
    
    # 基於實際檢測經驗的難度評估
    difficulty_levels = [
        "medium",  # 伸縮縫：形狀規則，中等難度
        "medium",  # 路面接縫：常見特徵，中等難度  
        "hard",    # 線性裂縫：細小特徵，困難檢測
        "medium",  # 龜裂：形狀複雜但面積較大
        "easy"     # 坑洞：明顯凹陷，容易檢測
    ]
    
    return create_balanced_class_configs(class_names, difficulty_levels)


def apply_confidence_adjustments(class_configs, adjustments):
    """
    應用個別置信度調整
    """
    if not adjustments:
        return class_configs
        
    from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
        modify_class_confidence
    )
    
    print("🎯 應用個別置信度調整:")
    return modify_class_confidence(class_configs, adjustments)

def main():
    """Enhanced YOLO使用示例主函數"""
    
    print("🚀 Enhanced YOLO使用示例")
    print("=" * 50)
    
    # 🔧 啟用詳細日誌以診斷GT載入問題
    import logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    print("✅ 已啟用詳細日誌，請注意觀察GT載入過程！")
    
    # ===================================================================
    # 📋 參數設定區域 - 請根據您的需求修改以下參數
    # ===================================================================
    
    # 🎯 基礎配置
    detection_model_path = ""                      # 檢測模型路徑 (YOLO12) - 空字符串則跳過檢測
    segmentation_model_path = r"D:\4_road_crack\best.pt"                   # 分割模型路徑 (YOLO11) - 測試GT顯示時可留空
    input_path = r"D:\image\5_test_image_test\test"             # 輸入圖像或目錄路徑
    # input_path = r"D:\image\5_test_image\test_2_org"             # 輸入圖像或目錄路徑
    # input_path = r"D:\image\5_test_image\test_1"             # 輸入圖像或目錄路徑
    output_path = r"D:\image\5_test_image_test\test_6448_sahi"               # 輸出目錄路徑
    # output_path = r"D:\image\5_test_image_test\test_100"               # 輸出目錄路徑
    task_type = "segmentation"                     # 任務類型: "detection", "segmentation", "both"
    
    # 🎛️ 推理參數
    img_size = 640                                 # 圖像大小
    global_conf = 0.05                             # 全局置信度閾值
    iou_threshold = 0.35                           # IoU閾值
    max_det = 1000                                 # 最大檢測數量
    device = "cuda"                                # 設備: "auto", "cuda", "cpu"
    
    # 🧩 SAHI配置 (大圖像切片推理) - 完整參數設定
    enable_sahi = True                             # 是否啟用SAHI
    
    # 核心切片參數
    slice_height = 640                             # 切片高度
    slice_width = 640                              # 切片寬度
    overlap_height_ratio = 0.3                     # 高度重疊比例 (0.0-1.0)
    overlap_width_ratio = 0.3                      # 寬度重疊比例 (0.0-1.0)
    
    # 進階SAHI參數
    auto_slice_resolution = False                    # 自動切片解析度
    perform_standard_pred = True                    # 執行標準預測
    roi_ratio = (0, 0.3, 1, 0.7)              # ROI區域比例 (x1, y1, x2, y2)
    
    # 後處理參數
    postprocess_type = "GREEDYNMM"                 # 後處理類型: "GREEDYNMM", "NMM", "NMS"
    postprocess_match_threshold = 0.2               # 後處理匹配閾值
    postprocess_class_agnostic = False             # 類別無關後處理
    
    # 類別過濾參數
    exclude_classes_by_name = []                    # 按名稱排除類別 ["unwanted_class"]
    exclude_classes_by_id = []                      # 按ID排除類別 [0, 1]
    
    # 輸出控制
    no_standard_prediction = False                  # 禁用標準預測
    no_sliced_prediction = False                    # 禁用切片預測
    export_pickle = False                           # 導出pickle格式
    export_crop = False                             # 導出裁剪圖像
    
    # 🤝 Simple Tool功能整合 - 雙模型協同
    secondary_detection_model_path = ""             # 副檢測模型路徑
    secondary_segmentation_model_path = ""          # 副分割模型路徑
    enable_dual_model_consensus = False             # 啟用雙模型共識機制
    consensus_threshold = 0.05                       # 共識閾值
    consensus_iou_threshold = 0.05                   # 共識IoU閾值
    
    # 🧠 Simple Tool功能整合 - 智能檢測過濾
    enable_intelligent_filtering = True            # 啟用智能過濾 (linear vs alligator, linear vs joint)
    enable_detection_merge = True                  # 啟用檢測合併
    iou_merge_threshold = 0.05                       # 合併IoU閾值
    
    # 🎯 Simple Tool功能整合 - 選擇性類別保存
    target_classes = None                           # 指定保存類別 [1, 2, 3] 或 None=保存全部
    save_all_when_target_found = True               # 找到目標類別時是否保存全部結果
    skip_empty_results = True                       # 跳過空檢測結果
    
    # 📊 輸出配置
    save_visualizations = True                     # 保存可視化結果
    save_predictions = True                        # 保存預測結果
    save_statistics = True                         # 保存統計信息
    
    # 🏷️ LabelMe自動配置 (如果有LabelMe標註)
    labelme_dir = r"D:\image\5_test_image_test\test"            # LabelMe標註目錄，空則使用預設類別
    # labelme_dir = r"D:\image\5_test_image\test_2_org"            # LabelMe標註目錄，空則使用預設類別
    auto_generate_classes = True                   # 是否自動從LabelMe生成類別配置
    
    # ===================================================================
    # 🏷️ 類別配置選項 - 五種配置方式任選其一
    # ===================================================================
    
    # 📋 配置方式選擇 ("auto", "manual_detailed", "manual_quick", "balanced", "modify_existing")
    class_config_method = "auto"
    
    # 🎯 個別類別置信度微調（可選，適用於所有配置方式）
    individual_confidence_adjustments = {
        # "類別名稱": 新置信度閾值
        "linear_crack": 0.1,       # 
        "potholes": 0.1,           # 
        "joint": 0.4,          #  0.4
        "Alligator_crack": 0.1,     # 0.15
        "potholes": 0.1,           # 
        "patch": 0.4,               # 0.4
        "manhole": 0.1,     #        
        "deformation": 0.1,     #
        "dirt": 0.1,                #
        "lane_line_linear": 0.1,    # 
        "expansion_joint": 0.1

    }
    
    # ===================================================================
    # 📚 類別配置方法說明
    # ===================================================================
    """
    🔧 五種類別配置方法:
    
    1. "auto" - 自動從LabelMe生成 (推薦)
       優點: 自動化，基於實際標註數據
       適用: 有LabelMe標註文件的情況
       
    2. "manual_detailed" - 手動詳細配置
       優點: 完全控制，可設定顏色、描述等
       適用: 需要精確控制的專業應用
       
    3. "manual_quick" - 手動快速配置  
       優點: 簡單快速，僅需名稱和閾值
       適用: 快速原型和測試
       
    4. "balanced" - 平衡難度配置
       優點: 智能化，基於檢測難度
       適用: 不確定閾值設定的情況
       
    5. "modify_existing" - 修改現有配置
       優點: 在自動配置基礎上微調
       適用: 需要在自動配置基礎上調整
       
    🎯 置信度閾值設定建議:
    - 裂縫類 (linear_crack): 0.2-0.3 (容易漏檢，使用低閾值)
    - 龜裂類 (alligator_crack): 0.3-0.4 (形狀複雜但面積大)
    - 坑洞類 (potholes): 0.6-0.8 (誤檢代價高，使用高閾值)
    - 接縫類 (joint): 0.4-0.5 (常見特徵，中等閾值)
    - 伸縮縫 (expansion_joint): 0.4-0.6 (功能結構，中等閾值)
    """
    
    # 🎨 進階配置
    batch_processing = True                        # 批次處理 (True=目錄, False=單張圖像)
    enable_tracking = False                        # 物件追蹤
    
    # ===================================================================
    # 🔍 執行檢查和驗證
    # ===================================================================
    
    if not ENHANCED_YOLO_AVAILABLE:
        print("❌ Enhanced YOLO模組不可用")
        print("請檢查import_helper.py和enhanced_yolo_inference.py")
        return
    
    # 檢查輸入路徑
    if not Path(input_path).exists():
        print(f"❌ 錯誤: 輸入路徑不存在: {input_path}")
        print("請修改input_path參數或創建對應目錄")
        return
    
    # 檢查模型路徑 (測試GT顯示時可以沒有模型)
    if not detection_model_path and not segmentation_model_path:
        print("⚠️ 注意: 沒有指定模型路徑，將只顯示GT標註")
    
    if segmentation_model_path and not Path(segmentation_model_path).exists():
        print(f"⚠️ 分割模型路徑不存在: {segmentation_model_path}")
        print("將只顯示GT標註，不進行預測")
        segmentation_model_path = ""
    
    # ===================================================================
    # 🚀 執行Enhanced YOLO推理
    # ===================================================================
    
    try:
        # 導入Enhanced YOLO相關模組
        from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
            EnhancedYOLOConfig, 
            create_enhanced_yolo_inference,
            scan_labelme_annotations,
            generate_class_configs_from_labelme,
            validate_and_display_class_configs
        )
        
        print("✅ Enhanced YOLO模組導入成功")
        
        # ===================================================================
        # 🏷️ 類別配置處理 - 根據選擇的方法創建配置
        # ===================================================================
        
        print(f"🔧 使用類別配置方法: {class_config_method}")
        class_configs = {}
        
        if class_config_method == "auto":
            # 方式1: 自動從LabelMe生成
            if labelme_dir and Path(labelme_dir).exists() and auto_generate_classes:
                print(f"🏷️  正在掃描LabelMe標註: {labelme_dir}")
                try:
                    class_configs = generate_class_configs_from_labelme(labelme_dir)
                    print(f"✅ 自動生成了 {len(class_configs)} 個類別配置")
                except Exception as e:
                    print(f"⚠️  LabelMe掃描失敗: {e}")
                    print("   將使用預設類別配置")
            else:
                print("⚠️  自動配置失敗，LabelMe目錄不存在或未啟用")
                
        elif class_config_method == "manual_detailed":
            # 方式2: 手動詳細配置
            print("🎯 使用手動詳細配置")
            class_configs = setup_manual_class_configs_detailed()
            print(f"✅ 手動配置了 {len(class_configs)} 個類別（詳細模式）")
            
        elif class_config_method == "manual_quick":
            # 方式3: 手動快速配置
            print("⚡ 使用手動快速配置")
            class_configs = setup_manual_class_configs_quick()
            print(f"✅ 快速配置了 {len(class_configs)} 個類別")
            
        elif class_config_method == "balanced":
            # 方式4: 平衡配置
            print("⚖️  使用平衡難度配置")
            class_configs = setup_balanced_class_configs()
            print(f"✅ 平衡配置了 {len(class_configs)} 個類別")
            
        elif class_config_method == "modify_existing":
            # 方式5: 修改現有配置
            if labelme_dir and Path(labelme_dir).exists():
                print("🔄 先自動生成，然後修改配置")
                try:
                    class_configs = generate_class_configs_from_labelme(labelme_dir)
                    print(f"✅ 自動生成了 {len(class_configs)} 個類別配置")
                except Exception as e:
                    print(f"⚠️  LabelMe掃描失敗，使用預設配置: {e}")
            else:
                print("⚠️  LabelMe目錄不存在，使用預設配置")
        
        else:
            print(f"❌ 未知的配置方法: {class_config_method}")
            print("   使用預設配置")
        
        # 應用個別置信度調整（適用於所有配置方法）
        if individual_confidence_adjustments:
            print("\n🎯 應用個別置信度調整...")
            class_configs = apply_confidence_adjustments(class_configs, individual_confidence_adjustments)
        
        # 驗證和顯示最終配置
        if class_configs:
            print("\n" + "="*60)
            validate_and_display_class_configs(class_configs)
            print("="*60)
        else:
            print("⚠️  沒有類別配置，將使用系統預設設定")
        
        # 創建配置對象
        config = EnhancedYOLOConfig(
            # 模型配置
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            device=device,
            
            # 推理配置
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            
            # 類別配置
            class_configs=class_configs,
            
            # SAHI配置 - 完整23個參數
            enable_sahi=enable_sahi,
            slice_height=slice_height,
            slice_width=slice_width,
            overlap_height_ratio=overlap_height_ratio,
            overlap_width_ratio=overlap_width_ratio,
            auto_slice_resolution=auto_slice_resolution,
            perform_standard_pred=perform_standard_pred,
            roi_ratio=roi_ratio,
            postprocess_type=postprocess_type,
            postprocess_match_threshold=postprocess_match_threshold,
            postprocess_class_agnostic=postprocess_class_agnostic,
            exclude_classes_by_name=exclude_classes_by_name,
            exclude_classes_by_id=exclude_classes_by_id,
            no_standard_prediction=no_standard_prediction,
            no_sliced_prediction=no_sliced_prediction,
            export_pickle=export_pickle,
            export_crop=export_crop,
            
            # Simple Tool功能整合 - 雙模型協同
            secondary_detection_model_path=secondary_detection_model_path,
            secondary_segmentation_model_path=secondary_segmentation_model_path,
            enable_dual_model_consensus=enable_dual_model_consensus,
            consensus_threshold=consensus_threshold,
            consensus_iou_threshold=consensus_iou_threshold,
            
            # Simple Tool功能整合 - 智能檢測過濾
            enable_intelligent_filtering=enable_intelligent_filtering,
            enable_detection_merge=enable_detection_merge,
            iou_merge_threshold=iou_merge_threshold,
            
            # Simple Tool功能整合 - 選擇性類別保存
            target_classes=target_classes,
            save_all_when_target_found=save_all_when_target_found,
            skip_empty_results=skip_empty_results,
            
            # 輸出配置
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics,
            
            # 進階功能
            enable_tracking=enable_tracking,
            auto_convert_annotations=True
        )
        
        print("🔧 創建推理器...")
        inference = create_enhanced_yolo_inference(config)
        print("✅ 推理器創建成功")
        
        # 創建輸出目錄
        Path(output_path).mkdir(parents=True, exist_ok=True)
        
        print("📊 配置信息:")
        print(f"   🎯 任務類型: {task_type}")
        print(f"   📁 輸入: {input_path}")
        print(f"   📁 輸出: {output_path}")
        print(f"   🎛️  圖像大小: {img_size}")
        print(f"   🎛️  置信度: {global_conf}")
        print(f"   🧩 SAHI: {'啟用' if enable_sahi else '禁用'}")
        if class_configs:
            print(f"   🏷️  類別數: {len(class_configs)}")
        
        print("\\n🚀 開始推理...")
        
        if batch_processing and Path(input_path).is_dir():
            # 批次處理
            print(f"📂 批次處理目錄: {input_path}")
            
            # 檢查是否有標註文件
            input_dir = Path(input_path)
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
            annotation_extensions = ['.json', '.txt', '.xml']
            
            # 統計圖像和標註文件
            image_files = []
            for ext in image_extensions:
                image_files.extend(input_dir.glob(f"*{ext}"))
                image_files.extend(input_dir.glob(f"*{ext.upper()}"))
            
            annotation_files = []
            for ext in annotation_extensions:
                annotation_files.extend(input_dir.glob(f"*{ext}"))
                annotation_files.extend(input_dir.glob(f"*{ext.upper()}"))
            
            has_labels = len(annotation_files) > 0
            
            results = inference.batch_predict(
                input_dir=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            
            # 根據是否有標註文件調整顯示數量
            processed_count = len(results) if results else 0
            if has_labels and processed_count > 0:
                print(f"✅ 批次處理完成! 處理了 {processed_count} 組數據 (圖像+標註)")
            else:
                print(f"✅ 批次處理完成! 處理了 {processed_count} 張圖像")
            
        else:
            # 單張圖像處理
            print(f"🖼️  處理單張圖像: {input_path}")
            results = inference.predict_single_image(
                image_path=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            print("✅ 單張圖像處理完成!")
        
        # 顯示統計信息
        if hasattr(inference, 'inference_stats'):
            stats = inference.inference_stats
            print("\\n📊 統計信息:")
            print(f"   🎯 總檢測數: {stats.get('total_detections', 0)}")
            print(f"   📊 類別統計: {stats.get('class_counts', {})}")
            print(f"   ⏱️  處理時間: {stats.get('total_time', 0):.2f}秒")
        
        print(f"\\n🎉 推理完成! 結果保存至: {output_path}")
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保Enhanced YOLO相關依賴已安裝:")
        print("   pip install torch torchvision ultralytics")
        print("   pip install opencv-python numpy matplotlib")
        print("   pip install sahi")
        
    except Exception as e:
        print(f"❌ 推理失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()