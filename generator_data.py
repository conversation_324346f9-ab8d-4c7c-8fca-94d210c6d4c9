import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import random
import json
from shapely.geometry import Polygon, box
import tempfile


# 設置繪圖樣式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False


def imread_unicode(path, flags=cv2.IMREAD_COLOR):
    # 先用 fromfile 讀取二進位內容，再使用 imdecode
    # 可避免 Windows 路徑編碼問題
    if not os.path.exists(path):
        print(f"檔案不存在: {path}")
        return None
    with open(path, 'rb') as f:
        filedata = f.read()
    img_array = np.asarray(bytearray(filedata), dtype=np.uint8)
    return cv2.imdecode(img_array, flags)


class ImageRegionTransfer:
    """
    將一張圖像中的標籤區域轉移到另一張圖像，並生成相應的新標籤
    支持矩形框和多邊形標籤
    """

    def __init__(self, debug=False):
        """
        初始化參數

        參數:
            debug: 是否開啟調試模式，顯示中間過程
        """
        self.debug = debug

    def read_label_file(self, label_path):
        """
        讀取標籤檔案，支持矩形框和多邊形

        參數:
            label_path: 標籤檔案路徑

        返回:
            labels: 標籤列表，每個元素為 [class_id, coordinates, label_type]
                   其中 coordinates 根據 label_type 的不同有不同的格式:
                   - 'box': [x_center, y_center, width, height] (YOLO格式，歸一化座標)
                   - 'polygon': [[x1, y1], [x2, y2], ..., [xn, yn]] (歸一化座標)
        """
        labels = []

        try:
            with open(label_path, 'r') as f:
                lines = f.readlines()

            for line in lines:
                parts = line.strip().split()
                if len(parts) < 5:  # 至少需要class_id和四個座標(一個框)
                    continue

                class_id = int(parts[0])
                coordinates = [float(p) for p in parts[1:]]

                # 根據座標數量判斷是矩形框還是多邊形
                if len(coordinates) == 4:
                    # YOLO格式的矩形框: [x_center, y_center, width, height]
                    labels.append([class_id, coordinates, 'box'])
                else:
                    # 多邊形: [[x1, y1], [x2, y2], ..., [xn, yn]]
                    # 確保座標數量是偶數
                    if len(coordinates) % 2 != 0:
                        print(f"警告: 忽略不完整的多邊形座標: {coordinates}")
                        continue

                    # 將一維列表轉換為二維座標對列表
                    polygon_coords = []
                    for i in range(0, len(coordinates), 2):
                        polygon_coords.append(
                            [coordinates[i], coordinates[i+1]])

                    labels.append([class_id, polygon_coords, 'polygon'])

        except Exception as e:
            print(f"讀取標籤檔案 {label_path} 時發生錯誤: {e}")

        return labels

    def write_label_file(self, label_path, labels):
        """
        寫入標籤檔案，支持矩形框和多邊形

        參數:
            label_path: 輸出標籤檔案路徑
            labels: 標籤列表，每個元素為 [class_id, coordinates, label_type]
        """
        try:
            os.makedirs(os.path.dirname(label_path), exist_ok=True)

            with open(label_path, 'w') as f:
                for label in labels:
                    class_id, coordinates, label_type = label

                    if label_type == 'box':
                        # 矩形框: class_id x_center y_center width height
                        line = f"{int(class_id)} {coordinates[0]:.6f} {coordinates[1]:.6f} {coordinates[2]:.6f} {coordinates[3]:.6f}\n"
                        f.write(line)
                    elif label_type == 'polygon':
                        # 多邊形: class_id x1 y1 x2 y2 ... xn yn
                        coords_flat = []
                        for point in coordinates:
                            coords_flat.extend(
                                [f"{point[0]:.6f}", f"{point[1]:.6f}"])
                        line = f"{int(class_id)} {' '.join(coords_flat)}\n"
                        f.write(line)
        except Exception as e:
            print(f"寫入標籤檔案 {label_path} 時發生錯誤: {e}")

    def normalized_to_pixel_coordinates(self, label, image_width, image_height):
        """
        將歸一化座標轉換為像素座標，支持矩形框和多邊形

        參數:
            label: [class_id, coordinates, label_type]
            image_width: 圖像寬度
            image_height: 圖像高度

        返回:
            [class_id, pixel_coordinates, label_type]: 像素座標格式的標籤
        """
        class_id, coordinates, label_type = label

        if label_type == 'box':
            # 矩形框 [x_center, y_center, width, height] -> [x, y, w, h]
            x_center, y_center, width, height = coordinates

            pixel_x_center = x_center * image_width
            pixel_y_center = y_center * image_height
            pixel_width = width * image_width
            pixel_height = height * image_height

            # 轉換為左上角座標
            pixel_x = int(pixel_x_center - pixel_width / 2)
            pixel_y = int(pixel_y_center - pixel_height / 2)
            pixel_w = int(pixel_width)
            pixel_h = int(pixel_height)

            return [class_id, [pixel_x, pixel_y, pixel_w, pixel_h], label_type]

        elif label_type == 'polygon':
            # 多邊形 [[x1, y1], [x2, y2], ..., [xn, yn]] -> 像素座標
            pixel_coords = []
            for point in coordinates:
                pixel_x = int(point[0] * image_width)
                pixel_y = int(point[1] * image_height)
                pixel_coords.append([pixel_x, pixel_y])

            return [class_id, pixel_coords, label_type]

        return None

    def pixel_to_normalized_coordinates(self, label, image_width, image_height):
        """
        將像素座標轉換為歸一化座標，支持矩形框和多邊形

        參數:
            label: [class_id, pixel_coordinates, label_type]
            image_width: 圖像寬度
            image_height: 圖像高度

        返回:
            [class_id, normalized_coordinates, label_type]: 歸一化座標格式的標籤
        """
        class_id, pixel_coordinates, label_type = label

        if label_type == 'box':
            # 矩形框 [x, y, w, h] -> [x_center, y_center, width, height]
            x, y, w, h = pixel_coordinates

            # 轉換為中心點座標
            x_center = (x + w / 2) / image_width
            y_center = (y + h / 2) / image_height
            width = w / image_width
            height = h / image_height

            return [class_id, [x_center, y_center, width, height], label_type]

        elif label_type == 'polygon':
            # 多邊形 [[pixel_x1, pixel_y1], ...] -> [[norm_x1, norm_y1], ...]
            norm_coords = []
            for point in pixel_coordinates:
                norm_x = point[0] / image_width
                norm_y = point[1] / image_height
                norm_coords.append([norm_x, norm_y])

            return [class_id, norm_coords, label_type]

        return None


def is_labels_overlap(label1, label2, iou_threshold=0.0):
    """
    檢測兩個標籤是否重疊，支持矩形框和多邊形

    參數:
        label1: 第一個標籤 [class_id, coordinates, label_type] (像素座標)
        label2: 第二個標籤 [class_id, coordinates, label_type] (像素座標)
        iou_threshold: IoU閾值，超過此值視為重疊

    返回:
        bool: 是否重疊
    """
    _, coords1, type1 = label1
    _, coords2, type2 = label2

    # 將標籤轉換為Shapely對象
    shape1 = None
    shape2 = None

    # 處理第一個標籤
    if type1 == 'box':
        x1, y1, w1, h1 = coords1
        shape1 = box(x1, y1, x1 + w1, y1 + h1)
    elif type1 == 'polygon':
        # 確保多邊形至少有3個點
        if len(coords1) < 3:
            return False
        shape1 = Polygon(coords1)

    # 處理第二個標籤
    if type2 == 'box':
        x2, y2, w2, h2 = coords2
        shape2 = box(x2, y2, x2 + w2, y2 + h2)
    elif type2 == 'polygon':
        # 確保多邊形至少有3個點
        if len(coords2) < 3:
            return False
        shape2 = Polygon(coords2)

    # 檢查兩個形狀是否有效
    if not shape1.is_valid or not shape2.is_valid:
        return False

    # 計算交集面積
    intersection_area = shape1.intersection(shape2).area

    # 如果沒有交集，則不重疊
    if intersection_area == 0:
        return False

    # 計算IoU
    union_area = shape1.area + shape2.area - intersection_area
    iou = intersection_area / union_area if union_area > 0 else 0

    # 如果IoU超過閾值，則視為重疊
    return iou > iou_threshold


def extract_region_from_label(image, label_pixel):
    """
    從圖像中提取指定標籤區域

    參數:
        image: 原始圖像
        label_pixel: 標籤 [class_id, coordinates, label_type] (像素座標)

    返回:
        region: 提取的區域圖像
        bbox: 邊界框 [x, y, w, h]
    """
    _, coords, label_type = label_pixel

    if label_type == 'box':
        # 直接提取矩形區域
        x, y, w, h = coords
        return image[y:y+h, x:x+w].copy(), [x, y, w, h]

    elif label_type == 'polygon':
        # 為多邊形創建遮罩
        h, w = image.shape[:2]
        mask = np.zeros((h, w), dtype=np.uint8)

        # 繪製多邊形
        pts = np.array(coords, dtype=np.int32)
        cv2.fillPoly(mask, [pts], 255)

        # 找到多邊形的邊界框
        x, y, w, h = cv2.boundingRect(pts)

        # 應用遮罩到原始圖像
        masked_img = cv2.bitwise_and(image, image, mask=mask)

        # 提取邊界框內的區域
        region = masked_img[y:y+h, x:x+w].copy()

        return region, [x, y, w, h]

    return None, None


def road_focused_batch_processing(transfer, 
                                  source_config,          # 來源設定，包含多個來源
                                  target_label_dir, target_image_dir,  # 目標資料夾
                                  output_dir,
                                  num_generations=10,
                                  regions_per_image=(1, 3),
                                  blend_alpha_range=(0.7, 1.0),
                                  scale_range=(0.6, 1.2),
                                  cache_file=None,
                                  focus_bottom=True,    # 是否聚焦在畫面下方
                                  focus_center=True,    # 是否聚焦在畫面中間
                                  bottom_ratio=0.6,    # 下方區域的比例 (0.6表示圖像下方60%的區域)
                                  center_ratio=0.6,    # 中間區域的比例 (0.6表示圖像中間60%的區域)
                                  avoid_overlap=True,   # 是否避免標籤重疊
                                  iou_threshold=0.1,    # IoU閾值，超過此值視為重疊
                                  label_mode='box',  # 'box': 將 polygon 轉換成 YOLO 格式 box，'polygon': 保留 polygon
                                  class_counts=None,    # 類別數量限制
                                  grid_placement=True,  # 是否啟用網格放置策略
                                  grid_size=8,         # 網格大小 (將圖像分為 grid_size x grid_size 的網格)
                                  max_placement_attempts=50,  # 每個區域最大嘗試放置次數
                                  temp_label_file=None, # 暫存標籤文件
                                  avoid_similar_positions=True):  # 避免相似位置
    """
    專注於道路區域的批次處理功能，隨機融合圖像並生成標籤
    支持矩形框和多邊形標籤，並支持指定類別的數量限制
    增強版：增加網格放置和避免相似位置功能
    
    參數:
        transfer: ImageRegionTransfer實例
        source_config: 來源設定列表，每項包含 {'label_dir': 路徑, 'image_dir': 路徑, 'weight': 權重}
        target_label_dir: 目標標籤目錄
        target_image_dir: 目標圖像目錄
        output_dir: 輸出目錄
        num_generations: 要生成的圖像數量
        regions_per_image: 每張圖像添加區域的數量範圍 (min, max)
        blend_alpha_range: 混合透明度範圍 (min, max)
        scale_range: 縮放比例範圍 (min, max)
        cache_file: 緩存文件路徑，用於記錄類別計數，防止超出限制
        focus_bottom: 是否聚焦在畫面下方
        focus_center: 是否聚焦在畫面中間
        bottom_ratio: 下方區域的比例
        center_ratio: 中間區域的比例
        avoid_overlap: 是否避免標籤重疊
        iou_threshold: IoU閾值，超過此值視為重疊
        label_mode: 'box': 將 polygon 轉換成 YOLO 格式 box，'polygon': 保留 polygon
        class_counts: 類別數量限制字典，格式為 {類別ID: 最大數量}
        grid_placement: 是否啟用網格放置策略
        grid_size: 網格大小
        max_placement_attempts: 每個區域的最大嘗試放置次數
        temp_label_file: 暫存標籤文件路徑，若為None則自動生成
        avoid_similar_positions: 是否避免相似位置
    """
    import os
    import cv2
    import numpy as np
    import random
    import json
    import tempfile
    from tqdm import tqdm
    from shapely.geometry import Polygon, box
    
    # 創建輸出目錄
    os.makedirs(output_dir, exist_ok=True)
    
    # 設置暫存標籤文件
    if temp_label_file is None:
        temp_label_file = os.path.join(output_dir, "temp_labels.json")
    
    # 處理來源設定
    source_data = []
    
    # 讀取所有來源數據
    for source in source_config:
        source_label_dir = source['label_dir']
        source_image_dir = source['image_dir']
        source_weight = source.get('weight', 1.0)
        
        # 獲取來源標籤文件和圖像文件
        source_label_files = [f for f in os.listdir(source_label_dir) if f.endswith('.txt')]
        source_image_files = [f for f in os.listdir(
            source_image_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

        # 創建來源標籤文件和圖像文件的映射
        source_label_to_image = {}
        for label_file in source_label_files:
            base_name = os.path.splitext(label_file)[0]
            potential_images = [f for f in source_image_files if os.path.splitext(f)[
                0] == base_name]
            if potential_images:
                source_label_to_image[label_file] = potential_images[0]

        # 增加來源權重信息
        source_data.append({
            'label_dir': source_label_dir,
            'image_dir': source_image_dir,
            'label_to_image': source_label_to_image,
            'weight': source_weight
        })
        
        print(f"找到 {len(source_label_to_image)} 對有效的來源標籤和圖像，來源: {source_label_dir}")

    # 獲取目標標籤文件和圖像文件
    target_label_files = [f for f in os.listdir(target_label_dir) if f.endswith('.txt')]
    target_image_files = [f for f in os.listdir(
        target_image_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

    # 創建目標標籤文件和圖像文件的映射
    target_label_to_image = {}
    for label_file in target_label_files:
        base_name = os.path.splitext(label_file)[0]
        potential_images = [f for f in target_image_files if os.path.splitext(f)[
            0] == base_name]
        if potential_images:
            target_label_to_image[label_file] = potential_images[0]

    print(f"找到 {len(target_label_to_image)} 對有效的目標標籤和圖像")

    # 檢查是否有足夠的數據
    if not any([len(source['label_to_image']) > 0 for source in source_data]):
        print("沒有找到匹配的來源標籤和圖像對")
        return
    
    if len(target_label_to_image) == 0:
        print("沒有找到匹配的目標標籤和圖像對")
        return

    # 加載類別計數（如果存在）
    class_count_stats = {}

    if cache_file and os.path.exists(cache_file):
        try:
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)

                # 從緩存中獲取類別計數信息
                if isinstance(cache_data, dict) and 'class_counts' in cache_data:
                    class_count_stats = cache_data.get('class_counts', {})

            print("當前類別統計:")
            for class_id, count in class_count_stats.items():
                class_id_str = str(class_id)
                max_count = class_counts.get(
                    class_id_str, "無限制") if class_counts else "無限制"
                print(f"  類別 {class_id}: {count}/{max_count}")
        except Exception as e:
            print(f"加載緩存時出錯: {e}")
            class_count_stats = {}

    # 將字符串鍵轉換為整數鍵（用於後續處理）
    if class_count_stats:
        class_count_stats = {int(k): v for k, v in class_count_stats.items()}

    # 開始批次處理
    successful_count = 0
    target_reached = False  # 是否已達到所有類別的目標數量

    # 暫存標籤信息
    temp_labels_data = {}
    
    # 如果暫存標籤文件存在，嘗試加載
    if os.path.exists(temp_label_file):
        try:
            with open(temp_label_file, 'r') as f:
                temp_labels_data = json.load(f)
            print(f"已載入暫存標籤數據，包含 {len(temp_labels_data)} 張圖像的標籤")
        except Exception as e:
            print(f"載入暫存標籤文件失敗: {e}")
            temp_labels_data = {}

    for i in tqdm(range(num_generations), desc="生成融合圖像"):
        # 檢查是否達到所有類別的目標數量
        if class_counts:
            all_targets_reached = True
            for class_id_str, max_count in class_counts.items():
                if max_count > 0:  # 如果目標數量大於0
                    current_count = class_count_stats.get(int(class_id_str), 0)
                    if current_count < max_count:
                        all_targets_reached = False
                        break

            if all_targets_reached:
                print("所有類別的目標數量已達到，停止生成")
                target_reached = True
                break

        # 選擇目標圖像和標籤
        target_label = random.choice(list(target_label_to_image.keys()))
        target_image = target_label_to_image[target_label]

        # 讀取目標圖像和標籤
        target_image_path = os.path.join(target_image_dir, target_image)
        target_label_path = os.path.join(target_label_dir, target_label)

        # 初始化結果圖像和標籤
        result_image = imread_unicode(target_image_path)
        if result_image is None:
            print(f"無法讀取目標圖像: {target_image_path}")
            continue

        result_labels = transfer.read_label_file(target_label_path)
        
        # 生成輸出文件名
        output_basename = f"fusion_{i+1:04d}"
        output_key = output_basename  # 用於暫存標籤數據的鍵
        
        # 如果圖像已經在暫存標籤中，獲取之前的標籤
        if output_key in temp_labels_data:
            temp_labels = temp_labels_data[output_key]['labels']
            # 將暫存標籤轉換為程式可用的格式
            result_labels = []
            for temp_label in temp_labels:
                class_id, coords, label_type = temp_label
                if label_type == 'box':
                    result_labels.append([int(class_id), [float(c) for c in coords], label_type])
                elif label_type == 'polygon':
                    polygon_coords = []
                    for point in coords:
                        polygon_coords.append([float(point[0]), float(point[1])])
                    result_labels.append([int(class_id), polygon_coords, label_type])
            
            print(f"從暫存數據中讀取圖像 {output_key} 的標籤，共 {len(result_labels)} 個")

        # 獲取目標圖像尺寸
        th, tw = result_image.shape[:2]

        # 計算關注區域
        if focus_bottom:
            y_min = int(th * (1 - bottom_ratio))
            y_max = th
        else:
            y_min = 0
            y_max = th

        if focus_center:
            x_offset = int(tw * (1 - center_ratio) / 2)
            x_min = x_offset
            x_max = tw - x_offset
        else:
            x_min = 0
            x_max = tw

        # 將結果標籤轉換為像素座標，用於檢測重疊
        result_labels_pixel = []
        for label in result_labels:
            label_pixel = transfer.normalized_to_pixel_coordinates(
                label, tw, th)
            result_labels_pixel.append(label_pixel)

        # 決定添加多少個區域
        num_regions = random.randint(*regions_per_image)

        # 初始化位置記錄，用於避免相似位置
        position_history = []
        
        # 設置網格
        if grid_placement:
            # 創建網格 (grid_size x grid_size)
            grid_cells = []
            cell_width = (x_max - x_min) / grid_size
            cell_height = (y_max - y_min) / grid_size
            
            for row in range(grid_size):
                for col in range(grid_size):
                    # 記錄每個網格單元的邊界
                    cell_x_min = x_min + col * cell_width
                    cell_y_min = y_min + row * cell_height
                    cell_x_max = cell_x_min + cell_width
                    cell_y_max = cell_y_min + cell_height
                    
                    grid_cells.append({
                        'x_min': cell_x_min,
                        'y_min': cell_y_min,
                        'x_max': cell_x_max,
                        'y_max': cell_y_max,
                        'used': False  # 標記是否已使用
                    })
            
            # 打亂網格順序以增加隨機性
            random.shuffle(grid_cells)

        # 對每個要添加的區域
        added_regions = 0
        max_total_attempts = num_regions * max_placement_attempts  # 總嘗試次數限制
        total_attempts = 0

        while added_regions < num_regions and total_attempts < max_total_attempts:
            total_attempts += 1

            # 根據權重隨機選擇來源
            source_weights = [source['weight'] for source in source_data]
            selected_source = random.choices(source_data, weights=source_weights, k=1)[0]
            
            if not selected_source['label_to_image']:
                continue
                
            # 隨機選擇來源標籤和圖像
            source_label = random.choice(list(selected_source['label_to_image'].keys()))
            source_image = selected_source['label_to_image'][source_label]

            # 讀取來源標籤
            source_label_path = os.path.join(selected_source['label_dir'], source_label)
            source_labels = transfer.read_label_file(source_label_path)

            if not source_labels:
                continue

            # 篩選處理：根據類別限制選擇區域索引
            valid_indices = []
            for idx, label in enumerate(source_labels):
                class_id = int(label[0])

                # 檢查該類別是否已達到限制
                if class_counts and str(class_id) in class_counts:
                    max_count = class_counts[str(class_id)]
                    current_count = class_count_stats.get(class_id, 0)

                    # 如果設置為0，表示不生成此類別
                    if max_count == 0:
                        continue

                    # 如果已達到或超過限制，則跳過此類別
                    if max_count > 0 and current_count >= max_count:
                        continue

                valid_indices.append(idx)

            # 如果沒有有效的區域索引，跳過此來源
            if not valid_indices:
                continue

            # 從有效索引中隨機選擇
            region_index = random.choice(valid_indices)
            selected_label = source_labels[region_index]
            selected_class_id = int(selected_label[0])

            # 讀取來源圖像
            source_image_path = os.path.join(selected_source['image_dir'], source_image)
            source_img = imread_unicode(source_image_path)

            if source_img is None:
                print(f"無法讀取來源圖像: {source_image_path}")
                continue

            # 設置隨機參數
            blend_alpha = random.uniform(*blend_alpha_range)
            scale = random.uniform(*scale_range)

            # 計算來源圖像區域
            sh, sw = source_img.shape[:2]
            source_label_pixel = transfer.normalized_to_pixel_coordinates(
                selected_label, sw, sh)

            # 提取來源區域
            region, bbox = extract_region_from_label(
                source_img, source_label_pixel)

            if region is None or bbox is None:
                print(f"無法提取區域: {source_image}[{region_index}]")
                continue

            sx, sy, sw, sh = bbox

            # 應用縮放
            if scale != 1.0:
                new_w = max(1, int(sw * scale))
                new_h = max(1, int(sh * scale))
                try:
                    region = cv2.resize(region, (new_w, new_h))
                    sw, sh = new_w, new_h
                except Exception as e:
                    print(f"調整區域大小時出錯: {e}")
                    continue

            # 選擇放置位置策略
            placement_success = False
            placement_attempts = 0
            
            while not placement_success and placement_attempts < max_placement_attempts:
                placement_attempts += 1
                
                # 放置策略: 網格放置或完全隨機
                if grid_placement and grid_cells:
                    # 嘗試找到一個未使用的網格單元
                    unused_cells = [cell for cell in grid_cells if not cell['used']]
                    if unused_cells:
                        cell = random.choice(unused_cells)
                        # 在單元格內隨機選擇一個位置
                        tx = random.randint(int(cell['x_min']), max(int(cell['x_min']), int(cell['x_max']) - sw))
                        ty = random.randint(int(cell['y_min']), max(int(cell['y_min']), int(cell['y_max']) - sh))
                        # 標記單元格為已使用
                        cell['used'] = True
                    else:
                        # 如果所有單元格都已使用，回到完全隨機放置
                        tx = random.randint(x_min, max(x_min, x_max - sw))
                        ty = random.randint(y_min, max(y_min, y_max - sh))
                else:
                    # 完全隨機放置
                    tx = random.randint(x_min, max(x_min, x_max - sw))
                    ty = random.randint(y_min, max(y_min, y_max - sh))
                
                # 檢查是否與歷史位置相似 (如果啟用)
                if avoid_similar_positions and position_history:
                    too_similar = False
                    # 定義相似閾值 (圖像寬度和高度的百分比)
                    similarity_threshold_x = tw * 0.1
                    similarity_threshold_y = th * 0.1
                    
                    for pos in position_history:
                        old_tx, old_ty, old_sw, old_sh = pos
                        # 計算中心點
                        center_x1, center_y1 = tx + sw/2, ty + sh/2
                        center_x2, center_y2 = old_tx + old_sw/2, old_ty + old_sh/2
                        
                        # 檢查距離
                        distance_x = abs(center_x1 - center_x2)
                        distance_y = abs(center_y1 - center_y2)
                        
                        if distance_x < similarity_threshold_x and distance_y < similarity_threshold_y:
                            too_similar = True
                            break
                    
                    if too_similar:
                        continue  # 嘗試新位置
                
                # 創建新的標籤（根據原始標籤類型）
                label_type = selected_label[2]
                new_label_pixel = None

                if label_type == 'box':
                    new_label_pixel = [selected_class_id, [tx, ty, sw, sh], 'box']
                elif label_type == 'polygon':
                    # 計算偏移量
                    orig_box = source_label_pixel[1]  # 原始多邊形座標
                    bbox_x, bbox_y = bbox[0], bbox[1]  # 原始包圍框左上角

                    # 計算縮放和平移後的多邊形座標
                    new_polygon = []
                    for point in orig_box:
                        # 相對於邊界框的位置
                        rel_x = point[0] - bbox_x
                        rel_y = point[1] - bbox_y

                        # 應用縮放
                        rel_x = int(rel_x * scale)
                        rel_y = int(rel_y * scale)

                        # 加上新位置
                        new_x = tx + rel_x
                        new_y = ty + rel_y

                        new_polygon.append([new_x, new_y])

                    new_label_pixel = [selected_class_id, new_polygon, 'polygon']

                # 檢查是否會與現有標籤重疊
                overlapping = False
                if avoid_overlap and result_labels_pixel:
                    for existing_label in result_labels_pixel:
                        if is_labels_overlap(new_label_pixel, existing_label, iou_threshold):
                            overlapping = True
                            break

                if not overlapping:
                    placement_success = True
                    break  # 成功找到放置位置

            # 如果找不到適合的位置，跳過這個區域
            if not placement_success:
                print(f"無法為圖像 {output_key} 找到適合的放置位置，跳過該區域")
                continue

            # 放置區域
            try:
                # 根據標籤類型不同的處理方式
                if label_type == 'box':
                    # 矩形框處理方式不變
                    # 確保不超出圖像邊界
                    if ty + sh > th:
                        region = region[:th-ty, :]
                        sh = th - ty
                    if tx + sw > tw:
                        region = region[:, :tw-tx]
                        sw = tw - tx

                    # 混合區域
                    roi = result_image[ty:ty+sh, tx:tx+sw]
                    if blend_alpha < 1.0:
                        # 處理可能有不同通道數的情況
                        if region.shape[:2] != roi.shape[:2]:
                            region = cv2.resize(
                                region, (roi.shape[1], roi.shape[0]))

                        result_image[ty:ty+sh, tx:tx+sw] = cv2.addWeighted(
                            roi, 1 - blend_alpha, region, blend_alpha, 0)
                    else:
                        result_image[ty:ty+sh, tx:tx+sw] = region

                elif label_type == 'polygon':
                    # 多邊形直接處理方式
                    # 創建目標圖像上的多邊形遮罩
                    mask = np.zeros((th, tw), dtype=np.uint8)
                    polygon = np.array(new_label_pixel[1], dtype=np.int32)
                    cv2.fillPoly(mask, [polygon], 255)

                    # 獲取多邊形的邊界框
                    x, y, w, h = cv2.boundingRect(polygon)

                    # 確保邊界框不超出圖像邊界
                    x = max(0, x)
                    y = max(0, y)
                    w = min(tw - x, w)
                    h = min(th - y, h)

                    if w <= 0 or h <= 0:
                        raise ValueError("處理後的多邊形區域無效")

                    # 提取多邊形區域的遮罩部分
                    roi_mask = mask[y:y+h, x:x+w]

                    # 提取目標圖像中對應的區域
                    roi = result_image[y:y+h, x:x+w].copy()

                    # 縮放來源區域到適合的大小
                    if region.shape[:2] != (h, w):
                        region = cv2.resize(region, (w, h))

                    # 使用遮罩進行混合
                    mask_3ch = cv2.merge(
                        [roi_mask, roi_mask, roi_mask]) / 255.0

                    # 僅在遮罩區域內進行混合
                    if blend_alpha < 1.0:
                        blended = cv2.addWeighted(
                            roi, 1 - blend_alpha, region, blend_alpha, 0)
                        result = roi * (1 - mask_3ch) + blended * mask_3ch
                    else:
                        result = roi * (1 - mask_3ch) + region * mask_3ch

                    # 將結果放回原圖
                    result_image[y:y+h, x:x+w] = result

                    # 更新位置信息用於輸出
                    tx, ty = x, y
                    sw, sh = w, h

                # 記錄放置位置，用於避免相似位置
                position_history.append((tx, ty, sw, sh))

                # 轉換為歸一化座標並添加到結果標籤
                if label_mode == 'box':
                    # 如果原始是 polygon，則轉換成 box（YOLO 格式）
                    if new_label_pixel[2] == 'polygon':
                        # 取多邊形邊界框轉 YOLO 格式
                        pts = np.array(new_label_pixel[1])
                        x_min, y_min = pts.min(axis=0)
                        x_max, y_max = pts.max(axis=0)
                        bbox_pixel = [x_min, y_min, x_max - x_min, y_max - y_min]
                        yolo_box = transfer.pixel_to_normalized_coordinates(
                            [new_label_pixel[0], bbox_pixel, 'box'], tw, th)
                        result_labels.append(yolo_box)
                    else:
                        # 已是 box，直接轉換
                        new_label_norm = transfer.pixel_to_normalized_coordinates(
                            new_label_pixel, tw, th)
                        result_labels.append(new_label_norm)
                else:
                    # label_mode == 'polygon'，不轉換 polygon
                    new_label_norm = transfer.pixel_to_normalized_coordinates(
                        new_label_pixel, tw, th)
                    result_labels.append(new_label_norm)

                # 同時添加到現有標籤列表，用於後續重疊檢測
                result_labels_pixel.append(new_label_pixel)

                # 更新類別計數
                class_count_stats[selected_class_id] = class_count_stats.get(
                    selected_class_id, 0) + 1

                added_regions += 1
                source_name = os.path.basename(selected_source['label_dir'])
                print(
                    f"成功添加區域 {added_regions}/{num_regions}: 來源[{source_name}]:{source_image}[{region_index}] 類別 {selected_class_id} -> {output_key} 位置: ({tx}, {ty})")
                
                # 將標籤保存到暫存數據中
                temp_labels = []
                for label in result_labels:
                    class_id, coords, label_type = label
                    # 確保可以序列化為JSON
                    temp_labels.append([int(class_id), coords, label_type])
                
                temp_labels_data[output_key] = {
                    'labels': temp_labels,
                    'image_width': tw,
                    'image_height': th
                }
                
                # 定期保存暫存標籤數據
                if added_regions % 5 == 0 or added_regions == num_regions:
                    try:
                        with open(temp_label_file, 'w') as f:
                            json.dump(temp_labels_data, f, indent=2)
                        print(f"已更新暫存標籤數據，目前有 {len(temp_labels_data)} 張圖像的標籤")
                    except Exception as e:
                        print(f"保存暫存標籤數據失敗: {e}")
                    
            except Exception as e:
                print(f"放置區域時出錯: {e}")
                continue

        # 如果成功添加了任何區域，保存結果
        if added_regions > 0:
            # 輸出檔案路徑
            output_image_path = os.path.join(
                output_dir, f"{output_basename}.jpg")
            output_label_path = os.path.join(
                output_dir, f"{output_basename}.txt")

            # 保存結果圖像和標籤
            success, encoded_image = cv2.imencode('.jpg', result_image)
            if success:
                with open(output_image_path, 'wb') as f:
                    f.write(encoded_image.tobytes())
                print(f"圖片成功保存至 {output_image_path}")
            else:
                print("圖像編碼失敗")
            transfer.write_label_file(output_label_path, result_labels)

            successful_count += 1
            
            # 從暫存數據中移除已保存的圖像
            if output_key in temp_labels_data:
                del temp_labels_data[output_key]
                # 更新暫存檔案
                try:
                    with open(temp_label_file, 'w') as f:
                        json.dump(temp_labels_data, f, indent=2)
                except Exception as e:
                    print(f"更新暫存標籤數據失敗: {e}")

        # 如果嘗試次數太多但未添加任何區域，輸出警告
        elif total_attempts >= max_total_attempts and added_regions == 0:
            print(f"警告: 在圖像 {output_basename} 上未能添加任何區域，可能是由於避免重疊或類別限制太嚴格")

    # 更新類別計數緩存
    if cache_file:
        try:
            # 僅保存類別計數信息
            cache_data = {
                'class_counts': {str(k): v for k, v in class_count_stats.items()}
            }

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)

            print("類別統計:")
            for class_id, count in class_count_stats.items():
                class_id_str = str(class_id)
                max_count = class_counts.get(
                    class_id_str, "無限制") if class_counts else "無限制"
                print(f"  類別 {class_id}: {count}/{max_count}")
        except Exception as e:
            print(f"保存緩存時出錯: {e}")

    # 輸出處理結果
    if target_reached:
        print(f"批次處理完成! 已達到所有類別的目標數量，成功生成 {successful_count}/{i+1} 張融合圖像")
    else:
        print(f"批次處理完成! 成功生成 {successful_count}/{num_generations} 張融合圖像")

    return successful_count


def class_count_example():
    """
    示範如何使用類別數量限制功能，支持多邊形標籤
    """
    # 創建轉移器
    transfer = ImageRegionTransfer(debug=False)

    # 設置路徑
    # 來源 = 拿誰;目標 = 貼誰
    source_dir = r"D:\image\6_test_fus"  # 來源目錄
    target_dir = r"D:\image\6_test_fus"  # 目標目錄
    output_dir = r"D:\image\test_fus"  # 輸出目錄
    cache_file = os.path.join(output_dir, "road_class_counts.json")  # 只記錄類別計數
    
    # 確保輸出目錄存在
    os.makedirs(output_dir, exist_ok=True)

    # 設置類別數量限制
    '''
    0: expansion_joint_伸縮縫
    1: joint_路面接縫
    2: linear_crack_裂縫
    3: Alligator_crack_龜裂
    4: potholes_坑洞
    5: patch_補綻
    '''

    # 嚴格按照指定的數量限制生成各類別
    class_counts = {
        "0": 0,       # 生成30個類別0
        "1": 0,       # 生成50個類別1
        "2": 20,       # 生成80個類別2
        "3": 0,       # 生成90個類別3
        "4": 5,       # 生成20個類別4
        "5": 0,       # 生成70個類別5
        "6": 0,        # 不生成類別6
        "7": 0,        # 不生成類別7
        "8": 0,        # 不生成類別8
    }
    
    # 讓使用者選擇模式
    print("請選擇資料來源模式:")
    print("1. 來源給目標 (來源圖像區域貼到目標圖像)")
    print("2. 目標給目標 (目標圖像區域貼到其他目標圖像)")
    print("3. 來源和目標一起給目標 (兩種來源混合)")
    
    choice = input("請選擇 (1/2/3): ")
    
    # 根據使用者選擇配置來源
    source_config = []
    
    if choice == '1':
        # 來源給目標
        source_config.append({
            'label_dir': source_dir,
            'image_dir': source_dir,
            'weight': 1.0
        })
        print("已選擇: 來源給目標")
        
    elif choice == '2':
        # 目標給目標
        source_config.append({
            'label_dir': target_dir,
            'image_dir': target_dir,
            'weight': 1.0
        })
        print("已選擇: 目標給目標")
        
    elif choice == '3':
        # 來源和目標一起給目標
        source_config.append({
            'label_dir': source_dir,
            'image_dir': source_dir,
            'weight': 1.0
        })
        source_config.append({
            'label_dir': target_dir,
            'image_dir': target_dir,
            'weight': 1.0
        })
        print("已選擇: 來源和目標一起給目標")
        
    else:
        print("無效選擇，使用預設模式: 來源給目標")
        source_config.append({
            'label_dir': source_dir,
            'image_dir': source_dir,
            'weight': 1.0
        })
    '''
    假設你有三個來源目錄：

    來源A：包含正常路面的圖像，權重設為 1.0
    來源B：包含裂縫的圖像，權重設為 3.0
    來源C：包含坑洞的圖像，權重設為 2.0
    source_config = [
        {'label_dir': 'D:/來源A', 'image_dir': 'D:/來源A', 'weight': 1.0},
        {'label_dir': 'D:/來源B', 'image_dir': 'D:/來源B', 'weight': 3.0},
        {'label_dir': 'D:/來源C', 'image_dir': 'D:/來源C', 'weight': 2.0},
    ]

    '''
    # 使用修改後的函數進行生成
    successful_count = road_focused_batch_processing(transfer, 
        source_config,          # 來源設定，包含多個來源
        target_dir, target_dir,  # 目標資料夾
        output_dir,
        num_generations=10,
        regions_per_image=(1, 3),
        blend_alpha_range=(0.7, 1.0),
        scale_range=(0.6, 1.2),
        cache_file=None,
        focus_bottom=True,    # 是否聚焦在畫面下方
        focus_center=True,    # 是否聚焦在畫面中間
        bottom_ratio=0.6,    # 下方區域的比例 (0.6表示圖像下方60%的區域)
        center_ratio=0.6,    # 中間區域的比例 (0.6表示圖像中間60%的區域)
        avoid_overlap=True,   # 是否避免標籤重疊
        iou_threshold=0.1,    # IoU閾值，超過此值視為重疊
        label_mode='box',  # 'box': 將 polygon 轉換成 YOLO 格式 box，'polygon': 保留 polygon
        class_counts=None,    # 類別數量限制
        grid_placement=True,  # 是否啟用網格放置策略
        grid_size=4,         # 網格大小 (將圖像分為 grid_size x grid_size 的網格)
        max_placement_attempts=50,  # 每個區域最大嘗試放置次數
        temp_label_file=None, # 暫存標籤文件
        avoid_similar_positions=True)  # 避免相似位置

    print(f"成功生成了 {successful_count} 張融合圖像，符合類別數量限制要求")


def road_from_labelme(labelme_dir, output_yolo_dir):
    """
    將LabelMe格式的標註數據轉換為YOLO格式
    
    參數:
        labelme_dir: LabelMe格式標籤目錄
        output_yolo_dir: 輸出的YOLO格式目錄
    """
    import json
    
    # 確保輸出目錄存在
    os.makedirs(output_yolo_dir, exist_ok=True)
    
    # 獲取所有JSON文件
    json_files = [f for f in os.listdir(labelme_dir) if f.endswith('.json')]
    
    # 類別映射字典，如果有需要可以在這裡定義
    class_map = {
        # 例如: "坑洞": 0, "裂縫": 1
    }
    
    for json_file in tqdm(json_files, desc="轉換LabelMe到YOLO"):
        try:
            # 讀取JSON文件
            with open(os.path.join(labelme_dir, json_file), 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 獲取圖像尺寸
            img_width = data.get('imageWidth')
            img_height = data.get('imageHeight')
            
            if not img_width or not img_height:
                print(f"警告: {json_file} 中未找到圖像尺寸信息")
                continue
                
            # 獲取基本文件名（不含擴展名）
            base_name = os.path.splitext(json_file)[0]
            
            # 準備輸出文件
            output_file = os.path.join(output_yolo_dir, f"{base_name}.txt")
            
            with open(output_file, 'w') as f_out:
                # 處理每個形狀
                for shape in data['shapes']:
                    label = shape['label']
                    shape_type = shape['shape_type']
                    points = shape['points']
                    
                    # 獲取類別ID
                    if label in class_map:
                        class_id = class_map[label]
                    else:
                        # 如果沒有指定映射，嘗試將標籤轉換為整數
                        try:
                            class_id = int(label)
                        except ValueError:
                            # 如果不能轉換，創建新的映射
                            if label not in class_map:
                                class_map[label] = len(class_map)
                            class_id = class_map[label]
                    
                    # 根據形狀類型處理
                    if shape_type == 'rectangle':
                        # 矩形: 左上角和右下角
                        if len(points) == 2:
                            x1, y1 = points[0]
                            x2, y2 = points[1]
                            
                            # 轉換為YOLO格式: class_id, x_center, y_center, width, height
                            x_center = (x1 + x2) / 2 / img_width
                            y_center = (y1 + y2) / 2 / img_height
                            width = abs(x2 - x1) / img_width
                            height = abs(y2 - y1) / img_height
                            
                            f_out.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
                    
                    elif shape_type == 'polygon':
                        # 多邊形: 所有點的座標
                        if len(points) >= 3:
                            # 寫入YOLO格式的多邊形: class_id, x1, y1, x2, y2, ..., xn, yn
                            coords = []
                            for x, y in points:
                                # 歸一化座標
                                norm_x = x / img_width
                                norm_y = y / img_height
                                coords.extend([f"{norm_x:.6f}", f"{norm_y:.6f}"])
                            
                            f_out.write(f"{class_id} {' '.join(coords)}\n")
                                
                    # 其他形狀類型（如有需要可以添加）
            
            print(f"已轉換 {json_file} 到 {output_file}")
            
        except Exception as e:
            print(f"處理 {json_file} 時出錯: {e}")
    
    # 保存類別映射（如果需要）
    class_map_file = os.path.join(output_yolo_dir, "class_map.json")
    with open(class_map_file, 'w', encoding='utf-8') as f:
        json.dump(class_map, f, ensure_ascii=False, indent=2)
    
    print(f"轉換完成! 類別映射已保存到 {class_map_file}")

if __name__ == "__main__":
    class_count_example()