#!/usr/bin/env python3
"""
資料前處理框架 - 企業級資料處理和轉換解決方案

該模組提供了完整的資料前處理功能，包括：
- 格式轉換: 支援LabelMe、YOLO、VOC、COCO格式互轉
- 資料處理: 圖像增強、資料集分割、標註編輯
- GUI應用: PyQt6現代化界面
- 統一架構: 基於策略模式和配置驅動的企業級設計
"""

from pathlib import Path
import sys

# 添加路徑設定
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 核心模組
from .core import (
    BaseTool,
    ConfigManager, 
    DataProcessingException,
    FileUtils,
    LoggerUtils
)

# 轉換器模組
from .converters import (
    AnnotationConverter,
    FormatDetector,
    ImageProcessor
)

__version__ = "3.0.0"
__author__ = "Road AI Framework Team"

__all__ = [
    # 核心類別
    'BaseTool',
    'ConfigManager',
    'DataProcessingException', 
    'FileUtils',
    'LoggerUtils',
    
    # 轉換器類別
    'AnnotationConverter',
    'FormatDetector',
    'ImageProcessor',
    
    # 版本信息
    '__version__',
    '__author__'
]