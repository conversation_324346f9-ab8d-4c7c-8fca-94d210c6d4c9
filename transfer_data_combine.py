import os
import json
import cv2
import numpy as np
from pathlib import Path
from tqdm import tqdm

def convert_yolo_polygon_to_labelme(yolo_folder, output_folder, class_names, image_ext=".jpg"):
    """
    將 YOLO Polygon 格式（class_id x1 y1 x2 y2 ... xn yn）轉換為 LabelMe 格式（.json）

    yolo_folder: 含有 .txt + 對應圖片的資料夾
    output_folder: 產出的 LabelMe JSON 儲存位置
    class_names: class_id 對應的名稱清單
    image_ext: 圖片副檔名，預設 .jpg
    """
    from PIL import Image

    yolo_folder = Path(yolo_folder)
    output_folder = Path(output_folder)
    output_folder.mkdir(parents=True, exist_ok=True)

    for txt_file in yolo_folder.glob("*.txt"):
        base = txt_file.stem
        image_file = yolo_folder / f"{base}{image_ext}"
        if not image_file.exists():
            print(f"⚠️ 找不到圖片：{image_file}")
            continue

        img = Image.open(image_file)
        w, h = img.size
        shapes = []

        with open(txt_file, "r", encoding="utf-8") as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) < 7 or len(parts) % 2 == 0:
                    print(f"❌ 無效的 polygon 行（{txt_file.name}）：{line}")
                    continue

                class_id = int(parts[0])
                coords = list(map(float, parts[1:]))

                # YOLO 格式是歸一化 → 要乘回原圖尺寸
                points = []
                for i in range(0, len(coords), 2):
                    x = coords[i] * w
                    y = coords[i + 1] * h
                    points.append([x, y])

                label = class_names[class_id] if class_id < len(class_names) else f"class_{class_id}"

                shape = {
                    "label": label,
                    "points": points,
                    "group_id": None,
                    "shape_type": "polygon",
                    "flags": {}
                }
                shapes.append(shape)

        if not shapes:
            print(f"⚠️ 無標註（跳過空 .json）：{txt_file.name}")
            continue

        labelme_data = {
            "version": "5.0.1",
            "flags": {},
            "shapes": shapes,
            "imagePath": image_file.name,
            "imageHeight": h,
            "imageWidth": w
        }

        out_path = output_folder / f"{base}.json"
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(labelme_data, f, ensure_ascii=False, indent=2)

        print(f"✅ 已轉換：{txt_file.name} → {out_path.name}")


def visualize_overlays_and_save(folder, save_folder, limit=3):
    """
    從指定資料夾中讀取圖像與 LabelMe 標註，繪製 overlay 並儲存至 save_folder。
    """
    folder = Path(folder)
    save_folder = Path(save_folder)
    save_folder.mkdir(parents=True, exist_ok=True)

    image_files = sorted([f for f in folder.glob("*.jpg")])
    processed_count = 0

    print(f"📁 從 {folder} 中查找 overlay 資料...")
    for img_path in tqdm(image_files):
        base = img_path.stem
        json_path = folder / f"{base}.json"
        if not json_path.exists():
            continue

        try:
            buf = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(buf, cv2.IMREAD_COLOR)
            with open(json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            overlay = img.copy()
            for shape in data.get("shapes", []):
                pts = np.array(shape["points"], dtype=np.int32)
                cv2.polylines(overlay, [pts], isClosed=True, color=(0, 255, 0), thickness=2)

            save_path = save_folder / f"{base}_overlay.jpg"
            _, enc = cv2.imencode(".jpg", overlay, [cv2.IMWRITE_JPEG_QUALITY, 90])
            with open(save_path, "wb") as f:
                f.write(enc.tobytes())

            processed_count += 1
            if limit and processed_count >= limit:
                break

        except Exception as e:
            print(f"❌ 發生錯誤於 {img_path}: {e}")
            continue

    print(f"\n✅ 已完成 {processed_count} 張 overlay 圖片儲存至：{save_folder}")


class RoadImageProcessor:
    def __init__(self,
                 source_roots,
                 dest_folder,
                 target_size=(1200, 900),
                 quality=75,
                 keep_original_name=False):
        self.source_roots = source_roots
        self.dest_folder = dest_folder
        self.target_w, self.target_h = target_size
        self.quality = quality
        self.keep_original_name = keep_original_name
        self.counter = 1

        os.makedirs(self.dest_folder, exist_ok=True)

        self.missing_json = []
        self.missing_image = []

    def _generate_unique_filename(self, base_name, extension):
        filename = f"{base_name}.{extension}"
        path = os.path.join(self.dest_folder, filename)
        suffix = 1
        while os.path.exists(path):
            filename = f"{base_name}_{suffix}.{extension}"
            path = os.path.join(self.dest_folder, filename)
            suffix += 1
        return filename

    def process(self):
        image_basenames = set()
        json_basenames = set()

        # 收集檔名 base 用於後續比對
        for source_root in self.source_roots:
            for root, _, files in os.walk(source_root):
                for file in files:
                    if file.lower().endswith('.jpg'):
                        image_basenames.add(os.path.splitext(file)[0])
                    elif file.lower().endswith('.json'):
                        json_basenames.add(os.path.splitext(file)[0])

        self.missing_json = sorted(list(image_basenames - json_basenames))
        self.missing_image = sorted(list(json_basenames - image_basenames))

        # 正式處理圖片
        for source_root in self.source_roots:
            for root, _, files in os.walk(source_root):
                for file in files:
                    if not file.lower().endswith('.jpg'):
                        continue

                    image_path = os.path.join(root, file)
                    label_path = os.path.join(root, os.path.splitext(file)[0] + '.json')

                    try:
                        buf = np.fromfile(image_path, dtype=np.uint8)
                        img = cv2.imdecode(buf, cv2.IMREAD_COLOR)
                    except Exception as e:
                        print(f"❌ 讀取圖片失敗 {image_path}，錯誤訊息：{e}")
                        continue

                    if img is None:
                        print(f"❌ 無法解碼圖片：{image_path}")
                        continue

                    result, enc_img = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, self.quality])
                    if not result:
                        print(f"❌ 圖片壓縮失敗：{image_path}")
                        continue
                    img_compressed = cv2.imdecode(enc_img, cv2.IMREAD_COLOR)
                    img_resized = cv2.resize(img_compressed, (self.target_w, self.target_h), interpolation=cv2.INTER_AREA)

                    if self.keep_original_name:
                        base_name = os.path.splitext(file)[0]
                        new_image_name = self._generate_unique_filename(base_name, "jpg")
                        new_label_name = os.path.splitext(new_image_name)[0] + ".json"
                    else:
                        new_image_name = f"image_{self.counter:04d}.jpg"
                        new_label_name = f"image_{self.counter:04d}.json"

                    dest_image_path = os.path.join(self.dest_folder, new_image_name)
                    result, final_enc = cv2.imencode('.jpg', img_resized, [cv2.IMWRITE_JPEG_QUALITY, self.quality])
                    if not result:
                        print(f"❌ 圖片編碼失敗：{dest_image_path}")
                        continue
                    with open(dest_image_path, "wb") as f:
                        f.write(final_enc.tobytes())
                    print(f"✅ 已處理圖片：{dest_image_path}")

                    if os.path.exists(label_path):
                        dest_label_path = os.path.join(self.dest_folder, new_label_name)
                        try:
                            with open(label_path, 'r', encoding='utf-8') as f:
                                label_data = json.load(f)
                            # 修改 imagePath 欄位，請確認該欄位在你的 JSON 結構中的位置
                            # 修改 imagePath 欄位
                            label_data["imagePath"] = new_image_name
                            
                            # ✅ 加上這段縮放處理：
                            orig_w = label_data.get("imageWidth", img.shape[1])
                            orig_h = label_data.get("imageHeight", img.shape[0])
                            scale_x = self.target_w / orig_w
                            scale_y = self.target_h / orig_h
                            for shape in label_data.get("shapes", []):
                                new_points = []
                                for point in shape.get("points", []):
                                    x, y = point
                                    new_points.append([x * scale_x, y * scale_y])
                                shape["points"] = new_points
                            label_data["imageWidth"] = self.target_w
                            label_data["imageHeight"] = self.target_h

                            # 將修改後的 JSON 資料寫回新的檔案中
                            with open(dest_label_path, 'w', encoding='utf-8') as f:
                                json.dump(label_data, f, ensure_ascii=False, indent=2)
                            print(f"📝 已複製並更新標籤：{dest_label_path}")
                        except Exception as e:
                            print(f"❌ 更新 JSON 檔失敗：{label_path}，錯誤訊息：{e}")
                    else:
                        print(f"⚠️ 找不到對應 JSON：{label_path}")

                    self.counter += 1

        self._save_missing_report()

    def _save_missing_report(self):
        report_lines = []

        if self.missing_json:
            report_lines.append(f"[⚠️ 缺少 JSON 的圖片，共 {len(self.missing_json)} 筆]")
            report_lines.extend(self.missing_json)
            report_lines.append("")

        if self.missing_image:
            report_lines.append(f"[⚠️ 缺少圖片的 JSON，共 {len(self.missing_image)} 筆]")
            report_lines.extend(self.missing_image)

        if not report_lines:
            report_lines.append("✅ 所有圖片與 JSON 對應正確，無缺失！")

        report_path = os.path.join(self.dest_folder, "missing_report.txt")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write("\n".join(report_lines))

        print("\n📋 缺失報告已儲存：", report_path)
        print("🧾 摘要：")
        print(f"  缺少 JSON 的圖片數量：{len(self.missing_json)}")
        print(f"  缺少圖片的 JSON 數量：{len(self.missing_image)}")

if __name__ == "__main__":
    # 控制是否執行圖片與標註處理
    input_folder = r"C:\Users\<USER>\Downloads\Pothole_Segmentation_YOLOv8"
    output_folder = r"C:\Users\<USER>\Downloads\process"
    overlay_output = r"C:\Users\<USER>\Downloads\AAA_overlaya"
    run_process = True           # ✅ 是否執行 processor.process()
    run_overlay = True            # ✅ 是否執行 overlay 可視化與保存
    overlay_limit = 5000            # ✅ 限制最多輸出幾張 overlay（設 None 表示不限制）
    # 若原始資料為 YOLO 格式，請加上：
    run_yolo_convert = True
    yolo_classes = ["potholes", "crack", "patch", "manhole"]

    if run_yolo_convert:
        convert_yolo_polygon_to_labelme(
            yolo_folder=input_folder,
            output_folder=input_folder,  # 直接存在原地或指定其他位置
            class_names=yolo_classes
        )

    processor = RoadImageProcessor(
        source_roots=[input_folder],
        dest_folder=output_folder,
        keep_original_name=True
    )

    if run_process:
        processor.process()

    if run_overlay:
        visualize_overlays_and_save(
            folder=processor.dest_folder,
            save_folder=overlay_output,
            limit=overlay_limit
        )
