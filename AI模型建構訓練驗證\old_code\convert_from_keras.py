import argparse
import collections
import h5py
import torch
import numpy as np
import os

alphabet = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z']

parser = argparse.ArgumentParser(description='Convert keras-mask-rcnn model to pytorch-mask-rcnn model')
parser.add_argument('--keras_model',
                    help='the path of the keras model',
                    default=None, type=str, required=True)
parser.add_argument('--pytorch_model',
                    help='the path of the pytorch model',
                    default=None, type=str, required=True)
parser.add_argument('--verbose',
                    help='print detailed conversion information',
                    action='store_true')

args = parser.parse_args()

print(f"Converting keras model: {args.keras_model}")
print(f"Output pytorch model will be saved to: {args.pytorch_model}")

# Create output directory if it doesn't exist
os.makedirs(os.path.dirname(os.path.abspath(args.pytorch_model)) or '.', exist_ok=True)

# Load the Keras model
f = h5py.File(args.keras_model, mode='r')
if args.verbose:
    print("Keys in the h5 file:", f.keys())

# Extract weights from the h5 file
state_dict = collections.OrderedDict()
skipped_layers = []

def process_dataset(name, dataset):
    """Process HDF5 dataset and extract weights"""
    if args.verbose:
        print(f"Processing dataset: {name}")
    try:
        if isinstance(dataset, h5py.Dataset):
            # It's a dataset with actual weight values
            key = name
            state_dict[key] = np.array(dataset)
            return True
        return False
    except Exception as e:
        print(f"Error loading dataset {name}: {e}")
        skipped_layers.append(name)
        return False

def process_group(name, group):
    """Process group and extract weights recursively"""
    if args.verbose:
        print(f"Processing group: {name}")
    
    try:
        # Process all items in the group
        for key, item in group.items():
            item_path = f"{name}.{key}" if name else key
            
            if isinstance(item, h5py.Group):
                # Recursively process nested groups
                process_group(item_path, item)
            elif isinstance(item, h5py.Dataset):
                process_dataset(item_path, item)
    except Exception as e:
        print(f"Error processing group {name}: {e}")
        skipped_layers.append(name)

# Use visit pattern to handle all types of groups and datasets
def visitor_func(name, obj):
    if isinstance(obj, h5py.Dataset):
        process_dataset(name, obj)
    
f.visititems(visitor_func)

print(f"Loaded {len(state_dict)} weight tensors from keras model")
if skipped_layers:
    print(f"Skipped {len(skipped_layers)} layers due to errors")

# Define mapping dictionaries for layer name conversion
replace_dict = collections.OrderedDict([
    # Weight naming convention mappings
    ('beta:0', 'bias'),
    ('gamma:0', 'weight'),
    ('moving_mean:0', 'running_mean'),
    ('moving_variance:0', 'running_var'),
    ('bias:0', 'bias'),
    ('kernel:0', 'weight'),
    
    # Model architecture specific mappings
    ('mrcnn_mask_', 'mask.'),
    ('mrcnn_mask', 'mask.conv5'),
    ('mrcnn_class_', 'classifier.'),
    ('logits', 'linear_class'),
    ('mrcnn_bbox_fc', 'classifier.linear_bbox'),
    ('rpn_', 'rpn.'),
    ('class_raw', 'conv_class'),
    ('bbox_pred', 'conv_bbox'),
    
    # FPN layer mappings
    ('bn_conv1', 'fpn.C1.1'),
    ('bn2a_branch1', 'fpn.C2.0.downsample.1'),
    ('res2a_branch1', 'fpn.C2.0.downsample.0'),
    ('bn3a_branch1', 'fpn.C3.0.downsample.1'),
    ('res3a_branch1', 'fpn.C3.0.downsample.0'),
    ('bn4a_branch1', 'fpn.C4.0.downsample.1'),
    ('res4a_branch1', 'fpn.C4.0.downsample.0'),
    ('bn5a_branch1', 'fpn.C5.0.downsample.1'),
    ('res5a_branch1', 'fpn.C5.0.downsample.0'),
    
    # FPN P layers
    ('fpn_c2p2', 'fpn.P2_conv1'),
    ('fpn_c3p3', 'fpn.P3_conv1'),
    ('fpn_c4p4', 'fpn.P4_conv1'),
    ('fpn_c5p5', 'fpn.P5_conv1'),
    ('fpn_p2', 'fpn.P2_conv2.1'),
    ('fpn_p3', 'fpn.P3_conv2.1'),
    ('fpn_p4', 'fpn.P4_conv2.1'),
    ('fpn_p5', 'fpn.P5_conv2.1'),
    
    # Additional FPN mappings based on the keys in paste.txt
    ('fpn_p2add', 'fpn.P2_add'),
    ('fpn_p3add', 'fpn.P3_add'),
    ('fpn_p4add', 'fpn.P4_add'),
    ('fpn_p3upsampled', 'fpn.P3_upsampled'),
    ('fpn_p4upsampled', 'fpn.P4_upsampled'),
    ('fpn_p5upsampled', 'fpn.P5_upsampled'),
    ('fpn_p6', 'fpn.P6'),
    
    # ROI layers
    ('roi_align_classifier', 'roi_align_classifier'),
    ('roi_align_mask', 'roi_align_mask'),
    
    # Additional layers from paste.txt
    ('proposal_targets', 'proposal_targets'),
    ('anchors', 'anchors'),
])

replace_exact_dict = collections.OrderedDict([
    ('conv1.bias', 'fpn.C1.0.bias'),
    ('conv1.weight', 'fpn.C1.0.weight'),
])

# Generate ResNet block mappings
# C2 blocks (3 blocks)
for block in range(3):
    for branch in range(3):
        replace_dict['bn2' + alphabet[block] + '_branch2' + alphabet[branch]] = 'fpn.C2.' + str(block) + '.bn' + str(branch+1)
        replace_dict['res2'+alphabet[block]+'_branch2'+alphabet[branch]] = 'fpn.C2.'+str(block)+'.conv'+str(branch+1)

# C3 blocks (4 blocks)
for block in range(4):
    for branch in range(3):
        replace_dict['bn3' + alphabet[block] + '_branch2' + alphabet[branch]] = 'fpn.C3.' + str(block) + '.bn' + str(branch+1)
        replace_dict['res3'+alphabet[block]+'_branch2'+alphabet[branch]] = 'fpn.C3.'+str(block)+'.conv'+str(branch+1)

# C4 blocks (23 blocks) - using ResNet-101 architecture mapping
for block in range(23):
    for branch in range(3):
        replace_dict['bn4' + alphabet[block] + '_branch2' + alphabet[branch]] = 'fpn.C4.' + str(block) + '.bn' + str(branch+1)
        replace_dict['res4'+alphabet[block]+'_branch2'+alphabet[branch]] = 'fpn.C4.'+str(block)+'.conv'+str(branch+1)

# C5 blocks (3 blocks)
for block in range(3):
    for branch in range(3):
        replace_dict['bn5' + alphabet[block] + '_branch2' + alphabet[branch]] = 'fpn.C5.' + str(block) + '.bn' + str(branch+1)
        replace_dict['res5'+ alphabet[block] + '_branch2' + alphabet[branch]] = 'fpn.C5.' + str(block) + '.conv' + str(branch+1)

# Rename keys according to PyTorch naming conventions
converted_keys = []
not_converted = []

for orig, repl in replace_dict.items():
    for key in list(state_dict.keys()):
        if orig in key:
            new_key = key.replace(orig, repl)
            state_dict[new_key] = state_dict[key]
            converted_keys.append((key, new_key))
            del state_dict[key]

# Handle exact matches
for orig, repl in replace_exact_dict.items():
    for key in list(state_dict.keys()):
        if orig == key:
            state_dict[repl] = state_dict[key]
            converted_keys.append((key, repl))
            del state_dict[key]

# Check for unconverted keys
for key in list(state_dict.keys()):
    # If the key still contains ':0', it was not converted
    if ':0' in key:
        not_converted.append(key)

if not_converted and args.verbose:
    print(f"\nWARNING: {len(not_converted)} weights were not converted:")
    for key in not_converted:
        print(f"  - {key}")

# Transpose weights to match PyTorch conventions
for weight_name in list(state_dict.keys()):
    # Convolutional layers: Keras (h, w, in_c, out_c) -> PyTorch (out_c, in_c, h, w)
    if state_dict[weight_name].ndim == 4:
        state_dict[weight_name] = state_dict[weight_name].transpose((3, 2, 0, 1)).copy(order='C')
    # Fully connected layers: Keras (in_features, out_features) -> PyTorch (out_features, in_features)
    if state_dict[weight_name].ndim == 2:
        state_dict[weight_name] = state_dict[weight_name].transpose((1, 0)).copy(order='C')

# Convert numpy arrays to PyTorch tensors
for weight_name in list(state_dict.keys()):
    state_dict[weight_name] = torch.from_numpy(state_dict[weight_name])

# Check for any weights that might need special handling
for key in list(state_dict.keys()):
    # Some models use Variable:0 format or other special formats
    if key.startswith('Variable:') or 'Variable' in key:
        try:
            if args.verbose:
                print(f"Found Variable key: {key}")
            # Try to identify what this variable is for and map accordingly
            # This is just a guess - real mapping would depend on model architecture
            if len(state_dict[key].shape) == 1:  # Likely bias
                new_key = "fpn.variable_bias"
                state_dict[new_key] = state_dict[key]
                converted_keys.append((key, new_key))
                del state_dict[key]
            elif len(state_dict[key].shape) == 4:  # Likely weights
                new_key = "fpn.variable_weight"
                state_dict[new_key] = state_dict[key]
                converted_keys.append((key, new_key))
                del state_dict[key]
        except Exception as e:
            print(f"Error handling Variable key {key}: {e}")
            skipped_layers.append(key)
    
    # Handle dataset-like objects that might be in state_dict
    if not isinstance(state_dict[key], np.ndarray) and not isinstance(state_dict[key], torch.Tensor):
        print(f"Warning: {key} is not a numpy array or tensor. Type: {type(state_dict[key])}")
        try:
            state_dict[key] = np.array(state_dict[key])
        except Exception as e:
            print(f"Could not convert {key} to numpy array: {e}")
            skipped_layers.append(key)
            del state_dict[key]

print(f"\nConversion completed. Total converted parameters: {len(state_dict)}")

# Save the PyTorch state_dict
torch.save(state_dict, args.pytorch_model)
print(f"PyTorch model saved to {args.pytorch_model}")

if args.verbose:
    print("\nDetailed conversion report:")
    print(f"Total keys converted: {len(converted_keys)}")
    print(f"Keys not converted: {len(not_converted)}")
    print(f"Skipped layers due to errors: {len(skipped_layers)}")
    
    # Print some sample conversions
    if converted_keys:
        print("\nSample conversions:")
        for i, (old, new) in enumerate(converted_keys[:10]):
            print(f"  {old} -> {new}")
        if len(converted_keys) > 10:
            print(f"  ... and {len(converted_keys)-10} more")
    
    # Print skipped layers if any
    if skipped_layers:
        print("\nSkipped layers due to errors:")
        for i, layer in enumerate(skipped_layers[:10]):
            print(f"  - {layer}")
        if len(skipped_layers) > 10:
            print(f"  ... and {len(skipped_layers)-10} more")