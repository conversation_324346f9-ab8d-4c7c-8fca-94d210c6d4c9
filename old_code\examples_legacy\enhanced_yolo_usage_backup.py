#!/usr/bin/env python3
"""
Enhanced YOLO使用示例
展示如何使用Enhanced YOLO進行道路基礎設施檢測
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

def main():
    """Enhanced YOLO使用示例主函數"""
    
    print("🚀 Enhanced YOLO使用示例")
    print("=" * 50)
    
    # ===================================================================
    # 📋 參數設定區域 - 請根據您的需求修改以下參數
    # ===================================================================
    
    # 🎯 基礎配置
    detection_model_path = ""                      # 檢測模型路徑 (YOLO12) - 空字符串則跳過檢測
    segmentation_model_path = r"D:\4_road_crack\best.pt"    # 分割模型路徑 (YOLO11) - 請替換為實際路徑
    input_path = r"D:\image\5_test_image_test\test1\image"                    # 輸入圖像或目錄路徑
    output_path = r"D:\image\5_test_image_test\test1_output"                       # 輸出目錄路徑
    task_type = "segmentation"                     # 任務類型: "detection", "segmentation", "both"
    
    # 🎛️ 推理參數
    img_size = 640                                 # 圖像大小
    global_conf = 0.05                             # 全局置信度閾值
    iou_threshold = 0.45                           # IoU閾值
    max_det = 1000                                 # 最大檢測數量
    device = "cuda"                                # 設備: "auto", "cuda", "cpu"
    
    # 🧩 SAHI配置 (大圖像切片推理) - 完整參數設定
    enable_sahi = False                             # 是否啟用SAHI
    
    # 核心切片參數
    slice_height = 512                             # 切片高度
    slice_width = 512                              # 切片寬度
    overlap_height_ratio = 0.2                     # 高度重疊比例 (0.0-1.0)
    overlap_width_ratio = 0.2                      # 寬度重疊比例 (0.0-1.0)
    
    # 進階SAHI參數
    auto_slice_resolution = True                    # 自動切片解析度
    perform_standard_pred = True                    # 執行標準預測
    roi_ratio = (0.0, 0.0, 1.0, 1.0)              # ROI區域比例 (x1, y1, x2, y2)
    
    # 後處理參數
    postprocess_type = "GREEDYNMM"                 # 後處理類型: "GREEDYNMM", "NMM", "NMS"
    postprocess_match_threshold = 0.1               # 後處理匹配閾值
    postprocess_class_agnostic = False             # 類別無關後處理
    
    # 類別過濾參數
    exclude_classes_by_name = []                    # 按名稱排除類別 ["unwanted_class"]
    exclude_classes_by_id = []                      # 按ID排除類別 [0, 1]
    
    # 輸出控制
    no_standard_prediction = False                  # 禁用標準預測
    no_sliced_prediction = False                    # 禁用切片預測
    export_pickle = False                           # 導出pickle格式
    export_crop = False                             # 導出裁剪圖像
    
    # 📊 輸出配置
    save_visualizations = True                     # 保存可視化結果
    save_predictions = True                        # 保存預測結果
    save_statistics = True                         # 保存統計信息
    
    # 🏷️ LabelMe自動配置 (如果有LabelMe標註)
    labelme_dir = r"D:\image\5_test_image_test\test1\label"                               # LabelMe標註目錄，空則使用預設類別
    auto_generate_classes = True                   # 是否自動從LabelMe生成類別配置
    
    # 🎨 進階配置
    batch_processing = True                        # 批次處理 (True=目錄, False=單張圖像)
    enable_tracking = False                        # 物件追蹤
    
    # ===================================================================
    # 🔍 執行檢查和驗證
    # ===================================================================
    
    if not ENHANCED_YOLO_AVAILABLE:
        print("❌ Enhanced YOLO模組不可用")
        print("請檢查import_helper.py和enhanced_yolo_inference.py")
        return
    
    # 檢查輸入路徑
    if not Path(input_path).exists():
        print(f"❌ 錯誤: 輸入路徑不存在: {input_path}")
        print("請修改input_path參數或創建對應目錄")
        return
    
    # 檢查模型路徑
    if not detection_model_path and not segmentation_model_path:
        print("❌ 錯誤: 至少需要指定一個模型路徑")
        print("請設置 detection_model_path 或 segmentation_model_path")
        return
    
    if segmentation_model_path and not Path(segmentation_model_path).exists():
        print(f"❌ 警告: 分割模型路徑不存在: {segmentation_model_path}")
        print("如果這是示例，請替換為實際的模型路徑")
        return
    
    # ===================================================================
    # 🚀 執行Enhanced YOLO推理
    # ===================================================================
    
    try:
        # 導入Enhanced YOLO相關模組
        from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
            EnhancedYOLOConfig, 
            create_enhanced_yolo_inference,
            scan_labelme_annotations,
            generate_class_configs_from_labelme
        )
        
        print("✅ Enhanced YOLO模組導入成功")
        
        # 自動生成類別配置
        class_configs = {}
        if labelme_dir and Path(labelme_dir).exists() and auto_generate_classes:
            print(f"🏷️  正在掃描LabelMe標註: {labelme_dir}")
            try:
                class_configs = generate_class_configs_from_labelme(labelme_dir)
                print(f"✅ 自動生成了 {len(class_configs)} 個類別配置")
            except Exception as e:
                print(f"⚠️  LabelMe掃描失敗: {e}")
                print("   將使用預設類別配置")
        
        # 創建配置對象
        config = EnhancedYOLOConfig(
            # 模型配置
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            device=device,
            
            # 推理配置
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            
            # 類別配置
            class_configs=class_configs,
            
            # SAHI配置
            enable_sahi=enable_sahi,
            slice_height=slice_height,
            slice_width=slice_width,
            overlap_height_ratio=overlap_height_ratio,
            overlap_width_ratio=overlap_width_ratio,
            
            # 輸出配置
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics,
            
            # 進階功能
            enable_tracking=enable_tracking,
            auto_convert_annotations=True
        )
        
        print("🔧 創建推理器...")
        inference = create_enhanced_yolo_inference(config)
        print("✅ 推理器創建成功")
        
        # 創建輸出目錄
        Path(output_path).mkdir(parents=True, exist_ok=True)
        
        print("📊 配置信息:")
        print(f"   🎯 任務類型: {task_type}")
        print(f"   📁 輸入: {input_path}")
        print(f"   📁 輸出: {output_path}")
        print(f"   🎛️  圖像大小: {img_size}")
        print(f"   🎛️  置信度: {global_conf}")
        print(f"   🧩 SAHI: {'啟用' if enable_sahi else '禁用'}")
        if class_configs:
            print(f"   🏷️  類別數: {len(class_configs)}")
        
        print("\\n🚀 開始推理...")
        
        if batch_processing and Path(input_path).is_dir():
            # 批次處理
            print(f"📂 批次處理目錄: {input_path}")
            
            # 檢查是否有標註文件
            input_dir = Path(input_path)
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
            annotation_extensions = ['.json', '.txt', '.xml']
            
            # 統計圖像和標註文件
            image_files = []
            for ext in image_extensions:
                image_files.extend(input_dir.glob(f"*{ext}"))
                image_files.extend(input_dir.glob(f"*{ext.upper()}"))
            
            annotation_files = []
            for ext in annotation_extensions:
                annotation_files.extend(input_dir.glob(f"*{ext}"))
                annotation_files.extend(input_dir.glob(f"*{ext.upper()}"))
            
            has_labels = len(annotation_files) > 0
            
            results = inference.batch_predict(
                input_dir=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            
            # 根據是否有標註文件調整顯示數量
            processed_count = len(results) if results else 0
            if has_labels and processed_count > 0:
                display_count = processed_count  # 如果有標註文件，數量除以2
                print(f"✅ 批次處理完成! 處理了 {display_count} 組數據 (圖像+標註)")
            else:
                print(f"✅ 批次處理完成! 處理了 {processed_count} 張圖像")
            
        else:
            # 單張圖像處理
            print(f"🖼️  處理單張圖像: {input_path}")
            results = inference.predict_single_image(
                image_path=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            print("✅ 單張圖像處理完成!")
        
        # 顯示統計信息
        if hasattr(inference, 'inference_stats'):
            stats = inference.inference_stats
            print("\\n📊 統計信息:")
            print(f"   🎯 總檢測數: {stats.get('total_detections', 0)}")
            print(f"   📊 類別統計: {stats.get('class_counts', {})}")
            print(f"   ⏱️  處理時間: {stats.get('total_time', 0):.2f}秒")
        
        print(f"\\n🎉 推理完成! 結果保存至: {output_path}")
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保Enhanced YOLO相關依賴已安裝:")
        print("   pip install torch torchvision ultralytics")
        print("   pip install opencv-python numpy matplotlib")
        print("   pip install sahi")
        
    except Exception as e:
        print(f"❌ 推理失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()