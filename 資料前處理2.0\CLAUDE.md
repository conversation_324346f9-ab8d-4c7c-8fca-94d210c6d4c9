# CLAUDE.md

This file provides guidance to <PERSON> Code (claude.ai/code) when working with code in this repository.

此文件為 Claude Code (claude.ai/code) 在此代碼庫中工作時提供指導。

## 項目概述

這是一個**企業級圖像數據集預處理工具集**，專為計算機視覺任務設計，特別針對物件檢測和圖像分割。工具集包含5個主要組件和2個GUI應用程式，代碼總量約**14,000行**，全部組件均已完整實現並可投入生產使用。

## 核心組件 (✅ 全部完整實現)
1. **標籤格式轉換器** (`annotation_converter.py`) - 1,837行代碼，支持VOC XML、YOLO、COCO JSON轉換為LabelMe格式，具備自動格式檢測和多線程處理
2. **標籤編輯器** (`annotation_editor.py`) - 1,436行代碼，支持標籤合併、重命名、刪除，具備操作歷史和回滾功能
3. **數據集分割器** (`dataset_divider.py`) - 1,393行代碼，智能分割數據集為訓練/驗證/測試集，支持類別平衡和斷點續傳
4. **圖像增強器** (`img_augmenter.py`) - 2,584行代碼，基於區域融合的增強技術，支持網格策略和類別特定目標數量
5. **全景圖像擴增器** (`panorama_augmenter.py`) - 953行代碼，**頂級功能**，實現標準攝影測量OPK旋轉矩陣，支持5種擴增方法和外方位參數處理

## GUI應用程式
6. **Tkinter GUI** (`gui_application.py`) - 756行代碼，基礎功能界面，僅全景擴增器完全整合
7. **PyQt6 GUI** (`pyqt_gui_application.py`) - 3,024行代碼，**現代化專業界面**，支持深色/淺色主題，所有5個工具完全整合，具備多線程處理和實時進度追蹤

## 管道整合
8. **整合流程** (`img_dataset_pipeline.py`) - 680行代碼，協調所有4個核心組件，支持JSON配置和步驟化執行

## 架構設計

### 核心架構模式
這是一個**模塊化管道設計**，支持獨立組件和順序流程兩種使用模式：

**主入口點層級**：
```
start.py (簡單啟動器)
├── 獨立工具 (每個組件可單獨執行)
├── img_dataset_pipeline.py (順序管道：Step 1→2→3→4)
└── GUI應用程式 (pyqt_gui_application.py 推薦)
```

**組件設計模式**：所有組件遵循統一模式：
```python
class ComponentClass:
    def __init__(self, config)          # 初始化
    def configure(self, params)         # 配置參數
    def run(self, input_data)          # 執行處理
    def interactive_mode(self)         # 交互式模式
```

**數據流設計**：
```
Raw Data (VOC/YOLO/COCO) → LabelMe標準 → 清理標籤 → 分割數據集 → 增強數據
```

**組件導入模式**：
```python
from annotation_converter import AnnotationConverter
from annotation_editor import AnnotationEditor  
from dataset_divider import DatasetDivider
from img_augmenter import ImageAugmenter
from panorama_augmenter import PanoramaAugmenter  # 獨立專業組件
```

### 配置系統架構
多層級配置優先級：`默認值 → JSON配置 → 命令行參數 → 交互式輸入 → 最終參數`

## 常用命令

### 環境設置
```bash
pip install -r requirements.txt
```

### 開發與測試命令
```bash
# 快速測試組件功能
python annotation_converter.py --help
python panorama_augmenter.py --help

# 運行完整測試流程（使用test/目錄數據）
python demo_with_visualization.py

# 運行GUI測試
python test_pyqt_gui.py

# 檢查組件獨立性
python -c "from panorama_augmenter import PanoramaAugmenter; print('Import successful')"
```

### 啟動GUI應用程式（推薦）
```bash
# 啟動現代化PyQt GUI界面（推薦）
python pyqt_gui_application.py

# 啟動基礎Tkinter界面（僅全景擴增器完整）
python gui_application.py
```

### 運行完整流程
```bash
# 交互式模式執行所有步驟
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --all --interactive --resize 1

# 使用配置文件
python img_dataset_pipeline.py --config config.json --output 輸出目錄

# 只執行特定步驟
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --steps 1,3
```

### 運行獨立組件
```bash
# 轉換標籤格式
python annotation_converter.py --inputs 資料目錄1 資料目錄2 --output 輸出目錄 --resize 0.5 --quality 80

# 交互式編輯標籤
python annotation_editor.py --input 輸入目錄 --output 輸出目錄 --interactive

# 分割數據集
python dataset_divider.py --input 輸入目錄 --output 輸出目錄 --train-ratio 0.8 --val-ratio 0.1 --test-ratio 0.1

# 圖像增強
python img_augmenter.py --sources 來源目錄 --target 目標目錄 --output 輸出目錄 --num 200 --interactive

# 全景圖像擴增（單張處理）
python panorama_augmenter.py --input 全景圖像.jpg --label 標籤.json --output 輸出目錄 --omega -74.77 --phi -86.70 --kappa -165.42 --methods rotation perspective

# 全景圖像擴增（批量處理）
python panorama_augmenter.py --input 輸入目錄 --output 輸出目錄 --excel 外方位.xlsx --methods rotation perspective tilt --batch

# 全景圖像擴增（交互式）
python panorama_augmenter.py --output 輸出目錄 --interactive
```

## 配置說明

工具集使用JSON配置文件(參見 `config.json` 範例)來指定所有流程步驟的參數。強烈建議初次使用者使用交互式模式來理解參數效果。

## 關鍵設計模式

### 核心設計原則
- **統一組件模式**：初始化 → 配置 → 處理 → 輸出
- **模塊化獨立性**：每個組件可獨立導入和使用
- **多線程架構**：ThreadPoolExecutor用於性能關鍵操作
- **錯誤恢復機制**：自定義異常層級和graceful degradation
- **LabelMe標準化**：統一使用LabelMe JSON作為標籤交換格式

### GUI架構模式
**PyQt6 GUI** (推薦)：
- `WorkerThread` 基類：非阻塞操作的統一多線程模式
- `ThemeManager`：深色/淺色主題管理
- 自定義 `LogHandler`：實時進度追蹤
- 選項卡式架構：5個工具完全整合

### 專業特色：全景圖像處理
- **攝影測量標準**：OPK旋轉矩陣實現
- **5種擴增方法**：rotation, perspective, tilt, orientation, multi_angle
- **外方位參數支持**：Excel批量輸入和參數驗證
- **精確座標轉換**：球面座標系統和標籤同步變換

### 記憶體管理模式
- 顯式垃圾收集 (`gc.collect()`)
- 檢查點/恢復機制用於大數據集
- 批量處理防止記憶體溢出

## 測試與文檔

### 測試覆蓋
- **test/** 目錄包含31個JSON測試文件和5個可視化圖像
- 所有5種全景擴增方法均已測試
- 使用真實台北全景圖像和外方位數據
- 專用PyQt GUI測試腳本

### 文檔系統
- **4個Markdown文檔**：完整使用手冊和README文件
- **2個Demo腳本**：帶可視化的示例代碼
- **HTML教程**：旋轉變換教程和Jupyter範例
- **樣本數據**：真實全景圖像和外方位文件

## 項目狀態

### ✅ 生產就緒功能
- 所有5個核心工具完全實現並測試
- PyQt GUI提供完整的現代化界面
- 管道整合支持完整工作流程
- 專業級全景圖像處理（攝影測量標準）
- 全面的配置和文檔支援

### 📋 技術指標
- **代碼總量**：約14,000行Python代碼
- **類別數量**：42+個設計良好的類
- **錯誤處理**：自定義異常層級結構
- **國際化**：完整中文界面支援
- **記憶體管理**：適當的清理和垃圾收集

### 🎯 推薦使用方式
1. **新用戶**：使用 `python pyqt_gui_application.py` 啟動現代化GUI
2. **批量處理**：使用 `python img_dataset_pipeline.py` 進行自動化處理
3. **全景圖像**：重點使用全景擴增器進行專業級處理
4. **開發整合**：直接導入各模組類別進行程式化使用

## 開發指南

### 添加新功能時的重要考慮
1. **組件獨立性**：確保新功能可以獨立運行，不依賴其他組件
2. **配置一致性**：遵循JSON配置和命令行參數的統一模式
3. **多線程支持**：使用ThreadPoolExecutor進行並行處理
4. **錯誤處理**：實現適當的異常捕獲和用戶友好的錯誤信息
5. **日誌記錄**：使用統一的日誌格式和等級
6. **LabelMe格式**：確保標籤數據符合LabelMe JSON格式標準

### 組件間協作規則
- **Step 1 (AnnotationConverter)**：必須輸出LabelMe格式
- **Step 2-4**：必須能夠處理LabelMe格式輸入
- **PanoramaAugmenter**：獨立組件，可處理任何2:1比例圖像
- **GUI整合**：新工具需同時支持PyQt6和基礎模式

### 測試要求
- 在 `test/` 目錄添加相應測試文件
- 使用真實數據進行功能驗證
- 確保可視化功能正常工作
- 驗證批量處理和交互模式