# Copyright (c) OpenMMLab. All rights reserved.
# 修復錯誤的導入路徑
try:
    from .regi.registry import Registry
except ImportError:
    # 如果導入失敗，定義基礎版本
    class Registry:
        def __init__(self, name):
            self._name = name
            self._module_dict = {}
        
        def register_module(self, name=None, force=False, module=None):
            def _register(cls):
                return cls
            return _register

CONV_LAYERS = Registry('conv layer')
NORM_LAYERS = Registry('norm layer')
ACTIVATION_LAYERS = Registry('activation layer')
PADDING_LAYERS = Registry('padding layer')
UPSAMPLE_LAYERS = Registry('upsample layer')
PLUGIN_LAYERS = Registry('plugin layer')

DROPOUT_LAYERS = Registry('drop out layers')
POSITIONAL_ENCODING = Registry('position encoding')
ATTENTION = Registry('attention')
FEEDFORWARD_NETWORK = Registry('feed-forward Network')
TRANSFORMER_LAYER = Registry('transformerLayer')
TRANSFORMER_LAYER_SEQUENCE = Registry('transformer-layers sequence')
