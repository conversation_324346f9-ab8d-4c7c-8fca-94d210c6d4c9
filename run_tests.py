#!/usr/bin/env python3
"""
測試運行腳本
運行所有測試並生成報告
"""

import os
import sys
import logging
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

try:
    from AI模型建構訓練驗證.model_create.util.test_framework import (
        TestConfig, TestRunner, CIRunner, run_all_tests, run_ci_pipeline
    )
    TEST_FRAMEWORK_AVAILABLE = True
except ImportError as e:
    print(f"Test framework not available: {e}")
    TEST_FRAMEWORK_AVAILABLE = False

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_basic_tests():
    """運行基礎測試（不依賴測試框架）"""
    logger.info("運行基礎測試...")
    
    import unittest
    
    # 發現並運行測試
    loader = unittest.TestLoader()
    start_dir = Path(__file__).parent / "tests"
    
    if not start_dir.exists():
        logger.warning(f"測試目錄不存在: {start_dir}")
        return False
    
    try:
        suite = loader.discover(str(start_dir), pattern='test_*.py')
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        logger.info(f"測試完成 - 運行: {result.testsRun}, 失敗: {len(result.failures)}, 錯誤: {len(result.errors)}")
        
        if result.failures:
            logger.error("測試失敗:")
            for test, traceback in result.failures:
                logger.error(f"  {test}: {traceback}")
        
        if result.errors:
            logger.error("測試錯誤:")
            for test, traceback in result.errors:
                logger.error(f"  {test}: {traceback}")
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except Exception as e:
        logger.error(f"運行基礎測試失敗: {e}")
        return False


def run_framework_tests():
    """使用測試框架運行測試"""
    logger.info("使用測試框架運行測試...")
    
    try:
        # 配置測試
        config = TestConfig(
            test_dirs=[
                "tests/",
                "AI模型建構訓練驗證/tests/",
                "資料前處理/tests/"
            ],
            output_dir="./test_results",
            generate_html_report=True,
            generate_coverage_report=False,  # 暫時禁用覆蓋率報告
            verbose=True
        )
        
        # 運行測試
        runner = TestRunner(config)
        result = runner.run_tests()
        
        logger.info("=== 測試結果 ===")
        logger.info(f"通過: {result.passed}")
        logger.info(f"失敗: {result.failed}")
        logger.info(f"跳過: {result.skipped}")
        logger.info(f"總計: {result.total}")
        logger.info(f"成功率: {result.success_rate:.1f}%")
        logger.info(f"執行時間: {result.duration:.2f}秒")
        
        if result.errors:
            logger.error("錯誤詳情:")
            for error in result.errors:
                logger.error(f"  {error}")
        
        return result.failed == 0
        
    except Exception as e:
        logger.error(f"框架測試失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def check_project_structure():
    """檢查項目結構"""
    logger.info("檢查項目結構...")
    
    required_paths = [
        "AI模型建構訓練驗證/model_create",
        "AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py",
        "AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py",
        "AI模型建構訓練驗證/enhanced_yolo_gui.py",
        "資料前處理",
        "tests"
    ]
    
    missing_paths = []
    for path in required_paths:
        if not Path(path).exists():
            missing_paths.append(path)
    
    if missing_paths:
        logger.warning("缺少以下路徑:")
        for path in missing_paths:
            logger.warning(f"  {path}")
        return False
    
    logger.info("項目結構檢查通過")
    return True


def main():
    """主函數"""
    logger.info("=== 道路基礎設施AI檢測框架測試 ===")
    
    # 檢查項目結構
    if not check_project_structure():
        logger.warning("項目結構不完整，但繼續運行測試")
    
    # 選擇測試運行方式
    if TEST_FRAMEWORK_AVAILABLE:
        logger.info("使用統一測試框架")
        success = run_framework_tests()
    else:
        logger.info("使用基礎測試方式")
        success = run_basic_tests()
    
    if success:
        logger.info("🎉 所有測試通過！")
    else:
        logger.error("❌ 測試失敗")
    
    # 顯示下一步
    logger.info("\n=== 下一步 ===")
    logger.info("1. 查看測試報告: ./test_results/")
    logger.info("2. 運行CI/CD: python run_tests.py --ci")
    logger.info("3. 生成API文檔: python generate_docs_simple.py")
    
    return success


def run_ci():
    """運行CI/CD管線"""
    logger.info("=== CI/CD管線 ===")
    
    if not TEST_FRAMEWORK_AVAILABLE:
        logger.error("CI/CD需要測試框架支援")
        return False
    
    try:
        success = run_ci_pipeline(
            output_dir="./test_results",
            verbose=True
        )
        
        if success:
            logger.info("🎉 CI/CD管線通過！")
        else:
            logger.error("❌ CI/CD管線失敗")
        
        return success
        
    except Exception as e:
        logger.error(f"CI/CD管線錯誤: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="測試運行腳本")
    parser.add_argument("--ci", action="store_true", help="運行CI/CD管線")
    parser.add_argument("--verbose", "-v", action="store_true", help="詳細輸出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    if args.ci:
        success = run_ci()
    else:
        success = main()
    
    sys.exit(0 if success else 1)