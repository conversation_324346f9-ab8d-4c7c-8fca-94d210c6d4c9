#!/usr/bin/env python3
"""
測試shared模組功能

此腳本用於驗證第一階段重構完成的shared模組功能
"""

import sys
from pathlib import Path
import tempfile

# 添加shared模組到路徑
sys.path.insert(0, str(Path(__file__).parent))

try:
    from shared import (
        BaseTool, ConfigManager, BaseConfig,
        ProcessingError, ConfigError, ValidationError,
        FileUtils, PathUtils, StructuredLogger, setup_logger
    )
    print("✅ 所有shared模組導入成功")
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)


def test_config_manager():
    """測試配置管理器"""
    print("\n🔧 測試ConfigManager...")
    
    try:
        # 創建臨時配置目錄
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)
            
            # 測試默認配置創建
            config_manager.create_default_configs()
            print("  ✅ 默認配置創建成功")
            
            # 測試配置加載
            converter_config = config_manager.get_converter_config("default")
            print(f"  ✅ 轉換器配置加載: {converter_config.input_format} -> {converter_config.output_format}")
            
            # 測試GUI配置
            gui_config = config_manager.get_gui_config()
            print(f"  ✅ GUI配置加載: {gui_config['window_size']}")
            
            print("  ✅ ConfigManager測試通過")
            
    except Exception as e:
        print(f"  ❌ ConfigManager測試失敗: {e}")


def test_file_utils():
    """測試文件工具"""
    print("\n📁 測試FileUtils...")
    
    try:
        # 創建臨時目錄
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 測試目錄創建
            test_dir = temp_path / "test_subdir"
            FileUtils.ensure_dir(test_dir)
            assert test_dir.exists()
            print("  ✅ 目錄創建成功")
            
            # 測試文件創建和複製
            test_file = test_dir / "test.txt"
            test_file.write_text("Hello World", encoding='utf-8')
            
            copy_file = test_dir / "test_copy.txt"
            FileUtils.safe_copy(test_file, copy_file)
            assert copy_file.exists()
            print("  ✅ 文件複製成功")
            
            # 測試文件信息
            file_info = FileUtils.get_file_info(test_file)
            print(f"  ✅ 文件信息: {file_info['name']}, {file_info['size']} bytes")
            
            print("  ✅ FileUtils測試通過")
            
    except Exception as e:
        print(f"  ❌ FileUtils測試失敗: {e}")


def test_path_utils():
    """測試路徑工具"""
    print("\n🛤️  測試PathUtils...")
    
    try:
        # 測試中文路徑檢測
        chinese_path = "測試/路徑"
        english_path = "test/path"
        
        assert PathUtils.is_chinese_path(chinese_path)
        assert not PathUtils.is_chinese_path(english_path)
        print("  ✅ 中文路徑檢測正常")
        
        # 測試文件名清理
        dirty_filename = "file<>:|*.txt"
        clean_filename = PathUtils.sanitize_filename(dirty_filename)
        print(f"  ✅ 文件名清理: '{dirty_filename}' -> '{clean_filename}'")
        
        # 測試路徑標準化
        test_path = Path("./test/../test.txt")
        normalized = PathUtils.normalize_path(test_path)
        print(f"  ✅ 路徑標準化: '{test_path}' -> '{normalized}'")
        
        print("  ✅ PathUtils測試通過")
        
    except Exception as e:
        print(f"  ❌ PathUtils測試失敗: {e}")


def test_logger():
    """測試日誌工具"""
    print("\n📝 測試StructuredLogger...")
    
    try:
        # 創建日誌記錄器
        logger = setup_logger("test_logger", level="DEBUG")
        
        # 測試各種日誌方法
        logger.info("這是一條測試信息")
        logger.log_processing_start("/path/to/file.txt", "轉換")
        logger.log_processing_success("/path/to/file.txt", "轉換", duration="1.5s")
        logger.log_progress(50, 100, "處理")
        
        stats = {"processed": 50, "failed": 2, "skipped": 3}
        logger.log_statistics(stats, "轉換")
        
        print("  ✅ 日誌記錄功能正常")
        print("  ✅ StructuredLogger測試通過")
        
    except Exception as e:
        print(f"  ❌ StructuredLogger測試失敗: {e}")


class TestTool(BaseTool):
    """測試工具類"""
    
    def validate_inputs(self) -> bool:
        """驗證輸入"""
        return True
    
    def _execute_main_logic(self, **kwargs):
        """執行邏輯"""
        self.stats['total_files'] = 10
        
        # 模擬處理文件
        for i in range(10):
            self.log_processing_start(f"file_{i}.txt", "測試處理")
            
            if i == 7:  # 模擬一個錯誤
                self.log_processing_error(f"file_{i}.txt", Exception("測試錯誤"), "測試處理")
            else:
                self.log_processing_success(f"file_{i}.txt", "測試處理")
        
        return self.stats


def test_base_tool():
    """測試基礎工具類"""
    print("\n🔨 測試BaseTool...")
    
    try:
        # 創建測試工具
        tool = TestTool()
        
        # 執行工具
        result = tool.run()
        
        # 檢查結果
        assert result['total_files'] == 10
        assert result['processed_files'] == 9
        assert result['failed_files'] == 1
        
        # 檢查進度
        progress = tool.get_progress()
        assert progress == 1.0
        
        # 檢查摘要
        summary = tool.get_summary()
        print(f"  ✅ 工具摘要: {summary['tool_name']}, 成功率: {summary.get('success_rate', 0):.2f}")
        
        print("  ✅ BaseTool測試通過")
        
    except Exception as e:
        print(f"  ❌ BaseTool測試失敗: {e}")


def test_exceptions():
    """測試異常處理"""
    print("\n⚠️  測試異常處理...")
    
    try:
        # 測試基礎異常
        try:
            raise ProcessingError("測試錯誤", file_path="/test/file.txt", 
                                details={"operation": "test", "step": 1})
        except ProcessingError as e:
            assert e.file_path == "/test/file.txt"
            assert e.details["operation"] == "test"
            print("  ✅ ProcessingError功能正常")
        
        # 測試配置異常
        try:
            raise ConfigError("配置錯誤", config_key="max_workers")
        except ConfigError as e:
            assert e.config_key == "max_workers"
            print("  ✅ ConfigError功能正常")
        
        print("  ✅ 異常處理測試通過")
        
    except Exception as e:
        print(f"  ❌ 異常處理測試失敗: {e}")


def main():
    """主測試函數"""
    print("🚀 開始測試資料前處理shared模組...")
    print("=" * 50)
    
    # 運行所有測試
    test_config_manager()
    test_file_utils()
    test_path_utils()
    test_logger()
    test_base_tool()
    test_exceptions()
    
    print("\n" + "=" * 50)
    print("✅ 所有shared模組測試完成！")
    print("\n📋 第一階段重構成果:")
    print("  🔧 BaseTool - 統一工具基類")
    print("  📄 ConfigManager - 配置管理系統")
    print("  📁 FileUtils/PathUtils - 文件處理工具")
    print("  📝 StructuredLogger - 結構化日誌")
    print("  ⚠️  Exception體系 - 統一異常處理")
    print("\n🎯 下一步: 開始階段2 - 核心類別重構")


if __name__ == "__main__":
    main()