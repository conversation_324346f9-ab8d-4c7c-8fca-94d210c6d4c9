#!/usr/bin/env python3
"""
SAM微調訓練使用範例
展示如何使用新的SAM微調系統訓練道路基礎設施檢測模型
"""

import os
import sys
import logging
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

from model_create.training.sam_finetuning_trainer import (
    SAMTrainingConfig, SAMFineTuningTrainer, SAMPromptDataset,
    create_sam_finetuning_trainer, create_sam_dataset
)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函數"""
    
    # === 配置設置 ===
    config = SAMTrainingConfig(
        # 模型配置
        model_type="vit_h",  # 使用最大的SAM模型
        sam_checkpoint="./checkpoints/sam_vit_h_4b8939.pth",
        freeze_image_encoder=False,  # 微調圖像編碼器
        freeze_prompt_encoder=True,  # 凍結提示編碼器
        freeze_mask_decoder=False,   # 微調遮罩解碼器
        
        # LoRA配置（推薦用於大模型）
        use_lora=True,
        lora_rank=4,
        lora_alpha=32,
        lora_dropout=0.1,
        
        # 訓練配置
        num_epochs=50,
        batch_size=2,  # 根據GPU記憶體調整
        learning_rate=1e-4,
        weight_decay=0.01,
        warmup_epochs=5,
        
        # 損失配置
        loss_weights={
            "focal": 2.0,
            "dice": 1.0, 
            "iou": 1.0
        },
        use_focal_loss=True,
        use_dice_loss=True,
        use_iou_loss=True,
        
        # 數據配置
        image_size=1024,
        prompt_type="auto",  # 自動生成點和框提示
        num_points=5,
        num_classes=5,
        
        # 優化配置
        optimizer="adamw",
        scheduler="cosine",
        gradient_clip=1.0,
        accumulation_steps=2,  # 梯度累積以模擬更大batch
        
        # 保存配置
        save_every=10,
        val_every=5,
        early_stopping_patience=15
    )
    
    # === 數據準備 ===
    
    # 使用test_image作為示例數據
    data_root = Path("./test_image")
    
    # 檢查數據
    if not data_root.exists():
        logger.error(f"數據目錄不存在: {data_root}")
        return
    
    # 創建訓練數據集
    train_dataset = create_sam_dataset(
        images_dir=str(data_root),
        masks_dir=str(data_root),  # 假設遮罩在同一目錄
        image_size=config.image_size,
        prompt_type=config.prompt_type,
        num_points=config.num_points
    )
    
    logger.info(f"訓練數據集大小: {len(train_dataset)}")
    
    # 可選：創建驗證數據集
    # val_dataset = create_sam_dataset(...)
    
    # === 創建訓練器 ===
    save_dir = "./sam_training_results"
    trainer = create_sam_finetuning_trainer(config, save_dir)
    
    # === 開始訓練 ===
    logger.info("開始SAM微調訓練...")
    
    try:
        history = trainer.fit(
            train_dataset=train_dataset,
            val_dataset=None,  # 如果有驗證集可以傳入
        )
        
        logger.info("訓練完成！")
        logger.info(f"最終訓練損失: {history['train_loss'][-1]:.4f}")
        
        if 'val_iou' in history and history['val_iou']:
            logger.info(f"最佳驗證IoU: {max(history['val_iou']):.4f}")
        
    except Exception as e:
        logger.error(f"訓練過程中出現錯誤: {e}")
        raise
    
    # === 模型使用示例 ===
    logger.info("訓練完成，可以使用以下方式載入模型進行推理：")
    
    example_code = '''
# 載入微調後的模型
import torch
from segment_anything import sam_model_registry

# 載入基礎SAM模型
model = sam_model_registry["vit_h"](checkpoint="./checkpoints/sam_vit_h_4b8939.pth")

# 載入微調權重
checkpoint = torch.load("./sam_training_results/best_model.pth")
model.load_state_dict(checkpoint['model_state_dict'])

# 創建預測器
from segment_anything import SamPredictor
predictor = SamPredictor(model)

# 進行推理
predictor.set_image(your_image)
masks, scores, logits = predictor.predict(
    point_coords=your_points,
    point_labels=your_labels,
    box=your_box,
    multimask_output=False
)
    '''
    
    print("\n=== 使用範例 ===")
    print(example_code)


def quick_test():
    """快速測試 - 使用較小的配置"""
    
    logger.info("執行快速測試...")
    
    # 簡化配置用於測試
    config = SAMTrainingConfig(
        model_type="vit_b",  # 使用較小的模型
        num_epochs=5,        # 少量epoch
        batch_size=1,        # 小batch
        learning_rate=1e-3,  # 較高學習率
        use_lora=True,       # 使用LoRA減少記憶體
        lora_rank=2,         # 較小的rank
        image_size=512,      # 較小的圖像大小
        val_every=2,
        save_every=2
    )
    
    # 模擬數據（實際使用時需要真實數據）
    logger.info("注意：這是一個快速測試，需要真實數據才能正常運行")
    
    return config


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="SAM微調訓練範例")
    parser.add_argument("--mode", choices=["full", "test"], default="full",
                       help="運行模式: full=完整訓練, test=快速測試")
    parser.add_argument("--config", help="配置文件路徑")
    
    args = parser.parse_args()
    
    if args.mode == "test":
        config = quick_test()
        logger.info("快速測試配置已準備就緒")
    else:
        main()