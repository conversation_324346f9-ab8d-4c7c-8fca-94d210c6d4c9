#!/usr/bin/env python3
"""
測試GT和預測結果顏色一致性
"""

def test_gt_pred_color_consistency():
    """測試GT和預測結果是否使用一致的顏色方案"""
    
    # 模擬類別配置
    class ClassConfig:
        def __init__(self, name, threshold, color, enabled):
            self.name = name
            self.threshold = threshold
            self.color = color
            self.enabled = enabled
    
    # 模擬配置
    class_configs = {
        0: ClassConfig("expansion_joint_伸縮縫", 0.5, (255, 100, 100), True),  # 紅色系
        1: ClassConfig("joint_路面接縫", 0.4, (100, 255, 100), True),          # 綠色系  
        2: ClassConfig("linear_crack_裂縫", 0.6, (100, 100, 255), True),        # 藍色系
        3: ClassConfig("Alligator_crack_龜裂", 0.3, (255, 255, 100), True),    # 黃色系
        4: ClassConfig("potholes_坑洞", 0.5, (255, 100, 255), True),           # 洋紅色系
    }
    
    print("=== GT和預測顏色一致性測試 ===")
    print()
    
    # 模擬GT和預測數據
    test_data = [
        {'class_id': 0, 'class_name': 'expansion_joint_伸縮縫', 'type': 'GT'},
        {'class_id': 0, 'class_name': 'expansion_joint_伸縮縫', 'type': 'Prediction'},
        {'class_id': 1, 'class_name': 'joint_路面接縫', 'type': 'GT'},
        {'class_id': 1, 'class_name': 'joint_路面接縫', 'type': 'Prediction'},
        {'class_id': 2, 'class_name': 'linear_crack_裂縫', 'type': 'GT'},
        {'class_id': 2, 'class_name': 'linear_crack_裂縫', 'type': 'Prediction'},
        {'class_id': 99, 'class_name': 'unknown_class', 'type': 'GT'},  # 未配置的類別
        {'class_id': 99, 'class_name': 'unknown_class', 'type': 'Prediction'},
    ]
    
    def get_gt_color(class_id, class_configs):
        """模擬GT顏色邏輯"""
        if class_id in class_configs:
            base_color = class_configs[class_id].color
            # GT使用稍暗的版本以區分預測
            color = tuple(max(0, min(255, int(c * 0.8))) for c in base_color)
            polygon_color = tuple(max(0, min(255, int(c * 0.6))) for c in base_color)
            return color, polygon_color
        else:
            # 如果沒有配置，使用預設綠色
            return (0, 255, 0), (0, 200, 0)
    
    def get_prediction_color(class_id, class_configs):
        """模擬預測顏色邏輯"""
        if class_id in class_configs:
            color = class_configs[class_id].color
            return color
        else:
            return (255, 0, 0)  # 預設紅色
    
    print("=== 顏色對比結果 ===")
    
    # 按類別分組測試
    classes_tested = set()
    color_consistency_results = []
    
    for item in test_data:
        class_id = item['class_id']
        class_name = item['class_name']
        item_type = item['type']
        
        if class_id not in classes_tested:
            classes_tested.add(class_id)
            
            # 獲取GT和預測顏色
            gt_color, gt_polygon_color = get_gt_color(class_id, class_configs)
            pred_color = get_prediction_color(class_id, class_configs)
            
            print(f"\n類別 {class_id}: {class_name}")
            print(f"  預測顏色:     {pred_color}")
            print(f"  GT邊界框顏色: {gt_color}")
            print(f"  GT多邊形顏色: {gt_polygon_color}")\n            \n            # 檢查顏色是否相關（基於相同的基礎顏色）\n            if class_id in class_configs:\n                base_color = class_configs[class_id].color\n                expected_gt_color = tuple(max(0, min(255, int(c * 0.8))) for c in base_color)\n                expected_gt_polygon = tuple(max(0, min(255, int(c * 0.6))) for c in base_color)\n                \n                gt_color_correct = gt_color == expected_gt_color\n                gt_polygon_correct = gt_polygon_color == expected_gt_polygon\n                pred_color_correct = pred_color == base_color\n                \n                print(f\"  ✅ 預測顏色正確: {pred_color_correct}\")\n                print(f\"  ✅ GT邊界框正確: {gt_color_correct}\")\n                print(f\"  ✅ GT多邊形正確: {gt_polygon_correct}\")\n                \n                # 檢查是否屬於同一色系\n                color_family_match = (\n                    all(abs(pred_color[i] - gt_color[i]) <= abs(pred_color[i] * 0.2) + 10 for i in range(3))\n                )\n                print(f\"  🎨 顏色系一致性: {color_family_match}\")\n                \n                color_consistency_results.append({\n                    'class_id': class_id,\n                    'class_name': class_name,\n                    'pred_correct': pred_color_correct,\n                    'gt_correct': gt_color_correct,\n                    'polygon_correct': gt_polygon_correct,\n                    'family_match': color_family_match\n                })\n            else:\n                print(f\"  ⚠️  未配置類別，使用預設顏色\")\n                fallback_correct = (gt_color == (0, 255, 0) and pred_color == (255, 0, 0))\n                print(f\"  📝 預設顏色設定: {fallback_correct}\")\n                \n                color_consistency_results.append({\n                    'class_id': class_id,\n                    'class_name': class_name,\n                    'pred_correct': True,  # 預設值算正確\n                    'gt_correct': True,\n                    'polygon_correct': True,\n                    'family_match': False  # 預設顏色不同色系\n                })\n    \n    # 整體評估\n    print(f\"\\n=== 整體評估 ===\\n\")\n    \n    total_classes = len(color_consistency_results)\n    pred_correct_count = sum(1 for r in color_consistency_results if r['pred_correct'])\n    gt_correct_count = sum(1 for r in color_consistency_results if r['gt_correct'])\n    polygon_correct_count = sum(1 for r in color_consistency_results if r['polygon_correct'])\n    family_match_count = sum(1 for r in color_consistency_results if r['family_match'])\n    \n    print(f\"測試類別總數: {total_classes}\")\n    print(f\"預測顏色正確: {pred_correct_count}/{total_classes} ({pred_correct_count/total_classes*100:.1f}%)\")\n    print(f\"GT邊界框正確: {gt_correct_count}/{total_classes} ({gt_correct_count/total_classes*100:.1f}%)\")\n    print(f\"GT多邊形正確: {polygon_correct_count}/{total_classes} ({polygon_correct_count/total_classes*100:.1f}%)\")\n    print(f\"顏色系一致性: {family_match_count}/{total_classes} ({family_match_count/total_classes*100:.1f}%)\")\n    \n    # 驗證結果\n    all_tests_passed = (\n        pred_correct_count == total_classes and\n        gt_correct_count == total_classes and\n        polygon_correct_count == total_classes\n    )\n    \n    color_consistency_good = family_match_count >= (total_classes - 1)  # 允許一個未配置類別\n    \n    if all_tests_passed and color_consistency_good:\n        print(f\"\\n🎉 GT和預測顏色一致性測試通過！\")\n        print(f\"✅ GT使用了與預測相同的基礎顏色，但稍作區分\")\n        print(f\"✅ 同一類別的GT和預測屬於相同色系\")\n        print(f\"✅ 視覺上容易關聯相同類別的GT和預測\")\n    else:\n        print(f\"\\n❌ 顏色一致性測試失敗\")\n        if not all_tests_passed:\n            print(f\"❌ 顏色計算邏輯有誤\")\n        if not color_consistency_good:\n            print(f\"❌ GT和預測顏色系不夠一致\")\n    \n    # 顯示顏色映射表\n    print(f\"\\n=== 顏色映射表 ===\\n\")\n    print(f\"{'類別ID':<8} {'類別名稱':<20} {'預測顏色':<15} {'GT顏色':<15} {'相關性'}\")\n    print(\"-\" * 75)\n    \n    for result in color_consistency_results:\n        class_id = result['class_id']\n        class_name = result['class_name'][:18] + \"..\" if len(result['class_name']) > 20 else result['class_name']\n        \n        if class_id in class_configs:\n            pred_color = class_configs[class_id].color\n            gt_color, _ = get_gt_color(class_id, class_configs)\n        else:\n            pred_color = (255, 0, 0)\n            gt_color = (0, 255, 0)\n        \n        family_match = \"✅\" if result['family_match'] else \"❌\"\n        \n        print(f\"{class_id:<8} {class_name:<20} {str(pred_color):<15} {str(gt_color):<15} {family_match}\")\n\nif __name__ == \"__main__\":\n    test_gt_pred_color_consistency()\n