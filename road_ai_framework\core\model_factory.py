"""
模型工廠：配置驅動的模型創建系統
支持動態創建編碼器、解碼器和完整模型
"""

from typing import Dict, Any, Optional, Type, Union
import torch.nn as nn
from pathlib import Path

from .registry import (
    get_encoder, get_decoder, get_model,
    list_encoders, list_decoders, list_models
)
from .config_manager import ConfigManager
from .base_encoder import BaseEncoder, EncoderConfig
from .base_decoder import BaseDecoder, DecoderConfig


class ModelFactory:
    """模型工廠類"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化模型工廠
        
        Args:
            config_manager: 配置管理器實例
        """
        self.config_manager = config_manager or ConfigManager()
    
    def create_encoder(self, 
                      encoder_config: Union[str, Dict[str, Any], EncoderConfig]) -> BaseEncoder:
        """
        創建編碼器
        
        Args:
            encoder_config: 編碼器配置（配置名稱、配置字典或配置對象）
            
        Returns:
            編碼器實例
        """
        # 處理不同類型的配置輸入
        if isinstance(encoder_config, str):
            # 從配置文件加載
            config_dict = self.config_manager.get_encoder_config(encoder_config)
            config = EncoderConfig.from_dict(config_dict)
        elif isinstance(encoder_config, dict):
            # 直接使用字典配置
            config = EncoderConfig.from_dict(encoder_config)
        elif isinstance(encoder_config, EncoderConfig):
            # 直接使用配置對象
            config = encoder_config
        else:
            raise ValueError(f"不支持的配置類型: {type(encoder_config)}")
        
        # 獲取編碼器類別
        encoder_class = get_encoder(config.encoder_type)
        
        # 創建編碼器實例
        encoder = encoder_class(
            input_channels=config.input_channels,
            output_channels=config.output_channels,
            variant=config.encoder_variant,
            **config.config
        )
        
        return encoder
    
    def create_decoder(self,
                      decoder_config: Union[str, Dict[str, Any], DecoderConfig],
                      encoder_channels: Optional[list] = None) -> BaseDecoder:
        """
        創建解碼器
        
        Args:
            decoder_config: 解碼器配置
            encoder_channels: 編碼器輸出通道數（如果配置中沒有）
            
        Returns:
            解碼器實例
        """
        # 處理不同類型的配置輸入
        if isinstance(decoder_config, str):
            config_dict = self.config_manager.get_decoder_config(decoder_config)
            config = DecoderConfig.from_dict(config_dict)
        elif isinstance(decoder_config, dict):
            config = DecoderConfig.from_dict(decoder_config)
        elif isinstance(decoder_config, DecoderConfig):
            config = decoder_config
        else:
            raise ValueError(f"不支持的配置類型: {type(decoder_config)}")
        
        # 使用提供的編碼器通道數（如果有）
        if encoder_channels is not None:
            config.encoder_channels = encoder_channels
        
        # 獲取解碼器類別
        decoder_class = get_decoder(config.decoder_type)
        
        # 創建解碼器實例
        decoder = decoder_class(
            encoder_channels=config.encoder_channels,
            decoder_channels=config.decoder_channels,
            num_classes=config.num_classes,
            variant=config.decoder_variant,
            **config.config
        )
        
        return decoder
    
    def create_model(self, model_config: Union[str, Dict[str, Any]]) -> nn.Module:
        """
        創建完整模型（編碼器+解碼器）
        
        Args:
            model_config: 模型配置
            
        Returns:
            完整模型實例
        """
        # 加載模型配置
        if isinstance(model_config, str):
            config = self.config_manager.get_model_config(model_config)
        elif isinstance(model_config, dict):
            config = model_config
        else:
            raise ValueError(f"不支持的配置類型: {type(model_config)}")
        
        # 檢查是否為完整模型配置
        if 'model_type' in config:
            # 直接創建預定義的完整模型
            model_class = get_model(config['model_type'])
            return model_class(**config.get('model_params', {}))
        
        # 分別創建編碼器和解碼器
        encoder_config = config.get('encoder', {})
        decoder_config = config.get('decoder', {})
        
        # 創建編碼器
        encoder = self.create_encoder(encoder_config)
        
        # 創建解碼器（使用編碼器的輸出通道）
        decoder = self.create_decoder(decoder_config, encoder.get_output_channels())
        
        # 創建編碼器-解碼器模型
        model = EncoderDecoderModel(encoder, decoder)
        
        return model
    
    def list_available_components(self) -> Dict[str, list]:
        """
        列出所有可用的組件
        
        Returns:
            可用組件字典
        """
        return {
            'encoders': list_encoders(),
            'decoders': list_decoders(),
            'models': list_models(),
            'configs': self.config_manager.list_configs()
        }
    
    def save_model_config(self, 
                         config: Dict[str, Any], 
                         config_name: str,
                         config_type: str = 'models'):
        """
        保存模型配置
        
        Args:
            config: 配置字典
            config_name: 配置名稱
            config_type: 配置類型
        """
        config_path = f"{config_type}/{config_name}.yaml"
        self.config_manager.save_config(config, config_path)


class EncoderDecoderModel(nn.Module):
    """編碼器-解碼器模型"""
    
    def __init__(self, encoder: BaseEncoder, decoder: BaseDecoder):
        """
        初始化編碼器-解碼器模型
        
        Args:
            encoder: 編碼器實例
            decoder: 解碼器實例
        """
        super().__init__()
        self.encoder = encoder
        self.decoder = decoder
    
    def forward(self, x):
        """前向傳播"""
        # 編碼器提取特徵
        features = self.encoder(x)
        
        # 解碼器生成輸出
        output = self.decoder(features)
        
        return output
    
    def get_encoder(self) -> BaseEncoder:
        """獲取編碼器"""
        return self.encoder
    
    def get_decoder(self) -> BaseDecoder:
        """獲取解碼器"""
        return self.decoder


def create_model_from_config(config_path: str) -> nn.Module:
    """
    便捷函數：從配置文件創建模型
    
    Args:
        config_path: 配置文件路徑
        
    Returns:
        模型實例
    """
    factory = ModelFactory()
    return factory.create_model(config_path)


def create_encoder_from_config(config_path: str) -> BaseEncoder:
    """
    便捷函數：從配置文件創建編碼器
    
    Args:
        config_path: 配置文件路徑
        
    Returns:
        編碼器實例
    """
    factory = ModelFactory()
    return factory.create_encoder(config_path)


def create_decoder_from_config(config_path: str, encoder_channels: list = None) -> BaseDecoder:
    """
    便捷函數：從配置文件創建解碼器
    
    Args:
        config_path: 配置文件路徑
        encoder_channels: 編碼器輸出通道數
        
    Returns:
        解碼器實例
    """
    factory = ModelFactory()
    return factory.create_decoder(config_path, encoder_channels)