#!/usr/bin/env python3
"""
簡化的批次處理模組
專注於核心批次推理功能，移除複雜的處理邏輯
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from tqdm import tqdm

from .inference_engine import SimplifiedYOLOInference


class BatchProcessor:
    """
    簡化的批次處理器
    移除了過度複雜的處理邏輯，專注於基本的批次推理
    """
    
    def __init__(self, inference_engine: SimplifiedYOLOInference):
        """初始化批次處理器"""
        self.inference = inference_engine
        self.logger = inference_engine.logger
    
    def process_directory(self, input_dir: str, 
                         output_dir: str,
                         task_type: str = "auto",
                         image_extensions: List[str] = None) -> List[Dict[str, Any]]:
        """
        處理目錄中的所有圖像
        
        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄  
            task_type: 任務類型
            image_extensions: 支援的圖像副檔名
            
        Returns:
            List[Dict]: 批次處理結果
        """
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        # 檢查輸入目錄
        if not os.path.exists(input_dir):
            raise FileNotFoundError(f"輸入目錄不存在: {input_dir}")
        
        # 創建輸出目錄
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 查找所有圖像文件
        image_files = self._find_image_files(input_dir, image_extensions)
        
        if not image_files:
            self.logger.warning(f"⚠️ 在目錄 {input_dir} 中沒有找到圖像文件")
            return []
        
        self.logger.info(f"🚀 開始批次處理: {len(image_files)} 張圖像")
        
        # 批次處理
        results = []
        failed_files = []
        
        start_time = time.time()
        
        with tqdm(image_files, desc="處理圖像") as pbar:
            for image_path in pbar:
                try:
                    # 處理單張圖像
                    result = self.inference.predict_single_image(
                        image_path=str(image_path),
                        output_dir=output_dir,
                        task_type=task_type
                    )
                    
                    # 添加文件信息
                    result['image_path'] = str(image_path)
                    result['image_name'] = image_path.name
                    results.append(result)
                    
                    # 更新進度條
                    pbar.set_postfix({
                        'processed': len(results),
                        'failed': len(failed_files)
                    })
                    
                except Exception as e:
                    self.logger.error(f"❌ 處理失敗: {image_path} - {e}")
                    failed_files.append(str(image_path))
        
        # 處理完成統計
        total_time = time.time() - start_time
        success_count = len(results)
        total_count = len(image_files)
        
        self.logger.info(f"✅ 批次處理完成:")
        self.logger.info(f"   總圖像數: {total_count}")
        self.logger.info(f"   成功處理: {success_count}")
        self.logger.info(f"   處理失敗: {len(failed_files)}")
        self.logger.info(f"   總用時: {total_time:.2f}秒")
        self.logger.info(f"   平均用時: {total_time/max(success_count, 1):.2f}秒/圖")
        
        # 保存批次統計
        if self.inference.config.save_statistics:
            self._save_batch_statistics(
                output_dir, results, failed_files, total_time
            )
        
        # 顯示失敗文件（如果有）
        if failed_files:
            self.logger.warning("⚠️ 處理失敗的文件:")
            for failed_file in failed_files:
                self.logger.warning(f"   {failed_file}")
        
        return results
    
    def _find_image_files(self, directory: str, 
                         extensions: List[str]) -> List[Path]:
        """查找目錄中的所有圖像文件"""
        image_files = []
        directory_path = Path(directory)
        
        for ext in extensions:
            # 查找小寫和大寫副檔名
            pattern = f"*{ext}"
            image_files.extend(directory_path.glob(pattern))
            pattern = f"*{ext.upper()}"
            image_files.extend(directory_path.glob(pattern))
        
        # 去重並排序
        image_files = list(set(image_files))
        image_files.sort()
        
        return image_files
    
    def _save_batch_statistics(self, output_dir: str, 
                              results: List[Dict[str, Any]],
                              failed_files: List[str],
                              total_time: float):
        """保存批次處理統計信息"""
        import json
        from datetime import datetime
        
        # 收集統計信息
        stats = {
            'batch_info': {
                'timestamp': datetime.now().isoformat(),
                'total_images': len(results) + len(failed_files),
                'successful_images': len(results),
                'failed_images': len(failed_files),
                'total_time': total_time,
                'avg_time_per_image': total_time / max(len(results), 1)
            },
            'detection_stats': self._calculate_detection_stats(results),
            'failed_files': failed_files,
            'class_distribution': self._calculate_class_distribution(results)
        }
        
        # 保存統計文件
        stats_path = Path(output_dir) / "batch_statistics.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"📊 批次統計已保存: {stats_path}")
    
    def _calculate_detection_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算檢測統計信息"""
        total_detections = 0
        total_segments = 0
        total_sahi = 0
        
        images_with_detections = 0
        images_with_segments = 0
        images_with_sahi = 0
        
        for result in results:
            # 檢測統計
            if 'detections' in result and result['detections']:
                total_detections += len(result['detections'])
                images_with_detections += 1
            
            # 分割統計
            if 'segments' in result and result['segments']:
                total_segments += len(result['segments'])
                images_with_segments += 1
            
            # SAHI統計
            if 'sahi_predictions' in result and result['sahi_predictions']:
                total_sahi += len(result['sahi_predictions'])
                images_with_sahi += 1
        
        return {
            'total_detections': total_detections,
            'total_segments': total_segments,
            'total_sahi_predictions': total_sahi,
            'images_with_detections': images_with_detections,
            'images_with_segments': images_with_segments,
            'images_with_sahi': images_with_sahi,
            'avg_detections_per_image': total_detections / max(len(results), 1),
            'avg_segments_per_image': total_segments / max(len(results), 1)
        }
    
    def _calculate_class_distribution(self, results: List[Dict[str, Any]]) -> Dict[str, int]:
        """計算類別分布統計"""
        class_counts = {}
        
        for result in results:
            # 統計所有類型的預測結果
            for key in ['detections', 'segments', 'sahi_predictions']:
                if key in result:
                    for item in result[key]:
                        class_name = item.get('class_name', 'unknown')
                        class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        return class_counts
    
    def process_single_with_ground_truth(self, image_path: str,
                                       annotation_path: str,
                                       output_dir: str,
                                       task_type: str = "auto") -> Dict[str, Any]:
        """
        處理單張帶有ground truth的圖像
        
        Args:
            image_path: 圖像路徑
            annotation_path: 標註文件路徑
            output_dir: 輸出目錄
            task_type: 任務類型
            
        Returns:
            Dict: 包含預測和ground truth的結果
        """
        # 運行推理
        result = self.inference.predict_single_image(
            image_path=image_path,
            output_dir=output_dir,
            task_type=task_type
        )
        
        # 載入ground truth（如果存在）
        gt_data = self._load_ground_truth(annotation_path)
        if gt_data:
            result['ground_truth'] = gt_data
        
        return result
    
    def _load_ground_truth(self, annotation_path: str) -> Optional[Dict[str, Any]]:
        """載入ground truth標註"""
        if not os.path.exists(annotation_path):
            return None
        
        try:
            if annotation_path.endswith('.json'):
                # LabelMe格式
                import json
                with open(annotation_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return self._parse_labelme_annotation(data)
            elif annotation_path.endswith('.txt'):
                # YOLO格式
                return self._parse_yolo_annotation(annotation_path)
        except Exception as e:
            self.logger.warning(f"⚠️ 載入ground truth失敗: {annotation_path} - {e}")
        
        return None
    
    def _parse_labelme_annotation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """解析LabelMe標註格式"""
        annotations = []
        for shape in data.get('shapes', []):
            if shape['shape_type'] == 'rectangle':
                points = shape['points']
                x1, y1 = points[0]
                x2, y2 = points[1]
                annotations.append({
                    'label': shape['label'],
                    'bbox': [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)],
                    'type': 'detection'
                })
            elif shape['shape_type'] == 'polygon':
                annotations.append({
                    'label': shape['label'],
                    'points': shape['points'],
                    'type': 'segmentation'
                })
        
        return {'annotations': annotations, 'format': 'labelme'}
    
    def _parse_yolo_annotation(self, annotation_path: str) -> Dict[str, Any]:
        """解析YOLO標註格式"""
        annotations = []
        with open(annotation_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    annotations.append({
                        'class_id': class_id,
                        'bbox_normalized': [x_center, y_center, width, height],
                        'type': 'detection'
                    })
        
        return {'annotations': annotations, 'format': 'yolo'}