#!/usr/bin/env python3
"""
簡化的YOLO推理系統 - 主模組
基於重構計劃簡化的現代化YOLO推理框架
"""

# 主要API導入
from .simplified_yolo import (
    SimplifiedYOLO,
    create_yolo,
    quick_predict,
    quick_batch,
    configure_global_classes,
    configure_label_aliases
)

# 配置類導入
from .config.yolo_config import (
    SimplifiedYOLOConfig,
    ClassConfig,
    get_default_config,
    create_detection_config,
    create_segmentation_config,
    create_sahi_config
)

# 工具函數導入
from .utils.validation_utils import (
    check_dependencies,
    print_dependency_report,
    validate_system_requirements
)

# 版本信息
__version__ = "1.0.0"
__author__ = "Road AI Framework Team"
__description__ = "Simplified YOLO inference system for road infrastructure detection"

# 公開API
__all__ = [
    # 主要類
    'SimplifiedYOLO',
    'SimplifiedYOLOConfig',
    'ClassConfig',
    
    # 快速創建函數
    'create_yolo',
    'quick_predict', 
    'quick_batch',
    
    # 配置函數
    'get_default_config',
    'create_detection_config',
    'create_segmentation_config',
    'create_sahi_config',
    'configure_global_classes',
    'configure_label_aliases',
    
    # 工具函數
    'check_dependencies',
    'print_dependency_report',
    'validate_system_requirements',
    
    # 版本信息
    '__version__',
    '__author__',
    '__description__'
]


def print_welcome():
    """顯示歡迎信息和基本使用指南"""
    print("🚀 簡化YOLO推理系統 v1.0.0")
    print("=" * 50)
    print("📘 快速開始:")
    print("  from simplified import SimplifiedYOLO")
    print("  yolo = SimplifiedYOLO.from_model('model.pt')")
    print("  result = yolo.predict('image.jpg')")
    print("")
    print("📘 批次推理:")
    print("  from simplified import quick_batch")
    print("  results = quick_batch('model.pt', 'input_dir', 'output_dir')")
    print("")
    print("📘 檢查依賴:")
    print("  from simplified import print_dependency_report")
    print("  print_dependency_report()")
    print("=" * 50)


def get_system_info():
    """獲取系統信息"""
    import sys
    import platform
    
    info = {
        'version': __version__,
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'platform': platform.platform(),
        'architecture': platform.architecture()[0],
    }
    
    # 檢查GPU
    try:
        import torch
        if torch.cuda.is_available():
            info['gpu'] = torch.cuda.get_device_name(0)
            info['cuda_version'] = torch.version.cuda
        else:
            info['gpu'] = 'None (CPU only)'
    except ImportError:
        info['gpu'] = 'Unknown (torch not available)'
    
    return info


def print_system_info():
    """顯示系統信息"""
    info = get_system_info()
    print("🖥️ 系統信息:")
    print("=" * 30)
    for key, value in info.items():
        print(f"  {key.replace('_', ' ').title()}: {value}")


# 模組載入時的自動檢查
try:
    # 檢查關鍵依賴
    import numpy
    import cv2
    _CORE_DEPENDENCIES_OK = True
except ImportError:
    _CORE_DEPENDENCIES_OK = False

if not _CORE_DEPENDENCIES_OK:
    print("⚠️  警告: 缺少核心依賴，請運行: pip install numpy opencv-python")


# 便利的模組級函數
def quick_check():
    """快速系統檢查"""
    print_welcome()
    print("")
    print_system_info()
    print("")
    print_dependency_report()


if __name__ == "__main__":
    # 當直接運行模組時，顯示系統信息
    quick_check()