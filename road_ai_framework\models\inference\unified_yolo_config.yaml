# 🚀 統一YOLO推理系統配置文件
# 支持完整的參數配置和類別管理

# ==================== 基礎模型配置 ====================
model:
  detection_model_path: ""                           # YOLO檢測模型路徑 (YOLO12)
  segmentation_model_path: "/mnt/d/4_road_crack/best.pt"  # YOLO分割模型路徑 (YOLO11)
  device: "cuda"                                     # 運算設備: "auto", "cpu", "cuda", "mps"
  half_precision: true                               # 使用半精度推理（節省內存）

# ==================== 推理參數配置 ====================
inference:
  img_size: 640                                      # 輸入圖像尺寸
  global_conf: 0.05                                  # 全局置信度閾值（未配置類別的默認值）
  iou_threshold: 0.45                                # IoU閾值
  max_det: 1000                                      # 最大檢測數量
  augment: false                                     # 測試時增強
  visualize: false                                   # 可視化特徵圖

# ==================== SAHI大圖切片配置 ====================
sahi:
  enable_sahi: false                                 # 是否啟用SAHI
  slice_height: 640                                  # 切片高度
  slice_width: 640                                   # 切片寬度
  overlap_height_ratio: 0.2                         # 高度重疊比例
  overlap_width_ratio: 0.2                          # 寬度重疊比例
  postprocess_type: "GREEDYNMM"                      # 後處理類型: "GREEDYNMM", "NMM", "NMS"
  postprocess_match_threshold: 0.5                   # 後處理匹配閾值
  postprocess_class_agnostic: false                  # 類別無關後處理
  auto_slice_resolution: true                        # 自動切片解析度
  
# ==================== 智能過濾配置 ====================
filtering:
  enable_intelligent_filtering: true                 # 啟用智能過濾
  enable_detection_merge: true                       # 啟用檢測合併
  
  # Step 1: linear_crack vs Alligator_crack 過濾參數
  step1_iou_threshold: 0.0                          # Step1 IoU閾值（大於此值才進行過濾）
  linear_aspect_ratio_threshold: 0.8                # 長寬比閾值
  area_ratio_threshold: 0.4                         # 面積比閾值
  
  # Step 2: linear_crack vs joint 過濾參數
  step2_iou_threshold: 0.3                          # Step2 IoU閾值
  joint_overlap_threshold: 0.3                      # joint覆蓋閾值
  
  # 合併參數
  merge_iou_threshold: 0.2                          # 合併IoU閾值
  merge_classes: ["linear_crack", "Alligator_crack"] # 需要合併的類別

# ==================== 視覺化配置 ====================
visualization:
  save_visualizations: true                         # 保存可視化結果
  save_predictions: true                            # 保存預測結果
  save_statistics: true                             # 保存統計信息
  
  # 🆕 增強三視圖配置
  enable_three_view_output: true                    # 啟用三視圖輸出
  three_view_layout: "horizontal"                   # 佈局: "horizontal", "vertical"
  three_view_spacing: 10                            # 視圖間距（像素）
  auto_load_gt: true                               # 自動加載GT標註到三視圖
  gt_polygon_color: [0, 255, 255]                  # GT多邊形顏色（黃色）
  gt_label_format: "GT: {class_name} ({shape_type})" # GT標籤格式
  
  # 字體配置
  font_path: ""                                     # 自定義字體路徑（空則使用默認）
  font_size: 1.0                                    # 字體大小倍數
  font_thickness: 2                                 # 字體粗細
  font_scale: 1.0                                   # 字體縮放比例
  
  # 圖像質量配置
  output_image_quality: 95                          # 輸出圖像質量 (1-100)
  line_thickness: 3                                 # 邊框線條粗細
  fill_alpha: 0.3                                   # mask填充透明度
  
  # 顏色配置
  color_mode: "consistent"                          # 顏色模式: "consistent", "random", "class_based"
  background_color: [255, 255, 255]                # 背景顏色 (RGB)

# ==================== GT比較配置 ====================
ground_truth:
  enable_gt_comparison: true                        # 啟用GT比較
  gt_format: "labelme"                             # GT格式: "labelme", "yolo", "coco"
  gt_path: "/mnt/d/image/5_test_image_test/test_6448/labels"  # GT標註路徑
  match_threshold: 0.5                             # GT匹配IoU閾值
  
  # 🆕 GT加載和顯示配置
  auto_load_for_three_view: true                    # 自動為三視圖加載GT
  gt_visualization_style: "polygon"                # GT可視化樣式: "polygon", "bbox", "both"
  show_gt_labels: true                             # 顯示GT標籤
  gt_label_background: true                        # GT標籤背景
  
# ==================== 輸出配置 ====================
output:
  enable_reports: true                              # 啟用報告生成
  report_format: ["csv"]                           # 報告格式（增量模式使用CSV）
  create_summary: true                              # 創建總結報告
  
  # 目錄結構
  images_subdir: "images"                           # 圖像子目錄名稱
  reports_subdir: "reports"                         # 報告子目錄名稱
  temp_subdir: "temp"                              # 臨時文件子目錄
  
  # 文件命名
  timestamp_format: "%Y%m%d_%H%M%S"                # 時間戳格式
  include_timestamp: true                           # 文件名包含時間戳
  
  # 🆕 增量更新配置
  csv_incremental_mode: true                        # CSV增量更新模式
  csv_flush_immediately: true                       # 立即刷新CSV文件
  max_records_per_file: 10000                      # 單個CSV文件最大記錄數

# ==================== 性能配置 ====================
performance:
  batch_size: 1                                     # 批處理大小
  num_workers: 4                                    # 數據加載工作進程數
  pin_memory: true                                  # 固定內存
  enable_benchmark: true                            # 啟用cudnn benchmark

# ==================== 類別配置 ====================
classes:
  # 類別ID映射（支持動態添加）
  class_mapping:
    0:
      name: "expansion_joint"
      display_name: "expansion_joint"
      color: [255, 0, 0]                           # RGB顏色
      confidence: 0.3                              # 直接推理閾值
      sahi_confidence: 0.15                        # SAHI推理閾值
      enabled: true                                # 是否啟用
      min_area: 50                                 # 最小面積（像素）
      max_area: 50000                             # 最大面積（像素）
      
    1:
      name: "joint"
      display_name: "joint"
      color: [0, 255, 0]
      confidence: 0.25
      sahi_confidence: 0.1
      enabled: true
      min_area: 100
      max_area: 30000
      
    2:
      name: "linear_crack"
      display_name: "linear_crack"
      color: [0, 0, 255]
      confidence: 0.2                              # 裂縫閾值較低，提高敏感度
      sahi_confidence: 0.08
      enabled: true
      min_area: 30
      max_area: 20000
      
    3:
      name: "Alligator_crack"
      display_name: "Alligator_crack"
      color: [255, 255, 0]
      confidence: 0.3
      sahi_confidence: 0.15
      enabled: true
      min_area: 200
      max_area: 40000
      
    4:
      name: "potholes"
      display_name: "potholes"
      color: [255, 0, 255]
      confidence: 0.4                              # 坑洞閾值較高，減少誤報
      sahi_confidence: 0.2
      enabled: true
      min_area: 100
      max_area: 15000
      
    5:
      name: "patch"
      display_name: "patch"
      color: [0, 255, 255]
      confidence: 0.35
      sahi_confidence: 0.18
      enabled: true
      min_area: 80
      max_area: 25000
      
    6:
      name: "manhole"
      display_name: "manhole"
      color: [128, 0, 128]
      confidence: 0.5                              # 人孔蓋較容易識別
      sahi_confidence: 0.25
      enabled: true
      min_area: 500
      max_area: 20000
      
    7:
      name: "deformation"
      display_name: "deformation"
      color: [255, 165, 0]
      confidence: 0.3
      sahi_confidence: 0.15
      enabled: true
      min_area: 200
      max_area: 30000
      
    8:
      name: "dirt"
      display_name: "dirt"
      color: [139, 69, 19]
      confidence: 0.4
      sahi_confidence: 0.2
      enabled: false                               # 默認禁用
      min_area: 50
      max_area: 10000
      
    9:
      name: "lane_line_linear"
      display_name: "lane_line_linear"
      color: [0, 128, 255]
      confidence: 0.25
      sahi_confidence: 0.12
      enabled: true
      min_area: 40
      max_area: 15000

  # 標籤別名映射（處理標註不匹配問題）
  label_aliases:
    "manhole_人孔蓋或排水溝": "manhole"
    "potholes_坑洞": "potholes"
    "linear_crack_裂縫": "linear_crack"
    "expansion_joint_伸縮縫": "expansion_joint"
    "joint_路面接縫": "joint"
    "deformation_變形": "deformation"
    "patch_補綻": "patch"
    "Alligator_crack_龜裂": "Alligator_crack"
    "lane_line_linear_白綫裂縫": "lane_line_linear"
    "dirt_污垢": "dirt"

# ==================== 路徑配置 ====================
paths:
  input_path: "/mnt/d/image/5_test_image_test/test_6448/images"     # 輸入路徑
  output_path: "/mnt/d/image/5_test_image_test/test_6448_output"    # 輸出路徑
  labelme_dir: "/mnt/d/image/5_test_image_test/test_6448/labels"    # LabelMe標註目錄（用於GT加載和三視圖）
  
  # 自動檢測配置
  auto_detect_gt: true                              # 自動檢測GT格式和路徑
  auto_create_output_dirs: true                     # 自動創建輸出目錄
  
  # 🆕 增強功能配置
  enable_incremental_csv: true                      # 啟用CSV增量更新（每張圖像處理完立即寫入）
  enable_gt_auto_loading: true                      # 啟用三視圖GT自動加載

# ==================== 高級功能配置 ====================
advanced:
  enable_tracking: false                            # 啟用目標追蹤
  enable_auto_annotation: false                     # 啟用自動標註
  enable_ensemble: false                           # 啟用模型集成
  enable_tta: false                                # 啟用測試時增強
  
  # 質量控制
  min_detection_quality: 0.1                       # 最小檢測質量分數
  max_detections_per_class: 100                    # 每類最大檢測數
  
  # 後處理
  enable_morphology: false                         # 啟用形態學後處理
  morphology_kernel_size: 3                        # 形態學核大小
  
# ==================== 日誌配置 ====================
logging:
  level: "INFO"                                     # 日誌級別: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
  save_logs: true                                   # 保存日誌文件
  log_file: "yolo_inference.log"                   # 日誌文件名