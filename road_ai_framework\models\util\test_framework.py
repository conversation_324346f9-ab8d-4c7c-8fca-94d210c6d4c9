#!/usr/bin/env python3
"""
統一測試框架
提供完整的測試基礎設施和CI/CD支援
"""

import os
import sys
import unittest
import pytest
import logging
import json
import time
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
import subprocess
import tempfile
import shutil

# 測試配置
@dataclass
class TestConfig:
    """測試配置類"""
    
    # 測試範圍
    test_dirs: List[str] = field(default_factory=lambda: [
        "tests/",
        "AI模型建構訓練驗證/tests/",
        "資料前處理/tests/"
    ])
    
    # 測試模式
    test_types: List[str] = field(default_factory=lambda: [
        "unit",        # 單元測試
        "integration", # 整合測試
        "performance", # 性能測試
        "real_data"    # 真實數據測試
    ])
    
    # 輸出配置
    output_dir: str = "./test_results"
    generate_html_report: bool = True
    generate_coverage_report: bool = True
    
    # 性能配置
    timeout_seconds: int = 300
    max_memory_mb: int = 4096
    
    # CI/CD配置
    enable_parallel: bool = True
    fail_fast: bool = False
    verbose: bool = True


class TestResult:
    """測試結果類"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.skipped = 0
        self.errors = []
        self.duration = 0.0
        self.coverage_percentage = 0.0
        self.memory_usage_mb = 0.0
        
    def add_result(self, status: str, test_name: str, duration: float, error: str = None):
        """添加測試結果"""
        if status == "passed":
            self.passed += 1
        elif status == "failed":
            self.failed += 1
            if error:
                self.errors.append(f"{test_name}: {error}")
        elif status == "skipped":
            self.skipped += 1
        
        self.duration += duration
    
    @property
    def total(self):
        return self.passed + self.failed + self.skipped
    
    @property
    def success_rate(self):
        if self.total == 0:
            return 0.0
        return (self.passed / self.total) * 100
    
    def to_dict(self):
        """轉換為字典"""
        return {
            'passed': self.passed,
            'failed': self.failed,
            'skipped': self.skipped,
            'total': self.total,
            'success_rate': self.success_rate,
            'duration': self.duration,
            'coverage_percentage': self.coverage_percentage,
            'memory_usage_mb': self.memory_usage_mb,
            'errors': self.errors
        }


class BaseTestCase:
    """測試基類"""
    
    def __init__(self):
        self.setup_logging()
        self.test_data_dir = Path(__file__).parent.parent.parent.parent / "test_image"
        
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_test_image_paths(self) -> List[Path]:
        """獲取測試圖像路徑"""
        if not self.test_data_dir.exists():
            return []
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        images = []
        
        for ext in image_extensions:
            images.extend(self.test_data_dir.glob(f"*{ext}"))
        
        return images
    
    def get_image_annotation_pair(self, index: int = 0):
        """獲取圖像和標註對"""
        images = self.get_test_image_paths()
        if not images or index >= len(images):
            raise ValueError(f"No test images found or index {index} out of range")
        
        image_path = images[index]
        annotation_path = image_path.with_suffix('.json')
        
        if not annotation_path.exists():
            annotation_path = None
        
        return image_path, annotation_path, image_path.stem
    
    def assert_image_valid(self, image_path: Path):
        """驗證圖像有效性"""
        assert image_path.exists(), f"Image file not found: {image_path}"
        assert image_path.stat().st_size > 0, f"Image file is empty: {image_path}"
    
    def assert_annotation_valid(self, annotation_path: Optional[Path]):
        """驗證標註有效性"""
        if annotation_path is None:
            return
        
        assert annotation_path.exists(), f"Annotation file not found: {annotation_path}"
        
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            assert 'shapes' in data, "Invalid LabelMe annotation format"
        except Exception as e:
            raise AssertionError(f"Invalid annotation file {annotation_path}: {e}")
    
    def assert_output_valid(self, output: Any, expected_type: type = None):
        """驗證輸出有效性"""
        assert output is not None, "Output is None"
        
        if expected_type:
            assert isinstance(output, expected_type), f"Expected {expected_type}, got {type(output)}"
    
    def assert_shape_correct(self, tensor, expected_shape: tuple):
        """驗證張量形狀"""
        try:
            import torch
            if isinstance(tensor, torch.Tensor):
                actual_shape = tensor.shape
                assert actual_shape == expected_shape, f"Expected shape {expected_shape}, got {actual_shape}"
        except ImportError:
            self.logger.warning("PyTorch not available, skipping shape validation")


class TestRunner:
    """測試運行器"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 確保輸出目錄存在
        os.makedirs(self.config.output_dir, exist_ok=True)
    
    def discover_tests(self) -> List[str]:
        """發現測試文件"""
        test_files = []
        
        for test_dir in self.config.test_dirs:
            test_path = Path(test_dir)
            if test_path.exists():
                # 查找test_*.py文件
                test_files.extend(test_path.rglob("test_*.py"))
                # 查找*_test.py文件
                test_files.extend(test_path.rglob("*_test.py"))
        
        return [str(f) for f in test_files]
    
    def run_tests(self) -> TestResult:
        """運行所有測試"""
        self.logger.info("開始運行測試...")
        
        start_time = time.time()
        result = TestResult()
        
        # 發現測試
        test_files = self.discover_tests()
        self.logger.info(f"發現 {len(test_files)} 個測試文件")
        
        if not test_files:
            self.logger.warning("未找到任何測試文件")
            return result
        
        # 運行測試
        for test_file in test_files:
            try:
                file_result = self._run_test_file(test_file)
                result.passed += file_result.passed
                result.failed += file_result.failed
                result.skipped += file_result.skipped
                result.errors.extend(file_result.errors)
                
            except Exception as e:
                self.logger.error(f"運行測試文件失敗 {test_file}: {e}")
                result.failed += 1
                result.errors.append(f"{test_file}: {str(e)}")
        
        result.duration = time.time() - start_time
        
        # 生成報告
        self._generate_reports(result)
        
        self.logger.info(f"測試完成 - 通過: {result.passed}, 失敗: {result.failed}, 跳過: {result.skipped}")
        
        return result
    
    def _run_test_file(self, test_file: str) -> TestResult:
        """運行單個測試文件"""
        result = TestResult()
        
        try:
            # 使用pytest運行測試
            cmd = [
                sys.executable, "-m", "pytest", 
                test_file,
                "-v",
                "--tb=short",
                f"--timeout={self.config.timeout_seconds}"
            ]
            
            if self.config.generate_coverage_report:
                cmd.extend(["--cov=./", "--cov-report=term-missing"])
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.config.timeout_seconds
            )
            
            # 解析結果
            output = process.stdout + process.stderr
            
            if "passed" in output:
                import re
                matches = re.findall(r'(\d+) passed', output)
                if matches:
                    result.passed = int(matches[0])
            
            if "failed" in output:
                import re
                matches = re.findall(r'(\d+) failed', output)
                if matches:
                    result.failed = int(matches[0])
            
            if "skipped" in output:
                import re
                matches = re.findall(r'(\d+) skipped', output)
                if matches:
                    result.skipped = int(matches[0])
            
            if process.returncode != 0 and result.failed == 0:
                result.failed = 1
                result.errors.append(f"{test_file}: {output}")
            
        except subprocess.TimeoutExpired:
            result.failed = 1
            result.errors.append(f"{test_file}: Test timeout")
        except Exception as e:
            result.failed = 1
            result.errors.append(f"{test_file}: {str(e)}")
        
        return result
    
    def _generate_reports(self, result: TestResult):
        """生成測試報告"""
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        # JSON報告
        json_report = {
            'timestamp': timestamp,
            'config': {
                'test_dirs': self.config.test_dirs,
                'test_types': self.config.test_types,
                'timeout_seconds': self.config.timeout_seconds
            },
            'results': result.to_dict()
        }
        
        json_path = Path(self.config.output_dir) / f"test_report_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)
        
        # HTML報告
        if self.config.generate_html_report:
            html_report = self._generate_html_report(result, timestamp)
            html_path = Path(self.config.output_dir) / f"test_report_{timestamp}.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_report)
        
        # 簡要報告
        summary_path = Path(self.config.output_dir) / "latest_summary.txt"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"測試報告 - {timestamp}\n")
            f.write("=" * 50 + "\n")
            f.write(f"通過: {result.passed}\n")
            f.write(f"失敗: {result.failed}\n")
            f.write(f"跳過: {result.skipped}\n")
            f.write(f"總計: {result.total}\n")
            f.write(f"成功率: {result.success_rate:.1f}%\n")
            f.write(f"執行時間: {result.duration:.2f}秒\n")
            
            if result.errors:
                f.write("\n錯誤詳情:\n")
                for error in result.errors:
                    f.write(f"- {error}\n")
    
    def _generate_html_report(self, result: TestResult, timestamp: str) -> str:
        """生成HTML報告"""
        status_color = "green" if result.failed == 0 else "red"
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>測試報告 - {timestamp}</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .status {{ color: {status_color}; font-weight: bold; font-size: 24px; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-box {{ background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }}
        .errors {{ background: #ffe6e6; padding: 15px; border-radius: 5px; margin-top: 20px; }}
        .timestamp {{ color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>道路基礎設施AI檢測框架 - 測試報告</h1>
        <div class="status">{'✅ 測試通過' if result.failed == 0 else '❌ 測試失敗'}</div>
        <div class="timestamp">生成時間: {timestamp}</div>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>通過</h3>
            <div style="font-size: 32px; color: green;">{result.passed}</div>
        </div>
        <div class="stat-box">
            <h3>失敗</h3>
            <div style="font-size: 32px; color: red;">{result.failed}</div>
        </div>
        <div class="stat-box">
            <h3>跳過</h3>
            <div style="font-size: 32px; color: orange;">{result.skipped}</div>
        </div>
        <div class="stat-box">
            <h3>總計</h3>
            <div style="font-size: 32px;">{result.total}</div>
        </div>
        <div class="stat-box">
            <h3>成功率</h3>
            <div style="font-size: 32px; color: {status_color};">{result.success_rate:.1f}%</div>
        </div>
        <div class="stat-box">
            <h3>執行時間</h3>
            <div style="font-size: 24px;">{result.duration:.1f}s</div>
        </div>
    </div>
    
    {'<div class="errors"><h3>錯誤詳情</h3><ul>' + ''.join([f'<li>{error}</li>' for error in result.errors]) + '</ul></div>' if result.errors else ''}
    
</body>
</html>
"""
        return html


class CIRunner:
    """CI/CD測試運行器"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def run_ci_pipeline(self) -> bool:
        """運行CI/CD管線"""
        self.logger.info("開始CI/CD管線...")
        
        try:
            # 1. 代碼檢查
            if not self._run_code_quality_checks():
                return False
            
            # 2. 運行測試
            runner = TestRunner(self.config)
            result = runner.run_tests()
            
            # 3. 檢查結果
            if result.failed > 0:
                self.logger.error(f"測試失敗: {result.failed} 個測試未通過")
                return False
            
            # 4. 生成CI報告
            self._generate_ci_report(result)
            
            self.logger.info("CI/CD管線完成")
            return True
            
        except Exception as e:
            self.logger.error(f"CI/CD管線失敗: {e}")
            return False
    
    def _run_code_quality_checks(self) -> bool:
        """運行代碼品質檢查"""
        checks = [
            self._check_syntax,
            self._check_imports,
            self._check_basic_structure
        ]
        
        for check in checks:
            try:
                if not check():
                    return False
            except Exception as e:
                self.logger.error(f"代碼檢查失敗: {e}")
                return False
        
        return True
    
    def _check_syntax(self) -> bool:
        """檢查語法"""
        self.logger.info("檢查Python語法...")
        
        try:
            import ast
            for test_dir in self.config.test_dirs:
                test_path = Path(test_dir)
                if test_path.exists():
                    for py_file in test_path.rglob("*.py"):
                        try:
                            with open(py_file, 'r', encoding='utf-8') as f:
                                ast.parse(f.read())
                        except SyntaxError as e:
                            self.logger.error(f"語法錯誤 {py_file}: {e}")
                            return False
            
            self.logger.info("語法檢查通過")
            return True
            
        except Exception as e:
            self.logger.error(f"語法檢查失敗: {e}")
            return False
    
    def _check_imports(self) -> bool:
        """檢查導入"""
        self.logger.info("檢查導入依賴...")
        # 基本檢查，實際項目中可以更詳細
        return True
    
    def _check_basic_structure(self) -> bool:
        """檢查基本結構"""
        self.logger.info("檢查項目結構...")
        
        required_dirs = [
            "AI模型建構訓練驗證/model_create",
            "資料前處理"
        ]
        
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                self.logger.error(f"缺少必要目錄: {dir_path}")
                return False
        
        return True
    
    def _generate_ci_report(self, result: TestResult):
        """生成CI報告"""
        ci_report = {
            'ci_status': 'success' if result.failed == 0 else 'failure',
            'timestamp': datetime.now().isoformat(),
            'test_results': result.to_dict(),
            'quality_checks': {
                'syntax_check': 'passed',
                'import_check': 'passed',
                'structure_check': 'passed'
            }
        }
        
        ci_path = Path(self.config.output_dir) / "ci_report.json"
        with open(ci_path, 'w', encoding='utf-8') as f:
            json.dump(ci_report, f, indent=2, ensure_ascii=False)


def create_test_runner(output_dir: str = "./test_results", **kwargs) -> TestRunner:
    """創建測試運行器"""
    config = TestConfig(output_dir=output_dir, **kwargs)
    return TestRunner(config)


def run_all_tests(output_dir: str = "./test_results", **kwargs) -> TestResult:
    """運行所有測試"""
    runner = create_test_runner(output_dir=output_dir, **kwargs)
    return runner.run_tests()


def run_ci_pipeline(output_dir: str = "./test_results", **kwargs) -> bool:
    """運行CI/CD管線"""
    config = TestConfig(output_dir=output_dir, **kwargs)
    ci_runner = CIRunner(config)
    return ci_runner.run_ci_pipeline()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="統一測試框架")
    parser.add_argument("--mode", "-m",
                       choices=["test", "ci"],
                       default="test",
                       help="運行模式")
    parser.add_argument("--output", "-o",
                       default="./test_results",
                       help="輸出目錄")
    parser.add_argument("--verbose", "-v",
                       action="store_true",
                       help="詳細輸出")
    
    args = parser.parse_args()
    
    # 設置日誌
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    if args.mode == "test":
        result = run_all_tests(output_dir=args.output, verbose=args.verbose)
        sys.exit(0 if result.failed == 0 else 1)
    elif args.mode == "ci":
        success = run_ci_pipeline(output_dir=args.output, verbose=args.verbose)
        sys.exit(0 if success else 1)