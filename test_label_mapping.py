#!/usr/bin/env python3
"""
測試智能標籤映射功能
"""

def test_label_mapping():
    """測試標籤映射功能"""
    
    # 模擬 LABEL_ALIASES
    LABEL_ALIASES = {
        'manhole': 'manhole_人孔蓋或排水溝',
        'potholes': 'potholes_坑洞', 
        'linear_crack': 'linear_crack_裂縫',
        'dirt': 'dirt_污垢',
        'expansion_joint': 'expansion_joint_伸縮縫',
        'joint': 'joint_路面接縫',
        'deformation': 'deformation_變形',
        'patch': 'patch_補綻',
        'Alligator_crack': 'Alligator_crack_龜裂',
        'lane_lline_linear': 'lane_line_linear_白綫裂縫',  # 處理拼寫錯誤
        'lane_line_linear': 'lane_line_linear_白綫裂縫'
    }
    
    # 模擬 CLASS_NAMES
    CLASS_NAMES = [
        "expansion_joint_伸縮縫",      # ID: 0
        "joint_路面接縫",               # ID: 1
        "linear_crack_裂縫",        # ID: 2
        "Alligator_crack_龜裂",     # ID: 3
        "potholes_坑洞",            # ID: 4
        "patch_補綻",               # ID: 5
        "manhole_人孔蓋或排水溝",             # ID: 6
        "deformation_變形",         # ID: 7
        "dirt_污垢",                # ID: 8
        "lane_line_linear_白綫裂縫"     # ID: 9
    ]
    
    # 測試的未識別標籤
    unrecognized_labels = {'manhole', 'potholes', 'linear_crack', 'dirt', 'expansion_joint', 
                          'joint', 'deformation', 'patch', 'Alligator_crack', 'lane_lline_linear'}
    
    print("=== 智能標籤映射測試 ===")
    print(f"未識別標籤: {unrecognized_labels}")
    print()
    
    def find_matching_class_name(label: str) -> str:
        """智能匹配標籤到CLASS_NAMES"""
        # 直接匹配
        if label in LABEL_ALIASES:
            return LABEL_ALIASES[label]
        
        # 模糊匹配：檢查是否包含關鍵字
        for class_name in CLASS_NAMES:
            if '_' in class_name:
                english_part = class_name.split('_')[0]
                if english_part.lower() == label.lower():
                    return class_name
        
        return label  # 如果找不到匹配，返回原標籤
    
    # 處理未識別的標籤
    matched_labels = {}
    still_unrecognized = set()
    
    for label in unrecognized_labels:
        matched = find_matching_class_name(label)
        if matched in CLASS_NAMES:
            matched_labels[label] = matched
            print(f"✅ 自動匹配: '{label}' -> '{matched}'")
        else:
            still_unrecognized.add(label)
            print(f"❌ 無法匹配: '{label}'")
    
    print(f"\n=== 匹配結果統計 ===")
    print(f"成功匹配: {len(matched_labels)}")
    print(f"匹配失敗: {len(still_unrecognized)}")
    print(f"匹配率: {len(matched_labels) / len(unrecognized_labels) * 100:.1f}%")
    
    if still_unrecognized:
        print(f"\n⚠️  仍無法匹配的標籤: {still_unrecognized}")
    else:
        print(f"\n🎉 所有標籤都成功匹配！")
    
    # 驗證映射結果
    print(f"\n=== 映射驗證 ===")
    expected_mappings = {
        'manhole': 'manhole_人孔蓋或排水溝',
        'potholes': 'potholes_坑洞',
        'linear_crack': 'linear_crack_裂縫',
        'dirt': 'dirt_污垢',
        'expansion_joint': 'expansion_joint_伸縮縫',
        'joint': 'joint_路面接縫',
        'deformation': 'deformation_變形',
        'patch': 'patch_補綻',
        'Alligator_crack': 'Alligator_crack_龜裂',
        'lane_lline_linear': 'lane_line_linear_白綫裂縫'
    }
    
    all_correct = True
    for original, expected in expected_mappings.items():
        actual = matched_labels.get(original, "未匹配")
        correct = (actual == expected)
        all_correct = all_correct and correct
        status = "✅" if correct else "❌"
        print(f"{status} {original}: {actual}")
    
    if all_correct:
        print(f"\n🎉 所有映射都正確！智能標籤匹配功能運作正常。")
    else:
        print(f"\n❌ 部分映射有誤，需要檢查 LABEL_ALIASES 配置。")

if __name__ == "__main__":
    test_label_mapping()