import os
import json
import cv2
import numpy as np
import random
import tempfile
from pathlib import Path
from tqdm import tqdm
import logging
from collections import Counter
from shapely.geometry import Polygon, box
import matplotlib.pyplot as plt

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False


class ImageAugmenter:
    """
    數據增強器
    通過融合區域來增強數據集，僅支持LabelMe格式
    增強版：支持網格放置、避免相似位置、暫存標籤等功能
    """

    def __init__(self,
                 source_dirs,
                 target_dir,
                 output_dir,
                 resize=None,
                 quality=75,
                 class_targets=None,
                 task_type='both',  # 新增: 任務類型，可為 'seg', 'det', 或 'both'
                 logger=None):
        """
        初始化數據增強器

        參數:
            source_dirs: 來源目錄列表，包含要提取區域的圖像和標註
            target_dir: 目標目錄，包含要放置區域的圖像和標註（已處理過的圖像）
            output_dir: 輸出目錄，生成的增強數據將保存在此
            resize: 調整來源圖像尺寸的參數，可以是：
                - 元組 (寬, 高)，例如 (1200, 900)
                - 浮點數，表示縮放比例，例如 0.3
                - None 表示不調整大小
            quality: 來源圖像的JPEG品質 (1-100)，僅適用於來源圖像
            class_targets: 類別目標計數 {類別: 目標數量}
            task_type: 任務類型，可為 'seg', 'det', 或 'both'
            logger: 日誌記錄器，如果為None則創建新的
        """
        self.source_dirs = source_dirs
        self.target_dir = Path(target_dir)
        self.output_dir = Path(output_dir)
        self.resize = resize
        self.quality = quality
        self.class_targets = class_targets or {}
        self.task_type = task_type
        self.logger = logger or logging.getLogger(__name__)

        # 處理resize參數
        self.resize_method = None
        self.target_size = None
        self.scale_factor = None

        if isinstance(resize, tuple) and len(resize) == 2:
            self.resize_method = 'absolute'
            self.target_size = resize
        elif isinstance(resize, (float, int)) and 0 < resize < 1:
            self.resize_method = 'relative'
            self.scale_factor = float(resize)
        elif resize is not None:
            # 預設值
            self.resize_method = 'absolute'
            self.target_size = (1200, 900)

        # 初始化統計
        self.stats = {
            "generated": 0,
            "class_counts": Counter(),
            "failures": 0,
            "resize_info": {
                "method": self.resize_method,
                "target_size": self.target_size,
                "scale_factor": self.scale_factor
            },
            "sources": {},
            "targets": {}
        }

        # 創建輸出目錄結構
        if task_type == 'both':
            tasks = ['seg', 'det']
        else:
            tasks = [task_type]

        for task in tasks:
            task_dir = self.output_dir / task
            task_dir.mkdir(parents=True, exist_ok=True)

        # 設置顏色映射
        self.label_colors = self._create_color_map()

    # 運行數據增強
    def run(self,
            num_generations=100,
            regions_per_image=(2, 3),  # 修改: 最小值設為2，確保至少有2個區域
            scale_range=(0.6, 1.2),
            margins=(0, 0, 0, 0),  # 邊距 (top, right, bottom, left)
            grid_placement=True,  # 網格放置策略
            grid_size=8,  # 網格大小
            max_placement_attempts=50,  # 每個區域最大嘗試放置次數
            temp_label_file=None,  # 暫存標籤文件
            avoid_similar_positions=True,  # 避免相似位置
            avoid_overlap=True,
            iou_threshold=0.1,
            visualize_region=False):  # 是否生成放置區域可視化
        """
        運行數據增強流程

        參數:
            num_generations: 要生成的圖像數量
            regions_per_image: 每張圖像添加區域的數量範圍 (min, max)，最小值至少為2
            scale_range: 縮放比例範圍 (min, max)
            margins: 放置區域的邊距 (top, right, bottom, left)，數值範圍 0-5
                    0 表示沒有邊距，5 表示從邊緣到中心點
            grid_placement: 是否啟用網格放置策略
            grid_size: 網格大小
            max_placement_attempts: 每個區域最大嘗試放置次數
            temp_label_file: 暫存標籤文件路徑，若為None則自動生成
            avoid_similar_positions: 是否避免相似位置
            avoid_overlap: 是否避免標籤重疊
            iou_threshold: IoU閾值，超過此值視為重疊
            visualize_region: 是否生成放置區域可視化

        返回:
            增強統計信息
        """
        # 確保最小區域數量至少為2
        if regions_per_image[0] < 2:
            regions_per_image = (2, regions_per_image[1])
            self.logger.warning(f"最小區域數量已調整為2，新範圍: {regions_per_image}")

        # 設置暫存標籤文件
        if temp_label_file is None:
            temp_label_file = os.path.join(self.output_dir, "temp_labels.json")

        # 1. 收集來源區域
        self.logger.info("收集來源區域...")
        source_regions = self._collect_source_regions()

        # 2. 收集目標圖像
        self.logger.info("收集目標圖像...")
        target_images = self._collect_target_images()

        # 3. 生成增強數據
        self.logger.info(f"開始生成 {num_generations} 張增強圖像...")

        # 生成可視化圖像顯示放置區域
        if visualize_region:
            self._visualize_placement_region(target_images[0], margins)

        # 首先生成所有增強圖像的資料
        augmented_images_data = []

        self.logger.info(f"第一階段: 生成增強圖像資料...")
        augmented_images_data = self._generate_augmentation_data(
            source_regions,
            target_images,
            num_generations,
            regions_per_image,
            scale_range,
            margins,
            grid_placement,
            grid_size,
            max_placement_attempts,
            avoid_similar_positions,
            avoid_overlap,
            iou_threshold,
            temp_label_file
        )

        self.logger.info(f"第二階段: 根據任務類型保存增強圖像...")
        # 根據任務類型保存增強圖像
        if self.task_type == 'both':
            tasks = ['seg', 'det']
        else:
            tasks = [self.task_type]

        for task in tasks:
            self.logger.info(f"為任務類型 '{task}' 保存增強數據...")
            label_mode = 'polygon' if task == 'seg' else 'box'

            task_dir = self.output_dir / task
            task_dir.mkdir(parents=True, exist_ok=True)

            # 保存為該任務類型的圖像和標籤
            self._save_task_images(augmented_images_data,
                                   task_dir, label_mode, task)

        # 驗證任務輸出的標籤類型
        self.logger.info("驗證任務輸出的標籤類型...")
        for task in tasks:
            task_dir = self.output_dir / task
            expected_type = "polygon" if task == "seg" else "rectangle"
            stats, non_valid_files = self._verify_task_labels(
                task_dir, expected_type)

            # 如果發現非法標籤，並且是 det 任務，嘗試修復它們
            if task == "det" and (stats["polygon"] > 0 or stats["other"] > 0):
                self.logger.warning(f"檢測到 {task} 任務中有非矩形標籤，嘗試修復...")
                self._fix_det_labels(task_dir, non_valid_files)

        # 4. 生成報告
        self._generate_report()

        return self.stats

    # 區域放置函數
    def _place_region(self, result_img, region, tx, ty, region_w, region_h):
        """放置區域到結果圖像中"""
        th, tw = result_img.shape[:2]

        try:
            # 根據標籤類型不同的處理方式
            if region["type"] == "rectangle":
                # 確保不超出圖像邊界
                if ty + region_h > th:
                    region_img = region["image"][:th-ty, :]
                    region_h = th - ty
                if tx + region_w > tw:
                    region_img = region["image"][:, :tw-tx]
                    region_w = tw - tx

                # 放置區域
                result_img[ty:ty+region_h, tx:tx +
                           region_w] = region_img[:region_h, :region_w]

                return True, tx, ty, region_w, region_h

            elif region["type"] == "polygon":
                # 創建目標圖像上的多邊形遮罩
                mask = np.zeros((th, tw), dtype=np.uint8)
                polygon = np.array(region["points"], dtype=np.int32)
                cv2.fillPoly(mask, [polygon], 255)

                # 獲取多邊形的邊界框
                x, y, w, h = cv2.boundingRect(polygon)

                # 確保邊界框不超出圖像邊界
                x = max(0, x)
                y = max(0, y)
                w = min(tw - x, w)
                h = min(th - y, h)

                if w <= 0 or h <= 0:
                    raise ValueError("處理後的多邊形區域無效")

                # 提取多邊形區域的遮罩部分
                roi_mask = mask[y:y+h, x:x+w]

                # 提取目標圖像中對應的區域
                roi = result_img[y:y+h, x:x+w].copy()

                # 縮放來源區域到適合的大小
                region_img = region["image"]
                if region_img.shape[:2] != (h, w):
                    region_img = cv2.resize(region_img, (w, h))

                # 使用遮罩進行混合
                mask_3ch = cv2.merge([roi_mask, roi_mask, roi_mask]) / 255.0

                # 在遮罩區域內直接放置區域 (不使用alpha混合)
                result = roi * (1 - mask_3ch) + region_img * mask_3ch

                # 將結果放回原圖
                result_img[y:y+h, x:x+w] = result

                return True, x, y, w, h

        except Exception as e:
            self.logger.error(f"放置區域時出錯: {e}")
            return False, 0, 0, 0, 0

    # 區域重疊檢查函數
    def _check_overlap(self, region_points, result_labels, iou_threshold):
        """檢查區域是否與已有標籤重疊"""
        if not result_labels:
            return False

        for existing_label in result_labels:
            if existing_label["type"] == "polygon":
                existing_poly = Polygon(existing_label["points"])
                new_poly = Polygon(region_points)

                if not existing_poly.is_valid or not new_poly.is_valid:
                    continue

                try:
                    intersection = new_poly.intersection(existing_poly).area
                    union = new_poly.area + existing_poly.area - intersection
                    iou = intersection / union if union > 0 else 0

                    if iou > iou_threshold:
                        return True
                except Exception:
                    continue
            else:  # rectangle
                xmin, ymin, xmax, ymax = existing_label["bbox"]
                existing_poly = box(xmin, ymin, xmax, ymax)
                new_poly = Polygon(region_points)

                if not existing_poly.is_valid or not new_poly.is_valid:
                    continue

                try:
                    intersection = new_poly.intersection(existing_poly).area
                    union = new_poly.area + existing_poly.area - intersection
                    iou = intersection / union if union > 0 else 0

                    if iou > iou_threshold:
                        return True
                except Exception:
                    continue

        return False

    # 計算邊距和有效區域
    def _calculate_placement_area(self, image_height, image_width, margins):
        """計算放置區域（考慮邊距）"""
        top, right, bottom, left = margins

        # 計算有效區域
        top_margin = int(image_height * (top / 10))
        right_margin = int(image_width * (right / 10))
        bottom_margin = int(image_height * (bottom / 10))
        left_margin = int(image_width * (left / 10))

        # 確保區域有效
        if top_margin + bottom_margin >= image_height or left_margin + right_margin >= image_width:
            self.logger.warning(f"指定的邊距導致無效的放置區域，使用默認區域")
            top_margin = right_margin = bottom_margin = left_margin = 0

        # 定義放置區域
        x_min = left_margin
        x_max = image_width - right_margin
        y_min = top_margin
        y_max = image_height - bottom_margin

        return x_min, x_max, y_min, y_max

    # 修復檢測任務中的非矩形標籤
    def _fix_det_labels(self, task_dir, non_rectangle_files):
        """修復檢測任務中的非矩形標籤"""
        fixed_count = 0

        for json_file in tqdm(non_rectangle_files, desc="修復非矩形標籤"):
            json_path = task_dir / json_file
            try:
                # 讀取標籤文件
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 確定使用 shapes 或 annotations
                shapes_field = "shapes" if "shapes" in data else "annotations"

                # 找出需要修復的標籤
                new_shapes = []
                needs_fix = False

                for shape in data.get(shapes_field, []):
                    shape_type = shape.get("shape_type", "unknown")

                    if shape_type != "rectangle":
                        needs_fix = True
                        # 取得標籤資訊
                        label = shape.get("label", "unknown")
                        points = shape.get("points", [])

                        # 計算邊界框
                        if len(points) >= 3:  # 多邊形
                            pts = np.array(points)
                            x_min, y_min = pts.min(axis=0)
                            x_max, y_max = pts.max(axis=0)

                            # 創建新的矩形標籤
                            new_shapes.append({
                                "label": label,
                                "points": [[x_min, y_min], [x_max, y_max]],
                                "group_id": None,
                                "shape_type": "rectangle",
                                "flags": {}
                            })
                        else:
                            # 保留無法修復的標籤原樣
                            new_shapes.append(shape)
                    else:
                        # 已經是矩形，直接保留
                        new_shapes.append(shape)

                # 如果需要修復，更新並保存
                if needs_fix:
                    data[shapes_field] = new_shapes
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    fixed_count += 1

            except Exception as e:
                self.logger.error(f"修復標籤時出錯: {json_path}, {e}")

        self.logger.info(f"已修復 {fixed_count} 個包含非矩形標籤的檔案")

        # 再次驗證修復結果
        if fixed_count > 0:
            self.logger.info("驗證修復結果...")
            stats, _ = self._verify_task_labels(task_dir, "rectangle")

            if stats["polygon"] > 0 or stats["other"] > 0:
                self.logger.warning(
                    f"修復後仍有 {stats['polygon'] + stats['other']} 個非矩形標籤")
            else:
                self.logger.info("所有標籤已成功修復為矩形")

    # 將所有標籤轉換為矩形格式
    def _convert_to_rectangles(self, labels):
        """將所有標籤轉換為矩形格式，無論原始類型為何"""
        rectangle_labels = []

        for label in labels:
            # 獲取標籤類別
            label_class = label["label"]

            # 確定邊界框座標
            if "bbox" in label and label["bbox"]:
                # 已經有 bbox 資訊
                bbox = label["bbox"]
            elif "points" in label and len(label["points"]) >= 3:
                # 將多邊形轉換為邊界框
                points = np.array(label["points"])
                x_min, y_min = points.min(axis=0)
                x_max, y_max = points.max(axis=0)
                bbox = [x_min, y_min, x_max, y_max]
            else:
                self.logger.warning(f"無法將標籤 {label_class} 轉換為矩形，缺少有效的點或邊界框資訊")
                continue  # 跳過無法處理的標籤

            # 添加矩形標籤
            rectangle_labels.append({
                "label": label_class,
                "bbox": bbox,
                "type": "rectangle"
            })

        return rectangle_labels

    # 網格策略
    def _setup_grid(self, x_min, x_max, y_min, y_max, grid_size):
        """設置網格放置策略"""
        grid_cells = []

        # 創建網格 (grid_size x grid_size)
        cell_width = (x_max - x_min) / grid_size
        cell_height = (y_max - y_min) / grid_size

        for row in range(grid_size):
            for col in range(grid_size):
                # 記錄每個網格單元的邊界
                cell_x_min = x_min + col * cell_width
                cell_y_min = y_min + row * cell_height
                cell_x_max = cell_x_min + cell_width
                cell_y_max = cell_y_min + cell_height

                grid_cells.append({
                    'x_min': cell_x_min,
                    'y_min': cell_y_min,
                    'x_max': cell_x_max,
                    'y_max': cell_y_max,
                    'used': False  # 標記是否已使用
                })

        # 打亂網格順序以增加隨機性
        random.shuffle(grid_cells)

        return grid_cells

    # 根據網格策略找到放置位置
    def _find_grid_placement(self, grid_cells, region_w, region_h, x_min, x_max, y_min, y_max):
        """根據網格策略找到放置位置"""
        # 嘗試找到一個未使用的網格單元
        unused_cells = [cell for cell in grid_cells if not cell['used']]
        if unused_cells:
            cell = random.choice(unused_cells)
            # 在單元格內隨機選擇一個位置
            tx = random.randint(int(cell['x_min']), max(
                int(cell['x_min']), int(cell['x_max']) - region_w))
            ty = random.randint(int(cell['y_min']), max(
                int(cell['y_min']), int(cell['y_max']) - region_h))
            # 標記單元格為已使用
            cell['used'] = True
        else:
            # 如果所有單元格都已使用，回到完全隨機放置
            tx = random.randint(x_min, max(x_min, x_max - region_w))
            ty = random.randint(y_min, max(y_min, y_max - region_h))

        return tx, ty

    # 計算類別計數
    def _check_class_target_reached(self, class_name, current_counts):
        """檢查類別是否已達到目標數量"""
        target_count = self.class_targets.get(class_name, 0)
        # 如果該類別沒有設定目標或設定為0，均視為不生成
        if target_count == 0:
            return True
        # 如果已達到目標數量
        if target_count > 0 and current_counts[class_name] >= target_count:
            return True
        return False

    # 圖像預處理
    def _preprocess_region(self, region, scale_range):
        """預處理區域圖像（縮放等）"""
        # 隨機縮放
        scale = random.uniform(*scale_range)
        region_img = region["image"].copy()
        region_h, region_w = region_img.shape[:2]

        if scale != 1.0:
            new_w = max(1, int(region_w * scale))
            new_h = max(1, int(region_h * scale))
            try:
                region_img = cv2.resize(
                    region_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                region_w, region_h = new_w, new_h
            except Exception as e:
                self.logger.warning(f"縮放區域失敗: {e}")
                return None, 0, 0, 0

        return region_img, region_h, region_w, scale

    # 暫存標籤數據的讀寫
    def _load_temp_labels(self, temp_label_file):
        """加載暫存標籤"""
        temp_labels_data = {}
        if os.path.exists(temp_label_file):
            try:
                with open(temp_label_file, 'r') as f:
                    temp_labels_data = json.load(f)
                self.logger.info(
                    f"已載入暫存標籤數據，包含 {len(temp_labels_data)} 張圖像的標籤")
            except Exception as e:
                self.logger.error(f"載入暫存標籤文件失敗: {e}")
        return temp_labels_data

    # 保存暫存標籤
    def _save_temp_labels(self, temp_label_file, temp_labels_data):
        """保存暫存標籤"""
        try:
            with open(temp_label_file, 'w') as f:
                json.dump(temp_labels_data, f, indent=2)
            self.logger.info(f"已更新暫存標籤數據，目前有 {len(temp_labels_data)} 張圖像的標籤")
            return True
        except Exception as e:
            self.logger.error(f"保存暫存標籤數據失敗: {e}")
            return False

    # 生成增強圖像
    def _generate_augmentation_data(self,
                                    source_regions,
                                    target_images,
                                    num_generations,
                                    regions_per_image,
                                    scale_range,
                                    margins,
                                    grid_placement,
                                    grid_size,
                                    max_placement_attempts,
                                    avoid_similar_positions,
                                    avoid_overlap,
                                    iou_threshold,
                                    temp_label_file):
        """生成增強圖像資料但不保存，返回結果供不同任務使用"""

        # 檢查是否有足夠的來源和目標
        if not source_regions:
            self.logger.error("沒有可用的來源區域")
            return []

        if not target_images:
            self.logger.error("沒有可用的目標圖像")
            return []

        # 記錄各類別的當前計數
        class_counts = Counter()

        # 暫存標籤信息
        temp_labels_data = {}

        # 如果暫存標籤文件存在，嘗試加載
        if os.path.exists(temp_label_file):
            try:
                with open(temp_label_file, 'r') as f:
                    temp_labels_data = json.load(f)
                self.logger.info(
                    f"已載入暫存標籤數據，包含 {len(temp_labels_data)} 張圖像的標籤")
            except Exception as e:
                self.logger.error(f"載入暫存標籤文件失敗: {e}")
                temp_labels_data = {}

        # 檢查是否有設置類別目標
        has_class_targets = bool(self.class_targets) and any(
            target > 0 for target in self.class_targets.values())

        # 用於存儲生成的圖像數據
        augmented_data = []

        # 生成指定數量的增強圖像
        for i in tqdm(range(num_generations), desc=f"生成增強圖像資料"):
            # 檢查是否達到所有類別的目標數量
            if has_class_targets:
                all_targets_reached = True
                for class_name, target_count in self.class_targets.items():
                    if target_count > 0 and class_counts[class_name] < target_count:
                        all_targets_reached = False
                        break

                if all_targets_reached:
                    self.logger.info("所有類別的目標數量已達到，停止生成")
                    break

            # 隨機選擇目標圖像
            target = random.choice(target_images)
            target_img = self._imdecode_unicode(target["path"])
            if target_img is None:
                continue

            # 生成輸出文件名
            output_basename = f"augmented_{i+1:04d}"
            common_key = f"common_{output_basename}"  # 用於暫存標籤數據的鍵

            # 如果圖像已經在暫存標籤中，獲取之前的標籤
            if common_key in temp_labels_data:
                temp_labels = temp_labels_data[common_key]['labels']
                result_labels = []
                # 將暫存標籤轉換為程式可用的格式
                for temp_label in temp_labels:
                    label, points, label_type = temp_label
                    result_labels.append({
                        "label": label,
                        "points": points if label_type == "polygon" else None,
                        "bbox": points if label_type == "rectangle" else None,
                        "type": label_type
                    })
                self.logger.info(
                    f"從暫存數據中讀取圖像 {common_key} 的標籤，共 {len(result_labels)} 個")

                # 存儲增強數據
                augmented_data.append({
                    "basename": output_basename,
                    "image": target_img.copy(),
                    "target": target,
                    "labels": result_labels
                })

                # 更新統計
                for label in result_labels:
                    class_counts[label["label"]] += 1

                continue

            # 獲取目標圖像尺寸
            th, tw = target_img.shape[:2]

            # 解析邊距參數
            top, right, bottom, left = margins

            # 計算有效區域
            top_margin = int(th * (top / 10))
            right_margin = int(tw * (right / 10))
            bottom_margin = int(th * (bottom / 10))
            left_margin = int(tw * (left / 10))

            # 確保區域有效
            if top_margin + bottom_margin >= th or left_margin + right_margin >= tw:
                self.logger.warning(f"圖像 {common_key} 指定的邊距導致無效的放置區域，使用默認區域")
                top_margin = right_margin = bottom_margin = left_margin = 0

            # 定義放置區域
            x_min = left_margin
            x_max = tw - right_margin
            y_min = top_margin
            y_max = th - bottom_margin

            # 將結果標籤轉換為像素座標，用於檢測重疊
            result_labels = target["existing_labels"].copy()

            # 設置網格
            grid_cells = []
            if grid_placement:
                # 創建網格 (grid_size x grid_size)
                cell_width = (x_max - x_min) / grid_size
                cell_height = (y_max - y_min) / grid_size

                for row in range(grid_size):
                    for col in range(grid_size):
                        # 記錄每個網格單元的邊界
                        cell_x_min = x_min + col * cell_width
                        cell_y_min = y_min + row * cell_height
                        cell_x_max = cell_x_min + cell_width
                        cell_y_max = cell_y_min + cell_height

                        grid_cells.append({
                            'x_min': cell_x_min,
                            'y_min': cell_y_min,
                            'x_max': cell_x_max,
                            'y_max': cell_y_max,
                            'used': False  # 標記是否已使用
                        })

                # 打亂網格順序以增加隨機性
                random.shuffle(grid_cells)

            # 初始化位置記錄，用於避免相似位置
            position_history = []

            # 決定添加多少個區域
            num_regions = random.randint(*regions_per_image)

            # 創建結果圖像
            result_img = target_img.copy()

            # 對每個要添加的區域
            added_regions = 0
            max_total_attempts = num_regions * max_placement_attempts  # 總嘗試次數限制
            total_attempts = 0

            while added_regions < num_regions and total_attempts < max_total_attempts:
                total_attempts += 1

                # 隨機選擇一個區域
                if not source_regions:
                    break

                region = random.choice(source_regions)
                region_class = region["label"]

                # 檢查類別是否已達到目標數量或設為不生成
                # 如果該類別沒有設定目標或設定為0，均視為不生成
                target_count = self.class_targets.get(region_class, 0)
                if target_count == 0 or (target_count > 0 and class_counts[region_class] >= target_count):
                    continue

                # 隨機縮放
                scale = random.uniform(*scale_range)
                region_img = region["image"].copy()
                region_h, region_w = region_img.shape[:2]

                if scale != 1.0:
                    new_w = max(1, int(region_w * scale))
                    new_h = max(1, int(region_h * scale))
                    try:
                        region_img = cv2.resize(
                            region_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                        region_w, region_h = new_w, new_h
                    except Exception as e:
                        self.logger.warning(f"縮放區域失敗: {e}")
                        continue

                # 選擇放置位置策略
                placement_success = False
                placement_attempts = 0

                while not placement_success and placement_attempts < max_placement_attempts:
                    placement_attempts += 1

                    # 放置策略: 網格放置或完全隨機
                    if grid_placement and grid_cells:
                        # 嘗試找到一個未使用的網格單元
                        unused_cells = [
                            cell for cell in grid_cells if not cell['used']]
                        if unused_cells:
                            cell = random.choice(unused_cells)
                            # 在單元格內隨機選擇一個位置
                            tx = random.randint(int(cell['x_min']), max(
                                int(cell['x_min']), int(cell['x_max']) - region_w))
                            ty = random.randint(int(cell['y_min']), max(
                                int(cell['y_min']), int(cell['y_max']) - region_h))
                            # 標記單元格為已使用
                            cell['used'] = True
                        else:
                            # 如果所有單元格都已使用，回到完全隨機放置
                            tx = random.randint(
                                x_min, max(x_min, x_max - region_w))
                            ty = random.randint(
                                y_min, max(y_min, y_max - region_h))
                    else:
                        # 完全隨機放置
                        tx = random.randint(
                            x_min, max(x_min, x_max - region_w))
                        ty = random.randint(
                            y_min, max(y_min, y_max - region_h))

                    # 檢查是否與歷史位置相似 (如果啟用)
                    if avoid_similar_positions and position_history:
                        too_similar = False
                        # 定義相似閾值 (圖像寬度和高度的百分比)
                        similarity_threshold_x = tw * 0.1
                        similarity_threshold_y = th * 0.1

                        for pos in position_history:
                            old_tx, old_ty, old_sw, old_sh = pos
                            # 計算中心點
                            center_x1, center_y1 = tx + region_w/2, ty + region_h/2
                            center_x2, center_y2 = old_tx + old_sw/2, old_ty + old_sh/2

                            # 檢查距離
                            distance_x = abs(center_x1 - center_x2)
                            distance_y = abs(center_y1 - center_y2)

                            if distance_x < similarity_threshold_x and distance_y < similarity_threshold_y:
                                too_similar = True
                                break

                        if too_similar:
                            continue  # 嘗試新位置

                    # 創建新的標籤（根據原始標籤類型）
                    region_points = []
                    if region["type"] == "polygon":
                        # 調整多邊形點位置
                        points = region["points"]
                        orig_bbox = region["bbox"]

                        # 計算偏移和縮放
                        offset_x = tx - orig_bbox[0] * scale
                        offset_y = ty - orig_bbox[1] * scale

                        for point in points:
                            new_x = point[0] * scale + offset_x
                            new_y = point[1] * scale + offset_y
                            region_points.append([new_x, new_y])
                    else:  # rectangle
                        # 創建矩形框的四個角點
                        region_points = [
                            [tx, ty],
                            [tx + region_w, ty],
                            [tx + region_w, ty + region_h],
                            [tx, ty + region_h]
                        ]

                    # 檢查重疊
                    overlapping = False
                    if avoid_overlap and result_labels:
                        for existing_label in result_labels:
                            if existing_label["type"] == "polygon":
                                existing_poly = Polygon(
                                    existing_label["points"])
                                new_poly = Polygon(region_points)

                                if not existing_poly.is_valid or not new_poly.is_valid:
                                    continue

                                try:
                                    intersection = new_poly.intersection(
                                        existing_poly).area
                                    union = new_poly.area + existing_poly.area - intersection
                                    iou = intersection / union if union > 0 else 0

                                    if iou > iou_threshold:
                                        overlapping = True
                                        break
                                except Exception:
                                    continue
                            else:  # rectangle
                                xmin, ymin, xmax, ymax = existing_label["bbox"]
                                existing_poly = box(xmin, ymin, xmax, ymax)
                                new_poly = Polygon(region_points)

                                if not existing_poly.is_valid or not new_poly.is_valid:
                                    continue

                                try:
                                    intersection = new_poly.intersection(
                                        existing_poly).area
                                    union = new_poly.area + existing_poly.area - intersection
                                    iou = intersection / union if union > 0 else 0

                                    if iou > iou_threshold:
                                        overlapping = True
                                        break
                                except Exception:
                                    continue

                    if not overlapping:
                        placement_success = True
                        break  # 成功找到放置位置

                # 如果找不到適合的位置，跳過這個區域
                if not placement_success:
                    self.logger.info(f"無法為圖像 {common_key} 找到適合的放置位置，跳過該區域")
                    continue

                # 放置區域
                try:
                    # 根據標籤類型不同的處理方式
                    if region["type"] == "rectangle":
                        # 確保不超出圖像邊界
                        if ty + region_h > th:
                            region_img = region_img[:th-ty, :]
                            region_h = th - ty
                        if tx + region_w > tw:
                            region_img = region_img[:, :tw-tx]
                            region_w = tw - tx

                        # 放置區域
                        roi = result_img[ty:ty+region_h, tx:tx+region_w]
                        result_img[ty:ty+region_h, tx:tx+region_w] = region_img

                    elif region["type"] == "polygon":
                        # 創建目標圖像上的多邊形遮罩
                        mask = np.zeros((th, tw), dtype=np.uint8)
                        polygon = np.array(region_points, dtype=np.int32)
                        cv2.fillPoly(mask, [polygon], 255)

                        # 獲取多邊形的邊界框
                        x, y, w, h = cv2.boundingRect(polygon)

                        # 確保邊界框不超出圖像邊界
                        x = max(0, x)
                        y = max(0, y)
                        w = min(tw - x, w)
                        h = min(th - y, h)

                        if w <= 0 or h <= 0:
                            raise ValueError("處理後的多邊形區域無效")

                        # 提取多邊形區域的遮罩部分
                        roi_mask = mask[y:y+h, x:x+w]

                        # 提取目標圖像中對應的區域
                        roi = result_img[y:y+h, x:x+w].copy()

                        # 縮放來源區域到適合的大小
                        if region_img.shape[:2] != (h, w):
                            region_img = cv2.resize(region_img, (w, h))

                        # 使用遮罩進行混合
                        mask_3ch = cv2.merge(
                            [roi_mask, roi_mask, roi_mask]) / 255.0

                        # 在遮罩區域內直接放置區域 (不使用alpha混合)
                        result = roi * (1 - mask_3ch) + region_img * mask_3ch

                        # 將結果放回原圖
                        result_img[y:y+h, x:x+w] = result

                        # 更新位置信息用於輸出
                        tx, ty = x, y
                        region_w, region_h = w, h

                    # 記錄放置位置，用於避免相似位置
                    position_history.append((tx, ty, region_w, region_h))

                    # 保存兩種形式的標籤 (同時保存 polygon 和 rectangle)
                    # 計算邊界框
                    if region["type"] == "polygon":
                        pts = np.array(region_points)
                        x_min, y_min = pts.min(axis=0)
                        x_max, y_max = pts.max(axis=0)

                        # 添加多邊形標籤
                        result_labels.append({
                            "label": region_class,
                            "points": region_points,
                            "bbox": [x_min, y_min, x_max, y_max],  # 同時保存邊界框資訊
                            "type": "polygon"
                        })
                    else:  # rectangle
                        # 添加矩形標籤
                        result_labels.append({
                            "label": region_class,
                            "bbox": [tx, ty, tx + region_w, ty + region_h],
                            "points": [[tx, ty], [tx + region_w, ty],
                                       # 同時保存四個角點
                                       [tx + region_w, ty + region_h], [tx, ty + region_h]],
                            "type": "rectangle"
                        })

                    # 更新類別計數
                    class_counts[region_class] += 1

                    added_regions += 1
                    self.logger.info(
                        f"成功添加區域 {added_regions}/{num_regions}: 類別 {region_class} -> {common_key} 位置: ({tx}, {ty})")

                except Exception as e:
                    self.logger.error(f"放置區域時出錯: {e}")
                    continue

            # 如果成功添加區域，保存結果
            if added_regions > 0:
                # 存儲增強數據
                augmented_data.append({
                    "basename": output_basename,
                    "image": result_img,
                    "target": target,
                    "labels": result_labels
                })

                # 將標籤保存到暫存數據中
                temp_labels = []
                for label in result_labels:
                    # 保存完整的標籤信息
                    if label["type"] == "polygon":
                        temp_labels.append(
                            [label["label"], label["points"], "polygon"])
                    else:  # rectangle
                        temp_labels.append(
                            [label["label"], label["bbox"], "rectangle"])

                temp_labels_data[common_key] = {
                    'labels': temp_labels,
                    'image_width': result_img.shape[1],
                    'image_height': result_img.shape[0]
                }

                # 更新暫存檔案
                try:
                    with open(temp_label_file, 'w') as f:
                        json.dump(temp_labels_data, f, indent=2)
                    self.logger.info(
                        f"已更新暫存標籤數據，目前有 {len(temp_labels_data)} 張圖像的標籤")
                except Exception as e:
                    self.logger.error(f"保存暫存標籤數據失敗: {e}")

            # 如果嘗試次數太多但未添加任何區域，輸出警告
            elif total_attempts >= max_total_attempts and added_regions == 0:
                self.logger.warning(
                    f"在圖像 {output_basename} 上未能添加任何區域，可能是由於避免重疊或類別限制太嚴格")

        self.logger.info(f"共生成 {len(augmented_data)} 個增強圖像資料")
        return augmented_data

    # 保存增強圖像和標籤
    def _save_task_images(self, augmented_data, task_dir, label_mode, task):
        """根據任務類型保存增強圖像和標籤"""
        for item in tqdm(augmented_data, desc=f"保存 {task} 任務圖像"):
            output_basename = item["basename"]
            result_img = item["image"]
            target = item["target"]
            result_labels = item["labels"]

            # 保存圖像
            output_img_path = task_dir / f"{output_basename}.jpg"
            self._imencode_tofile(result_img, output_img_path)

            # 保存標籤
            output_json_path = task_dir / f"{output_basename}.json"

            # 準備更新JSON數據
            annotation_data = target["data"].copy()
            annotation_data["imagePath"] = f"{output_basename}.jpg"
            annotation_data["imageWidth"] = result_img.shape[1]
            annotation_data["imageHeight"] = result_img.shape[0]

            # 確定使用 shapes 或 annotations
            shapes_field = "shapes" if "shapes" in annotation_data else "annotations"

            # 根據任務類型處理標籤
            if task == "det":
                # 檢測任務：將所有標籤轉換為矩形框
                converted_labels = self._convert_to_rectangles(result_labels)

                # 創建最終的標籤列表
                new_shapes = []
                for label in converted_labels:
                    new_shapes.append({
                        "label": label["label"],
                        "points": [[label["bbox"][0], label["bbox"][1]],
                                   [label["bbox"][2], label["bbox"][3]]],
                        "group_id": None,
                        "shape_type": "rectangle",
                        "flags": {}
                    })

                    # 更新統計
                    self.stats["class_counts"][label["label"]] += 1
            else:
                # 分割任務：保留多邊形標籤
                new_shapes = []
                for label in result_labels:
                    if "points" in label and len(label["points"]) >= 3:
                        # 添加多邊形標籤
                        new_shapes.append({
                            "label": label["label"],
                            "points": label["points"],
                            "group_id": None,
                            "shape_type": "polygon",
                            "flags": {}
                        })

                        # 更新統計
                        self.stats["class_counts"][label["label"]] += 1

            # 更新標籤字段
            annotation_data[shapes_field] = new_shapes

            # 保存JSON
            with open(output_json_path, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)

            # 更新生成計數
            self.stats["generated"] += 1

    # 驗證任務標籤
    def _verify_task_labels(self, task_dir, expected_type="rectangle"):
        """驗證指定任務目錄中的標籤類型"""
        stats = {"total": 0, "rectangle": 0, "polygon": 0, "other": 0}
        non_rectangle_files = []

        # 收集所有 JSON 檔案
        json_files = list(task_dir.glob("*.json"))

        for json_path in tqdm(json_files, desc=f"驗證 {task_dir.name} 標籤"):
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 確定使用 shapes 或 annotations
                shapes_field = "shapes" if "shapes" in data else "annotations"

                # 檢查每個標籤
                for shape in data.get(shapes_field, []):
                    stats["total"] += 1
                    shape_type = shape.get("shape_type", "unknown")

                    if shape_type == "rectangle":
                        stats["rectangle"] += 1
                    elif shape_type == "polygon":
                        stats["polygon"] += 1
                        if expected_type == "rectangle":
                            non_rectangle_files.append(json_path.name)
                    else:
                        stats["other"] += 1
                        if expected_type == "rectangle":
                            non_rectangle_files.append(json_path.name)

            except Exception as e:
                self.logger.error(f"驗證標籤時出錯: {json_path}, {e}")

        # 報告結果
        self.logger.info(f"標籤驗證結果 ({task_dir.name}):")
        self.logger.info(f"  總標籤數: {stats['total']}")
        self.logger.info(
            f"  矩形標籤: {stats['rectangle']} ({stats['rectangle']/stats['total']*100:.1f}% 如果總標籤數>0)")
        self.logger.info(
            f"  多邊形標籤: {stats['polygon']} ({stats['polygon']/stats['total']*100:.1f}% 如果總標籤數>0)")
        self.logger.info(
            f"  其他標籤: {stats['other']} ({stats['other']/stats['total']*100:.1f}% 如果總標籤數>0)")

        # 如果發現非預期類型的標籤，顯示警告
        if expected_type == "rectangle" and (stats["polygon"] > 0 or stats["other"] > 0):
            self.logger.warning(
                f"發現 {len(set(non_rectangle_files))} 個包含非矩形標籤的檔案")
            self.logger.warning(
                f"  前幾個檔案: {', '.join(list(set(non_rectangle_files))[:5])}")

        return stats, non_rectangle_files

    # 生成放置區域
    def _visualize_placement_region(self, target_image, margins, grid_size=None):
        """生成放置區域的可視化圖像，顯示上下左右區域範圍和生成區域分布"""
        try:
            # 讀取一個目標圖像用來可視化
            img_path = target_image["path"]
            img = self._imdecode_unicode(img_path)
            if img is None:
                self.logger.error("無法讀取圖像用於可視化")
                return

            h, w = img.shape[:2]
            top, right, bottom, left = margins

            # 保存當前的網格大小
            self.grid_size = grid_size if grid_size is not None else getattr(
                self, 'grid_size', 8)

            # 計算有效區域 (margins值範圍是0-5，表示圖像邊緣的百分比)
            top_margin = int(h * (top / 10))
            right_margin = int(w * (right / 10))
            bottom_margin = int(h * (bottom / 10))
            left_margin = int(w * (left / 10))

            # 檢查區域是否有效
            if top_margin + bottom_margin >= h or left_margin + right_margin >= w:
                self.logger.warning("指定的邊距導致無效的放置區域")
                return

            # 創建可視化圖像
            vis_img = img.copy()

            # 繪製上下左右邊緣區域，使用半透明覆蓋
            overlay = vis_img.copy()

            # 上邊緣區域 (紅色)
            if top > 0:
                cv2.rectangle(overlay, (0, 0),
                              (w, top_margin), (0, 0, 255), -1)

            # 下邊緣區域 (綠色)
            if bottom > 0:
                cv2.rectangle(overlay, (0, h-bottom_margin),
                              (w, h), (0, 255, 0), -1)

            # 左邊緣區域 (黃色)
            if left > 0:
                cv2.rectangle(overlay, (0, 0), (left_margin, h),
                              (0, 255, 255), -1)

            # 右邊緣區域 (橙色)
            if right > 0:
                cv2.rectangle(overlay, (w-right_margin, 0),
                              (w, h), (0, 165, 255), -1)

            # 將半透明覆蓋添加到原圖
            alpha = 0.4  # 透明度
            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)

            # 在邊緣繪製矩形，顯示有效區域
            cv2.rectangle(vis_img,
                          (left_margin, top_margin),
                          (w - right_margin, h - bottom_margin),
                          (255, 255, 255), 3)

            # 如果啟用網格，繪製網格線
            if self.grid_size > 1:
                cell_width = (w - left_margin - right_margin) / self.grid_size
                cell_height = (h - top_margin - bottom_margin) / self.grid_size

                # 繪製垂直線
                for i in range(1, self.grid_size):
                    x = int(left_margin + i * cell_width)
                    cv2.line(vis_img, (x, top_margin),
                             (x, h - bottom_margin), (255, 255, 255), 1)

                # 繪製水平線
                for i in range(1, self.grid_size):
                    y = int(top_margin + i * cell_height)
                    cv2.line(vis_img, (left_margin, y),
                             (w - right_margin, y), (255, 255, 255), 1)

            # 添加說明文字
            font = cv2.FONT_HERSHEY_SIMPLEX
            margin_text = f"邊距設置: 上={top}, 右={right}, 下={bottom}, 左={left}"
            cv2.putText(vis_img, margin_text, (10, 30),
                        font, 1, (255, 255, 255), 2)

            valid_area_text = f"有效區域: {w-left_margin-right_margin}x{h-top_margin-bottom_margin} 像素"
            cv2.putText(vis_img, valid_area_text, (10, 70),
                        font, 1, (255, 255, 255), 2)

            grid_text = f"網格大小: {self.grid_size}"
            cv2.putText(vis_img, grid_text, (10, 110),
                        font, 1, (255, 255, 255), 2)

            # 添加圖例
            cv2.rectangle(vis_img, (10, h-140), (40, h-110),
                          (0, 0, 255), -1)  # 紅色方塊
            cv2.putText(vis_img, "上邊緣區域", (50, h-115),
                        font, 0.8, (255, 255, 255), 2)

            cv2.rectangle(vis_img, (10, h-100), (40, h-70),
                          (0, 255, 0), -1)  # 綠色方塊
            cv2.putText(vis_img, "下邊緣區域", (50, h-75),
                        font, 0.8, (255, 255, 255), 2)

            cv2.rectangle(vis_img, (10, h-60), (40, h-30),
                          (0, 255, 255), -1)  # 黃色方塊
            cv2.putText(vis_img, "左邊緣區域", (50, h-35),
                        font, 0.8, (255, 255, 255), 2)

            cv2.rectangle(vis_img, (10, h-20), (40, h+10),
                          (0, 165, 255), -1)  # 橙色方塊
            cv2.putText(vis_img, "右邊緣區域", (50, h+5),
                        font, 0.8, (255, 255, 255), 2)

            # 儲存可視化圖像
            vis_path = os.path.join(
                self.output_dir, "placement_region_visualization.jpg")
            self._imencode_tofile(vis_img, vis_path)
            self.logger.info(f"放置區域可視化已保存至 {vis_path}")

            # 嘗試在控制台顯示圖像位置信息
            print(f"可視化圖像已更新，保存在: {vis_path}")

            # 嘗試使用系統默認程序打開圖像
            try:
                # 根據不同平台嘗試打開圖像
                if os.name == 'nt':  # Windows
                    os.startfile(vis_path)
                elif os.name == 'posix':  # Linux, Mac OS X
                    if sys.platform == 'darwin':  # Mac OS X
                        os.system(f'open "{vis_path}"')
                    else:  # Linux
                        os.system(f'xdg-open "{vis_path}"')
                else:
                    self.logger.warning(f"無法自動打開圖像，不支持的操作系統: {os.name}")
            except Exception as e:
                self.logger.warning(f"嘗試打開圖像時出錯: {e}")
                print("請手動打開圖像查看")

        except Exception as e:
            self.logger.error(f"生成可視化圖像時出錯: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    # 收集來源區域
    def _collect_source_regions(self):
        """收集來源區域"""
        all_regions = []

        # 收集每個來源目錄的區域
        for source_dir in self.source_dirs:
            source_dir = Path(source_dir)
            source_name = source_dir.name
            self.logger.info(f"從 {source_name} 收集區域")

            # 統計計數
            self.stats["sources"][source_name] = {"total": 0, "valid": 0}

            # 收集目錄中的所有JSON文件
            annotation_files = list(source_dir.glob("**/*.json"))
            if not annotation_files:
                self.logger.warning(f"在 {source_name} 中找不到JSON文件")
                continue

            # 處理每個JSON文件
            for annotation_file in tqdm(annotation_files, desc=f"處理 {source_name}"):
                self.stats["sources"][source_name]["total"] += 1

                # 讀取JSON
                try:
                    with open(annotation_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                except Exception as e:
                    self.logger.warning(f"無法讀取 {annotation_file.name}: {e}")
                    continue

                # 獲取圖像文件名和路徑
                image_filename = data.get("imagePath")
                if not image_filename:
                    image_filename = f"{annotation_file.stem}.jpg"

                # 查找對應的圖像文件
                image_path = self._find_image_file(
                    annotation_file.parent, image_filename, annotation_file.stem)
                if not image_path:
                    continue

                # 讀取圖像
                img = self._imdecode_unicode(image_path)
                if img is None:
                    continue

                # 調整圖像尺寸
                orig_h, orig_w = img.shape[:2]
                if self.resize_method is not None:
                    img = self._resize_source_image(img)
                    new_h, new_w = img.shape[:2]

                    # 調整標註尺寸
                    scale_x = new_w / orig_w
                    scale_y = new_h / orig_h

                    # 檢查是否使用 shapes 或 annotations
                    shapes_field = "shapes" if "shapes" in data else "annotations"

                    for shape in data.get(shapes_field, []):
                        points = shape.get("points", [])
                        new_points = []
                        for point in points:
                            if len(point) >= 2:
                                x, y = point[:2]
                                new_points.append([x * scale_x, y * scale_y])
                        shape["points"] = new_points

                    data["imageWidth"] = new_w
                    data["imageHeight"] = new_h

                # 從每個形狀提取區域
                # 檢查是否使用 shapes 或 annotations
                shapes_field = "shapes" if "shapes" in data else "annotations"

                for shape in data.get(shapes_field, []):
                    region = self._extract_region(img, shape, data)
                    if region:
                        all_regions.append(region)
                        self.stats["sources"][source_name]["valid"] += 1

        self.logger.info(f"共收集到 {len(all_regions)} 個有效區域")
        return all_regions

    # 收集目標圖像
    def _collect_target_images(self):
        """收集目標圖像"""
        target_images = []

        self.logger.info(f"從 {self.target_dir} 收集目標圖像")

        # 統計計數
        self.stats["targets"]["total"] = 0
        self.stats["targets"]["valid"] = 0

        # 收集目錄中的所有JSON文件
        annotation_files = list(self.target_dir.glob("**/*.json"))
        if not annotation_files:
            self.logger.warning(f"在 {self.target_dir} 中找不到JSON文件")
            return target_images

        # 處理每個JSON文件
        for annotation_file in tqdm(annotation_files, desc="處理目標圖像"):
            self.stats["targets"]["total"] += 1

            # 讀取JSON
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            except Exception as e:
                self.logger.warning(f"無法讀取 {annotation_file.name}: {e}")
                continue

            # 獲取圖像文件名和路徑
            image_filename = data.get("imagePath")
            if not image_filename:
                image_filename = f"{annotation_file.stem}.jpg"

            # 查找對應的圖像文件
            image_path = self._find_image_file(
                annotation_file.parent, image_filename, annotation_file.stem)
            if not image_path:
                continue

            # 讀取圖像
            img = self._imdecode_unicode(image_path)
            if img is None:
                continue

            # 提取已有標籤
            # 檢查是否使用 shapes 或 annotations
            shapes_field = "shapes" if "shapes" in data else "annotations"
            existing_labels = []

            for shape in data.get(shapes_field, []):
                label = shape.get("label", "unknown")
                shape_type = shape.get("shape_type", "polygon")

                if shape_type == "polygon" and len(shape.get("points", [])) >= 3:
                    points = shape.get("points", [])
                    existing_labels.append({
                        "label": label,
                        "points": points,
                        "type": "polygon"
                    })
                elif shape_type == "rectangle" and len(shape.get("points", [])) >= 2:
                    xmin, ymin = shape.get("points")[0]
                    xmax, ymax = shape.get("points")[1]
                    existing_labels.append({
                        "label": label,
                        "bbox": [xmin, ymin, xmax, ymax],
                        "type": "rectangle"
                    })

            target_images.append({
                "path": image_path,
                "json_path": annotation_file,
                "data": data,
                "width": img.shape[1],
                "height": img.shape[0],
                "existing_labels": existing_labels
            })

            self.stats["targets"]["valid"] += 1

        self.logger.info(f"共收集到 {len(target_images)} 個有效目標圖像")
        return target_images

    # 保存增強圖像
    def _save_augmented_image(self, task_dir, output_basename, result_img, target, result_labels):
        """保存增強圖像及標籤"""
        output_img_path = task_dir / f"{output_basename}.jpg"
        output_json_path = task_dir / f"{output_basename}.json"

        # 保存結果圖像
        self._imencode_tofile(result_img, output_img_path)

        # 準備更新JSON數據
        annotation_data = target["data"].copy()
        annotation_data["imagePath"] = f"{output_basename}.jpg"
        annotation_data["imageWidth"] = result_img.shape[1]
        annotation_data["imageHeight"] = result_img.shape[0]

        # 確定使用 shapes 或 annotations
        shapes_field = "shapes" if "shapes" in annotation_data else "annotations"

        # 更新shapes/annotations
        new_shapes = []
        for label in result_labels:
            if label["type"] == "polygon":
                new_shapes.append({
                    "label": label["label"],
                    "points": label["points"],
                    "group_id": None,
                    "shape_type": "polygon",
                    "flags": {}
                })
            else:  # rectangle
                new_shapes.append({
                    "label": label["label"],
                    "points": [[label["bbox"][0], label["bbox"][1]],
                               [label["bbox"][2], label["bbox"][3]]],
                    "group_id": None,
                    "shape_type": "rectangle",
                    "flags": {}
                })

        annotation_data[shapes_field] = new_shapes

        # 保存JSON
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(annotation_data, f, ensure_ascii=False, indent=2)

        return True

    # 提取區域
    def _extract_region(self, img, shape, annotation_data):
        """從圖像和形狀中提取區域"""
        label = shape.get("label", "unknown")
        shape_type = shape.get("shape_type", "polygon")
        points = shape.get("points", [])

        # 檢查是否有足夠的點
        if (shape_type == "polygon" and len(points) < 3) or (shape_type == "rectangle" and len(points) < 2):
            return None

        h, w = img.shape[:2]

        if shape_type == "polygon":
            # 創建遮罩
            mask = np.zeros((h, w), dtype=np.uint8)
            pts = np.array(points, dtype=np.int32)
            cv2.fillPoly(mask, [pts], 255)

            # 計算邊界框
            x, y, w_roi, h_roi = cv2.boundingRect(pts)
            xmin, ymin, xmax, ymax = x, y, x + w_roi, y + h_roi

            # 檢查邊界框是否在圖像範圍內
            if xmin >= w or ymin >= h or xmax <= 0 or ymax <= 0 or w_roi <= 0 or h_roi <= 0:
                self.logger.warning(
                    f"標籤 {label} 邊界框超出圖像範圍: ({xmin}, {ymin}, {xmax}, {ymax}), 圖像尺寸: {w}x{h}")
                return None

            # 確保邊界框在圖像範圍內
            xmin = max(0, xmin)
            ymin = max(0, ymin)
            xmax = min(w, xmax)
            ymax = min(h, ymax)
            w_roi = xmax - xmin
            h_roi = ymax - ymin

            # 確保區域大小有效
            if w_roi <= 0 or h_roi <= 0:
                self.logger.warning(f"標籤 {label} 邊界框尺寸無效: {w_roi}x{h_roi}")
                return None

            # 提取區域
            bbox_img = img[ymin:ymax, xmin:xmax].copy()
            bbox_mask = mask[ymin:ymax, xmin:xmax]

            # 檢查提取的圖像和遮罩是否有效
            if bbox_img.size == 0 or bbox_mask.size == 0:
                self.logger.warning(
                    f"標籤 {label} 提取的區域為空: 圖像尺寸 {bbox_img.shape if hasattr(bbox_img, 'shape') else 'None'}, 遮罩尺寸 {bbox_mask.shape if hasattr(bbox_mask, 'shape') else 'None'}")
                return None

            # 檢查圖像是否有3個通道
            if len(bbox_img.shape) < 3 or bbox_img.shape[2] != 3:
                self.logger.warning(
                    f"標籤 {label} 提取的圖像通道數不正確: {bbox_img.shape}")
                return None

            # 應用遮罩
            region_img = cv2.bitwise_and(bbox_img, bbox_img, mask=bbox_mask)

            # 再次檢查區域圖像是否有效
            if region_img.size == 0 or len(region_img.shape) < 3 or region_img.shape[2] != 3:
                self.logger.warning(
                    f"標籤 {label} 應用遮罩後的區域無效: {region_img.shape if hasattr(region_img, 'shape') else 'None'}")
                return None

            try:
                # 透明背景處理
                b, g, r = cv2.split(region_img)
                alpha = bbox_mask
                rgba = cv2.merge((b, g, r, alpha))

                return {
                    "image": region_img,
                    "rgba": rgba,
                    "mask": bbox_mask,
                    "label": label,
                    "points": points,
                    "bbox": (xmin, ymin, xmax, ymax),
                    "type": "polygon"
                }
            except ValueError as e:
                self.logger.error(f"處理標籤 {label} 的透明背景時出錯: {e}")
                # 直接返回有效的區域，不進行透明處理
                return {
                    "image": region_img,
                    "rgba": None,
                    "mask": bbox_mask,
                    "label": label,
                    "points": points,
                    "bbox": (xmin, ymin, xmax, ymax),
                    "type": "polygon"
                }

        elif shape_type == "rectangle":
            # 提取矩形區域
            try:
                xmin, ymin = map(int, points[0])
                xmax, ymax = map(int, points[1])
            except (IndexError, ValueError):
                self.logger.warning(f"標籤 {label} 的點格式無效: {points}")
                return None

            # 確保坐標在圖像範圍內
            xmin = max(0, xmin)
            ymin = max(0, ymin)
            xmax = min(w, xmax)
            ymax = min(h, ymax)

            if xmin >= xmax or ymin >= ymax:
                self.logger.warning(
                    f"標籤 {label} 的矩形區域無效: ({xmin}, {ymin}, {xmax}, {ymax})")
                return None

            try:
                region_img = img[ymin:ymax, xmin:xmax].copy()

                # 檢查區域是否有效
                if region_img.size == 0:
                    self.logger.warning(f"標籤 {label} 提取的矩形區域為空")
                    return None

                return {
                    "image": region_img,
                    "label": label,
                    "bbox": (xmin, ymin, xmax, ymax),
                    "type": "rectangle"
                }
            except Exception as e:
                self.logger.error(f"提取標籤 {label} 的矩形區域時出錯: {e}")
                return None

        return None

    # 獲取圖像文件
    def _find_image_file(self, dir_path, filename, stem):
        """查找圖像文件"""
        # 嘗試直接使用filename
        file_path = dir_path / filename
        if file_path.exists():
            return file_path

        # 嘗試不同的擴展名
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            file_path = dir_path / f"{stem}{ext}"
            if file_path.exists():
                return file_path

        # 向上一級查找original目錄
        original_dir = dir_path.parent / "original"
        if original_dir.exists():
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                file_path = original_dir / f"{stem}{ext}"
                if file_path.exists():
                    return file_path

        self.logger.warning(f"找不到圖像文件: {filename} 或 {stem}.*")
        return None

    # 調整來源圖像尺寸
    def _resize_source_image(self, img):
        """調整來源圖像尺寸"""
        if self.resize_method is None:
            return img

        h, w = img.shape[:2]

        if self.resize_method == 'absolute':
            new_w, new_h = self.target_size
        else:  # relative
            new_w = int(w * self.scale_factor)
            new_h = int(h * self.scale_factor)

        # 只有當尺寸變化時才進行縮放
        if new_w != w or new_h != h:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
        return img

    # 讀取圖像
    def _imdecode_unicode(self, path):
        """讀取圖像，支援 Unicode 路徑"""
        path = Path(path)
        if not path.exists():
            return None

        try:
            with open(path, 'rb') as f:
                buf = f.read()

            if not buf:
                return None

            arr = np.frombuffer(buf, dtype=np.uint8)
            return cv2.imdecode(arr, cv2.IMREAD_COLOR)
        except Exception as e:
            self.logger.error(f"讀取圖像失敗: {path}, 錯誤: {e}")
            return None

    # 將圖像編碼並保存到文件
    def _imencode_tofile(self, img, out_path):
        """將圖像編碼並保存到文件"""
        out_path = Path(out_path)
        out_path.parent.mkdir(parents=True, exist_ok=True)

        # 確保副檔名為 .jpg
        out_path = out_path.with_suffix(".jpg")

        encode_params = [int(cv2.IMWRITE_JPEG_QUALITY), self.quality]

        try:
            ret, buf = cv2.imencode(".jpg", img, encode_params)
            if ret:
                with open(out_path, 'wb') as f:
                    f.write(buf)
                return True
            else:
                self.logger.error(f"編碼圖像失敗: {out_path}")
                return False
        except Exception as e:
            self.logger.error(f"保存圖像失敗: {out_path}, 錯誤: {e}")
            return False

    # 創建標籤顏色映射
    def _create_color_map(self):
        """創建標籤顏色映射 (動態生成)"""
        color_map = {}

        # 從目標目錄收集所有類別
        target_images = self._collect_target_images()
        class_distribution = self._analyze_target_class_distribution()

        for label in class_distribution.keys():
            # 為未定義的類別生成隨機顏色
            color_map[label] = (
                random.randint(0, 255),
                random.randint(0, 255),
                random.randint(0, 255)
            )

        return color_map

    # 生成報告
    def _generate_report(self):
        """生成增強報告"""
        # 保存統計信息
        stats_file = self.output_dir / "augmentation_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            # 將Counter轉換為字典
            stats_dict = {
                "generated": self.stats["generated"],
                "failures": self.stats["failures"],
                "class_counts": dict(self.stats["class_counts"]),
                "resize_info": self.stats["resize_info"],
                "sources": self.stats["sources"],
                "targets": self.stats["targets"]
            }
            json.dump(stats_dict, f, ensure_ascii=False, indent=2)

        # 生成可讀報告
        report_file = self.output_dir / "augmentation_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("===== 數據增強報告 =====\n\n")

            f.write(f"生成圖像數量: {self.stats['generated']}\n")
            f.write(f"失敗次數: {self.stats['failures']}\n\n")

            f.write("類別統計:\n")
            for label, count in sorted(self.stats["class_counts"].items(), key=lambda x: -x[1]):
                target = self.class_targets.get(label, 0)
                f.write(f"  {label}: {count}")
                if target > 0:
                    f.write(f" (目標: {target})")
                elif target == 0:
                    f.write(" (設置為不生成)")
                f.write("\n")

            f.write("\n來源統計:\n")
            for source, counts in self.stats["sources"].items():
                f.write(
                    f"  {source}: 總數={counts['total']}, 有效={counts['valid']}\n")

            f.write(
                f"\n目標圖像: 總數={self.stats['targets']['total']}, 有效={self.stats['targets']['valid']}\n")

        self.logger.info(f"增強報告已保存至 {report_file}")

    # 互動式運行
    def run_interactive(self,
                        num_generations=100,
                        regions_per_image=(2, 3),
                        scale_range=(0.6, 1.2),
                        margins=(0, 0, 0, 0),
                        grid_placement=True,
                        grid_size=8,
                        max_placement_attempts=50,
                        temp_label_file=None,
                        avoid_similar_positions=True,
                        avoid_overlap=True,
                        iou_threshold=0.1):
        """
        互動式運行數據增強流程

        參數與 run() 方法相同，但增加了交互式配置界面
        """
        # 設置暫存標籤文件
        if temp_label_file is None:
            temp_label_file = os.path.join(self.output_dir, "temp_labels.json")

        # 用於追踪圖像窗口
        self.current_figure = None

        # 獲取至少一個有效的目標圖像用於可視化
        self.logger.info("收集目標圖像用於可視化...")
        target_images = self._collect_target_images()

        if not target_images:
            self.logger.error("沒有可用的目標圖像")
            return None

        # 選擇一個目標圖像用於可視化
        target_image = target_images[0]

        # 互動式設置參數
        current_margins = list(margins)
        current_grid_size = grid_size
        current_regions_per_image = list(regions_per_image)
        current_scale_range = list(scale_range)
        current_num_generations = num_generations
        current_avoid_overlap = avoid_overlap
        current_avoid_similar_positions = avoid_similar_positions
        current_max_placement_attempts = max_placement_attempts
        current_class_targets = self.class_targets.copy() if self.class_targets else {}

        # 初始可視化
        self._visualize_placement_region(
            target_image, tuple(current_margins), current_grid_size)

        print("\n=== 互動式數據增強配置 ===")
        print("請設置生成參數，確認後將開始生成增強數據")

        # 互動循環
        while True:
            print("\n當前設置:")
            print(
                f"邊距設置: 上={current_margins[0]}, 右={current_margins[1]}, 下={current_margins[2]}, 左={current_margins[3]}")
            print(f"網格大小: {current_grid_size}")
            print(
                f"預計生成: {current_num_generations} 張圖像，每張圖像 {current_regions_per_image[0]}-{current_regions_per_image[1]} 個區域")
            print(f"縮放比例範圍: {current_scale_range[0]}-{current_scale_range[1]}")
            print(f"避免重疊: {'是' if current_avoid_overlap else '否'}")
            print(f"避免相似位置: {'是' if current_avoid_similar_positions else '否'}")
            print(f"每個區域最大嘗試放置次數: {current_max_placement_attempts}")
            print(f"任務類型: {self.task_type}")

            print("\n選項:")
            print("1. 修改上邊距")
            print("2. 修改右邊距")
            print("3. 修改下邊距")
            print("4. 修改左邊距")
            print("5. 修改網格大小")
            print("6. 修改生成數量")
            print("7. 修改每張圖像區域數量範圍")
            print("8. 修改縮放比例範圍")
            print("9. 切換避免重疊設置")
            print("10. 切換避免相似位置設置")
            print("11. 設置類別目標數量")
            print("12. 重新可視化當前設置")
            print("13. 確認設置並開始生成")
            print("0. 取消操作")

            choice = input("\n請輸入選項 (0-13): ")

            try:
                if choice == "1":
                    value = input("請輸入上邊距 (0-5): ")
                    value = int(value)
                    if 0 <= value <= 5:
                        current_margins[0] = value
                        self._visualize_placement_region(
                            target_image, tuple(current_margins), current_grid_size)
                    else:
                        print("錯誤: 值必須在 0-5 範圍內")

                elif choice == "2":
                    value = input("請輸入右邊距 (0-5): ")
                    value = int(value)
                    if 0 <= value <= 5:
                        current_margins[1] = value
                        self._visualize_placement_region(
                            target_image, tuple(current_margins), current_grid_size)
                    else:
                        print("錯誤: 值必須在 0-5 範圍內")

                elif choice == "3":
                    value = input("請輸入下邊距 (0-5): ")
                    value = int(value)
                    if 0 <= value <= 5:
                        current_margins[2] = value
                        self._visualize_placement_region(
                            target_image, tuple(current_margins), current_grid_size)
                    else:
                        print("錯誤: 值必須在 0-5 範圍內")

                elif choice == "4":
                    value = input("請輸入左邊距 (0-5): ")
                    value = int(value)
                    if 0 <= value <= 5:
                        current_margins[3] = value
                        self._visualize_placement_region(
                            target_image, tuple(current_margins), current_grid_size)
                    else:
                        print("錯誤: 值必須在 0-5 範圍內")

                elif choice == "5":
                    value = input("請輸入網格大小 (2-10): ")
                    value = int(value)
                    if 2 <= value <= 10:
                        current_grid_size = value
                        self._visualize_placement_region(
                            target_image, tuple(current_margins), current_grid_size)
                    else:
                        print("錯誤: 值必須在 2-10 範圍內")

                elif choice == "6":
                    value = input("請輸入生成圖像數量: ")
                    value = int(value)
                    if value > 0:
                        current_num_generations = value
                    else:
                        print("錯誤: 生成數量必須大於0")

                elif choice == "7":
                    min_val = input("請輸入每張圖像最少區域數量 (至少為2): ")
                    max_val = input("請輸入每張圖像最多區域數量: ")
                    min_val = int(min_val)
                    max_val = int(max_val)
                    if 2 <= min_val <= max_val:
                        current_regions_per_image = [min_val, max_val]
                    else:
                        print("錯誤: 最小值必須為2或更大，且不大於最大值")

                elif choice == "8":
                    min_val = input("請輸入最小縮放比例 (0.1-2.0): ")
                    max_val = input("請輸入最大縮放比例 (0.1-2.0): ")
                    min_val = float(min_val)
                    max_val = float(max_val)
                    if 0.1 <= min_val <= max_val <= 2.0:
                        current_scale_range = [min_val, max_val]
                    else:
                        print("錯誤: 縮放比例必須在0.1-2.0範圍內，且最小值不大於最大值")

                elif choice == "9":
                    current_avoid_overlap = not current_avoid_overlap
                    print(
                        f"避免重疊設置已切換為: {'開啟' if current_avoid_overlap else '關閉'}")

                elif choice == "10":
                    current_avoid_similar_positions = not current_avoid_similar_positions
                    print(
                        f"避免相似位置設置已切換為: {'開啟' if current_avoid_similar_positions else '關閉'}")

                elif choice == "11":
                    # 先分析目標目錄中的所有類別數量
                    class_distribution = self._analyze_target_class_distribution()

                    # 改進後的類別目標設置流程 - 連續修改多個類別
                    self._interactive_class_targets_setting(
                        class_distribution, current_class_targets)

                    # 更新實例變量
                    self.class_targets = current_class_targets

                # 顯示可視化
                elif choice == "12":
                    print("重新生成可視化...")
                    self._visualize_placement_region(
                        target_image, tuple(current_margins), current_grid_size)
                    print("可視化已更新，請查看輸出目錄中的 placement_region_visualization.jpg")

                elif choice == "13":
                    # 列出所有將要使用的設置
                    print("\n=== 確認最終設置 ===")
                    print("邊距設置:")
                    print(
                        f"  上邊距: {current_margins[0]} (圖像高度的 {current_margins[0]/10:.1%})")
                    print(
                        f"  右邊距: {current_margins[1]} (圖像寬度的 {current_margins[1]/10:.1%})")
                    print(
                        f"  下邊距: {current_margins[2]} (圖像高度的 {current_margins[2]/10:.1%})")
                    print(
                        f"  左邊距: {current_margins[3]} (圖像寬度的 {current_margins[3]/10:.1%})")
                    print(f"網格設置: {current_grid_size}x{current_grid_size} 網格")
                    print(f"生成數量: {current_num_generations} 張圖像")
                    print(
                        f"每張圖像區域數量: {current_regions_per_image[0]}-{current_regions_per_image[1]} 個")
                    print(
                        f"縮放比例範圍: {current_scale_range[0]:.1f}-{current_scale_range[1]:.1f}")
                    print(f"避免重疊: {'是' if current_avoid_overlap else '否'}")
                    print(
                        f"避免相似位置: {'是' if current_avoid_similar_positions else '否'}")
                    print(f"輸出目錄: {self.output_dir}")
                    print(f"來源目錄數量: {len(self.source_dirs)}")
                    print(f"目標目錄: {self.target_dir}")
                    print(f"任務類型: {self.task_type}")
                    print("類別目標設置:")
                    if not current_class_targets:
                        print("  沒有設置類別目標（預設所有類別均不生成）")
                    else:
                        # 先從目標目錄中所有類別開始
                        class_distribution = self._analyze_target_class_distribution()
                        for cls in class_distribution.keys():
                            if cls in current_class_targets:
                                target = current_class_targets[cls]
                                if target > 0:
                                    print(f"  {cls}: {target}")
                                else:
                                    print(f"  {cls}: 不生成")
                            else:
                                print(f"  {cls}: 預設不生成")

                    confirm = input("\n確認開始生成增強數據? (y/n): ")
                    if confirm.lower() == 'y':
                        # 關閉當前圖像窗口
                        self._close_current_figure()

                        print("\n開始生成增強數據...")

                        # 收集來源區域
                        self.logger.info("收集來源區域...")
                        source_regions = self._collect_source_regions()

                        if not source_regions:
                            self.logger.error("沒有可用的來源區域")
                            print("錯誤: 沒有找到可用的來源區域")
                            return None

                        # =====================================================================
                        # 更新：與 run() 方法使用相同的流程
                        # =====================================================================

                        # 首先生成所有增強圖像資料
                        self.logger.info(f"第一階段: 生成增強圖像資料...")
                        augmented_images_data = self._generate_augmentation_data(
                            source_regions,
                            target_images,
                            current_num_generations,
                            tuple(current_regions_per_image),
                            tuple(current_scale_range),
                            tuple(current_margins),
                            grid_placement,
                            current_grid_size,
                            current_max_placement_attempts,
                            current_avoid_similar_positions,
                            current_avoid_overlap,
                            iou_threshold,
                            temp_label_file
                        )

                        # 根據任務類型保存增強圖像
                        self.logger.info(f"第二階段: 根據任務類型保存增強圖像...")

                        if self.task_type == 'both':
                            tasks = ['seg', 'det']
                        else:
                            tasks = [self.task_type]

                        for task in tasks:
                            self.logger.info(f"為任務類型 '{task}' 保存增強數據...")
                            label_mode = 'polygon' if task == 'seg' else 'box'

                            task_dir = self.output_dir / task
                            task_dir.mkdir(parents=True, exist_ok=True)

                            # 保存為該任務類型的圖像和標籤
                            self._save_task_images(
                                augmented_images_data, task_dir, label_mode, task)

                        # 驗證任務輸出的標籤類型
                        self.logger.info("驗證任務輸出的標籤類型...")
                        for task in tasks:
                            task_dir = self.output_dir / task
                            expected_type = "polygon" if task == "seg" else "rectangle"
                            stats, non_valid_files = self._verify_task_labels(
                                task_dir, expected_type)

                            # 如果發現非法標籤，並且是 det 任務，嘗試修復它們
                            if task == "det" and (stats["polygon"] > 0 or stats["other"] > 0):
                                self.logger.warning(
                                    f"檢測到 {task} 任務中有非矩形標籤，嘗試修復...")
                                self._fix_det_labels(task_dir, non_valid_files)

                        # 生成報告
                        self._generate_report()

                        return self.stats

                elif choice == "0":
                    # 關閉當前圖像窗口
                    self._close_current_figure()
                    print("操作已取消")
                    return None

                else:
                    print("無效的選項，請重新輸入")

            except ValueError as e:
                print(f"輸入錯誤: {e}")

        return None

    # 互動式設置類別目標數量
    def _interactive_class_targets_setting(self, class_distribution, current_class_targets):
        """互動式設置類別目標數量"""

        while True:
            print("\n目標目錄中的類別分布:")
            for cls, count in sorted(class_distribution.items(), key=lambda x: -x[1]):
                print(f"  {cls}: {count} 個標籤")

            print("\n當前類別目標設置:")
            if not current_class_targets:
                print("  沒有設置類別目標（預設所有類別均不生成）")
            else:
                for cls, count in current_class_targets.items():
                    if count > 0:
                        print(f"  {cls}: {count}")
                    else:  # count == 0
                        print(f"  {cls}: 不生成")

            print("\n選項:")
            print("1. 添加或修改類別目標")
            print("2. 移除類別目標")
            print("3. 清空所有類別目標")
            print("0. 返回上層菜單")

            subchoice = input("請選擇操作: ")

            if subchoice == "1":
                while True:
                    cls_name = input("請輸入類別名稱 (直接按Enter返回上一層): ")
                    if not cls_name:
                        break

                    # 檢查類別是否存在於目標目錄中
                    if cls_name in class_distribution:
                        print(
                            f"目標目錄中 '{cls_name}' 類別有 {class_distribution[cls_name]} 個標籤")
                    else:
                        print(f"警告: 目標目錄中沒有發現 '{cls_name}' 類別")
                        confirm = input(
                            f"確定要為不存在的類別 '{cls_name}' 設置目標數量嗎? (y/n): ")
                        if confirm.lower() != 'y':
                            continue
                    try:
                        current_target = current_class_targets.get(cls_name, 0)
                        print(
                            f"當前 '{cls_name}' 的目標數量為: {current_target if current_target > 0 else '不生成'}")

                        count_input = input(
                            f"請輸入 {cls_name} 的目標數量 (enter=回上一頁，0=不生成，數字=生成數量): ")

                        # 如果直接按 Enter，回到上一層
                        if not count_input.strip():
                            print(f"回到上一層")
                            break

                        count = int(count_input)
                        if count > 0:
                            # 設定正數表示生成指定數量
                            current_class_targets[cls_name] = count
                            print(f"已設置 {cls_name} 的目標數量為 {count}")

                            # 詢問是否繼續添加/修改其他類別
                            continue_adding = input("是否繼續添加/修改其他類別? (y/n): ")
                            if continue_adding.lower() != 'y':
                                break
                        elif count == 0:
                            # 設定為0表示不生成此類
                            current_class_targets[cls_name] = 0
                            print(f"已設置 {cls_name} 為不生成")

                            # 詢問是否繼續添加/修改其他類別
                            continue_adding = input("是否繼續添加/修改其他類別? (y/n): ")
                            if continue_adding.lower() != 'y':
                                break
                        else:
                            print("錯誤: 目標數量必須大於或等於0")
                    except ValueError:
                        print("錯誤: 請輸入有效的數字")

            elif subchoice == "2":
                while True:
                    if not current_class_targets:
                        print("目前沒有設置任何類別目標")
                        break

                    cls_name = input("請輸入要移除的類別名稱 (直接按Enter返回上一層): ")
                    if not cls_name:
                        break

                    if cls_name in current_class_targets:
                        del current_class_targets[cls_name]
                        print(f"已移除 {cls_name} 的目標設置")

                        # 詢問是否繼續移除其他類別
                        continue_removing = input("是否繼續移除其他類別? (y/n): ")
                        if continue_removing.lower() != 'y':
                            break
                    else:
                        print(f"類別 {cls_name} 未設置目標")

            elif subchoice == "3":
                confirm = input("確定要清空所有類別目標設置嗎? (y/n): ")
                if confirm.lower() == 'y':
                    current_class_targets.clear()
                    print("已清空所有類別目標設置")

            elif subchoice == "0":
                break

            else:
                print("無效的選項，請重新輸入")

    # 用於分析目標目錄中的類別分布
    def _analyze_target_class_distribution(self):
        """分析目標目錄中的類別分布"""
        class_distribution = Counter()

        # 收集目標圖像的所有標籤
        target_images = self._collect_target_images()

        for target in target_images:
            for label in target["existing_labels"]:
                class_distribution[label["label"]] += 1

        return class_distribution

    # 用於關閉當前的圖像窗口
    def _close_current_figure(self):
        """關閉當前的圖像窗口"""
        try:
            import matplotlib.pyplot as plt
            plt.close('all')
        except:
            pass

    # 用於生成放置區域的可視化圖像
    def _visualize_placement_region(self, target_image, margins, grid_size=None):
        """生成放置區域的可視化圖像，顯示上下左右區域範圍和生成區域分布"""
        try:
            # 首先關閉之前的圖像窗口
            self._close_current_figure()

            # 讀取一個目標圖像用來可視化
            img_path = target_image["path"]
            img = self._imdecode_unicode(img_path)
            if img is None:
                self.logger.error("無法讀取圖像用於可視化")
                return

            h, w = img.shape[:2]
            top, right, bottom, left = margins

            # 保存當前的網格大小
            self.grid_size = grid_size if grid_size is not None else getattr(
                self, 'grid_size', 8)

            # 計算有效區域 (margins值範圍是0-5，表示圖像邊緣的百分比)
            top_margin = int(h * (top / 10))
            right_margin = int(w * (right / 10))
            bottom_margin = int(h * (bottom / 10))
            left_margin = int(w * (left / 10))

            # 檢查區域是否有效
            if top_margin + bottom_margin >= h or left_margin + right_margin >= w:
                self.logger.warning("指定的邊距導致無效的放置區域")
                return

            # 創建可視化圖像
            vis_img = img.copy()

            # 繪製上下左右邊緣區域，使用半透明覆蓋
            overlay = vis_img.copy()

            # 上邊緣區域 (紅色)
            if top > 0:
                cv2.rectangle(overlay, (0, 0),
                              (w, top_margin), (0, 0, 255), -1)

            # 下邊緣區域 (綠色)
            if bottom > 0:
                cv2.rectangle(overlay, (0, h-bottom_margin),
                              (w, h), (0, 255, 0), -1)

            # 左邊緣區域 (黃色)
            if left > 0:
                cv2.rectangle(overlay, (0, 0), (left_margin, h),
                              (0, 255, 255), -1)

            # 右邊緣區域 (橙色)
            if right > 0:
                cv2.rectangle(overlay, (w-right_margin, 0),
                              (w, h), (0, 165, 255), -1)

            # 將半透明覆蓋添加到原圖
            alpha = 0.4  # 透明度
            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)

            # 在邊緣繪製矩形，顯示有效區域
            cv2.rectangle(vis_img,
                          (left_margin, top_margin),
                          (w - right_margin, h - bottom_margin),
                          (255, 255, 255), 3)

            # 如果啟用網格，繪製網格線
            if self.grid_size > 1:
                cell_width = (w - left_margin - right_margin) / self.grid_size
                cell_height = (h - top_margin - bottom_margin) / self.grid_size

                # 繪製垂直線
                for i in range(1, self.grid_size):
                    x = int(left_margin + i * cell_width)
                    cv2.line(vis_img, (x, top_margin),
                             (x, h - bottom_margin), (255, 255, 255), 1)

                # 繪製水平線
                for i in range(1, self.grid_size):
                    y = int(top_margin + i * cell_height)
                    cv2.line(vis_img, (left_margin, y),
                             (w - right_margin, y), (255, 255, 255), 1)

            # 添加說明文字
            font = cv2.FONT_HERSHEY_SIMPLEX
            margin_text = f"邊距設置: 上={top}, 右={right}, 下={bottom}, 左={left}"
            cv2.putText(vis_img, margin_text, (10, 30),
                        font, 1, (255, 255, 255), 2)

            valid_area_text = f"有效區域: {w-left_margin-right_margin}x{h-top_margin-bottom_margin} 像素"
            cv2.putText(vis_img, valid_area_text, (10, 70),
                        font, 1, (255, 255, 255), 2)

            grid_text = f"網格大小: {self.grid_size}x{self.grid_size}"
            cv2.putText(vis_img, grid_text, (10, 110),
                        font, 1, (255, 255, 255), 2)

            # 添加網格單元格大小信息
            if self.grid_size > 1:
                cell_width = (w - left_margin - right_margin) / self.grid_size
                cell_height = (h - top_margin - bottom_margin) / self.grid_size
                cell_text = f"網格單元大小: {cell_width:.1f}x{cell_height:.1f} 像素"
                cv2.putText(vis_img, cell_text, (10, 150),
                            font, 1, (255, 255, 255), 2)

            # 添加圖例
            cv2.rectangle(vis_img, (10, h-140), (40, h-110),
                          (0, 0, 255), -1)  # 紅色方塊
            cv2.putText(vis_img, "上邊緣區域", (50, h-115),
                        font, 0.8, (255, 255, 255), 2)

            cv2.rectangle(vis_img, (10, h-100), (40, h-70),
                          (0, 255, 0), -1)  # 綠色方塊
            cv2.putText(vis_img, "下邊緣區域", (50, h-75),
                        font, 0.8, (255, 255, 255), 2)

            cv2.rectangle(vis_img, (10, h-60), (40, h-30),
                          (0, 255, 255), -1)  # 黃色方塊
            cv2.putText(vis_img, "左邊緣區域", (50, h-35),
                        font, 0.8, (255, 255, 255), 2)

            cv2.rectangle(vis_img, (10, h-20), (40, h+10),
                          (0, 165, 255), -1)  # 橙色方塊
            cv2.putText(vis_img, "右邊緣區域", (50, h+5),
                        font, 0.8, (255, 255, 255), 2)

            # 儲存可視化圖像
            vis_path = os.path.join(
                self.output_dir, "placement_region_visualization.jpg")
            self._imencode_tofile(vis_img, vis_path)
            self.logger.info(f"放置區域可視化已保存至 {vis_path}")

            # 嘗試在控制台顯示圖像位置信息
            print(f"可視化圖像已更新，保存在: {vis_path}")

            # 嘗試顯示圖像
            try:
                import matplotlib.pyplot as plt
                plt.figure(figsize=(12, 10))
                plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
                plt.axis('off')
                plt.title(
                    f"放置區域可視化 (上={top}, 右={right}, 下={bottom}, 左={left}, 網格={self.grid_size}x{self.grid_size})")
                plt.show(block=False)  # 非阻塞顯示
            except Exception as e:
                self.logger.debug(f"無法使用matplotlib顯示圖像: {e}")
                # 嘗試使用系統默認程序打開圖像
                try:
                    if os.name == 'nt':  # Windows
                        os.startfile(vis_path)
                    elif os.name == 'posix':  # Linux, Mac OS X
                        if sys.platform == 'darwin':  # Mac OS X
                            os.system(f'open "{vis_path}"')
                        else:  # Linux
                            os.system(f'xdg-open "{vis_path}"')
                    else:
                        self.logger.warning(f"無法自動打開圖像，不支持的操作系統: {os.name}")
                except Exception as e:
                    self.logger.warning(f"嘗試打開圖像時出錯: {e}")
                    print("請手動打開圖像查看")

        except Exception as e:
            self.logger.error(f"生成可視化圖像時出錯: {e}")
            import traceback
            self.logger.error(traceback.format_exc())


# 如果作為獨立腳本運行
if __name__ == "__main__":
    import argparse

    # 設置命令行參數
    parser = argparse.ArgumentParser(description="增強版數據增強工具")
    parser.add_argument("--sources", nargs='+', required=True, help="來源目錄列表")
    parser.add_argument("--target", required=True, help="目標目錄")
    parser.add_argument("--output", required=True, help="輸出目錄")
    parser.add_argument("--num", type=int, default=100, help="生成圖像數量")
    parser.add_argument("--resize", default=0.3, help="調整來源尺寸為浮點數縮放比例")
    parser.add_argument("--quality", type=int, default=75, help="JPEG品質，1-100")
    parser.add_argument("--class-targets", help="類別目標計數JSON文件路徑")
    parser.add_argument("--margins", type=str, default="0,0,0,0",
                        help="放置區域邊距 (top,right,bottom,left)，0-5")
    parser.add_argument("--grid-size", type=int, default=4, help="網格大小")
    parser.add_argument("--temp-file", help="暫存標籤文件路徑")
    parser.add_argument("--visualize", action="store_true", help="是否生成放置區域可視化")
    parser.add_argument("--task", choices=["seg", "det", "both"],
                        default="both", help="任務類型: seg(分割), det(檢測), both(兩者)")
    parser.add_argument("--scale-range", type=str,
                        default="0.6,1.2", help="縮放比例範圍，格式為 'min,max'")
    parser.add_argument("--interactive", action="store_true",
                        help="啟用互動模式，可視化並調整生成參數")

    args = parser.parse_args()

    # 解析resize參數
    resize = None
    if args.resize:
        try:
            scale = float(args.resize)
            if 0 < scale < 1:
                resize = scale
            else:
                print(f"無效的resize參數: {args.resize}，使用默認值0.3")
                resize = 0.3
        except:
            print(f"無效的resize參數: {args.resize}，使用默認值0.3")
            resize = 0.3

    # 解析類別目標計數
    class_targets = {}
    if args.class_targets:
        try:
            with open(args.class_targets, 'r', encoding='utf-8') as f:
                class_targets = json.load(f)
        except Exception as e:
            print(f"無法讀取類別目標文件: {e}")

    # 解析邊距參數
    try:
        margins = tuple(map(int, args.margins.split(',')))
        if len(margins) != 4:
            raise ValueError("邊距參數必須包含4個值")
        for m in margins:
            if not 0 <= m <= 5:
                raise ValueError("邊距參數必須在0-5範圍內")
    except Exception as e:
        print(f"無效的邊距參數: {args.margins}，使用默認值 (0,0,0,0)")
        margins = (0, 0, 0, 0)

    # 解析縮放比例範圍
    try:
        scale_range = tuple(map(float, args.scale_range.split(',')))
        if len(scale_range) != 2:
            raise ValueError("縮放比例範圍參數必須包含2個值")
        if not (0.1 <= scale_range[0] <= scale_range[1] <= 2.0):
            raise ValueError("縮放比例範圍必須在0.1-2.0之間，且最小值不大於最大值")
    except Exception as e:
        print(f"無效的縮放比例範圍參數: {args.scale_range}，使用默認值 (0.6,1.2)")
        scale_range = (0.6, 1.2)

    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("data_augmenter.log",
                                mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("ImageAugmenter")

    # 創建增強器
    augmenter = ImageAugmenter(
        source_dirs=args.sources,
        target_dir=args.target,
        output_dir=args.output,
        resize=resize,
        quality=args.quality,
        class_targets=class_targets,
        task_type=args.task,
        logger=logger
    )

    # 根據模式決定運行方式
    if args.interactive:
        # 互動模式
        print("啟動互動模式...")
        stats = augmenter.run_interactive(
            num_generations=args.num,
            regions_per_image=(2, 3),  # 最少區域數量為2
            scale_range=scale_range,
            margins=margins,
            grid_placement=True,
            grid_size=args.grid_size,
            max_placement_attempts=50,
            temp_label_file=args.temp_file,
            avoid_similar_positions=True,
            avoid_overlap=True,
            iou_threshold=0.1
        )
    else:
        # 常規模式
        stats = augmenter.run(
            num_generations=args.num,
            regions_per_image=(2, 3),  # 最少區域數量為2
            scale_range=scale_range,
            margins=margins,
            grid_placement=True,
            grid_size=args.grid_size,
            max_placement_attempts=50,
            temp_label_file=args.temp_file,
            avoid_similar_positions=True,
            avoid_overlap=True,
            iou_threshold=0.1,
            visualize_region=args.visualize
        )

    if stats:
        print(f"增強完成！生成了 {stats['generated']} 張圖像")
        print(
            f"詳細報告已保存至 {os.path.join(args.output, 'augmentation_report.txt')}")
    else:
        print("操作已取消或失敗")
