"""
統一架構測試腳本

測試所有整合的架構變體：
- CSP_IFormer 2024變體
- SegMAN變體  
- 混合架構
- 統一工廠系統

驗證功能：
- 模型創建
- 前向傳播
- 特徵提取
- 參數統計
- 性能基準測試
"""

import torch
import torch.nn as nn
import time
import warnings
from typing import Dict, List, Tuple, Any
import traceback

# 導入統一架構系統
try:
    from .core.enhanced_factory import EnhancedModelFactory, create_enhanced_model
    from .core.unified_registry import get_unified_registry, list_available_architectures
    from .core.registry import ENCODER_REGISTRY, MODEL_REGISTRY
except ImportError as e:
    print(f"Import warning: {e}")
    # 嘗試相對導入
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from core.enhanced_factory import EnhancedModelFactory, create_enhanced_model
        from core.unified_registry import get_unified_registry, list_available_architectures
        from core.registry import ENCODER_REGISTRY, MODEL_REGISTRY
    except ImportError as e2:
        print(f"Could not import unified architecture system: {e2}")
        print("Running in standalone mode...")


class ArchitectureTester:
    """架構測試器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.test_results = {}
        self.failed_tests = []
        
        # 初始化統一註冊系統
        try:
            self.registry = get_unified_registry()
            self.factory = EnhancedModelFactory()
            print(f"✓ Unified registry initialized successfully")
        except Exception as e:
            print(f"✗ Failed to initialize unified registry: {e}")
            self.registry = None
            self.factory = None
    
    def run_comprehensive_test(self):
        """運行綜合測試"""
        
        print("Starting Comprehensive Architecture Test")
        print("=" * 60)
        
        if not self.registry or not self.factory:
            print("Cannot run tests without unified registry")
            return
        
        # 測試統一註冊系統
        self.test_unified_registry()
        
        # 測試CSP_IFormer變體
        self.test_csp_iformer_variants()
        
        # 測試SegMAN變體
        self.test_segman_variants()
        
        # 測試混合架構
        self.test_hybrid_architectures()
        
        # 測試增強工廠
        self.test_enhanced_factory()
        
        # 性能基準測試
        self.run_performance_benchmark()
        
        # 生成測試報告
        self.generate_test_report()
    
    def test_unified_registry(self):
        """測試統一註冊系統"""
        
        print("\n1. Testing Unified Registry System")
        print("-" * 40)
        
        try:
            # 獲取註冊狀態
            status = self.registry.get_registration_status()
            components = self.registry.list_all_components()
            
            print(f"Registration Status:")
            for component, success in status.items():
                icon = "✓" if success else "✗"
                print(f"  {icon} {component}")
            
            print(f"\nComponent Counts:")
            print(f"  Encoders: {len(components['encoders'])}")
            print(f"  Decoders: {len(components['decoders'])}")
            print(f"  Models: {len(components['models'])}")
            
            self.test_results['unified_registry'] = {
                'status': 'success',
                'components': len(components['encoders']) + len(components['decoders']) + len(components['models']),
                'registration_success_rate': sum(status.values()) / len(status) * 100
            }
            
        except Exception as e:
            print(f"✗ Unified registry test failed: {e}")
            self.test_results['unified_registry'] = {'status': 'failed', 'error': str(e)}
            self.failed_tests.append('unified_registry')
    
    def test_csp_iformer_variants(self):
        """測試CSP_IFormer變體"""
        
        print("\n2. Testing CSP_IFormer 2024 Variants")
        print("-" * 40)
        
        # 定義測試變體
        test_variants = [
            ('csp_iformer', 'efficient', 'tiny'),
            ('csp_iformer', 'efficient', 'small'),
            ('csp_iformer', 'mamba', 'tiny'),
            ('csp_iformer', 'mamba', 'small'),
            ('csp_iformer', 'enhanced', 'small'),
            ('csp_iformer', 'enhanced', 'base'),
        ]
        
        variant_results = {}
        
        for family, variant, size in test_variants:
            variant_name = f"{family}_{variant}_{size}"
            print(f"\nTesting {variant_name}:")
            
            try:
                # 測試模型創建
                model = self.factory.create_enhanced_encoder(
                    family=family,
                    variant=variant,
                    size=size,
                    num_classes=5
                )
                
                # 基本信息
                param_count = sum(p.numel() for p in model.parameters()) / 1e6
                print(f"  ✓ Model created - {param_count:.2f}M parameters")
                
                # 測試前向傳播
                model.eval()
                test_input = torch.randn(1, 3, 224, 224)
                
                with torch.no_grad():
                    # 測試分類輸出
                    output = model(test_input)
                    print(f"  ✓ Forward pass - Output shape: {output.shape}")
                    
                    # 測試特徵提取
                    if hasattr(model, 'forward_features'):
                        features = model.forward_features(test_input)
                        if isinstance(features, (list, tuple)):
                            feature_shapes = [f.shape for f in features]
                        else:
                            feature_shapes = [features.shape]
                        print(f"  ✓ Feature extraction - Shapes: {feature_shapes}")
                    else:
                        print(f"  ! No forward_features method")
                
                # 記錄成功結果
                variant_results[variant_name] = {
                    'status': 'success',
                    'parameters_M': param_count,
                    'output_shape': output.shape,
                    'features_available': hasattr(model, 'forward_features')
                }
                
            except Exception as e:
                print(f"  ✗ Failed: {e}")
                variant_results[variant_name] = {'status': 'failed', 'error': str(e)}
                self.failed_tests.append(variant_name)
        
        self.test_results['csp_iformer_variants'] = variant_results
    
    def test_segman_variants(self):
        """測試SegMAN變體"""
        
        print("\n3. Testing SegMAN Variants")
        print("-" * 40)
        
        # 測試SegMAN工廠系統
        try:
            from full_model.segman.segman_factory import SegMANFactory, create_segman_model
            
            segman_factory = SegMANFactory()
            available_models = segman_factory.list_available_models()
            print(f"Available SegMAN models: {available_models}")
            
            # 測試不同變體
            test_variants = [
                ('mambavision', 'small'),
                ('efficient', 'nano'),
                ('enhanced', 'small'),
            ]
            
            segman_results = {}
            
            for variant, size in test_variants:
                variant_name = f"segman_{variant}_{size}"
                print(f"\nTesting {variant_name}:")
                
                try:
                    # 測試編碼器
                    encoder = segman_factory.create_encoder(variant, size, num_classes=5)
                    param_count = sum(p.numel() for p in encoder.parameters()) / 1e6
                    print(f"  ✓ Encoder created - {param_count:.2f}M parameters")
                    
                    # 測試前向傳播
                    test_input = torch.randn(1, 3, 224, 224)
                    with torch.no_grad():
                        output = encoder(test_input)
                        print(f"  ✓ Forward pass - Output shape: {output.shape}")
                    
                    # 測試分割模型
                    seg_model = segman_factory.create_segmentation_model(variant, size, num_classes=5)
                    seg_param_count = sum(p.numel() for p in seg_model.parameters()) / 1e6
                    print(f"  ✓ Segmentation model created - {seg_param_count:.2f}M parameters")
                    
                    segman_results[variant_name] = {
                        'status': 'success',
                        'encoder_parameters_M': param_count,
                        'segmentation_parameters_M': seg_param_count,
                        'output_shape': output.shape
                    }
                    
                except Exception as e:
                    print(f"  ✗ Failed: {e}")
                    segman_results[variant_name] = {'status': 'failed', 'error': str(e)}
                    self.failed_tests.append(variant_name)
            
            self.test_results['segman_variants'] = segman_results
            
        except ImportError as e:
            print(f"✗ SegMAN factory not available: {e}")
            self.test_results['segman_variants'] = {'status': 'failed', 'error': 'Import failed'}
            self.failed_tests.append('segman_variants')
    
    def test_hybrid_architectures(self):
        """測試混合架構"""
        
        print("\n4. Testing Hybrid Architectures")
        print("-" * 40)
        
        hybrid_results = {}
        
        # 測試CSP_IFormer + SegMAN混合
        try:
            if MODEL_REGISTRY.contains('csp_iformer_segman_hybrid'):
                print(f"Testing CSP_IFormer + SegMAN hybrid:")
                
                hybrid_class = MODEL_REGISTRY.get('csp_iformer_segman_hybrid')
                hybrid_model = hybrid_class(
                    encoder_variant='enhanced',
                    encoder_size='small',
                    decoder_variant='enhanced',
                    num_classes=5
                )
                
                param_count = sum(p.numel() for p in hybrid_model.parameters()) / 1e6
                print(f"  ✓ Hybrid model created - {param_count:.2f}M parameters")
                
                # 測試前向傳播
                test_input = torch.randn(1, 3, 224, 224)
                with torch.no_grad():
                    output = hybrid_model(test_input)
                    print(f"  ✓ Forward pass - Output shape: {output.shape}")
                
                hybrid_results['csp_iformer_segman'] = {
                    'status': 'success',
                    'parameters_M': param_count,
                    'output_shape': output.shape
                }
            else:
                print(f"✗ CSP_IFormer + SegMAN hybrid not registered")
                hybrid_results['csp_iformer_segman'] = {'status': 'failed', 'error': 'Not registered'}
                
        except Exception as e:
            print(f"✗ Hybrid architecture test failed: {e}")
            hybrid_results['csp_iformer_segman'] = {'status': 'failed', 'error': str(e)}
            self.failed_tests.append('hybrid_csp_segman')
        
        # 測試Mamba + CSP混合
        try:
            if MODEL_REGISTRY.contains('mamba_csp_hybrid'):
                print(f"\nTesting Mamba + CSP hybrid:")
                
                hybrid_class = MODEL_REGISTRY.get('mamba_csp_hybrid')
                hybrid_model = hybrid_class(num_classes=5)
                
                param_count = sum(p.numel() for p in hybrid_model.parameters()) / 1e6
                print(f"  ✓ Mamba-CSP hybrid created - {param_count:.2f}M parameters")
                
                # 測試前向傳播
                test_input = torch.randn(1, 3, 224, 224)
                with torch.no_grad():
                    output = hybrid_model(test_input)
                    print(f"  ✓ Forward pass - Output shape: {output.shape}")
                
                hybrid_results['mamba_csp'] = {
                    'status': 'success',
                    'parameters_M': param_count,
                    'output_shape': output.shape
                }
            else:
                print(f"✗ Mamba + CSP hybrid not registered")
                hybrid_results['mamba_csp'] = {'status': 'failed', 'error': 'Not registered'}
                
        except Exception as e:
            print(f"✗ Mamba-CSP hybrid test failed: {e}")
            hybrid_results['mamba_csp'] = {'status': 'failed', 'error': str(e)}
            self.failed_tests.append('hybrid_mamba_csp')
        
        self.test_results['hybrid_architectures'] = hybrid_results
    
    def test_enhanced_factory(self):
        """測試增強工廠系統"""
        
        print("\n5. Testing Enhanced Factory System")
        print("-" * 40)
        
        factory_results = {}
        
        # 測試便捷創建函數
        try:
            print(f"Testing convenience function:")
            
            # 創建模型
            model = create_enhanced_model(
                family='csp_iformer',
                variant='enhanced',
                size='small',
                task='classification',
                num_classes=10
            )
            
            param_count = sum(p.numel() for p in model.parameters()) / 1e6
            print(f"  ✓ Model created via convenience function - {param_count:.2f}M parameters")
            
            # 測試前向傳播
            test_input = torch.randn(1, 3, 224, 224)
            with torch.no_grad():
                output = model(test_input)
                print(f"  ✓ Forward pass - Output shape: {output.shape}")
            
            factory_results['convenience_function'] = {
                'status': 'success',
                'parameters_M': param_count,
                'output_shape': output.shape
            }
            
        except Exception as e:
            print(f"  ✗ Convenience function test failed: {e}")
            factory_results['convenience_function'] = {'status': 'failed', 'error': str(e)}
            self.failed_tests.append('enhanced_factory_convenience')
        
        # 測試工廠模型信息獲取
        try:
            print(f"\nTesting model info retrieval:")
            
            info = self.factory.get_model_info('csp_iformer', 'enhanced', 'small')
            print(f"  ✓ Model info retrieved:")
            print(f"    Parameters: {info.get('parameters_M', 'N/A')}")
            print(f"    Output shape: {info.get('output_shape', 'N/A')}")
            
            factory_results['model_info'] = {
                'status': 'success',
                'info': info
            }
            
        except Exception as e:
            print(f"  ✗ Model info test failed: {e}")
            factory_results['model_info'] = {'status': 'failed', 'error': str(e)}
            self.failed_tests.append('enhanced_factory_info')
        
        self.test_results['enhanced_factory'] = factory_results
    
    def run_performance_benchmark(self):
        """運行性能基準測試"""
        
        print("\n6. Performance Benchmark")
        print("-" * 40)
        
        benchmark_models = [
            ('csp_iformer', 'efficient', 'small'),
            ('csp_iformer', 'enhanced', 'small'),
            ('csp_iformer', 'mamba', 'small'),
        ]
        
        benchmark_results = {}
        
        for family, variant, size in benchmark_models:
            model_name = f"{family}_{variant}_{size}"
            print(f"\nBenchmarking {model_name}:")
            
            try:
                # 創建模型
                model = self.factory.create_enhanced_encoder(
                    family=family,
                    variant=variant,
                    size=size,
                    num_classes=1000
                )
                model.eval()
                
                # 準備測試數據
                test_input = torch.randn(1, 3, 224, 224)
                
                # 預熱
                with torch.no_grad():
                    for _ in range(10):
                        _ = model(test_input)
                
                # 測量推理時間
                torch.cuda.synchronize() if torch.cuda.is_available() else None
                start_time = time.time()
                
                with torch.no_grad():
                    for _ in range(100):
                        output = model(test_input)
                
                torch.cuda.synchronize() if torch.cuda.is_available() else None
                end_time = time.time()
                
                avg_time = (end_time - start_time) / 100 * 1000  # ms
                fps = 1000 / avg_time
                
                param_count = sum(p.numel() for p in model.parameters()) / 1e6
                
                print(f"  ✓ Avg inference time: {avg_time:.2f}ms")
                print(f"  ✓ FPS: {fps:.1f}")
                print(f"  ✓ Parameters: {param_count:.2f}M")
                
                benchmark_results[model_name] = {
                    'status': 'success',
                    'avg_time_ms': avg_time,
                    'fps': fps,
                    'parameters_M': param_count,
                    'output_shape': output.shape
                }
                
            except Exception as e:
                print(f"  ✗ Benchmark failed: {e}")
                benchmark_results[model_name] = {'status': 'failed', 'error': str(e)}
                self.failed_tests.append(f'benchmark_{model_name}')
        
        self.test_results['performance_benchmark'] = benchmark_results
    
    def generate_test_report(self):
        """生成測試報告"""
        
        print("\n" + "=" * 60)
        print("COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        # 統計總體結果
        total_tests = 0
        passed_tests = 0
        
        for category, results in self.test_results.items():
            if isinstance(results, dict):
                if 'status' in results:
                    total_tests += 1
                    if results['status'] == 'success':
                        passed_tests += 1
                else:
                    # 嵌套結果
                    for sub_test, sub_result in results.items():
                        if isinstance(sub_result, dict) and 'status' in sub_result:
                            total_tests += 1
                            if sub_result['status'] == 'success':
                                passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\nOverall Results:")
        print(f"  Total Tests: {total_tests}")
        print(f"  Passed: {passed_tests}")
        print(f"  Failed: {total_tests - passed_tests}")
        print(f"  Success Rate: {success_rate:.1f}%")
        
        # 詳細結果
        print(f"\nDetailed Results:")
        print("-" * 40)
        
        for category, results in self.test_results.items():
            print(f"\n{category.upper()}:")
            
            if isinstance(results, dict) and 'status' in results:
                status_icon = "✓" if results['status'] == 'success' else "✗"
                print(f"  {status_icon} {results['status']}")
                if 'error' in results:
                    print(f"    Error: {results['error']}")
            else:
                for sub_test, sub_result in results.items():
                    if isinstance(sub_result, dict) and 'status' in sub_result:
                        status_icon = "✓" if sub_result['status'] == 'success' else "✗"
                        print(f"  {status_icon} {sub_test}: {sub_result['status']}")
                        
                        if sub_result['status'] == 'success':
                            if 'parameters_M' in sub_result:
                                print(f"    Parameters: {sub_result['parameters_M']:.2f}M")
                            if 'fps' in sub_result:
                                print(f"    FPS: {sub_result['fps']:.1f}")
                        else:
                            if 'error' in sub_result:
                                print(f"    Error: {sub_result['error']}")
        
        # 失敗測試摘要
        if self.failed_tests:
            print(f"\nFailed Tests:")
            for failed_test in self.failed_tests:
                print(f"  ✗ {failed_test}")
        
        # 性能摘要
        if 'performance_benchmark' in self.test_results:
            print(f"\nPerformance Summary:")
            print("-" * 40)
            
            for model_name, result in self.test_results['performance_benchmark'].items():
                if result['status'] == 'success':
                    print(f"{model_name}:")
                    print(f"  Parameters: {result['parameters_M']:.2f}M")
                    print(f"  FPS: {result['fps']:.1f}")
                    print(f"  Inference: {result['avg_time_ms']:.2f}ms")
        
        print(f"\n" + "=" * 60)
        print("TEST COMPLETED")
        print("=" * 60)


def main():
    """主測試函數"""
    
    print("Unified Architecture Comprehensive Test Suite")
    print("=" * 60)
    
    # 創建測試器
    tester = ArchitectureTester()
    
    # 運行綜合測試
    tester.run_comprehensive_test()
    
    return tester.test_results


if __name__ == "__main__":
    # 運行測試
    try:
        test_results = main()
        
        # 可選：保存測試結果
        import json
        with open('architecture_test_results.json', 'w') as f:
            # 轉換torch.Size為字符串以便JSON序列化
            def convert_for_json(obj):
                if hasattr(obj, 'tolist'):  # tensor
                    return obj.tolist()
                elif isinstance(obj, torch.Size):
                    return list(obj)
                elif isinstance(obj, torch.dtype):
                    return str(obj)
                return obj
            
            def clean_for_json(data):
                if isinstance(data, dict):
                    return {k: clean_for_json(v) for k, v in data.items()}
                elif isinstance(data, (list, tuple)):
                    return [clean_for_json(item) for item in data]
                else:
                    return convert_for_json(data)
            
            clean_results = clean_for_json(test_results)
            json.dump(clean_results, f, indent=2)
            
        print(f"\nTest results saved to architecture_test_results.json")
        
    except Exception as e:
        print(f"Test execution failed: {e}")
        traceback.print_exc()