#!/usr/bin/env python3
"""
SAM模型微調訓練系統
支持Segment Anything Model的完整微調和LoRA訓練
"""

import os
import sys
import logging
import warnings
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
from dataclasses import dataclass
import yaml
import json
from datetime import datetime

# 添加SAM路徑（如果需要）
sam_paths = [
    r'D:\code\segment-anything-main',
    r'/opt/segment-anything',
    r'./segment-anything'
]

for path in sam_paths:
    if os.path.exists(path):
        sys.path.append(path)
        break

try:
    from segment_anything import sam_model_registry, SamPredictor
    from segment_anything.modeling import Sam, ImageEncoderViT, MaskDecoder, PromptEncoder
    SAM_AVAILABLE = True
except ImportError:
    SAM_AVAILABLE = False
    warnings.warn("SAM module not found. Please install segment-anything or set correct path.")

try:
    import peft
    from peft import LoraConfig, get_peft_model, TaskType
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False

from .base_trainer import BaseTrainer
from ..core.config_manager import ConfigManager
from ..util.losses import DiceLoss, IoULoss, FocalLoss


@dataclass
class SAMTrainingConfig:
    """SAM訓練配置"""
    
    # 模型配置
    model_type: str = "vit_h"  # vit_h, vit_l, vit_b
    sam_checkpoint: str = "sam_vit_h_4b8939.pth"
    freeze_image_encoder: bool = False
    freeze_prompt_encoder: bool = True
    freeze_mask_decoder: bool = False
    
    # LoRA配置
    use_lora: bool = True
    lora_rank: int = 4
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    lora_target_modules: List[str] = None
    
    # 訓練配置
    num_epochs: int = 100
    batch_size: int = 4
    learning_rate: float = 1e-4
    weight_decay: float = 0.01
    warmup_epochs: int = 5
    
    # 損失配置
    loss_weights: Dict[str, float] = None
    use_focal_loss: bool = True
    use_dice_loss: bool = True
    use_iou_loss: bool = True
    
    # 數據配置
    image_size: int = 1024
    prompt_type: str = "box"  # point, box, mask, auto
    num_points: int = 5
    num_classes: int = 5
    
    # 優化配置
    optimizer: str = "adamw"
    scheduler: str = "cosine"
    gradient_clip: float = 1.0
    accumulation_steps: int = 1
    
    # 保存配置
    save_every: int = 10
    val_every: int = 5
    early_stopping_patience: int = 20
    
    def __post_init__(self):
        if self.loss_weights is None:
            self.loss_weights = {
                "focal": 2.0,
                "dice": 1.0,
                "iou": 1.0
            }
        
        if self.lora_target_modules is None:
            # SAM ViT的典型LoRA目標模組
            self.lora_target_modules = [
                "qkv", "proj", "fc1", "fc2"
            ]


class SAMPromptDataset(Dataset):
    """SAM提示資料集"""
    
    def __init__(self, 
                 images_dir: str,
                 masks_dir: str,
                 annotations_dir: str = None,
                 image_size: int = 1024,
                 prompt_type: str = "box",
                 num_points: int = 5,
                 transform=None):
        
        self.images_dir = Path(images_dir)
        self.masks_dir = Path(masks_dir)
        self.annotations_dir = Path(annotations_dir) if annotations_dir else None
        self.image_size = image_size
        self.prompt_type = prompt_type
        self.num_points = num_points
        self.transform = transform
        
        # 獲取所有圖像文件
        self.image_files = list(self.images_dir.glob("*.jpg")) + list(self.images_dir.glob("*.png"))
        self.image_files.sort()
        
        # 驗證對應的遮罩文件
        valid_files = []
        for img_file in self.image_files:
            mask_file = self.masks_dir / f"{img_file.stem}.png"
            if mask_file.exists():
                valid_files.append(img_file)
        
        self.image_files = valid_files
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"載入 {len(self.image_files)} 個有效的圖像-遮罩對")
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        # 載入圖像
        img_path = self.image_files[idx]
        image = cv2.imread(str(img_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 載入遮罩
        mask_path = self.masks_dir / f"{img_path.stem}.png"
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        
        # 調整大小
        image = cv2.resize(image, (self.image_size, self.image_size))
        mask = cv2.resize(mask, (self.image_size, self.image_size))
        
        # 生成提示
        prompts = self._generate_prompts(mask)
        
        # 轉換
        if self.transform:
            transformed = self.transform(image=image, mask=mask)
            image = transformed['image']
            mask = transformed['mask']
        
        # 轉換為tensor
        if isinstance(image, np.ndarray):
            image = torch.from_numpy(image.transpose(2, 0, 1)).float() / 255.0
        if isinstance(mask, np.ndarray):
            mask = torch.from_numpy(mask).long()
        
        return {
            'image': image,
            'mask': mask,
            'prompts': prompts,
            'image_path': str(img_path)
        }
    
    def _generate_prompts(self, mask):
        """根據遮罩生成提示"""
        prompts = {}
        
        if self.prompt_type in ["point", "auto"]:
            # 生成點提示
            points = self._generate_point_prompts(mask)
            prompts['points'] = points
        
        if self.prompt_type in ["box", "auto"]:
            # 生成邊界框提示
            boxes = self._generate_box_prompts(mask)
            prompts['boxes'] = boxes
        
        return prompts
    
    def _generate_point_prompts(self, mask):
        """生成點提示"""
        points = []
        labels = []
        
        # 找到所有非零像素（前景）
        y_coords, x_coords = np.where(mask > 0)
        
        if len(y_coords) > 0:
            # 隨機選擇前景點
            num_fg_points = min(self.num_points // 2, len(y_coords))
            if num_fg_points > 0:
                indices = np.random.choice(len(y_coords), num_fg_points, replace=False)
                for idx in indices:
                    points.append([x_coords[idx], y_coords[idx]])
                    labels.append(1)  # 前景標籤
        
        # 添加背景點
        y_coords, x_coords = np.where(mask == 0)
        if len(y_coords) > 0:
            num_bg_points = self.num_points - len(points)
            num_bg_points = min(num_bg_points, len(y_coords))
            if num_bg_points > 0:
                indices = np.random.choice(len(y_coords), num_bg_points, replace=False)
                for idx in indices:
                    points.append([x_coords[idx], y_coords[idx]])
                    labels.append(0)  # 背景標籤
        
        return {
            'coordinates': np.array(points) if points else np.array([]).reshape(0, 2),
            'labels': np.array(labels) if labels else np.array([])
        }
    
    def _generate_box_prompts(self, mask):
        """生成邊界框提示"""
        # 找到所有連通組件
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        boxes = []
        for contour in contours:
            if cv2.contourArea(contour) > 100:  # 過濾小區域
                x, y, w, h = cv2.boundingRect(contour)
                boxes.append([x, y, x + w, y + h])
        
        return np.array(boxes) if boxes else np.array([]).reshape(0, 4)


class SAMFineTuningTrainer(BaseTrainer):
    """SAM微調訓練器"""
    
    def __init__(self, config: SAMTrainingConfig, save_dir: str = "./sam_training_results"):
        super().__init__()
        
        if not SAM_AVAILABLE:
            raise ImportError("SAM not available. Please install segment-anything.")
        
        self.config = config
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 初始化模型
        self.model = self._load_sam_model()
        if config.use_lora and PEFT_AVAILABLE:
            self.model = self._setup_lora()
        
        self._setup_training()
    
    def _load_sam_model(self) -> Sam:
        """載入SAM模型"""
        # 檢查checkpoint路徑
        checkpoint_path = self.config.sam_checkpoint
        if not os.path.exists(checkpoint_path):
            # 嘗試一些常見路徑
            possible_paths = [
                f"./checkpoints/{checkpoint_path}",
                f"/opt/sam_checkpoints/{checkpoint_path}",
                f"../checkpoints/{checkpoint_path}"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    checkpoint_path = path
                    break
            else:
                self.logger.warning(f"SAM checkpoint not found: {checkpoint_path}")
                self.logger.info("Will download from Facebook Research...")
                # 這裡可以添加自動下載邏輯
        
        # 載入模型
        model = sam_model_registry[self.config.model_type](checkpoint=checkpoint_path)
        model = model.to(self.device)
        
        # 凍結組件
        if self.config.freeze_image_encoder:
            for param in model.image_encoder.parameters():
                param.requires_grad = False
                
        if self.config.freeze_prompt_encoder:
            for param in model.prompt_encoder.parameters():
                param.requires_grad = False
                
        if self.config.freeze_mask_decoder:
            for param in model.mask_decoder.parameters():
                param.requires_grad = False
        
        self.logger.info(f"載入SAM模型: {self.config.model_type}")
        return model
    
    def _setup_lora(self) -> nn.Module:
        """設置LoRA"""
        if not PEFT_AVAILABLE:
            self.logger.warning("PEFT not available. Using full fine-tuning.")
            return self.model
        
        # LoRA配置
        lora_config = LoraConfig(
            r=self.config.lora_rank,
            lora_alpha=self.config.lora_alpha,
            target_modules=self.config.lora_target_modules,
            lora_dropout=self.config.lora_dropout,
            bias="none",
            task_type=TaskType.FEATURE_EXTRACTION
        )
        
        # 應用LoRA到圖像編碼器
        if not self.config.freeze_image_encoder:
            self.model.image_encoder = get_peft_model(self.model.image_encoder, lora_config)
        
        self.logger.info(f"應用LoRA配置: rank={self.config.lora_rank}, alpha={self.config.lora_alpha}")
        return self.model
    
    def _setup_training(self):
        """設置訓練組件"""
        # 損失函數
        self.criterion = SAMMultiLoss(self.config)
        
        # 優化器
        params_to_optimize = [p for p in self.model.parameters() if p.requires_grad]
        
        if self.config.optimizer == "adamw":
            self.optimizer = optim.AdamW(
                params_to_optimize,
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        elif self.config.optimizer == "adam":
            self.optimizer = optim.Adam(
                params_to_optimize,
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        else:
            raise ValueError(f"Unsupported optimizer: {self.config.optimizer}")
        
        # 學習率調度器
        if self.config.scheduler == "cosine":
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=self.config.num_epochs
            )
        elif self.config.scheduler == "step":
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        else:
            self.scheduler = None
        
        # 記錄訓練參數
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        self.logger.info(f"總參數: {total_params:,}")
        self.logger.info(f"可訓練參數: {trainable_params:,} ({100*trainable_params/total_params:.2f}%)")
    
    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """訓練一個epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_samples = 0
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            masks = batch['mask'].to(self.device)
            prompts = batch['prompts']
            
            # 前向傳播
            self.optimizer.zero_grad()
            
            # SAM推理
            with torch.no_grad():
                image_embeddings = self.model.image_encoder(images)
            
            # 處理提示
            sparse_embeddings, dense_embeddings = self._process_prompts(prompts, images.shape[-2:])
            
            # 遮罩解碼
            low_res_masks, iou_predictions = self.model.mask_decoder(
                image_embeddings=image_embeddings,
                image_pe=self.model.prompt_encoder.get_dense_pe(),
                sparse_prompt_embeddings=sparse_embeddings,
                dense_prompt_embeddings=dense_embeddings,
                multimask_output=False
            )
            
            # 上採樣遮罩
            masks_pred = F.interpolate(
                low_res_masks,
                size=(images.shape[-2], images.shape[-1]),
                mode="bilinear",
                align_corners=False
            )
            
            # 計算損失
            loss = self.criterion(masks_pred, masks, iou_predictions)
            
            # 反向傳播
            loss.backward()
            
            # 梯度裁剪
            if self.config.gradient_clip > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip)
            
            # 優化器步驟
            if (batch_idx + 1) % self.config.accumulation_steps == 0:
                self.optimizer.step()
                self.optimizer.zero_grad()
            
            total_loss += loss.item()
            total_samples += images.size(0)
            
            if batch_idx % 10 == 0:
                self.logger.info(
                    f"Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, "
                    f"Loss: {loss.item():.4f}, Avg Loss: {total_loss/(batch_idx+1):.4f}"
                )
        
        avg_loss = total_loss / len(train_loader)
        return {'loss': avg_loss}
    
    def _process_prompts(self, prompts: Dict, image_size: Tuple[int, int]):
        """處理提示，生成嵌入"""
        batch_size = len(prompts)
        
        # 初始化提示
        points = None
        labels = None
        boxes = None
        
        # 處理點提示
        if 'points' in prompts[0]:
            all_points = []
            all_labels = []
            max_points = 0
            
            for prompt in prompts:
                point_data = prompt['points']
                points_coords = point_data['coordinates']
                points_labels = point_data['labels']
                
                if len(points_coords) > max_points:
                    max_points = len(points_coords)
                
                all_points.append(points_coords)
                all_labels.append(points_labels)
            
            # 填充到相同長度
            if max_points > 0:
                padded_points = np.zeros((batch_size, max_points, 2))
                padded_labels = np.zeros((batch_size, max_points))
                
                for i, (pts, lbls) in enumerate(zip(all_points, all_labels)):
                    if len(pts) > 0:
                        padded_points[i, :len(pts)] = pts
                        padded_labels[i, :len(lbls)] = lbls
                
                points = torch.from_numpy(padded_points).float().to(self.device)
                labels = torch.from_numpy(padded_labels).int().to(self.device)
        
        # 處理邊界框提示
        if 'boxes' in prompts[0]:
            all_boxes = []
            for prompt in prompts:
                box_data = prompt['boxes']
                if len(box_data) > 0:
                    all_boxes.append(box_data[0])  # 只取第一個框
                else:
                    all_boxes.append([0, 0, image_size[1], image_size[0]])  # 預設整個圖像
            
            boxes = torch.from_numpy(np.array(all_boxes)).float().to(self.device)
        
        # 生成嵌入
        sparse_embeddings, dense_embeddings = self.model.prompt_encoder(
            points=(points, labels) if points is not None else None,
            boxes=boxes,
            masks=None
        )
        
        return sparse_embeddings, dense_embeddings
    
    def validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """驗證"""
        self.model.eval()
        
        total_loss = 0.0
        total_iou = 0.0
        total_samples = 0
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                masks = batch['mask'].to(self.device)
                prompts = batch['prompts']
                
                # SAM推理
                image_embeddings = self.model.image_encoder(images)
                sparse_embeddings, dense_embeddings = self._process_prompts(prompts, images.shape[-2:])
                
                low_res_masks, iou_predictions = self.model.mask_decoder(
                    image_embeddings=image_embeddings,
                    image_pe=self.model.prompt_encoder.get_dense_pe(),
                    sparse_prompt_embeddings=sparse_embeddings,
                    dense_prompt_embeddings=dense_embeddings,
                    multimask_output=False
                )
                
                masks_pred = F.interpolate(
                    low_res_masks,
                    size=(images.shape[-2], images.shape[-1]),
                    mode="bilinear",
                    align_corners=False
                )
                
                # 計算損失和指標
                loss = self.criterion(masks_pred, masks, iou_predictions)
                
                # 計算IoU
                masks_pred_binary = (torch.sigmoid(masks_pred) > 0.5).float()
                iou = self._calculate_iou(masks_pred_binary, masks.float())
                
                total_loss += loss.item()
                total_iou += iou.item()
                total_samples += images.size(0)
        
        avg_loss = total_loss / len(val_loader)
        avg_iou = total_iou / len(val_loader)
        
        return {'val_loss': avg_loss, 'val_iou': avg_iou}
    
    def _calculate_iou(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """計算IoU"""
        intersection = (pred * target).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) - intersection
        iou = (intersection + 1e-7) / (union + 1e-7)
        return iou.mean()
    
    def fit(self, 
            train_dataset: Dataset,
            val_dataset: Dataset = None,
            train_transforms=None,
            val_transforms=None) -> Dict[str, Any]:
        """完整訓練流程"""
        
        # 創建資料載入器
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )
        
        val_loader = None
        if val_dataset:
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=4,
                pin_memory=True
            )
        
        # 訓練歷史
        history = {
            'train_loss': [],
            'val_loss': [],
            'val_iou': [],
            'learning_rate': []
        }
        
        best_val_iou = 0.0
        patience_counter = 0
        
        self.logger.info(f"開始SAM微調訓練，共 {self.config.num_epochs} epochs")
        
        for epoch in range(1, self.config.num_epochs + 1):
            # 訓練
            train_metrics = self.train_epoch(train_loader, epoch)
            history['train_loss'].append(train_metrics['loss'])
            
            # 驗證
            if val_loader and epoch % self.config.val_every == 0:
                val_metrics = self.validate(val_loader)
                history['val_loss'].append(val_metrics['val_loss'])
                history['val_iou'].append(val_metrics['val_iou'])
                
                self.logger.info(
                    f"Epoch {epoch}: Train Loss: {train_metrics['loss']:.4f}, "
                    f"Val Loss: {val_metrics['val_loss']:.4f}, "
                    f"Val IoU: {val_metrics['val_iou']:.4f}"
                )
                
                # 早停機制
                if val_metrics['val_iou'] > best_val_iou:
                    best_val_iou = val_metrics['val_iou']
                    patience_counter = 0
                    
                    # 保存最佳模型
                    self.save_model("best_model.pth", epoch, val_metrics)
                else:
                    patience_counter += 1
                    
                    if patience_counter >= self.config.early_stopping_patience:
                        self.logger.info(f"Early stopping at epoch {epoch}")
                        break
            
            # 學習率調度
            if self.scheduler:
                history['learning_rate'].append(self.scheduler.get_last_lr()[0])
                self.scheduler.step()
            
            # 定期保存
            if epoch % self.config.save_every == 0:
                self.save_model(f"checkpoint_epoch_{epoch}.pth", epoch, train_metrics)
        
        # 保存最終模型
        self.save_model("final_model.pth", epoch, train_metrics)
        
        # 保存訓練歷史
        self.save_training_history(history)
        
        return history
    
    def save_model(self, filename: str, epoch: int, metrics: Dict[str, float]):
        """保存模型"""
        save_path = self.save_dir / filename
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config.__dict__,
            'metrics': metrics
        }
        
        if self.scheduler:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        torch.save(checkpoint, save_path)
        self.logger.info(f"模型已保存: {save_path}")
    
    def save_training_history(self, history: Dict[str, List]):
        """保存訓練歷史"""
        history_path = self.save_dir / "training_history.json"
        
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"訓練歷史已保存: {history_path}")


class SAMMultiLoss(nn.Module):
    """SAM多重損失函數"""
    
    def __init__(self, config: SAMTrainingConfig):
        super().__init__()
        self.config = config
        
        # 初始化損失函數
        self.focal_loss = FocalLoss() if config.use_focal_loss else None
        self.dice_loss = DiceLoss() if config.use_dice_loss else None
        self.iou_loss = IoULoss() if config.use_iou_loss else None
        self.bce_loss = nn.BCEWithLogitsLoss()
        
    def forward(self, 
                pred_masks: torch.Tensor, 
                target_masks: torch.Tensor,
                iou_predictions: torch.Tensor = None) -> torch.Tensor:
        """計算總損失"""
        
        total_loss = 0.0
        
        # 主要遮罩損失
        if self.focal_loss:
            focal_loss = self.focal_loss(pred_masks, target_masks)
            total_loss += self.config.loss_weights["focal"] * focal_loss
        
        if self.dice_loss:
            dice_loss = self.dice_loss(pred_masks, target_masks)
            total_loss += self.config.loss_weights["dice"] * dice_loss
        
        if self.iou_loss:
            iou_loss = self.iou_loss(pred_masks, target_masks)
            total_loss += self.config.loss_weights["iou"] * iou_loss
        
        # BCE損失作為基礎
        bce_loss = self.bce_loss(pred_masks.squeeze(1), target_masks.float())
        total_loss += 0.5 * bce_loss
        
        # IoU預測損失（如果提供）
        if iou_predictions is not None:
            # 計算真實IoU
            pred_masks_sigmoid = torch.sigmoid(pred_masks)
            true_iou = self._calculate_iou(pred_masks_sigmoid, target_masks.float())
            iou_pred_loss = F.mse_loss(iou_predictions.squeeze(), true_iou)
            total_loss += 0.1 * iou_pred_loss
        
        return total_loss
    
    def _calculate_iou(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """計算IoU"""
        intersection = (pred * target).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) - intersection
        iou = (intersection + 1e-7) / (union + 1e-7)
        return iou.squeeze()


def create_sam_finetuning_trainer(config: Union[SAMTrainingConfig, Dict, str], 
                                 save_dir: str = "./sam_training_results") -> SAMFineTuningTrainer:
    """創建SAM微調訓練器"""
    
    if isinstance(config, str):
        # 從文件載入配置
        with open(config, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        config = SAMTrainingConfig(**config_dict)
    elif isinstance(config, dict):
        config = SAMTrainingConfig(**config)
    
    return SAMFineTuningTrainer(config, save_dir)


def create_sam_dataset(images_dir: str,
                      masks_dir: str,
                      annotations_dir: str = None,
                      **kwargs) -> SAMPromptDataset:
    """創建SAM資料集"""
    return SAMPromptDataset(
        images_dir=images_dir,
        masks_dir=masks_dir,
        annotations_dir=annotations_dir,
        **kwargs
    )


# 使用範例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="SAM微調訓練")
    parser.add_argument("--config", help="配置文件路徑")
    parser.add_argument("--train_images", required=True, help="訓練圖像目錄")
    parser.add_argument("--train_masks", required=True, help="訓練遮罩目錄")
    parser.add_argument("--val_images", help="驗證圖像目錄")
    parser.add_argument("--val_masks", help="驗證遮罩目錄")
    parser.add_argument("--save_dir", default="./sam_training_results", help="保存目錄")
    
    args = parser.parse_args()
    
    # 配置
    config = SAMTrainingConfig(
        num_epochs=50,
        batch_size=2,
        learning_rate=1e-4,
        use_lora=True,
        lora_rank=4
    )
    
    if args.config:
        with open(args.config, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        config = SAMTrainingConfig(**config_dict)
    
    # 創建資料集
    train_dataset = create_sam_dataset(
        images_dir=args.train_images,
        masks_dir=args.train_masks,
        image_size=config.image_size,
        prompt_type=config.prompt_type
    )
    
    val_dataset = None
    if args.val_images and args.val_masks:
        val_dataset = create_sam_dataset(
            images_dir=args.val_images,
            masks_dir=args.val_masks,
            image_size=config.image_size,
            prompt_type=config.prompt_type
        )
    
    # 創建訓練器
    trainer = create_sam_finetuning_trainer(config, args.save_dir)
    
    # 開始訓練
    history = trainer.fit(train_dataset, val_dataset)
    
    print(f"訓練完成！結果保存在: {args.save_dir}")