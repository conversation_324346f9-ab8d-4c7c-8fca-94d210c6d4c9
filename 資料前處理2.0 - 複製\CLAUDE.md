# CLAUDE.md

此文件為 Claude Code (claude.ai/code) 在此代碼庫中工作時提供指導。

## 項目概述

這是一個圖像數據集預處理工具集，專為計算機視覺任務設計，特別針對物件檢測和圖像分割。工具集包含5個主要組件，可以獨立使用或作為整合管道：

1. **標籤格式轉換器** (`annotation_converter.py`) - 將各種標籤格式(VOC、YOLO、COCO)轉換為統一的LabelMe格式
2. **標籤編輯器** (`annotation_editor.py`) - 進行標籤編輯操作(合併、重命名、刪除)
3. **數據集分割器** (`dataset_divider.py`) - 將數據集分為訓練/驗證/測試集並支持類別平衡
4. **圖像增強器** (`img_augmenter.py`) - 通過區域融合方式生成增強數據
5. **全景圖像擴增器** (`panorama_augmenter.py`) - 針對全景圖像的專業擴增工具，支持外方位旋轉、視角變換等方法

## 架構設計

主入口點是 `img_dataset_pipeline.py`，它按順序編排所有4個組件。每個組件都設計為獨立的類，可以單獨導入和使用：

```python
from annotation_converter import AnnotationConverter
from annotation_editor import AnnotationEditor  
from dataset_divider import DatasetDivider
from img_augmenter import ImageAugmenter
from panorama_augmenter import PanoramaAugmenter
```

所有組件都支持命令行和程序化使用，並通過JSON配置文件和命令行參數提供豐富的配置選項。

## 常用命令

### 環境設置
```bash
pip install -r requirements.txt
```

### 運行完整流程
```bash
# 交互式模式執行所有步驟
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --all --interactive --resize 1

# 使用配置文件
python img_dataset_pipeline.py --config config.json --output 輸出目錄

# 只執行特定步驟
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --steps 1,3
```

### 運行獨立組件
```bash
# 轉換標籤格式
python annotation_converter.py --inputs 資料目錄1 資料目錄2 --output 輸出目錄 --resize 0.5 --quality 80

# 交互式編輯標籤
python annotation_editor.py --input 輸入目錄 --output 輸出目錄 --interactive

# 分割數據集
python dataset_divider.py --input 輸入目錄 --output 輸出目錄 --train-ratio 0.8 --val-ratio 0.1 --test-ratio 0.1

# 圖像增強
python img_augmenter.py --sources 來源目錄 --target 目標目錄 --output 輸出目錄 --num 200 --interactive

# 全景圖像擴增（單張處理）
python panorama_augmenter.py --input 全景圖像.jpg --label 標籤.json --output 輸出目錄 --omega -74.77 --phi -86.70 --kappa -165.42 --methods rotation perspective

# 全景圖像擴增（批量處理）
python panorama_augmenter.py --input 輸入目錄 --output 輸出目錄 --excel 外方位.xlsx --methods rotation perspective tilt --batch

# 全景圖像擴增（交互式）
python panorama_augmenter.py --output 輸出目錄 --interactive
```

## 配置說明

工具集使用JSON配置文件(參見 `config.json` 範例)來指定所有流程步驟的參數。強烈建議初次使用者使用交互式模式來理解參數效果。

## 關鍵設計模式

- 所有組件遵循相似模式：初始化 → 配置 → 處理 → 輸出
- 全面的日誌記錄和錯誤處理
- 多線程支持以提升性能
- 帶可視化的交互式模式用於參數調整
- 模塊化設計允許靈活組合使用組件
- 使用LabelMe格式作為統一的標籤標準
- 針對全景圖像的專業擴增方法，支持外方位參數和Excel批量輸入
- 基於攝影測量標準OPK旋轉矩陣的精確座標轉換