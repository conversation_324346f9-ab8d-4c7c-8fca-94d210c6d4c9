"""
Feature Pyramid Network (FPN) 實現

從 model.py 提取並重構的 FPN 架構，支援多尺度特徵融合。
包含 ResNet 骨幹網路、FPN 頸部網路和相關工具層。
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple, Optional, Dict, Any
import logging

# 設定日誌
logger = logging.getLogger(__name__)


class SamePad2d(nn.Module):
    """模擬 TensorFlow 的 'SAME' 填充"""
    
    def __init__(self, kernel_size: int, stride: int):
        super(SamePad2d, self).__init__()
        self.kernel_size = torch.nn.modules.utils._pair(kernel_size)
        self.stride = torch.nn.modules.utils._pair(stride)

    def forward(self, input: torch.Tensor) -> torch.Tensor:
        in_width = input.size()[2]
        in_height = input.size()[3]
        out_width = math.ceil(float(in_width) / float(self.stride[0]))
        out_height = math.ceil(float(in_height) / float(self.stride[1]))
        pad_along_width = ((out_width - 1) * self.stride[0] +
                           self.kernel_size[0] - in_width)
        pad_along_height = ((out_height - 1) * self.stride[1] +
                            self.kernel_size[1] - in_height)
        pad_left = math.floor(pad_along_width / 2)
        pad_top = math.floor(pad_along_height / 2)
        pad_right = pad_along_width - pad_left
        pad_bottom = pad_along_height - pad_top
        return F.pad(input, (pad_left, pad_right, pad_top, pad_bottom), 'constant', 0)

    def __repr__(self):
        return self.__class__.__name__


class TopDownLayer(nn.Module):
    """FPN 自上而下融合層"""
    
    def __init__(self, in_channels: int, out_channels: int):
        super(TopDownLayer, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=1)
        self.padding2 = SamePad2d(kernel_size=3, stride=1)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1)

    def forward(self, x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 當前層特徵
            y: 上一層特徵（需要上採樣）
        """
        y = F.interpolate(y, scale_factor=2, mode='nearest')
        x = self.conv1(x)
        return self.conv2(self.padding2(x + y))


class Bottleneck(nn.Module):
    """ResNet Bottleneck 塊"""
    expansion = 4

    def __init__(self, inplanes: int, planes: int, stride: int = 1, 
                 downsample: Optional[nn.Module] = None):
        super(Bottleneck, self).__init__()
        self.conv1 = nn.Conv2d(inplanes, planes, kernel_size=1, stride=stride)
        self.bn1 = nn.BatchNorm2d(planes, eps=0.001, momentum=0.01)
        self.padding2 = SamePad2d(kernel_size=3, stride=1)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3)
        self.bn2 = nn.BatchNorm2d(planes, eps=0.001, momentum=0.01)
        self.conv3 = nn.Conv2d(planes, planes * 4, kernel_size=1)
        self.bn3 = nn.BatchNorm2d(planes * 4, eps=0.001, momentum=0.01)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.padding2(out)
        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)

        return out


class ResNetBackbone(nn.Module):
    """ResNet 骨幹網路"""

    def __init__(self, architecture: str = "resnet50", stage5: bool = False):
        super(ResNetBackbone, self).__init__()
        assert architecture in ["resnet50", "resnet101"], f"Unsupported architecture: {architecture}"
        
        self.inplanes = 64
        self.layers = [3, 4, {"resnet50": 6, "resnet101": 23}[architecture], 3]
        self.block = Bottleneck
        self.stage5 = stage5
        self.architecture = architecture

        # C1: 輸入處理層
        self.C1 = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm2d(64, eps=0.001, momentum=0.01),
            nn.ReLU(inplace=True),
            SamePad2d(kernel_size=3, stride=2),
            nn.MaxPool2d(kernel_size=3, stride=2),
        )
        
        # C2-C5: ResNet 階段
        self.C2 = self._make_layer(self.block, 64, self.layers[0])
        self.C3 = self._make_layer(self.block, 128, self.layers[1], stride=2)
        self.C4 = self._make_layer(self.block, 256, self.layers[2], stride=2)
        
        if self.stage5:
            self.C5 = self._make_layer(self.block, 512, self.layers[3], stride=2)
        else:
            self.C5 = None

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        """前向傳播，返回多個尺度的特徵"""
        c1 = self.C1(x)
        c2 = self.C2(c1)
        c3 = self.C3(c2)
        c4 = self.C4(c3)
        
        if self.stage5 and self.C5 is not None:
            c5 = self.C5(c4)
            return c1, c2, c3, c4, c5
        else:
            return c1, c2, c3, c4

    def get_stages(self) -> List[nn.Module]:
        """獲取所有階段的模組"""
        stages = [self.C1, self.C2, self.C3, self.C4]
        if self.stage5 and self.C5 is not None:
            stages.append(self.C5)
        return stages

    def _make_layer(self, block: nn.Module, planes: int, blocks: int, 
                   stride: int = 1) -> nn.Sequential:
        """構建 ResNet 層"""
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv2d(self.inplanes, planes * block.expansion,
                          kernel_size=1, stride=stride),
                nn.BatchNorm2d(planes * block.expansion, eps=0.001, momentum=0.01),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)


class FeaturePyramidNetwork(nn.Module):
    """特徵金字塔網路 (FPN)"""
    
    def __init__(self, 
                 backbone: Optional[ResNetBackbone] = None,
                 in_channels_list: List[int] = [256, 512, 1024, 2048],
                 out_channels: int = 256,
                 num_outs: int = 5,
                 start_level: int = 0,
                 end_level: int = -1,
                 add_extra_convs: str = 'on_output',
                 relu_before_extra_convs: bool = False):
        """
        Args:
            backbone: ResNet 骨幹網路
            in_channels_list: 輸入特徵的通道數列表
            out_channels: 輸出特徵的通道數
            num_outs: 輸出特徵層數
            start_level: 開始層級
            end_level: 結束層級
            add_extra_convs: 額外卷積添加方式
            relu_before_extra_convs: 額外卷積前是否使用ReLU
        """
        super(FeaturePyramidNetwork, self).__init__()
        
        self.backbone = backbone
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        self.num_ins = len(in_channels_list)
        self.num_outs = num_outs
        self.start_level = start_level
        self.end_level = end_level if end_level != -1 else self.num_ins - 1
        self.add_extra_convs = add_extra_convs
        self.relu_before_extra_convs = relu_before_extra_convs
        
        # 橫向連接（1x1 卷積）
        self.lateral_convs = nn.ModuleList()
        for i in range(self.start_level, self.end_level + 1):
            lateral_conv = nn.Conv2d(
                in_channels_list[i], out_channels, kernel_size=1)
            self.lateral_convs.append(lateral_conv)
        
        # FPN 卷積（3x3 卷積）
        self.fpn_convs = nn.ModuleList()
        for i in range(self.start_level, self.end_level + 1):
            fpn_conv = nn.Sequential(
                SamePad2d(kernel_size=3, stride=1),
                nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1)
            )
            self.fpn_convs.append(fpn_conv)
        
        # 額外層（如 P6）
        extra_levels = num_outs - self.end_level + self.start_level - 1
        if self.add_extra_convs and extra_levels >= 1:
            self.extra_convs = nn.ModuleList()
            for i in range(extra_levels):
                if i == 0 and self.add_extra_convs == 'on_input':
                    in_channels = in_channels_list[self.end_level]
                else:
                    in_channels = out_channels
                    
                extra_conv = nn.Conv2d(
                    in_channels, out_channels, kernel_size=3,
                    stride=2, padding=1)
                self.extra_convs.append(extra_conv)

    def forward(self, inputs: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Args:
            inputs: 多尺度輸入特徵列表
            
        Returns:
            多尺度輸出特徵列表
        """
        assert len(inputs) == len(self.in_channels_list)
        
        # 構建橫向連接
        laterals = []
        for i, lateral_conv in enumerate(self.lateral_convs):
            laterals.append(lateral_conv(inputs[i + self.start_level]))
        
        # 自上而下路徑
        for i in range(len(laterals) - 1, 0, -1):
            # 上採樣
            prev_shape = laterals[i - 1].shape[2:]
            laterals[i - 1] = laterals[i - 1] + F.interpolate(
                laterals[i], size=prev_shape, mode='nearest')
        
        # 構建輸出
        outs = []
        for i, fpn_conv in enumerate(self.fpn_convs):
            outs.append(fpn_conv(laterals[i]))
        
        # 額外層
        if self.num_outs > len(outs):
            if not hasattr(self, 'extra_convs'):
                for i in range(self.num_outs - len(outs)):
                    outs.append(F.max_pool2d(outs[-1], 1, stride=2))
            else:
                if self.add_extra_convs == 'on_input':
                    extra_source = inputs[self.end_level]
                elif self.add_extra_convs == 'on_lateral':
                    extra_source = laterals[-1]
                elif self.add_extra_convs == 'on_output':
                    extra_source = outs[-1]
                else:
                    raise NotImplementedError
                    
                outs.append(self.extra_convs[0](extra_source))
                for i in range(1, len(self.extra_convs)):
                    if self.relu_before_extra_convs:
                        outs.append(self.extra_convs[i](F.relu(outs[-1])))
                    else:
                        outs.append(self.extra_convs[i](outs[-1]))
        
        return outs


class MaskRCNN_FPN(nn.Module):
    """原始 Mask R-CNN 風格的 FPN 實現（向後兼容）"""
    
    def __init__(self, C1: nn.Module, C2: nn.Module, C3: nn.Module, 
                 C4: nn.Module, C5: nn.Module, out_channels: int = 256):
        super(MaskRCNN_FPN, self).__init__()
        self.out_channels = out_channels
        self.C1 = C1
        self.C2 = C2
        self.C3 = C3
        self.C4 = C4
        self.C5 = C5
        
        # P6 用於第5個錨點尺度
        self.P6 = nn.MaxPool2d(kernel_size=1, stride=2)
        
        # P5 分支
        self.P5_conv1 = nn.Conv2d(2048, self.out_channels, kernel_size=1, stride=1)
        self.P5_conv2 = nn.Sequential(
            SamePad2d(kernel_size=3, stride=1),
            nn.Conv2d(self.out_channels, self.out_channels, kernel_size=3, stride=1),
        )
        
        # P4 分支
        self.P4_conv1 = nn.Conv2d(1024, self.out_channels, kernel_size=1, stride=1)
        self.P4_conv2 = nn.Sequential(
            SamePad2d(kernel_size=3, stride=1),
            nn.Conv2d(self.out_channels, self.out_channels, kernel_size=3, stride=1),
        )
        
        # P3 分支
        self.P3_conv1 = nn.Conv2d(512, self.out_channels, kernel_size=1, stride=1)
        self.P3_conv2 = nn.Sequential(
            SamePad2d(kernel_size=3, stride=1),
            nn.Conv2d(self.out_channels, self.out_channels, kernel_size=3, stride=1),
        )
        
        # P2 分支
        self.P2_conv1 = nn.Conv2d(256, self.out_channels, kernel_size=1, stride=1)
        self.P2_conv2 = nn.Sequential(
            SamePad2d(kernel_size=3, stride=1),
            nn.Conv2d(self.out_channels, self.out_channels, kernel_size=3, stride=1),
        )

    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """前向傳播"""
        x = self.C1(x)
        x = self.C2(x)
        c2_out = x
        x = self.C3(x)
        c3_out = x
        x = self.C4(x)
        c4_out = x
        x = self.C5(x)
        
        # 自上而下路徑
        p5_out = self.P5_conv1(x)
        p4_out = self.P4_conv1(c4_out) + F.interpolate(p5_out, scale_factor=2, mode='nearest')
        p3_out = self.P3_conv1(c3_out) + F.interpolate(p4_out, scale_factor=2, mode='nearest')
        p2_out = self.P2_conv1(c2_out) + F.interpolate(p3_out, scale_factor=2, mode='nearest')

        # 最終卷積
        p5_out = self.P5_conv2(p5_out)
        p4_out = self.P4_conv2(p4_out)
        p3_out = self.P3_conv2(p3_out)
        p2_out = self.P2_conv2(p2_out)

        # P6 從 P5 下採樣得到
        p6_out = self.P6(p5_out)

        return [p2_out, p3_out, p4_out, p5_out, p6_out]


# 工廠函數
def create_resnet_backbone(architecture: str = "resnet50", stage5: bool = True) -> ResNetBackbone:
    """創建 ResNet 骨幹網路"""
    return ResNetBackbone(architecture=architecture, stage5=stage5)


def create_fpn(backbone: Optional[ResNetBackbone] = None,
               in_channels_list: Optional[List[int]] = None,
               out_channels: int = 256,
               num_outs: int = 5) -> FeaturePyramidNetwork:
    """創建 FPN 網路"""
    if in_channels_list is None:
        in_channels_list = [256, 512, 1024, 2048]  # ResNet 默認通道數
    
    return FeaturePyramidNetwork(
        backbone=backbone,
        in_channels_list=in_channels_list,
        out_channels=out_channels,
        num_outs=num_outs
    )


def create_mask_rcnn_fpn(backbone: ResNetBackbone, out_channels: int = 256) -> MaskRCNN_FPN:
    """創建 Mask R-CNN 風格的 FPN"""
    stages = backbone.get_stages()
    if len(stages) < 5:
        raise ValueError("Backbone must have 5 stages for Mask R-CNN FPN")
    
    return MaskRCNN_FPN(
        C1=stages[0], C2=stages[1], C3=stages[2], 
        C4=stages[3], C5=stages[4], 
        out_channels=out_channels
    )


def create_resnet_fpn(architecture: str = "resnet50", 
                     out_channels: int = 256,
                     num_outs: int = 5,
                     stage5: bool = True) -> Tuple[ResNetBackbone, FeaturePyramidNetwork]:
    """創建完整的 ResNet + FPN 組合"""
    backbone = create_resnet_backbone(architecture=architecture, stage5=stage5)
    
    # 根據 ResNet 架構確定通道數
    if stage5:
        in_channels_list = [256, 512, 1024, 2048]
    else:
        in_channels_list = [256, 512, 1024]
    
    fpn = create_fpn(
        backbone=backbone,
        in_channels_list=in_channels_list,
        out_channels=out_channels,
        num_outs=num_outs
    )
    
    return backbone, fpn


# 向後兼容的類別別名
FPN = FeaturePyramidNetwork
ResNet = ResNetBackbone


if __name__ == "__main__":
    # 測試代碼
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 測試 ResNet 骨幹
    backbone = create_resnet_backbone("resnet50", stage5=True)
    backbone.to(device)
    
    # 測試輸入
    x = torch.randn(1, 3, 224, 224).to(device)
    features = backbone(x)
    print(f"ResNet features shapes: {[f.shape for f in features]}")
    
    # 測試 FPN
    fpn = create_fpn(out_channels=256, num_outs=5)
    fpn.to(device)
    
    fpn_features = fpn(list(features[1:]))  # 跳過 C1
    print(f"FPN features shapes: {[f.shape for f in fpn_features]}")
    
    # 測試組合
    backbone, fpn = create_resnet_fpn("resnet50", out_channels=256, num_outs=5)
    backbone.to(device)
    fpn.to(device)
    
    features = backbone(x)
    fpn_features = fpn(list(features[1:]))
    print(f"Combined features shapes: {[f.shape for f in fpn_features]}")
    
    print("FPN module test completed successfully!")