# CSP IFormer 最終分割版本配置
# 對應原始的 CSP_IFormer_final_SegMode.py

encoder_type: csp_iformer
encoder_variant: final_segmentation
input_channels: 3
mode: segmentation
img_size: 256

# 架構參數
depths: [3, 3, 9, 3]
embed_dims: [96, 192, 320, 384]
num_heads: [3, 6, 10, 12]
attention_heads: [3, 6, 10, 12]
mlp_ratios: [4, 4, 4, 4]

# 特殊功能 - 啟用所有高級功能
enable_channel_shuffle: true
enable_dropkey: true
enable_efficient_ffn: false
channel_shuffle_groups: 8
channel_shuffle_times: 1
dropkey_rate: 0.1

# 訓練參數
qkv_bias: true
drop_rate: 0.0
attn_drop_rate: 0.0
drop_path_rate: 0.1

# 其他參數
part_ratio: 0.5
skip_stages: []

# 分割特定參數
output_stride: 32
feature_pyramid: true