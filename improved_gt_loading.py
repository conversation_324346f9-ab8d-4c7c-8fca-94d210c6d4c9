#!/usr/bin/env python3
"""
改進的GT標註載入方法
"""

def improved_load_gt_annotations(self, annotation_path: str, image_shape: tuple) -> list:
    """
    改進的GT標註載入方法，增強調試和錯誤處理
    
    Args:
        annotation_path: 標註文件路徑
        image_shape: 圖像形狀 (H, W, C)
    
    Returns:
        GT標註列表
    """
    import os
    import json
    from pathlib import Path
    
    # 基本驗證
    if not annotation_path:
        self.logger.warning("GT標註路徑為空")
        return []
    
    if not os.path.exists(annotation_path):
        self.logger.warning(f"GT標註文件不存在: {annotation_path}")
        return []
    
    # 圖像尺寸
    h, w = image_shape[:2]
    gt_annotations = []
    
    self.logger.info(f"🔍 開始載入GT標註: {annotation_path}")
    self.logger.info(f"📐 圖像尺寸: {w}x{h}")
    
    try:
        file_ext = Path(annotation_path).suffix.lower()
        self.logger.info(f"📄 文件類型: {file_ext}")
        
        if file_ext == '.json':  # LabelMe格式
            self.logger.info("🏷️  處理LabelMe JSON格式")
            
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 詳細日誌
            shapes = data.get('shapes', [])
            self.logger.info(f"📊 JSON包含 {len(shapes)} 個shapes")
            
            if 'imageWidth' in data and 'imageHeight' in data:
                json_w, json_h = data['imageWidth'], data['imageHeight']
                self.logger.info(f"📐 JSON中的圖像尺寸: {json_w}x{json_h}")
                
                if json_w != w or json_h != h:
                    self.logger.warning(f"⚠️  圖像尺寸不匹配! 實際: {w}x{h}, JSON: {json_w}x{json_h}")
            
            # 處理每個shape
            for i, shape in enumerate(shapes):
                shape_type = shape.get('shape_type', '')
                class_name = shape.get('label', '')
                points = shape.get('points', [])
                
                self.logger.debug(f"   Shape {i+1}: {class_name} ({shape_type}) - {len(points)} 個點")
                self.logger.debug(f"     原始點數據: {points}")
                
                bbox = None
                
                # 處理矩形
                if shape_type == 'rectangle' and len(points) >= 2:
                    try:
                        x1, y1 = points[0]
                        x2, y2 = points[1]
                        
                        # 確保坐標正確
                        x1, x2 = min(x1, x2), max(x1, x2)
                        y1, y2 = min(y1, y2), max(y1, y2)
                        
                        # 邊界檢查
                        x1 = max(0, min(x1, w-1))
                        y1 = max(0, min(y1, h-1))
                        x2 = max(0, min(x2, w-1))
                        y2 = max(0, min(y2, h-1))
                        
                        bbox = [x1, y1, x2, y2]
                        self.logger.debug(f"     計算矩形邊界框: {bbox}")
                        
                    except (ValueError, IndexError, TypeError) as e:
                        self.logger.error(f"     ❌ 矩形點解析錯誤: {e}")
                        continue
                
                # 處理多邊形
                elif shape_type == 'polygon' and len(points) >= 3:
                    try:
                        # 判斷點格式
                        if isinstance(points[0], list):
                            # 格式: [[x1,y1], [x2,y2], ...]
                            xs = [p[0] for p in points]
                            ys = [p[1] for p in points]
                        else:
                            # 格式: [x1, y1, x2, y2, ...]
                            xs = points[::2]
                            ys = points[1::2]
                        
                        if xs and ys:
                            x1, x2 = min(xs), max(xs)
                            y1, y2 = min(ys), max(ys)
                            
                            # 邊界檢查
                            x1 = max(0, min(x1, w-1))
                            y1 = max(0, min(y1, h-1))
                            x2 = max(0, min(x2, w-1))
                            y2 = max(0, min(y2, h-1))
                            
                            bbox = [x1, y1, x2, y2]
                            self.logger.debug(f"     計算多邊形邊界框: {bbox}")
                        
                    except (ValueError, IndexError, TypeError) as e:
                        self.logger.error(f"     ❌ 多邊形點解析錯誤: {e}")
                        continue
                
                else:
                    self.logger.warning(f"     ⚠️  不支持的形狀類型或點數不足: {shape_type}, {len(points)} 個點")
                    continue
                
                # 如果成功計算出邊界框
                if bbox and bbox[2] > bbox[0] and bbox[3] > bbox[1]:  # 確保有效邊界框
                    # 尋找類別ID
                    class_id = self._find_class_id(class_name)
                    
                    if class_id >= 0:
                        annotation = {
                            'id': i,
                            'class_id': class_id,
                            'class_name': class_name,
                            'bbox': bbox,
                            'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                            'shape_type': shape_type,
                            'confidence': 1.0  # GT標註置信度為1
                        }
                        
                        gt_annotations.append(annotation)
                        self.logger.info(f"     ✅ 成功加入GT: {class_name} (ID: {class_id}) - {bbox}")
                    else:
                        self.logger.warning(f"     ⚠️  找不到類別: '{class_name}'")
                else:
                    self.logger.warning(f"     ❌ 無效邊界框: {bbox}")
        
        elif file_ext == '.txt':  # YOLO格式
            self.logger.info("🎯 處理YOLO TXT格式")
            
            with open(annotation_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.logger.info(f"📊 TXT包含 {len(lines)} 行")
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                self.logger.debug(f"   行 {i+1}: {line}")
                
                parts = line.split()
                if len(parts) >= 5:
                    try:
                        class_id = int(parts[0])
                        cx, cy, bw, bh = map(float, parts[1:5])
                        
                        # 轉換為絕對坐標
                        x1 = (cx - bw/2) * w
                        y1 = (cy - bh/2) * h
                        x2 = (cx + bw/2) * w
                        y2 = (cy + bh/2) * h
                        
                        # 邊界檢查
                        x1 = max(0, min(x1, w-1))
                        y1 = max(0, min(y1, h-1))
                        x2 = max(0, min(x2, w-1))
                        y2 = max(0, min(y2, h-1))
                        
                        bbox = [x1, y1, x2, y2]
                        
                        # 獲取類別名稱
                        class_name = self._get_class_name(class_id)
                        
                        annotation = {
                            'id': i,
                            'class_id': class_id,
                            'class_name': class_name,
                            'bbox': bbox,
                            'area': (x2 - x1) * (y2 - y1),
                            'confidence': 1.0
                        }
                        
                        gt_annotations.append(annotation)
                        self.logger.info(f"     ✅ 成功加入YOLO GT: {class_name} (ID: {class_id}) - [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
                        
                    except (ValueError, IndexError) as e:
                        self.logger.error(f"     ❌ YOLO格式解析錯誤 行{i+1}: {e}")
                        continue
                else:
                    self.logger.warning(f"     ⚠️  YOLO格式錯誤 行{i+1}: 需要至少5個值，得到 {len(parts)} 個")
        
        else:
            self.logger.error(f"❌ 不支持的標註格式: {file_ext}")
            return []
    
    except json.JSONDecodeError as e:
        self.logger.error(f"❌ JSON解析錯誤: {e}")
        return []
    except UnicodeDecodeError as e:
        self.logger.error(f"❌ 文件編碼錯誤: {e}")
        return []
    except Exception as e:
        self.logger.error(f"❌ GT載入失敗: {e}")
        import traceback
        self.logger.debug(traceback.format_exc())
        return []
    
    # 最終結果
    self.logger.info(f"🎉 GT載入完成: 成功載入 {len(gt_annotations)} 個標註")
    
    if gt_annotations:
        self.logger.info("📋 GT標註摘要:")
        class_counts = {}
        for gt in gt_annotations:
            class_name = gt['class_name']
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        for class_name, count in class_counts.items():
            self.logger.info(f"   - {class_name}: {count} 個")
    else:
        self.logger.warning("⚠️  未載入任何GT標註，請檢查:")
        self.logger.warning("   1. 標註文件格式是否正確")
        self.logger.warning("   2. 類別配置是否包含標註中的類別")
        self.logger.warning("   3. 標註坐標是否有效")
    
    return gt_annotations

def _find_class_id(self, class_name: str) -> int:
    """尋找類別ID，包含精確匹配和模糊匹配"""
    if not class_name:
        return -1
    
    # 方法1: 精確匹配
    for cid, config in self.config.class_configs.items():
        if config.name == class_name:
            self.logger.debug(f"精確匹配: '{class_name}' -> ID: {cid}")
            return cid
    
    # 方法2: 模糊匹配 (忽略大小寫和空格)
    normalized_input = class_name.lower().replace(' ', '').replace('_', '').replace('-', '')
    for cid, config in self.config.class_configs.items():
        normalized_config = config.name.lower().replace(' ', '').replace('_', '').replace('-', '')
        if normalized_config == normalized_input:
            self.logger.debug(f"模糊匹配: '{class_name}' -> '{config.name}' (ID: {cid})")
            return cid
    
    # 方法3: 包含匹配
    for cid, config in self.config.class_configs.items():
        if class_name.lower() in config.name.lower() or config.name.lower() in class_name.lower():
            self.logger.debug(f"包含匹配: '{class_name}' -> '{config.name}' (ID: {cid})")
            return cid
    
    # 方法4: 如果沒有配置類別，創建新類別
    if not self.config.class_configs:
        # 使用哈希創建穩定的ID
        class_id = hash(class_name) % 1000
        self.logger.info(f"創建新類別: '{class_name}' -> ID: {class_id}")
        return class_id
    
    # 記錄可用類別
    available_classes = [config.name for config in self.config.class_configs.values()]
    self.logger.warning(f"找不到類別匹配: '{class_name}', 可用類別: {available_classes}")
    return -1

def _get_class_name(self, class_id: int) -> str:
    """根據類別ID獲取類別名稱"""
    if class_id in self.config.class_configs:
        return self.config.class_configs[class_id].name
    else:
        return f'class_{class_id}'


# 使用說明:
# 將上述方法替換enhanced_yolo_inference.py中的_load_gt_annotations方法，
# 並添加_find_class_id和_get_class_name方法
print("""
📋 改進的GT載入方法已準備完成

🔧 使用方法:
1. 將improved_load_gt_annotations方法複製到enhanced_yolo_inference.py
2. 替換現有的_load_gt_annotations方法
3. 添加_find_class_id和_get_class_name輔助方法
4. 重新運行Enhanced YOLO

✨ 改進內容:
- 詳細的調試日誌
- 更好的錯誤處理
- 支持多種點格式
- 邊界檢查和驗證
- 類別匹配增強
- 完整的結果摘要

🔍 調試技巧:
- 設置日誌級別為DEBUG: logger.setLevel(logging.DEBUG)
- 檢查日誌輸出中的詳細信息
- 確認類別配置包含標註中的所有類別
""")