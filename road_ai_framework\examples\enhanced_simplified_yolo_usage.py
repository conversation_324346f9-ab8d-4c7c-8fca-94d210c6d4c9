#!/usr/bin/env python3
"""
增強型簡化YOLO推理系統 - 詳細參數配置版
支援每個類別獨立confidence設定和三視圖輸出
"""

import sys
import os
from pathlib import Path

# 添加模組路徑
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# ==================== 詳細參數設定區域 ====================
# 🔧 在這裡修改所有可用的配置參數

# ===== 模型配置 (3個參數) =====
detection_model_path = ""      # YOLO檢測模型路徑
segmentation_model_path = r"D:\4_road_crack\best.pt"  # YOLO分割模型路徑（優先使用）
device = "cuda"                                        # 運算設備: "auto", "cpu", "cuda", "mps"

# ===== 推理配置 (4個參數) =====
img_size = 640                    # 輸入圖像尺寸
global_conf = 0.05               # 全局置信度閾值 (0.0-1.0) - 未設定類別的預設值
iou_threshold = 0.45             # IoU閾值 (0.0-1.0)
max_det = 1000                   # 最大檢測數量

# ===== SAHI配置 (5個參數) =====
enable_sahi = False              # 是否啟用SAHI大圖像切片推理
slice_size = 640                 # SAHI切片大小
overlap_ratio = 0.2              # SAHI重疊比例 (0.0-1.0)
sahi_conf = 0.1                  # SAHI專用置信度閾值 - 未設定類別的預設值
sahi_iou = 0.5                   # SAHI專用IoU閾值

# ===== 輸出配置 (7個參數) =====
save_visualizations = True       # 是否保存可視化結果
save_predictions = True          # 是否保存預測結果
save_statistics = True           # 是否保存統計信息
enable_three_view_output = True  # 啟用三視圖輸出 (原圖/GT/pred)
enable_gt_comparison = False     # 是否啟用GT比較 (需要提供GT路徑)
enable_reports = True           # 啟用報告生成 (images/ 和 reports/ 資料夾)
enable_intelligent_filtering = True  # 啟用智能過濾 (linear_crack vs Alligator_crack)

# ===== 視覺化配置 (4個參數) =====
output_image_quality = 95        # 輸出圖像品質 (1-100)
text_font_size = 2.5            # 文字大小 (建議1.5-3.0)
gt_format = "labelme"           # GT格式: "labelme", "yolo", "coco"
color_consistency = True        # 保持GT和預測顏色一致性

# ===== 智能過濾配置 (3個參數) =====
aspect_ratio_threshold = 0.8     # 長寬比閾值 (用於linear vs alligator判斷)
area_ratio_threshold = 0.4       # 面積比閾值 (用於linear vs alligator判斷)
joint_overlap_threshold = 0.3    # joint覆蓋閾值 (用於linear vs joint判斷)

# ===== 輸入輸出路徑配置 =====
input_path = r"D:\image\5_test_image\test_2_org"       # 輸入圖片路徑或資料夾
output_path = r"D:\image\5_test_image_test\test_enhanced_output"    # 輸出結果資料夾
gt_path = ""                                           # GT標註路徑 (啟用GT比較時需要)

# ==================== 類別詳細配置區域 ====================
# 🎯 每個類別可以獨立設定confidence閾值

# 預設類別配置 - 按照您的10個類別
class_configs = {
    # 類別ID: {"name": "類別名稱", "conf": 直接推理閾值, "sahi_conf": SAHI推理閾值, "enabled": 是否啟用, "color": RGB顏色, "display_name": "顯示名稱"}
    0: {
        "name": "expansion_joint_伸縮縫",
        "conf": 0.3,                    # 直接推理閾值
        "sahi_conf": 0.15,             # SAHI推理閾值
        "enabled": True,
        "color": (255, 0, 0),          # 紅色
        "display_name": "伸縮縫"
    },
    1: {
        "name": "joint_路面接縫",
        "conf": 0.25,
        "sahi_conf": 0.1,
        "enabled": True,
        "color": (0, 255, 0),          # 綠色
        "display_name": "路面接縫"
    },
    2: {
        "name": "linear_crack_裂縫",
        "conf": 0.2,                   # 裂縫檢測閾值較低，提高敏感度
        "sahi_conf": 0.08,
        "enabled": True,
        "color": (0, 0, 255),          # 藍色
        "display_name": "縱向裂縫"
    },
    3: {
        "name": "Alligator_crack_龜裂",
        "conf": 0.3,
        "sahi_conf": 0.15,
        "enabled": True,
        "color": (255, 255, 0),        # 黃色
        "display_name": "龜裂"
    },
    4: {
        "name": "potholes_坑洞",
        "conf": 0.4,                   # 坑洞檢測閾值較高，減少誤報
        "sahi_conf": 0.2,
        "enabled": True,
        "color": (255, 0, 255),        # 洋紅色
        "display_name": "坑洞"
    },
    5: {
        "name": "patch_補綻",
        "conf": 0.35,
        "sahi_conf": 0.18,
        "enabled": True,
        "color": (0, 255, 255),        # 青色
        "display_name": "補綻"
    },
    6: {
        "name": "manhole_人孔蓋或排水溝",
        "conf": 0.5,                   # 人孔蓋較容易識別，設定較高閾值
        "sahi_conf": 0.25,
        "enabled": True,
        "color": (128, 0, 128),        # 紫色
        "display_name": "人孔蓋"
    },
    7: {
        "name": "deformation_變形",
        "conf": 0.3,
        "sahi_conf": 0.15,
        "enabled": True,
        "color": (255, 165, 0),        # 橘色
        "display_name": "路面變形"
    },
    8: {
        "name": "dirt_污垢",
        "conf": 0.4,
        "sahi_conf": 0.2,
        "enabled": False,              # 預設停用污垢檢測
        "color": (139, 69, 19),        # 棕色
        "display_name": "污垢"
    },
    9: {
        "name": "lane_line_linear_白綫裂縫",
        "conf": 0.25,
        "sahi_conf": 0.12,
        "enabled": True,
        "color": (0, 128, 255),        # 淺藍色
        "display_name": "車道線裂縫"
    }
}

# ===== 標籤別名映射 (處理標註不匹配問題) =====
custom_label_aliases = {
    'manhole': 'manhole_人孔蓋或排水溝',
    'potholes': 'potholes_坑洞',
    'linear_crack': 'linear_crack_裂縫',
    'linear_crack_': 'linear_crack_裂縫',
    'dirt': 'dirt_污垢',
    'expansion_joint': 'expansion_joint_伸縮縫',
    'expansion_joint_': 'expansion_joint_伸縮縫',
    'joint': 'joint_路面接縫',
    'joint_': 'joint_路面接縫',
    'deformation': 'deformation_變形',
    'patch': 'patch_補綻',
    'Alligator_crack': 'Alligator_crack_龜裂',
    'lane_line_linear': 'lane_line_linear_白綫裂縫'
}

# ==================== 執行模式設定 ====================
run_mode = "inference"    # 執行模式: "demo", "inference", "batch", "all", "test_config", "test_filtering"


def create_enhanced_config():
    """創建增強型配置"""
    print("🔧 創建增強型配置...")
    
    try:
        from models.inference.simplified import SimplifiedYOLOConfig
        
        # 創建基礎配置
        config = SimplifiedYOLOConfig(
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            device=device,
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            enable_sahi=enable_sahi,
            slice_size=slice_size,
            overlap_ratio=overlap_ratio,
            sahi_conf=sahi_conf,
            sahi_iou=sahi_iou,
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics,
            enable_three_view_output=enable_three_view_output,
            enable_gt_comparison=enable_gt_comparison,
            gt_format=gt_format,
            output_image_quality=output_image_quality,
            text_font_size=text_font_size,
            enable_reports=enable_reports,
            gt_path=gt_path,
            enable_intelligent_filtering=enable_intelligent_filtering
        )
        
        # 添加類別配置
        print(f"📋 設定 {len(class_configs)} 個類別的詳細配置...")
        for class_id, class_info in class_configs.items():
            config.add_class_config(
                class_id=class_id,
                name=class_info["name"],
                conf_threshold=class_info["conf"],
                sahi_conf_threshold=class_info["sahi_conf"],
                enabled=class_info["enabled"],
                color=class_info.get("color"),
                display_name=class_info.get("display_name", class_info["name"])
            )
        
        # 設定全域標籤別名
        from models.inference.simplified.config.yolo_config import set_global_label_aliases
        if custom_label_aliases:
            set_global_label_aliases(custom_label_aliases)
            print(f"✅ 設定 {len(custom_label_aliases)} 個標籤別名")
        
        print("✅ 增強型配置創建完成")
        return config
        
    except ImportError as e:
        print(f"❌ 配置創建失敗: {e}")
        return None


def show_detailed_config():
    """顯示詳細配置信息"""
    print("\n📋 增強型配置詳情")
    print("=" * 70)
    
    # 基礎配置
    print("🔧 基礎推理配置:")
    print(f"  模型路徑: {segmentation_model_path or detection_model_path}")
    print(f"  設備: {device}")
    print(f"  圖像尺寸: {img_size}")
    print(f"  全域置信度: {global_conf}")
    print(f"  IoU閾值: {iou_threshold}")
    print(f"  最大檢測數: {max_det}")
    
    # SAHI配置
    print(f"\n🔄 SAHI配置: {'啟用' if enable_sahi else '停用'}")
    if enable_sahi:
        print(f"  切片大小: {slice_size}")
        print(f"  重疊比例: {overlap_ratio}")
        print(f"  SAHI置信度: {sahi_conf}")
        print(f"  SAHI IoU: {sahi_iou}")
    
    # 輸出配置
    print(f"\n💾 輸出配置:")
    print(f"  三視圖輸出: {'啟用' if enable_three_view_output else '停用'}")
    print(f"  GT比較: {'啟用' if enable_gt_comparison else '停用'}")
    print(f"  報告生成: {'啟用' if enable_reports else '停用'}")
    print(f"  智能過濾: {'啟用' if enable_intelligent_filtering else '停用'}")
    print(f"  圖像品質: {output_image_quality}")
    print(f"  文字大小: {text_font_size}")
    
    # 智能過濾配置
    if enable_intelligent_filtering:
        print(f"\n🧠 智能過濾配置:")
        print(f"  長寬比閾值: {aspect_ratio_threshold}")
        print(f"  面積比閾值: {area_ratio_threshold}")
        print(f"  joint重疊閾值: {joint_overlap_threshold}")
    
    # 類別配置
    print(f"\n🎯 類別配置 ({len(class_configs)} 個類別):")
    enabled_count = sum(1 for c in class_configs.values() if c["enabled"])
    disabled_count = len(class_configs) - enabled_count
    print(f"  啟用類別: {enabled_count} 個")
    print(f"  停用類別: {disabled_count} 個")
    
    for class_id, class_info in class_configs.items():
        status = "✅" if class_info["enabled"] else "❌"
        print(f"  {status} {class_id}: {class_info['display_name']} (直接:{class_info['conf']}, SAHI:{class_info['sahi_conf']})")
    
    # 路徑配置
    print(f"\n📁 路徑配置:")
    print(f"  輸入路徑: {input_path}")
    print(f"  輸出路徑: {output_path}")
    if enable_gt_comparison and gt_path:
        print(f"  GT路徑: {gt_path}")


def run_enhanced_inference():
    """運行增強型推理"""
    print("\n🚀 開始增強型推理")
    print("=" * 70)
    
    try:
        # 創建配置
        config = create_enhanced_config()
        if not config:
            return False
        
        # 顯示配置摘要
        print("\n📊 配置摘要:")
        print(config.summary())
        
        # 檢查路徑
        if not os.path.exists(input_path):
            print(f"\n❌ 輸入路徑不存在: {input_path}")
            return False
        
        model_path = config.get_model_path()
        if not os.path.exists(model_path):
            print(f"\n❌ 模型路徑不存在: {model_path}")
            return False
        
        # 創建輸出目錄結構
        output_dir = Path(output_path)
        images_dir = output_dir / "images"
        reports_dir = output_dir / "reports"
        
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(reports_dir, exist_ok=True)
        
        print(f"\n📁 輸出目錄結構:")
        print(f"  主目錄: {output_dir}")
        print(f"  圖像目錄: {images_dir}")
        print(f"  報告目錄: {reports_dir}")
        
        # 導入推理模組
        from models.inference.simplified import SimplifiedYOLO
        
        print(f"\n🎯 開始推理...")
        print("-" * 50)
        
        # 創建YOLO實例
        yolo = SimplifiedYOLO(config)
        
        # 執行推理
        if os.path.isfile(input_path):
            print(f"📸 處理單張圖片: {input_path}")
            result = yolo.predict(input_path, output_dir=str(images_dir))
            print(f"✅ 推理完成，檢測到 {len(result.get('boxes', []))} 個物件")
        else:
            print(f"📁 批次處理資料夾: {input_path}")
            results = yolo.predict_batch(input_path, str(images_dir))
            print(f"✅ 批次推理完成，處理了 {len(results)} 張圖片")
        
        print(f"\n💾 結果保存至:")
        print(f"  圖像: {images_dir}")
        print(f"  報告: {reports_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_compatibility():
    """測試配置兼容性"""
    print("\n🔍 配置兼容性測試")
    print("=" * 50)
    
    try:
        config = create_enhanced_config()
        if not config:
            print("❌ 配置創建失敗")
            return False
        
        print("✅ 配置創建成功")
        print(f"✅ 支援 {len(config.class_configs)} 個類別")
        
        # 測試類別配置功能
        test_class_id = 2  # linear_crack
        if test_class_id in config.class_configs:
            test_conf = config.get_class_confidence(test_class_id, use_sahi=False)
            test_sahi_conf = config.get_class_confidence(test_class_id, use_sahi=True)
            print(f"✅ 類別 {test_class_id} 置信度測試: 直接推理={test_conf}, SAHI={test_sahi_conf}")
        
        enabled_classes = config.get_enabled_classes()
        print(f"✅ 啟用類別數量: {len(enabled_classes)}")
        
        print("✅ 所有測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 兼容性測試失敗: {e}")
        return False


def demonstrate_advanced_features():
    """演示進階功能"""
    print("\n🌟 進階功能演示")
    print("=" * 50)
    
    try:
        from models.inference.simplified import SimplifiedYOLO
        
        print("1️⃣ 類別特定配置演示:")
        config = create_enhanced_config()
        if config:
            print("   ✅ 每個類別可設定不同的confidence閾值")
            print("   ✅ 支援SAHI和直接推理的獨立閾值")
            print("   ✅ 支援類別啟用/停用控制")
            print("   ✅ 支援自定義顏色和顯示名稱")
        
        print("\n2️⃣ 輸出格式演示:")
        print("   ✅ 三視圖輸出 (原圖/GT/預測)")
        print("   ✅ 舊版兼容的reports結構")
        print("   ✅ images/ 和 reports/ 雙資料夾結構")
        print("   ✅ CSV格式的詳細統計報告")
        
        print("\n3️⃣ 視覺化增強:")
        print(f"   ✅ 高品質圖像輸出 (品質: {output_image_quality})")
        print(f"   ✅ 可調文字大小 (大小: {text_font_size})")
        print("   ✅ GT和預測顏色一致性")
        print("   ✅ 中文標籤支援")
        
        print("\n4️⃣ API使用範例:")
        print("   # 類別特定推理")
        print("   yolo = SimplifiedYOLO(enhanced_config)")
        print("   yolo.set_class_confidence(class_id=2, confidence=0.15)")
        print("   result = yolo.predict('image.jpg')")
        
        print("\n✅ 進階功能演示完成")
        
    except Exception as e:
        print(f"❌ 演示失敗: {e}")


def main():
    """主函數"""
    print("🚀 增強型簡化YOLO推理系統")
    print("=" * 70)
    print("支援每個類別獨立confidence設定和三視圖輸出")
    print("相比基礎版本: 新增類別特定配置、GT比較、報告生成")
    print("=" * 70)
    
    # 顯示當前設定
    show_detailed_config()
    
    try:
        if run_mode == "demo":
            print("\n🎯 執行演示模式...")
            demonstrate_advanced_features()
            test_config_compatibility()
            
        elif run_mode == "inference":
            print("\n🚀 執行推理模式...")
            success = run_enhanced_inference()
            if not success:
                print("\n💡 提示: 請檢查模型路徑和輸入路徑設定")
                
        elif run_mode == "test_config":
            print("\n🔍 執行配置測試模式...")
            test_config_compatibility()
            
        elif run_mode == "all":
            print("\n🎯 執行完整模式...")
            demonstrate_advanced_features()
            test_config_compatibility()
            
            # 如果路徑正確，執行實際推理
            if os.path.exists(input_path) and os.path.exists(segmentation_model_path or detection_model_path):
                print("\n🚀 執行實際推理...")
                run_enhanced_inference()
            else:
                print("\n⚠️ 跳過實際推理（路徑不存在）")
                
        else:
            print(f"❌ 未知執行模式: {run_mode}")
            print("支援的模式: demo, inference, test_config, all")
            return
            
    except KeyboardInterrupt:
        print("\n⏹️ 用戶中斷執行")
    except Exception as e:
        print(f"\n💥 執行失敗: {e}")
        import traceback
        traceback.print_exc()
    
    # 最終總結
    print("\n" + "=" * 70)
    print("🎉 增強型簡化YOLO推理系統執行完成!")
    print("\n📊 增強功能:")
    enabled_classes = sum(1 for c in class_configs.values() if c["enabled"])
    print(f"  ✅ 類別特定配置 ({enabled_classes}/{len(class_configs)} 個類別啟用)")
    print(f"  ✅ 三視圖輸出 ({'啟用' if enable_three_view_output else '停用'})")
    print(f"  ✅ GT比較功能 ({'啟用' if enable_gt_comparison else '停用'})")
    print(f"  ✅ 報告生成 ({'啟用' if enable_reports else '停用'})")
    print(f"  ✅ SAHI支援 ({'啟用' if enable_sahi else '停用'})")
    print(f"  ✅ 智能過濾 ({'啟用' if enable_intelligent_filtering else '停用'})")
    if enable_intelligent_filtering:
        print(f"    • linear vs alligator: 長寬比<{aspect_ratio_threshold} 且面積比<{area_ratio_threshold} 保留linear")
        print(f"    • linear vs joint: 重疊>{joint_overlap_threshold} 移除linear")
    print("\n🔗 使用指南:")
    print("  1. 修改類別配置區域的confidence閾值")
    print("  2. 設定三視圖和GT比較選項")
    print("  3. 選擇執行模式")
    print("  4. 直接運行: python enhanced_simplified_yolo_usage.py")


if __name__ == "__main__":
    main()