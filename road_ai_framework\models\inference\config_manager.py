#!/usr/bin/env python3
"""
統一YOLO推理系統配置管理器
支持YAML配置文件讀取、驗證和動態修改
"""

import os
import yaml
import json
import logging
from pathlib import Path
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple

@dataclass
class FusionConfig:
    """物件融合配置"""
    strategy: str = "largest_object"  # 融合策略
    iou_threshold: float = 0.4
    confidence_threshold: float = 0.1

from copy import deepcopy


@dataclass
class ClassConfig:
    """單個類別配置"""
    name: str
    display_name: str
    color: List[int]  # RGB顏色
    confidence: float
    sahi_confidence: float
    enabled: bool = True
    min_area: int = 0
    max_area: int = 999999


@dataclass
class ModelConfig:
    """模型配置"""
    detection_model_path: str = ""
    segmentation_model_path: str = ""
    device: str = "cuda"
    half_precision: bool = True


@dataclass
class InferenceConfig:
    """推理配置"""
    img_size: int = 640
    global_conf: float = 0.05
    iou_threshold: float = 0.45
    max_det: int = 1000
    augment: bool = False
    visualize: bool = False
    resize_ratio: float = 1.0


@dataclass
class SAHIConfig:
    """SAHI配置"""
    enable_sahi: bool = False
    slice_height: int = 640
    slice_width: int = 640
    overlap_height_ratio: float = 0.2
    overlap_width_ratio: float = 0.2
    postprocess_type: str = "GREEDYNMM"
    postprocess_match_threshold: float = 0.5
    postprocess_class_agnostic: bool = False
    auto_slice_resolution: bool = True
    enable_sahi_roi: bool = True  # 🆕 啟用ROI預覽
    roi_top_ratio: float = 2.0     # 🆕 ROI上邊界比例
    roi_bottom_ratio: float = 2.0  # 🆕 ROI下邊界比例
    roi_left_ratio: float = 2.0    # 🆕 ROI左邊界比例
    roi_right_ratio: float = 2.0   # 🆕 ROI右邊界比例


@dataclass
class FilteringConfig:
    """智能過濾配置"""
    enable_intelligent_filtering: bool = True
    enable_detection_merge: bool = True
    step1_iou_threshold: float = 0.0
    linear_aspect_ratio_threshold: float = 0.8
    area_ratio_threshold: float = 0.4
    step2_iou_threshold: float = 0.3
    joint_overlap_threshold: float = 0.3
    merge_iou_threshold: float = 0.2
    merge_classes: List[str] = field(default_factory=lambda: ["linear_crack", "Alligator_crack"])


@dataclass
class VisualizationConfig:
    """視覺化配置"""
    save_visualizations: bool = True
    save_predictions: bool = True
    save_statistics: bool = True
    enable_three_view_output: bool = True
    three_view_layout: str = "horizontal"
    three_view_spacing: int = 10
    font_path: str = ""
    font_size: float = 1.0
    font_thickness: int = 2
    font_scale: float = 1.0
    output_image_quality: int = 95
    line_thickness: int = 3
    fill_alpha: float = 0.3
    color_mode: str = "consistent"
    background_color: List[int] = field(default_factory=lambda: [255, 255, 255])
    enable_roi_preview: bool = True  # 🆕 啟用ROI預覽
    force_single_image_roi_preview: bool = False  # 🆕 強制單圖像ROI預覽


@dataclass
class GroundTruthConfig:
    """Ground Truth配置"""
    enable_gt_comparison: bool = False
    gt_format: str = "labelme"
    gt_path: str = ""
    match_threshold: float = 0.5


@dataclass
class OutputConfig:
    """輸出配置"""
    enable_reports: bool = True
    report_format: List[str] = field(default_factory=lambda: ["csv", "json"])
    create_summary: bool = True
    images_subdir: str = "images"
    reports_subdir: str = "reports"
    temp_subdir: str = "temp"
    timestamp_format: str = "%Y%m%d_%H%M%S"
    include_timestamp: bool = True


@dataclass
class PathsConfig:
    """路徑配置"""
    input_path: str = ""
    output_path: str = ""
    labelme_dir: str = ""
    auto_detect_gt: bool = True
    auto_create_output_dirs: bool = True
    enable_roi_processing: bool = False  # 🆕 啟用ROI處理


@dataclass
class JSONScanConfig:
    """JSON標籤掃描配置"""
    enable_json_scan: bool = True
    display_json_summary: bool = True
    save_json_scan_report: bool = True
    include_file_list: bool = True
    include_shape_types: bool = True
    max_files_to_display: int = 10


class UnifiedYOLOConfigManager:
    """統一YOLO推理系統配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路徑，如果為None則使用默認配置
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化配置
        self.model = ModelConfig()
        self.inference = InferenceConfig()
        self.sahi = SAHIConfig()
        self.filtering = FilteringConfig()
        self.visualization = VisualizationConfig()
        self.ground_truth = GroundTruthConfig()
        self.output = OutputConfig()
        self.paths = PathsConfig()
        self.json_scan = JSONScanConfig()
        self.fusion = FusionConfig()
        
        # 類別配置和別名映射
        self.classes: Dict[int, ClassConfig] = {}
        self.label_aliases: Dict[str, str] = {}
        
        # 如果提供了配置文件路徑，則加載
        if config_path:
            self.load_config(config_path)
        else:
            self._load_default_classes()
    
    def load_config(self, config_path: str) -> bool:
        """
        從YAML文件加載配置
        
        Args:
            config_path: 配置文件路徑
            
        Returns:
            是否加載成功
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                self.logger.error(f"配置文件不存在: {config_path}")
                return False
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 加載各個配置段
            self._load_model_config(config_data.get('model', {}))
            self._load_inference_config(config_data.get('inference', {}))
            self._load_sahi_config(config_data.get('sahi', {}))
            self._load_filtering_config(config_data.get('filtering', {}))
            self._load_visualization_config(config_data.get('visualization', {}))
            self._load_ground_truth_config(config_data.get('ground_truth', {}))
            self._load_output_config(config_data.get('output', {}))
            self._load_paths_config(config_data.get('paths', {}))
            self._load_classes_config(config_data.get('classes', {}))
            
            self.logger.info(f"配置文件加載成功: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件加載失敗: {e}")
            return False
    
    def save_config(self, config_path: str) -> bool:
        """
        保存配置到YAML文件
        
        Args:
            config_path: 配置文件保存路徑
            
        Returns:
            是否保存成功
        """
        try:
            config_data = {
                'model': asdict(self.model),
                'inference': asdict(self.inference),
                'sahi': asdict(self.sahi),
                'filtering': asdict(self.filtering),
                'visualization': asdict(self.visualization),
                'ground_truth': asdict(self.ground_truth),
                'output': asdict(self.output),
                'paths': asdict(self.paths),
                'classes': {
                    'class_mapping': {
                        str(k): asdict(v) for k, v in self.classes.items()
                    },
                    'label_aliases': self.label_aliases
                }
            }
            
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config_data, f, default_flow_style=False, 
                             allow_unicode=True, sort_keys=False)
            
            self.logger.info(f"配置文件保存成功: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件保存失敗: {e}")
            return False
    
    def get_class_config(self, class_id: int) -> Optional[ClassConfig]:
        """獲取指定類別的配置"""
        return self.classes.get(class_id)
    
    def get_class_confidence(self, class_id: int, use_sahi: bool = False) -> float:
        """獲取指定類別的置信度閾值"""
        class_config = self.get_class_config(class_id)
        if class_config:
            return class_config.sahi_confidence if use_sahi else class_config.confidence
        return self.inference.global_conf
    
    def get_class_name(self, class_id: int) -> str:
        """
        獲取類別名稱 (通過label_aliases處理)
        
        Args:
            class_id: 類別ID
            
        Returns:
            經過label_aliases轉換的類別名稱
        """
        class_config = self.get_class_config(class_id)
        if class_config:
            # 使用display_name，然後通過resolve_label_alias處理
            raw_name = class_config.display_name
            return self.resolve_label_alias(raw_name)
        return f"Class_{class_id}"
    
    def get_class_display_name(self, class_id: int) -> str:
        """獲取類別顯示名稱"""
        class_config = self.get_class_config(class_id)
        return class_config.display_name if class_config else f"Class_{class_id}"
    
    def get_class_color(self, class_id: int) -> List[int]:
        """獲取類別顏色"""
        class_config = self.get_class_config(class_id)
        if class_config:
            return class_config.color
        # 如果沒有配置，返回隨機顏色
        return [(class_id * 50) % 255, (class_id * 80) % 255, (class_id * 120) % 255]
    
    def resolve_label_alias(self, label: str) -> str:
        """解析標籤別名"""
        return self.label_aliases.get(label, label)
    
    def add_class_config(self, class_id: int, name: str, display_name: str = "", 
                        color: Optional[List[int]] = None, confidence: float = 0.5,
                        sahi_confidence: float = 0.25, enabled: bool = True) -> None:
        """添加類別配置"""
        if not display_name:
            display_name = name
        if not color:
            color = self.get_class_color(class_id)
        
        self.classes[class_id] = ClassConfig(
            name=name,
            display_name=display_name,
            color=color,
            confidence=confidence,
            sahi_confidence=sahi_confidence,
            enabled=enabled
        )
    
    def update_class_confidence(self, class_id: int, confidence: Optional[float] = None,
                               sahi_confidence: Optional[float] = None) -> bool:
        """更新類別置信度"""
        if class_id not in self.classes:
            return False
        
        if confidence is not None:
            self.classes[class_id].confidence = confidence
        if sahi_confidence is not None:
            self.classes[class_id].sahi_confidence = sahi_confidence
        
        return True
    
    def get_enabled_classes(self) -> List[int]:
        """獲取所有啟用的類別ID"""
        return [class_id for class_id, config in self.classes.items() if config.enabled]
    
    def validate_config(self) -> Dict[str, List[str]]:
        """驗證配置合法性"""
        errors = {
            'model': [],
            'paths': [],
            'classes': [],
            'general': []
        }
        
        # 驗證模型路徑
        if not self.model.detection_model_path and not self.model.segmentation_model_path:
            errors['model'].append("至少需要指定一個模型路徑")
        
        # 驗證路徑
        if self.paths.input_path and not Path(self.paths.input_path).exists():
            errors['paths'].append(f"輸入路徑不存在: {self.paths.input_path}")
        
        # 驗證類別配置
        if not self.classes:
            errors['classes'].append("沒有配置任何類別")
        
        # 驗證置信度範圍
        for class_id, class_config in self.classes.items():
            if not (0 <= class_config.confidence <= 1):
                errors['classes'].append(f"類別{class_id}的confidence超出範圍[0,1]")
            if not (0 <= class_config.sahi_confidence <= 1):
                errors['classes'].append(f"類別{class_id}的sahi_confidence超出範圍[0,1]")
        
        return errors
    
    def get_summary(self) -> str:
        """獲取配置摘要"""
        enabled_classes = self.get_enabled_classes()
        
        summary = f"""
🔧 統一YOLO推理系統配置摘要
{'='*50}
📋 模型配置:
  檢測模型: {self.model.detection_model_path or '未設置'}
  分割模型: {self.model.segmentation_model_path or '未設置'}
  設備: {self.model.device}
  
🎯 推理配置:
  圖像尺寸: {self.inference.img_size}
  全局置信度: {self.inference.global_conf}
  IoU閾值: {self.inference.iou_threshold}
  
🧩 SAHI配置: {'啟用' if self.sahi.enable_sahi else '禁用'}
  切片尺寸: {self.sahi.slice_width}x{self.sahi.slice_height}
  重疊比例: {self.sahi.overlap_width_ratio}
  
🧠 智能過濾: {'啟用' if self.filtering.enable_intelligent_filtering else '禁用'}
  Step1 閾值: 長寬比<{self.filtering.linear_aspect_ratio_threshold}, 面積比<{self.filtering.area_ratio_threshold}
  Step2 閾值: IoU>{self.filtering.step2_iou_threshold}
  
🎨 視覺化配置:
  三視圖: {'啟用' if self.visualization.enable_three_view_output else '禁用'}
  字體大小: {self.visualization.font_size}
  圖像質量: {self.visualization.output_image_quality}
  
📊 類別配置:
  總類別數: {len(self.classes)}
  啟用類別: {len(enabled_classes)}
  禁用類別: {len(self.classes) - len(enabled_classes)}
        """
        
        return summary
    
    def _load_model_config(self, data: Dict[str, any]) -> None:
        """加載模型配置"""
        for key, value in data.items():
            if hasattr(self.model, key):
                setattr(self.model, key, value)
    
    def _load_inference_config(self, data: Dict[str, any]) -> None:
        """加載推理配置"""
        for key, value in data.items():
            if hasattr(self.inference, key):
                setattr(self.inference, key, value)
    
    def _load_sahi_config(self, data: Dict[str, any]) -> None:
        """加載SAHI配置"""
        for key, value in data.items():
            if hasattr(self.sahi, key):
                setattr(self.sahi, key, value)
    
    def _load_filtering_config(self, data: Dict[str, any]) -> None:
        """加載過濾配置"""
        for key, value in data.items():
            if hasattr(self.filtering, key):
                setattr(self.filtering, key, value)
    
    def _load_visualization_config(self, data: Dict[str, any]) -> None:
        """加載視覺化配置"""
        for key, value in data.items():
            if hasattr(self.visualization, key):
                setattr(self.visualization, key, value)
    
    def _load_ground_truth_config(self, data: Dict[str, any]) -> None:
        """加載Ground Truth配置"""
        for key, value in data.items():
            if hasattr(self.ground_truth, key):
                setattr(self.ground_truth, key, value)
    
    def _load_output_config(self, data: Dict[str, any]) -> None:
        """加載輸出配置"""
        for key, value in data.items():
            if hasattr(self.output, key):
                setattr(self.output, key, value)
    
    def _load_paths_config(self, data: Dict[str, any]) -> None:
        """加載路徑配置"""
        for key, value in data.items():
            if hasattr(self.paths, key):
                setattr(self.paths, key, value)
    
    def _load_classes_config(self, data: Dict[str, any]) -> None:
        """加載類別配置"""
        # 加載類別映射
        class_mapping = data.get('class_mapping', {})
        for class_id_str, class_data in class_mapping.items():
            class_id = int(class_id_str)
            self.classes[class_id] = ClassConfig(**class_data)
        
        # 加載標籤別名
        self.label_aliases = data.get('label_aliases', {})
        
        # 如果沒有類別配置，加載默認配置
        if not self.classes:
            self._load_default_classes()
    
    def _load_default_classes(self) -> None:
        """加載默認類別配置"""
        default_classes = {
            0: ("expansion_joint", "expansion_joint", [255, 0, 0], 0.3, 0.15),
            1: ("joint", "joint", [0, 255, 0], 0.25, 0.1),
            2: ("linear_crack", "linear_crack", [0, 0, 255], 0.2, 0.08),
            3: ("Alligator_crack", "Alligator_crack", [255, 255, 0], 0.3, 0.15),
            4: ("potholes", "potholes", [255, 0, 255], 0.4, 0.2),
            5: ("patch", "patch", [0, 255, 255], 0.35, 0.18),
            6: ("manhole", "manhole", [128, 0, 128], 0.5, 0.25),
            7: ("deformation", "deformation", [255, 165, 0], 0.3, 0.15),
            8: ("dirt", "dirt", [139, 69, 19], 0.4, 0.2),
            9: ("lane_line_linear", "lane_line_linear", [0, 128, 255], 0.25, 0.12),
        }
        
        for class_id, (name, display_name, color, conf, sahi_conf) in default_classes.items():
            self.classes[class_id] = ClassConfig(
                name=name,
                display_name=display_name,
                color=color,
                confidence=conf,
                sahi_confidence=sahi_conf,
                enabled=True if class_id != 8 else False  # 污垢默認禁用
            )


def create_default_config_file(output_path: str) -> bool:
    """創建默認配置文件"""
    try:
        config_manager = UnifiedYOLOConfigManager()
        return config_manager.save_config(output_path)
    except Exception as e:
        logging.error(f"創建默認配置文件失敗: {e}")
        return False


if __name__ == "__main__":
    # 測試配置管理器
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 創建配置管理器
    config_manager = UnifiedYOLOConfigManager()
    
    # 打印摘要
    print(config_manager.get_summary())
    
    # 驗證配置
    errors = config_manager.validate_config()
    if any(errors.values()):
        print("\n❌ 配置驗證錯誤:")
        for category, error_list in errors.items():
            if error_list:
                print(f"  {category}: {error_list}")
    else:
        print("\n✅ 配置驗證通過")