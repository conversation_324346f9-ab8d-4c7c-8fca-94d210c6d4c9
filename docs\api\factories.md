# 工廠函數API文檔

*自動生成於: 2025-06-20 22:16:15*

## 概覽

本框架提供了 **118** 個工廠函數和 **13** 個工廠類。

## 工廠函數

### create_dataset_yaml

**模組**: `AI模型建構訓練驗證/0_yolo.py`

**說明**: 無文檔說明...

---

### create_visualization_grid

**模組**: `AI模型建構訓練驗證/0_yolo.py`

**說明**: 創建一個檢測結果的圖像網格...

---

### create_model_config_group

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建模型配置組...

---

### create_inference_config_group

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建推理配置組...

---

### create_sahi_config_group

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建SAHI配置組...

---

### create_output_config_group

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建輸出配置組...

---

### create_advanced_config_group

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建高級配置組...

---

### create_inference_widget

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建推理執行組件...

---

### create_log_widget

**模組**: `AI模型建構訓練驗證/enhanced_yolo_gui.py`

**說明**: 創建日誌組件...

---

### create_measurement_table

**模組**: `AI模型建構訓練驗證/pci_calculator.py`

**說明**: 建立測量數據表

Args:
    damage_measurements: 損壞測量數據列表
    
Returns:
    測量數據表DataFrame...

---

### create_comprehensive_test_suite

**模組**: `tests/test_comprehensive.py`

**說明**: 創建全面測試套件...

---

### create_test_suite

**模組**: `tests/test_suite.py`

**說明**: 創建完整測試套件...

---

### create_comparison_view

**模組**: `資料前處理/panorama_augmenter.py`

**說明**: 創建原始圖像和擴增圖像的對比視圖

Args:
    original_image: 原始圖像
    augmented_image: 擴增後圖像
    title: 圖像標題
    save_path: 保存路徑...

---

### create_content

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建內容區域，子類需要實現...

---

### create_action_buttons

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建操作按鈕...

---

### create_file_input_group

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建文件輸入組...

---

### create_directory_input_group

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建目錄輸入組...

---

### create_content

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建內容區域...

---

### create_content

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建內容區域...

---

### create_merge_tab

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建合併標籤頁...

---

### create_rename_tab

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建重命名標籤頁...

---

### create_delete_tab

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建刪除標籤頁...

---

### create_quick_operations_tab

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建快速操作標籤頁...

---

### create_content

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建內容區域...

---

### create_content

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建內容區域...

---

### create_content

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建內容區域...

---

### create_menu_bar

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建菜單欄...

---

### create_main_widget

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建主窗口部件...

---

### create_tool_tabs

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建工具標籤頁...

---

### create_bottom_panel

**模組**: `資料前處理/pyqt_gui_application.py`

**說明**: 創建底部面板...

---

### create_test_panorama

**模組**: `資料前處理/test_fixed_augmenter.py`

**說明**: 創建測試用的全景圖像...

---

### create_test_image_and_label

**模組**: `資料前處理/test_panorama_fix.py`

**說明**: 創建測試用的圖像和標籤...

---

### create_pci_calculator

**模組**: `AI模型建構訓練驗證/model_create/analysis/pci_calculator.py`

**說明**: 創建PCI計算器的工廠函數

參數:
    sample_area: 樣本面積
    
返回:
    PCICalculator實例...

---

### create_measurement_table

**模組**: `AI模型建構訓練驗證/model_create/analysis/pci_calculator.py`

**說明**: 建立測量數據表...

---

### create_inference

**模組**: `AI模型建構訓練驗證/model_create/core/base_inference.py`

**說明**: 創建推理器實例

參數:
    name: 推理器名稱
    **kwargs: 初始化參數
    
返回:
    推理器實例...

---

### create_enhanced_model

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 便捷函數：創建增強模型

Args:
    family: 模型系列
    variant: 變體
    size: 尺寸
    task: 任務
    num_classes: 類別數
    **kwargs: 額外參數
    
Returns:
    模型實例...

---

### create_enhanced_encoder

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 創建增強編碼器

Args:
    family: 模型系列 ('csp_iformer', 'segman')
    variant: 變體 ('efficient', 'mamba', 'enhanced')
    size: 尺寸 ('tiny', 'small', 'base', 'large')
    config: 自定義配置
    **kwargs: 額外參數
    
R...

---

### create_enhanced_model

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 創建完整增強模型

Args:
    family: 模型系列
    variant: 變體
    size: 尺寸
    task: 任務類型
    num_classes: 類別數
    config: 自定義配置
    **kwargs: 額外參數
    
Returns:
    完整模型...

---

### create_encoder

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 無文檔說明...

---

### create_segmentation_model

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 無文檔說明...

---

### create_model_from_config

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 便捷函數：從配置文件創建模型

Args:
    config_path: 配置文件路徑
    
Returns:
    模型實例...

---

### create_encoder_from_config

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 便捷函數：從配置文件創建編碼器

Args:
    config_path: 配置文件路徑
    
Returns:
    編碼器實例...

---

### create_decoder_from_config

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 便捷函數：從配置文件創建解碼器

Args:
    config_path: 配置文件路徑
    encoder_channels: 編碼器輸出通道數
    
Returns:
    解碼器實例...

---

### create_encoder

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 創建編碼器

Args:
    encoder_config: 編碼器配置（配置名稱、配置字典或配置對象）
    
Returns:
    編碼器實例...

---

### create_decoder

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 創建解碼器

Args:
    decoder_config: 解碼器配置
    encoder_channels: 編碼器輸出通道數（如果配置中沒有）
    
Returns:
    解碼器實例...

---

### create_model

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 創建完整模型（編碼器+解碼器）

Args:
    model_config: 模型配置
    
Returns:
    完整模型實例...

---

### create_efficient_wrapper

**模組**: `AI模型建構訓練驗證/model_create/core/unified_registry.py`

**說明**: 無文檔說明...

---

### create_mamba_wrapper

**模組**: `AI模型建構訓練驗證/model_create/core/unified_registry.py`

**說明**: 無文檔說明...

---

### create_enhanced_wrapper

**模組**: `AI模型建構訓練驗證/model_create/core/unified_registry.py`

**說明**: 無文檔說明...

---

### create_model

**模組**: `AI模型建構訓練驗證/model_create/core/unified_registry.py`

**說明**: 無文檔說明...

---

### create_unified_dataset

**模組**: `AI模型建構訓練驗證/model_create/data/unified_dataset.py`

**說明**: 便捷函數：創建統一資料集

Args:
    data_path: 資料路徑
    dataset_type: 資料集類型 (自動檢測如果為None)
    task_type: 任務類型
    split: 資料集分割
    **kwargs: 額外配置
    
Returns:
    統一資料集實例...

---

### create_unified_dataloader

**模組**: `AI模型建構訓練驗證/model_create/data/unified_dataset.py`

**說明**: 便捷函數：創建統一資料載入器

Args:
    data_path: 資料路徑
    dataset_type: 資料集類型
    task_type: 任務類型
    split: 資料集分割
    batch_size: 批次大小
    **kwargs: 額外配置
    
Returns:
    DataLoader實例...

---

### create_dataset

**模組**: `AI模型建構訓練驗證/model_create/data/unified_dataset.py`

**說明**: 創建統一資料集

Args:
    config: 資料集配置
    split: 資料集分割
    
Returns:
    統一資料集實例...

---

### create_dataloader

**模組**: `AI模型建構訓練驗證/model_create/data/unified_dataset.py`

**說明**: 創建統一資料載入器

Args:
    config: 資料集配置
    split: 資料集分割
    **kwargs: DataLoader額外參數
    
Returns:
    DataLoader實例...

---

### create_multi_machine_ray_setup

**模組**: `AI模型建構訓練驗證/model_create/distributed/multi_machine_ray_setup.py`

**說明**: 創建多機Ray設置...

---

### create_distributed_trainer

**模組**: `AI模型建構訓練驗證/model_create/distributed/multi_machine_ray_setup.py`

**說明**: 創建分散式訓練器...

---

### create_multi_machine_training

**模組**: `AI模型建構訓練驗證/model_create/distributed/multi_machine_training_example.py`

**說明**: 創建多機訓練管理器...

---

### create_ray_training_system

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_ai_integration.py`

**說明**: 創建Ray訓練系統...

---

### create_search_space_for_model

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_ai_integration.py`

**說明**: 為特定模型類型創建搜索空間...

---

### create_distributed_training_function

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_ai_integration.py`

**說明**: 創建分散式訓練函數 - 整合現有UnifiedTrainer...

---

### create_objective_function

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_ai_integration.py`

**說明**: 創建優化目標函數...

---

### create_ray_search_space

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_integration.py`

**說明**: 創建Ray Tune搜索空間...

---

### create_distributed_trainer

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_integration.py`

**說明**: 創建分散式訓練器...

---

### create_tuning_config

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_integration.py`

**說明**: 創建調優配置...

---

### create_distributed_dataset

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_integration.py`

**說明**: 創建分散式資料集...

---

### create_encoder

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 便捷函數：創建編碼器

Args:
    encoder_name: 編碼器名稱
    encoder_type: 編碼器類型
    task_type: 任務類型
    **kwargs: 額外配置
    
Returns:
    編碼器包裝器實例...

---

### create_cnn_encoder

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 創建CNN編碼器...

---

### create_vit_encoder

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 創建VIT編碼器...

---

### create_mamba_encoder

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 創建Mamba編碼器...

---

### create_encoder

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 創建編碼器

Args:
    config: 編碼器配置
    
Returns:
    編碼器包裝器實例...

---

### create_confusion_matrix_evaluator

**模組**: `AI模型建構訓練驗證/model_create/evaluation/confusion_matrix.py`

**說明**: 創建混淆矩陣評估器...

---

### create_visualizer

**模組**: `AI模型建構訓練驗證/model_create/evaluation/confusion_matrix.py`

**說明**: 創建可視化器...

---

### create_enhanced_yolo_inference

**模組**: `AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py`

**說明**: 創建增強YOLO推理器...

---

### create_default_config

**模組**: `AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py`

**說明**: 創建預設配置...

---

### create_infrastructure_detector

**模組**: `AI模型建構訓練驗證/model_create/inference/object_detection.py`

**說明**: 創建基礎設施檢測器...

---

### create_manhole_detector

**模組**: `AI模型建構訓練驗證/model_create/inference/object_detection.py`

**說明**: 創建孔蓋檢測器...

---

### create_road_damage_detector

**模組**: `AI模型建構訓練驗證/model_create/inference/object_detection.py`

**說明**: 創建道路損壞檢測器...

---

### create_sam_fine_inference

**模組**: `AI模型建構訓練驗證/model_create/inference/sam_fine_inference.py`

**說明**: 創建SAM Fine-tuning推理器的工廠函數

參數:
    model_path: 模型權重路徑
    model_type: 模型類型
    **kwargs: 其他參數
    
返回:
    SAMFineInference實例...

---

### create_yolo_inference

**模組**: `AI模型建構訓練驗證/model_create/inference/yolo_inference.py`

**說明**: 創建YOLO推理器的工廠函數

參數:
    model_path: 模型權重路徑
    device: 計算設備
    conf_threshold: 置信度閾值
    iou_threshold: IoU閾值
    original_scale: 圖像比例因子
    class_names: 類別名稱字典
    
返回:
    YOLOInference實例...

---

### create_resnet_backbone

**模組**: `AI模型建構訓練驗證/model_create/neck/fpn.py`

**說明**: 創建 ResNet 骨幹網路...

---

### create_fpn

**模組**: `AI模型建構訓練驗證/model_create/neck/fpn.py`

**說明**: 創建 FPN 網路...

---

### create_mask_rcnn_fpn

**模組**: `AI模型建構訓練驗證/model_create/neck/fpn.py`

**說明**: 創建 Mask R-CNN 風格的 FPN...

---

### create_resnet_fpn

**模組**: `AI模型建構訓練驗證/model_create/neck/fpn.py`

**說明**: 創建完整的 ResNet + FPN 組合...

---

### create_csv_processor

**模組**: `AI模型建構訓練驗證/model_create/preprocessing/csv_processor.py`

**說明**: 創建CSV處理器的工廠函數

參數:
    aspect_ratio_threshold: 長寬比閾值
    area_ratio_threshold: 面積比閾值
    sample_rate: 抽樣比例
    
返回:
    CSVProcessor實例...

---

### create_fisheye_corrector

**模組**: `AI模型建構訓練驗證/model_create/preprocessing/fisheye_correction.py`

**說明**: 創建魚眼校正器的工廠函數

參數:
    parameters_dir: 參數目錄
    mtx_dir: 矩陣子目錄
    xy_mtx_filename: XY矩陣檔名
    
返回:
    FisheyeCorrector實例...

---

### create_trainer

**模組**: `AI模型建構訓練驗證/model_create/training/base_trainer.py`

**說明**: 創建訓練器實例

參數:
    name: 訓練器名稱
    **kwargs: 初始化參數
    
返回:
    訓練器實例...

---

### create_metrics_calculator

**模組**: `AI模型建構訓練驗證/model_create/training/metrics.py`

**說明**: 便捷函數：創建指標計算器

Args:
    task_type: 任務類型
    num_classes: 類別數量
    custom_config: 自定義配置
    legacy_metrics_fn: 現有指標函數
    
Returns:
    UnifiedMetricsCalculator: 統一指標計算器...

---

### create_metrics

**模組**: `AI模型建構訓練驗證/model_create/training/metrics.py`

**說明**: 創建指標計算器

Args:
    task_type: 任務類型 ('classification', 'segmentation', 'regression')
    config: 指標配置
    
Returns:
    BaseMetrics: 指標計算器實例...

---

### create_optimizer

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 創建優化器

Args:
    model: 要優化的模型
    config: 優化器配置
    
Returns:
    torch.optim.Optimizer: 優化器實例...

---

### create_optimizer_from_dict

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 從字典創建優化器

Args:
    model: 要優化的模型
    config_dict: 配置字典
    
Returns:
    torch.optim.Optimizer: 優化器實例...

---

### create_scheduler

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 創建學習率調度器

Args:
    optimizer: 優化器
    config: 調度器配置
    
Returns:
    torch.optim.lr_scheduler._LRScheduler: 調度器實例...

---

### create_scheduler_from_dict

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 從字典創建調度器

Args:
    optimizer: 優化器
    config_dict: 配置字典
    
Returns:
    torch.optim.lr_scheduler._LRScheduler: 調度器實例...

---

### create_sam_finetuning_trainer

**模組**: `AI模型建構訓練驗證/model_create/training/sam_finetuning_trainer.py`

**說明**: 創建SAM微調訓練器...

---

### create_sam_dataset

**模組**: `AI模型建構訓練驗證/model_create/training/sam_finetuning_trainer.py`

**說明**: 創建SAM資料集...

---

### create_yolo_trainer

**模組**: `AI模型建構訓練驗證/model_create/training/yolo_trainer.py`

**說明**: 創建YOLO訓練器的工廠函數

參數:
    model_name: 模型名稱
    **kwargs: 其他參數
    
返回:
    YOLOTrainer實例...

---

### create_checkpoint_manager

**模組**: `AI模型建構訓練驗證/model_create/util/checkpoint.py`

**說明**: 創建檢查點管理器的便捷函數...

---

### create_default_config

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 創建默認配置

Args:
    name: 配置名稱
    
Returns:
    默認配置對象...

---

### create_doc_generator

**模組**: `AI模型建構訓練驗證/model_create/util/doc_generator.py`

**說明**: 創建文檔生成器...

---

### create_image_processor

**模組**: `AI模型建構訓練驗證/model_create/util/image_processing.py`

**說明**: 創建圖像處理器實例（工廠函數）

參數:
    conf_threshold: 置信度閾值
    default_resize_ratio: 默認調整大小比例
    
返回:
    ImageProcessor實例...

---

### create_image_comparison

**模組**: `AI模型建構訓練驗證/model_create/util/image_processing.py`

**說明**: 創建圖像對比視圖

參數:
    img1_path: 第一張圖像路徑
    img2_path: 第二張圖像路徑
    output_path: 輸出路徑
    title1: 第一張圖像標題
    title2: 第二張圖像標題
    
返回:
    是否成功...

---

### create_loss

**模組**: `AI模型建構訓練驗證/model_create/util/losses.py`

**說明**: 創建損失函數...

---

### create_combined_loss

**模組**: `AI模型建構訓練驗證/model_create/util/losses.py`

**說明**: 從配置創建組合損失...

---

### create_keras_to_pytorch_converter

**模組**: `AI模型建構訓練驗證/model_create/util/model_converters.py`

**說明**: 創建 Keras 到 PyTorch 轉換器的工廠函數...

---

### create_pytorch_to_onnx_converter

**模組**: `AI模型建構訓練驗證/model_create/util/model_converters.py`

**說明**: 創建 PyTorch 到 ONNX 轉換器的工廠函數...

---

### create_converter

**模組**: `AI模型建構訓練驗證/model_create/util/model_converters.py`

**說明**: 創建模型轉換器...

---

### create_vision_mamba_tiny

**模組**: `AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py`

**說明**: 創建Vision Mamba Tiny模型...

---

### create_vision_mamba_small

**模組**: `AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py`

**說明**: 創建Vision Mamba Small模型...

---

### create_vision_mamba_base

**模組**: `AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py`

**說明**: 創建Vision Mamba Base模型...

---

### create_unified_csp_iformer

**模組**: `AI模型建構訓練驗證/model_create/encoder/VIT/unified_csp_iformer.py`

**說明**: 創建統一的CSP_IFormer模型

Args:
    variant: 變體類型 ('final', 'channel_shuffle', 'dropkey', 'cs_dk', 'efficient', 'mamba', 'enhanced')
    mode: 模式 ('segmentation', 'classification')
    **kwargs: 額外配置參數
    ...

---

### create_segman_model

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Convenience function to create SegMAN models

Args:
    variant: Model variant ('original', 'mambavision', 'efficient', 'enhanced')
    size: Model size ('nano', 'tiny', 'small', 'medium', 'base', 'la...

---

### create_encoder

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Create SegMAN encoder

Args:
    variant: Model variant ('original', 'mambavision', 'efficient', 'enhanced')
    size: Model size ('nano', 'tiny', 'small', 'medium', 'base', 'large')
    config: Custo...

---

### create_decoder

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Create SegMAN decoder

Args:
    variant: Decoder variant
    in_channels: Input channels from encoder stages
    channels: Decoder channels
    **kwargs: Additional arguments
    
Returns:
    SegMAN...

---

### create_segmentation_model

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Create complete SegMAN segmentation model

Args:
    variant: Model variant
    size: Model size
    num_classes: Number of segmentation classes
    config: Custom configuration
    **kwargs: Addition...

---

### create_default_configs

**模組**: `資料前處理/shared/config_manager.py`

**說明**: 創建默認配置文件...

---

### create_processing_error

**模組**: `資料前處理/shared/exceptions.py`

**說明**: 創建特定類型的處理異常

Args:
    error_type: 錯誤類型
    message: 錯誤消息
    **kwargs: 其他參數
    
Returns:
    ProcessingError: 對應的異常實例...

---

### create_temp_dir

**模組**: `資料前處理/shared/file_utils.py`

**說明**: 創建臨時目錄

Args:
    prefix: 前綴
    suffix: 後綴
    
Returns:
    Path: 臨時目錄路徑...

---

### create_log_file_path

**模組**: `資料前處理/shared/logger_utils.py`

**說明**: 創建日誌文件路徑

Args:
    tool_name: 工具名稱
    base_dir: 基礎目錄
    
Returns:
    Path: 日誌文件路徑...

---

### create_logger

**模組**: `資料前處理/shared/logger_utils.py`

**說明**: 創建標準logger實例（類方法）

Args:
    name: 日誌記錄器名稱
    log_file: 日誌文件路徑（可選）
    
Returns:
    logging.Logger: 標準logger實例...

---

## 工廠類

### InferenceFactory

**模組**: `AI模型建構訓練驗證/model_create/core/base_inference.py`

**說明**: 推理器工廠類

用於創建不同類型的推理器...

---

### EnhancedModelFactory

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 增強模型工廠，整合所有新架構...

---

### ModelFactory

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 模型工廠類...

---

### SegMANFactoryModel

**模組**: `AI模型建構訓練驗證/model_create/core/unified_registry.py`

**說明**: 無文檔說明...

---

### UnifiedDatasetFactory

**模組**: `AI模型建構訓練驗證/model_create/data/unified_dataset.py`

**說明**: 統一資料集工廠...

---

### UnifiedEncoderFactory

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 統一編碼器工廠...

---

### TrainerFactory

**模組**: `AI模型建構訓練驗證/model_create/training/base_trainer.py`

**說明**: 訓練器工廠類

用於創建不同類型的訓練器...

---

### MetricsFactory

**模組**: `AI模型建構訓練驗證/model_create/training/metrics.py`

**說明**: 指標工廠...

---

### OptimizerFactory

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 優化器工廠...

---

### SchedulerFactory

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 學習率調度器工廠...

---

### LossFactory

**模組**: `AI模型建構訓練驗證/model_create/util/losses.py`

**說明**: 損失函數工廠類...

---

### ModelConverterFactory

**模組**: `AI模型建構訓練驗證/model_create/util/model_converters.py`

**說明**: 模型轉換器工廠...

---

### SegMANFactory

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Factory class for creating SegMAN model variants...

---

