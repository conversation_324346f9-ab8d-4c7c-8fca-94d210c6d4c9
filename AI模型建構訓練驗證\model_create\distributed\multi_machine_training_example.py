#!/usr/bin/env python3
"""
多台電腦Ray分散式訓練使用範例
展示如何在多台機器上分散式訓練AI模型
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader

try:
    import ray
    from ray import train
    from ray.train import ScalingConfig, RunConfig, CheckpointConfig
    from ray.train.torch import TorchTrainer
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

from multi_machine_ray_setup import MultiMachineRaySetup
from ..training.trainer import UnifiedTrainer, TrainingConfig
from ..core.model_factory import ModelFactory
from ..util.dataset import YOLODataset


def train_function_per_worker(config: Dict[str, Any]):
    """每個worker的訓練函數"""
    import torch
    import torch.distributed as dist
    from torch.nn.parallel import DistributedDataParallel as DDP
    
    # 獲取分散式資訊
    rank = train.get_context().get_world_rank()
    local_rank = train.get_context().get_local_rank()
    world_size = train.get_context().get_world_size()
    
    print(f"Worker {rank}/{world_size} 開始訓練...")
    
    # 設置設備
    device = torch.device(f"cuda:{local_rank}" if torch.cuda.is_available() else "cpu")
    torch.cuda.set_device(local_rank)
    
    # 創建模型
    model_factory = ModelFactory()
    model = model_factory.create_model(config["model_config"])
    model = model.to(device)
    
    # 包裝為分散式模型
    if world_size > 1:
        model = DDP(model, device_ids=[local_rank])
    
    # 創建優化器
    optimizer = optim.AdamW(model.parameters(), lr=config["learning_rate"])
    
    # 創建資料載入器
    train_dataset = YOLODataset(
        images_dir=config["datasets"]["train_images"],
        labels_dir=config["datasets"]["train_labels"],
        img_size=config["img_size"]
    )
    
    # 分散式採樣器
    train_sampler = torch.utils.data.distributed.DistributedSampler(
        train_dataset, 
        num_replicas=world_size,
        rank=rank,
        shuffle=True
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        sampler=train_sampler,
        num_workers=4,
        pin_memory=True
    )
    
    # 損失函數
    criterion = nn.CrossEntropyLoss()
    
    # 從checkpoint恢復
    checkpoint = train.get_checkpoint()
    if checkpoint:
        with checkpoint.as_directory() as checkpoint_dir:
            checkpoint_path = Path(checkpoint_dir) / "model.pt"
            if checkpoint_path.exists():
                state_dict = torch.load(checkpoint_path, map_location=device)
                model.load_state_dict(state_dict["model"])
                optimizer.load_state_dict(state_dict["optimizer"])
                start_epoch = state_dict["epoch"] + 1
                print(f"從第 {start_epoch} epoch 恢復訓練")
            else:
                start_epoch = 0
    else:
        start_epoch = 0
    
    # 訓練循環
    for epoch in range(start_epoch, config["num_epochs"]):
        model.train()
        total_loss = 0.0
        num_batches = 0
        
        # 設置epoch for sampler
        train_sampler.set_epoch(epoch)
        
        for batch_idx, (images, targets) in enumerate(train_loader):
            images = images.to(device)
            targets = targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 10 == 0:
                print(f"Worker {rank}, Epoch {epoch}, Batch {batch_idx}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / num_batches
        
        # 驗證（只在rank 0執行）
        if rank == 0:
            model.eval()
            val_accuracy = evaluate_model(model, config, device)
            
            # 報告指標
            train.report({
                "epoch": epoch,
                "train_loss": avg_loss,
                "val_accuracy": val_accuracy
            })
            
            # 保存checkpoint
            with train.get_context().get_trial_dir().as_posix() as trial_dir:
                checkpoint_dir = Path(trial_dir) / "checkpoints" / f"epoch_{epoch}"
                checkpoint_dir.mkdir(parents=True, exist_ok=True)
                
                torch.save({
                    "model": model.state_dict(),
                    "optimizer": optimizer.state_dict(),
                    "epoch": epoch,
                    "loss": avg_loss,
                    "accuracy": val_accuracy
                }, checkpoint_dir / "model.pt")
        else:
            # 其他worker只報告loss
            train.report({
                "epoch": epoch,
                "train_loss": avg_loss
            })


def evaluate_model(model, config: Dict[str, Any], device) -> float:
    """模型評估"""
    val_dataset = YOLODataset(
        images_dir=config["datasets"]["val_images"],
        labels_dir=config["datasets"]["val_labels"],
        img_size=config["img_size"]
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4
    )
    
    correct = 0
    total = 0
    
    with torch.no_grad():
        for images, targets in val_loader:
            images = images.to(device)
            targets = targets.to(device)
            
            outputs = model(images)
            _, predicted = torch.max(outputs.data, 1)
            total += targets.size(0)
            correct += (predicted == targets).sum().item()
    
    accuracy = 100 * correct / total
    return accuracy


class MultiMachineTraining:
    """多機訓練管理器"""
    
    def __init__(self, config_path: str = None):
        self.logger = logging.getLogger(__name__)
        self.ray_setup = MultiMachineRaySetup(config_path)
    
    def run_distributed_training(self, 
                                model_config: Dict[str, Any],
                                dataset_config: Dict[str, Any],
                                training_config: Dict[str, Any]) -> Any:
        """執行分散式訓練"""
        
        # 檢查集群狀態
        status = self.ray_setup.check_cluster_status()
        if not status["connected"]:
            raise RuntimeError(f"無法連接到Ray集群: {status.get('error', 'Unknown error')}")
        
        self.logger.info(f"集群資源: {status['total_cpus']} CPUs, {status['total_gpus']} GPUs")
        
        # 準備訓練配置
        train_config = {
            "model_config": model_config,
            "datasets": dataset_config,
            **training_config
        }
        
        # 創建分散式訓練器
        trainer = self.ray_setup.create_distributed_trainer(
            train_func=train_function_per_worker,
            datasets=dataset_config
        )
        
        # 更新訓練器配置
        trainer._train_loop_config = train_config
        
        self.logger.info("開始分散式訓練...")
        
        # 執行訓練
        result = trainer.fit()
        
        self.logger.info("分散式訓練完成")
        return result
    
    def run_hyperparameter_optimization(self,
                                      search_space: Dict[str, Any],
                                      base_config: Dict[str, Any],
                                      num_samples: int = 10) -> Any:
        """運行超參數優化"""
        from ray import tune
        from ray.tune.schedulers import ASHAScheduler
        from ray.tune.search.optuna import OptunaSearch
        
        # 創建搜索算法
        search_alg = OptunaSearch(metric="val_accuracy", mode="max")
        
        # 創建調度器
        scheduler = ASHAScheduler(
            metric="val_accuracy",
            mode="max",
            max_t=base_config.get("num_epochs", 100),
            grace_period=10,
            reduction_factor=2
        )
        
        # 創建Tuner
        tuner = tune.Tuner(
            tune.with_resources(
                tune.with_parameters(train_function_per_worker),
                resources=self.ray_setup.config["training"]["resources_per_worker"]
            ),
            param_space={**base_config, **search_space},
            tune_config=tune.TuneConfig(
                search_alg=search_alg,
                scheduler=scheduler,
                num_samples=num_samples,
                max_concurrent_trials=2
            ),
            run_config=RunConfig(
                name="hyperparameter_optimization",
                storage_path="/mnt/shared/tune_results"
            )
        )
        
        # 執行優化
        results = tuner.fit()
        
        # 獲取最佳結果
        best_result = results.get_best_result("val_accuracy", "max")
        
        self.logger.info(f"最佳配置: {best_result.config}")
        self.logger.info(f"最佳準確率: {best_result.metrics['val_accuracy']}")
        
        return results


def create_multi_machine_training(config_path: str = None) -> MultiMachineTraining:
    """創建多機訓練管理器"""
    return MultiMachineTraining(config_path)


# 使用範例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="多機分散式訓練")
    parser.add_argument("--config", help="Ray配置文件路徑")
    parser.add_argument("--action", choices=["train", "tune"], default="train")
    parser.add_argument("--model", default="csp_iformer", help="模型類型")
    parser.add_argument("--dataset", required=True, help="數據集路徑")
    
    args = parser.parse_args()
    
    # 模型配置
    model_config = {
        "model_type": args.model,
        "variant": "final_segmentation",
        "num_classes": 5,
        "backbone_config": {
            "enable_channel_shuffle": True,
            "enable_dropkey": True,
            "dropout_rate": 0.3
        }
    }
    
    # 數據集配置
    dataset_config = {
        "train_images": f"{args.dataset}/train/images",
        "train_labels": f"{args.dataset}/train/labels",
        "val_images": f"{args.dataset}/val/images",
        "val_labels": f"{args.dataset}/val/labels"
    }
    
    # 訓練配置
    training_config = {
        "num_epochs": 100,
        "batch_size": 8,
        "learning_rate": 0.001,
        "img_size": 640
    }
    
    # 創建訓練管理器
    trainer = create_multi_machine_training(args.config)
    
    if args.action == "train":
        # 分散式訓練
        result = trainer.run_distributed_training(
            model_config, dataset_config, training_config
        )
        print(f"訓練結果: {result}")
        
    elif args.action == "tune":
        # 超參數優化
        search_space = {
            "learning_rate": tune.loguniform(1e-4, 1e-2),
            "batch_size": tune.choice([4, 8, 16]),
            "dropout_rate": tune.uniform(0.1, 0.5)
        }
        
        results = trainer.run_hyperparameter_optimization(
            search_space, training_config, num_samples=20
        )
        print(f"優化結果: {results.get_best_result()}")