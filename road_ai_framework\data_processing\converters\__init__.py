#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重構後的標註轉換工具模組

提供模組化的標註格式轉換功能
"""

# 首先導入不需要PIL的模組
from .format_detector import FormatDetector

# 檢查PIL是否可用
try:
    import PIL
    _HAS_PIL = True
except ImportError:
    _HAS_PIL = False

# 條件導入需要PIL的模組
if _HAS_PIL:
    from .conversion_strategy import (
        ConversionStrategy,
        LabelMeToYOLOConverter,
        VOCToLabelMeConverter,
        YOLOToLabelMeConverter,
        COCOToLabelMeConverter
    )
    from .image_processor import ImageProcessor
    from .annotation_converter_v2 import AnnotationConverterV2
    
    __all__ = [
        'FormatDetector',
        'ConversionStrategy',
        'LabelMeToYOLOConverter',
        'VOCToLabelMeConverter',
        'YOLOToLabelMeConverter', 
        'COCOToLabelMeConverter',
        'ImageProcessor',
        'AnnotationConverterV2'
    ]
else:
    # PIL不可用時的佔位符
    class PILRequiredMixin:
        def __init__(self, *args, **kwargs):
            raise ImportError("此功能需要安裝PIL/Pillow: pip install Pillow")
    
    ConversionStrategy = PILRequiredMixin
    LabelMeToYOLOConverter = PILRequiredMixin
    VOCToLabelMeConverter = PILRequiredMixin
    YOLOToLabelMeConverter = PILRequiredMixin
    COCOToLabelMeConverter = PILRequiredMixin
    ImageProcessor = PILRequiredMixin
    AnnotationConverterV2 = PILRequiredMixin
    
    __all__ = [
        'FormatDetector',
        'ConversionStrategy',
        'LabelMeToYOLOConverter',
        'VOCToLabelMeConverter',
        'YOLOToLabelMeConverter', 
        'COCOToLabelMeConverter',
        'ImageProcessor',
        'AnnotationConverterV2'
    ]