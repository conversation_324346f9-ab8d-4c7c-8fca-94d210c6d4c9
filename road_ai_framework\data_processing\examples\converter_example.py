#!/usr/bin/env python3
"""
轉換器使用示例
展示如何使用現代化的資料格式轉換功能
"""

from pathlib import Path
import sys

# 添加模組路徑
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.converters.annotation_converter import AnnotationConverter
from data_processing.core.config_manager import ConfigManager

def main():
    """主要示例函數"""
    
    # 設定轉換參數
    config = {
        'input_dir': './input_data',
        'output_dir': './output_data', 
        'input_format': 'labelme',
        'output_format': 'yolo',
        'batch_size': 100,
        'enable_validation': True,
        'num_workers': 4
    }
    
    # 創建轉換器
    converter = AnnotationConverter(
        input_dir=config['input_dir'],
        output_dir=config['output_dir'],
        input_format=config['input_format'],
        output_format=config['output_format'],
        config=config
    )
    
    # 執行轉換
    print("🚀 開始格式轉換...")
    print(f"📁 輸入格式: {config['input_format']}")
    print(f"📁 輸出格式: {config['output_format']}")
    
    try:
        result = converter.run()
        
        # 顯示結果
        print("\n✅ 轉換完成！")
        print(f"📊 處理文件數: {result.get('total_files', 0)}")
        print(f"✅ 成功轉換: {result.get('success_count', 0)}")
        print(f"❌ 失敗數量: {result.get('error_count', 0)}")
        print(f"⏱️ 處理時間: {result.get('duration', 0):.2f}秒")
        
    except Exception as e:
        print(f"❌ 轉換失敗: {e}")

if __name__ == "__main__":
    main()