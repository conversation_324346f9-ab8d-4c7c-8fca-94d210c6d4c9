import matplotlib.pyplot as plt
from ultralytics import YOLO
from pathlib import Path
from datetime import datetime
import logging
import sys
import csv
import shutil
import time
import io
import numpy as np
import cv2
import re
import json
import itertools
import yaml


class StreamToLogger(io.StringIO):
    def __init__(self, logger, level):
        super().__init__()
        self.logger = logger
        self.level = level

    def write(self, message):
        if message.rstrip() != "":
            self.logger.log(self.level, message.rstrip())

    def flush(self):
        pass

# 在程式最前面做
logging.basicConfig(level=logging.INFO)
custom_logger = logging.getLogger("yolo_stdout_redirect")
sl = StreamToLogger(custom_logger, logging.DEBUG)
sys.stdout = sl

# 強制設定 matplotlib 字型為 微軟正黑體（如有需要可調整）
plt.rcParams['font.family'] = 'Microsoft JhengHei'
plt.rcParams['axes.unicode_minus'] = False  # 避免負號亂碼

class YoloTaskRunner:
    def __init__(self, use_trained_model=False):
        self.task_mode = "segment"  # 強制為 segment 模式
        self.use_trained_model = use_trained_model
        
        # 初始化時不立即載入模型，因為 data.yaml 可能還不存在
        self.model = None
        self.data_yaml = None
        
        self.output_dir = None
        self.summary_records = []
        self._init_logger()

    def labelme_to_yolo_dataset(self, base_dir: str, class_map: dict):
        """
        將包含 train/val 子資料夾的 LabelMe 資料集轉換為 YOLO 分割格式，並在原地產生 data.yaml。
        - base_dir: 包含 'train' 和 'val' 子資料夾的基礎路徑。
                    每個子資料夾內應有 .json 和對應的影像檔。
        - class_map: 一個字典，將 LabelMe 中的類別名稱對應到 YOLO 的 class_id。
        """
        self.logger.info(f"🚀 開始 LabelMe 資料集到 YOLO 格式的轉換...")
        base_path = Path(base_dir)
        self.logger.info(f"基礎資料夾: {base_path}")

        subfolders = [d for d in base_path.iterdir() if d.is_dir() and d.name in ['train', 'val']]
        if not subfolders:
            self.logger.error(f"❌ 在 {base_path} 中找不到 'train' 或 'val' 子資料夾。")
            return None

        for folder_path in subfolders:
            self.logger.info(f"--- 正在處理子資料夾: {folder_path.name} ---")
            output_images_path = folder_path / "images"
            output_labels_path = folder_path / "labels"
            output_images_path.mkdir(exist_ok=True)
            output_labels_path.mkdir(exist_ok=True)

            json_files = list(folder_path.glob("*.json"))
            if not json_files:
                self.logger.warning(f"⚠️ 在 {folder_path} 中找不到任何 .json 檔案，跳過。")
                continue

            conversion_count = 0
            for json_file in json_files:
                try:
                    with open(json_file, "r", encoding="utf-8") as f:
                        data = json.load(f)

                    image_filename = data.get("imagePath")
                    if not image_filename:
                        self.logger.warning(f"⚠️ {json_file.name} 缺少 'imagePath'，跳過。")
                        continue
                    
                    source_image_path = folder_path / image_filename
                    if not source_image_path.exists():
                        self.logger.warning(f"⚠️ 找不到影像檔 {source_image_path}，跳過。")
                        continue

                    # 將影像檔移動到 images/ 子資料夾
                    dest_image_path = output_images_path / source_image_path.name
                    shutil.move(str(source_image_path), str(dest_image_path))

                    image_height = data["imageHeight"]
                    image_width = data["imageWidth"]
                    yolo_txt_path = output_labels_path / f"{json_file.stem}.txt"
                    
                    with open(yolo_txt_path, "w", encoding="utf-8") as yolo_f:
                        for shape in data["shapes"]:
                            label = shape["label"]
                            if label not in class_map:
                                self.logger.warning(f"⚠️ 在 {json_file.name} 中找到未知類別 '{label}'，跳過。")
                                continue
                            
                            class_id = class_map[label]
                            points = shape["points"]
                            normalized_points = [coord / (image_width if i % 2 == 0 else image_height) for i, coord in enumerate(itertools.chain(*points))]
                            line = f"{class_id} " + " ".join(map(str, normalized_points))
                            yolo_f.write(line + "\n")
                    
                    json_file.unlink() # 刪除已處理的 json
                    conversion_count += 1

                except Exception as e:
                    self.logger.error(f"❌ 處理 {json_file.name} 時發生錯誤: {e}")
            
            self.logger.info(f"✅ {folder_path.name} 子資料夾轉換完成！共處理了 {conversion_count} 個檔案。")

        # 產生 data.yaml
        self.logger.info("--- 正在產生 data.yaml ---")
        yaml_path = base_path / "data.yaml"
        
        yaml_content = {
            'train': str((base_path / 'train' / 'images').resolve()),
            'val': str((base_path / 'val' / 'images').resolve()),
            'nc': len(class_map),
            'names': list(class_map.keys())
        }

        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_content, f, allow_unicode=True, default_flow_style=False, sort_keys=False)
        
        self.logger.info(f"🎉🎉🎉 資料集轉換與設定完成！YAML 檔案已儲存至: {yaml_path}")
        return str(yaml_path)

    def _resolve_model_path(self):
        """根據 use_trained_model 選擇模型路徑。"""
        if self.use_trained_model:
            return r"D:\shiuming\new_road_crack\best.pt"
        else:
            return r"D:\路面缺陷資料集\YOLO\ultralytics\download_model\semantic_segmentation\yolo11x-seg.pt"

    def _init_logger(self):
        self.logger = logging.getLogger("YoloTaskRunner")
        self.logger.setLevel(logging.DEBUG)
        if self.logger.hasHandlers(): self.logger.handlers.clear()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        ch = logging.StreamHandler(sys.stdout)
        ch.setFormatter(formatter)
        self.logger.addHandler(ch)
        self.logger.info("📓 Logging initialized.")

    def _setup_run_environment(self, data_yaml_path: str):
        """設定單次執行的環境，包括輸出資料夾和檔案日誌。"""
        self.data_yaml = data_yaml_path
        model_path = self._resolve_model_path()
        self.model = YOLO(model_path)
        self.logger.info(f"已載入模型: {model_path}")
        self.logger.info(f"使用資料設定檔: {self.data_yaml}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"{Path(self.data_yaml).parent.name}_{timestamp}"
        self.output_dir = Path(r"D:\shiuming") / run_name
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 設定檔案日誌
        log_file = self.output_dir / "yolo_task.log"
        fh = logging.FileHandler(log_file, encoding="utf-8")
        fh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(fh)
        self.logger.info(f"📂 本次執行結果將儲存至：{self.output_dir}")

    def train(self, epochs=1, imgsz=640, batch=32, **kwargs):
        self.logger.info("🚀 開始訓練...")
        start = time.time()
        results = self.model.train(
            data=self.data_yaml, 
            epochs=epochs, 
            imgsz=imgsz, 
            batch=batch,
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=10.0,
            translate=0.1,
            scale=0.9,
            shear=2.0,
            perspective=0.0002,
            flipud=0,
            fliplr=0.5,
            erasing=0,
            mosaic=1.0,
            mixup=0.15,
            cutmix=0,
            patience=0,
            cos_lr=True,
            lr0=0.001,
            lrf=0.01,
            warmup_epochs=3,
            **kwargs)
        end = time.time()
        duration = end - start
        self.summary_records.append({"task": "train", "duration": duration})
        self.logger.info(f"⏱️ 訓練總耗時：{duration / 3600:.2f} 小時")
        self._copy_yolo_results(results, "train_run_results")
        self.logger.info("✅ 訓練完成！")

    def validate(self):
        self.logger.info("🔍 開始驗證...")
        start = time.time()
        metrics = self.model.val(data=self.data_yaml, plots=True)
        end = time.time()
        duration = end - start
        self.summary_records.append({"task": "validate", "duration": duration})
        self.logger.info(f"⏱️ 驗證耗時：{duration:.2f} 秒")
        self._save_metrics_to_csv(metrics, "val_metrics.csv")
        self._copy_yolo_results(metrics, "val_run_results")
        self.logger.info("✅ 驗證完成！")

    def run_selected_tasks(self, data_yaml_path: str, train=False, val=False, train_params=None):
        self._setup_run_environment(data_yaml_path)
        if train: self.train(**(train_params or {}))
        if val: self.validate()
        self._save_summary_csv()
        self.logger.info("🎉 所有任務執行完畢！")

    def _copy_yolo_results(self, results, subfolder):
        try:
            save_dir = getattr(results, "save_dir", None)
            if save_dir and Path(save_dir).exists():
                target_dir = self.output_dir / subfolder
                shutil.copytree(save_dir, target_dir, dirs_exist_ok=True)
                self.logger.info(f"📂 已複製 YOLO 輸出 {save_dir} → {target_dir}")
                shutil.rmtree(save_dir, ignore_errors=True)
            if Path("runs").exists(): shutil.rmtree("runs", ignore_errors=True)
        except Exception as e:
            self.logger.error(f"❌ 複製或清除 YOLO 輸出時發生錯誤: {e}")

    def _save_metrics_to_csv(self, metrics_obj, save_name):
        try:
            metrics = metrics_obj.seg
            if not metrics: self.logger.warning("⚠️ 無法提取分割評估指標"); return
            csv_path = self.output_dir / save_name
            with open(csv_path, "w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f)
                writer.writerow(["class", "precision", "recall", "mAP50", "mAP50-95"])
                for i, name in metrics_obj.names.items():
                    p, r, ap50, ap = metrics.class_result(i)
                    writer.writerow([name, round(p, 4), round(r, 4), round(ap50, 4), round(ap, 4)])
                writer.writerow(["all", round(metrics.mp, 4), round(metrics.mr, 4), round(metrics.map50, 4), round(metrics.map, 4)])
            self.logger.info(f"📄 已儲存評估結果至：{csv_path}")
        except Exception as e:
            self.logger.error(f"❌ 儲存 CSV 時發生錯誤: {e}")

    def _save_summary_csv(self):
        if not self.summary_records: return
        summary_path = self.output_dir / "summary.csv"
        try:
            with open(summary_path, "w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f)
                writer.writerow(["任務", "耗時 (秒)"])
                for record in self.summary_records:
                    writer.writerow([record["task"], f"{record['duration']:.2f}"])
            self.logger.info(f"📈 已儲存任務統計至：{summary_path}")
        except Exception as e:
            self.logger.error(f"❌ 儲存 summary.csv 時發生錯誤: {e}")

if __name__ == "__main__":
    # --- 工作流程 --- #
    # 步驟 1: 設定您的資料集路徑和類別
    # TODO: 請將此路徑改為您存放 train/ 和 val/ 資料夾的基礎目錄
    DATASET_BASE_DIR = r"D:\shiuming\new_road_crack\segment"
    
    # TODO: 請根據您的需求修改類別名稱和對應的 ID
    # **重要**: 這個映射的順序將決定 data.yaml 中的類別順序
    CLASS_MAP = {
        "Alligator_crack": 0, 
        "deformation": 1, 
        "dirt": 2,
        "expansion_joint": 3,
        "joint": 4, 
        "lane_line_linear": 5,
        "linear_crack": 6,
        "manhole": 7,
        "patch": 8,
        "patch_square": 9, 
        "potholes": 10,
    }

    # 步驟 2: 執行資料轉換並產生 data.yaml
    # 建立一個 Runner 實例來處理轉換任務
    converter = YoloTaskRunner()
    # 執行轉換，並取得產生的 yaml 檔案路徑
    generated_yaml_path = converter.labelme_to_yolo_dataset(
        base_dir=DATASET_BASE_DIR,
        class_map=CLASS_MAP
    )

    # 步驟 3: 使用產生的 data.yaml 進行訓練與驗證
    if generated_yaml_path:
        print("\n" + "="*50 + "\n")
        print("--- 開始執行訓練與驗證流程 ---")
        
        # 建立一個新的 Runner 實例來進行訓練
        trainer = YoloTaskRunner(use_trained_model=True) # False=從預訓練yolo11x-seg.pt開始, True=從自訂last.pt開始
        
        # 執行任務
        trainer.run_selected_tasks(
            data_yaml_path=generated_yaml_path,
            train=True,
            val=True,
            train_params={
                "epochs": 150, 
                "imgsz": 1280, 
                "batch": 4, 
                "device": 0,
            }
        )
    else:
        print("資料轉換失敗，無法進行訓練。")