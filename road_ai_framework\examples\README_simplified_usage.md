# 簡化YOLO推理系統 - 完整參數使用指南

## 🚀 概述

`simplified_yolo_usage.py` 是一個可直接運行的腳本，支援所有 **15個核心參數** 的完整配置和調整。相比原版的 enhanced_yolo_inference.py：

- **參數減少79%**: 從70+個參數簡化為15個核心參數
- **代碼簡化97%**: 從~100行配置代碼簡化為~3行
- **直觀配置**: 在腳本頂部直接修改所有參數，無需複雜的配置文件

## 📋 支援的15個核心參數

### 🔧 模型配置 (3個參數)
```python
detection_model_path = "path/to/your/yolo_detection.pt"      # YOLO檢測模型路徑
segmentation_model_path = "path/to/your/yolo_segmentation.pt" # YOLO分割模型路徑（優先使用）
device = "auto"                                              # 運算設備: "auto", "cpu", "cuda", "mps"
```

### ⚡ 推理配置 (4個參數)
```python
img_size = 640                    # 輸入圖像尺寸
global_conf = 0.25               # 全局置信度閾值 (0.0-1.0)
iou_threshold = 0.45             # IoU閾值 (0.0-1.0)
max_det = 1000                   # 最大檢測數量
```

### 🔄 SAHI配置 (5個參數)
```python
enable_sahi = False              # 是否啟用SAHI大圖像切片推理
slice_size = 512                 # SAHI切片大小
overlap_ratio = 0.2              # SAHI重疊比例 (0.0-1.0)
sahi_conf = 0.1                  # SAHI專用置信度閾值
sahi_iou = 0.5                   # SAHI專用IoU閾值
```

### 💾 輸出配置 (3個參數)
```python
save_visualizations = True       # 是否保存可視化結果
save_predictions = True          # 是否保存預測結果
save_statistics = True           # 是否保存統計信息
```

### 📁 輸入輸出路徑配置
```python
input_path = "./test_image"       # 輸入圖片路徑或資料夾
output_path = "./output"          # 輸出結果資料夾
```

### 🌍 全域類別設定 (可選)
```python
custom_class_names = [           # 自定義類別名稱，空列表表示自動檢測
    # "linear_crack_裂縫",
    # "potholes_坑洞",
    # "joint_路面接縫"
]

custom_label_aliases = {         # 標籤別名映射，空字典表示無映射
    # "crack": "linear_crack_裂縫",
    # "hole": "potholes_坑洞"
}
```

## 🎯 執行模式

腳本支援4種執行模式，通過 `run_mode` 參數控制：

```python
run_mode = "demo"                # 執行模式: "demo", "inference", "batch", "all"
```

### 📖 模式說明

| 模式 | 功能 | 適用場景 |
|------|------|----------|
| **demo** | 演示API使用方式 | 學習和理解簡化API |
| **inference** | 執行實際推理 | 單張圖片或小批量處理 |
| **batch** | 批次處理模式 | 大量圖片批次處理 |
| **all** | 完整功能展示 | 完整演示和實際推理 |

## 🚀 快速開始

### 1. 修改參數設定
```bash
# 編輯腳本頂部的參數設定區域
vim examples/simplified_yolo_usage.py

# 修改必要參數:
segmentation_model_path = "path/to/your/yolo11_seg.pt"
input_path = "./test_image"
output_path = "./output"
run_mode = "inference"
```

### 2. 直接運行
```bash
cd road_ai_framework/
python3 examples/simplified_yolo_usage.py
```

## 💡 使用範例

### 範例1: 基礎推理
```python
# 設定基本參數
segmentation_model_path = "yolo11n-seg.pt"
input_path = "./image.jpg"
global_conf = 0.3
run_mode = "inference"
```

### 範例2: 啟用SAHI大圖處理
```python
# 設定SAHI參數
segmentation_model_path = "yolo11n-seg.pt"
input_path = "./large_image.jpg"
enable_sahi = True
slice_size = 640
overlap_ratio = 0.2
run_mode = "inference"
```

### 範例3: 批次處理
```python
# 設定批次處理
segmentation_model_path = "yolo11n-seg.pt"
input_path = "./input_folder"
output_path = "./output_folder"
save_visualizations = True
save_statistics = True
run_mode = "batch"
```

### 範例4: 自定義類別
```python
# 設定自定義類別和別名
custom_class_names = [
    "linear_crack_裂縫",
    "potholes_坑洞",
    "joint_路面接縫"
]

custom_label_aliases = {
    "crack": "linear_crack_裂縫",
    "hole": "potholes_坑洞"
}

run_mode = "demo"
```

## 🔍 輸出結果

腳本執行後會生成：

1. **可視化結果** (`save_visualizations=True`)
   - 帶有檢測框和標籤的圖片
   - 保存在 `output_path/visualizations/`

2. **預測數據** (`save_predictions=True`)
   - JSON格式的檢測結果
   - 包含邊界框座標、置信度、類別信息

3. **統計信息** (`save_statistics=True`)
   - 檢測數量統計
   - 推理時間記錄
   - 性能指標

## 🆚 與原版對比

| 項目 | 原版 enhanced_yolo | 簡化版 simplified_yolo |
|------|-------------------|----------------------|
| **參數數量** | 70+ 個 | 15 個 (-79%) |
| **配置方式** | 複雜配置文件 | 頂部直觀設定 |
| **代碼行數** | ~100 行 | ~3 行 (-97%) |
| **學習成本** | 高 | 低 |
| **維護難度** | 複雜 | 簡單 |
| **功能完整性** | ✅ 完整 | ✅ 完整保留 |

## 🛠️ 進階使用

### 程式化調用

```python
from models.inference.simplified import SimplifiedYOLO, SimplifiedYOLOConfig

# 使用完整配置
config = SimplifiedYOLOConfig(
    segmentation_model_path="model.pt",
    global_conf=0.3,
    enable_sahi=True,
    slice_size=512,
    save_visualizations=True
)

yolo = SimplifiedYOLO(config)
result = yolo.predict("image.jpg")
```

### 快速API

```python
from models.inference.simplified import quick_predict, quick_batch

# 快速單張推理
result = quick_predict("model.pt", "image.jpg", confidence=0.3)

# 快速批次處理
results = quick_batch("model.pt", "input_dir", "output_dir")
```

## 🔧 故障排除

### 常見問題

1. **模型路徑錯誤**
   ```
   ❌ 模型路徑不存在: path/to/your/model.pt
   ```
   **解決**: 修改 `segmentation_model_path` 或 `detection_model_path` 為正確路徑

2. **輸入路徑錯誤**
   ```
   ❌ 輸入路徑不存在: ./test_image
   ```
   **解決**: 修改 `input_path` 為存在的圖片或資料夾路徑

3. **依賴缺失**
   ```
   ❌ 導入失敗: No module named 'numpy'
   ```
   **解決**: 安裝必要依賴
   ```bash
   pip install numpy opencv-python ultralytics torch torchvision
   ```

### 調試建議

1. **先運行demo模式** 確認API正常
2. **檢查路徑設定** 確保模型和圖片路徑正確
3. **逐步增加複雜度** 從基礎參數開始，逐步添加SAHI等功能

## 📞 技術支援

- **範例腳本**: `examples/simplified_yolo_usage.py`
- **測試套件**: `tests/test_simplified_yolo.py`
- **配置文檔**: `models/inference/simplified/config/`
- **API文檔**: `models/inference/simplified/__init__.py`

## 🎉 總結

簡化YOLO推理系統成功將複雜的70+參數配置簡化為15個核心參數，同時保持了所有核心功能。通過直觀的頂部參數設定和多種執行模式，讓YOLO推理變得更加簡單和高效。

**立即開始**: 修改腳本頂部參數，直接運行即可體驗簡化的YOLO推理！