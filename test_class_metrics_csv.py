#!/usr/bin/env python3
"""
測試 class_metrics.csv 生成功能
"""

def test_class_metrics_accumulation():
    """測試類別數據累積和CSV生成邏輯"""
    
    # 模擬 metrics 數據
    metrics_batch = [
        {
            'per_class': {
                'class_0': {'tp': 1, 'fp': 1, 'fn': 0, 'precision': 0.5, 'recall': 1.0, 'f1_score': 0.667, 'gt_count': 1, 'pred_count': 2},
                'class_1': {'tp': 1, 'fp': 0, 'fn': 0, 'precision': 1.0, 'recall': 1.0, 'f1_score': 1.0, 'gt_count': 1, 'pred_count': 1}
            }
        },
        {
            'per_class': {
                'class_0': {'tp': 0, 'fp': 1, 'fn': 1, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'gt_count': 1, 'pred_count': 1},
                'class_1': {'tp': 1, 'fp': 0, 'fn': 0, 'precision': 1.0, 'recall': 1.0, 'f1_score': 1.0, 'gt_count': 1, 'pred_count': 1}
            }
        }
    ]
    
    # 模擬 batch_class_data 初始化和累積
    batch_class_data = {}
    
    def accumulate_class_data(metrics, image_name):
        """模擬 _accumulate_class_data 方法"""
        if 'per_class' not in metrics:
            return
        
        for class_name, class_metrics in metrics['per_class'].items():
            if class_name not in batch_class_data:
                batch_class_data[class_name] = {
                    'total_images': 0,
                    'total_gt': 0,
                    'total_pred': 0,
                    'total_tp': 0,
                    'total_fp': 0,
                    'total_fn': 0,
                    'precision_sum': 0,
                    'recall_sum': 0,
                    'f1_sum': 0,
                    'images_with_class': []
                }
            
            batch_class_data[class_name]['total_images'] += 1
            batch_class_data[class_name]['total_gt'] += class_metrics.get('gt_count', 0)
            batch_class_data[class_name]['total_pred'] += class_metrics.get('pred_count', 0)
            batch_class_data[class_name]['total_tp'] += class_metrics.get('tp', 0)
            batch_class_data[class_name]['total_fp'] += class_metrics.get('fp', 0)
            batch_class_data[class_name]['total_fn'] += class_metrics.get('fn', 0)
            batch_class_data[class_name]['precision_sum'] += class_metrics.get('precision', 0)
            batch_class_data[class_name]['recall_sum'] += class_metrics.get('recall', 0)
            batch_class_data[class_name]['f1_sum'] += class_metrics.get('f1_score', 0)
            batch_class_data[class_name]['images_with_class'].append(image_name)
    
    print("=== 測試類別數據累積 ===")
    
    # 處理每個圖像的 metrics
    for i, metrics in enumerate(metrics_batch):
        image_name = f"image_{i+1}.jpg"
        print(f"\n處理 {image_name}:")
        print(f"  輸入 metrics: {metrics['per_class']}")
        
        accumulate_class_data(metrics, image_name)
        
        print(f"  累積後的 batch_class_data:")
        for class_name, data in batch_class_data.items():
            print(f"    {class_name}: TP={data['total_tp']}, FP={data['total_fp']}, FN={data['total_fn']}")
    
    print(f"\n=== 最終累積結果 ===")
    for class_name, data in batch_class_data.items():
        print(f"{class_name}:")
        print(f"  總圖像數: {data['total_images']}")
        print(f"  累積 TP: {data['total_tp']}")
        print(f"  累積 FP: {data['total_fp']}")
        print(f"  累積 FN: {data['total_fn']}")
        print(f"  總 GT: {data['total_gt']}")
        print(f"  總預測: {data['total_pred']}")
    
    # 模擬 CSV 生成
    print(f"\n=== 模擬 CSV 生成 ===")
    csv_lines = []
    csv_lines.append("各類別名稱,TP,FP,FN,Precision,Recall,F1,類別總數,誤判率,漏判率")
    
    # 使用已累積的 batch_class_data
    class_stats = {}
    for class_name, data in batch_class_data.items():
        class_stats[class_name] = {
            'tp': data['total_tp'],
            'fp': data['total_fp'], 
            'fn': data['total_fn']
        }
    
    for class_name, stats in class_stats.items():
        tp = stats['tp']
        fp = stats['fp']
        fn = stats['fn']
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        total_count = tp + fn  # 實際存在的該類別數量
        false_positive_rate = fp / (fp + tp) if (fp + tp) > 0 else 0  # 誤判率
        false_negative_rate = fn / (fn + tp) if (fn + tp) > 0 else 0  # 漏判率
        
        csv_line = f"{class_name},{tp},{fp},{fn},{precision:.3f},{recall:.3f},{f1:.3f},{total_count},{false_positive_rate:.3f},{false_negative_rate:.3f}"
        csv_lines.append(csv_line)
        
        print(f"{class_name}: TP={tp}, FP={fp}, FN={fn}, P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}")
    
    # 添加整體統計
    if class_stats:
        total_tp = sum(stats['tp'] for stats in class_stats.values())
        total_fp = sum(stats['fp'] for stats in class_stats.values())
        total_fn = sum(stats['fn'] for stats in class_stats.values())
        
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        
        overall_total = total_tp + total_fn
        overall_fpr = total_fp / (total_fp + total_tp) if (total_fp + total_tp) > 0 else 0
        overall_fnr = total_fn / (total_fn + total_tp) if (total_fn + total_tp) > 0 else 0
        
        csv_lines.append(f"整體平均,{total_tp},{total_fp},{total_fn},"
                       f"{overall_precision:.3f},{overall_recall:.3f},{overall_f1:.3f},"
                       f"{overall_total},{overall_fpr:.3f},{overall_fnr:.3f}")
        
        print(f"\n整體統計: TP={total_tp}, FP={total_fp}, FN={total_fn}")
        print(f"整體指標: P={overall_precision:.3f}, R={overall_recall:.3f}, F1={overall_f1:.3f}")
    
    print(f"\n=== 生成的 CSV 內容 ===")
    for line in csv_lines:
        print(line)
    
    # 驗證結果
    print(f"\n=== 驗證 ===")
    expected_class_0 = {'tp': 1, 'fp': 2, 'fn': 1}  # 圖像1: tp=1,fp=1,fn=0 + 圖像2: tp=0,fp=1,fn=1
    expected_class_1 = {'tp': 2, 'fp': 0, 'fn': 0}  # 圖像1: tp=1,fp=0,fn=0 + 圖像2: tp=1,fp=0,fn=0
    
    actual_class_0 = class_stats['class_0']
    actual_class_1 = class_stats['class_1']
    
    class_0_correct = (actual_class_0['tp'] == expected_class_0['tp'] and 
                      actual_class_0['fp'] == expected_class_0['fp'] and 
                      actual_class_0['fn'] == expected_class_0['fn'])
    
    class_1_correct = (actual_class_1['tp'] == expected_class_1['tp'] and 
                      actual_class_1['fp'] == expected_class_1['fp'] and 
                      actual_class_1['fn'] == expected_class_1['fn'])
    
    print(f"class_0 預期: {expected_class_0}, 實際: {actual_class_0}, 正確: {class_0_correct}")
    print(f"class_1 預期: {expected_class_1}, 實際: {actual_class_1}, 正確: {class_1_correct}")
    
    if class_0_correct and class_1_correct and len(csv_lines) > 1:
        print(f"\n✅ 類別數據累積和CSV生成功能正常！")
        print(f"✅ CSV 包含 {len(csv_lines)-1} 個類別的數據")
    else:
        print(f"\n❌ 功能有問題，需要檢查邏輯")

if __name__ == "__main__":
    test_class_metrics_accumulation()