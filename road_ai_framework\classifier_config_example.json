{"data_dir": "./data", "output_dir": "./classifier_output", "num_classes": 10, "model_name": "efficientnet_v2_l", "image_size": 480, "pretrained": true, "batch_size": 16, "num_epochs": 100, "learning_rate": 0.001, "weight_decay": 0.0001, "optimizer": "adamw", "scheduler": "cosine", "warmup_epochs": 5, "mixed_precision": true, "gradient_clipping": 1.0, "early_stopping_patience": 15, "use_advanced_augmentation": true, "cutmix_prob": 0.5, "mixup_alpha": 0.2, "device": "auto", "num_workers": 4, "pin_memory": true, "test_time_augmentation": false, "save_best_only": true, "save_predictions": true}