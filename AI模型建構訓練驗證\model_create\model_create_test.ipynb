{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "c:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\timm\\models\\layers\\__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers\n", "  warnings.warn(f\"Importing from {__name__} is deprecated, please import via timm.layers\", FutureWarning)\n", "c:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\timm\\models\\registry.py:4: FutureWarning: Importing from timm.models.registry is deprecated, please import via timm.models\n", "  warnings.warn(f\"Importing from {__name__} is deprecated, please import via timm.models\", FutureWarning)\n", "d:\\99_AI_model\\AI模型建構訓練驗證\\model_create\\full_model\\segman\\segman_encoder.py:79: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.\n", "  @torch.cuda.amp.custom_fwd\n", "d:\\99_AI_model\\AI模型建構訓練驗證\\model_create\\full_model\\segman\\segman_encoder.py:87: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.\n", "  @torch.cuda.amp.custom_bwd\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "from full_model.segman.segman_encoder import SegMANEncoder_s\n", "from full_model.segman.segman_decoder import SegMANDecoder\n", "# from torchsummary import summary\n", "# # encoder\n", "# from Models.incepformer import IncepTransformer\n", "# from Models.CSP_IFormer_final_SegMode import iformer_small as CSP_iformer_seg\n", "# from Models.CSP_IFormer_final_ClsMode import iformer_small as CSP_iformer_cls\n", "# decoder\n", "# from decoder.BiFPN_2_CSPIformer import BiFPNDecoder\n", "# from decoder.Unet import UnetDecoder\n", "# from decoder.up_concat import UpConcat\n", "# from decoder.FPN import FPNDecoder\n", "# from decoder.Unet import UnetDecoder\n", "# from decoder.BiFPN import BiFPN\n", "# from decoder.BiFPN_1 import BiFPN as BiFPN_1\n", "# segmentation head\n", "# from head.heads import SegmentationHead\n", "# segmentation concat\n", "# from util.encoder_decoder_cat import Encoder_decoder\n", "# from Models.Transception import Transception"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["size = 256\n", "device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')\n", "inputs = torch.rand(1, 3, 224, 224).to(device)\n", "device"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Full model\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# model = Transception(num_classes=2, head_count=8, dil_conv = 1, token_mlp_mode=\"mix_skip\", concat='sk')\n", "\n", "# print(model(inputs).shape)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# encoder\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["encoder = SegMANEncoder_s().to(device)\n", "out = encoder(inputs)\n", "for o in out:\n", "    print(o.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from thop import profile\n", "\n", "# flops, params = profile(encoder, inputs=(inputs,))\n", "# print('params：',params)\n", "# print('FLOPS：',flops)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# decoder\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1 = torch.randn([1, 64, 64, 64])\n", "c2 = torch.randn([1, 128, 32, 32])\n", "c3 = torch.randn([1, 320, 16, 16])\n", "c4 = torch.randn([1, 512, 8, 8])\n", "\n", "feats = [c1, c2, c3, c4]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["decoder = UpConcat(encoder_channels=[64, 128, 320, 512]).to(device)\n", "output = decoder(out)\n", "print(output.shape)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# head\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["seghead = SegmentationHead(in_channels=128, out_channels=2,\n", "                           kernel_size=3, activation=None, upsampling=1).to(device)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# finish model\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = Encoder_decoder(encoder, decoder, seghead).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out = model(inputs).to(device)\n", "print(out.shape)"]}], "metadata": {"kernelspec": {"display_name": "night1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}