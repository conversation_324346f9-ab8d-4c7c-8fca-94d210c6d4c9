"""
重構模組功能測試腳本
測試dataset、checkpoint、losses模組的基本功能
"""

import torch
import torch.nn as nn
import numpy as np
import logging
import tempfile
import os
from pathlib import Path

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dataset_module():
    """測試dataset模組"""
    logger.info("=== 測試Dataset模組 ===")
    
    try:
        from .dataset import DatasetConfig, ImageProcessor, FontManager
        logger.info("✓ Dataset模組導入成功")
        
        # 測試DatasetConfig
        config = DatasetConfig(
            data_dir="/tmp/test_data",
            size=224,
            split="train"
        )
        assert config.size == 224
        logger.info("✓ DatasetConfig創建成功")
        
        # 測試ImageProcessor
        test_image = np.random.rand(3, 224, 224).astype(np.float32)
        denorm_image = ImageProcessor.denormalize(test_image)
        assert denorm_image.shape == (224, 224, 3)
        assert denorm_image.dtype == np.uint8
        logger.info("✓ ImageProcessor.denormalize功能正常")
        
        # 測試向後兼容的導入
        from . import YOLODataset, LabelmeDataset, convert_labelme_to_yolo
        logger.info("✓ 向後兼容導入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Dataset模組測試失敗: {e}")
        return False

def test_checkpoint_module():
    """測試checkpoint模組"""
    logger.info("=== 測試Checkpoint模組 ===")
    
    try:
        from .checkpoint import CheckpointManager, find_latest_checkpoint, load_checkpoint
        logger.info("✓ Checkpoint模組導入成功")
        
        # 創建臨時目錄用於測試
        with tempfile.TemporaryDirectory() as temp_dir:
            # 測試CheckpointManager
            manager = CheckpointManager(temp_dir, max_checkpoints=2)
            assert manager.checkpoint_dir == Path(temp_dir)
            logger.info("✓ CheckpointManager創建成功")
            
            # 創建一個簡單的測試模型
            model = nn.Linear(10, 1)
            optimizer = torch.optim.Adam(model.parameters())
            
            # 測試保存檢查點
            checkpoint_path = manager.save_checkpoint(
                model=model,
                epoch=1,
                metrics={'loss': 0.5, 'accuracy': 0.8},
                optimizer=optimizer,
                model_name="test_model"
            )
            assert os.path.exists(checkpoint_path)
            logger.info("✓ 檢查點保存成功")
            
            # 測試載入檢查點
            new_model = nn.Linear(10, 1)
            epoch, metrics, extra_state = manager.load_checkpoint(
                model=new_model,
                optimizer=optimizer
            )
            assert epoch == 1
            assert 'loss' in metrics
            logger.info("✓ 檢查點載入成功")
            
            # 測試向後兼容函數
            latest_dir = find_latest_checkpoint(temp_dir, "test_model")
            logger.info("✓ 向後兼容函數正常")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Checkpoint模組測試失敗: {e}")
        return False

def test_losses_module():
    """測試losses模組"""
    logger.info("=== 測試Losses模組 ===")
    
    try:
        from .losses import (
            DiceLoss, FocalLoss, BoundaryAwareLoss, IoULoss,
            StagedRoadDamageLoss, CombinedLoss, LossFactory,
            cross_entropy2d
        )
        logger.info("✓ Losses模組導入成功")
        
        # 創建測試數據
        batch_size, num_classes, height, width = 2, 4, 64, 64
        inputs = torch.randn(batch_size, num_classes, height, width)
        targets = torch.randint(0, num_classes, (batch_size, height, width))
        
        # 測試DiceLoss
        dice_loss = DiceLoss()
        loss_value = dice_loss(inputs, targets)
        assert loss_value.requires_grad
        logger.info("✓ DiceLoss功能正常")
        
        # 測試FocalLoss
        focal_loss = FocalLoss()
        loss_value = focal_loss(inputs, targets)
        assert loss_value.requires_grad
        logger.info("✓ FocalLoss功能正常")
        
        # 測試BoundaryAwareLoss
        boundary_loss = BoundaryAwareLoss()
        loss_value = boundary_loss(inputs, targets)
        assert isinstance(loss_value, torch.Tensor)
        logger.info("✓ BoundaryAwareLoss功能正常")
        
        # 測試組合損失
        loss_config = {
            'dice': {'type': 'dice', 'weight': 0.6},
            'focal': {'type': 'focal', 'weight': 0.4}
        }
        combined_loss = LossFactory.create_combined_loss(loss_config)
        loss_value = combined_loss(inputs, targets)
        assert loss_value.requires_grad
        logger.info("✓ CombinedLoss功能正常")
        
        # 測試分階段損失
        staged_loss = StagedRoadDamageLoss(total_epochs=100)
        staged_loss.update_epoch(10)
        loss_value = staged_loss(inputs, targets)
        assert loss_value.requires_grad
        logger.info("✓ StagedRoadDamageLoss功能正常")
        
        # 測試損失組件獲取
        components = staged_loss.get_loss_components(inputs, targets)
        assert isinstance(components, dict)
        assert 'ce_loss' in components
        logger.info("✓ 損失組件獲取功能正常")
        
        # 測試便捷函數
        ce_loss = cross_entropy2d(inputs, targets)
        assert ce_loss.requires_grad
        logger.info("✓ cross_entropy2d功能正常")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Losses模組測試失敗: {e}")
        return False

def test_unified_imports():
    """測試統一導入接口"""
    logger.info("=== 測試統一導入接口 ===")
    
    try:
        # 測試從util模組統一導入
        from . import (
            YOLODataset, LabelmeDataset, 
            CheckpointManager, find_latest_checkpoint,
            DiceLoss, FocalLoss, LossFactory
        )
        logger.info("✓ 統一導入接口正常")
        return True
        
    except Exception as e:
        logger.error(f"✗ 統一導入測試失敗: {e}")
        return False

def run_all_tests():
    """執行所有測試"""
    logger.info("開始執行重構模組測試...")
    
    test_results = []
    
    # 執行各個測試
    test_results.append(("Dataset模組", test_dataset_module()))
    test_results.append(("Checkpoint模組", test_checkpoint_module()))
    test_results.append(("Losses模組", test_losses_module()))
    test_results.append(("統一導入", test_unified_imports()))
    
    # 輸出測試結果
    logger.info("\n=== 測試結果總結 ===")
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "通過" if result else "失敗"
        symbol = "✓" if result else "✗"
        logger.info(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed == total:
        logger.info("🎉 所有測試通過！重構成功！")
        return True
    else:
        logger.warning(f"⚠️  有 {total - passed} 個測試失敗，請檢查相關模組")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)