#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO推理模組

基於modified-code.py建構的YOLO分割推理系統
支援道路損壞檢測、結果視覺化和批次處理
"""

import os
import time
import gc
import sys
import locale
import logging
import cv2
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import glob
from tqdm import tqdm
import psutil
import csv
from typing import Dict, List, Optional, Tuple, Union
from ultralytics import YOLO


class YOLOInference:
    """
    YOLO分割推理器
    
    提供道路損壞檢測的完整推理管線
    支援結果視覺化、統計分析和批次處理
    """
    
    def __init__(self,
                 model_path: str,
                 device: str = "auto",
                 conf_threshold: float = 0.7,
                 iou_threshold: float = 0.5,
                 original_scale: float = 0.0166,
                 class_names: Optional[Dict[int, str]] = None):
        """
        初始化YOLO推理器
        
        參數:
            model_path: YOLO模型權重路徑
            device: 計算設備 ("cuda", "cpu", "auto")
            conf_threshold: 置信度閾值
            iou_threshold: IoU閾值
            original_scale: 原始圖像比例因子，每像素對應的實際距離(米)
            class_names: 類別名稱字典
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 設定參數
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.original_scale = original_scale
        
        # 設定設備
        if device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
        
        # 預設類別名稱
        self.class_names = class_names or {
            0: 'expansion_joint', 1: 'joint',
            2: 'linear_crack', 3: 'Alligator_crack',
            4: 'potholes', 5: 'patch', 6: 'manhole',
            7: 'deformation', 8: 'dirt', 9: 'lane_line_linear',10:'patch_square'
        }
        
        # 載入模型
        self.model = self._load_model()
        
        # 設定環境
        self._setup_environment()
        
        # 統計變數
        self.reset_statistics()
        
        self.logger.info("YOLO推理器初始化完成")
    
    def _load_model(self) -> YOLO:
        """載入YOLO模型"""
        try:
            model = YOLO(self.model_path)
            self.logger.info(f"成功載入模型: {self.model_path}")
            return model
        except Exception as e:
            self.logger.error(f"載入模型失敗: {e}")
            raise
    
    def _setup_environment(self):
        """設定環境"""
        # 設定編碼
        if sys.platform.startswith('win'):
            try:
                locale.setlocale(locale.LC_ALL, 'zh_TW.UTF-8')
            except:
                pass
        else:
            try:
                locale.setlocale(locale.LC_ALL, 'zh_TW.utf8')
            except:
                pass
        
        # 設定matplotlib中文支援
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        plt.ioff()  # 關閉互動模式
    
    def reset_statistics(self):
        """重置統計資料"""
        self.total_time = 0
        self.total_frames = 0
        self.record_count = 0
        self.initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    def cv2_chinese_text(self, 
                        img: np.ndarray, 
                        text: str, 
                        position: Tuple[int, int],
                        font_size: float = 0.7, 
                        color: Tuple[int, int, int] = (255, 255, 255),
                        thickness: int = 2) -> np.ndarray:
        """
        在OpenCV圖像上添加中文文字
        
        參數:
            img: 輸入圖像
            text: 文字內容
            position: 文字位置
            font_size: 字體大小
            color: 文字顏色
            thickness: 線條粗細
            
        返回:
            添加文字後的圖像
        """
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 將OpenCV圖像轉換為PIL圖像
            pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_img)
            
            # 獲取系統支援的字型
            try:
                if sys.platform.startswith('win'):
                    font_path = 'C:\\Windows\\Fonts\\msjh.ttc'
                elif sys.platform.startswith('darwin'):
                    font_path = '/System/Library/Fonts/PingFang.ttc'
                else:
                    font_path = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'
                font = ImageFont.truetype(font_path, int(font_size * 20))
            except:
                font = ImageFont.load_default()
            
            # 繪製文字
            draw.text(position, text, font=font, fill=color)
            
            # 將PIL圖像轉換回OpenCV圖像
            return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
            
        except ImportError:
            # 如果沒有PIL，使用OpenCV的putText (無中文支援)
            return cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                             font_size, color, thickness)
    
    def predict_single_image(self, 
                           image_path: str,
                           save_visualization: bool = True,
                           output_dir: Optional[str] = None) -> Dict:
        """
        對單張圖像進行推理
        
        參數:
            image_path: 圖像路徑
            save_visualization: 是否保存視覺化結果
            output_dir: 輸出目錄
            
        返回:
            推理結果字典
        """
        try:
            # 讀取圖像
            original_img = cv2.imread(image_path)
            if original_img is None:
                raise ValueError(f"無法讀取圖像: {image_path}")
            
            img_height, img_width = original_img.shape[:2]
            img_name = os.path.basename(image_path)
            
            # 模型預測
            start_time = time.time()
            results = self.model.predict(
                source=image_path,
                conf=self.conf_threshold,
                iou=self.iou_threshold,
                verbose=False
            )
            inference_time = time.time() - start_time
            
            # 更新統計
            self.total_time += inference_time
            self.total_frames += 1
            
            # 處理預測結果
            result = results[0]
            detections = self._process_detections(result, img_name, img_width, img_height)
            
            # 創建視覺化
            if save_visualization and output_dir:
                visualization_path = self._create_visualization(
                    original_img, result, detections, img_name, output_dir
                )
            else:
                visualization_path = None
            
            return {
                'image_path': image_path,
                'image_name': img_name,
                'inference_time': inference_time,
                'detections': detections,
                'visualization_path': visualization_path,
                'total_detections': len(detections),
                'total_area': sum(float(d['面積']) for d in detections)
            }
            
        except Exception as e:
            self.logger.error(f"推理單張圖像失敗 {image_path}: {e}")
            raise
    
    def _process_detections(self, 
                          result, 
                          img_name: str,
                          img_width: int, 
                          img_height: int) -> List[Dict]:
        """處理檢測結果"""
        detections = []
        
        # 檢查是否有分割掩碼
        if not hasattr(result, 'masks') or result.masks is None:
            self.logger.warning(f"圖像 {img_name} 沒有分割掩碼，請確保使用分割模型")
            return detections
        
        boxes = result.boxes
        masks = result.masks
        
        if len(boxes) == 0:
            self.logger.info(f"圖像 {img_name} 沒有檢測到任何物體")
            return detections
        
        # 處理每個檢測到的物體
        for i, c in enumerate(result):
            try:
                # 獲取類別ID和置信度
                cls_id = int(c.boxes.cls.tolist().pop())
                conf = float(c.boxes.conf.tolist().pop())
                
                # 跳過特定類別 (路面接縫和伸縮縫)
                if cls_id in [0, 1]:
                    continue
                
                # 獲取類別名稱
                class_name = self.class_names.get(cls_id, f"類別 {cls_id}")
                
                # 獲取框座標
                x1, y1, x2, y2 = c.boxes.xyxy.cpu().numpy().squeeze().astype(np.int32)
                
                # 創建二值掩碼
                b_mask = np.zeros((img_height, img_width), dtype=np.uint8)
                
                # 獲取輪廓
                try:
                    if hasattr(c.masks, 'xy') and len(c.masks.xy) > 0:
                        contour = c.masks.xy[0].astype(np.int32).reshape(-1, 1, 2)
                        cv2.drawContours(b_mask, [contour], -1, (255, 255, 255), cv2.FILLED)
                    else:
                        cv2.rectangle(b_mask, (x1, y1), (x2, y2), 255, -1)
                except Exception as e:
                    self.logger.warning(f"提取輪廓時出錯: {str(e)}")
                    cv2.rectangle(b_mask, (x1, y1), (x2, y2), 255, -1)
                
                # 計算掩碼面積
                area_px = np.sum(b_mask > 0)
                area_m2 = area_px * (self.original_scale ** 2)
                
                # 計算尺寸
                width_px = x2 - x1
                height_px = y2 - y1
                width_m = width_px * self.original_scale
                height_m = height_px * self.original_scale
                
                # 特殊處理：裂縫分類
                if cls_id == 3:  # 裂縫類別
                    class_name = self._classify_crack(width_px, height_px, area_px)
                
                # 增加記錄計數
                self.record_count += 1
                
                # 添加檢測結果
                detection = {
                    "序號": self.record_count,
                    "檔案名稱": img_name,
                    "類別": class_name,
                    "長": f"{height_m:.2f}",
                    "寬": f"{width_m:.2f}",
                    "面積": f"{area_m2:.2f}",
                    "置信度": f"{conf:.4f}",
                    "x_min": x1,
                    "y_min": y1,
                    "x_max": x2,
                    "y_max": y2
                }
                detections.append(detection)
                
            except Exception as e:
                self.logger.warning(f"處理物體 {i} 時出錯: {str(e)}")
                continue
        
        return detections
    
    def _classify_crack(self, width_px: int, height_px: int, area_px: int) -> str:
        """分類裂縫類型"""
        aspect_ratio_threshold = 0.8
        area_ratio_threshold = 0.4
        
        # 計算長寬比
        aspect_ratio = min(width_px, height_px) / max(width_px, height_px)
        
        # 計算框面積與掩碼面積的比率
        box_area = width_px * height_px
        mask_to_box_ratio = area_px / box_area if box_area > 0 else 0
        
        # 判斷是裂縫還是龜裂
        if (aspect_ratio < aspect_ratio_threshold and 
            mask_to_box_ratio < area_ratio_threshold):
            return "裂縫"
        else:
            return "龜裂"
    
    def _create_visualization(self, 
                            original_img: np.ndarray,
                            result,
                            detections: List[Dict],
                            img_name: str,
                            output_dir: str) -> str:
        """創建視覺化結果"""
        try:
            img_height, img_width = original_img.shape[:2]
            
            # 創建彩色掩碼圖像
            mask_img = np.zeros((img_height, img_width, 3), dtype=np.uint8)
            
            # 固定的顏色映射
            np.random.seed(42)
            colors = {}
            
            # 處理掩碼
            if hasattr(result, 'masks') and result.masks is not None:
                boxes = result.boxes
                
                # 為每個類創建隨機顏色
                unique_classes = set()
                for j in range(len(boxes)):
                    cls_id = int(boxes.cls[j].item())
                    if cls_id not in [0, 1]:  # 跳過特定類別
                        unique_classes.add(cls_id)
                
                for cls in unique_classes:
                    if cls not in colors:
                        colors[cls] = np.random.randint(0, 255, 3).tolist()
                
                # 繪製掩碼
                for i, c in enumerate(result):
                    try:
                        cls_id = int(c.boxes.cls.tolist().pop())
                        if cls_id in [0, 1]:
                            continue
                        
                        x1, y1, x2, y2 = c.boxes.xyxy.cpu().numpy().squeeze().astype(np.int32)
                        b_mask = np.zeros((img_height, img_width), dtype=np.uint8)
                        
                        if hasattr(c.masks, 'xy') and len(c.masks.xy) > 0:
                            contour = c.masks.xy[0].astype(np.int32).reshape(-1, 1, 2)
                            cv2.drawContours(b_mask, [contour], -1, (255, 255, 255), cv2.FILLED)
                        else:
                            cv2.rectangle(b_mask, (x1, y1), (x2, y2), 255, -1)
                        
                        color = colors[cls_id]
                        mask_img[b_mask > 0] = color
                        
                    except Exception as e:
                        continue
            
            # 創建組合圖像
            original_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
            
            # 創建掩碼疊加圖像
            alpha = 0.7
            overlay_img = original_rgb.copy()
            mask = np.any(mask_img > 0, axis=2)
            overlay_img[mask] = (1-alpha) * overlay_img[mask] + alpha * mask_img[mask]
            
            # 並排顯示
            combined_img = np.hstack((original_rgb, overlay_img))
            combined_img = cv2.cvtColor(combined_img, cv2.COLOR_RGB2BGR)
            
            # 添加圖例
            legend_y = 0
            for cls_id, color in colors.items():
                class_name = self.class_names.get(cls_id, f"類別 {cls_id}")
                
                # 繪製顏色方塊
                cv2.rectangle(combined_img,
                            (img_width * 2 - 400, legend_y + 50),
                            (img_width * 2 - 250, legend_y + 200),
                            color, -1)
                
                # 添加類別名稱
                combined_img = self.cv2_chinese_text(
                    combined_img, class_name,
                    (img_width * 2 - 240, legend_y + 100),
                    font_size=2.0, color=(0, 0, 0)
                )
                legend_y += 230
            
            # 添加標題
            combined_img = self.cv2_chinese_text(
                combined_img, "原始圖像", (img_width // 4, 30),
                font_size=2.0, color=(255, 255, 255)
            )
            combined_img = self.cv2_chinese_text(
                combined_img, "分割掩碼", (img_width + img_width // 4, 30),
                font_size=2.0, color=(255, 255, 255)
            )
            
            # 添加統計信息
            if detections:
                info_y = img_height - 80
                combined_img = self.cv2_chinese_text(
                    combined_img, f"掩碼數量: {len(detections)}",
                    (img_width + 20, info_y), font_size=0.8, color=(255, 255, 255)
                )
                
                total_area = sum(float(d['面積']) for d in detections)
                combined_img = self.cv2_chinese_text(
                    combined_img, f"總面積: {total_area:.2f} m²",
                    (img_width + 20, info_y + 25), font_size=0.8, color=(255, 255, 255)
                )
            
            # 保存圖像
            safe_filename = ''.join(
                c if c.isalnum() or c in '._- ' else '_' for c in img_name
            )
            save_path = os.path.join(output_dir, f'segmentation_{safe_filename}')
            cv2.imwrite(save_path, combined_img)
            
            return save_path
            
        except Exception as e:
            self.logger.error(f"創建視覺化失敗: {e}")
            return ""
    
    def batch_inference_with_csv(self,
                               csv_files: List[str],
                               image_dirs: List[str],
                               output_dir: str,
                               secondary_img_dir: Optional[str] = None) -> str:
        """
        批次推理處理CSV檔案中的圖像
        
        參數:
            csv_files: CSV檔案路徑列表
            image_dirs: 圖像目錄列表
            output_dir: 輸出目錄
            secondary_img_dir: 次要比對圖像目錄
            
        返回:
            最終統計CSV檔案路徑
        """
        try:
            # 確保輸出路徑存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 為CSV數據創建臨時目錄
            csv_temp_dir = os.path.join(output_dir, 'temp_csv')
            os.makedirs(csv_temp_dir, exist_ok=True)
            
            # 讀取所有CSV檔案
            all_images = self._load_csv_images(csv_files)
            
            # 建立次要圖像列表
            secondary_image_list = {}
            if secondary_img_dir:
                secondary_image_list = self._build_secondary_image_list(secondary_img_dir)
            
            self.logger.info(f"共需處理 {len(all_images)} 張圖像")
            
            # 重置統計
            self.reset_statistics()
            
            # 批次結果列表
            batch_results_list = []
            csv_counter = 0
            
            # 創建進度條
            progress_bar = tqdm(enumerate(all_images), 
                              total=len(all_images), desc="處理圖像")
            
            # 處理每個圖像
            for batch_idx, img_name in progress_bar:
                try:
                    # 尋找圖像檔案
                    img_path = self._find_image_file(img_name, image_dirs)
                    if not img_path:
                        self.logger.warning(f"找不到圖像: {img_name}")
                        continue
                    
                    # 執行推理
                    result = self.predict_single_image(
                        img_path, save_visualization=True, output_dir=output_dir
                    )
                    
                    # 添加檢測結果
                    if result['detections']:
                        batch_results_list.extend(result['detections'])
                    
                    # 定期保存臨時結果
                    if len(batch_results_list) >= 1000:
                        temp_df = pd.DataFrame(batch_results_list)
                        temp_csv_path = os.path.join(
                            csv_temp_dir, f'temp_{csv_counter:06d}.csv'
                        )
                        temp_df.to_csv(temp_csv_path, index=False, encoding='utf_8_sig')
                        csv_counter += 1
                        batch_results_list = []
                    
                    # 更新進度條
                    current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    memory_diff = current_memory - self.initial_memory
                    progress_bar.set_postfix(
                        Progress=f"{batch_idx+1}/{len(all_images)}",
                        Records=self.record_count,
                        Time=f"{result['inference_time']:.4f}s",
                        Memory=f"{current_memory:.2f}MB ({memory_diff:+.2f})"
                    )
                    
                    # 定期清理記憶體
                    if batch_idx % 50 == 0 and batch_idx > 0:
                        self._cleanup_memory()
                    
                except Exception as e:
                    self.logger.error(f"處理圖像 {img_name} 時發生錯誤: {str(e)}")
                    continue
            
            # 保存最後的批次結果
            if batch_results_list:
                temp_df = pd.DataFrame(batch_results_list)
                temp_csv_path = os.path.join(
                    csv_temp_dir, f'temp_{csv_counter:06d}.csv'
                )
                temp_df.to_csv(temp_csv_path, index=False, encoding='utf_8_sig')
            
            # 合併所有臨時CSV文件
            final_csv_path = self._merge_temp_csvs(csv_temp_dir, output_dir)
            
            # 保存性能指標
            self._save_performance_metrics(output_dir)
            
            self.logger.info(f"批次推理完成，結果已保存至: {final_csv_path}")
            return final_csv_path
            
        except Exception as e:
            self.logger.error(f"批次推理失敗: {e}")
            raise
    
    def _load_csv_images(self, csv_files: List[str]) -> List[str]:
        """載入CSV檔案中的圖像列表"""
        all_images = []
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                
                # 確定圖像欄位名稱
                img_col = '圖片' if '圖片' in df.columns else '檔案名稱'
                if img_col in df.columns:
                    all_images.extend(df[img_col].tolist())
                
            except Exception as e:
                self.logger.error(f"讀取CSV檔案失敗 {csv_file}: {e}")
        
        # 去除重複
        return list(set(all_images))
    
    def _build_secondary_image_list(self, secondary_img_dir: str) -> Dict[str, str]:
        """建立次要圖像列表"""
        secondary_image_list = {}
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            for img_path in glob.glob(os.path.join(secondary_img_dir, ext)):
                img_name = os.path.basename(img_path)
                secondary_image_list[img_name] = img_path
        return secondary_image_list
    
    def _find_image_file(self, img_name: str, image_dirs: List[str]) -> Optional[str]:
        """在多個目錄中尋找圖像檔案"""
        for img_dir in image_dirs:
            # 直接路徑檢查
            img_path = os.path.join(img_dir, img_name)
            if os.path.exists(img_path):
                return img_path
            
            # 遞迴搜尋
            for root, dirs, files in os.walk(img_dir):
                if img_name in files:
                    return os.path.join(root, img_name)
        
        return None
    
    def _cleanup_memory(self):
        """清理記憶體"""
        for _ in range(3):
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            time.sleep(0.1)
    
    def _merge_temp_csvs(self, temp_dir: str, output_dir: str) -> str:
        """合併臨時CSV文件"""
        all_files = glob.glob(os.path.join(temp_dir, 'temp_*.csv'))
        
        if not all_files:
            self.logger.warning("沒有找到臨時CSV文件")
            return ""
        
        all_files.sort()
        all_dfs = []
        
        for file in all_files:
            try:
                df = pd.read_csv(file, encoding='utf_8_sig')
                all_dfs.append(df)
            except Exception as e:
                self.logger.error(f"讀取文件 {file} 時發生錯誤: {str(e)}")
        
        if all_dfs:
            combined_df = pd.concat(all_dfs, ignore_index=True)
            output_path = os.path.join(output_dir, 'segment_statistics.csv')
            combined_df.to_csv(output_path, index=False, encoding='utf_8_sig')
            self.logger.info(f"已合併 {len(all_dfs)} 個臨時CSV文件，共 {len(combined_df)} 行數據")
            return output_path
        
        return ""
    
    def _save_performance_metrics(self, output_dir: str):
        """保存性能指標"""
        fps = self.total_frames / self.total_time if self.total_time > 0 else 0
        
        self.logger.info(f"推理性能指標:")
        self.logger.info(f"總推理時間: {self.total_time:.2f} 秒")
        self.logger.info(f"總幀數: {self.total_frames}")
        self.logger.info(f"平均FPS: {fps:.2f}")
        
        # 保存到CSV
        performance_path = os.path.join(output_dir, 'segment_performance.csv')
        with open(performance_path, 'w', newline='', encoding='utf_8_sig') as f:
            writer = csv.writer(f)
            writer.writerow(['總推理時間(秒)', '總幀數', '平均FPS'])
            writer.writerow([f"{self.total_time:.2f}", self.total_frames, f"{fps:.2f}"])
    
    def get_model_info(self) -> Dict:
        """獲取模型信息"""
        return {
            'model_path': self.model_path,
            'device': self.device,
            'conf_threshold': self.conf_threshold,
            'iou_threshold': self.iou_threshold,
            'original_scale': self.original_scale,
            'class_names': self.class_names,
            'total_classes': len(self.class_names)
        }


# 工廠函數
def create_yolo_inference(model_path: str,
                         device: str = "auto",
                         conf_threshold: float = 0.7,
                         iou_threshold: float = 0.5,
                         original_scale: float = 0.0166,
                         class_names: Optional[Dict[int, str]] = None) -> YOLOInference:
    """
    創建YOLO推理器的工廠函數
    
    參數:
        model_path: 模型權重路徑
        device: 計算設備
        conf_threshold: 置信度閾值
        iou_threshold: IoU閾值
        original_scale: 圖像比例因子
        class_names: 類別名稱字典
        
    返回:
        YOLOInference實例
    """
    return YOLOInference(
        model_path=model_path,
        device=device,
        conf_threshold=conf_threshold,
        iou_threshold=iou_threshold,
        original_scale=original_scale,
        class_names=class_names
    )


# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建YOLO推理器
    inference_engine = create_yolo_inference(
        model_path="yolo_model.pt",
        device="cuda",
        conf_threshold=0.7,
        iou_threshold=0.5
    )
    
    # 單張圖像推理
    result = inference_engine.predict_single_image(
        "test_image.jpg", 
        save_visualization=True,
        output_dir="./output"
    )
    
    print(f"推理結果: {result}")
    print(f"模型信息: {inference_engine.get_model_info()}")