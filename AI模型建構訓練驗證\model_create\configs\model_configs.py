"""
完整模型配置：定義編碼器+解碼器組合的完整模型配置
針對不同任務（道路損壞檢測、基礎設施監控等）提供預定義模型
"""

from typing import Dict, Any, List
from .encoder_configs import get_encoder_config
from .decoder_configs import get_decoder_config


def get_road_damage_model_config(variant: str = "csp_iformer_fpn") -> Dict[str, Any]:
    """
    獲取道路損壞檢測模型配置
    
    Args:
        variant: 模型變體名稱
        
    Returns:
        完整模型配置字典
    """
    
    variants = {
        "csp_iformer_fpn": {
            "model_name": "CSP_IFormer_FPN_RoadDamage",
            "task_type": "segmentation",
            "encoder": {
                "encoder_type": "csp_iformer",
                "variant": "final_segmentation",
                "img_size": 512,  # 道路圖像通常需要較高解析度
            },
            "decoder": {
                "decoder_type": "fpn",
                "variant": "road_damage",
                "num_classes": 5,  # 裂縫、坑洞、剝落、修補、正常
            },
            "training": {
                "loss_type": "combined",
                "loss_weights": {
                    "dice": 0.6,
                    "focal": 0.4
                },
                "optimizer": "adamw",
                "learning_rate": 1e-4,
                "weight_decay": 0.01,
                "scheduler": "cosine"
            }
        },
        
        "mobilenet_unet": {
            "model_name": "MobileNet_UNet_RoadDamage",
            "task_type": "segmentation",
            "encoder": {
                "encoder_type": "mobilenet",
                "variant": "csp_large",
            },
            "decoder": {
                "decoder_type": "unet_decoder",
                "variant": "default",
                "num_classes": 5,
            },
            "training": {
                "loss_type": "dice",
                "optimizer": "adam",
                "learning_rate": 2e-4,
                "scheduler": "step"
            }
        },
        
        "iformer_aspp": {
            "model_name": "IFormer_ASPP_RoadDamage",
            "task_type": "segmentation",
            "encoder": {
                "encoder_type": "iformer",
                "variant": "variant_1",
            },
            "decoder": {
                "decoder_type": "segmentation_head",
                "variant": "aspp",
                "num_classes": 5,
            }
        }
    }
    
    if variant not in variants:
        raise ValueError(f"未知的道路損壞模型變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return variants[variant]


def get_infrastructure_monitoring_config(variant: str = "csp_iformer_detection") -> Dict[str, Any]:
    """
    獲取基礎設施監控模型配置
    
    Args:
        variant: 模型變體名稱
        
    Returns:
        完整模型配置字典
    """
    
    variants = {
        "csp_iformer_detection": {
            "model_name": "CSP_IFormer_Detection_Infrastructure",
            "task_type": "detection",
            "encoder": {
                "encoder_type": "csp_iformer", 
                "variant": "final_classification",
                "img_size": 640,
            },
            "decoder": {
                "decoder_type": "detection_head",
                "variant": "road_elements",
                "num_classes": 15,  # 人孔蓋、排水系統、標誌等
            },
            "training": {
                "loss_type": "focal",
                "optimizer": "adamw",
                "learning_rate": 1e-4,
                "scheduler": "cosine"
            }
        },
        
        "mobilenet_yolo": {
            "model_name": "MobileNet_YOLO_Infrastructure",
            "task_type": "detection",
            "encoder": {
                "encoder_type": "mobilenet",
                "variant": "csp_large",
            },
            "decoder": {
                "decoder_type": "detection_head",
                "variant": "yolo",
                "num_classes": 15,
            }
        }
    }
    
    if variant not in variants:
        raise ValueError(f"未知的基礎設施監控變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return variants[variant]


def get_traffic_analysis_config(variant: str = "multi_task") -> Dict[str, Any]:
    """
    獲取交通分析模型配置
    
    Args:
        variant: 模型變體名稱
        
    Returns:
        完整模型配置字典
    """
    
    variants = {
        "multi_task": {
            "model_name": "MultiTask_Traffic_Analysis",
            "task_type": "multi_task",
            "encoder": {
                "encoder_type": "csp_iformer",
                "variant": "final_segmentation",
                "img_size": 512,
            },
            "decoders": {
                "segmentation": {
                    "decoder_type": "fpn",
                    "variant": "default",
                    "num_classes": 3,  # 道路、車輛、背景
                },
                "detection": {
                    "decoder_type": "detection_head", 
                    "variant": "road_elements",
                    "num_classes": 8,  # 車輛類別
                }
            },
            "training": {
                "loss_weights": {
                    "segmentation": 0.6,
                    "detection": 0.4
                },
                "optimizer": "adamw",
                "learning_rate": 1e-4,
                "scheduler": "cosine"
            }
        },
        
        "vehicle_detection": {
            "model_name": "Vehicle_Detection_Traffic",
            "task_type": "detection",
            "encoder": {
                "encoder_type": "csp_iformer",
                "variant": "final_classification",
            },
            "decoder": {
                "decoder_type": "detection_head",
                "variant": "yolo",
                "num_classes": 8,
            }
        }
    }
    
    if variant not in variants:
        raise ValueError(f"未知的交通分析變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return variants[variant]


def get_custom_model_config(encoder_config: Dict[str, Any], 
                          decoder_config: Dict[str, Any],
                          training_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    創建自定義模型配置
    
    Args:
        encoder_config: 編碼器配置
        decoder_config: 解碼器配置  
        training_config: 訓練配置（可選）
        
    Returns:
        完整模型配置字典
    """
    
    config = {
        "model_name": f"{encoder_config['encoder_type']}_{decoder_config['decoder_type']}_custom",
        "encoder": encoder_config,
        "decoder": decoder_config,
    }
    
    if training_config:
        config["training"] = training_config
    
    return config


def get_segmentation_model_config(encoder_type: str = "csp_iformer",
                                encoder_variant: str = "final_segmentation",
                                decoder_type: str = "fpn",
                                decoder_variant: str = "default",
                                num_classes: int = 1) -> Dict[str, Any]:
    """
    快速創建分割模型配置
    
    Args:
        encoder_type: 編碼器類型
        encoder_variant: 編碼器變體
        decoder_type: 解碼器類型
        decoder_variant: 解碼器變體
        num_classes: 類別數
        
    Returns:
        分割模型配置字典
    """
    
    encoder_config = get_encoder_config(encoder_type, encoder_variant)
    encoder_config["mode"] = "segmentation"
    
    decoder_config = get_decoder_config(decoder_type, decoder_variant)
    decoder_config["num_classes"] = num_classes
    
    return get_custom_model_config(encoder_config, decoder_config)


def get_detection_model_config(encoder_type: str = "csp_iformer",
                             encoder_variant: str = "final_classification", 
                             decoder_type: str = "detection_head",
                             decoder_variant: str = "default",
                             num_classes: int = 80) -> Dict[str, Any]:
    """
    快速創建檢測模型配置
    
    Args:
        encoder_type: 編碼器類型
        encoder_variant: 編碼器變體
        decoder_type: 解碼器類型
        decoder_variant: 解碼器變體
        num_classes: 類別數
        
    Returns:
        檢測模型配置字典
    """
    
    encoder_config = get_encoder_config(encoder_type, encoder_variant)
    encoder_config["mode"] = "classification"
    
    decoder_config = get_decoder_config(decoder_type, decoder_variant)
    decoder_config["num_classes"] = num_classes
    
    return get_custom_model_config(encoder_config, decoder_config)


def list_model_configs() -> Dict[str, List[str]]:
    """列出所有可用的模型配置"""
    return {
        "road_damage": [
            "csp_iformer_fpn", "mobilenet_unet", "iformer_aspp"
        ],
        "infrastructure_monitoring": [
            "csp_iformer_detection", "mobilenet_yolo"
        ],
        "traffic_analysis": [
            "multi_task", "vehicle_detection"
        ],
        "custom": [
            "segmentation_builder", "detection_builder", "custom_builder"
        ]
    }


def get_model_config(model_category: str, variant: str) -> Dict[str, Any]:
    """
    統一的模型配置獲取函數
    
    Args:
        model_category: 模型類別
        variant: 變體名稱
        
    Returns:
        模型配置字典
    """
    
    config_getters = {
        "road_damage": get_road_damage_model_config,
        "infrastructure_monitoring": get_infrastructure_monitoring_config,
        "traffic_analysis": get_traffic_analysis_config,
    }
    
    if model_category not in config_getters:
        raise ValueError(f"未知的模型類別: {model_category}. 可用類別: {list(config_getters.keys())}")
    
    return config_getters[model_category](variant)