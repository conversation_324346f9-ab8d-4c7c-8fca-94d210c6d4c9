import cv2
import numpy as np
from PIL import Image
import json
import xml.etree.ElementTree as ET
import logging
import os

class YoloVisualizer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("初始化 YoloVisualizer")

    @staticmethod
    def imread_unicode(path):
        try:
            return cv2.imdecode(np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR)
        except Exception as e:
            logging.getLogger(__name__).error(f"讀取圖檔失敗: {path} -> {e}")
            return None

    def visualize(self, image_path, label_path, output_path=None):
        try:
            image = self.imread_unicode(image_path)
            if image is None:
                self.logger.warning("圖片讀取失敗，跳過視覺化")
                return

            height, width, _ = image.shape
            self.logger.info(f"圖片尺寸: {width}x{height}")

            ext = os.path.splitext(label_path)[1].lower()

            if ext == ".txt":
                self._draw_yolo_auto(image, label_path, width, height)
            elif ext == ".json":
                self._draw_labelme_format(image, label_path)
            elif ext == ".xml":
                self._draw_voc_format(image, label_path)
            else:
                self.logger.warning(f"不支援的標籤格式：{ext}")
                return

            if output_path:
                cv2.imwrite(output_path, image)
                self.logger.info(f"✅ 疊加圖像已保存到: {output_path}")
            else:
                self.logger.info("🔍 顯示圖像視覺化結果")
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                pil_image.show()

        except Exception as e:
            self.logger.exception(f"⚠️ 發生錯誤: {e}")

    def _draw_yolo_auto(self, image, label_path, width, height):
        with open(label_path, 'r') as f:
            for line in f:
                data = line.strip().split()
                if len(data) < 5:
                    continue
                class_id = int(data[0])
                coords = list(map(float, data[1:]))

                if len(coords) >= 6:
                    # polygon 模式
                    points = [[int(coords[i] * width), int(coords[i + 1] * height)] for i in range(0, len(coords), 2)]
                    pts = np.array(points, np.int32).reshape((-1, 1, 2))
                    cv2.polylines(image, [pts], isClosed=True, color=(0, 255, 0), thickness=3)
                    cv2.putText(image, str(class_id), tuple(pts[0][0]), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                else:
                    # box 模式
                    x_center, y_center, w, h = coords[:4]
                    x1 = int((x_center - w / 2) * width)
                    y1 = int((y_center - h / 2) * height)
                    x2 = int((x_center + w / 2) * width)
                    y2 = int((y_center + h / 2) * height)
                    cv2.rectangle(image, (x1, y1), (x2, y2), (255, 0, 0), 3)
                    cv2.putText(image, str(class_id), (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

    def _draw_labelme_format(self, image, label_path):
        with open(label_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            for shape in data['shapes']:
                label = shape.get('label', 'unknown')
                shape_type = shape.get('shape_type', 'polygon')  # 預設 polygon
                if shape_type == 'polygon':
                    points = np.array(shape['points'], dtype=np.int32).reshape((-1, 1, 2))
                    cv2.polylines(image, [points], isClosed=True, color=(0, 255, 0), thickness=3)
                    cv2.putText(image, label, tuple(points[0][0]), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                elif shape_type == 'rectangle' and len(shape['points']) == 2:
                    (x1, y1), (x2, y2) = shape['points']
                    cv2.rectangle(image, (int(x1), int(y1)), (int(x2), int(y2)), (255, 0, 0), 3)
                    cv2.putText(image, label, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

    def _draw_voc_format(self, image, label_path):
        tree = ET.parse(label_path)
        root = tree.getroot()
        for obj in root.findall('object'):
            name = obj.find('name').text
            bndbox = obj.find('bndbox')
            x1 = int(float(bndbox.find('xmin').text))
            y1 = int(float(bndbox.find('ymin').text))
            x2 = int(float(bndbox.find('xmax').text))
            y2 = int(float(bndbox.find('ymax').text))
            cv2.rectangle(image, (x1, y1), (x2, y2), (255, 0, 0), 3)
            cv2.putText(image, name, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)

# ========== 主程式 ==========

if __name__ == "__main__":
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s", datefmt="%H:%M:%S")
    file_handler = logging.FileHandler("label_visualizer.log", mode='w', encoding='utf-8')
    file_handler.setFormatter(formatter)
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logging.basicConfig(level=logging.DEBUG, handlers=[file_handler, console_handler])

    # === 設定圖片與標註路徑 ===
    image_path = r"D:\image\5_test_image\test_2\BMP-2291_20241126_130131461.jpg"
    label_path = r"D:\image\5_test_image\test_2\BMP-2291_20241126_130131461.txt" # .txt / .json / .xml 都支援
    output_path = None  # 或 r"output.jpg"

    visualizer = YoloVisualizer()
    visualizer.visualize(image_path, label_path, output_path)