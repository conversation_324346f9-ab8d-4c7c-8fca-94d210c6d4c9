#!/usr/bin/env python3
"""
自動類別檢測模組
簡化的類別配置 - 僅保留auto方法，移除所有手動配置選項
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from .yolo_config import ClassConfig, CLASS_NAMES, LABEL_ALIASES


def auto_detect_classes_from_model(model_path: str) -> List[str]:
    """
    從YOLO模型自動檢測類別名稱
    
    Args:
        model_path: 模型檔案路徑
        
    Returns:
        List[str]: 類別名稱列表，按模型ID順序排列
    """
    if not model_path or not os.path.exists(model_path):
        return []
    
    try:
        # 嘗試使用ultralytics載入模型
        from ultralytics import YOLO
        model = YOLO(model_path)
        
        if hasattr(model, 'names') and model.names:
            # 按ID順序提取類別名稱
            class_names = [model.names[i] for i in sorted(model.names.keys())]
            print(f"✅ 從模型自動檢測到 {len(class_names)} 個類別")
            return class_names
            
    except Exception as e:
        print(f"⚠️ 模型類別檢測失敗: {e}")
    
    return []


def scan_labelme_directory(labelme_dir: str) -> List[str]:
    """
    掃描LabelMe標註目錄獲取類別名稱
    
    Args:
        labelme_dir: LabelMe標註目錄路徑
        
    Returns:
        List[str]: 按字母順序排列的類別名稱列表
    """
    if not labelme_dir or not os.path.exists(labelme_dir):
        return []
    
    found_labels = set()
    labelme_files = list(Path(labelme_dir).glob("*.json"))
    
    if not labelme_files:
        return []
    
    try:
        for json_file in labelme_files:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                if 'shapes' in data:
                    for shape in data['shapes']:
                        if 'label' in shape and shape['label']:
                            # 應用標籤別名映射
                            original_label = shape['label']
                            mapped_label = LABEL_ALIASES.get(original_label, original_label)
                            found_labels.add(mapped_label)
        
        if found_labels:
            class_names = sorted(list(found_labels))
            print(f"✅ 從LabelMe掃描到 {len(class_names)} 個類別")
            return class_names
            
    except Exception as e:
        print(f"⚠️ LabelMe掃描失敗: {e}")
    
    return []


def create_auto_class_configs(model_path: str = None,
                            labelme_dir: str = None,
                            default_conf: float = 0.5) -> Dict[int, ClassConfig]:
    """
    統一的自動類別配置方法 - 簡化版
    
    優先順序:
    1. 從模型自動檢測 (如果提供model_path)
    2. 使用全域CLASS_NAMES (如果非空)
    3. 從LabelMe掃描 (如果提供labelme_dir)
    4. 使用預設fallback
    
    Args:
        model_path: 模型檔案路徑
        labelme_dir: LabelMe標註目錄
        default_conf: 預設置信度閾值
        
    Returns:
        Dict[int, ClassConfig]: 自動生成的類別配置
    """
    class_names = []
    detection_source = "unknown"
    
    # 優先級1: 從模型自動檢測
    if model_path and os.path.exists(model_path):
        class_names = auto_detect_classes_from_model(model_path)
        if class_names:
            detection_source = "model"
    
    # 優先級2: 使用全域CLASS_NAMES
    if not class_names and CLASS_NAMES:
        class_names = CLASS_NAMES.copy()
        detection_source = "global_config"
        print(f"✅ 使用全域CLASS_NAMES: {len(class_names)} 個類別")
    
    # 優先級3: 從LabelMe掃描
    if not class_names and labelme_dir:
        class_names = scan_labelme_directory(labelme_dir)
        if class_names:
            detection_source = "labelme"
    
    # 優先級4: 預設fallback
    if not class_names:
        class_names = ["object"]
        detection_source = "fallback"
        print("⚠️ 使用預設fallback類別: 'object'")
    
    # 創建類別配置
    class_configs = {}
    for i, name in enumerate(class_names):
        class_configs[i] = ClassConfig(
            name=name,
            conf_threshold=default_conf,
            enabled=True
        )
    
    print(f"🎯 類別配置完成: {len(class_configs)} 個類別 (來源: {detection_source})")
    
    return class_configs


def apply_label_mapping(original_label: str) -> str:
    """
    應用標籤別名映射
    僅在標註標籤與模型輸出不匹配時使用
    
    Args:
        original_label: 原始標籤名稱
        
    Returns:
        str: 映射後的標籤名稱
    """
    if original_label in LABEL_ALIASES:
        mapped_label = LABEL_ALIASES[original_label]
        print(f"🎯 標籤映射: '{original_label}' -> '{mapped_label}'")
        return mapped_label
    
    return original_label


def validate_class_configs(class_configs: Dict[int, ClassConfig]) -> bool:
    """
    驗證類別配置的有效性
    
    Args:
        class_configs: 類別配置字典
        
    Returns:
        bool: 配置是否有效
    """
    if not class_configs:
        print("❌ 錯誤: 沒有類別配置")
        return False
    
    enabled_count = sum(1 for config in class_configs.values() if config.enabled)
    if enabled_count == 0:
        print("❌ 錯誤: 沒有啟用的類別")
        return False
    
    # 檢查置信度閾值範圍
    for class_id, config in class_configs.items():
        if not (0.0 <= config.conf_threshold <= 1.0):
            print(f"❌ 錯誤: 類別 {class_id} ({config.name}) 的置信度閾值無效: {config.conf_threshold}")
            return False
    
    print(f"✅ 類別配置驗證通過: {len(class_configs)} 個類別，{enabled_count} 個啟用")
    return True


def print_class_summary(class_configs: Dict[int, ClassConfig]):
    """
    顯示類別配置摘要
    
    Args:
        class_configs: 類別配置字典
    """
    if not class_configs:
        print("⚠️ 無類別配置")
        return
    
    print("\n" + "="*50)
    print("📊 類別配置摘要")
    print("="*50)
    
    enabled_configs = [config for config in class_configs.values() if config.enabled]
    disabled_configs = [config for config in class_configs.values() if not config.enabled]
    
    print(f"總類別數: {len(class_configs)}")
    print(f"啟用類別: {len(enabled_configs)}")
    print(f"禁用類別: {len(disabled_configs)}")
    
    if enabled_configs:
        thresholds = [config.conf_threshold for config in enabled_configs]
        print(f"置信度範圍: {min(thresholds):.2f} - {max(thresholds):.2f}")
        print(f"平均置信度: {sum(thresholds)/len(thresholds):.2f}")
    
    print("\n📋 類別詳情:")
    for class_id, config in class_configs.items():
        status = "✅" if config.enabled else "❌"
        print(f"  {class_id:2d}. {config.name:<20} {status} (conf: {config.conf_threshold:.2f})")
    
    print("="*50)