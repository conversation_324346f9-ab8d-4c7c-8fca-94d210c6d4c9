#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打包腳本 - 一鍵生成exe
"""

import subprocess
import sys
import os

def quick_build():
    """快速打包函數"""
    print("🚀 快速打包模式")
    print("=" * 30)
    
    if not os.path.exists('image_sample_extractor.py'):
        print("❌ 找不到主程式文件")
        return False
    
    # 簡單的一行命令打包
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed', 
        '--name=圖像樣本抽取工具',
        '--clean',
        '--noconfirm',
        'image_sample_extractor.py'
    ]
    
    print("📦 正在打包...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 打包完成！")
        print("📁 輸出位置: dist/圖像樣本抽取工具.exe")
        return True
    except subprocess.CalledProcessError:
        print("❌ 打包失敗")
        return False
    except FileNotFoundError:
        print("❌ 找不到 pyinstaller，請先安裝:")
        print("pip install pyinstaller")
        return False

if __name__ == "__main__":
    quick_build()