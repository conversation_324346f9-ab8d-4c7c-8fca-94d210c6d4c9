#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圖像處理器模組

負責圖像文件的處理，包括複製、調整大小、格式轉換等
"""

import shutil
from pathlib import Path
from typing import Union, Tuple, Optional, Dict, Any
import logging
from PIL import Image, ImageOps
import json

from shared.exceptions import ImageProcessingError
from shared.logger_utils import StructuredLogger
from shared.file_utils import FileUtils


class ImageProcessor:
    """
    圖像處理器
    
    負責圖像文件的處理和標註座標的調整
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化圖像處理器
        
        Args:
            logger: 日誌記錄器
        """
        self.logger = logger or StructuredLogger.create_logger("ImageProcessor")
        
        # 支援的圖像格式
        self.supported_formats = {
            '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'
        }
        
        # 處理統計
        self.stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def process_image_with_annotation(self,
                                    image_path: Path,
                                    annotation_path: Path,
                                    output_dir: Path,
                                    resize: Union[float, Tuple[int, int]] = None,
                                    quality: int = 75,
                                    format_convert: Optional[str] = None) -> bool:
        """
        處理圖像和對應的標註文件
        
        Args:
            image_path: 圖像文件路徑
            annotation_path: 標註文件路徑
            output_dir: 輸出目錄
            resize: 調整大小參數（比例或(寬,高)）
            quality: 圖像品質（1-100）
            format_convert: 圖像格式轉換（如'jpg', 'png'）
            
        Returns:
            bool: 處理是否成功
        """
        try:
            self.stats['processed'] += 1
            
            # 檢查輸入文件
            if not image_path.exists():
                raise ImageProcessingError(f"圖像文件不存在: {image_path}")
            
            if not annotation_path.exists():
                raise ImageProcessingError(f"標註文件不存在: {annotation_path}")
            
            # 創建輸出目錄
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 讀取原始圖像
            original_image = Image.open(image_path)
            original_size = original_image.size  # (width, height)
            
            # 處理圖像
            processed_image, new_size = self._process_image(
                original_image, resize, quality, format_convert
            )
            
            # 計算縮放比例
            scale_x = new_size[0] / original_size[0] if original_size[0] > 0 else 1.0
            scale_y = new_size[1] / original_size[1] if original_size[1] > 0 else 1.0
            
            # 保存處理後的圖像
            output_image_path = self._get_output_image_path(
                image_path, output_dir, format_convert
            )
            
            if format_convert and format_convert.lower() in ['jpg', 'jpeg']:
                # JPG格式需要轉換為RGB
                if processed_image.mode in ('RGBA', 'P'):
                    processed_image = processed_image.convert('RGB')
                processed_image.save(output_image_path, quality=quality, optimize=True)
            else:
                processed_image.save(output_image_path, quality=quality, optimize=True)
            
            # 處理標註文件
            if annotation_path.suffix.lower() == '.json':
                self._process_labelme_annotation(
                    annotation_path, output_dir, scale_x, scale_y, new_size
                )
            else:
                # 其他格式直接複製
                output_annotation_path = output_dir / annotation_path.name
                shutil.copy2(annotation_path, output_annotation_path)
            
            self.logger.debug(f"圖像處理成功: {image_path} -> {output_image_path}")
            self.stats['success'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"圖像處理失敗: {image_path}, 錯誤: {e}")
            self.stats['failed'] += 1
            return False
        finally:
            # 釋放圖像記憶體
            if 'original_image' in locals():
                original_image.close()
            if 'processed_image' in locals():
                processed_image.close()
    
    def copy_image_simple(self,
                         image_path: Path,
                         output_dir: Path,
                         new_name: Optional[str] = None) -> bool:
        """
        簡單複製圖像文件
        
        Args:
            image_path: 圖像文件路徑
            output_dir: 輸出目錄
            new_name: 新文件名（可選）
            
        Returns:
            bool: 複製是否成功
        """
        try:
            if not image_path.exists():
                raise ImageProcessingError(f"圖像文件不存在: {image_path}")
            
            output_dir.mkdir(parents=True, exist_ok=True)
            
            output_name = new_name or image_path.name
            output_path = output_dir / output_name
            
            shutil.copy2(image_path, output_path)
            
            self.logger.debug(f"圖像複製成功: {image_path} -> {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"圖像複製失敗: {image_path}, 錯誤: {e}")
            return False
    
    def resize_image(self,
                    image_path: Path,
                    output_path: Path,
                    resize: Union[float, Tuple[int, int]],
                    quality: int = 75) -> Tuple[int, int]:
        """
        調整圖像大小
        
        Args:
            image_path: 輸入圖像路徑
            output_path: 輸出圖像路徑
            resize: 調整大小參數
            quality: 圖像品質
            
        Returns:
            Tuple[int, int]: 新的圖像尺寸 (width, height)
        """
        try:
            with Image.open(image_path) as img:
                original_size = img.size
                new_size = self._calculate_new_size(original_size, resize)
                
                # 調整大小
                if new_size != original_size:
                    resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
                else:
                    resized_img = img.copy()
                
                # 保存
                if output_path.suffix.lower() in ['.jpg', '.jpeg']:
                    if resized_img.mode in ('RGBA', 'P'):
                        resized_img = resized_img.convert('RGB')
                    resized_img.save(output_path, quality=quality, optimize=True)
                else:
                    resized_img.save(output_path, quality=quality, optimize=True)
                
                return new_size
                
        except Exception as e:
            raise ImageProcessingError(f"調整圖像大小失敗: {e}")
    
    def get_image_info(self, image_path: Path) -> Dict[str, Any]:
        """
        獲取圖像基本信息
        
        Args:
            image_path: 圖像文件路徑
            
        Returns:
            Dict[str, Any]: 圖像信息
        """
        try:
            with Image.open(image_path) as img:
                info = {
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format': img.format,
                    'size_bytes': image_path.stat().st_size
                }
                
                # 獲取EXIF信息（如果有）
                if hasattr(img, '_getexif') and img._getexif():
                    info['exif'] = dict(img._getexif().items())
                
                return info
                
        except Exception as e:
            raise ImageProcessingError(f"獲取圖像信息失敗: {e}")
    
    def validate_image(self, image_path: Path) -> bool:
        """
        驗證圖像文件是否有效
        
        Args:
            image_path: 圖像文件路徑
            
        Returns:
            bool: 圖像是否有效
        """
        try:
            if not image_path.exists():
                return False
            
            if image_path.suffix.lower() not in self.supported_formats:
                return False
            
            with Image.open(image_path) as img:
                # 嘗試載入圖像數據
                img.verify()
                return True
                
        except Exception:
            return False
    
    def find_related_image(self, annotation_path: Path) -> Optional[Path]:
        """
        查找與標註文件對應的圖像文件
        
        Args:
            annotation_path: 標註文件路徑
            
        Returns:
            Optional[Path]: 對應的圖像文件路徑
        """
        base_name = annotation_path.stem
        parent_dir = annotation_path.parent
        
        # 嘗試常見的圖像擴展名
        for ext in self.supported_formats:
            image_path = parent_dir / f"{base_name}{ext}"
            if image_path.exists() and self.validate_image(image_path):
                return image_path
        
        return None
    
    def _process_image(self,
                      image: Image.Image,
                      resize: Union[float, Tuple[int, int], None],
                      quality: int,
                      format_convert: Optional[str]) -> Tuple[Image.Image, Tuple[int, int]]:
        """
        處理圖像（內部方法）
        
        Args:
            image: PIL圖像對象
            resize: 調整大小參數
            quality: 圖像品質
            format_convert: 格式轉換
            
        Returns:
            Tuple[Image.Image, Tuple[int, int]]: 處理後的圖像和新尺寸
        """
        processed_image = image.copy()
        original_size = image.size
        
        # 調整大小
        if resize is not None:
            new_size = self._calculate_new_size(original_size, resize)
            if new_size != original_size:
                processed_image = processed_image.resize(new_size, Image.Resampling.LANCZOS)
            new_size = processed_image.size
        else:
            new_size = original_size
        
        # 格式轉換
        if format_convert:
            if format_convert.lower() in ['jpg', 'jpeg']:
                if processed_image.mode in ('RGBA', 'P'):
                    processed_image = processed_image.convert('RGB')
            elif format_convert.lower() == 'png':
                if processed_image.mode != 'RGBA':
                    processed_image = processed_image.convert('RGBA')
        
        return processed_image, new_size
    
    def _calculate_new_size(self,
                           original_size: Tuple[int, int],
                           resize: Union[float, Tuple[int, int]]) -> Tuple[int, int]:
        """
        計算新的圖像尺寸
        
        Args:
            original_size: 原始尺寸 (width, height)
            resize: 調整參數
            
        Returns:
            Tuple[int, int]: 新尺寸 (width, height)
        """
        width, height = original_size
        
        if isinstance(resize, (int, float)):
            # 按比例縮放
            new_width = int(width * resize)
            new_height = int(height * resize)
        elif isinstance(resize, (list, tuple)) and len(resize) == 2:
            # 指定新尺寸
            new_width, new_height = int(resize[0]), int(resize[1])
        else:
            raise ImageProcessingError(f"無效的調整參數: {resize}")
        
        # 確保尺寸至少為1
        new_width = max(1, new_width)
        new_height = max(1, new_height)
        
        return (new_width, new_height)
    
    def _get_output_image_path(self,
                              image_path: Path,
                              output_dir: Path,
                              format_convert: Optional[str]) -> Path:
        """
        獲取輸出圖像路徑
        
        Args:
            image_path: 原始圖像路徑
            output_dir: 輸出目錄
            format_convert: 格式轉換
            
        Returns:
            Path: 輸出圖像路徑
        """
        base_name = image_path.stem
        
        if format_convert:
            if format_convert.lower() in ['jpg', 'jpeg']:
                extension = '.jpg'
            elif format_convert.lower() == 'png':
                extension = '.png'
            else:
                extension = f'.{format_convert.lower()}'
        else:
            extension = image_path.suffix
        
        return output_dir / f"{base_name}{extension}"
    
    def _process_labelme_annotation(self,
                                   annotation_path: Path,
                                   output_dir: Path,
                                   scale_x: float,
                                   scale_y: float,
                                   new_size: Tuple[int, int]):
        """
        處理LabelMe標註文件，調整座標
        
        Args:
            annotation_path: 標註文件路徑
            output_dir: 輸出目錄
            scale_x: X軸縮放比例
            scale_y: Y軸縮放比例
            new_size: 新的圖像尺寸
        """
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)
            
            # 更新圖像尺寸
            annotation_data['imageWidth'] = new_size[0]
            annotation_data['imageHeight'] = new_size[1]
            
            # 調整標註座標
            if 'shapes' in annotation_data:
                for shape in annotation_data['shapes']:
                    if 'points' in shape:
                        # 調整每個點的座標
                        new_points = []
                        for point in shape['points']:
                            if len(point) >= 2:
                                new_x = point[0] * scale_x
                                new_y = point[1] * scale_y
                                new_points.append([new_x, new_y])
                            else:
                                new_points.append(point)
                        shape['points'] = new_points
            
            # 保存調整後的標註
            output_annotation_path = output_dir / annotation_path.name
            with open(output_annotation_path, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            raise ImageProcessingError(f"處理LabelMe標註失敗: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """
        獲取處理統計信息
        
        Returns:
            Dict[str, int]: 統計信息
        """
        return self.stats.copy()
    
    def reset_stats(self):
        """重置統計信息"""
        self.stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def batch_process_images(self,
                           image_files: list,
                           annotation_files: list,
                           output_dir: Path,
                           resize: Union[float, Tuple[int, int]] = None,
                           quality: int = 75,
                           format_convert: Optional[str] = None) -> Dict[str, Any]:
        """
        批次處理圖像和標註文件
        
        Args:
            image_files: 圖像文件列表
            annotation_files: 標註文件列表
            output_dir: 輸出目錄
            resize: 調整大小參數
            quality: 圖像品質
            format_convert: 格式轉換
            
        Returns:
            Dict[str, Any]: 處理結果統計
        """
        self.reset_stats()
        
        # 建立文件對應關係
        file_pairs = self._match_image_annotation_pairs(image_files, annotation_files)
        
        for image_path, annotation_path in file_pairs:
            if annotation_path:
                self.process_image_with_annotation(
                    image_path, annotation_path, output_dir,
                    resize, quality, format_convert
                )
            else:
                # 只處理圖像
                if resize or format_convert:
                    try:
                        output_image_path = self._get_output_image_path(
                            image_path, output_dir, format_convert
                        )
                        self.resize_image(image_path, output_image_path, resize or 1.0, quality)
                        self.stats['success'] += 1
                    except Exception as e:
                        self.logger.error(f"處理圖像失敗: {image_path}, 錯誤: {e}")
                        self.stats['failed'] += 1
                else:
                    self.copy_image_simple(image_path, output_dir)
                    self.stats['success'] += 1
                
                self.stats['processed'] += 1
        
        return self.get_stats()
    
    def _match_image_annotation_pairs(self,
                                    image_files: list,
                                    annotation_files: list) -> list:
        """
        匹配圖像和標註文件對
        
        Args:
            image_files: 圖像文件列表
            annotation_files: 標註文件列表
            
        Returns:
            list: 文件對列表 [(image_path, annotation_path), ...]
        """
        pairs = []
        annotation_dict = {Path(f).stem: Path(f) for f in annotation_files}
        
        for image_file in image_files:
            image_path = Path(image_file)
            image_stem = image_path.stem
            
            annotation_path = annotation_dict.get(image_stem)
            pairs.append((image_path, annotation_path))
        
        return pairs