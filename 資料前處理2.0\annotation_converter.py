import os
import json
import xml.etree.ElementTree as ET
import logging
from pathlib import Path
from PIL import Image
import numpy as np
import argparse
import shutil
import time
import gc
from typing import Dict, List, Union, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 自定義異常類型
class ConverterError(Exception):
    """標籤轉換器基本異常類型"""
    pass

class FormatDetectionError(ConverterError):
    """格式檢測錯誤"""
    pass

class ConversionError(ConverterError):
    """轉換過程中的錯誤"""
    pass

class ImageProcessingError(ConverterError):
    """圖像處理錯誤"""
    pass


class AnnotationConverter:
    """
    標籤格式轉換器
    支持將不同格式的標籤轉換為LabelMe格式，並進行圖像處理
    """
    
    def __init__(self, logger=None, max_workers: int = None):
        """
        初始化標籤轉換器
        
        參數:
            logger: 日誌記錄器，如果為None則創建新的
            max_workers: 最大並行工作線程數量，None表示根據CPU數量自動決定
        """
        self.logger = logger or logging.getLogger(__name__)
        self.max_workers = max_workers or min(32, os.cpu_count() * 2)
        
        # 轉換統計
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }
        
        # 全局鎖用於多線程更新統計
        self.stats_lock = threading.Lock()
        
        
        # 支持的格式
        self.supported_formats = ['voc', 'yolo', 'coco', 'labelme']
        
        # 保存進度狀態的檔案名
        self.checkpoint_file = ".converter_checkpoint.json"

    def convert_format(self, 
                      input_path: Union[str, Path], 
                      output_path: Union[str, Path], 
                      source_format: str = 'auto', 
                      target_shape: str = 'polygon', 
                      resize: Union[float, Tuple[int, int]] = 0.3, 
                      quality: int = 75,
                      batch_size: int = 10,
                      resume: bool = True) -> Dict[str, Any]:
        """
        將各種格式轉換為LabelMe格式
        
        參數:
            input_path: 輸入文件或目錄路徑
            output_path: 輸出目錄路徑
            source_format: 輸入格式 ('auto', 'voc', 'yolo', 'coco', 'labelme')
            target_shape: 轉換目標形狀 ('box', 'polygon')
            resize: 圖像縮放參數，可以是縮放比例(如0.3)或(寬,高)元組
            quality: 圖像品質參數 (1-100)
            batch_size: 批處理大小
            resume: 是否從中斷點恢復
        
        返回:
            轉換統計信息字典
        """
        start_time = time.time()
        
        self._reset_stats()
        
        # 確保輸出目錄存在
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 嘗試加載檢查點
        checkpoint = self._load_checkpoint(output_path) if resume else None
        
        # 實現格式自動檢測
        if source_format == 'auto':
            try:
                source_format = self._detect_format(input_path)
                self.logger.info(f"檢測到輸入格式: {source_format}")
            except FormatDetectionError as e:
                self.logger.error(f"格式檢測失敗: {e}")
                return self.stats
        
        # 檢查輸入格式是否支持
        if source_format not in self.supported_formats:
            self.logger.error(f"不支持的源格式: {source_format}")
            return self.stats
        
        # 實現各種格式到LabelMe的轉換
        if source_format == 'labelme':
            self.logger.info(f"檢測到LabelMe格式，直接進行檢查和圖像處理")
            self._process_labelme_format(input_path, output_path, resize, quality, checkpoint, batch_size)
        else:
            # 使用對應的轉換方法
            converter_method = getattr(self, f"_convert_{source_format}_to_labelme", None)
            if converter_method:
                self.logger.info(f"開始將 {source_format} 格式轉換為 LabelMe 格式")
                converter_method(input_path, output_path, target_shape, resize, quality, checkpoint, batch_size)
            else:
                self.logger.error(f"未實現的轉換方法: {source_format}")
                return self.stats
        
        # 執行檢查
        if source_format != 'labelme':
            check_stats = self.validate_annotations(output_path, resize=resize, quality=quality)
            # 更新統計信息
            self._update_stats(check_stats)
        
        end_time = time.time()
        self.logger.info(f"處理完成: 總計 {self.stats['total']} 個文件，耗時 {end_time - start_time:.2f} 秒")
        self.logger.info(
            f"成功: {self.stats.get('success', 0)}, 失敗: {self.stats.get('failed', 0)}, 跳過: {self.stats.get('skipped', 0)}")
        
        # 刪除檢查點
        if os.path.exists(os.path.join(output_path, self.checkpoint_file)):
            os.remove(os.path.join(output_path, self.checkpoint_file))
        
        return self.stats

    def _reset_stats(self) -> None:
        """重置統計信息"""
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }
    
    def _update_stats(self, stats_update: Dict[str, Any]) -> None:
        """
        線程安全地更新統計信息
        
        參數:
            stats_update: 要更新的統計信息
        """
        with self.stats_lock:
            for key, value in stats_update.items():
                if key in self.stats:
                    if isinstance(value, dict) and isinstance(self.stats[key], dict):
                        for sub_key, sub_value in value.items():
                            if sub_key in self.stats[key]:
                                self.stats[key][sub_key] += sub_value
                            else:
                                self.stats[key][sub_key] = sub_value
                    else:
                        self.stats[key] += value
                else:
                    self.stats[key] = value
    
    def _save_checkpoint(self, output_path: Path, processed_files: List[str]) -> None:
        """
        保存處理進度檢查點
        
        參數:
            output_path: 輸出目錄
            processed_files: 已處理檔案列表
        """
        checkpoint_data = {
            "stats": self.stats,
            "processed_files": processed_files
        }
        
        checkpoint_path = output_path / self.checkpoint_file
        
        try:
            with open(checkpoint_path, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False)
                
            self.logger.debug(f"已保存檢查點: {len(processed_files)} 個檔案")
        except Exception as e:
            self.logger.warning(f"保存檢查點失敗: {e}")
    
    def _load_checkpoint(self, output_path: Path) -> Optional[Dict[str, Any]]:
        """
        加載處理進度檢查點
        
        參數:
            output_path: 輸出目錄
            
        返回:
            檢查點數據，如果不存在則返回None
        """
        checkpoint_path = output_path / self.checkpoint_file
        
        if not checkpoint_path.exists():
            return None
        
        try:
            with open(checkpoint_path, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            # 更新統計信息
            self.stats = checkpoint_data.get("stats", self.stats)
            
            self.logger.info(f"已加載檢查點: {len(checkpoint_data.get('processed_files', []))} 個檔案")
            
            return checkpoint_data
        except Exception as e:
            self.logger.warning(f"加載檢查點失敗: {e}")
            return None

    def _detect_format(self, path: Union[str, Path]) -> str:
        """
        檢測輸入路徑的標註格式
        
        參數:
            path: 輸入路徑
            
        返回:
            檢測到的格式名稱
            
        異常:
            FormatDetectionError: 格式檢測失敗
        """
        path = Path(path)
        
        # 先檢測目錄格式
        if path.is_dir():
            return self._detect_directory_format(path)
        # 檢測單個文件
        elif path.is_file():
            return self._detect_file_format(path)
        else:
            raise FormatDetectionError(f"無效的路徑類型: {path}")

    def _detect_directory_format(self, directory: Path) -> str:
        """
        檢測目錄中的標註格式
        
        參數:
            directory: 目錄路徑
            
        返回:
            檢測到的格式名稱
        """
        # 檢查是否有 .json 文件（可能是LabelMe或COCO格式）
        json_files = list(directory.glob("*.json"))
        if json_files:
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8-sig') as f:
                        data = json.load(f)
                        # LabelMe格式檢測
                        if self._is_labelme_format(data):
                            return "labelme"
                        # COCO格式檢測
                        elif self._is_coco_format(data):
                            return "coco"
                except Exception:
                    continue

        # 檢查是否有 .xml 文件（VOC格式）
        xml_files = list(directory.glob("*.xml"))
        if xml_files:
            sample_xml = xml_files[0]
            try:
                tree = ET.parse(sample_xml)
                root = tree.getroot()
                if root.tag == "annotation":
                    return "voc"
            except Exception:
                pass

        # 檢查是否有 .txt 文件（YOLO格式）
        txt_files = list(directory.glob("*.txt"))
        if txt_files:
            # 排除 classes.txt 文件
            txt_files = [f for f in txt_files if f.name.lower() != "classes.txt"]
            if txt_files:
                sample_txt = txt_files[0]
                try:
                    with open(sample_txt, 'r') as f:
                        line = f.readline().strip()
                        parts = line.split()
                        if len(parts) >= 5 and parts[0].isdigit():
                            return "yolo"
                except Exception:
                    pass

        self.logger.warning(f"無法檢測格式，使用默認格式: voc")
        return "voc"

    def _detect_file_format(self, file_path: Path) -> str:
        """
        檢測單個文件的標註格式
        
        參數:
            file_path: 文件路徑
            
        返回:
            檢測到的格式名稱
        """
        # 檢查是否是 JSON 文件（LabelMe或COCO格式）
        if file_path.suffix.lower() == ".json":
            try:
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    data = json.load(f)
                    # LabelMe格式檢測
                    if self._is_labelme_format(data):
                        return "labelme"
                    # COCO格式檢測
                    elif self._is_coco_format(data):
                        return "coco"
            except Exception:
                pass

        # 檢查是否是 XML 文件（VOC格式）
        if file_path.suffix.lower() == ".xml":
            try:
                tree = ET.parse(file_path)
                root = tree.getroot()
                if root.tag == "annotation":
                    return "voc"
            except Exception:
                pass

        # 檢查是否是 TXT 文件（YOLO格式）
        if file_path.suffix.lower() == ".txt":
            try:
                with open(file_path, 'r') as f:
                    line = f.readline().strip()
                    parts = line.split()
                    if len(parts) >= 5 and parts[0].isdigit():
                        return "yolo"
            except Exception:
                pass

        self.logger.warning(f"無法檢測格式，使用默認格式: voc")
        return "voc"

    def _is_labelme_format(self, data: Dict[str, Any]) -> bool:
        """
        檢查JSON數據是否符合LabelMe格式
        
        參數:
            data: JSON數據
            
        返回:
            是否為LabelMe格式
        """
        return "version" in data and "imagePath" in data and ("shapes" in data or "annotations" in data)

    def _is_coco_format(self, data: Dict[str, Any]) -> bool:
        """
        檢查JSON數據是否符合COCO格式
        
        參數:
            data: JSON數據
            
        返回:
            是否為COCO格式
        """
        return "images" in data and "annotations" in data and "categories" in data

    def _process_labelme_format(self, 
                               input_path: Path, 
                               output_path: Path, 
                               resize: Union[float, Tuple[int, int]], 
                               quality: int,
                               checkpoint: Optional[Dict[str, Any]],
                               batch_size: int) -> None:
        """
        處理LabelMe格式的數據
        
        參數:
            input_path: 輸入路徑
            output_path: 輸出路徑
            resize: 圖像縮放參數
            quality: 圖像品質
            checkpoint: 檢查點數據
            batch_size: 批處理大小
        """
        # 直接使用驗證功能處理
        check_stats = self.validate_annotations(input_path, output_path, clean_image_data=True, 
                                               resize=resize, quality=quality, 
                                               checkpoint=checkpoint, batch_size=batch_size)
        
        # 更新統計信息
        self._update_stats(check_stats)

    def _convert_voc_to_labelme(self, 
                               input_path: Union[str, Path], 
                               output_path: Union[str, Path], 
                               target_shape: str,
                               resize: Union[float, Tuple[int, int]], 
                               quality: int,
                               checkpoint: Optional[Dict[str, Any]],
                               batch_size: int) -> None:
        """
        VOC XML 轉 LabelMe
        
        參數:
            input_path: 輸入目錄路徑，包含 VOC XML 文件
            output_path: 輸出目錄路徑
            target_shape: 目標形狀 ('box' 或 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數
            checkpoint: 檢查點數據
            batch_size: 批處理大小
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)

        # 如果輸入是目錄，則獲取所有 XML 文件（包括子目錄）
        if input_path.is_dir():
            xml_files = list(input_path.glob("**/*.xml"))
        # 如果輸入是單個文件，則直接處理
        elif input_path.is_file() and input_path.suffix.lower() == ".xml":
            xml_files = [input_path]
        else:
            self.logger.error(f"無效的輸入路徑: {input_path}")
            return

        # 加載已處理文件列表
        processed_files = []
        if checkpoint and "processed_files" in checkpoint:
            processed_files = checkpoint["processed_files"]
            self.logger.info(f"從檢查點恢復: 跳過 {len(processed_files)} 個已處理檔案")

        # 更新統計
        with self.stats_lock:
            self.stats["total"] = len(xml_files)
            if "voc" not in self.stats["formats"]:
                self.stats["formats"]["voc"] = 0
            self.stats["formats"]["voc"] += len(xml_files)

        # 過濾已處理文件
        xml_files_to_process = [f for f in xml_files if str(f) not in processed_files]
        self.logger.info(f"需要處理的文件數: {len(xml_files_to_process)}/{len(xml_files)}")

        # 如果檔案較少，不使用批處理
        if len(xml_files_to_process) <= batch_size:
            self._convert_voc_files_parallel(xml_files_to_process, output_path, target_shape, resize, quality)
            return

        # 批量處理文件
        for i in range(0, len(xml_files_to_process), batch_size):
            batch = xml_files_to_process[i:i + batch_size]
            self.logger.info(f"處理批次 {i//batch_size + 1}/{(len(xml_files_to_process) + batch_size - 1)//batch_size}: {len(batch)} 個檔案")
            
            self._convert_voc_files_parallel(batch, output_path, target_shape, resize, quality)
            
            # 更新已處理文件列表
            processed_files.extend([str(f) for f in batch])
            
            # 每批次保存一次檢查點
            self._save_checkpoint(output_path, processed_files)
            
            # 定期釋放內存
            gc.collect()

    def _convert_voc_files_parallel(self, 
                                   files: List[Path], 
                                   output_path: Path, 
                                   target_shape: str,
                                   resize: Union[float, Tuple[int, int]], 
                                   quality: int) -> None:
        """
        並行處理VOC文件
        
        參數:
            files: 要處理的文件列表
            output_path: 輸出目錄
            target_shape: 目標形狀
            resize: 圖像縮放參數
            quality: 圖像品質參數
        """
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for xml_file in files:
                future = executor.submit(
                    self._convert_voc_file, xml_file, output_path, target_shape, resize, quality
                )
                futures.append(future)
            
            # 收集結果
            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    if result:
                        with self.stats_lock:
                            self.stats["success"] += 1
                    else:
                        with self.stats_lock:
                            self.stats["failed"] += 1
                except Exception as e:
                    self.logger.error(f"處理失敗: {e}")
                    with self.stats_lock:
                        self.stats["failed"] += 1
                
                # 每10個更新一次進度
                if (i + 1) % 10 == 0:
                    with self.stats_lock:
                        processed = self.stats["success"] + self.stats["failed"] + self.stats["skipped"]
                        self.logger.info(f"進度: {processed}/{self.stats['total']} ({processed/self.stats['total']*100:.1f}%)")

    def _convert_voc_file(self, 
                         xml_file: Path, 
                         output_path: Path, 
                         target_shape: str,
                         resize: Union[float, Tuple[int, int]], 
                         quality: int) -> bool:
        """
        轉換單個VOC文件
        
        參數:
            xml_file: XML文件路徑
            output_path: 輸出目錄
            target_shape: 目標形狀
            resize: 圖像縮放參數
            quality: 圖像品質參數
            
        返回:
            是否成功
        """
        try:
            # 解析 XML
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 提取基本信息
            filename = root.find('filename').text
            size_elem = root.find('size')
            width = int(size_elem.find('width').text)
            height = int(size_elem.find('height').text)

            # 提取所有物體
            annotations = []
            for obj in root.findall('object'):
                label = obj.find('name').text
                bndbox = obj.find('bndbox')
                xmin = float(bndbox.find('xmin').text)
                ymin = float(bndbox.find('ymin').text)
                xmax = float(bndbox.find('xmax').text)
                ymax = float(bndbox.find('ymax').text)

                if target_shape == 'polygon':
                    # 轉換為多邊形
                    points = self._get_polygon_from_box(
                        xmin, ymin, xmax, ymax)
                    shape_type = "polygon"
                else:
                    # 保持為矩形
                    points = [[xmin, ymin], [xmax, ymax]]
                    shape_type = "rectangle"

                annotations.append({
                    "label": label,
                    "points": points,
                    "group_id": None,
                    "shape_type": shape_type,
                    "flags": {}
                })

            # 創建 LabelMe JSON
            labelme_data = {
                "version": "4.5.6",
                "flags": {},
                "annotations": annotations,
                "imagePath": filename,
                "imageData": None,
                "imageHeight": height,
                "imageWidth": width
            }

            # 寫入 JSON 文件
            output_json = output_path / f"{xml_file.stem}.json"
            with open(output_json, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, ensure_ascii=False, indent=2)
            
            # 複製對應的圖像文件到輸出目錄
            # 從包含XML文件的目錄開始查找圖像文件
            source_img_dir = xml_file.parent
            self._copy_image_file(source_img_dir, output_path, filename, resize=resize, quality=quality)

            self.logger.debug(f"成功轉換: {xml_file.name} -> {output_json.name}")
            return True

        except Exception as e:
            self.logger.error(f"轉換失敗: {xml_file.name}, 錯誤: {e}")
            return False

    def _convert_yolo_to_labelme(self, 
                                input_path: Union[str, Path], 
                                output_path: Union[str, Path], 
                                target_shape: str,
                                resize: Union[float, Tuple[int, int]], 
                                quality: int,
                                checkpoint: Optional[Dict[str, Any]],
                                batch_size: int) -> None:
        """
        YOLO 轉 LabelMe
        
        參數:
            input_path: 輸入目錄路徑，包含 YOLO txt 文件和對應的圖像
            output_path: 輸出目錄路徑
            target_shape: 目標形狀 ('box' 或 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數
            checkpoint: 檢查點數據
            batch_size: 批處理大小
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)

        # YOLO格式需要對應的圖像來獲取尺寸信息
        if not input_path.is_dir():
            self.logger.error(f"YOLO格式轉換需要輸入目錄包含 .txt 文件和對應的圖像: {input_path}")
            return

        # 獲取所有 txt 文件（包括子目錄）
        txt_files = list(input_path.glob("**/*.txt"))

        # 排除 classes.txt 和資料夾路徑包含"classes"的文件
        txt_files = [f for f in txt_files if f.name.lower() != "classes.txt" and "classes" not in str(f).lower()]

        # 加載已處理文件列表
        processed_files = []
        if checkpoint and "processed_files" in checkpoint:
            processed_files = checkpoint["processed_files"]
            self.logger.info(f"從檢查點恢復: 跳過 {len(processed_files)} 個已處理檔案")

        # 更新統計
        with self.stats_lock:
            self.stats["total"] = len(txt_files)
            if "yolo" not in self.stats["formats"]:
                self.stats["formats"]["yolo"] = 0
            self.stats["formats"]["yolo"] += len(txt_files)

        # 過濾已處理文件
        txt_files_to_process = [f for f in txt_files if str(f) not in processed_files]
        self.logger.info(f"需要處理的文件數: {len(txt_files_to_process)}/{len(txt_files)}")

        # 讀取類別映射（如果有classes.txt）
        class_names = self._load_yolo_classes(input_path)

        # 如果檔案較少，不使用批處理
        if len(txt_files_to_process) <= batch_size:
            self._convert_yolo_files_parallel(txt_files_to_process, input_path, output_path, target_shape, 
                                             resize, quality, class_names)
            return

        # 批量處理文件
        for i in range(0, len(txt_files_to_process), batch_size):
            batch = txt_files_to_process[i:i + batch_size]
            self.logger.info(f"處理批次 {i//batch_size + 1}/{(len(txt_files_to_process) + batch_size - 1)//batch_size}: {len(batch)} 個檔案")
            
            self._convert_yolo_files_parallel(batch, input_path, output_path, target_shape, 
                                             resize, quality, class_names)
            
            # 更新已處理文件列表
            processed_files.extend([str(f) for f in batch])
            
            # 每批次保存一次檢查點
            self._save_checkpoint(output_path, processed_files)
            
            # 定期釋放內存
            gc.collect()

    def _load_yolo_classes(self, input_path: Path) -> List[str]:
        """
        載入YOLO類別文件
        
        參數:
            input_path: 輸入目錄
            
        返回:
            類別名稱列表
        """
        class_names = []
        
        # 在輸入目錄及其子目錄中查找classes.txt
        classes_file = None
        for classes_path in input_path.glob("**/classes.txt"):
            classes_file = classes_path
            break
        
        # 如果找不到classes.txt，找data.yaml或dataset.yaml
        if classes_file is None:
            for yaml_path in input_path.glob("**/*.yaml"):
                if yaml_path.name.lower() in ["data.yaml", "dataset.yaml"]:
                    # 這裡需要解析YAML文件來獲取類別，但此簡化版不實現
                    self.logger.info(f"找到YAML配置文件: {yaml_path}，但不解析。請提供classes.txt")
                    break
        
        if classes_file and classes_file.exists():
            try:
                with open(classes_file, 'r', encoding='utf-8') as f:
                    class_names = [line.strip() for line in f if line.strip()]
                self.logger.info(f"讀取到 {len(class_names)} 個類別名稱，來自 {classes_file}")
            except Exception as e:
                self.logger.warning(f"無法讀取 classes.txt: {e}")
        
        return class_names

    def _convert_yolo_files_parallel(self, 
                                    files: List[Path],
                                    base_dir: Path,
                                    output_path: Path, 
                                    target_shape: str,
                                    resize: Union[float, Tuple[int, int]], 
                                    quality: int,
                                    class_names: List[str]) -> None:
        """
        並行處理YOLO文件
        
        參數:
            files: 要處理的文件列表
            base_dir: 基礎目錄（用於查找圖像）
            output_path: 輸出目錄
            target_shape: 目標形狀
            resize: 圖像縮放參數
            quality: 圖像品質參數
            class_names: 類別名稱列表
        """
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for txt_file in files:
                future = executor.submit(
                    self._convert_yolo_file, txt_file, base_dir, output_path, 
                    target_shape, resize, quality, class_names
                )
                futures.append(future)
            
            # 收集結果
            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    if result == "success":
                        with self.stats_lock:
                            self.stats["success"] += 1
                    elif result == "skipped":
                        with self.stats_lock:
                            self.stats["skipped"] += 1
                    else:
                        with self.stats_lock:
                            self.stats["failed"] += 1
                except Exception as e:
                    self.logger.error(f"處理失敗: {e}")
                    with self.stats_lock:
                        self.stats["failed"] += 1
                
                # 每10個更新一次進度
                if (i + 1) % 10 == 0:
                    with self.stats_lock:
                        processed = self.stats["success"] + self.stats["failed"] + self.stats["skipped"]
                        self.logger.info(f"進度: {processed}/{self.stats['total']} ({processed/self.stats['total']*100:.1f}%)")

    def _convert_yolo_file(self, 
                          txt_file: Path,
                          base_dir: Path,
                          output_path: Path, 
                          target_shape: str,
                          resize: Union[float, Tuple[int, int]], 
                          quality: int,
                          class_names: List[str]) -> str:
        """
        轉換單個YOLO文件
        
        參數:
            txt_file: TXT文件路徑
            base_dir: 基礎目錄（用於查找圖像）
            output_path: 輸出目錄
            target_shape: 目標形狀
            resize: 圖像縮放參數
            quality: 圖像品質參數
            class_names: 類別名稱列表
            
        返回:
            處理結果: "success", "skipped", "failed"
        """
        # 查找對應的圖像文件，可能在txt文件所在目錄或其他相關目錄
        img_file = self._find_related_files(
            txt_file.parent,  # 從txt文件所在目錄開始查找
            txt_file.stem,   # 使用txt文件的基本名稱
            ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
            recursive=True   # 允許在子目錄中查找
        )

        if not img_file:
            self.logger.warning(f"找不到對應的圖像文件: {txt_file.stem}.*，跳過")
            return "skipped"

        try:
            # 獲取圖像尺寸
            with Image.open(img_file) as img:
                img_width, img_height = img.size

            # 讀取 YOLO 格式文件
            with open(txt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            annotations = []
            for line in lines:
                parts = line.strip().split()
                if len(parts) < 5:
                    continue

                class_id = int(parts[0])

                # 獲取類別名稱
                if class_id < len(class_names):
                    label = class_names[class_id]
                else:
                    label = f"class_{class_id}"

                # 判斷是否為多邊形標註
                if len(parts) > 5 and len(parts) % 2 == 1:  # 多邊形標註
                    points = []
                    for i in range(1, len(parts), 2):
                        x = float(parts[i]) * img_width
                        y = float(parts[i+1]) * img_height
                        points.append([x, y])

                    shape_type = "polygon"
                else:  # 標準 YOLO 矩形框
                    # YOLO 格式: [類別id, 中心x, 中心y, 寬, 高]
                    x_center = float(parts[1]) * img_width
                    y_center = float(parts[2]) * img_height
                    box_width = float(parts[3]) * img_width
                    box_height = float(parts[4]) * img_height

                    # 計算四個角點
                    xmin = x_center - (box_width / 2)
                    ymin = y_center - (box_height / 2)
                    xmax = x_center + (box_width / 2)
                    ymax = y_center + (box_height / 2)

                    if target_shape == 'polygon':
                        points = self._get_polygon_from_box(
                            xmin, ymin, xmax, ymax)
                        shape_type = "polygon"
                    else:
                        points = [[xmin, ymin], [xmax, ymax]]
                        shape_type = "rectangle"

                annotations.append({
                    "label": label,
                    "points": points,
                    "group_id": None,
                    "shape_type": shape_type,
                    "flags": {}
                })

            # 創建 LabelMe JSON
            labelme_data = {
                "version": "4.5.6",
                "flags": {},
                "annotations": annotations,
                "imagePath": img_file.name,
                "imageData": None,
                "imageHeight": img_height,
                "imageWidth": img_width
            }

            # 寫入 JSON 文件
            output_json = output_path / f"{txt_file.stem}.json"
            with open(output_json, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, ensure_ascii=False, indent=2)
            
            # 複製對應的圖像文件到輸出目錄
            self._copy_image_file(img_file.parent, output_path, img_file.name, resize=resize, quality=quality)

            self.logger.debug(f"成功轉換: {txt_file.name} -> {output_json.name}")
            return "success"

        except Exception as e:
            self.logger.error(f"轉換失敗: {txt_file.name}, 錯誤: {e}")
            return "failed"

    def _convert_coco_to_labelme(self, 
                               input_path: Union[str, Path], 
                               output_path: Union[str, Path], 
                               target_shape: str,
                               resize: Union[float, Tuple[int, int]], 
                               quality: int,
                               checkpoint: Optional[Dict[str, Any]],
                               batch_size: int) -> None:
        """
        COCO JSON 轉 LabelMe
        
        參數:
            input_path: 輸入文件路徑，COCO JSON 文件
            output_path: 輸出目錄路徑
            target_shape: 目標形狀 ('box' 或 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數
            checkpoint: 檢查點數據
            batch_size: 批處理大小
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)

        # 需要找到對應的圖像目錄
        img_dir = input_path.parent if input_path.is_file() else input_path

        try:
            # 讀取 COCO JSON
            coco_file = self._find_coco_file(input_path)
            if coco_file is None:
                self.logger.error(f"找不到 COCO 格式的 JSON 文件: {input_path}")
                return

            with open(coco_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)

            # 獲取類別映射
            categories = {cat["id"]: cat["name"] for cat in coco_data.get("categories", [])}

            # 建立圖像ID到標註的映射
            annotations_by_image = {}
            for ann in coco_data.get("annotations", []):
                img_id = ann["image_id"]
                if img_id not in annotations_by_image:
                    annotations_by_image[img_id] = []
                annotations_by_image[img_id].append(ann)

            # 獲取所有圖像信息
            all_images = coco_data.get("images", [])
            
            # 加載已處理文件列表
            processed_files = []
            if checkpoint and "processed_files" in checkpoint:
                processed_files = checkpoint["processed_files"]
                self.logger.info(f"從檢查點恢復: 跳過 {len(processed_files)} 個已處理檔案")

            # 更新統計
            with self.stats_lock:
                self.stats["total"] = len(all_images)
                if "coco" not in self.stats["formats"]:
                    self.stats["formats"]["coco"] = 0
                self.stats["formats"]["coco"] += len(all_images)

            # 過濾已處理文件
            # 對於COCO格式，使用圖像ID作為標識
            processed_ids = set()
            for path in processed_files:
                # 嘗試從處理過的文件路徑中提取ID
                try:
                    basename = Path(path).stem
                    if basename.isdigit():
                        processed_ids.add(int(basename))
                except:
                    pass

            # 過濾已處理圖像
            images_to_process = [img for img in all_images if img["id"] not in processed_ids]
            self.logger.info(f"需要處理的圖像數: {len(images_to_process)}/{len(all_images)}")

            # 如果圖像較少，不使用批處理
            if len(images_to_process) <= batch_size:
                self._convert_coco_images_parallel(images_to_process, annotations_by_image, 
                                                 categories, img_dir, output_path, 
                                                 target_shape, resize, quality)
                return

            # 批量處理文件
            for i in range(0, len(images_to_process), batch_size):
                batch = images_to_process[i:i + batch_size]
                self.logger.info(f"處理批次 {i//batch_size + 1}/{(len(images_to_process) + batch_size - 1)//batch_size}: {len(batch)} 個圖像")
                
                self._convert_coco_images_parallel(batch, annotations_by_image, 
                                                 categories, img_dir, output_path, 
                                                 target_shape, resize, quality)
                
                # 更新已處理文件列表
                for img in batch:
                    processed_files.append(str(output_path / f"{img['id']}.json"))
                
                # 每批次保存一次檢查點
                self._save_checkpoint(output_path, processed_files)
                
                # 定期釋放內存
                gc.collect()

        except Exception as e:
            self.logger.error(f"轉換 COCO 格式失敗, 錯誤: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _find_coco_file(self, input_path: Path) -> Optional[Path]:
        """
        查找COCO格式的JSON文件
        
        參數:
            input_path: 輸入路徑
            
        返回:
            COCO文件路徑，如果找不到則返回None
        """
        if input_path.is_file() and input_path.suffix.lower() == ".json":
            # 檢查是否是COCO格式
            try:
                with open(input_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if "images" in data and "annotations" in data and "categories" in data:
                        return input_path
            except Exception:
                pass
        
        elif input_path.is_dir():
            # 查找目錄中的 COCO 格式 JSON 文件
            json_files = list(input_path.glob("*.json"))
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if "images" in data and "annotations" in data and "categories" in data:
                            return json_file
                except Exception:
                    continue
        
        return None

    def _convert_coco_images_parallel(self, 
                                     images: List[Dict[str, Any]],
                                     annotations_by_image: Dict[int, List[Dict[str, Any]]],
                                     categories: Dict[int, str],
                                     img_dir: Path,
                                     output_path: Path,
                                     target_shape: str,
                                     resize: Union[float, Tuple[int, int]],
                                     quality: int) -> None:
        """
        並行處理COCO圖像
        
        參數:
            images: 圖像信息列表
            annotations_by_image: 按圖像ID組織的標註數據
            categories: 類別ID到名稱的映射
            img_dir: 圖像目錄
            output_path: 輸出目錄
            target_shape: 目標形狀
            resize: 圖像縮放參數
            quality: 圖像品質參數
        """
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for img_info in images:
                future = executor.submit(
                    self._convert_coco_image, img_info, annotations_by_image, 
                    categories, img_dir, output_path, target_shape, resize, quality
                )
                futures.append(future)
            
            # 收集結果
            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    if result:
                        with self.stats_lock:
                            self.stats["success"] += 1
                    else:
                        with self.stats_lock:
                            self.stats["failed"] += 1
                except Exception as e:
                    self.logger.error(f"處理失敗: {e}")
                    with self.stats_lock:
                        self.stats["failed"] += 1
                
                # 每10個更新一次進度
                if (i + 1) % 10 == 0:
                    with self.stats_lock:
                        processed = self.stats["success"] + self.stats["failed"] + self.stats["skipped"]
                        self.logger.info(f"進度: {processed}/{self.stats['total']} ({processed/self.stats['total']*100:.1f}%)")

    def _convert_coco_image(self, 
                           img_info: Dict[str, Any],
                           annotations_by_image: Dict[int, List[Dict[str, Any]]],
                           categories: Dict[int, str],
                           img_dir: Path,
                           output_path: Path,
                           target_shape: str,
                           resize: Union[float, Tuple[int, int]],
                           quality: int) -> bool:
        """
        轉換單個COCO圖像
        
        參數:
            img_info: 圖像信息
            annotations_by_image: 按圖像ID組織的標註數據
            categories: 類別ID到名稱的映射
            img_dir: 圖像目錄
            output_path: 輸出目錄
            target_shape: 目標形狀
            resize: 圖像縮放參數
            quality: 圖像品質參數
            
        返回:
            是否成功
        """
        try:
            img_id = img_info["id"]
            img_file_name = img_info["file_name"]
            img_width = img_info["width"]
            img_height = img_info["height"]

            # 獲取圖像的標註
            anns = annotations_by_image.get(img_id, [])

            annotations = []
            for ann in anns:
                cat_id = ann.get("category_id")
                label = categories.get(cat_id, f"category_{cat_id}")

                # 處理分割標註（多邊形）
                if "segmentation" in ann and ann["segmentation"] and target_shape == 'polygon':
                    for seg in ann["segmentation"]:
                        if len(seg) >= 6:  # 至少需要三個點
                            points = []
                            for i in range(0, len(seg), 2):
                                points.append([seg[i], seg[i+1]])

                            annotations.append({
                                "label": label,
                                "points": points,
                                "group_id": None,
                                "shape_type": "polygon",
                                "flags": {}
                            })
                else:  # 使用邊界框
                    bbox = ann.get("bbox", [0, 0, 0, 0])
                    x, y, w, h = bbox
                    xmin, ymin = x, y
                    xmax, ymax = x + w, y + h

                    if target_shape == 'polygon':
                        points = self._get_polygon_from_box(
                            xmin, ymin, xmax, ymax)
                        shape_type = "polygon"
                    else:
                        points = [[xmin, ymin], [xmax, ymax]]
                        shape_type = "rectangle"

                    annotations.append({
                        "label": label,
                        "points": points,
                        "group_id": None,
                        "shape_type": shape_type,
                        "flags": {}
                    })

            # 創建 LabelMe JSON
            labelme_data = {
                "version": "4.5.6",
                "flags": {},
                "annotations": annotations,
                "imagePath": img_file_name,
                "imageData": None,
                "imageHeight": img_height,
                "imageWidth": img_width
            }

            # 寫入 JSON 文件
            output_stem = Path(img_file_name).stem
            output_json = output_path / f"{output_stem}.json"
            with open(output_json, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, ensure_ascii=False, indent=2)
            
            # 尋找並複製對應的圖像文件到輸出目錄
            image_file_path = img_dir / img_file_name
            if image_file_path.exists():
                self._copy_image_file(img_dir, output_path, img_file_name, resize=resize, quality=quality)

            self.logger.debug(f"成功轉換: {img_file_name} -> {output_json.name}")
            return True

        except Exception as e:
            self.logger.error(f"轉換COCO圖像失敗: {e}")
            return False

    def _get_polygon_from_box(self, xmin: float, ymin: float, xmax: float, ymax: float) -> List[List[float]]:
        """
        將矩形框轉為四角多邊形
        
        參數:
            xmin, ymin, xmax, ymax: 矩形框座標
            
        返回:
            多邊形座標列表
        """
        return [[xmin, ymin], [xmax, ymin], [xmax, ymax], [xmin, ymax]]

    def _copy_image_file(self, 
                        source_dir: Union[str, Path], 
                        target_dir: Union[str, Path], 
                        image_name: str, 
                        resize: Union[float, Tuple[int, int], None] = None, 
                        quality: int = 75) -> Tuple[bool, Optional[Tuple[int, int]], Optional[Tuple[int, int]], float, float]:
        """
        複製圖像文件到目標目錄，並可調整大小和品質
        
        參數:
            source_dir: 源目錄
            target_dir: 目標目錄
            image_name: 圖像文件名
            resize: 可以是縮放比例(如0.3)或(寬,高)元組
            quality: JPEG壓縮品質(1-100)
            
        返回:
            (成功標誌, 原始尺寸, 新尺寸, 水平縮放比例, 垂直縮放比例)
        """
        source_dir = Path(source_dir)
        target_dir = Path(target_dir)
        target_dir.mkdir(exist_ok=True, parents=True)
        
        # 首先嘗試直接在源目錄找到圖像
        source_path = source_dir / image_name
        if not source_path.exists():
            # 如果直接路徑不存在，使用擴展搜索功能
            source_path = self._find_related_files(
                source_dir,
                Path(image_name).stem,
                ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                recursive=True
            )
            
            if not source_path:
                self.logger.warning(f"找不到圖像文件: {image_name}")
                return False, None, None, 1.0, 1.0
        
        target_path = target_dir / Path(image_name).name
        
        # 如果源目錄和目標目錄相同且不需要調整大小/品質，則不需要處理
        if source_dir == target_dir and resize is None and quality == 100 and source_path.suffix.lower() in ['.jpg', '.jpeg']:
            return True, None, None, 1.0, 1.0
            
        try:
            # 讀取並處理圖像
            with Image.open(source_path) as img:
                # 保存原始尺寸
                orig_width, orig_height = img.size
                orig_size = (orig_width, orig_height)
                scale_x, scale_y = 1.0, 1.0
                new_size = orig_size

                # 調整大小
                if resize is not None:
                    if isinstance(resize, tuple) and len(resize) == 2:
                        # 如果resize是(寬,高)元組
                        new_size = resize
                        scale_x = new_size[0] / orig_width  # 水平縮放比例
                        scale_y = new_size[1] / orig_height  # 垂直縮放比例
                        img = img.resize(new_size, Image.LANCZOS)
                    elif isinstance(resize, (int, float)):
                        # 如果resize是縮放比例
                        scale_x = scale_y = float(resize)  # 等比例縮放
                        new_size = (int(orig_width * scale_x), int(orig_height * scale_y))
                        img = img.resize(new_size, Image.LANCZOS)
                
                # 確保目標路徑的副檔名是.jpg
                if target_path.suffix.lower() not in ['.jpg', '.jpeg']:
                    target_path = target_path.with_suffix('.jpg')
                
                # 儲存為JPEG，設定品質
                img.save(target_path, 'JPEG', quality=quality, optimize=True)
                
            self.logger.debug(f"已處理並儲存圖像: {source_path.name} -> {target_path.name} (縮放: {scale_x:.2f}x{scale_y:.2f})")
            return True, orig_size, new_size, scale_x, scale_y
            
        except Exception as e:
            self.logger.error(f"處理圖像文件失敗: {source_path.name}, 錯誤: {e}")
            return False, None, None, 1.0, 1.0

    def validate_annotations(self, 
                           input_path: Union[str, Path], 
                           output_path: Optional[Union[str, Path]] = None, 
                           clean_image_data: bool = True, 
                           resize: Union[float, Tuple[int, int], None] = None, 
                           quality: int = 75,
                           checkpoint: Optional[Dict[str, Any]] = None,
                           batch_size: int = 10) -> Dict[str, Any]:
        """
        檢查和處理 LabelMe 格式文件
        
        參數:
            input_path: 輸入目錄，包含 LabelMe JSON 文件
            output_path: 輸出目錄，默認與輸入相同
            clean_image_data: 是否清除 imageData 中的 base64 編碼
            resize: 圖像縮放參數
            quality: 圖像品質參數
            checkpoint: 檢查點數據
            batch_size: 批處理大小
            
        返回:
            檢查統計信息
        """
        input_path = Path(input_path)
        if output_path:
            output_path = Path(output_path)
        else:
            output_path = input_path

        # 確保輸出目錄存在
        output_path.mkdir(parents=True, exist_ok=True)

        # 獲取所有 JSON 文件（包括子目錄）
        annotation_files = list(input_path.glob("**/*.json"))

        # 加載已處理文件列表
        processed_files = []
        if checkpoint and "processed_files" in checkpoint:
            processed_files = checkpoint["processed_files"]
            self.logger.info(f"從檢查點恢復: 跳過 {len(processed_files)} 個已處理檔案")

        # 過濾已處理文件
        annotation_files_to_process = [f for f in annotation_files if str(f) not in processed_files]

        stats = {
            "total": len(annotation_files),
            "valid": 0,
            "missing_image": 0,
            "cleaned_image_data": 0,
            "other_errors": 0
        }

        self.logger.info(f"開始檢查 {len(annotation_files_to_process)}/{len(annotation_files)} 個 LabelMe 文件")

        # 如果檔案較少，不使用批處理
        if len(annotation_files_to_process) <= batch_size:
            self._validate_annotations_parallel(annotation_files_to_process, input_path, output_path, 
                                              clean_image_data, resize, quality, stats)
            return stats

        # 批量處理文件
        for i in range(0, len(annotation_files_to_process), batch_size):
            batch = annotation_files_to_process[i:i + batch_size]
            self.logger.info(f"處理批次 {i//batch_size + 1}/{(len(annotation_files_to_process) + batch_size - 1)//batch_size}: {len(batch)} 個檔案")
            
            batch_stats = {
                "total": len(batch),
                "valid": 0,
                "missing_image": 0,
                "cleaned_image_data": 0,
                "other_errors": 0
            }
            
            self._validate_annotations_parallel(batch, input_path, output_path, 
                                              clean_image_data, resize, quality, batch_stats)
            
            # 更新統計信息
            for key in batch_stats:
                if key != "total":  # 總數已在外部設置
                    stats[key] += batch_stats[key]
            
            # 更新已處理文件列表
            processed_files.extend([str(f) for f in batch])
            
            # 每批次保存一次檢查點
            if checkpoint is not None:
                self._save_checkpoint(output_path, processed_files)
            
            # 定期釋放內存
            gc.collect()

        self.logger.info(
            f"檢查完成: 總計 {stats['total']} 個文件，有效 {stats['valid']}，"
            f"缺少圖像 {stats['missing_image']}，清理 imageData {stats['cleaned_image_data']}，"
            f"其他錯誤 {stats['other_errors']}")

        return stats

    def _validate_annotations_parallel(self, 
                                     files: List[Path],
                                     input_path: Path,
                                     output_path: Path,
                                     clean_image_data: bool,
                                     resize: Union[float, Tuple[int, int], None],
                                     quality: int,
                                     stats: Dict[str, int]) -> None:
        """
        並行處理LabelMe標註文件
        
        參數:
            files: 要處理的文件列表
            input_path: 輸入目錄
            output_path: 輸出目錄
            clean_image_data: 是否清除 imageData
            resize: 圖像縮放參數
            quality: 圖像品質參數
            stats: 統計信息字典，將被修改
        """
        stats_lock = threading.Lock()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for annotation_file in files:
                future = executor.submit(
                    self._validate_annotation_file, annotation_file, input_path, output_path, 
                    clean_image_data, resize, quality
                )
                futures.append(future)
            
            # 收集結果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    with stats_lock:
                        if result == "valid":
                            stats["valid"] += 1
                        elif result == "missing_image":
                            stats["missing_image"] += 1
                        elif result == "cleaned_image_data":
                            stats["cleaned_image_data"] += 1
                            stats["valid"] += 1
                        else:
                            stats["other_errors"] += 1
                except Exception as e:
                    self.logger.error(f"處理標註文件時出錯: {e}")
                    with stats_lock:
                        stats["other_errors"] += 1

    def _validate_annotation_file(self, 
                                 annotation_file: Path,
                                 input_path: Path,
                                 output_path: Path,
                                 clean_image_data: bool,
                                 resize: Union[float, Tuple[int, int], None],
                                 quality: int) -> str:
        """
        驗證和處理單個LabelMe標註文件
        
        參數:
            annotation_file: 標註文件路徑
            input_path: 輸入目錄
            output_path: 輸出目錄
            clean_image_data: 是否清除 imageData
            resize: 圖像縮放參數
            quality: 圖像品質參數
            
        返回:
            處理結果: "valid", "missing_image", "cleaned_image_data", "error"
        """
        try:
            # 讀取 JSON
            with open(annotation_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 檢查是否是LabelMe格式
            if not self._is_labelme_format(data):
                self.logger.warning(f"{annotation_file.name}: 不是有效的LabelMe格式，跳過")
                return "error"
            
            # 檢查 imagePath
            image_path = data.get("imagePath")
            if not image_path:
                self.logger.warning(f"{annotation_file.name}: 缺少 imagePath")
                return "error"
            
            # 查找對應的圖像文件
            # 首先嘗試直接在JSON文件所在目錄找圖像
            found_image_path = self._find_related_files(
                annotation_file.parent, 
                image_path, 
                ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                recursive=True
            )
            
            # 如果找不到圖像，嘗試使用JSON文件名稱查找
            if not found_image_path:
                found_image_path = self._find_related_files(
                    annotation_file.parent, 
                    annotation_file.stem, 
                    ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                    recursive=True
                )
            
            # 如果仍然找不到圖像，標記為缺少圖像
            if not found_image_path:
                self.logger.warning(f"{annotation_file.name}: 找不到對應的圖像文件")
                
                # 如果是同一路徑，直接刪除缺少圖像的 JSON 文件
                if input_path == output_path:
                    os.remove(annotation_file)
                    self.logger.info(f"已刪除缺少圖像的標籤文件: {annotation_file.name}")
                return "missing_image"
            
            # 更新圖像路徑為找到的實際文件名
            data["imagePath"] = found_image_path.name
            
            # 清除 imageData
            result = "valid"
            if clean_image_data and "imageData" in data and data["imageData"]:
                data["imageData"] = None
                result = "cleaned_image_data"
                
            # 複製對應的圖像文件到輸出目錄，並進行調整
            if found_image_path:
                try:
                    # 使用改進的圖像處理函數，獲取縮放資訊
                    success, orig_size, new_size, scale_x, scale_y = self._copy_image_file(
                        found_image_path.parent, output_path, found_image_path.name, resize=resize, quality=quality
                    )
                    
                    # 如果圖像成功處理並且尺寸發生變化
                    if success and orig_size and new_size and orig_size != new_size and scale_x is not None and scale_y is not None:
                        # 調整標籤座標
                        data = self._adjust_annotation_coordinates(
                            data, scale_x, scale_y, new_size[0], new_size[1]
                        )
                        self.logger.debug(f"已調整標籤座標: {annotation_file.name} (縮放: {scale_x:.2f}x{scale_y:.2f})")
                        
                except Exception as e:
                    self.logger.error(f"處理圖像或調整座標時出錯: {found_image_path.name}, 錯誤: {e}")
            
            # 保存修改後的 JSON
            output_json = output_path / annotation_file.name
            with open(output_json, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return result
            
        except Exception as e:
            self.logger.error(f"處理 {annotation_file.name} 時出錯: {e}")
            return "error"

    def _find_related_files(self, 
                          base_path: Union[str, Path], 
                          filename: str, 
                          extensions: List[str], 
                          recursive: bool = True) -> Optional[Path]:
        """
        在目錄中尋找相關文件（可能在子目錄中）
        
        參數:
            base_path: 基礎路徑
            filename: 文件名（可以是帶或不帶擴展名的基本名稱）
            extensions: 可能的擴展名列表，如 ['.jpg', '.png']
            recursive: 是否遞迴搜索子目錄
            
        返回:
            找到的文件路徑，如果沒找到則返回 None
        """
        base_path = Path(base_path)
        basename = Path(filename).stem
        
        # 首先在當前目錄中尋找
        for ext in extensions:
            file_path = base_path / f"{basename}{ext}"
            if file_path.exists():
                return file_path
            
            # 如果提供的是完整文件名，直接檢查
            if filename.endswith(ext):
                file_path = base_path / filename
                if file_path.exists():
                    return file_path
        
        # 如果沒找到並且允許遞迴搜索
        if recursive:
            # 在子目錄中尋找
            for subdir in base_path.glob('*'):
                if subdir.is_dir():
                    # 檢查常見的子目錄命名模式
                    common_dirs = [subdir]
                    if subdir.name.lower() in ['images', 'image', 'img', 'imgs', 'pictures', 'pics']:
                        common_dirs.append(subdir)
                    elif 'label' in subdir.name.lower() or 'annotation' in subdir.name.lower() or 'json' in subdir.name.lower():
                        # 如果是標籤目錄，可能需要檢查平行的圖像目錄
                        for img_dir_name in ['images', 'image', 'img', 'imgs', 'pictures', 'pics']:
                            img_dir = base_path / img_dir_name
                            if img_dir.exists():
                                common_dirs.append(img_dir)
                    
                    # 在這些目錄中尋找文件
                    for search_dir in common_dirs:
                        for ext in extensions:
                            file_path = search_dir / f"{basename}{ext}"
                            if file_path.exists():
                                return file_path
                            
                            # 如果提供的是完整文件名，直接檢查
                            if filename.endswith(ext):
                                file_path = search_dir / filename
                                if file_path.exists():
                                    return file_path
                    
                    # 遞迴搜索更深層的子目錄
                    result = self._find_related_files(subdir, basename, extensions, recursive=True)
                    if result:
                        return result
        
        return None

    def _adjust_annotation_coordinates(self, 
                                      data: Dict[str, Any], 
                                      scale_x: float, 
                                      scale_y: float, 
                                      new_width: int, 
                                      new_height: int) -> Dict[str, Any]:
        """
        調整標籤座標以匹配縮放後的圖像尺寸
        
        參數:
            data: LabelMe格式的標籤數據
            scale_x: X方向縮放比例
            scale_y: Y方向縮放比例
            new_width: 新圖像寬度
            new_height: 新圖像高度
        
        返回:
            調整後的標籤數據
        """
        # 更新圖像尺寸
        data["imageWidth"] = new_width
        data["imageHeight"] = new_height
        
        # 更新所有標籤的座標
        if "annotations" in data:
            for anno in data["annotations"]:
                # 處理點座標
                if "points" in anno:
                    new_points = []
                    for point in anno["points"]:
                        # 根據縮放比例調整座標
                        new_x = point[0] * scale_x
                        new_y = point[1] * scale_y
                        new_points.append([new_x, new_y])
                    anno["points"] = new_points
        
        # 如果使用舊版LabelMe格式(shapes而非annotations)
        elif "shapes" in data:
            for shape in data["shapes"]:
                # 處理點座標
                if "points" in shape:
                    new_points = []
                    for point in shape["points"]:
                        # 根據縮放比例調整座標
                        new_x = point[0] * scale_x
                        new_y = point[1] * scale_y
                        new_points.append([new_x, new_y])
                    shape["points"] = new_points
        
        return data

    def process_multiple_inputs(self, 
                              input_paths: List[Union[str, Path]], 
                              output_path: Union[str, Path], 
                              resize: Union[float, Tuple[int, int], None] = None, 
                              quality: int = 75, 
                              force_overwrite: bool = False,
                              batch_size: int = 10) -> Dict[str, Any]:
        """
        處理多個輸入路徑
        
        參數:
            input_paths: 輸入路徑列表
            output_path: 輸出目錄路徑
            resize: 圖像縮放參數
            quality: 圖像品質參數
            force_overwrite: 強制覆寫已存在檔案
            batch_size: 批處理大小
        
        返回:
            整體處理統計信息
        """
        start_time = time.time()
        
        overall_stats = {
            "total_processed": 0,
            "total_success": 0,
            "total_failed": 0,
            "total_skipped": 0,
            "total_reused": 0,  # 新增：重用已存在檔案的計數
            "paths": {}
        }

        # 確保輸出目錄存在
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)

        for input_path in input_paths:
            input_path = Path(input_path)
            
            # 為每個輸入路徑創建對應的輸出子目錄
            path_output = output_path / input_path.name
            path_output.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"開始處理輸入路徑: {input_path}")
            
            # 使用 polygon 作為默認目標形狀
            stats = self.convert_format(
                input_path, 
                path_output, 
                source_format='auto', 
                target_shape='polygon',
                resize=resize,
                quality=quality,
                batch_size=batch_size
            )
            
            # 記錄每個路徑的處理結果
            overall_stats["paths"][str(input_path)] = stats
            
            # 更新總體統計
            overall_stats["total_processed"] += stats.get("total", 0)
            overall_stats["total_success"] += stats.get("success", 0) 
            overall_stats["total_failed"] += stats.get("failed", 0)
            overall_stats["total_skipped"] += stats.get("skipped", 0)
            overall_stats["total_reused"] += stats.get("reused", 0)  # 更新重用檔案統計
            
            self.logger.info(f"完成處理: {input_path}")
        
        end_time = time.time()
        self.logger.info(f"所有路徑處理完成！用時 {end_time - start_time:.2f} 秒")
        self.logger.info(f"總計處理: {overall_stats['total_processed']} 個文件")
        self.logger.info(f"總成功: {overall_stats['total_success']}, 總失敗: {overall_stats['total_failed']}, 總跳過: {overall_stats['total_skipped']}, 總重用: {overall_stats['total_reused']}")
        
        return overall_stats


# 如果作為獨立腳本運行
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="標籤格式自動轉換與檢查工具")
    parser.add_argument("--inputs", nargs='+', required=True, help="輸入路徑，可指定多個路徑")
    parser.add_argument("--output", required=True, help="輸出路徑")
    parser.add_argument("--resize", type=str, default="0.3", help="調整圖像大小，可以是縮放比例(如0.3)或寬x高格式(如'800x600')")
    parser.add_argument("--quality", type=int, default=85, help="JPEG品質設定(1-100)，預設為85")
    parser.add_argument("--force", action="store_true", help="強制覆寫已存在檔案")
    parser.add_argument("--format", choices=['auto', 'voc', 'yolo', 'coco', 'labelme'], default='auto', help="輸入格式")
    parser.add_argument("--shape", choices=['box', 'polygon'], default='polygon', help="目標形狀")
    parser.add_argument("--threads", type=int, default=2, help="並行處理的線程數(預設2)")
    parser.add_argument("--batch-size", type=int, default=10, help="批處理大小(預設10預設10)")
    parser.add_argument("--no-resume", action="store_true", help="不從中斷點恢復")
    
    args = parser.parse_args()

    # 處理resize參數
    resize_value = args.resize
    if 'x' in resize_value:
        # 如果是寬x高格式
        try:
            width, height = map(int, resize_value.split('x'))
            resize_param = (width, height)
        except ValueError:
            print(f"無效的尺寸格式: {resize_value}，使用預設值0.3")
            resize_param = 0.3
    else:
        # 如果是縮放比例
        try:
            resize_param = float(resize_value)
        except ValueError:
            print(f"無效的縮放比例: {resize_value}，使用預設值0.3")
            resize_param = 0.3

    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("annotation_converter.log", mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("AnnotationConverter")

    # 記錄開始時間
    start_time = time.time()
    logger.info(f"開始執行轉換任務，輸入: {args.inputs}, 輸出: {args.output}")

    # 創建轉換器
    converter = AnnotationConverter(logger, max_workers=args.threads)
    
    if len(args.inputs) == 1 and args.format != 'auto':
        # 單一輸入，使用指定格式
        stats = converter.convert_format(
            input_path=args.inputs[0],
            output_path=args.output,
            source_format=args.format,
            target_shape=args.shape,
            resize=resize_param,
            quality=args.quality,
            batch_size=args.batch_size,
            resume=not args.no_resume
        )
    else:
        # 處理多個輸入路徑
        stats = converter.process_multiple_inputs(
            args.inputs, 
            args.output, 
            resize=resize_param, 
            quality=args.quality,
            force_overwrite=args.force,
            batch_size=args.batch_size
        )
    
    # 計算總耗時
    end_time = time.time()
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    logger.info(f"處理完成！總耗時: {int(hours)}小時 {int(minutes)}分 {seconds:.2f}秒")
    
    if "total_processed" in stats:
        print(f"\n處理完成！共處理 {stats['total_processed']} 個檔案")
        print(f"成功: {stats['total_success']}，失敗: {stats['total_failed']}，跳過: {stats['total_skipped']}，重用: {stats.get('total_reused', 0)}")
    else:
        print(f"\n處理完成！共處理 {stats['total']} 個檔案")
        print(f"成功: {stats['success']}，失敗: {stats['failed']}，跳過: {stats.get('skipped', 0)}")
    
    print(f"圖像處理參數: 縮放={resize_param}, 品質={args.quality}")
    print(f"詳細日誌請查看 annotation_converter.log")