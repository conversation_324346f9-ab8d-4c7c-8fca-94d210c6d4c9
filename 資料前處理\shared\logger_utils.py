"""
日誌工具

提供統一的日誌記錄功能
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime
import traceback


class StructuredLogger:
    """結構化日誌記錄器"""
    
    def __init__(self, name: str, log_file: Optional[Union[str, Path]] = None):
        """
        初始化結構化日誌記錄器
        
        Args:
            name: 日誌記錄器名稱
            log_file: 日誌文件路徑（可選）
        """
        self.logger = logging.getLogger(name)
        self.name = name
        
        # 避免重複添加處理器
        if not self.logger.handlers:
            self._setup_handlers(log_file)
    
    @classmethod
    def create_logger(cls, name: str, log_file: Optional[Union[str, Path]] = None) -> logging.Logger:
        """
        創建標準logger實例（類方法）
        
        Args:
            name: 日誌記錄器名稱
            log_file: 日誌文件路徑（可選）
            
        Returns:
            logging.Logger: 標準logger實例
        """
        structured_logger = cls(name, log_file)
        return structured_logger.logger
    
    def _setup_handlers(self, log_file: Optional[Union[str, Path]] = None):
        """設置日誌處理器"""
        # 設置格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台處理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        self.logger.addHandler(console_handler)
        
        # 文件處理器（如果指定了文件）
        if log_file:
            log_file = Path(log_file)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)
            self.logger.addHandler(file_handler)
        
        # 設置整體日誌級別
        self.logger.setLevel(logging.DEBUG)
    
    def set_level(self, level: Union[str, int]):
        """
        設置日誌級別
        
        Args:
            level: 日誌級別
        """
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        self.logger.setLevel(level)
    
    def log_processing_start(self, file_path: str, operation: str, **kwargs):
        """
        記錄處理開始
        
        Args:
            file_path: 文件路徑
            operation: 操作類型
            **kwargs: 額外信息
        """
        extra_info = " | ".join(f"{k}={v}" for k, v in kwargs.items())
        message = f"開始{operation}: {file_path}"
        if extra_info:
            message += f" | {extra_info}"
        self.logger.info(message)
    
    def log_processing_success(self, file_path: str, operation: str, **kwargs):
        """
        記錄處理成功
        
        Args:
            file_path: 文件路徑
            operation: 操作類型
            **kwargs: 額外信息
        """
        extra_info = " | ".join(f"{k}={v}" for k, v in kwargs.items())
        message = f"{operation}成功: {file_path}"
        if extra_info:
            message += f" | {extra_info}"
        self.logger.info(message)
    
    def log_processing_error(self, error: Exception, file_path: str = None, **kwargs):
        """
        記錄處理錯誤
        
        Args:
            error: 異常對象
            file_path: 文件路徑
            **kwargs: 額外信息
        """
        extra_info = " | ".join(f"{k}={v}" for k, v in kwargs.items())
        
        message_parts = [f"處理失敗: {error}"]
        if file_path:
            message_parts.append(f"文件: {file_path}")
        if extra_info:
            message_parts.append(extra_info)
        
        message = " | ".join(message_parts)
        self.logger.error(message)
        
        # 記錄詳細的堆疊信息到debug級別
        self.logger.debug(f"錯誤堆疊: {traceback.format_exc()}")
    
    def log_processing_skip(self, file_path: str, reason: str, **kwargs):
        """
        記錄跳過處理
        
        Args:
            file_path: 文件路徑
            reason: 跳過原因
            **kwargs: 額外信息
        """
        extra_info = " | ".join(f"{k}={v}" for k, v in kwargs.items())
        message = f"跳過處理: {file_path} | 原因: {reason}"
        if extra_info:
            message += f" | {extra_info}"
        self.logger.debug(message)
    
    def log_progress(self, current: int, total: int, operation: str = "處理", **kwargs):
        """
        記錄進度信息
        
        Args:
            current: 當前數量
            total: 總數量
            operation: 操作類型
            **kwargs: 額外信息
        """
        percentage = (current / total * 100) if total > 0 else 0
        extra_info = " | ".join(f"{k}={v}" for k, v in kwargs.items())
        
        message = f"{operation}進度: {current}/{total} ({percentage:.1f}%)"
        if extra_info:
            message += f" | {extra_info}"
        
        self.logger.info(message)
    
    def log_statistics(self, stats: Dict[str, Any], operation: str = "處理"):
        """
        記錄統計信息
        
        Args:
            stats: 統計字典
            operation: 操作類型
        """
        message = f"{operation}統計:"
        for key, value in stats.items():
            message += f" | {key}={value}"
        
        self.logger.info(message)
    
    def log_config(self, config: Dict[str, Any], config_name: str = "配置"):
        """
        記錄配置信息
        
        Args:
            config: 配置字典
            config_name: 配置名稱
        """
        self.logger.info(f"使用{config_name}:")
        for key, value in config.items():
            self.logger.info(f"  {key}: {value}")
    
    def debug(self, message: str, **kwargs):
        """調試級別日誌"""
        self._log_with_extra(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息級別日誌"""
        self._log_with_extra(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告級別日誌"""
        self._log_with_extra(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """錯誤級別日誌"""
        self._log_with_extra(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """嚴重錯誤級別日誌"""
        self._log_with_extra(logging.CRITICAL, message, **kwargs)
    
    def _log_with_extra(self, level: int, message: str, **kwargs):
        """
        帶額外信息的日誌記錄
        
        Args:
            level: 日誌級別
            message: 日誌消息
            **kwargs: 額外信息
        """
        if kwargs:
            extra_info = " | ".join(f"{k}={v}" for k, v in kwargs.items())
            message = f"{message} | {extra_info}"
        
        self.logger.log(level, message)


class PerformanceLogger:
    """性能日誌記錄器"""
    
    def __init__(self, logger: StructuredLogger):
        """
        初始化性能日誌記錄器
        
        Args:
            logger: 結構化日誌記錄器
        """
        self.logger = logger
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """
        開始計時
        
        Args:
            operation: 操作名稱
        """
        self.start_times[operation] = datetime.now()
        self.logger.debug(f"開始計時: {operation}")
    
    def end_timer(self, operation: str, log_level: str = "info"):
        """
        結束計時並記錄
        
        Args:
            operation: 操作名稱
            log_level: 日誌級別
        """
        if operation not in self.start_times:
            self.logger.warning(f"未找到計時器: {operation}")
            return
        
        end_time = datetime.now()
        duration = end_time - self.start_times[operation]
        duration_seconds = duration.total_seconds()
        
        # 選擇合適的時間單位
        if duration_seconds < 1:
            time_str = f"{duration_seconds * 1000:.1f}ms"
        elif duration_seconds < 60:
            time_str = f"{duration_seconds:.1f}s"
        else:
            minutes = duration_seconds // 60
            seconds = duration_seconds % 60
            time_str = f"{int(minutes)}m{seconds:.1f}s"
        
        message = f"{operation}耗時: {time_str}"
        
        # 根據指定級別記錄
        log_method = getattr(self.logger, log_level.lower(), self.logger.info)
        log_method(message)
        
        # 清理計時器
        del self.start_times[operation]
    
    def log_memory_usage(self, operation: str = "當前"):
        """
        記錄內存使用情況
        
        Args:
            operation: 操作名稱
        """
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            rss_mb = memory_info.rss / 1024 / 1024
            vms_mb = memory_info.vms / 1024 / 1024
            
            self.logger.info(f"{operation}內存使用: RSS={rss_mb:.1f}MB, VMS={vms_mb:.1f}MB")
            
        except ImportError:
            self.logger.debug("psutil未安裝，無法記錄內存使用情況")
        except Exception as e:
            self.logger.warning(f"記錄內存使用失敗: {e}")


def setup_logger(name: str, 
                 level: str = "INFO",
                 log_file: Optional[Union[str, Path]] = None,
                 console_output: bool = True) -> StructuredLogger:
    """
    設置統一的日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        level: 日誌級別
        log_file: 日誌文件路徑
        console_output: 是否輸出到控制台
        
    Returns:
        StructuredLogger: 配置好的日誌記錄器
    """
    logger = StructuredLogger(name, log_file)
    logger.set_level(level)
    
    if not console_output:
        # 移除控制台處理器
        for handler in logger.logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                logger.logger.removeHandler(handler)
    
    return logger


def create_log_file_path(tool_name: str, base_dir: Optional[Union[str, Path]] = None) -> Path:
    """
    創建日誌文件路徑
    
    Args:
        tool_name: 工具名稱
        base_dir: 基礎目錄
        
    Returns:
        Path: 日誌文件路徑
    """
    if base_dir is None:
        base_dir = Path.cwd() / "logs"
    else:
        base_dir = Path(base_dir)
    
    # 確保日誌目錄存在
    base_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成帶時間戳的日誌文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"{tool_name}_{timestamp}.log"
    
    return base_dir / log_filename