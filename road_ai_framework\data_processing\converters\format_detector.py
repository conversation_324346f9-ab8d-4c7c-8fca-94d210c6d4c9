#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式檢測器模組

負責檢測各種標註格式（LabelMe、COCO、VOC、YOLO）
"""

import json
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from shared.exceptions import FormatDetectionError
from shared.logger_utils import StructuredLogger


class FormatDetector:
    """
    標註格式檢測器
    
    支援自動檢測以下格式：
    - LabelMe JSON
    - COCO JSON
    - VOC XML
    - YOLO TXT
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化格式檢測器
        
        Args:
            logger: 日誌記錄器
        """
        self.logger = logger or StructuredLogger.create_logger("FormatDetector")
        
        # 支援的格式
        self.supported_formats = ['labelme', 'coco', 'voc', 'yolo']
        
        # 格式檢測權重（檢測可信度）
        self.format_confidence = {}
    
    def detect_format(self, input_path: Path) -> str:
        """
        檢測輸入路徑的標註格式
        
        Args:
            input_path: 輸入文件或目錄路徑
            
        Returns:
            str: 檢測到的格式名稱
            
        Raises:
            FormatDetectionError: 格式檢測失敗
        """
        try:
            input_path = Path(input_path)
            
            if input_path.is_file():
                return self._detect_file_format(input_path)
            elif input_path.is_dir():
                return self._detect_directory_format(input_path)
            else:
                raise FormatDetectionError(f"路徑不存在: {input_path}")
                
        except Exception as e:
            self.logger.error(f"格式檢測失敗: {e}")
            raise FormatDetectionError(f"格式檢測失敗: {e}")
    
    def _detect_directory_format(self, dir_path: Path) -> str:
        """
        檢測目錄中的標註格式
        
        Args:
            dir_path: 目錄路徑
            
        Returns:
            str: 檢測到的格式名稱
        """
        format_counts = {fmt: 0 for fmt in self.supported_formats}
        total_files = 0
        
        # 檢查JSON文件
        json_files = list(dir_path.glob("*.json"))
        for json_file in json_files[:10]:  # 最多檢查10個文件
            try:
                file_format = self._detect_file_format(json_file)
                format_counts[file_format] += 1
                total_files += 1
            except:
                continue
        
        # 檢查XML文件
        xml_files = list(dir_path.glob("*.xml"))
        for xml_file in xml_files[:10]:  # 最多檢查10個文件
            try:
                file_format = self._detect_file_format(xml_file)
                format_counts[file_format] += 1
                total_files += 1
            except:
                continue
        
        # 檢查TXT文件和classes.txt
        if (dir_path / "classes.txt").exists():
            format_counts['yolo'] += 5  # 增加YOLO權重
        
        txt_files = list(dir_path.glob("*.txt"))
        for txt_file in txt_files[:10]:  # 最多檢查10個文件
            if txt_file.name != "classes.txt":
                try:
                    file_format = self._detect_file_format(txt_file)
                    format_counts[file_format] += 1
                    total_files += 1
                except:
                    continue
        
        if total_files == 0:
            raise FormatDetectionError("目錄中沒有找到標註文件")
        
        # 選擇最多的格式
        detected_format = max(format_counts, key=format_counts.get)
        confidence = format_counts[detected_format] / total_files
        
        self.logger.info(f"目錄格式檢測結果: {detected_format} (可信度: {confidence:.2f})")
        
        if confidence < 0.3:
            raise FormatDetectionError(f"格式檢測可信度過低: {confidence:.2f}")
        
        return detected_format
    
    def _detect_file_format(self, file_path: Path) -> str:
        """
        檢測單個文件的標註格式
        
        Args:
            file_path: 文件路徑
            
        Returns:
            str: 檢測到的格式名稱
        """
        file_extension = file_path.suffix.lower()
        
        if file_extension == '.json':
            return self._detect_json_format(file_path)
        elif file_extension == '.xml':
            return self._detect_xml_format(file_path)
        elif file_extension == '.txt':
            return self._detect_txt_format(file_path)
        else:
            raise FormatDetectionError(f"不支援的文件格式: {file_extension}")
    
    def _detect_json_format(self, json_path: Path) -> str:
        """
        檢測JSON文件格式（LabelMe vs COCO）
        
        Args:
            json_path: JSON文件路徑
            
        Returns:
            str: 'labelme' 或 'coco'
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 檢測LabelMe格式
            if self._is_labelme_format(data):
                return 'labelme'
            
            # 檢測COCO格式
            if self._is_coco_format(data):
                return 'coco'
            
            raise FormatDetectionError(f"無法識別JSON格式: {json_path}")
            
        except json.JSONDecodeError as e:
            raise FormatDetectionError(f"JSON解析錯誤: {e}")
        except Exception as e:
            raise FormatDetectionError(f"檢測JSON格式失敗: {e}")
    
    def _detect_xml_format(self, xml_path: Path) -> str:
        """
        檢測XML文件格式（通常是VOC）
        
        Args:
            xml_path: XML文件路徑
            
        Returns:
            str: 'voc'
        """
        try:
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            # 檢查VOC格式特徵
            if (root.tag == 'annotation' and 
                root.find('filename') is not None and
                root.find('size') is not None):
                return 'voc'
            
            raise FormatDetectionError(f"無法識別XML格式: {xml_path}")
            
        except ET.ParseError as e:
            raise FormatDetectionError(f"XML解析錯誤: {e}")
        except Exception as e:
            raise FormatDetectionError(f"檢測XML格式失敗: {e}")
    
    def _detect_txt_format(self, txt_path: Path) -> str:
        """
        檢測TXT文件格式（通常是YOLO）
        
        Args:
            txt_path: TXT文件路徑
            
        Returns:
            str: 'yolo'
        """
        try:
            # 檢查是否為classes.txt
            if txt_path.name == "classes.txt":
                return 'yolo'
            
            with open(txt_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if not lines:
                raise FormatDetectionError(f"空文件: {txt_path}")
            
            # 檢查YOLO格式特徵
            for line in lines[:5]:  # 檢查前5行
                parts = line.strip().split()
                if len(parts) >= 5:
                    try:
                        # 檢查是否為數字格式
                        class_id = int(parts[0])
                        coords = [float(x) for x in parts[1:5]]
                        
                        # 檢查座標是否在[0,1]範圍內（YOLO格式特徵）
                        if all(0 <= coord <= 1 for coord in coords):
                            return 'yolo'
                    except ValueError:
                        continue
            
            raise FormatDetectionError(f"無法識別TXT格式: {txt_path}")
            
        except Exception as e:
            raise FormatDetectionError(f"檢測TXT格式失敗: {e}")
    
    def _is_labelme_format(self, data: Dict[str, Any]) -> bool:
        """
        檢查JSON是否為LabelMe格式
        
        Args:
            data: JSON數據
            
        Returns:
            bool: 是否為LabelMe格式
        """
        required_fields = ['version', 'flags', 'shapes', 'imagePath', 'imageData']
        
        # 檢查必需欄位
        if not all(field in data for field in required_fields[:3]):
            return False
        
        # 檢查shapes欄位結構
        if 'shapes' in data and isinstance(data['shapes'], list):
            for shape in data['shapes'][:3]:  # 檢查前3個shape
                if not isinstance(shape, dict):
                    return False
                
                shape_required = ['label', 'points', 'shape_type']
                if not all(field in shape for field in shape_required):
                    return False
        
        return True
    
    def _is_coco_format(self, data: Dict[str, Any]) -> bool:
        """
        檢查JSON是否為COCO格式
        
        Args:
            data: JSON數據
            
        Returns:
            bool: 是否為COCO格式
        """
        required_fields = ['images', 'annotations', 'categories']
        
        # 檢查必需欄位
        if not all(field in data for field in required_fields):
            return False
        
        # 檢查欄位類型
        if not all(isinstance(data[field], list) for field in required_fields):
            return False
        
        # 檢查images欄位結構
        if data['images']:
            image = data['images'][0]
            image_required = ['id', 'width', 'height', 'file_name']
            if not all(field in image for field in image_required):
                return False
        
        # 檢查annotations欄位結構
        if data['annotations']:
            annotation = data['annotations'][0]
            annotation_required = ['id', 'image_id', 'category_id']
            if not all(field in annotation for field in annotation_required):
                return False
        
        return True
    
    def get_format_confidence(self, input_path: Path) -> Dict[str, float]:
        """
        獲取各格式的檢測可信度
        
        Args:
            input_path: 輸入路徑
            
        Returns:
            Dict[str, float]: 各格式的可信度分數
        """
        confidence_scores = {fmt: 0.0 for fmt in self.supported_formats}
        
        try:
            detected_format = self.detect_format(input_path)
            confidence_scores[detected_format] = 1.0
            
            # 這裡可以實現更複雜的可信度計算邏輯
            
        except FormatDetectionError:
            pass
        
        return confidence_scores
    
    def validate_format_compatibility(self, input_format: str, target_format: str) -> bool:
        """
        驗證格式轉換兼容性
        
        Args:
            input_format: 輸入格式
            target_format: 目標格式
            
        Returns:
            bool: 是否支援轉換
        """
        # 定義支援的轉換組合
        supported_conversions = {
            'labelme': ['labelme'],  # LabelMe只支援處理自身
            'voc': ['labelme'],
            'yolo': ['labelme'],
            'coco': ['labelme'],
        }
        
        return target_format in supported_conversions.get(input_format, [])