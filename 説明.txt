流程程式檔案（順序）：
1.label_converter:各種label格式轉換
2.checkRawData.py:對初始資料集進行統計分析，讓使用者初步知道資料集的各類別數量等等
3.change_label_names:決定哪些類別需要合并、和刪除
4.RawDataset_preprocessing:對初步處理后的初始資料集進行系統性的資料結構目錄複製檔案，對只有圖像和JSON標簽的資料夾處理成單一類別缺陷和多類別缺陷的資料夾，底下有原始圖像，目標偵測任務和語義分割任務的父檔案，各別父檔案裏有不同的子檔案，
例如目標偵測任務父檔案裏有labelme_format label、overlay_iamge、voc_format label和 yolo_format_label。
例如語義分割任務父檔案裏有labelme_format label、overlay_iamge、mask_format label和 yolo_format_label。
5.dataset_split_output:會對RawDataset_preprocessing後的資料夾進行訓練、驗證、測試的資料集劃分以備進行下一步。