#!/usr/bin/env python3
"""
測試全景圖像擴增工具的像素轉換修復
"""

import numpy as np
import cv2
import json
from panorama_augmenter import PanoramaAugmenter, StandardEquirectRotate, LabelMeTransformer


def create_test_image_and_label():
    """創建測試用的圖像和標籤"""
    # 創建一個簡單的2:1比例測試圖像
    width, height = 360, 180  # 小尺寸便於測試
    
    # 創建彩色測試圖像
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 添加一些可識別的圖案
    cv2.rectangle(image, (50, 50), (100, 100), (255, 0, 0), -1)  # 藍色矩形
    cv2.circle(image, (200, 90), 30, (0, 255, 0), -1)           # 綠色圓形
    cv2.rectangle(image, (280, 120), (330, 160), (0, 0, 255), -1) # 紅色矩形
    
    # 創建對應的LabelMe標籤
    labelme_data = {
        "version": "5.0.1",
        "flags": {},
        "shapes": [
            {
                "label": "rectangle1",
                "points": [
                    [50, 50],
                    [100, 50],
                    [100, 100],
                    [50, 100]
                ],
                "group_id": None,
                "shape_type": "polygon",
                "flags": {}
            },
            {
                "label": "circle",
                "points": [
                    [170, 60],   # 圓的邊界框
                    [230, 120]
                ],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            },
            {
                "label": "rectangle2",
                "points": [
                    [280, 120],
                    [330, 120],
                    [330, 160],
                    [280, 160]
                ],
                "group_id": None,
                "shape_type": "polygon",
                "flags": {}
            }
        ],
        "imagePath": "test_image.jpg",
        "imageData": None,
        "imageHeight": height,
        "imageWidth": width
    }
    
    return image, labelme_data


def test_coordinate_consistency():
    """測試座標轉換的一致性"""
    print("=== 測試座標轉換一致性 ===")
    
    width, height = 360, 180
    transformer = LabelMeTransformer(height, width)
    
    # 測試一些關鍵點
    test_points = [
        (0, 0),           # 左上角
        (width-1, 0),     # 右上角
        (0, height-1),    # 左下角
        (width-1, height-1), # 右下角
        (width//2, height//2), # 中心點
        (100, 50),        # 任意點
    ]
    
    print("原始點 -> 球面座標 -> 恢復點")
    for x, y in test_points:
        # 像素 -> 球面 -> 像素
        sphere_coord = transformer._pixel_to_sphere_single(x, y)
        recovered_x, recovered_y = transformer._sphere_to_pixel_single(sphere_coord)
        
        error_x = abs(x - recovered_x)
        error_y = abs(y - recovered_y)
        
        print(f"({x:3.0f}, {y:3.0f}) -> {sphere_coord} -> ({recovered_x:6.2f}, {recovered_y:6.2f}) "
              f"誤差: ({error_x:.3f}, {error_y:.3f})")
        
        # 檢查誤差是否在可接受範圍內
        assert error_x < 0.1, f"X座標誤差過大: {error_x}"
        assert error_y < 0.1, f"Y座標誤差過大: {error_y}"
    
    print("✅ 座標轉換一致性測試通過")


def test_rotation_consistency():
    """測試圖像和標籤旋轉的一致性"""
    print("\n=== 測試旋轉一致性 ===")
    
    # 創建測試數據
    image, labelme_data = create_test_image_and_label()
    height, width = image.shape[:2]
    
    # 創建旋轉器和標籤轉換器
    rotator = StandardEquirectRotate(height, width)
    transformer = LabelMeTransformer(height, width)
    
    # 測試簡單旋轉：繞Z軸旋轉90度
    test_rotation = (0, 0, 90)  # 90度偏航
    
    # 應用圖像旋轉
    rotated_image = rotator.rotate(image, [[test_rotation, False]])
    
    # 應用標籤旋轉
    rotation_matrix = rotator.get_standard_rotation_matrix(np.array(test_rotation))
    rotated_labelme = transformer.transform_labelme(labelme_data, rotation_matrix)
    
    # 保存結果用於視覺檢查
    cv2.imwrite("test_original.jpg", image)
    cv2.imwrite("test_rotated.jpg", rotated_image)
    
    with open("test_original_label.json", "w", encoding="utf-8") as f:
        json.dump(labelme_data, f, ensure_ascii=False, indent=2)
    
    with open("test_rotated_label.json", "w", encoding="utf-8") as f:
        json.dump(rotated_labelme, f, ensure_ascii=False, indent=2)
    
    print("原始標籤點:")
    for shape in labelme_data['shapes']:
        print(f"  {shape['label']}: {shape['points']}")
    
    print("\n旋轉後標籤點:")
    for shape in rotated_labelme['shapes']:
        print(f"  {shape['label']}: {shape['points']}")
    
    print("✅ 已生成測試文件，請視覺檢查圖像和標籤的一致性")


def test_augmentation_methods():
    """測試各種擴增方法"""
    print("\n=== 測試擴增方法 ===")
    
    # 創建測試數據
    image, labelme_data = create_test_image_and_label()
    
    # 創建擴增器
    augmenter = PanoramaAugmenter()
    
    # 測試不同擴增方法
    test_methods = [
        ('perspective', lambda: augmenter.augment_perspective(image, labelme_data, 2)),
        ('tilt', lambda: augmenter.augment_tilt(image, labelme_data, 15.0, 2)),
        ('multi_angle', lambda: augmenter.augment_multi_angle(image, labelme_data, 90.0)),
    ]
    
    for method_name, method_func in test_methods:
        print(f"\n測試 {method_name} 擴增...")
        try:
            results = method_func()
            print(f"  ✅ {method_name} 擴增成功，生成 {len(results)} 個變化")
            
            # 保存第一個結果用於檢查
            if results:
                aug_image, aug_label = results[0]
                cv2.imwrite(f"test_{method_name}_0.jpg", aug_image)
                with open(f"test_{method_name}_0.json", "w", encoding="utf-8") as f:
                    json.dump(aug_label, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"  ❌ {method_name} 擴增失敗: {e}")


def main():
    """主測試函數"""
    print("開始測試全景圖像擴增工具的修復...")
    
    try:
        # 測試座標轉換一致性
        test_coordinate_consistency()
        
        # 測試旋轉一致性
        test_rotation_consistency()
        
        # 測試擴增方法
        test_augmentation_methods()
        
        print("\n🎉 所有測試完成！")
        print("請檢查生成的圖像和標籤文件，確認圖像變換和標籤變換的一致性。")
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        raise


if __name__ == "__main__":
    main()