"""
統一測試基礎類

提供所有測試的基礎設施，包括：
- test_image資料載入
- 模型測試工具
- 分散式測試支援
"""

import os
import json
import unittest
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import cv2
import numpy as np
from PIL import Image


class BaseTestCase(unittest.TestCase):
    """統一測試基礎類"""
    
    @classmethod
    def setUpClass(cls):
        """設置測試環境"""
        cls.project_root = Path(__file__).parent.parent
        cls.test_data_dir = cls.project_root / "資料前處理" / "test_image"
        cls.model_dir = cls.project_root / "AI模型建構訓練驗證"
        cls.preprocessing_dir = cls.project_root / "資料前處理"
        
        # 載入測試圖像資料
        cls.test_images = cls._load_test_images()
        cls.test_annotations = cls._load_test_annotations()
    
    @classmethod
    def _load_test_images(cls) -> Dict[str, np.ndarray]:
        """載入所有測試圖像"""
        images = {}
        for img_file in cls.test_data_dir.glob("*.jpg"):
            img = cv2.imread(str(img_file))
            if img is not None:
                images[img_file.stem] = img
        return images
    
    @classmethod
    def _load_test_annotations(cls) -> Dict[str, Dict]:
        """載入所有測試標註"""
        annotations = {}
        for json_file in cls.test_data_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    annotations[json_file.stem] = json.load(f)
            except Exception as e:
                print(f"警告：無法載入標註文件 {json_file}: {e}")
        return annotations
    
    def get_sample_image(self, index: int = 0) -> Tuple[np.ndarray, str]:
        """獲取樣本圖像"""
        if not self.test_images:
            self.skipTest("沒有可用的測試圖像")
        
        image_keys = list(self.test_images.keys())
        if index >= len(image_keys):
            index = 0
        
        key = image_keys[index]
        return self.test_images[key], key
    
    def get_sample_annotation(self, image_key: str) -> Optional[Dict]:
        """獲取對應的標註資料"""
        return self.test_annotations.get(image_key)
    
    def get_image_annotation_pair(self, index: int = 0) -> Tuple[np.ndarray, Dict, str]:
        """獲取圖像和標註對"""
        image, key = self.get_sample_image(index)
        annotation = self.get_sample_annotation(key)
        
        if annotation is None:
            self.skipTest(f"圖像 {key} 沒有對應的標註")
        
        return image, annotation, key
    
    def assert_image_valid(self, image: np.ndarray, min_height: int = 100, min_width: int = 100):
        """驗證圖像有效性"""
        self.assertIsInstance(image, np.ndarray)
        self.assertEqual(len(image.shape), 3)  # 彩色圖像
        self.assertGreaterEqual(image.shape[0], min_height)
        self.assertGreaterEqual(image.shape[1], min_width)
        self.assertEqual(image.shape[2], 3)  # RGB/BGR
    
    def assert_annotation_valid(self, annotation: Dict):
        """驗證標註有效性"""
        self.assertIn('shapes', annotation)
        self.assertIsInstance(annotation['shapes'], list)
        
        for shape in annotation['shapes']:
            self.assertIn('label', shape)
            self.assertIn('points', shape)
            self.assertIsInstance(shape['points'], list)


class ModelTestMixin:
    """模型測試混合類"""
    
    def assert_model_output_shape(self, output: Any, expected_shape: Tuple):
        """驗證模型輸出形狀"""
        if hasattr(output, 'shape'):
            self.assertEqual(output.shape, expected_shape)
        elif isinstance(output, (list, tuple)):
            self.assertEqual(len(output), expected_shape[0])
    
    def assert_prediction_valid(self, prediction: Any, num_classes: int):
        """驗證預測結果有效性"""
        if hasattr(prediction, 'shape'):
            # 分類任務
            if len(prediction.shape) == 2:
                self.assertEqual(prediction.shape[1], num_classes)
            # 分割任務
            elif len(prediction.shape) == 4:
                self.assertEqual(prediction.shape[1], num_classes)


class DistributedTestMixin:
    """分散式測試混合類"""
    
    def setUp_distributed(self):
        """設置分散式測試環境"""
        # Ray測試設置
        try:
            import ray
            if not ray.is_initialized():
                ray.init(local_mode=True)
            self.ray_available = True
        except ImportError:
            self.ray_available = False
    
    def tearDown_distributed(self):
        """清理分散式測試環境"""
        if hasattr(self, 'ray_available') and self.ray_available:
            try:
                import ray
                ray.shutdown()
            except:
                pass
    
    def skip_if_no_ray(self):
        """如果沒有Ray則跳過測試"""
        if not getattr(self, 'ray_available', False):
            self.skipTest("Ray未安裝或不可用")


class DataProcessingTestMixin:
    """資料處理測試混合類"""
    
    def assert_conversion_result(self, input_path: str, output_path: str, 
                               input_format: str, output_format: str):
        """驗證格式轉換結果"""
        self.assertTrue(os.path.exists(output_path))
        
        # 驗證輸出格式正確性
        if output_format.lower() == 'yolo':
            self._assert_yolo_format(output_path)
        elif output_format.lower() == 'coco':
            self._assert_coco_format(output_path)
    
    def _assert_yolo_format(self, output_path: str):
        """驗證YOLO格式"""
        txt_files = list(Path(output_path).glob("*.txt"))
        self.assertGreater(len(txt_files), 0)
        
        # 檢查第一個txt文件格式
        if txt_files:
            with open(txt_files[0], 'r') as f:
                lines = f.readlines()
                for line in lines:
                    parts = line.strip().split()
                    self.assertEqual(len(parts), 5)  # class_id, x, y, w, h
                    # 驗證座標範圍
                    for coord in parts[1:]:
                        val = float(coord)
                        self.assertGreaterEqual(val, 0.0)
                        self.assertLessEqual(val, 1.0)
    
    def _assert_coco_format(self, output_path: str):
        """驗證COCO格式"""
        json_files = list(Path(output_path).glob("*.json"))
        self.assertGreater(len(json_files), 0)
        
        # 檢查COCO JSON結構
        if json_files:
            with open(json_files[0], 'r') as f:
                coco_data = json.load(f)
                required_keys = ['images', 'annotations', 'categories']
                for key in required_keys:
                    self.assertIn(key, coco_data)