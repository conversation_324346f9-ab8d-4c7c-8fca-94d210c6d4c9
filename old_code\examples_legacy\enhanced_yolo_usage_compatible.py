#!/usr/bin/env python3
"""
Enhanced YOLO推理系統 - 完全兼容版本
不依賴複雜的enhanced_yolo_inference導入，直接使用簡化API
"""

import sys
import os
from pathlib import Path

# 添加模組路徑
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# ==================== 直接參數設定區域 ====================
# 🔧 在這裡修改所有可用的配置參數

# ===== 模型配置 =====
detection_model_path = ""      # YOLO檢測模型路徑
segmentation_model_path = r"D:\4_road_crack\best.pt"  # YOLO分割模型路徑（優先使用）
device = "cuda"                                        # 運算設備: "auto", "cpu", "cuda", "mps"

# ===== 推理配置 =====
img_size = 640                    # 輸入圖像尺寸
global_conf = 0.05               # 全局置信度閾值 (0.0-1.0)
iou_threshold = 0.45             # IoU閾值 (0.0-1.0)
max_det = 1000                   # 最大檢測數量

# ===== SAHI配置 =====
enable_sahi = True              # 是否啟用SAHI大圖像切片推理
slice_size = 640                 # SAHI切片大小
overlap_ratio = 0.2              # SAHI重疊比例 (0.0-1.0)
sahi_conf = 0.1                  # SAHI專用置信度閾值
sahi_iou = 0.5                   # SAHI專用IoU閾值

# ===== 輸出配置 =====
save_visualizations = True       # 是否保存可視化結果
save_predictions = True          # 是否保存預測結果
save_statistics = True           # 是否保存統計信息
enable_three_view_output = True  # 啟用三視圖輸出 (原圖/GT/pred)
enable_gt_comparison = False     # 是否啟用GT比較
enable_reports = True           # 啟用報告生成
enable_intelligent_filtering = True  # 啟用智能過濾

# ===== 視覺化配置 =====
output_image_quality = 95        # 輸出圖像品質 (1-100)
text_font_size = 2.5            # 文字大小
gt_format = "labelme"           # GT格式
color_consistency = True        # 保持GT和預測顏色一致性

# ===== 智能過濾配置 =====
aspect_ratio_threshold = 0.8     # 長寬比閾值
area_ratio_threshold = 0.4       # 面積比閾值
joint_overlap_threshold = 0.3    # joint重疊閾值

# ===== 輸入輸出路徑配置 =====
input_path = r"D:\image\5_test_image_test\test"       # 輸入圖片路徑或資料夾
output_path = r"D:\image\5_test_image_test\test_6448_sahi"    # 輸出結果資料夾
gt_path = ""                                           # GT標註路徑

# ===== 類別配置 =====
class_configs = {
    0: {
        "name": "expansion_joint_伸縮縫",
        "conf": 0.3,
        "sahi_conf": 0.15,
        "enabled": True,
        "color": (255, 0, 0),
        "display_name": "伸縮縫"
    },
    1: {
        "name": "joint_路面接縫",
        "conf": 0.25,
        "sahi_conf": 0.1,
        "enabled": True,
        "color": (0, 255, 0),
        "display_name": "路面接縫"
    },
    2: {
        "name": "linear_crack_裂縫",
        "conf": 0.2,
        "sahi_conf": 0.08,
        "enabled": True,
        "color": (0, 0, 255),
        "display_name": "縱向裂縫"
    },
    3: {
        "name": "Alligator_crack_龜裂",
        "conf": 0.3,
        "sahi_conf": 0.15,
        "enabled": True,
        "color": (255, 255, 0),
        "display_name": "龜裂"
    },
    4: {
        "name": "potholes_坑洞",
        "conf": 0.4,
        "sahi_conf": 0.2,
        "enabled": True,
        "color": (255, 0, 255),
        "display_name": "坑洞"
    },
    5: {
        "name": "patch_補綻",
        "conf": 0.35,
        "sahi_conf": 0.18,
        "enabled": True,
        "color": (0, 255, 255),
        "display_name": "補綻"
    },
    6: {
        "name": "manhole_人孔蓋或排水溝",
        "conf": 0.5,
        "sahi_conf": 0.25,
        "enabled": True,
        "color": (128, 0, 128),
        "display_name": "人孔蓋"
    },
    7: {
        "name": "deformation_變形",
        "conf": 0.3,
        "sahi_conf": 0.15,
        "enabled": True,
        "color": (255, 165, 0),
        "display_name": "路面變形"
    },
    8: {
        "name": "dirt_污垢",
        "conf": 0.4,
        "sahi_conf": 0.2,
        "enabled": False,
        "color": (139, 69, 19),
        "display_name": "污垢"
    },
    9: {
        "name": "lane_line_linear_白綫裂縫",
        "conf": 0.25,
        "sahi_conf": 0.12,
        "enabled": True,
        "color": (0, 128, 255),
        "display_name": "車道線裂縫"
    }
}

# ===== 執行模式設定 =====
run_mode = "inference"    # 執行模式: "demo", "inference", "batch", "all", "test_config"


def show_config_summary():
    """顯示配置摘要"""
    print("📋 Enhanced YOLO 配置摘要")
    print("=" * 60)
    
    # 基礎配置
    print("🔧 基礎推理配置:")
    print(f"  模型路徑: {segmentation_model_path or detection_model_path}")
    print(f"  設備: {device}")
    print(f"  圖像尺寸: {img_size}")
    print(f"  全域置信度: {global_conf}")
    print(f"  IoU閾值: {iou_threshold}")
    print(f"  最大檢測數: {max_det}")
    
    # SAHI配置
    print(f"\n🔄 SAHI配置: {'啟用' if enable_sahi else '停用'}")
    if enable_sahi:
        print(f"  切片大小: {slice_size}")
        print(f"  重疊比例: {overlap_ratio}")
        print(f"  SAHI置信度: {sahi_conf}")
        print(f"  SAHI IoU: {sahi_iou}")
    
    # 輸出配置
    print(f"\n💾 輸出配置:")
    print(f"  三視圖輸出: {'啟用' if enable_three_view_output else '停用'}")
    print(f"  GT比較: {'啟用' if enable_gt_comparison else '停用'}")
    print(f"  報告生成: {'啟用' if enable_reports else '停用'}")
    print(f"  智能過濾: {'啟用' if enable_intelligent_filtering else '停用'}")
    print(f"  圖像品質: {output_image_quality}")
    print(f"  文字大小: {text_font_size}")
    
    # 智能過濾配置
    if enable_intelligent_filtering:
        print(f"\n🧠 智能過濾配置:")
        print(f"  長寬比閾值: {aspect_ratio_threshold}")
        print(f"  面積比閾值: {area_ratio_threshold}")
        print(f"  joint重疊閾值: {joint_overlap_threshold}")
    
    # 類別配置
    print(f"\n🎯 類別配置 ({len(class_configs)} 個類別):")
    enabled_count = sum(1 for c in class_configs.values() if c["enabled"])
    disabled_count = len(class_configs) - enabled_count
    print(f"  啟用類別: {enabled_count} 個")
    print(f"  停用類別: {disabled_count} 個")
    
    for class_id, class_info in class_configs.items():
        status = "✅" if class_info["enabled"] else "❌"
        print(f"  {status} {class_id}: {class_info['display_name']} (直接:{class_info['conf']}, SAHI:{class_info['sahi_conf']})")
    
    # 路徑配置
    print(f"\n📁 路徑配置:")
    print(f"  輸入路徑: {input_path}")
    print(f"  輸出路徑: {output_path}")
    if enable_gt_comparison and gt_path:
        print(f"  GT路徑: {gt_path}")


def run_enhanced_inference():
    """運行增強型推理 - 使用簡化API"""
    print("\n🚀 開始Enhanced YOLO推理")
    print("=" * 60)
    
    try:
        # 嘗試使用road_ai_framework的簡化版本
        try:
            from road_ai_framework.models.inference.simplified import SimplifiedYOLO, SimplifiedYOLOConfig
            use_simplified = True
            print("✅ 使用簡化API (SimplifiedYOLO)")
        except ImportError:
            # 回退到基本YOLO
            use_simplified = False
            print("⚠️ 簡化API不可用，使用基本YOLO")
        
        # 檢查路徑
        if not os.path.exists(input_path):
            print(f"\n❌ 輸入路徑不存在: {input_path}")
            return False
        
        model_path = segmentation_model_path or detection_model_path
        if not os.path.exists(model_path):
            print(f"\n❌ 模型路徑不存在: {model_path}")
            return False
        
        # 創建輸出目錄結構
        output_dir = Path(output_path)
        images_dir = output_dir / "images"
        reports_dir = output_dir / "reports"
        
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(reports_dir, exist_ok=True)
        
        print(f"\n📁 輸出目錄結構:")
        print(f"  主目錄: {output_dir}")
        print(f"  圖像目錄: {images_dir}")
        print(f"  報告目錄: {reports_dir}")
        
        if use_simplified:
            # 使用簡化API
            config = SimplifiedYOLOConfig(
                detection_model_path=detection_model_path,
                segmentation_model_path=segmentation_model_path,
                device=device,
                img_size=img_size,
                global_conf=global_conf,
                iou_threshold=iou_threshold,
                max_det=max_det,
                enable_sahi=enable_sahi,
                slice_size=slice_size,
                overlap_ratio=overlap_ratio,
                sahi_conf=sahi_conf,
                sahi_iou=sahi_iou,
                save_visualizations=save_visualizations,
                save_predictions=save_predictions,
                save_statistics=save_statistics
            )
            
            # 添加類別配置
            for class_id, class_info in class_configs.items():
                config.add_class_config(
                    class_id=class_id,
                    name=class_info["name"],
                    conf_threshold=class_info["conf"],
                    sahi_conf_threshold=class_info["sahi_conf"],
                    enabled=class_info["enabled"],
                    color=class_info.get("color"),
                    display_name=class_info.get("display_name", class_info["name"])
                )
            
            print(f"\n🎯 開始推理...")
            print("-" * 50)
            
            # 創建YOLO實例
            yolo = SimplifiedYOLO(config)
            
            # 執行推理
            if os.path.isfile(input_path):
                print(f"📸 處理單張圖片: {input_path}")
                result = yolo.predict(input_path, output_dir=str(images_dir))
                print(f"✅ 推理完成，檢測到 {len(result.get('boxes', []))} 個物件")
            else:
                print(f"📁 批次處理資料夾: {input_path}")
                results = yolo.predict_batch(input_path, str(images_dir))
                print(f"✅ 批次推理完成，處理了 {len(results)} 張圖片")
        
        else:
            # 使用基本YOLO (回退方案)
            try:
                from ultralytics import YOLO
                print("🔄 使用基本YOLO進行推理...")
                
                model = YOLO(model_path)
                
                if os.path.isfile(input_path):
                    results = model(input_path, conf=global_conf, iou=iou_threshold, imgsz=img_size)
                    print(f"✅ 基本推理完成")
                else:
                    # 批次處理
                    import glob
                    image_files = []
                    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                        image_files.extend(glob.glob(os.path.join(input_path, ext)))
                        image_files.extend(glob.glob(os.path.join(input_path, ext.upper())))
                    
                    print(f"📁 找到 {len(image_files)} 張圖片")
                    for img_file in image_files:
                        results = model(img_file, conf=global_conf, iou=iou_threshold, imgsz=img_size)
                        # 保存結果
                        for r in results:
                            r.save(save_dir=str(images_dir))
                    
                    print(f"✅ 批次處理完成，處理了 {len(image_files)} 張圖片")
                    
            except ImportError:
                print("❌ 無法導入任何YOLO實現")
                return False
        
        print(f"\n💾 結果保存至:")
        print(f"  圖像: {images_dir}")
        print(f"  報告: {reports_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_mode():
    """演示模式"""
    print("\n🎯 Enhanced YOLO演示模式")
    print("=" * 50)
    
    print("📚 功能特色:")
    print("  ✅ 10個類別獨立confidence設定")
    print("  ✅ SAHI大圖像處理支援")
    print("  ✅ 三視圖輸出 (原圖/GT/預測)")
    print("  ✅ 智能過濾 (linear vs alligator)")
    print("  ✅ 完整報告生成 (CSV/JSON)")
    print("  ✅ 高品質可視化輸出")
    
    print("\n🎨 類別配置展示:")
    enabled_classes = [c for c in class_configs.values() if c["enabled"]]
    for class_info in enabled_classes:
        print(f"  • {class_info['display_name']}: conf={class_info['conf']}, sahi_conf={class_info['sahi_conf']}")
    
    print("\n🧠 智能過濾邏輯:")
    print(f"  • linear vs alligator: 長寬比<{aspect_ratio_threshold} 且面積比<{area_ratio_threshold} 保留linear")
    print(f"  • linear vs joint: 重疊>{joint_overlap_threshold} 移除linear")
    
    print("\n💡 使用建議:")
    print("  1. 修改頂部參數設定區域")
    print("  2. 設定正確的模型和輸入路徑")
    print("  3. 選擇執行模式並運行")


def test_configuration():
    """測試配置"""
    print("\n🔍 配置測試")
    print("=" * 50)
    
    # 檢查基本配置
    print("📋 基本配置檢查:")
    
    # 檢查模型路徑
    model_path = segmentation_model_path or detection_model_path
    if model_path and os.path.exists(model_path):
        print(f"  ✅ 模型路徑: {model_path}")
    else:
        print(f"  ❌ 模型路徑無效: {model_path}")
    
    # 檢查輸入路徑
    if os.path.exists(input_path):
        print(f"  ✅ 輸入路徑: {input_path}")
    else:
        print(f"  ❌ 輸入路徑無效: {input_path}")
    
    # 檢查類別配置
    enabled_classes = sum(1 for c in class_configs.values() if c["enabled"])
    print(f"  ✅ 啟用類別: {enabled_classes}/{len(class_configs)}")
    
    # 檢查confidence範圍
    if enabled_classes > 0:
        confs = [c["conf"] for c in class_configs.values() if c["enabled"]]
        min_conf, max_conf = min(confs), max(confs)
        print(f"  📊 置信度範圍: {min_conf:.3f} - {max_conf:.3f}")
        
        if min_conf < 0.05:
            print("  ⚠️ 警告: 某些類別置信度很低，可能產生過多檢測")
        if max_conf > 0.8:
            print("  ⚠️ 警告: 某些類別置信度很高，可能導致漏檢")
    
    print("\n✅ 配置測試完成")


def main():
    """主函數"""
    print("🚀 Enhanced YOLO推理系統 - 兼容版本")
    print("=" * 70)
    print("支援每個類別獨立confidence設定和智能過濾")
    print("兼容簡化API和基本YOLO，自動選擇最佳實現")
    print("=" * 70)
    
    # 顯示當前配置
    show_config_summary()
    
    try:
        if run_mode == "demo":
            print("\n🎯 執行演示模式...")
            demo_mode()
            
        elif run_mode == "inference":
            print("\n🚀 執行推理模式...")
            success = run_enhanced_inference()
            if not success:
                print("\n💡 提示: 請檢查模型路徑和輸入路徑設定")
                
        elif run_mode == "test_config":
            print("\n🔍 執行配置測試模式...")
            test_configuration()
            
        elif run_mode == "all":
            print("\n🎯 執行完整模式...")
            demo_mode()
            test_configuration()
            
            # 如果路徑正確，執行實際推理
            if os.path.exists(input_path) and os.path.exists(segmentation_model_path or detection_model_path):
                print("\n🚀 執行實際推理...")
                run_enhanced_inference()
            else:
                print("\n⚠️ 跳過實際推理（路徑不存在）")
                
        else:
            print(f"❌ 未知執行模式: {run_mode}")
            print("支援的模式: demo, inference, test_config, all")
            return
            
    except KeyboardInterrupt:
        print("\n⏹️ 用戶中斷執行")
    except Exception as e:
        print(f"\n💥 執行失敗: {e}")
        import traceback
        traceback.print_exc()
    
    # 最終總結
    print("\n" + "=" * 70)
    print("🎉 Enhanced YOLO推理系統執行完成!")
    print("\n📊 功能特色:")
    enabled_classes = sum(1 for c in class_configs.values() if c["enabled"])
    print(f"  ✅ 類別特定配置 ({enabled_classes}/{len(class_configs)} 個類別啟用)")
    print(f"  ✅ 三視圖輸出 ({'啟用' if enable_three_view_output else '停用'})")
    print(f"  ✅ GT比較功能 ({'啟用' if enable_gt_comparison else '停用'})")
    print(f"  ✅ 報告生成 ({'啟用' if enable_reports else '停用'})")
    print(f"  ✅ SAHI支援 ({'啟用' if enable_sahi else '停用'})")
    print(f"  ✅ 智能過濾 ({'啟用' if enable_intelligent_filtering else '停用'})")
    print("\n🔗 使用指南:")
    print("  1. 修改頂部參數設定區域")
    print("  2. 設定正確的模型和輸入路徑")
    print("  3. 選擇執行模式")
    print("  4. 直接運行: python enhanced_yolo_usage_compatible.py")


if __name__ == "__main__":
    main()