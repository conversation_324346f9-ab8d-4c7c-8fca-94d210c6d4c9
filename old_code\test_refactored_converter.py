#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重構後的標註轉換器測試腳本

驗證重構後的代碼是否正常工作
"""

import sys
import os
import logging
from pathlib import Path

# 添加當前目錄到Python路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """測試所有模組是否能正常導入"""
    try:
        print("=== 測試模組導入 ===")
        
        # 測試共享模組
        from shared.base_tool import BaseTool
        from shared.exceptions import ConversionError, FormatDetectionError
        from shared.config_manager import ConfigManager
        from shared.file_utils import FileUtils
        from shared.logger_utils import StructuredLogger
        print("✅ shared 模組導入成功")
        
        # 測試格式檢測器（不需要PIL）
        from tools.format_detector import FormatDetector
        print("✅ FormatDetector 導入成功")
        
        # 檢查PIL是否可用
        try:
            import PIL
            has_pil = True
            print("✅ PIL 可用")
        except ImportError:
            has_pil = False
            print("⚠️ PIL 不可用，跳過需要PIL的導入")
        
        if has_pil:
            # 測試需要PIL的工具模組
            from tools.conversion_strategy import (
                ConversionStrategy, LabelMeToYOLOConverter,
                VOCToLabelMeConverter, YOLOToLabelMeConverter,
                COCOToLabelMeConverter
            )
            from tools.image_processor import ImageProcessor
            from tools.annotation_converter_v2 import AnnotationConverterV2
            print("✅ tools 模組導入成功")
            
            # 測試向後兼容模組
            from annotation_converter_compat import AnnotationConverter
            print("✅ 向後兼容模組導入成功")
        else:
            print("✅ 跳過需要PIL的模組導入")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_format_detector():
    """測試格式檢測器"""
    try:
        print("\n=== 測試格式檢測器 ===")
        
        from tools.format_detector import FormatDetector
        
        detector = FormatDetector()
        
        # 測試支援的格式
        print(f"支援的格式: {detector.supported_formats}")
        
        # 測試格式兼容性檢查
        is_compatible = detector.validate_format_compatibility('labelme', 'yolo')
        print(f"LabelMe -> YOLO 兼容性: {is_compatible}")
        
        print("✅ 格式檢測器測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 格式檢測器測試失敗: {e}")
        return False

def test_conversion_strategies():
    """測試轉換策略"""
    try:
        print("\n=== 測試轉換策略 ===")
        
        # 先檢查是否有PIL
        try:
            import PIL
            has_pil = True
        except ImportError:
            has_pil = False
            print("⚠️ PIL未安裝，跳過需要PIL的測試")
        
        if not has_pil:
            print("✅ 轉換策略測試跳過（需要PIL）")
            return True
        
        from tools.conversion_strategy import (
            LabelMeToYOLOConverter, VOCToLabelMeConverter,
            YOLOToLabelMeConverter, COCOToLabelMeConverter
        )
        
        # 測試創建轉換器實例
        labelme_to_yolo = LabelMeToYOLOConverter()
        voc_to_labelme = VOCToLabelMeConverter()
        yolo_to_labelme = YOLOToLabelMeConverter()
        coco_to_labelme = COCOToLabelMeConverter()
        
        print("✅ 所有轉換策略創建成功")
        
        # 測試統計功能
        stats = labelme_to_yolo.get_stats()
        print(f"初始統計: {stats}")
        
        print("✅ 轉換策略測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 轉換策略測試失敗: {e}")
        return False

def test_image_processor():
    """測試圖像處理器"""
    try:
        print("\n=== 測試圖像處理器 ===")
        
        # 先檢查是否有PIL
        try:
            import PIL
            has_pil = True
        except ImportError:
            has_pil = False
            print("⚠️ PIL未安裝，跳過需要PIL的測試")
        
        if not has_pil:
            print("✅ 圖像處理器測試跳過（需要PIL）")
            return True
        
        from tools.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        
        # 測試支援的格式
        print(f"支援的圖像格式: {processor.supported_formats}")
        
        # 測試統計功能
        stats = processor.get_stats()
        print(f"初始統計: {stats}")
        
        print("✅ 圖像處理器測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 圖像處理器測試失敗: {e}")
        return False

def test_annotation_converter_v2():
    """測試重構後的主轉換器"""
    try:
        print("\n=== 測試重構後的主轉換器 ===")
        
        # 先檢查是否有PIL
        try:
            import PIL
            has_pil = True
        except ImportError:
            has_pil = False
            print("⚠️ PIL未安裝，跳過需要PIL的測試")
        
        if not has_pil:
            print("✅ 重構後的主轉換器測試跳過（需要PIL）")
            return True
        
        from tools.annotation_converter_v2 import AnnotationConverterV2
        
        # 測試創建實例
        converter = AnnotationConverterV2(
            input_dir="/tmp/test_input",
            output_dir="/tmp/test_output",
            input_format="auto",
            output_format="labelme"
        )
        
        print(f"支援的輸入格式: {converter.supported_input_formats}")
        print(f"支援的輸出格式: {converter.supported_output_formats}")
        
        # 測試配置
        print(f"配置: {converter.config}")
        
        print("✅ 重構後的主轉換器測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 重構後的主轉換器測試失敗: {e}")
        return False

def test_backward_compatibility():
    """測試向後兼容性"""
    try:
        print("\n=== 測試向後兼容性 ===")
        
        # 先檢查是否有PIL
        try:
            import PIL
            has_pil = True
        except ImportError:
            has_pil = False
            print("⚠️ PIL未安裝，跳過需要PIL的測試")
        
        if not has_pil:
            print("✅ 向後兼容性測試跳過（需要PIL）")
            return True
        
        from annotation_converter_compat import AnnotationConverter
        
        # 測試創建實例（使用原始接口）
        converter = AnnotationConverter()
        
        # 測試原始屬性
        print(f"支援的格式: {converter.supported_formats}")
        print(f"檢查點文件: {converter.checkpoint_file}")
        print(f"初始統計: {converter.stats}")
        
        # 測試獲取新功能
        format_detector = converter.get_format_detector()
        image_processor = converter.get_image_processor()
        
        print("✅ 向後兼容性測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 向後兼容性測試失敗: {e}")
        return False

def test_base_tool_inheritance():
    """測試BaseTool繼承"""
    try:
        print("\n=== 測試BaseTool繼承 ===")
        
        # 先檢查是否有PIL
        try:
            import PIL
            has_pil = True
        except ImportError:
            has_pil = False
            print("⚠️ PIL未安裝，跳過需要PIL的測試")
        
        if not has_pil:
            print("✅ BaseTool繼承測試跳過（需要PIL）")
            return True
        
        from tools.annotation_converter_v2 import AnnotationConverterV2
        from shared.base_tool import BaseTool
        
        converter = AnnotationConverterV2()
        
        # 檢查是否正確繼承
        assert isinstance(converter, BaseTool), "應該繼承BaseTool"
        
        # 測試BaseTool方法
        progress = converter.get_progress()
        summary = converter.get_summary()
        
        print(f"初始進度: {progress}")
        print(f"執行摘要: {summary}")
        
        print("✅ BaseTool繼承測試通過")
        return True
        
    except Exception as e:
        print(f"❌ BaseTool繼承測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("開始測試重構後的標註轉換器...")
    
    test_results = []
    
    # 執行所有測試
    test_functions = [
        test_imports,
        test_format_detector,
        test_conversion_strategies,
        test_image_processor,
        test_annotation_converter_v2,
        test_backward_compatibility,
        test_base_tool_inheritance
    ]
    
    for test_func in test_functions:
        try:
            result = test_func()
            test_results.append(result)
        except Exception as e:
            print(f"❌ 測試異常: {test_func.__name__}: {e}")
            test_results.append(False)
    
    # 統計結果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 測試結果總結 ===")
    print(f"通過: {passed}/{total}")
    print(f"失敗: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有測試通過！重構成功！")
        return True
    else:
        print("⚠️  部分測試失敗，需要修復")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)