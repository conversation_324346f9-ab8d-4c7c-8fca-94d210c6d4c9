import os
import cv2
import torch
import numpy as np
from lanedet.utils.config import Config
from lanedet.models.registry import build_net

import matplotlib.pyplot as plt

class LaneDetect(object):
    def __init__(self, cfg):

        from lanedet.datasets.process import Process
        self.cfg = cfg
        self.processes = Process(cfg.val_process, cfg)
        self.net = build_net(self.cfg)
        self.net = torch.nn.parallel.DataParallel(
                self.net, device_ids = range(1)).cuda()
        self.net.eval()
        # load model
        pretrained_model = torch.load(self.cfg.load_from)
        self.net.load_state_dict(pretrained_model['net'], strict=True)

    def preprocess(self, img):
        self.ori_img_size = (img.shape[1],img.shape[0]) # (x,y)
        ori_img = cv2.resize(img,(1640,590)) # condlane 101
        # ori_img = cv2.resize(ori_img,(1280,720)) # resa tusimple 34
        img = ori_img[self.cfg.cut_height:,:, :].astype(np.float32)
        # plt.imshow(img)
        # plt.show()
        # cv2.waitKey()
        data = {'img': img, 'lanes': []}   
        data = self.processes(data)
        data['img'] = data['img'].unsqueeze(0)
        data.update({'ori_img':ori_img})

        return data

    def inference(self, data):
        with torch.no_grad():
            data = self.net(data)
            data = self.net.module.get_lanes(data)
        return data

    def show(self, data):
        lanes = [lane.to_array(self.cfg) for lane in data['lanes']]
        img = data['ori_img']

        lane_dict = dict()
        lane_dict["all_lane"] = np.zeros((img.shape[0], img.shape[1]))
        lane_dict["sig_lane"] = dict()
        for i, lane in enumerate(lanes):
            cp = np.zeros((img.shape[0], img.shape[1]))
            lane_dict["sig_lane"][i] = cv2.polylines(cp, np.int_([lane]), isClosed=False, color=255, thickness=10)
            lane_dict["sig_lane"][i] = cv2.resize(lane_dict["sig_lane"][i],self.ori_img_size)
            
            cv2.polylines(lane_dict["all_lane"], np.int_([lane]), False, 255, 15)
            # img 可不畫
            # line_mask = cv2.polylines(np.zeros((img.shape),dtype=np.uint8), np.int_([lane]), False, (255,0,0), 15)
            # img = cv2.addWeighted(img,1,line_mask,0.5,0)

        #     plt.subplot(2,2,i+1),plt.imshow(lane_dict["sig_lane"][i],"gray"),plt.title(str(i))
        # plt.show()
        lane_dict["all_lane"] = cv2.resize(lane_dict["all_lane"],self.ori_img_size)
        # plt.imshow(lane_dict["all_lane"],"gray")

        # img = cv2.resize(img,self.ori_img_size)
        # plt.imshow(img)
        # plt.show()        
        return lane_dict

    def run(self, img):
        data = self.preprocess(img)
        data['lanes'] = self.inference(data)[0]
        if data["lanes"]:
            lane_dict = self.show(data)
            return lane_dict
        # 若報錯這邊要加東西
        

if __name__ == '__main__':
    def process():
        cfgfile = "configs/condlane/resnet101_culane.py"
        cfg = Config.fromfile(cfgfile)
        cfg.show = False
        cfg.load_from = './parameters/condlane_r101_culane.pth'
        detect = LaneDetect(cfg)
        img = cv2.imread("./KED-0881_20211223_100902177.jpg")
        sulhouette = dict()
        sulhouette = detect.run(img)
        # 有找到標線才做刪除重疊
        # if sulhouette["all_lane"]:
        #     return merge
        RecognitionArea = [1600,2850,100,3660]
        # 迴圈取範圍
        if sulhouette:
            sulhouette["all_lane"] = sulhouette["all_lane"][RecognitionArea[0]:RecognitionArea[1],RecognitionArea[2]:RecognitionArea[3]]
            for i in sulhouette["sig_lane"]:
                sulhouette["sig_lane"][i] = sulhouette["sig_lane"][i][RecognitionArea[0]:RecognitionArea[1],RecognitionArea[2]:RecognitionArea[3]]

            #     plt.subplot(2,2,i+1),plt.imshow(sulhouette["sig_lane"][i],"gray"),plt.title(str(i))
            # plt.show()
        
        print("--done--")
        # 改成判斷所有
    process()

