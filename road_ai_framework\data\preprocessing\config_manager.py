"""
資料前處理配置管理器

基於AI模型建構訓練驗證的ConfigManager進行擴展
專門針對資料前處理工具的配置需求進行優化
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from dataclasses import dataclass, asdict

from .exceptions import ConfigError


@dataclass
class BaseConfig:
    """基礎配置類"""
    max_workers: Optional[int] = None
    log_level: str = "INFO"
    backup_original: bool = True
    output_quality: int = 75
    
    def __post_init__(self):
        if self.max_workers is None:
            self.max_workers = min(32, (os.cpu_count() or 1) * 2)
        self.validate()
    
    def validate(self):
        """驗證配置參數"""
        if self.max_workers <= 0:
            raise ConfigError("max_workers must be positive")
        
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            raise ConfigError(f"log_level must be one of {valid_log_levels}")
            
        if not 1 <= self.output_quality <= 100:
            raise ConfigError("output_quality must be between 1 and 100")


@dataclass 
class ConverterConfig(BaseConfig):
    """轉換器配置"""
    input_format: str = "auto"
    output_format: str = "yolo"
    resize_factor: float = 1.0
    class_mapping: Optional[Dict[str, int]] = None
    
    def validate(self):
        super().validate()
        if not 0.1 <= self.resize_factor <= 2.0:
            raise ConfigError("resize_factor must be between 0.1 and 2.0")
        
        valid_formats = ["auto", "labelme", "yolo", "voc", "coco"]
        if self.input_format not in valid_formats:
            raise ConfigError(f"input_format must be one of {valid_formats}")
        if self.output_format not in valid_formats:
            raise ConfigError(f"output_format must be one of {valid_formats}")


@dataclass
class AugmenterConfig(BaseConfig):
    """增強器配置"""
    num_generations: int = 100
    grid_size: int = 8
    iou_threshold: float = 0.1
    enable_grid_placement: bool = True
    avoid_similar_positions: bool = True
    avoid_overlap: bool = True
    margins: tuple = (50, 50, 50, 50)  # (top, right, bottom, left)
    
    def validate(self):
        super().validate()
        if not 1 <= self.num_generations <= 10000:
            raise ConfigError("num_generations must be between 1 and 10000")
        if not 4 <= self.grid_size <= 32:
            raise ConfigError("grid_size must be between 4 and 32")
        if not 0.0 <= self.iou_threshold <= 1.0:
            raise ConfigError("iou_threshold must be between 0.0 and 1.0")


@dataclass
class DividerConfig(BaseConfig):
    """數據集分割器配置"""
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15
    batch_size: int = 50
    class_limits: Optional[Dict[str, int]] = None
    
    def validate(self):
        super().validate()
        if not abs(self.train_ratio + self.val_ratio + self.test_ratio - 1.0) < 1e-6:
            raise ConfigError("train_ratio + val_ratio + test_ratio must equal 1.0")
        if not 1 <= self.batch_size <= 1000:
            raise ConfigError("batch_size must be between 1 and 1000")


class ConfigManager:
    """資料前處理配置管理器"""
    
    def __init__(self, config_root: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_root: 配置文件根目錄
        """
        if config_root is None:
            # 默認配置目錄在資料前處理目錄下
            current_dir = Path(__file__).parent.parent
            config_root = current_dir / "configs"
        
        self.config_root = Path(config_root)
        self.config_root.mkdir(exist_ok=True, parents=True)
        
        # 創建資料前處理專用的子目錄
        (self.config_root / "tools").mkdir(exist_ok=True)
        (self.config_root / "converters").mkdir(exist_ok=True)
        (self.config_root / "augmenters").mkdir(exist_ok=True)
        (self.config_root / "dividers").mkdir(exist_ok=True)
        (self.config_root / "gui").mkdir(exist_ok=True)
    
    def load_config(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """
        加載配置文件
        
        Args:
            config_path: 配置文件路徑
            
        Returns:
            配置字典
        """
        config_path = Path(config_path)
        
        # 如果是相對路徑，則相對於配置根目錄
        if not config_path.is_absolute():
            config_path = self.config_root / config_path
        
        if not config_path.exists():
            raise ConfigError(f"配置文件未找到: {config_path}")
        
        try:
            # 根據文件擴展名選擇加載器
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                return self._load_yaml(config_path)
            elif config_path.suffix.lower() == '.json':
                return self._load_json(config_path)
            else:
                raise ConfigError(f"不支持的配置文件格式: {config_path.suffix}")
        except Exception as e:
            raise ConfigError(f"載入配置文件失敗: {e}", file_path=str(config_path))
    
    def save_config(self, config: Union[Dict[str, Any], BaseConfig], 
                   config_path: Union[str, Path], 
                   format: str = 'yaml'):
        """
        保存配置文件
        
        Args:
            config: 配置字典或配置對象
            config_path: 保存路徑
            format: 保存格式 ('yaml' 或 'json')
        """
        config_path = Path(config_path)
        
        # 如果是相對路徑，則相對於配置根目錄
        if not config_path.is_absolute():
            config_path = self.config_root / config_path
        
        # 確保目錄存在
        config_path.parent.mkdir(exist_ok=True, parents=True)
        
        # 轉換配置對象為字典
        if isinstance(config, BaseConfig):
            config = asdict(config)
        
        try:
            # 根據格式保存
            if format.lower() == 'yaml':
                self._save_yaml(config, config_path)
            elif format.lower() == 'json':
                self._save_json(config, config_path)
            else:
                raise ConfigError(f"不支持的保存格式: {format}")
        except Exception as e:
            raise ConfigError(f"保存配置文件失敗: {e}", file_path=str(config_path))
    
    def _load_yaml(self, config_path: Path) -> Dict[str, Any]:
        """加載YAML配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except yaml.YAMLError as e:
            raise ConfigError(f"YAML文件解析錯誤: {e}")
    
    def _load_json(self, config_path: Path) -> Dict[str, Any]:
        """加載JSON配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ConfigError(f"JSON文件解析錯誤: {e}")
    
    def _save_yaml(self, config: Dict[str, Any], config_path: Path):
        """保存YAML配置文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def _save_json(self, config: Dict[str, Any], config_path: Path):
        """保存JSON配置文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_tool_config(self, tool_name: str) -> Dict[str, Any]:
        """
        獲取工具配置
        
        Args:
            tool_name: 工具名稱
            
        Returns:
            工具配置字典
        """
        config_path = self.config_root / "tools" / f"{tool_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "tools" / f"{tool_name}.json"
        
        if config_path.exists():
            return self.load_config(config_path)
        return {}
    
    def get_converter_config(self, converter_name: str) -> ConverterConfig:
        """獲取轉換器配置"""
        config_path = self.config_root / "converters" / f"{converter_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "converters" / f"{converter_name}.json"
        
        if config_path.exists():
            config_dict = self.load_config(config_path)
            return ConverterConfig(**config_dict)
        return ConverterConfig()
    
    def get_augmenter_config(self, augmenter_name: str) -> AugmenterConfig:
        """獲取增強器配置"""
        config_path = self.config_root / "augmenters" / f"{augmenter_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "augmenters" / f"{augmenter_name}.json"
        
        if config_path.exists():
            config_dict = self.load_config(config_path)
            return AugmenterConfig(**config_dict)
        return AugmenterConfig()
    
    def get_divider_config(self, divider_name: str) -> DividerConfig:
        """獲取分割器配置"""
        config_path = self.config_root / "dividers" / f"{divider_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "dividers" / f"{divider_name}.json"
        
        if config_path.exists():
            config_dict = self.load_config(config_path)
            return DividerConfig(**config_dict)
        return DividerConfig()
    
    def get_gui_config(self) -> Dict[str, Any]:
        """獲取GUI配置"""
        config_path = self.config_root / "gui" / "gui_config.json"
        if config_path.exists():
            return self.load_config(config_path)
        
        # 返回默認GUI配置
        return {
            "window_size": "1200x800",
            "theme": "default",
            "last_input_dir": "",
            "last_output_dir": "",
            "log_level": "INFO",
            "auto_save_config": True
        }
    
    def save_gui_config(self, config: Dict[str, Any]):
        """保存GUI配置"""
        config_path = self.config_root / "gui" / "gui_config.json"
        self.save_config(config, config_path, format='json')
    
    def list_configs(self, config_type: str = None) -> Dict[str, List[str]]:
        """
        列出所有可用配置
        
        Args:
            config_type: 配置類型 ('tools', 'converters', 'augmenters', 'dividers', 'gui')
            
        Returns:
            配置列表字典
        """
        config_types = ['tools', 'converters', 'augmenters', 'dividers', 'gui']
        if config_type and config_type not in config_types:
            raise ConfigError(f"無效的配置類型: {config_type}")
        
        result = {}
        target_types = [config_type] if config_type else config_types
        
        for ct in target_types:
            config_dir = self.config_root / ct
            if config_dir.exists():
                configs = []
                for config_file in config_dir.glob("*.yaml"):
                    configs.append(config_file.stem)
                for config_file in config_dir.glob("*.json"):
                    if config_file.stem not in configs:  # 避免重複
                        configs.append(config_file.stem)
                result[ct] = sorted(configs)
            else:
                result[ct] = []
        
        return result
    
    def create_default_configs(self):
        """創建默認配置文件"""
        # 創建默認轉換器配置
        converter_config = ConverterConfig()
        self.save_config(converter_config, "converters/default.yaml")
        
        # 創建默認增強器配置
        augmenter_config = AugmenterConfig()
        self.save_config(augmenter_config, "augmenters/default.yaml")
        
        # 創建默認分割器配置
        divider_config = DividerConfig()
        self.save_config(divider_config, "dividers/default.yaml")
        
        # 創建默認GUI配置
        gui_config = self.get_gui_config()
        self.save_gui_config(gui_config)
    
    def merge_configs(self, *configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        合併多個配置
        
        Args:
            *configs: 多個配置字典
            
        Returns:
            合併後的配置字典
        """
        merged = {}
        for config in configs:
            if config:
                merged.update(config)
        return merged
    
    def get(self, key: str, default=None, config_type: str = "tools") -> Any:
        """
        獲取配置值
        
        Args:
            key: 配置鍵，支援點記法 (e.g., "converter.resize_factor")
            default: 默認值
            config_type: 配置類型
            
        Returns:
            配置值
        """
        try:
            if "." in key:
                config_name, config_key = key.split(".", 1)
                config = self.get_tool_config(config_name)
                keys = config_key.split('.')
                value = config
                for k in keys:
                    value = value.get(k) if isinstance(value, dict) else None
                    if value is None:
                        return default
                return value
            else:
                # 直接從指定類型的默認配置獲取
                config = self.get_tool_config("default")
                return config.get(key, default)
        except Exception:
            return default