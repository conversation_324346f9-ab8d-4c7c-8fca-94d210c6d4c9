"""
混淆矩陣評估器

基於 0_yolo.py 評估代碼重構，提供完整的目標檢測和分割模型評估功能。
包含混淆矩陣、IoU、精確率、召回率、F1分數等指標計算。
"""

import os
import json
import csv
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from collections import defaultdict
from abc import ABC, abstractmethod
import logging

# 設定日誌
logger = logging.getLogger(__name__)


class BaseEvaluator(ABC):
    """評估器基類"""
    
    def __init__(self, class_names: Dict[int, str], verbose: bool = False):
        self.class_names = class_names
        self.verbose = verbose
        self.num_classes = len(class_names)
        
    @abstractmethod
    def evaluate(self, predictions: Any, groundtruths: Any) -> Dict[str, Any]:
        """執行評估"""
        pass
    
    def _log_info(self, message: str) -> None:
        """日誌記錄"""
        if self.verbose:
            logger.info(message)
            print(message)


class DetectionMetrics:
    """目標檢測指標計算器"""
    
    def __init__(self, num_classes: int, iou_threshold: float = 0.5):
        self.num_classes = num_classes
        self.iou_threshold = iou_threshold
        self.reset()
    
    def reset(self):
        """重置指標"""
        self.tp = defaultdict(int)  # True Positive
        self.fp = defaultdict(int)  # False Positive  
        self.fn = defaultdict(int)  # False Negative
        self.tn = defaultdict(int)  # True Negative
        self.iou_values = defaultdict(list)
        
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算兩個框的 IoU"""
        if len(box1) != 4 or len(box2) != 4:
            return 0.0
            
        # 計算相交區域
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        # 檢查是否相交
        if x2 < x1 or y2 < y1:
            return 0.0
            
        intersection = (x2 - x1) * (y2 - y1)
        
        # 計算各自面積
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        
        # 計算 IoU
        union = area1 + area2 - intersection
        return intersection / union if union > 0 else 0.0
    
    def update(self, pred_boxes: List[Dict], gt_boxes: List[Dict]):
        """更新指標"""
        # 標記已匹配的框
        matched_gt = [False] * len(gt_boxes)
        matched_pred = [False] * len(pred_boxes)
        
        # 為每個預測框尋找最佳匹配的真實框
        for i, pred in enumerate(pred_boxes):
            pred_class = pred['class_id']
            pred_bbox = pred['bbox']
            
            best_iou = -1
            best_gt_idx = -1
            
            for j, gt in enumerate(gt_boxes):
                gt_class = gt['class_id']
                gt_bbox = gt['bbox']
                
                # 只考慮同類別的框
                if gt_class == pred_class:
                    iou = self.calculate_iou(pred_bbox, gt_bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = j
            
            # 如果 IoU 超過閾值且該真實框未被匹配
            if best_iou >= self.iou_threshold and not matched_gt[best_gt_idx]:
                matched_gt[best_gt_idx] = True
                matched_pred[i] = True
                
                # 記錄 TP
                self.tp[pred_class] += 1
                self.iou_values[pred_class].append(best_iou)
        
        # 計算 FP（未匹配的預測框）
        for i, matched in enumerate(matched_pred):
            if not matched:
                pred_class = pred_boxes[i]['class_id']
                self.fp[pred_class] += 1
        
        # 計算 FN（未匹配的真實框）
        for i, matched in enumerate(matched_gt):
            if not matched:
                gt_class = gt_boxes[i]['class_id']
                self.fn[gt_class] += 1
    
    def compute_metrics(self) -> Dict[str, Any]:
        """計算所有指標"""
        metrics = {
            'overall': {},
            'per_class': {},
            'summary': {}
        }
        
        # 計算每個類別的指標
        total_tp, total_fp, total_fn = 0, 0, 0
        class_metrics = {}
        
        for class_id in range(self.num_classes):
            tp = self.tp[class_id]
            fp = self.fp[class_id]
            fn = self.fn[class_id]
            
            # 計算 TN（簡化處理）
            tn = sum(self.tp[other_class] + self.fn[other_class] 
                    for other_class in range(self.num_classes) 
                    if other_class != class_id)
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0.0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
            accuracy = (tp + tn) / (tp + fp + fn + tn) if (tp + fp + fn + tn) > 0 else 0.0
            
            iou = tp / (tp + fp + fn) if (tp + fp + fn) > 0 else 0.0
            mean_iou = np.mean(self.iou_values[class_id]) if self.iou_values[class_id] else 0.0
            
            class_metrics[class_id] = {
                'TP': tp, 'FP': fp, 'FN': fn, 'TN': tn,
                'precision': precision, 'recall': recall,
                'specificity': specificity, 'f1': f1,
                'accuracy': accuracy, 'IoU': iou,
                'mean_IoU': mean_iou
            }
            
            total_tp += tp
            total_fp += fp
            total_fn += fn
        
        # 計算整體指標
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
        
        # 計算 mIoU
        valid_ious = [class_metrics[i]['IoU'] for i in range(self.num_classes) if class_metrics[i]['IoU'] > 0]
        miou = np.mean(valid_ious) if valid_ious else 0.0
        
        metrics['overall'] = {
            'TP': total_tp, 'FP': total_fp, 'FN': total_fn,
            'precision': overall_precision,
            'recall': overall_recall,
            'f1': overall_f1,
            'mIoU': miou
        }
        
        metrics['per_class'] = class_metrics
        
        # 摘要統計
        metrics['summary'] = {
            'mean_precision': np.mean([class_metrics[i]['precision'] for i in range(self.num_classes)]),
            'mean_recall': np.mean([class_metrics[i]['recall'] for i in range(self.num_classes)]),
            'mean_f1': np.mean([class_metrics[i]['f1'] for i in range(self.num_classes)]),
            'mean_iou': miou
        }
        
        return metrics


class ConfusionMatrixEvaluator(BaseEvaluator):
    """混淆矩陣評估器"""
    
    def __init__(self, 
                 class_names: Dict[int, str],
                 iou_threshold: float = 0.5,
                 confidence_threshold: float = 0.5,
                 task_type: str = 'detection',  # 'detection' or 'segmentation'
                 verbose: bool = False):
        super().__init__(class_names, verbose)
        self.iou_threshold = iou_threshold
        self.confidence_threshold = confidence_threshold
        self.task_type = task_type
        
        if task_type == 'detection':
            self.metrics = DetectionMetrics(self.num_classes, iou_threshold)
        else:
            raise NotImplementedError(f"Task type {task_type} not implemented yet")
    
    def read_yolo_labels(self, label_file: str, img_width: int, img_height: int) -> List[Dict]:
        """讀取 YOLO 格式標籤文件"""
        boxes = []
        if not os.path.exists(label_file):
            return boxes
        
        try:
            with open(label_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1]) * img_width
                    y_center = float(parts[2]) * img_height
                    width = float(parts[3]) * img_width
                    height = float(parts[4]) * img_height
                    
                    # 轉換為 [x1, y1, x2, y2] 格式
                    x1 = x_center - width / 2
                    y1 = y_center - height / 2
                    x2 = x_center + width / 2
                    y2 = y_center + height / 2
                    
                    boxes.append({
                        'class_id': class_id,
                        'bbox': [x1, y1, x2, y2]
                    })
        except Exception as e:
            logger.error(f"Error reading label file {label_file}: {e}")
        
        return boxes
    
    def evaluate_from_model_results(self, model_results: List[Dict]) -> Dict[str, Any]:
        """從模型結果評估"""
        self.metrics.reset()
        
        for result in model_results:
            pred_boxes = result.get('predictions', [])
            gt_boxes = result.get('groundtruths', [])
            
            # 過濾低置信度預測
            filtered_pred_boxes = [
                box for box in pred_boxes 
                if box.get('confidence', 1.0) >= self.confidence_threshold
            ]
            
            self.metrics.update(filtered_pred_boxes, gt_boxes)
        
        return self.metrics.compute_metrics()
    
    def evaluate_from_files(self, 
                           predictions_dir: str,
                           groundtruths_dir: str,
                           image_dir: str) -> Dict[str, Any]:
        """從文件目錄評估"""
        import glob
        
        self.metrics.reset()
        
        # 獲取所有圖像文件
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png']:
            image_files.extend(glob.glob(os.path.join(image_dir, ext)))
        
        self._log_info(f"Found {len(image_files)} images for evaluation")
        
        processed = 0
        for img_path in image_files:
            img_id = os.path.splitext(os.path.basename(img_path))[0]
            
            # 讀取圖像尺寸
            img = cv2.imread(img_path)
            if img is None:
                continue
            img_height, img_width = img.shape[:2]
            
            # 讀取預測和真實標籤
            pred_file = os.path.join(predictions_dir, f"{img_id}.txt")
            gt_file = os.path.join(groundtruths_dir, f"{img_id}.txt")
            
            pred_boxes = self.read_yolo_labels(pred_file, img_width, img_height)
            gt_boxes = self.read_yolo_labels(gt_file, img_width, img_height)
            
            self.metrics.update(pred_boxes, gt_boxes)
            processed += 1
            
            if processed % 100 == 0:
                self._log_info(f"Processed {processed}/{len(image_files)} images")
        
        self._log_info(f"Evaluation completed. Processed {processed} images.")
        return self.metrics.compute_metrics()
    
    def evaluate(self, predictions: Any, groundtruths: Any) -> Dict[str, Any]:
        """統一評估接口"""
        if isinstance(predictions, str) and isinstance(groundtruths, str):
            # 如果是目錄路徑，需要額外的圖像目錄
            raise ValueError("For file-based evaluation, use evaluate_from_files method")
        elif isinstance(predictions, list) and isinstance(groundtruths, list):
            # 直接從結果列表評估
            results = []
            for pred, gt in zip(predictions, groundtruths):
                results.append({'predictions': pred, 'groundtruths': gt})
            return self.evaluate_from_model_results(results)
        else:
            raise ValueError("Unsupported input format")


class ConfusionMatrixVisualizer:
    """混淆矩陣可視化器"""
    
    def __init__(self, class_names: Dict[int, str], save_dir: str = "./evaluation_results"):
        self.class_names = class_names
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
    
    def plot_confusion_matrix(self, 
                            confusion_matrix: np.ndarray,
                            normalize: bool = True,
                            title: str = "Confusion Matrix",
                            figsize: Tuple[int, int] = (10, 8)) -> str:
        """繪製混淆矩陣"""
        plt.figure(figsize=figsize)
        
        if normalize:
            # 歸一化
            row_sums = confusion_matrix.sum(axis=1)
            normalized_cm = confusion_matrix / row_sums[:, np.newaxis]
            fmt = '.2f'
            cm_data = normalized_cm
        else:
            fmt = 'd'
            cm_data = confusion_matrix
        
        # 創建類別標籤
        class_labels = [self.class_names.get(i, f'Class {i}') for i in range(len(confusion_matrix))]
        
        # 繪製熱力圖
        sns.heatmap(cm_data, 
                   annot=True, 
                   fmt=fmt, 
                   cmap='Blues',
                   xticklabels=class_labels,
                   yticklabels=class_labels)
        
        plt.title(title, fontsize=16)
        plt.xlabel('Predicted Label', fontsize=14)
        plt.ylabel('True Label', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        # 保存圖片
        save_path = self.save_dir / f"{title.lower().replace(' ', '_')}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def plot_metrics_comparison(self, 
                              metrics: Dict[str, Any],
                              figsize: Tuple[int, int] = (12, 8)) -> str:
        """繪製各類別指標對比圖"""
        per_class_metrics = metrics['per_class']
        class_ids = list(per_class_metrics.keys())
        class_labels = [self.class_names.get(i, f'Class {i}') for i in class_ids]
        
        # 提取指標數據
        precision_values = [per_class_metrics[i]['precision'] for i in class_ids]
        recall_values = [per_class_metrics[i]['recall'] for i in class_ids]
        f1_values = [per_class_metrics[i]['f1'] for i in class_ids]
        iou_values = [per_class_metrics[i]['IoU'] for i in class_ids]
        
        # 繪製對比圖
        x = np.arange(len(class_ids))
        width = 0.2
        
        plt.figure(figsize=figsize)
        plt.bar(x - 1.5*width, precision_values, width, label='Precision', alpha=0.8)
        plt.bar(x - 0.5*width, recall_values, width, label='Recall', alpha=0.8)
        plt.bar(x + 0.5*width, f1_values, width, label='F1 Score', alpha=0.8)
        plt.bar(x + 1.5*width, iou_values, width, label='IoU', alpha=0.8)
        
        plt.xlabel('Classes', fontsize=14)
        plt.ylabel('Score', fontsize=14)
        plt.title('Per-Class Metrics Comparison', fontsize=16)
        plt.xticks(x, class_labels, rotation=45, ha='right')
        plt.ylim(0, 1.1)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存圖片
        save_path = self.save_dir / "metrics_comparison.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def save_metrics_report(self, 
                          metrics: Dict[str, Any],
                          model_info: Optional[Dict[str, Any]] = None) -> Tuple[str, str]:
        """保存詳細評估報告"""
        # 保存 JSON 格式
        json_path = self.save_dir / "evaluation_metrics.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        
        # 保存文字報告
        txt_path = self.save_dir / "evaluation_report.txt"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("模型評估報告\n")
            f.write("=" * 60 + "\n\n")
            
            if model_info:
                f.write("模型資訊:\n")
                for key, value in model_info.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")
            
            # 整體指標
            f.write("整體指標:\n")
            overall = metrics['overall']
            f.write(f"  TP: {overall['TP']}\n")
            f.write(f"  FP: {overall['FP']}\n")
            f.write(f"  FN: {overall['FN']}\n")
            f.write(f"  Precision: {overall['precision']:.4f}\n")
            f.write(f"  Recall: {overall['recall']:.4f}\n")
            f.write(f"  F1 Score: {overall['f1']:.4f}\n")
            f.write(f"  mIoU: {overall['mIoU']:.4f}\n\n")
            
            # 各類別指標
            f.write("各類別指標:\n")
            for class_id, class_metrics in metrics['per_class'].items():
                class_name = self.class_names.get(class_id, f'Class {class_id}')
                f.write(f"\n{class_name} (Class {class_id}):\n")
                f.write(f"  TP: {class_metrics['TP']}\n")
                f.write(f"  FP: {class_metrics['FP']}\n")
                f.write(f"  FN: {class_metrics['FN']}\n")
                f.write(f"  Precision: {class_metrics['precision']:.4f}\n")
                f.write(f"  Recall: {class_metrics['recall']:.4f}\n")
                f.write(f"  Specificity: {class_metrics['specificity']:.4f}\n")
                f.write(f"  F1 Score: {class_metrics['f1']:.4f}\n")
                f.write(f"  IoU: {class_metrics['IoU']:.4f}\n")
                f.write(f"  Mean IoU: {class_metrics['mean_IoU']:.4f}\n")
        
        return str(json_path), str(txt_path)
    
    def save_csv_summary(self, metrics: Dict[str, Any]) -> str:
        """保存 CSV 格式摘要"""
        csv_path = self.save_dir / "metrics_summary.csv"
        
        data = []
        # 添加整體指標
        overall = metrics['overall']
        data.append({
            'Class': 'Overall',
            'TP': overall['TP'],
            'FP': overall['FP'], 
            'FN': overall['FN'],
            'Precision': f"{overall['precision']:.4f}",
            'Recall': f"{overall['recall']:.4f}",
            'F1_Score': f"{overall['f1']:.4f}",
            'IoU': f"{overall['mIoU']:.4f}"
        })
        
        # 添加各類別指標
        for class_id, class_metrics in metrics['per_class'].items():
            class_name = self.class_names.get(class_id, f'Class {class_id}')
            data.append({
                'Class': f"{class_name} ({class_id})",
                'TP': class_metrics['TP'],
                'FP': class_metrics['FP'],
                'FN': class_metrics['FN'],
                'Precision': f"{class_metrics['precision']:.4f}",
                'Recall': f"{class_metrics['recall']:.4f}",
                'F1_Score': f"{class_metrics['f1']:.4f}",
                'IoU': f"{class_metrics['IoU']:.4f}"
            })
        
        df = pd.DataFrame(data)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        return str(csv_path)


# 工廠函數
def create_confusion_matrix_evaluator(class_names: Dict[int, str],
                                     iou_threshold: float = 0.5,
                                     confidence_threshold: float = 0.5,
                                     task_type: str = 'detection',
                                     verbose: bool = False) -> ConfusionMatrixEvaluator:
    """創建混淆矩陣評估器"""
    return ConfusionMatrixEvaluator(
        class_names=class_names,
        iou_threshold=iou_threshold,
        confidence_threshold=confidence_threshold,
        task_type=task_type,
        verbose=verbose
    )


def create_visualizer(class_names: Dict[int, str], 
                     save_dir: str = "./evaluation_results") -> ConfusionMatrixVisualizer:
    """創建可視化器"""
    return ConfusionMatrixVisualizer(class_names=class_names, save_dir=save_dir)


def evaluate_detection_model(model_predictions: List[Dict],
                            groundtruths: List[Dict],
                            class_names: Dict[int, str],
                            iou_threshold: float = 0.5,
                            confidence_threshold: float = 0.5,
                            save_dir: str = "./evaluation_results",
                            model_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """完整的檢測模型評估流程"""
    
    # 創建評估器
    evaluator = create_confusion_matrix_evaluator(
        class_names=class_names,
        iou_threshold=iou_threshold,
        confidence_threshold=confidence_threshold,
        task_type='detection',
        verbose=True
    )
    
    # 執行評估
    logger.info("Starting model evaluation...")
    results = []
    for pred, gt in zip(model_predictions, groundtruths):
        results.append({'predictions': pred, 'groundtruths': gt})
    
    metrics = evaluator.evaluate_from_model_results(results)
    
    # 創建可視化器並生成報告
    visualizer = create_visualizer(class_names, save_dir)
    
    # 生成可視化圖表
    visualizer.plot_metrics_comparison(metrics)
    
    # 保存報告
    json_path, txt_path = visualizer.save_metrics_report(metrics, model_info)
    csv_path = visualizer.save_csv_summary(metrics)
    
    logger.info(f"Evaluation completed. Results saved to:")
    logger.info(f"  JSON: {json_path}")
    logger.info(f"  TXT: {txt_path}")
    logger.info(f"  CSV: {csv_path}")
    
    return metrics


if __name__ == "__main__":
    # 測試代碼
    class_names = {0: 'background', 1: 'person', 2: 'car', 3: 'bike'}
    
    # 創建評估器
    evaluator = create_confusion_matrix_evaluator(
        class_names=class_names,
        iou_threshold=0.5,
        confidence_threshold=0.5,
        verbose=True
    )
    
    # 模擬一些測試數據
    pred_boxes = [
        {'class_id': 1, 'bbox': [10, 10, 50, 50], 'confidence': 0.9},
        {'class_id': 2, 'bbox': [100, 100, 150, 150], 'confidence': 0.8}
    ]
    
    gt_boxes = [
        {'class_id': 1, 'bbox': [12, 12, 52, 52]},
        {'class_id': 2, 'bbox': [98, 98, 148, 148]}
    ]
    
    results = [{'predictions': pred_boxes, 'groundtruths': gt_boxes}]
    metrics = evaluator.evaluate_from_model_results(results)
    
    print("Evaluation metrics:")
    print(f"Overall F1: {metrics['overall']['f1']:.4f}")
    print(f"Overall mIoU: {metrics['overall']['mIoU']:.4f}")
    
    print("Confusion matrix evaluator test completed successfully!")