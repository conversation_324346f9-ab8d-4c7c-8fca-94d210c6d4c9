# 圖像數據集處理工具集使用手冊

## 1. 概述

本工具集提供了一套完整的圖像數據集處理解決方案，專為物件檢測和圖像分割任務設計。它包含四個主要組件，可以作為獨立工具使用，或通過主流程腳本整合使用：

1. **標籤格式轉換器**：將各種格式(VOC、YOLO、COCO)的標籤轉換為統一的LabelMe格式
2. **標籤編輯器**：進行標籤合併、重命名和刪除等編輯操作
3. **數據集分割器**：將數據集分為訓練、驗證和測試集
4. **圖像增強器**：通過區域融合方式生成增強數據

主流程腳本將這四個組件整合為端到端的處理管道，可根據需要選擇執行特定步驟。

## 2. 環境要求與安裝

### 系統要求
- Python 3.7 或更高版本
- 足夠的磁盤空間用於處理大型數據集

### 依賴庫
```bash
pip install numpy opencv-python Pillow tqdm shapely matplotlib
```

### 安裝步驟
1. 下載所有腳本到同一目錄
2. 確保所有依賴庫已安裝
3. 確認腳本有執行權限

## 3. 快速入門

### 完整流程處理

使用主流程腳本可以一次性執行全部處理步驟：

```bash
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --all --interactive --resize 1
```

### 交互式處理

啟用交互式模式，在處理過程中配置各種參數：

```bash
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --interactive
```

### 選擇性執行特定步驟

只執行特定步驟(例如1和3)：

```bash
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --steps 1,3
```

## 4. 詳細功能說明

### 4.1 標籤格式轉換器 (annotation_converter.py)

該工具用於將各種標籤格式轉換為統一的LabelMe JSON格式，同時可以調整圖像大小和品質。

#### 主要功能
- 自動檢測輸入標籤格式
- 轉換各種格式(VOC XML、YOLO、COCO JSON)至LabelMe格式
- 調整圖像尺寸和品質
- 支持將矩形標註轉換為多邊形標註
- 批量處理多個目錄

#### 使用範例

**獨立使用**
```bash
python annotation_converter.py --inputs 資料目錄1 資料目錄2 --output 輸出目錄 --resize 0.5 --quality 80
```

**參數說明**
- `--inputs`：輸入目錄列表，可指定多個目錄
- `--output`：輸出目錄
- `--resize`：調整圖像大小，可以是縮放比例(如0.5)或特定尺寸(如'800x600')
- `--quality`：JPEG品質設定(1-100)，預設為75
- `--force`：強制覆寫已存在檔案

### 4.2 標籤編輯器 (annotation_editor.py)

該工具用於編輯LabelMe格式的標籤，包括合併、重命名和刪除標籤。

#### 主要功能
- 標籤合併：將多個不同標籤合併為一個標籤
- 標籤重命名：修改標籤名稱
- 標籤刪除：刪除指定標籤
- 交互式操作：提供命令行交互界面
- 操作歷史記錄和回滾功能

#### 使用範例

**獨立使用**
```bash
python annotation_editor.py --input 輸入目錄 --output 輸出目錄 --interactive
```

**批處理模式**
```bash
python annotation_editor.py --input 輸入目錄 --output 輸出目錄 --merge "target:old1,old2;target2:old3" --rename "old1:new1;old2:new2" --delete "label1,label2"
```

**參數說明**
- `--input`：輸入目錄
- `--output`：輸出目錄
- `--interactive`：啟動交互式會話
- `--merge`：合併規則，格式為'target:old1,old2;target2:old3'
- `--rename`：重命名規則，格式為'old1:new1;old2:new2'
- `--delete`：要刪除的標籤，格式為'label1,label2,label3'
- `--no-recursive`：不遞迴處理子目錄

### 4.3 數據集分割器 (dataset_divider.py)

該工具用於將處理好的數據集分為訓練、驗證和測試集，支持類別平衡。

#### 主要功能
- 按比例分割數據集(訓練/驗證/測試)
- 支持類別平衡和數量限制
- 自動為不同任務(分割/檢測)生成適合的標籤格式
- 生成詳細的分割統計報告
- 交互式設置類別限制

#### 使用範例

**獨立使用**
```bash
python dataset_divider.py --input 輸入目錄 --output 輸出目錄 --interactive
```

**自定義比例**
```bash
python dataset_divider.py --input 輸入目錄 --output 輸出目錄 --train-ratio 0.8 --val-ratio 0.1 --test-ratio 0.1
```

**參數說明**
- `--input`：輸入目錄
- `--output`：輸出目錄
- `--interactive`：交互式設定類別限制和分割比例
- `--train-ratio`：訓練集比例(預設0.7)
- `--val-ratio`：驗證集比例(預設0.15)
- `--test-ratio`：測試集比例(預設0.15)

### 4.4 圖像增強器 (img_augmenter.py)

該工具通過區域融合方式生成增強數據，提高數據多樣性。

#### 主要功能
- 從源圖像提取標註區域
- 將提取的區域放置到目標圖像上
- 支持網格放置策略和避免重疊
- 分別生成適用於分割和檢測任務的增強數據
- 類別目標數量控制，精確擴充特定類別

#### 使用範例

**獨立使用**
```bash
python img_augmenter.py --sources 來源目錄1 來源目錄2 --target 目標目錄 --output 輸出目錄 --interactive
```

**自定義生成參數**
```bash
python img_augmenter.py --sources 來源目錄 --target 目標目錄 --output 輸出目錄 --num 200 --margins "1,1,1,1" --grid-size 5 --task seg
```

**參數說明**
- `--sources`：來源目錄列表，包含要提取區域的圖像和標註
- `--target`：目標目錄，包含要放置區域的圖像和標註
- `--output`：輸出目錄
- `--num`：生成圖像數量(預設100)
- `--resize`：調整來源圖像尺寸的參數
- `--quality`：JPEG品質設定(1-100)
- `--margins`：放置區域邊距(top,right,bottom,left)，值範圍0-5
- `--grid-size`：網格大小(預設4)
- `--task`：任務類型，可選"seg"(分割)、"det"(檢測)或"both"(兩者)
- `--interactive`：啟用交互模式，可視化並調整生成參數

### 4.5 整合流程 (img_dataset_pipeline.py)

主流程腳本將上述四個組件整合為完整的處理管道。

#### 主要功能
- 順序執行標籤轉換、編輯、分割和增強步驟
- 支持選擇性執行特定步驟
- 統一的配置文件支持
- 詳細的日誌記錄

#### 使用範例

**完整流程**
```bash
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --all --interactive --resize 1
```

**使用配置文件**
```bash
python img_dataset_pipeline.py --config 配置文件.json --output 輸出目錄
```

**選擇性執行**
```bash
python img_dataset_pipeline.py --input 輸入目錄 --output 輸出目錄 --step 2
```

**參數說明**
- `--config`：配置文件路徑
- `--output`：輸出根目錄
- `--step`：執行特定步驟(1:轉換, 2:編輯, 3:分割, 4:增強)
- `--steps`：指定要執行的步驟，例如'1,2,3'
- `--all`：執行所有步驟
- `--interactive`：啟用交互式處理
- `--resize`：調整圖像大小
- `--quality`：JPEG品質
- 以及各組件的特定參數

## 5. 範例工作流程

### 從零開始的完整工作流程

1. **準備數據**：將原始標註數據放在合適的目錄結構中

2. **執行完整流程**：
```bash
python img_dataset_pipeline.py --input 原始數據目錄 --output 處理結果 --all --interactive
```

3. **檢查結果**：
   - 查看轉換後的標籤格式是否正確
   - 檢查編輯後的標籤是否符合預期
   - 確認數據集分割是否均衡
   - 驗證增強數據的質量

### 只處理特定步驟的工作流程

1. **已有LabelMe格式，只需編輯標籤**：
```bash
python img_dataset_pipeline.py --input 標籤目錄 --output 處理結果 --step 2 --interactive
```

2. **已完成編輯，只需分割數據集**：
```bash
python img_dataset_pipeline.py --input 編輯後目錄 --output 處理結果 --step 3 --train-ratio 0.8 --val-ratio 0.1 --test-ratio 0.1
```

3. **只需進行數據增強**：
```bash
python img_dataset_pipeline.py --input 分割後目錄 --output 處理結果 --step 4 --num-gen 200 --interactive
```

## 6. 配置文件格式

配置文件使用JSON格式，可以包含以下字段：

```json
{
  "input_dirs": ["目錄1", "目錄2"],
  "source_format": "auto",
  "target_shape": "polygon",
  "resize": 0.5,
  "quality": 80,
  
  "merge_rules": {
    "目標標籤1": ["來源標籤1", "來源標籤2"],
    "目標標籤2": ["來源標籤3"]
  },
  "rename_rules": {
    "舊標籤1": "新標籤1",
    "舊標籤2": "新標籤2"
  },
  "delete_labels": ["標籤1", "標籤2"],
  
  "train_ratio": 0.7,
  "val_ratio": 0.15,
  "test_ratio": 0.15,
  "class_limits": {
    "類別1": 100,
    "類別2": 200
  },
  
  "augment_source_dirs": ["來源目錄1"],
  "num_generations": 200,
  "class_targets": {
    "類別1": 300,
    "類別2": 400
  },
  "task_type": "both",
  
  "interactive": true,
  "interactive_mode": true
}
```

## 7. 常見問題解答

### Q: 如何確定使用哪種標籤格式轉換？
**A**: 可以使用自動檢測功能(`--format auto`)，工具會自動判斷輸入格式。

### Q: 為什麼要將標籤統一為LabelMe格式？
**A**: LabelMe格式支持多種形狀的標註(矩形、多邊形等)，且易於編輯和處理。

### Q: 如何處理大型數據集？
**A**: 可以使用`--resize`參數減小圖像尺寸，節省存儲空間並加快處理速度。

### Q: 標籤編輯時如何確保不會丟失數據？
**A**: 編輯器提供操作歷史和回滾功能，可以撤銷錯誤操作。使用`--output`指定新輸出目錄可以保留原始數據。

### Q: 如何解決數據集中類別不平衡問題？
**A**: 可在分割時使用類別限制功能，或在增強時設定類別目標數量，增加稀有類別的樣本。

### Q: 圖像增強的品質不佳怎麼辦？
**A**: 嘗試調整`--margins`和`--grid-size`參數，啟用`--visualize`選項查看放置區域，使用交互式模式調整參數。

### Q: 如何為特定任務生成數據？
**A**: 在圖像增強步驟中使用`--task-type`參數指定任務類型(seg、det或both)。

## 8. 最佳實踐

1. **處理前備份原始數據**：始終保留一份原始數據的備份。

2. **分階段處理大型數據集**：對於大型數據集，建議分階段處理並檢查中間結果。

3. **合理設置圖像尺寸**：根據目標模型的輸入需求設置合適的圖像尺寸。

4. **標籤命名規範化**：在編輯階段確保標籤命名規範統一。

5. **平衡分割比例**：根據具體任務需求調整分割比例，確保各集合數據充足。

6. **適度增強**：數據增強應當增加數據多樣性，而非製造噪聲或不合理的樣本。

7. **檢查增強結果**：定期檢查增強後的圖像和標籤質量。

8. **使用配置文件**：將常用參數保存為配置文件，提高重複執行效率。

9. **記錄處理參數**：記錄處理參數和統計信息，便於後續調整和重現。

10. **優先使用交互式模式**：初次使用時優先選擇交互式模式，熟悉工具功能和參數效果。

## 9. 進階應用

### 自定義處理流程

可以將各組件作為Python模塊導入，構建自定義處理流程：

```python
from annotation_converter import AnnotationConverter
from annotation_editor import AnnotationEditor
from dataset_divider import DatasetDivider
from img_augmenter import ImageAugmenter

# 自定義處理邏輯
converter = AnnotationConverter()
# ...其他代碼
```

### 批處理多個數據集

可使用腳本和配置文件批量處理多個數據集：

```bash
for dataset in dataset1 dataset2 dataset3; do
    python img_dataset_pipeline.py --config config.json --input $dataset --output ${dataset}_processed
done
```

### 與其他工具集成

本工具集生成的數據可以與各種深度學習框架(TensorFlow、PyTorch等)無縫集成，只需使用相應的數據加載器即可。

## 10. 更新和擴展

本工具集設計為模塊化結構，便於擴展新功能。若需增加新功能，可考慮：

1. **添加新的轉換格式**：在`annotation_converter.py`中擴展新的標籤格式支持

2. **增加更多增強方法**：在`img_augmenter.py`中添加新的增強策略

3. **擴展數據分割策略**：在`dataset_divider.py`中實現新的分割算法

4. **加入可視化工具**：增加更多可視化功能，幫助監控和驗證處理結果