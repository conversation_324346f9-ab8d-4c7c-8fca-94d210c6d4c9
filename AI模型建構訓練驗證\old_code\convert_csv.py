import pandas as pd
import os
import numpy as np
import shutil
from pathlib import Path
import random
import datetime


def convert_and_sample_data(input_csv_path, output_csv_path, sample_csv_path=None,
                            image_dir=None, sample_image_dir=None,
                            aspect_ratio_threshold=0.5, area_ratio_threshold=0.5, sample_rate=0.05):
    """
    讀取現有的CSV檔案，根據條件將裂縫分類為裂縫或龜裂，並抽取樣本檢查

    參數:
        input_csv_path: 輸入CSV檔案路徑
        output_csv_path: 輸出CSV檔案路徑
        sample_csv_path: 抽樣檔案路徑
        image_dir: 原始圖像目錄路徑
        sample_image_dir: 抽樣圖像保存目錄路徑
        aspect_ratio_threshold: 長寬比閾值，較小值/較大值低於此閾值視為細長形
        area_ratio_threshold: 面積比閾值，掩碼面積/框面積低於此閾值視為裂縫
        sample_rate: 抽樣比例
    """
    print(f"開始處理CSV檔案: {input_csv_path}")

    # 檢查圖像目錄
    has_image_dir = False
    if image_dir and os.path.isdir(image_dir):
        has_image_dir = True
        print(f"將從 {image_dir} 抽取圖像樣本")

        # 確保抽樣圖像目錄存在
        if sample_image_dir:
            os.makedirs(sample_image_dir, exist_ok=True)

            # 為每個類別建立子目錄
            for cls in ["裂縫", "龜裂", "補綻", "坑洞"]:
                os.makedirs(os.path.join(sample_image_dir, cls), exist_ok=True)
    else:
        print("未提供有效的圖像目錄，將只處理CSV資料")

    try:
        # 使用utf_8_sig編碼讀取CSV以支援中文
        df = pd.read_csv(input_csv_path, encoding='utf_8_sig')

        # 確認必要欄位存在
        required_columns = ["類別", "長", "寬", "面積", "檔案名稱"]
        for col in required_columns:
            if col not in df.columns:
                print(f"錯誤: CSV檔案缺少必要欄位 '{col}'")
                return False

        # 轉換欄位為數值型別
        df["長"] = pd.to_numeric(df["長"].astype(
            str).str.replace(',', ''), errors='coerce')
        df["寬"] = pd.to_numeric(df["寬"].astype(
            str).str.replace(',', ''), errors='coerce')
        df["面積"] = pd.to_numeric(df["面積"].astype(
            str).str.replace(',', ''), errors='coerce')

        # 確認座標欄位，若不存在則新增
        coord_columns = ["x1", "y1", "x2", "y2"]
        for col in coord_columns:
            if col not in df.columns:
                df[col] = np.nan

        # 確認置信度欄位，若不存在則新增
        if "置信度" not in df.columns:
            df["置信度"] = np.nan

        # 建立裂縫類型的新欄位
        df['長寬比'] = ''
        df['面積比'] = ''
        # df["裂縫類型"] = ""

        # 類別計數器
        class_counts = {}
        converted_counts = {
            "裂縫→裂縫": 0,
            "裂縫→龜裂": 0
        }

        # 抽樣記錄
        samples = {
            "裂縫": [],
            "龜裂": [],
            "補綻": [],
            "坑洞": []
        }

        # 建立檔案名稱到路徑的映射（如果有圖像目錄）
        file_to_path = {}
        if has_image_dir:
            print("正在掃描圖像目錄...")
            for root, _, files in os.walk(image_dir):
                for file in files:
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                        file_to_path[file] = os.path.join(root, file)
            print(f"找到 {len(file_to_path)} 個圖像檔案")

        # 遍歷每一行
        for index, row in df.iterrows():
            class_name = row["類別"]

            # 更新類別計數
            if class_name in class_counts:
                class_counts[class_name] += 1
            else:
                class_counts[class_name] = 1

            # 處理裂縫類別
            if class_name == "裂縫":
                # 計算長寬比 (確保使用較小值除以較大值)
                longer = max(row["長"], row["寬"])
                shorter = min(row["長"], row["寬"])
                aspect_ratio = shorter / longer if longer > 0 else 0
                df.at[index, "長寬比"] = aspect_ratio

                # 計算框面積與掩碼面積的比率
                box_area = row["長"] * row["寬"]
                mask_to_box_ratio = row["面積"] / box_area if box_area > 0 else 0
                df.at[index, "面積比"] = mask_to_box_ratio

                # 根據條件決定類型
                if aspect_ratio < aspect_ratio_threshold and mask_to_box_ratio < area_ratio_threshold:
                    # 符合裂縫條件
                    # df.at[index, "裂縫類型"] = "裂縫"
                    converted_counts["裂縫→裂縫"] += 1
                else:
                    # 變更為龜裂
                    # df.at[index, "裂縫類型"] = "龜裂"
                    df.at[index, "類別"] = "龜裂"
                    converted_counts["裂縫→龜裂"] += 1

                    # 更新類別計數
                    if "龜裂" in class_counts:
                        class_counts["龜裂"] += 1
                    else:
                        class_counts["龜裂"] = 1
                    class_counts["裂縫"] -= 1

        # 跟踪已抽樣的檔案
        sampled_files = set()

        # 再次遍歷，執行抽樣 (分成兩次遍歷是因為需要先完成所有類別轉換)
        for index, row in df.iterrows():
            class_name = row["類別"]
            filename = row["檔案名稱"]

            # 對指定類別進行抽樣
            if class_name in ["裂縫", "龜裂", "補綻", "坑洞"]:
                # 以相應比例抽樣
                if random.random() < sample_rate:
                    # 添加抽樣索引
                    sample_data = row.to_dict()
                    sample_data["原始索引"] = index
                    samples[class_name].append(sample_data)

                    # 如果有圖像目錄，則複製相應的圖像
                    if has_image_dir and sample_image_dir:
                        if filename in file_to_path:
                            src_path = file_to_path[filename]
                            # 為每個檔案建立唯一名稱以避免覆蓋
                            base_name = Path(filename).stem
                            ext = Path(filename).suffix
                            target_filename = f"{base_name}_{index}{ext}"

                            # 複製檔案到相應類別的目錄
                            dst_path = os.path.join(
                                sample_image_dir, class_name, target_filename)
                            if os.path.exists(src_path):
                                try:
                                    shutil.copy2(src_path, dst_path)
                                    # 更新樣本中的文件名
                                    samples[class_name][-1]["抽樣圖像路徑"] = dst_path
                                except Exception as e:
                                    print(f"複製圖像 {src_path} 時出錯: {str(e)}")
                            else:
                                print(f"圖像檔案不存在: {src_path}")

        # 保存結果
        os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
        df.to_csv(output_csv_path, index=False, encoding='utf_8_sig')

        # 建立抽樣DataFrame
        all_samples = []
        for class_name, items in samples.items():
            all_samples.extend(items)

        # 如果有抽樣資料，則保存抽樣結果
        if all_samples and sample_csv_path:
            sample_df = pd.DataFrame(all_samples)

            # 加入抽樣時間和註記欄位
            sample_df["抽樣時間"] = datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S")
            sample_df["檢查結果"] = ""  # 用於人工檢查標註

            # 保存抽樣檔案
            sample_df.to_csv(sample_csv_path, index=False,
                             encoding='utf_8_sig')
            print(f"抽樣檔案已保存至: {sample_csv_path}")
            print(
                f"抽樣數量: 裂縫 {len(samples['裂縫'])}, 龜裂 {len(samples['龜裂'])}, 補綻 {len(samples['補綻'])}, 坑洞 {len(samples['坑洞'])}")

            if has_image_dir and sample_image_dir:
                print(f"抽樣圖像已保存至: {sample_image_dir}")
                print(
                    f"裂縫圖像: {len([f for f in os.listdir(os.path.join(sample_image_dir, '裂縫')) if f.endswith(('.jpg', '.jpeg', '.png', '.bmp'))])} 張")
                print(
                    f"龜裂圖像: {len([f for f in os.listdir(os.path.join(sample_image_dir, '龜裂')) if f.endswith(('.jpg', '.jpeg', '.png', '.bmp'))])} 張")
                print(
                    f"補綻圖像: {len([f for f in os.listdir(os.path.join(sample_image_dir, '補綻')) if f.endswith(('.jpg', '.jpeg', '.png', '.bmp'))])} 張")
                print(
                    f"坑洞圖像: {len([f for f in os.listdir(os.path.join(sample_image_dir, '坑洞')) if f.endswith(('.jpg', '.jpeg', '.png', '.bmp'))])} 張")

        # 輸出轉換統計
        print("\n轉換完成！以下是詳細統計：")
        print("類別轉換:")
        for conversion, count in converted_counts.items():
            if count > 0:
                print(f"  {conversion}: {count}")

        # 輸出每類數量
        print("\n各類別數量:")
        for cls, count in sorted(class_counts.items()):
            print(f"  {cls}: {count}")

        print(f"\n結果已保存至: {output_csv_path}")

        return True

    except Exception as e:
        print(f"處理CSV檔案時發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def find_image_for_region(image_dir, filename, x1, y1, x2, y2, padding=50):
    """
    根據文件名和區域坐標找到圖像並裁剪感興趣區域

    參數:
        image_dir: 圖像目錄
        filename: 文件名
        x1, y1, x2, y2: 區域坐標
        padding: 周圍填充像素
    """
    try:
        import cv2

        # 嘗試找到圖像文件
        image_path = None
        for root, _, files in os.walk(image_dir):
            if filename in files:
                image_path = os.path.join(root, filename)
                break

        if not image_path:
            return None, f"找不到圖像: {filename}"

        # 讀取原始圖像
        img = cv2.imread(image_path)
        if img is None:
            return None, f"無法讀取圖像: {image_path}"

        # 獲取圖像尺寸
        height, width = img.shape[:2]

        # 轉換坐標為整數並添加填充
        x1 = max(0, int(x1) - padding)
        y1 = max(0, int(y1) - padding)
        x2 = min(width, int(x2) + padding)
        y2 = min(height, int(y2) + padding)

        # 裁剪圖像
        cropped_img = img[y1:y2, x1:x2]

        return cropped_img, image_path

    except Exception as e:
        return None, f"處理圖像時發生錯誤: {str(e)}"


if __name__ == "__main__":
    # 設定輸入和輸出路徑
    input_csv = input("請輸入要處理的CSV檔案路徑: ")

    # 生成輸出路徑
    input_path = Path(input_csv)
    output_csv = str(input_path.parent /
                     f"{input_path.stem}_converted{input_path.suffix}")
    sample_csv = str(input_path.parent /
                     f"{input_path.stem}_samples{input_path.suffix}")

    # 圖像目錄
    image_dir = input("請輸入原始圖像目錄路徑 (如果不需要處理圖像，請直接按Enter): ")

    # 如果提供了圖像目錄，則設定抽樣圖像目錄
    sample_image_dir = None
    if image_dir and os.path.isdir(image_dir):
        sample_image_dir = input(
            "請輸入抽樣圖像保存目錄路徑 (預設為 './sample_images'): ") or "./sample_images"

    # 設定參數
    aspect_ratio = float(input("請輸入長寬比閾值 (預設 0.5): ") or "0.5")
    area_ratio = float(input("請輸入面積比閾值 (預設 0.5): ") or "0.5")
    sample_rate = float(input("請輸入抽樣比例 (預設 0.05): ") or "0.05")

    # 執行轉換
    convert_and_sample_data(
        input_csv,
        output_csv,
        sample_csv,
        image_dir,
        sample_image_dir,
        aspect_ratio,
        area_ratio,
        sample_rate
    )