"""
編碼器配置便捷函數：提供快速訪問配置的函數
"""

from ..configs.encoder_configs import (
    get_csp_iformer_config,
    get_iformer_config,
    get_mobilenet_config,
    get_unet_config,
    list_encoder_configs,
    get_encoder_config
)

# 重新導出所有配置函數
__all__ = [
    'get_csp_iformer_config',
    'get_iformer_config', 
    'get_mobilenet_config',
    'get_unet_config',
    'list_encoder_configs',
    'get_encoder_config',
    'list_available_configs'
]


def list_available_configs():
    """列出所有可用配置的便捷函數"""
    return list_encoder_configs()