@echo off
chcp 65001 >nul
title 圖像樣本抽取工具 - 打包腳本

echo.
echo ╔══════════════════════════════════════════╗
echo ║          圖像樣本抽取工具                ║
echo ║            EXE打包工具                   ║
echo ╚══════════════════════════════════════════╝
echo.

:: 檢查Python環境
echo [1/5] 檢查Python環境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到Python環境
    echo 請確保已安裝Python並添加到PATH環境變數
    pause
    exit /b 1
)
echo ✅ Python環境正常

:: 檢查必要文件
echo.
echo [2/5] 檢查必要文件...
if not exist "image_sample_extractor.py" (
    echo ❌ 錯誤: 找不到 image_sample_extractor.py
    echo 請確保在正確的目錄中執行此批次文件
    pause
    exit /b 1
)
echo ✅ 找到主程式文件

:: 安裝/檢查依賴
echo.
echo [3/5] 檢查並安裝依賴套件...
echo 正在檢查PyQt6...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 正在安裝PyQt6...
    pip install PyQt6
)

echo 正在檢查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安裝PyInstaller...
    pip install pyinstaller
)
echo ✅ 依賴套件檢查完成

:: 清理舊文件
echo.
echo [4/5] 清理舊的建構文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"
echo ✅ 清理完成

:: 執行打包
echo.
echo [5/5] 開始打包程序...
echo 這可能需要幾分鐘時間，請耐心等待...

pyinstaller ^
    --onefile ^
    --windowed ^
    --name="圖像樣本抽取工具" ^
    --clean ^
    --noconfirm ^
    --add-data="*.py;." ^
    image_sample_extractor.py

:: 檢查結果
echo.
if exist "dist\圖像樣本抽取工具.exe" (
    echo ╔══════════════════════════════════════════╗
    echo ║              🎉 打包成功！               ║
    echo ╚══════════════════════════════════════════╝
    echo.
    echo 📦 輸出文件: dist\圖像樣本抽取工具.exe
    
    :: 獲取文件大小
    for %%A in ("dist\圖像樣本抽取工具.exe") do (
        set /a "file_size=%%~zA / 1024 / 1024"
    )
    echo 📏 文件大小: %file_size% MB
    echo.
    echo 💡 使用說明:
    echo    1. 將exe文件複製到目標電腦
    echo    2. 雙擊運行即可（無需安裝Python）
    echo    3. 首次運行可能需要較長時間
    echo.
    
    choice /c YN /m "是否現在運行測試程序？"
    if errorlevel 2 goto :end
    if errorlevel 1 (
        echo 正在啟動程序...
        start "" "dist\圖像樣本抽取工具.exe"
    )
) else (
    echo ╔══════════════════════════════════════════╗
    echo ║              ❌ 打包失敗！               ║
    echo ╚══════════════════════════════════════════╝
    echo.
    echo 請檢查以上錯誤信息，或嘗試手動打包:
    echo python build_exe.py
)

:end
echo.
echo 按任意鍵退出...
pause >nul