"""
配置管理器：處理模型配置的加載、保存和驗證
支持YAML和JSON格式的配置文件
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_root: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_root: 配置文件根目錄
        """
        if config_root is None:
            # 默認配置目錄
            current_dir = Path(__file__).parent.parent
            config_root = current_dir / "configs"
        
        self.config_root = Path(config_root)
        self.config_root.mkdir(exist_ok=True, parents=True)
        
        # 創建子目錄
        (self.config_root / "models").mkdir(exist_ok=True)
        (self.config_root / "encoders").mkdir(exist_ok=True)
        (self.config_root / "decoders").mkdir(exist_ok=True)
        (self.config_root / "training").mkdir(exist_ok=True)
    
    def load_config(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """
        加載配置文件
        
        Args:
            config_path: 配置文件路徑
            
        Returns:
            配置字典
        """
        config_path = Path(config_path)
        
        # 如果是相對路徑，則相對於配置根目錄
        if not config_path.is_absolute():
            config_path = self.config_root / config_path
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件未找到: {config_path}")
        
        # 根據文件擴展名選擇加載器
        if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
            return self._load_yaml(config_path)
        elif config_path.suffix.lower() == '.json':
            return self._load_json(config_path)
        else:
            raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
    
    def save_config(self, config: Dict[str, Any], config_path: Union[str, Path], format: str = 'yaml'):
        """
        保存配置文件
        
        Args:
            config: 配置字典
            config_path: 保存路徑
            format: 保存格式 ('yaml' 或 'json')
        """
        config_path = Path(config_path)
        
        # 如果是相對路徑，則相對於配置根目錄
        if not config_path.is_absolute():
            config_path = self.config_root / config_path
        
        # 確保目錄存在
        config_path.parent.mkdir(exist_ok=True, parents=True)
        
        # 根據格式保存
        if format.lower() == 'yaml':
            self._save_yaml(config, config_path)
        elif format.lower() == 'json':
            self._save_json(config, config_path)
        else:
            raise ValueError(f"不支持的保存格式: {format}")
    
    def _load_yaml(self, config_path: Path) -> Dict[str, Any]:
        """加載YAML配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except yaml.YAMLError as e:
            raise ValueError(f"YAML文件解析錯誤: {e}")
    
    def _load_json(self, config_path: Path) -> Dict[str, Any]:
        """加載JSON配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON文件解析錯誤: {e}")
    
    def _save_yaml(self, config: Dict[str, Any], config_path: Path):
        """保存YAML配置文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def _save_json(self, config: Dict[str, Any], config_path: Path):
        """保存JSON配置文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """
        獲取模型配置
        
        Args:
            model_name: 模型名稱
            
        Returns:
            模型配置字典
        """
        config_path = self.config_root / "models" / f"{model_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "models" / f"{model_name}.json"
        
        return self.load_config(config_path)
    
    def get_encoder_config(self, encoder_name: str) -> Dict[str, Any]:
        """獲取編碼器配置"""
        config_path = self.config_root / "encoders" / f"{encoder_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "encoders" / f"{encoder_name}.json"
        
        return self.load_config(config_path)
    
    def get_decoder_config(self, decoder_name: str) -> Dict[str, Any]:
        """獲取解碼器配置"""
        config_path = self.config_root / "decoders" / f"{decoder_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "decoders" / f"{decoder_name}.json"
        
        return self.load_config(config_path)
    
    def get_training_config(self, config_name: str) -> Dict[str, Any]:
        """獲取訓練配置"""
        config_path = self.config_root / "training" / f"{config_name}.yaml"
        if not config_path.exists():
            config_path = self.config_root / "training" / f"{config_name}.json"
        
        return self.load_config(config_path)
    
    def list_configs(self, config_type: str = None) -> Dict[str, list]:
        """
        列出所有可用配置
        
        Args:
            config_type: 配置類型 ('models', 'encoders', 'decoders', 'training')
            
        Returns:
            配置列表字典
        """
        config_types = ['models', 'encoders', 'decoders', 'training']
        if config_type and config_type not in config_types:
            raise ValueError(f"無效的配置類型: {config_type}")
        
        result = {}
        target_types = [config_type] if config_type else config_types
        
        for ct in target_types:
            config_dir = self.config_root / ct
            if config_dir.exists():
                configs = []
                for config_file in config_dir.glob("*.yaml"):
                    configs.append(config_file.stem)
                for config_file in config_dir.glob("*.json"):
                    if config_file.stem not in configs:  # 避免重複
                        configs.append(config_file.stem)
                result[ct] = sorted(configs)
            else:
                result[ct] = []
        
        return result
    
    def validate_config(self, config: Dict[str, Any], schema: Dict[str, Any] = None) -> bool:
        """
        驗證配置文件
        
        Args:
            config: 配置字典
            schema: 驗證模式（可選）
            
        Returns:
            是否驗證通過
        """
        # 基礎驗證
        if not isinstance(config, dict):
            return False
        
        # 如果提供了schema，可以在此添加更詳細的驗證
        # 目前只做基礎檢查
        return True
    
    def merge_configs(self, *configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        合併多個配置
        
        Args:
            *configs: 多個配置字典
            
        Returns:
            合併後的配置字典
        """
        merged = {}
        for config in configs:
            if config:
                merged.update(config)
        return merged