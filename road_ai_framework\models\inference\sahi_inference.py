#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四欄輸出：對資料夾內每一張影像，
  • Original      : 未標註原圖
  • Standard      : 整張 get_prediction() 推理後疊框/遮罩
  • Sliced (No Std): ROI 切片推理，但不做 perform_standard_pred
  • Sliced (With Std): ROI 切片推理，同時做 perform_standard_pred

這個版本會遍歷 INPUT_DIR 資料夾，對每張檔案做同樣的流程，結果分別存到 OUT_DIR 下。
"""
import os, time, datetime
from PIL import Image
import numpy as np
import cv2
from sahi import AutoDetectionModel
from sahi.predict import get_prediction, get_sliced_prediction
import colorsys

# ───── 參數 ──────────────────────────────────────────
MODEL_PATH  = r"C:\Users\<USER>\Desktop\best.pt"
INPUT_DIR   = r"C:\Users\<USER>\Desktop\比較\100外部測試\原圖"  # <-- 指定要遍歷的資料夾
OUT_DIR     = r"C:\Users\<USER>\Desktop\比較\100外部測試\四欄輸出"   # <-- 指定四欄結果要存放的資料夾
DEVICE      = "cuda:0"
CONF_THRES  = 0.1

# ROI 切片百分比
TOP_RATIO    = 0.20   # y1: 上邊界 20%
BOTTOM_RATIO = 0.80   # y2: 下邊界 80%
LEFT_RATIO   = 0     # x1: 左邊界 0%
RIGHT_RATIO  = 1     # x2: 右邊界 100%

# SAHI 切片參數（如果留空就由 auto_slice_resolution 自動決定）
SLICE_H, SLICE_W = 640, 640
OV_H, OV_W       = 0.2, 0.2
POSTPROCESS_TYPE           = "GREEDYNMM"
POSTPROCESS_METRIC         = "IOS"
POSTPROCESS_THRESHOLD      = 0.1
POSTPROCESS_CLASS_AGNOSTIC = False
MERGE_BUFFER_LENGTH        = 10
AUTO_SLICE_RESOLUTION      = True   # True 表示 SAHI 會自動決定 slice 大小
ALPHA                      = 0.3

# 支援的影像副檔名（小寫）
VALID_EXTS = {".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"}

# 建立輸出資料夾
os.makedirs(OUT_DIR, exist_ok=True)

# ───── 1. 載入模型 ───────────────────────────────────
model = AutoDetectionModel.from_pretrained(
    model_type="ultralytics",
    model_path=MODEL_PATH,
    confidence_threshold=CONF_THRES,
    device=DEVICE,
)

# ───── 色盤與 overlay 函式（不需修改） ───────────────────────────────────
def build_color_palette(n: int, s: float = 0.85, v: float = 0.95):
    """
    以黃金比例在 HSV 色環上跳躍，產生 n 個高飽和、高亮度且差異度大的 BGR 顏色。
    """
    palette = []
    h = 0.0
    phi = 0.618033988749895  # 黃金比例
    for _ in range(n):
        h = (h + phi) % 1.0
        r, g, b = colorsys.hsv_to_rgb(h, s, v)
        palette.append((int(b * 255), int(g * 255), int(r * 255)))  # → BGR
    return palette

PALETTE = build_color_palette(50)   # 產生 50 個 BGR 顏色
color_map = {}  # label → BGR

def overlay_predictions(img_np: np.ndarray, preds, alpha: float):
    """
    把 SAHI 結果疊回影像。
    輸出的 img_np 請確保是 BGR 格式，內容包含 mask 疊色＋bbox＋文字。
    """
    vis = img_np.copy()
    for obj in preds:
        raw   = obj.category.name if obj.category else str(obj.category_id)
        label = raw.split("_")[0]  # 只保留英文類別前綴

        # ① 先給每個新 label 指定一個顏色，如果超過 PALETTE 大小就輪迴使用
        if label not in color_map:
            idx = len(color_map) % len(PALETTE)
            color_map[label] = PALETTE[idx]
        color = color_map[label][::-1]   # BGR 轉 RGB 給 cv2 繪製文字和框用

        # ② 如果這個物件有 mask，就先疊色（按照 ALPHA）
        if obj.mask is not None:
            m = np.asarray(obj.mask.bool_mask, dtype=bool)
            if m.shape[:2] == vis.shape[:2]:
                for c in range(3):
                    vis[..., c] = np.where(
                        m,
                        vis[..., c] * (1 - alpha) + color[c] * alpha,
                        vis[..., c]
                    )

        # ③ 繪製 bounding box + 分數文字
        x_min, y_min, x_max, y_max = map(int, obj.bbox.to_xyxy())
        cv2.rectangle(vis, (x_min, y_min), (x_max, y_max), color, 2)
        txt = f"{label} {obj.score.value:.2f}"
        cv2.putText(
            vis, txt, (x_min, max(0, y_min - 8)),
            cv2.FONT_HERSHEY_DUPLEX, 1.2, color, 3, cv2.LINE_AA
        )
    return vis

def add_panel_title(img, title):
    """
    在影像左上角加一行白底黑字的標題文字
    """
    cv2.putText(img, title, (10, 40),
                cv2.FONT_HERSHEY_DUPLEX, 1.3,
                (255, 255, 255), 4, cv2.LINE_AA)
    cv2.putText(img, title, (10, 40),
                cv2.FONT_HERSHEY_DUPLEX, 1.3,
                (0, 0, 0), 2, cv2.LINE_AA)
    return img

# ───── 2. 遍歷資料夾，對每張影像做處理 ───────────────────────────────────
for fname in os.listdir(INPUT_DIR):
    ext = os.path.splitext(fname)[1].lower()
    if ext not in VALID_EXTS:
        # 如果不是圖片，就跳過
        continue

    img_path = os.path.join(INPUT_DIR, fname)
    print(f"\n▶ 正在處理：{fname}")

    # 2-1. 讀圖
    try:
        orig_pil = Image.open(img_path).convert("RGB")
    except Exception as e:
        print(f"  無法讀取影像 {fname}，已跳過。Error: {e}")
        continue

    W, H = orig_pil.size
    orig_np = np.array(orig_pil)  # BGR/RGB 都可，後續 overlay 會自行處理

    # 2-2. 定義 ROI 切片範圍（中央 80% 高度、左右 100% 寬度）
    x1 = int(W * LEFT_RATIO)
    y1 = int(H * TOP_RATIO)
    x2 = int(W * RIGHT_RATIO)
    y2 = int(H * BOTTOM_RATIO)
    roi_pil = orig_pil.crop((x1, y1, x2, y2))
    roi_np  = orig_np[y1:y2, x1:x2].copy()

    # ───── 3. 推理 ───────────────────────────────────────
    # 3-1 整張影像的標準推理（Standard Prediction）
    std_pred = get_prediction(orig_pil, model)

    # 3-2 ROI 切片推理（Sliced Prediction）—不做整圖推論
    t0 = time.perf_counter()
    slice_pred_no_std = get_sliced_prediction(
        roi_pil,
        detection_model=model,
        # 如果要指定 slice 大小，就取消下面兩行註解:
        # slice_height=SLICE_H,
        # slice_width=SLICE_W,
        overlap_height_ratio=OV_H,
        overlap_width_ratio=OV_W,
        perform_standard_pred=False,            # **不做 perform_standard_pred**
        postprocess_type=POSTPROCESS_TYPE,
        postprocess_match_metric=POSTPROCESS_METRIC,
        postprocess_match_threshold=POSTPROCESS_THRESHOLD,
        postprocess_class_agnostic=POSTPROCESS_CLASS_AGNOSTIC,
        merge_buffer_length=MERGE_BUFFER_LENGTH,
        auto_slice_resolution=AUTO_SLICE_RESOLUTION,
        slice_export_prefix=None,
        slice_dir=None
    )
    elapsed_no_std = time.perf_counter() - t0

    # 3-3 ROI 切片推理（Sliced Prediction）—做整圖推論
    t1 = time.perf_counter()
    slice_pred_with_std = get_sliced_prediction(
        roi_pil,
        detection_model=model,
        # slice_height=SLICE_H,
        # slice_width=SLICE_W,
        overlap_height_ratio=OV_H,
        overlap_width_ratio=OV_W,
        perform_standard_pred=True,             # **做 perform_standard_pred**
        postprocess_type=POSTPROCESS_TYPE,
        postprocess_match_metric=POSTPROCESS_METRIC,
        postprocess_match_threshold=POSTPROCESS_THRESHOLD,
        postprocess_class_agnostic=POSTPROCESS_CLASS_AGNOSTIC,
        merge_buffer_length=MERGE_BUFFER_LENGTH,
        auto_slice_resolution=AUTO_SLICE_RESOLUTION,
        slice_export_prefix=None,
        slice_dir=None
    )
    elapsed_with_std = time.perf_counter() - t1

    print(f"  ⏱ ROI 切片 (No Std) 耗時:  {elapsed_no_std:.2f} 秒")
    print(f"  ⏱ ROI 切片 (With Std) 耗時: {elapsed_with_std:.2f} 秒")

    # ───── 4. 自訂可視化 ─────────────────────────────────
    # 4-1 Original
    orig_panel = add_panel_title(orig_np.copy(), "Original")

    # 4-2 Standard Prediction 可視化
    std_vis   = overlay_predictions(orig_np.copy(), std_pred.object_prediction_list, ALPHA)
    std_panel = add_panel_title(std_vis, "Standard")

    # 4-3 Sliced Prediction (No Std) 可視化並貼回原圖
    roi_vis_no_std = overlay_predictions(roi_np.copy(), slice_pred_no_std.object_prediction_list, ALPHA)
    sliced_no_std  = orig_np.copy()
    sliced_no_std[y1:y2, x1:x2] = roi_vis_no_std
    sliced_no_std_panel = add_panel_title(sliced_no_std, "Sliced (No Std)")

    # 4-4 Sliced Prediction (With Std) 可視化並貼回原圖
    roi_vis_with_std = overlay_predictions(roi_np.copy(), slice_pred_with_std.object_prediction_list, ALPHA)
    sliced_with_std  = orig_np.copy()
    sliced_with_std[y1:y2, x1:x2] = roi_vis_with_std
    sliced_with_std_panel = add_panel_title(sliced_with_std, "Sliced (With Std)")

    # ───── 5. 拼接四欄 + 輸出 ───────────────────────────────────────
    # 依次：Original | Standard | Sliced (No Std) | Sliced (With Std)
    panel = np.hstack([
        orig_panel,
        std_panel,
        sliced_no_std_panel,
        sliced_with_std_panel
    ])  # 四欄拼接

    # 把當下時間串到檔名，避免覆蓋
    basename = os.path.splitext(fname)[0]
    now_str  = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    save_fname = f"{basename}_panel4_{now_str}.png"
    save_path  = os.path.join(OUT_DIR, save_fname)

    cv2.imwrite(save_path, panel[..., ::-1])  # 將 RGB→BGR，存成 PNG
    print(f"  ✅ 儲存完成 -> {save_fname}")

print("\n▶ 全部圖片處理完畢。")
