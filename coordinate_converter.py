#!/usr/bin/env python3
"""
LabelMe多邊形座標轉換為YOLO格式
"""

def polygon_to_yolo_bbox(polygon_points, image_width, image_height, class_id=0):
    """
    將多邊形座標轉換為YOLO格式的邊界框
    
    Args:
        polygon_points: 多邊形點列表 [[x1, y1], [x2, y2], ...]
        image_width: 圖像寬度
        image_height: 圖像高度
        class_id: 類別ID
    
    Returns:
        str: YOLO格式標註字符串
    """
    
    # 提取所有x和y座標
    x_coords = [point[0] for point in polygon_points]
    y_coords = [point[1] for point in polygon_points]
    
    # 計算邊界框
    x_min = min(x_coords)
    x_max = max(x_coords)
    y_min = min(y_coords)
    y_max = max(y_coords)
    
    print(f"邊界框座標:")
    print(f"  x_min: {x_min}")
    print(f"  x_max: {x_max}")
    print(f"  y_min: {y_min}")
    print(f"  y_max: {y_max}")
    
    # 計算邊界框的中心點和尺寸
    bbox_width = x_max - x_min
    bbox_height = y_max - y_min
    center_x = x_min + bbox_width / 2
    center_y = y_min + bbox_height / 2
    
    print(f"\n邊界框尺寸:")
    print(f"  寬度: {bbox_width}")
    print(f"  高度: {bbox_height}")
    print(f"  中心點: ({center_x}, {center_y})")
    
    # 歸一化到[0,1]範圍
    norm_center_x = center_x / image_width
    norm_center_y = center_y / image_height
    norm_width = bbox_width / image_width
    norm_height = bbox_height / image_height
    
    print(f"\n歸一化座標:")
    print(f"  中心點x: {norm_center_x:.6f}")
    print(f"  中心點y: {norm_center_y:.6f}")
    print(f"  寬度: {norm_width:.6f}")
    print(f"  高度: {norm_height:.6f}")
    
    # 生成YOLO格式標註
    yolo_annotation = f"{class_id} {norm_center_x:.6f} {norm_center_y:.6f} {norm_width:.6f} {norm_height:.6f}"
    
    return yolo_annotation

if __name__ == "__main__":
    # 輸入數據
    polygon_points = [
        [63.75, 433.125], [304.6875, 388.125], [484.6875, 368.4375], 
        [740.625, 348.75], [879.375, 343.125], [1018.125, 342.1875], 
        [1073.4375, 375.9375], [890.625, 385.3125], [751.875, 393.75], 
        [480.9375, 412.5], [292.5, 430.3125], [31.875, 465.0]
    ]
    
    image_width = 1200
    image_height = 900
    class_id = 0  # expansion_joint
    
    print("=== LabelMe多邊形座標轉換為YOLO格式 ===")
    print(f"類別: expansion_joint (ID = {class_id})")
    print(f"圖像尺寸: {image_width} x {image_height}")
    print(f"多邊形點數: {len(polygon_points)}")
    print()
    
    # 執行轉換
    yolo_result = polygon_to_yolo_bbox(polygon_points, image_width, image_height, class_id)
    
    print(f"\n=== 轉換結果 ===")
    print(f"YOLO格式標註: {yolo_result}")