# 模型架構與資料集讀取
# from model_create.Models.mobilev3seg.mobilenetv3_seg import MobileNetV3Seg
# from model_create.UNet import UNet
# from model_create.Models.CSP_IFormer_final_SegMode import iformer_small
# from model_create.decoder.BiFPN_2_CSPIformer import B<PERSON><PERSON><PERSON><PERSON>oder
# from model_create.head.heads import SegmentationHead
# from model_create.util.encoder_decoder_cat import Encoder_decoder

from model_create.util.yolo_dataset_read import SAMDataset
from model_create.util.metrics import get_metrics, runningScore
from model_create.util.train_function import train_sam, validate_sam, test
from model_create.util.show_img import show_img
from model_create.util.losses import SAMLoss

import sys

sys.path.append(r'D:\code\segment-anything-main')
from segment_anything import sam_model_registry, SamPredictor

# import
import logging
import os
import time
import wandb
from IPython.display import clear_output
import datetime
import torch
import numpy as np
import random
import matplotlib.pyplot as plt
import albumentations as A
from albumentations.pytorch import ToTensorV2

# pytorch
from torch.optim import AdamW
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False
# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Main')

def setup_seed(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.enabled = False


# set random seed
setup_seed(3407)

# 設定參數
use_wandb = True
modelname = 'mobilenetv3seg'
image_size = 384
learning_rate = 1e-4
train_epochs = 1000
batch_size = 64
n_classes = 7
num_workers = 0
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

# dataset from
data_dir = r'D:\image\5_test_image'
train_dir = os.path.join(data_dir, 'train')  # 訓練資料目錄
val_dir = os.path.join(data_dir, 'val')  # 驗證資料目錄
test_dir = os.path.join(data_dir, 'test')  # 測試資料目錄

# 取得當前時間
current_time = datetime.datetime.now()
# 格式化時間戳
timestamp = current_time.strftime("%Y%m%d_%H%M%S")
# running data savepath
path_total = r'./run_data'
if not os.path.isdir(path_total):
    os.mkdir(path_total)

# 這次要跑甚麼樣的資料
path_1 = f'/{modelname}_'+timestamp
if not os.path.isdir(path_total+path_1):
    os.mkdir(path_total+path_1)
# 資料存放位置
path_img = path_total+path_1+r'/img'
if not os.path.isdir(path_img):
    os.mkdir(path_img)
path_weight = path_total+path_1+r'/weight'
if not os.path.isdir(path_weight):
    os.mkdir(path_weight)

logger.info(f"使用設備: {device}")
logger.info(f"類別數量: {n_classes}")
logger.info(f"批次大小: {batch_size}")
logger.info(f"圖像大小: {image_size}x{image_size}")
logger.info(f"圖像存放位置:{path_img}")
logger.info(f"權重存放位置:{path_weight}")

# wandb
if use_wandb:
    wandb.init(project="road_crack", name="mobilenetv3seg")
    wandb.config.update({
        "learning_rate": learning_rate,
        "epochs": train_epochs,
        "batch_size": batch_size,
        "image_size": image_size,
        "n_classes": n_classes,
        "optimizer": "AdamW",
        "loss_function": "StagedRoadDamageLoss"
    })


# train Transform
train_transform = A.Compose([
    A.RandomResizedCrop(size=(image_size, image_size), scale=(0.6, 1.0), p=0.7),
    A.Resize(height=image_size,width=image_size),
    A.VerticalFlip(),
    A.HorizontalFlip(),
    A.RandomBrightnessContrast(p=0.2),
    A.RandomRotate90(p=0.2),
    A.Rotate(),
    A.Normalize(),
    ToTensorV2(),
], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))

# val/test Transform
val_transform = A.Compose([
    A.Resize(height=image_size, width=image_size),
    A.Normalize(),
    ToTensorV2(),
], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))

# 創建資料集
logger.info("建立資料集...")

# 使用新的數據集類
train_dataset = SAMDataset(
    data_dir=train_dir,
    size=image_size,
    split='train',
    transform=train_transform,
    log_enabled=False,
    prompt_type='point'  # 或 'point'
)
train_loader = DataLoader(
    train_dataset, batch_size=batch_size, shuffle=True, pin_memory=True)

val_dataset = SAMDataset(
    data_dir=val_dir,
    size=train_dir,
    split='val',
    transform=val_transform,
    log_enabled=False,
    prompt_type='point'  # 或 'point'
)
val_loader = DataLoader(val_dataset, batch_size=batch_size,
                        shuffle=False, pin_memory=True)

test_dataset = SAMDataset(
    data_dir=test_dir,
    size=image_size,
    split='test',
    transform=val_transform,
    log_enabled=False,
    prompt_type='point'  # 或 'point'
)
test_loader = DataLoader(test_dataset, batch_size=1,
                         shuffle=False, pin_memory=True)

print(f"train 資料集大小: {len(train_dataset)}")
print(f"valid 資料集大小: {len(val_dataset)}")
print(f"test 資料集大小: {len(test_dataset)}")

# 獲取並視覺化一個資料項
if len(train_dataset) > 0:
    idx = 0
    image, mask = train_dataset[idx]
    print(f"圖片 shape: {image.shape}")
    print(f"掩碼 shape: {mask.shape}")

    # 視覺化
    train_dataset.visualize(idx, show_n=3)

if len(val_dataset) > 0:
    idx = 0
    image, mask = val_dataset[idx]
    print(f"圖片 shape: {image.shape}")
    print(f"掩碼 shape: {mask.shape}")

    # 視覺化
    val_dataset.visualize(idx, show_n=3)

if len(test_dataset) > 0:
    idx = 0
    image, mask = test_dataset[idx]
    print(f"圖片 shape: {image.shape}")
    print(f"掩碼 shape: {mask.shape}")

    # 視覺化
    test_dataset.visualize(idx, show_n=3)

# input = torch.randn(1, 3, 768, 768).to(device)
sam_checkpoint = "sam_vit_h_4b8939.pth"  # 可修改為h或l版本
model_type = "vit_h"  # 可選 "vit_h", "vit_l", "vit_b"
model = sam_model_registry[model_type](checkpoint=sam_checkpoint)
model.to(device)
# 凍結編碼器（僅訓練解碼器）
for param in model.image_encoder.parameters():
    param.requires_grad = False
    
for param in model.prompt_encoder.parameters():
    param.requires_grad = False

# 掩碼解碼器設為訓練模式
model.mask_decoder.train()
# out = model(input)
# for o in out:
#     print(o.shape)
# # 如果有預訓練模型
# pretrain_weight_path=torch.load(r'D:\new_model_pipline\run_data\unet_20250419_095319\weight\epoch0198_f1_0.5010_best.pth')
# # 過濾掉不匹配的輸出層參數
# filtered_weights = {k: v for k, v in pretrain_weight_path.items() if 'outc.conv' not in k}

# model.load_state_dict(pretrain_weight_path, strict=False)

# 設定loss
# 例如，階段 1: 100 epochs, 階段 2: 100 epochs, 階段 3: 100 epochs, 階段 4: 100 epochs, 階段 5: 100 epochs
stage_epochs = [200, 200, 200, 200, 200]
loss_fn = SAMLoss(
    total_epochs=train_epochs, stage_epochs=stage_epochs)
# 設定優化器和學習率
optimizer = AdamW(model.parameters(), lr=learning_rate)
# 設定學習率衰減方法
scheduler = CosineAnnealingWarmRestarts(
    optimizer, T_0=50, T_mult=1, eta_min=1e-6)

logger.info(f"優化器: AdamW, 初始學習率: {optimizer.param_groups[0]['lr']}")
logger.info(f"學習率調度器: CosineAnnealingWarmRestarts")
logger.info(f"損失函數: StagedRoadDamageLoss")


# 開始訓練
best_F1 = 0
lr_list = []
# 用於保存每輪的損失值
loss_all_epochs = []

# 用於保存不同指標
Specificity_ = []
Senstivity_ = []
F1_ = []
acc_ = []
js_ = []

# 訓練循環中增加損失監控
ce_losses = []
dice_losses = []
focal_losses = []
boundary_losses = []


logger.info("開始訓練...")

for epoch in range(train_epochs):

    # 更新損失函數的 epoch
    loss_fn.update_epoch(epoch)
    # 訓練
    print(
        f"-----------------------------Epoch {epoch + 1:04d}|{train_epochs:04d}-----------------------------")
    t1 = time.time()
    input_img, target_img, pred_img, (loss_i, loss_comps) = train_sam(
        train_loader, model, optimizer, epoch, train_epochs, scheduler, device, loss_fn)
    loss_all_epochs.append(sum(loss_i)/len(loss_i))

    # 記錄各組件平均損失
    if loss_comps['ce']:
        ce_losses.append(sum(loss_comps['ce'])/len(loss_comps['ce']))
    if loss_comps['dice']:
        dice_losses.append(sum(loss_comps['dice'])/len(loss_comps['dice']))
    if loss_comps['focal']:
        focal_losses.append(sum(loss_comps['focal'])/len(loss_comps['focal']))
    if loss_comps['boundary']:
        boundary_losses.append(
            sum(loss_comps['boundary'])/len(loss_comps['boundary']))

    t2 = time.time()

    # 驗證
    t3 = time.time()
    val_input, val_mask, val_pred, score = train_sam(
        val_loader, model, epoch, train_epochs, n_classes, device, get_metrics, runningScore)
    t4 = time.time()

    # 清除輸出並顯示結果
    clear_output(wait=True)
    print(
        f"-----------------------------Epoch {epoch + 1:04d}|{train_epochs:04d}-----------------------------")
    print(f'當前學習率: {optimizer.param_groups[0]["lr"]}')
    lr_list.append(f'{optimizer.param_groups[0]["lr"]:.9f}')
    print(f"訓練時間: {t2-t1:.2f}s, 訓練損失: {(sum(loss_i)/len(loss_i)):.4f}")

    # 顯示訓練樣本
    show_img(epoch+1, input_img, target_img, pred_img, path_img, 'train')

    print(f"驗證時間: {t4-t3:.2f}s")
    # 顯示驗證樣本
    show_img(epoch+1, val_input, val_mask, val_pred, path_img, 'Valid')

    # 在顯示訓練和驗證樣本之後添加
    train_img = show_img(epoch+1, input_img, target_img,
                         pred_img, path_img, 'train', return_fig=True)
    val_img = show_img(epoch+1, val_input, val_mask, val_pred,
                       path_img, 'Valid', return_fig=True)
    if use_wandb:
        wandb.log({
            "train_visualization": wandb.Image(train_img),
            "validation_visualization": wandb.Image(val_img)
        })

    print("各項指標: ", score)
    print(f"當前損失階段: {epoch // (train_epochs // 5) + 1}/5")
    # 打印最新的損失組件值
    print(f"損失組件: CE={ce_losses[-1]:.4f}, Dice={dice_losses[-1]:.4f}, "
          f"Focal={focal_losses[-1]:.4f}, Boundary={boundary_losses[-1]:.4f}")

    # 保存最佳模型
    if score["F1"] > best_F1:
        best_F1 = score["F1"]
        print('最佳 F1: ', best_F1)
        model_path = f'{path_weight}/epoch{epoch + 1:04d}_f1_{best_F1:.4f}_best.pth'
        torch.save(model.state_dict(), model_path)
        print('保存權重...')

    # 保存最後一輪的模型
    if epoch == (train_epochs - 1):
        torch.save(model.state_dict(),
                   f'{path_weight}/epoch{epoch + 1:04d}_{best_F1:.4f}_last.pth')

    # 收集指標
    Specificity_.append(score["Specificity"])
    Senstivity_.append(score["Sensitivity"])
    F1_.append(score["F1"])
    acc_.append(score["acc"])
    js_.append(score["js"])
    if use_wandb:
        # wandb 追蹤
        wandb.log({
            "epoch": epoch + 1,
            "loss": sum(loss_i)/len(loss_i),
            "learning_rate": float(optimizer.param_groups[0]["lr"]),
            "F1": score["F1"],
            "Specificity": score["Specificity"],
            "Sensitivity": score["Sensitivity"],
            "Accuracy": score["acc"],
            "Jaccard_Score": score["js"],
            "MIoU": score["MIoU"],
            "Pixel_Accuracy": score["Pixel_Accuracy"]
        })


logger.info("訓練完成，繪製指標曲線...")

# 訓練結束，繪製指標曲線
plt.figure(figsize=(15, 10))
epochs = range(1, train_epochs + 1)

plt.subplot(2, 2, 1)
plt.plot(epochs, loss_all_epochs, 'b-', label='訓練損失')
plt.title('訓練損失')
plt.xlabel('輪次')
plt.ylabel('損失')
plt.legend()

plt.subplot(2, 2, 2)
plt.plot(epochs, F1_, 'r-', label='F1 分數')
plt.title('F1 分數')
plt.xlabel('輪次')
plt.ylabel('F1')
plt.legend()

plt.subplot(2, 2, 3)
plt.plot(epochs, acc_, 'g-', label='準確率')
plt.plot(epochs, js_, 'c-', label='Jaccard 分數')
plt.title('準確率和 Jaccard 分數')
plt.xlabel('輪次')
plt.ylabel('分數')
plt.legend()

plt.subplot(2, 2, 4)
plt.plot(epochs, Specificity_, 'm-', label='特異性')
plt.plot(epochs, Senstivity_, 'y-', label='敏感性')
plt.title('特異性和敏感性')
plt.xlabel('輪次')
plt.ylabel('分數')
plt.legend()

plt.tight_layout()
plt.savefig(os.path.join(path_img, 'training_metrics.png'))
plt.show()

plt.subplot(2, 3, 5)
plt.plot(epochs, ce_losses, 'b-', label='CE 損失')
plt.plot(epochs, dice_losses, 'r-', label='Dice 損失')
plt.title('CE 和 Dice 損失')
plt.xlabel('輪次')
plt.ylabel('損失')
plt.legend()

plt.subplot(2, 3, 6)
plt.plot(epochs, focal_losses, 'g-', label='Focal 損失')
plt.plot(epochs, boundary_losses, 'm-', label='邊界損失')
plt.title('Focal 和邊界損失')
plt.xlabel('輪次')
plt.ylabel('損失')
plt.legend()

logger.info("開始測試...")

# 加載最佳模型進行測試
best_model_path = f'{path_weight}/epoch{epoch + 1:04d}_f1_{best_F1:.4f}_best.pth'
if os.path.exists(best_model_path):
    model.load_state_dict(torch.load(best_model_path))
    logger.info(f"已加載最佳模型: {best_model_path}")

    # 進行測試
    test_score = test(test_loader, model, n_classes, path_img,
                      device, get_metrics, runningScore)
    logger.info("測試完成!")
    logger.info("測試結果:")
    for key, value in test_score.items():
        if isinstance(value, float):
            logger.info(f"{key}: {value:.4f}")
    if use_wandb:
        wandb.log({
            "test_F1": test_score["F1"],
            "test_MIoU": test_score["MIoU"],
            "test_Pixel_Accuracy": test_score["Pixel_Accuracy"],
            "test_Specificity": test_score["Specificity"],
            "test_Sensitivity": test_score["Sensitivity"],
            "test_Precision": test_score["Precision"]
        })

logger.info("訓練、驗證和測試完成!")

# 返回訓練結果
results = {
    'best_F1': best_F1,
    'loss_all_epochs': loss_all_epochs,
    'Specificity': Specificity_,
    'Sensitivity': Senstivity_,
    'F1': F1_,
    'acc': acc_,
    'js': js_,
    'ce_losses': ce_losses,
    'dice_losses': dice_losses,
    'focal_losses': focal_losses,
    'boundary_losses': boundary_losses,
    'model_path': best_model_path
}

if use_wandb:
    wandb.finish()