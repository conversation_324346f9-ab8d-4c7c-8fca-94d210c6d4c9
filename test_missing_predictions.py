#!/usr/bin/env python3
"""
測試 GT有但pred沒有的類別記錄功能
"""

def test_missing_predictions():
    """測試從未被預測的類別是否被正確記錄為FN"""
    
    # 模擬圖像數據
    batch_image_data = [
        {
            'image_name': 'image1.jpg',
            'detailed_results': {
                'ground_truth': [
                    {'class_name': 'class_A', 'bbox': [10, 10, 50, 50]},
                    {'class_name': 'class_B', 'bbox': [100, 100, 140, 140]},
                    {'class_name': 'class_C', 'bbox': [200, 200, 240, 240]}  # 這個類別從未被預測
                ],
                'detection': {
                    'detections': [
                        {'class_name': 'class_A', 'bbox': [12, 12, 52, 52], 'confidence': 0.9},
                        {'class_name': 'class_B', 'bbox': [102, 102, 142, 142], 'confidence': 0.8}
                        # 注意：沒有 class_C 的預測
                    ]
                }
            }
        },
        {
            'image_name': 'image2.jpg',
            'detailed_results': {
                'ground_truth': [
                    {'class_name': 'class_A', 'bbox': [20, 20, 60, 60]},
                    {'class_name': 'class_C', 'bbox': [300, 300, 340, 340]}  # class_C 再次出現在GT中但沒有預測
                ],
                'detection': {
                    'detections': [
                        {'class_name': 'class_A', 'bbox': [22, 22, 62, 62], 'confidence': 0.85}
                        # 仍然沒有 class_C 的預測
                    ]
                }
            }
        }
    ]
    
    # 模擬已累積的 batch_class_data（只包含被預測過的類別）
    batch_class_data = {
        'class_A': {'total_tp': 2, 'total_fp': 0, 'total_fn': 0},  # 完美預測
        'class_B': {'total_tp': 1, 'total_fp': 0, 'total_fn': 0}   # 完美預測
        # 注意：class_C 不在 batch_class_data 中，因為從未被預測
    }
    
    print("=== 測試場景 ===")
    print("圖像1 GT: class_A, class_B, class_C")
    print("圖像1 預測: class_A, class_B")
    print("圖像2 GT: class_A, class_C") 
    print("圖像2 預測: class_A")
    print("➜ class_C 在兩張圖像的GT中都有，但從未被預測到")
    print()
    
    # 模擬新的 class_metrics 生成邏輯
    print("=== 新邏輯：確保包含所有GT類別 ===")
    
    # 1. 收集所有GT中出現的類別
    all_gt_classes = set()
    for img_data in batch_image_data:
        if 'detailed_results' in img_data and 'ground_truth' in img_data['detailed_results']:
            for gt in img_data['detailed_results']['ground_truth']:
                all_gt_classes.add(gt.get('class_name', ''))
    
    print(f"所有GT中出現的類別: {all_gt_classes}")
    
    # 2. 初始化 class_stats，確保所有GT類別都包含
    class_stats = {}
    for class_name in all_gt_classes:
        if class_name and class_name in batch_class_data:
            # 使用已累積的數據
            data = batch_class_data[class_name]
            class_stats[class_name] = {
                'tp': data['total_tp'],
                'fp': data['total_fp'], 
                'fn': data['total_fn']
            }
            print(f"✅ 已預測過的類別 {class_name}: TP={data['total_tp']}, FP={data['total_fp']}, FN={data['total_fn']}")
        elif class_name:
            # 從未被預測的類別，計算純FN
            pure_fn = 0
            for img_data in batch_image_data:
                if 'detailed_results' in img_data and 'ground_truth' in img_data['detailed_results']:
                    gt_count = sum(1 for gt in img_data['detailed_results']['ground_truth'] 
                                 if gt.get('class_name') == class_name)
                    pure_fn += gt_count
            
            class_stats[class_name] = {
                'tp': 0,
                'fp': 0,
                'fn': pure_fn
            }
            print(f"📍 從未被預測的類別 {class_name}: 純FN={pure_fn}")
    
    # 3. 也包含batch_class_data中但不在GT中的類別（純FP情況）
    for class_name, data in batch_class_data.items():
        if class_name not in class_stats:
            class_stats[class_name] = {
                'tp': data['total_tp'],
                'fp': data['total_fp'], 
                'fn': data['total_fn']
            }
            print(f"⚠️  預測但不在GT中的類別 {class_name}: TP={data['total_tp']}, FP={data['total_fp']}, FN={data['total_fn']}")
    
    print(f"\n=== 最終 class_stats ===")
    for class_name, stats in class_stats.items():
        tp, fp, fn = stats['tp'], stats['fp'], stats['fn']
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        print(f"{class_name}: TP={tp}, FP={fp}, FN={fn}")
        print(f"  Precision={precision:.3f}, Recall={recall:.3f}, F1={f1:.3f}")
    
    # 模擬 CSV 生成
    print(f"\n=== 生成 CSV 內容 ===")
    csv_lines = []
    csv_lines.append("各類別名稱,TP,FP,FN,Precision,Recall,F1,類別總數,誤判率,漏判率")
    
    for class_name, stats in class_stats.items():
        tp = stats['tp']
        fp = stats['fp']
        fn = stats['fn']
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        total_count = tp + fn  # 實際存在的該類別數量
        false_positive_rate = fp / (fp + tp) if (fp + tp) > 0 else 0  # 誤判率
        false_negative_rate = fn / (fn + tp) if (fn + tp) > 0 else 0  # 漏判率
        
        csv_lines.append(f"{class_name},{tp},{fp},{fn},"
                       f"{precision:.3f},{recall:.3f},{f1:.3f},"
                       f"{total_count},{false_positive_rate:.3f},{false_negative_rate:.3f}")
    
    for line in csv_lines:
        print(line)
    
    # 驗證結果
    print(f"\n=== 驗證 ===")
    expected_results = {
        'class_A': {'tp': 2, 'fp': 0, 'fn': 0},  # 兩次完美匹配
        'class_B': {'tp': 1, 'fp': 0, 'fn': 0},  # 一次完美匹配
        'class_C': {'tp': 0, 'fp': 0, 'fn': 2}   # 兩次GT但從未被預測，純FN
    }
    
    all_correct = True
    for class_name, expected in expected_results.items():
        actual = class_stats.get(class_name, {'tp': -1, 'fp': -1, 'fn': -1})
        match = (actual['tp'] == expected['tp'] and 
                actual['fp'] == expected['fp'] and 
                actual['fn'] == expected['fn'])
        all_correct = all_correct and match
        
        status = "✅" if match else "❌"
        print(f"{status} {class_name}: 預期={expected}, 實際={actual}")
    
    # 檢查 CSV 是否包含所有類別
    csv_has_all_classes = len(csv_lines) == len(expected_results) + 1  # +1 for header
    
    if all_correct and csv_has_all_classes:
        print(f"\n🎉 測試通過！")
        print(f"✅ 所有GT類別都被正確記錄，包括從未被預測的 class_C")
        print(f"✅ class_C 正確記錄為純FN (TP=0, FP=0, FN=2)")
        print(f"✅ CSV 包含所有 {len(expected_results)} 個類別")
    else:
        print(f"\n❌ 測試失敗，邏輯需要檢查")

if __name__ == "__main__":
    test_missing_predictions()