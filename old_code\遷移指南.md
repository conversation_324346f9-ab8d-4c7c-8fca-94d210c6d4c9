# 模組重構遷移指南

## 概述

我們已經重構了三個核心模組：dataset、checkpoint、losses，將原來分散的功能整合到統一的模組中。本指南說明如何更新導入語句以使用新的重構模組。

## 主要變更

### 1. Dataset模組整合
- **舊模組**: `Dataset_read.py` 和 `base_dataset.py`
- **新模組**: `dataset.py`

### 2. Checkpoint模組整合
- **舊模組**: `checkpoint.py` 和 `checkpoint_optimized.py`
- **新模組**: `checkpoint.py` (已更新)

### 3. Losses模組整合
- **舊模組**: `losses.py` 和 `losses_optimized.py`
- **新模組**: `losses.py` (已更新)

## 導入語句更新對照表

### Dataset相關

#### 舊導入語句:
```python
from model_create.util.Dataset_read import YOLODataset, LabelmeDataset, convert_labelme_to_yolo, denormalize
from model_create.util.base_dataset import BaseVisionDataset, DatasetConfig
```

#### 新導入語句:
```python
from model_create.util.dataset import YOLODataset, LabelmeDataset, convert_labelme_to_yolo, denormalize
from model_create.util.dataset import BaseVisionDataset, DatasetConfig, ImageProcessor, FontManager
```

#### 或者使用統一導入:
```python
from model_create.util import YOLODataset, LabelmeDataset, convert_labelme_to_yolo, denormalize
```

### Checkpoint相關

#### 舊導入語句:
```python
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint
from model_create.util.checkpoint_optimized import CheckpointManager
```

#### 新導入語句:
```python
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint, CheckpointManager
```

#### 或者使用統一導入:
```python
from model_create.util import find_latest_checkpoint, load_checkpoint, CheckpointManager
```

### Losses相關

#### 舊導入語句:
```python
from model_create.util.losses import StagedRoadDamageLoss, DiceLoss, cross_entropy2d
from model_create.util.losses_optimized import FocalLoss, CombinedLoss, LossFactory
```

#### 新導入語句:
```python
from model_create.util.losses import StagedRoadDamageLoss, DiceLoss, FocalLoss, CombinedLoss, LossFactory, cross_entropy2d
```

#### 或者使用統一導入:
```python
from model_create.util import StagedRoadDamageLoss, DiceLoss, FocalLoss, CombinedLoss, LossFactory
```

## 需要更新的文件

根據搜索結果，以下文件需要更新導入語句：

### Jupyter Notebooks
- `0_seg.ipynb`
- `0_seg copy.ipynb`
- `0_seg copy 2.ipynb`
- `0_seg_SAMFine.ipynb`
- `0_yolo.ipynb`
- `0_yolo_1.ipynb`

### Python文件
- `model.py`
- `modified-code.py`
- `model_create/util/train_function.py`
- `model_create/util/train_function_optimized.py`
- `model_create/full_model/segman/segman_encoder.py`
- `model_create/full_model/segman/segman_decoder.py`
- `model_create/full_model/segman_encoder.py`

## 向後兼容性

為了確保向後兼容，我們在`__init__.py`中提供了別名：

```python
# 向後兼容的別名
from .dataset import YOLODataset as Dataset_read_YOLODataset
from .dataset import LabelmeDataset as Dataset_read_LabelmeDataset
```

## 推薦的更新步驟

1. **漸進式更新**: 先更新一個文件，測試確認無誤後再更新其他文件
2. **使用統一導入**: 推薦使用 `from model_create.util import ...` 的方式
3. **檢查功能**: 更新後確認所有功能正常運行
4. **移除舊文件**: 確認所有文件都更新完成後，可以考慮移除舊的模組文件

## 新功能

重構後的模組提供了一些新功能：

### Dataset模組
- 更好的模組化設計
- 統一的配置管理 (`DatasetConfig`)
- 改進的字體管理 (`FontManager`)
- 優化的圖像處理工具 (`ImageProcessor`)

### Checkpoint模組
- 更完整的檢查點管理 (`CheckpointManager`)
- 元資料管理
- 自動清理舊檢查點
- 檢查點導出功能

### Losses模組
- 現代化的損失函數架構
- 工廠模式創建損失函數 (`LossFactory`)
- 組合損失函數 (`CombinedLoss`)
- 改進的錯誤處理

## 範例更新

### 更新前 (0_seg.ipynb):
```python
from model_create.util.Dataset_read import YOLODataset, LabelmeDataset
from model_create.util.losses import StagedRoadDamageLoss
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint
```

### 更新後:
```python
from model_create.util import YOLODataset, LabelmeDataset, StagedRoadDamageLoss
from model_create.util import find_latest_checkpoint, load_checkpoint
```

或者：
```python
from model_create.util.dataset import YOLODataset, LabelmeDataset
from model_create.util.losses import StagedRoadDamageLoss
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint
```

## 測試建議

1. **單元測試**: 確認每個重構的模組功能正常
2. **集成測試**: 確認整個訓練流程可以正常運行
3. **性能測試**: 確認重構沒有影響性能

## 疑難排解

如果遇到導入錯誤：

1. 檢查文件路徑是否正確
2. 確認新模組文件存在
3. 檢查Python路徑設置
4. 查看錯誤訊息中的具體模組名稱

如有問題，請參考各模組內的文檔和使用示例。