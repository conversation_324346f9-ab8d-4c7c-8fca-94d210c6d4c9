#!/usr/bin/env python3
"""
Enhanced YOLO推理運行腳本
提供簡化的參數設定界面
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

if not ENHANCED_YOLO_AVAILABLE:
    print("❌ Enhanced YOLO模組不可用")
    sys.exit(1)

from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
    EnhancedYOLOConfig, create_enhanced_yolo_inference, ClassConfig,
    scan_labelme_annotations, generate_class_configs_from_labelme
)

def main():
    """
    Enhanced YOLO推理主函數
    在這裡設置所有推理參數
    """
    
    print("🚀 Enhanced YOLO推理系統")
    print("=" * 50)
    
    # ===================================================================
    # 📋 參數設定區域 - 請根據您的需求修改以下參數
    # ===================================================================
    
    # 🎯 基礎配置
    detection_model_path = "output/vision_mamba_model.pth"              # 檢測模型路徑 (YOLO12)
    segmentation_model_path = "output/vision_mamba_model.pth"  # 分割模型路徑 (YOLO11)
    input_path = "./test_image"            # 輸入圖像或目錄路徑
    output_path = "./output"               # 輸出目錄路徑
    task_type = "segmentation"             # 任務類型: "detection", "segmentation", "both"
    
    # 🎛️ 推理參數
    img_size = 640                         # 圖像大小
    global_conf = 0.25                     # 全局置信度閾值
    iou_threshold = 0.45                   # IoU閾值
    max_det = 1000                         # 最大檢測數量
    device = "auto"                        # 設備: "auto", "cuda", "cpu"
    
    # 🧩 SAHI配置 (大圖像切片推理)
    enable_sahi = True                     # 是否啟用SAHI
    slice_height = 512                     # 切片高度
    slice_width = 512                      # 切片寬度
    overlap_height_ratio = 0.2             # 高度重疊比例
    overlap_width_ratio = 0.2              # 寬度重疊比例
    
    # 📊 輸出配置
    save_visualizations = True             # 保存可視化結果
    save_predictions = True                # 保存預測結果
    save_statistics = True                 # 保存統計信息
    
    # 🏷️ LabelMe自動配置 (如果有LabelMe標註)
    labelme_dir = ""                       # LabelMe標註目錄，空則使用預設類別
    auto_generate_classes = True           # 是否自動從LabelMe生成類別配置
    
    # 🎨 進階配置
    batch_processing = True                # 批次處理 (True=目錄, False=單張圖像)
    enable_tracking = False                # 物件追蹤
    enable_pose_estimation = False         # 姿態估計
    enable_classification = False          # 分類
    
    # ===================================================================
    # 🔍 參數驗證和預處理
    # ===================================================================
    
    print("📋 配置檢查...")
    
    # 檢查模型路徑
    if not detection_model_path and not segmentation_model_path:
        print("❌ 錯誤: 至少需要指定一個模型路徑")
        print("   請設置 detection_model_path 或 segmentation_model_path")
        return
    
    # 檢查輸入路徑
    if not input_path or not Path(input_path).exists():
        print(f"❌ 錯誤: 輸入路徑不存在: {input_path}")
        print("   請檢查 input_path 設置")
        return
    
    # 創建輸出目錄
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # 自動生成類別配置
    class_configs = {}
    if labelme_dir and Path(labelme_dir).exists() and auto_generate_classes:
        print(f"🏷️  正在掃描LabelMe標註: {labelme_dir}")
        try:
            class_configs = generate_class_configs_from_labelme(labelme_dir)
            print(f"✅ 自動生成了 {len(class_configs)} 個類別配置")
        except Exception as e:
            print(f"⚠️  LabelMe掃描失敗: {e}")
            print("   將使用預設類別配置")
    
    # ===================================================================
    # ⚙️ 創建配置對象
    # ===================================================================
    
    config = EnhancedYOLOConfig(
        # 模型配置
        detection_model_path=detection_model_path,
        segmentation_model_path=segmentation_model_path,
        device=device,
        
        # 推理配置
        img_size=img_size,
        global_conf=global_conf,
        iou_threshold=iou_threshold,
        max_det=max_det,
        
        # 類別配置
        class_configs=class_configs,
        
        # SAHI配置
        enable_sahi=enable_sahi,
        slice_height=slice_height,
        slice_width=slice_width,
        overlap_height_ratio=overlap_height_ratio,
        overlap_width_ratio=overlap_width_ratio,
        
        # 輸出配置
        save_visualizations=save_visualizations,
        save_predictions=save_predictions,
        save_statistics=save_statistics,
        
        # 進階功能
        enable_tracking=enable_tracking,
        enable_pose_estimation=enable_pose_estimation,
        enable_classification=enable_classification,
        auto_convert_annotations=True
    )
    
    # ===================================================================
    # 🚀 執行推理
    # ===================================================================
    
    print("🔧 創建推理器...")
    try:
        inference = create_enhanced_yolo_inference(config)
        print("✅ 推理器創建成功")
    except Exception as e:
        print(f"❌ 推理器創建失敗: {e}")
        return
    
    print("📊 配置信息:")
    print(f"   🎯 任務類型: {task_type}")
    print(f"   📁 輸入: {input_path}")
    print(f"   📁 輸出: {output_path}")
    print(f"   🎛️  圖像大小: {img_size}")
    print(f"   🎛️  置信度: {global_conf}")
    print(f"   🧩 SAHI: {'啟用' if enable_sahi else '禁用'}")
    if class_configs:
        print(f"   🏷️  類別數: {len(class_configs)}")
    
    print("\n🚀 開始推理...")
    
    try:
        if batch_processing and Path(input_path).is_dir():
            # 批次處理
            print(f"📂 批次處理目錄: {input_path}")
            results = inference.batch_predict(
                input_dir=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            print(f"✅ 批次處理完成! 處理了 {len(results)} 張圖像")
            
        else:
            # 單張圖像處理
            print(f"🖼️  處理單張圖像: {input_path}")
            results = inference.predict_single_image(
                image_path=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            print("✅ 單張圖像處理完成!")
        
        # 顯示統計信息
        if hasattr(inference, 'inference_stats'):
            stats = inference.inference_stats
            print("\n📊 統計信息:")
            print(f"   🎯 總檢測數: {stats.get('total_detections', 0)}")
            print(f"   📊 類別統計: {stats.get('class_counts', {})}")
            print(f"   ⏱️  處理時間: {stats.get('total_time', 0):.2f}秒")
        
        print(f"\n🎉 推理完成! 結果保存至: {output_path}")
        
    except Exception as e:
        print(f"❌ 推理失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()