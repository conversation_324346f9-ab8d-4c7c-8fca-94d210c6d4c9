#!/usr/bin/env python3
"""
🧪 預覽功能測試腳本
用於測試ROI預覽生成器的功能
"""

import sys
from pathlib import Path

# 添加項目路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 設置項目路徑
from core.import_helper import setup_project_paths
setup_project_paths()

def test_roi_preview_generator():
    """測試ROI預覽生成器"""
    try:
        print("🧪 測試ROI預覽生成器...")
        
        # 導入預覽生成器
        from models.inference.roi_preview_generator import create_roi_preview_generator
        print("✅ 成功導入ROI預覽生成器")
        
        # 創建預覽生成器實例
        preview_generator = create_roi_preview_generator()
        print("✅ 成功創建預覽生成器實例")
        
        # 測試配置
        roi_ratios = {
            'top': 3.0,
            'bottom': 2.8,
            'left': 1.3,
            'right': 1.7
        }
        
        slice_config = {
            'height': 320,
            'width': 320,
            'overlap_ratio': 0.2
        }
        
        print("✅ 配置參數準備完成")
        print(f"   ROI配置: {roi_ratios}")
        print(f"   切片配置: {slice_config}")
        
        # 測試座標計算
        test_image_shape = (1080, 1920)  # 典型的Full HD圖像
        roi_coords = preview_generator.calculate_roi_coordinates(test_image_shape, roi_ratios)
        print("✅ ROI座標計算成功")
        print(f"   計算結果: {roi_coords}")
        
        # 計算ROI區域大小
        roi_width = roi_coords['right'] - roi_coords['left']
        roi_height = roi_coords['bottom'] - roi_coords['top']
        print(f"   ROI尺寸: {roi_width} x {roi_height} px")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_paths():
    """測試導入路徑"""
    try:
        print("\n🔍 測試導入路徑...")
        
        # 測試核心導入
        from core.import_helper import setup_project_paths
        print("✅ core.import_helper 導入成功")
        
        # 測試模型目錄
        from models.inference import roi_preview_generator
        print("✅ models.inference.roi_preview_generator 導入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 導入測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 ROI預覽功能測試")
    print("=" * 50)
    
    # 測試導入路徑
    if not test_import_paths():
        print("❌ 導入路徑測試失敗，停止測試")
        return
    
    # 測試ROI預覽生成器
    if test_roi_preview_generator():
        print("\n🎉 所有測試通過！")
        print("📷 預覽功能已準備就緒")
        print("\n💡 使用方法:")
        print("   1. 設置 preview_mode = True")
        print("   2. 運行 python run_unified_yolo.py")
        print("   3. 預覽圖將保存到 output_path/preview/ 目錄")
    else:
        print("\n❌ 測試失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main()