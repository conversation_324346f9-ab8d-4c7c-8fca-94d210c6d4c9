#!/usr/bin/env python3
"""
Vision Mamba 核心實現
基於2024年ICML最佳論文的Vision Mamba架構
實現線性複雜度的視覺Transformer替代方案
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union
import numpy as np
from dataclasses import dataclass
# einops操作函數 - 提供本地實現避免依賴問題
try:
    from einops import rearrange, repeat
    EINOPS_AVAILABLE = True
except ImportError:
    EINOPS_AVAILABLE = False
    
    def rearrange(tensor, pattern, **kwargs):
        """einops rearrange的本地實現"""
        if pattern == "b l d -> b d l":
            return tensor.transpose(1, 2)
        elif pattern == "b d l -> b l d":
            return tensor.transpose(1, 2)
        else:
            raise NotImplementedError(f"Pattern {pattern} not implemented in fallback rearrange")
    
    def repeat(tensor, pattern, **kwargs):
        """einops repeat的本地實現"""
        if pattern == "n -> d n":
            d = kwargs.get('d', 1)
            return tensor.unsqueeze(0).expand(d, -1)
        else:
            raise NotImplementedError(f"Pattern {pattern} not implemented in fallback repeat")
import logging


@dataclass
class VisionMambaConfig:
    """Vision Mamba配置"""
    
    # 模型基本配置
    d_model: int = 768           # 模型維度
    d_state: int = 16            # SSM狀態維度
    d_conv: int = 4              # 卷積核大小
    expand: int = 2              # 擴展因子
    
    # 圖像配置
    img_size: int = 224          # 輸入圖像大小
    patch_size: int = 16         # 補丁大小
    in_channels: int = 3         # 輸入通道數
    num_classes: int = 1000      # 分類數量
    
    # 架構配置
    depths: list = None          # 每層深度
    dims: list = None            # 每層維度
    drop_rate: float = 0.0       # Dropout率
    attn_drop_rate: float = 0.0  # 注意力Dropout率
    drop_path_rate: float = 0.1  # DropPath率
    
    # Mamba特定配置
    dt_rank: str = "auto"        # Delta參數秩
    dt_min: float = 0.001        # Delta最小值
    dt_max: float = 0.1          # Delta最大值
    dt_init: str = "random"      # Delta初始化方式
    dt_scale: float = 1.0        # Delta縮放因子
    
    # 雙向掃描配置
    bidirectional: bool = True   # 雙向SSM
    use_fast_path: bool = True   # 使用快速路徑
    
    def __post_init__(self):
        if self.depths is None:
            self.depths = [2, 2, 9, 2]  # 類似Swin-T
        if self.dims is None:
            self.dims = [96, 192, 384, 768]  # 類似Swin-T
        
        # 自動設置dt_rank
        if self.dt_rank == "auto":
            self.dt_rank = math.ceil(self.d_model / 16)


class PatchEmbed(nn.Module):
    """
    圖像補丁嵌入
    將圖像分割為補丁並嵌入到向量空間
    """
    
    def __init__(self, img_size=224, patch_size=16, in_chans=3, embed_dim=768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.grid_size = img_size // patch_size
        self.num_patches = self.grid_size * self.grid_size
        
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)
    
    def forward(self, x):
        B, C, H, W = x.shape
        assert H == self.img_size and W == self.img_size, \
            f"Input image size ({H}*{W}) doesn't match model ({self.img_size}*{self.img_size})."
        
        x = self.proj(x)  # [B, embed_dim, grid_size, grid_size]
        x = x.flatten(2).transpose(1, 2)  # [B, num_patches, embed_dim]
        x = self.norm(x)
        return x


class SSMLayer(nn.Module):
    """
    State Space Model層
    實現選擇性狀態空間模型的核心計算
    """
    
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2, dt_rank="auto", 
                 dt_min=0.001, dt_max=0.1, dt_init="random", dt_scale=1.0,
                 bidirectional=True):
        super().__init__()
        
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        self.d_inner = int(self.expand * self.d_model)
        self.bidirectional = bidirectional
        
        if dt_rank == "auto":
            self.dt_rank = math.ceil(self.d_model / 16)
        else:
            self.dt_rank = dt_rank
        
        # 線性投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2, bias=False)
        self.conv1d = nn.Conv1d(
            in_channels=self.d_inner,
            out_channels=self.d_inner,
            bias=True,
            kernel_size=d_conv,
            groups=self.d_inner,
            padding=d_conv - 1,
        )
        
        # SSM參數
        self.x_proj = nn.Linear(self.d_inner, self.dt_rank + self.d_state * 2, bias=False)
        self.dt_proj = nn.Linear(self.dt_rank, self.d_inner, bias=True)
        
        # A參數 (結構化矩陣)
        A = repeat(
            torch.arange(1, self.d_state + 1, dtype=torch.float32),
            "n -> d n",
            d=self.d_inner,
        )
        A_log = torch.log(A)
        self.A_log = nn.Parameter(A_log)
        
        # D參數 (跳躍連接)
        self.D = nn.Parameter(torch.ones(self.d_inner))
        
        # 輸出投影
        self.out_proj = nn.Linear(self.d_inner, d_model, bias=False)
        
        # 激活函數
        self.act = nn.SiLU()
        
        # 初始化delta參數
        dt_init_std = self.dt_rank**-0.5 * dt_scale
        if dt_init == "constant":
            nn.init.constant_(self.dt_proj.weight, dt_init_std)
        elif dt_init == "random":
            nn.init.uniform_(self.dt_proj.weight, -dt_init_std, dt_init_std)
        
        # 限制delta範圍
        dt = torch.exp(
            torch.rand(self.d_inner) * (math.log(dt_max) - math.log(dt_min)) + math.log(dt_min)
        ).clamp(min=dt_min)
        inv_dt = dt + torch.log(-torch.expm1(-dt))
        with torch.no_grad():
            self.dt_proj.bias.copy_(inv_dt)
        
        # 如果是雙向，創建反向參數
        if self.bidirectional:
            self.A_log_reverse = nn.Parameter(A_log.clone())
            self.D_reverse = nn.Parameter(torch.ones(self.d_inner))
    
    def forward(self, x):
        """
        Args:
            x: (B, L, D) 輸入序列
        Returns:
            (B, L, D) 輸出序列
        """
        B, L, D = x.shape
        
        # 輸入投影
        xz = self.in_proj(x)  # (B, L, 2*d_inner)
        x, z = xz.chunk(2, dim=-1)  # 分割為x和門控信號z
        
        # 1D卷積
        x = rearrange(x, "b l d -> b d l")
        x = self.conv1d(x)[:, :, :L]  # 去除padding
        x = rearrange(x, "b d l -> b l d")
        
        # 激活
        x = self.act(x)
        
        # SSM參數
        x_dbl = self.x_proj(x)  # (B, L, dt_rank + 2*d_state)
        dt, B_proj, C_proj = torch.split(x_dbl, [self.dt_rank, self.d_state, self.d_state], dim=-1)
        
        # Delta參數
        dt = self.dt_proj(dt)  # (B, L, d_inner)
        
        # 執行SSM
        y = self.selective_scan(x, dt, self.A_log, B_proj, C_proj, self.D)
        
        # 如果是雙向，還要執行反向掃描
        if self.bidirectional:
            # 反向輸入
            x_reverse = torch.flip(x, dims=[1])
            dt_reverse = torch.flip(dt, dims=[1])
            B_proj_reverse = torch.flip(B_proj, dims=[1])
            C_proj_reverse = torch.flip(C_proj, dims=[1])
            
            # 反向SSM
            y_reverse = self.selective_scan(
                x_reverse, dt_reverse, self.A_log_reverse, 
                B_proj_reverse, C_proj_reverse, self.D_reverse
            )
            
            # 恢復順序並合併
            y_reverse = torch.flip(y_reverse, dims=[1])
            y = y + y_reverse
        
        # 門控和輸出投影
        y = y * self.act(z)
        y = self.out_proj(y)
        
        return y
    
    def selective_scan(self, u, delta, A, B, C, D):
        """
        選擇性掃描算法
        實現狀態空間模型的高效計算
        """
        B_batch, L, d_inner = u.shape
        
        # 離散化
        A = -torch.exp(A.float())  # (d_inner, d_state)
        deltaA = torch.exp(torch.einsum('bld,dn->bldn', delta, A))
        deltaB_u = torch.einsum('bld,bln,bld->bldn', delta, B, u)
        
        # 初始化狀態
        x = torch.zeros((B_batch, d_inner, self.d_state), device=u.device, dtype=u.dtype)
        ys = []
        
        # 順序掃描
        for i in range(L):
            x = deltaA[:, i] * x + deltaB_u[:, i]
            y = torch.einsum('bdn,bn->bd', x, C[:, i])
            ys.append(y)
        
        y = torch.stack(ys, dim=1)  # (B, L, d_inner)
        
        # 跳躍連接
        y = y + u * D
        
        return y


class VisionMambaBlock(nn.Module):
    """
    Vision Mamba基本塊
    包含SSM層、標準化和殘差連接
    """
    
    def __init__(self, dim, config: VisionMambaConfig, drop_path=0.0):
        super().__init__()
        
        self.norm = nn.LayerNorm(dim)
        self.ssm = SSMLayer(
            d_model=dim,
            d_state=config.d_state,
            d_conv=config.d_conv,
            expand=config.expand,
            dt_rank=config.dt_rank,
            dt_min=config.dt_min,
            dt_max=config.dt_max,
            dt_init=config.dt_init,
            dt_scale=config.dt_scale,
            bidirectional=config.bidirectional
        )
        
        # DropPath - 使用內建實現避免複雜依賴
        self.drop_path = self._create_drop_path(drop_path) if drop_path > 0.0 else nn.Identity()
    
    def forward(self, x):
        """
        Args:
            x: (B, H*W, C) 特徵圖
        Returns:
            (B, H*W, C) 輸出特徵
        """
        x = x + self.drop_path(self.ssm(self.norm(x)))
        return x
    
    def _create_drop_path(self, drop_prob):
        """創建DropPath層的內建實現"""
        class DropPath(nn.Module):
            def __init__(self, drop_prob=None):
                super(DropPath, self).__init__()
                self.drop_prob = drop_prob

            def forward(self, x):
                if self.drop_prob == 0. or not self.training:
                    return x
                keep_prob = 1 - self.drop_prob
                shape = (x.shape[0],) + (1,) * (x.ndim - 1)
                random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
                random_tensor.floor_()
                output = x.div(keep_prob) * random_tensor
                return output
        
        return DropPath(drop_prob)


class PatchMerging(nn.Module):
    """
    補丁合併層
    降低空間分辨率，增加通道維度
    """
    
    def __init__(self, input_resolution, dim):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = nn.LayerNorm(4 * dim)
    
    def forward(self, x):
        """
        Args:
            x: (B, H*W, C)
        Returns:
            (B, H*W/4, 2*C) 或處理奇數尺寸的情況
        """
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        
        x = x.view(B, H, W, C)
        
        # 處理奇數尺寸的情況 - 添加padding使其變為偶數
        if H % 2 == 1:
            # 在高度維度添加padding
            pad_h = torch.zeros(B, 1, W, C, dtype=x.dtype, device=x.device)
            x = torch.cat([x, pad_h], dim=1)
            H = H + 1
        
        if W % 2 == 1:
            # 在寬度維度添加padding
            pad_w = torch.zeros(B, H, 1, C, dtype=x.dtype, device=x.device)
            x = torch.cat([x, pad_w], dim=2)
            W = W + 1
        
        # 2x2補丁合併
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H*W/4 4*C
        
        x = self.norm(x)
        x = self.reduction(x)
        
        return x


class VisionMambaStage(nn.Module):
    """
    Vision Mamba階段
    包含多個VisionMambaBlock
    """
    
    def __init__(self, dim, input_resolution, depth, config: VisionMambaConfig, 
                 downsample=None, drop_path_rates=None):
        super().__init__()
        
        self.dim = dim
        self.input_resolution = input_resolution
        self.depth = depth
        
        # 創建blocks
        self.blocks = nn.ModuleList([
            VisionMambaBlock(
                dim=dim,
                config=config,
                drop_path=drop_path_rates[i] if drop_path_rates is not None else 0.0
            )
            for i in range(depth)
        ])
        
        # 下採樣層
        if downsample is not None:
            self.downsample = downsample(input_resolution, dim=dim)
        else:
            self.downsample = None
    
    def forward(self, x):
        """
        Args:
            x: (B, H*W, C)
        Returns:
            (B, H*W or H*W/4, C or 2*C)
        """
        for blk in self.blocks:
            x = blk(x)
        
        if self.downsample is not None:
            x = self.downsample(x)
        
        return x


class VisionMamba(nn.Module):
    """
    Vision Mamba主模型
    基於2024年ICML論文的完整實現
    """
    
    def __init__(self, config: VisionMambaConfig):
        super().__init__()
        
        self.config = config
        self.num_classes = config.num_classes
        self.num_stages = len(config.depths)
        self.embed_dim = config.dims[0]
        self.patch_norm = True
        
        # 補丁嵌入
        self.patch_embed = PatchEmbed(
            img_size=config.img_size,
            patch_size=config.patch_size,
            in_chans=config.in_channels,
            embed_dim=self.embed_dim
        )
        
        num_patches = self.patch_embed.num_patches
        patches_resolution = [config.img_size // config.patch_size] * 2
        self.patches_resolution = patches_resolution
        
        # 位置嵌入
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches, self.embed_dim))
        
        # Dropout
        self.pos_drop = nn.Dropout(p=config.drop_rate)
        
        # DropPath率
        dpr = [x.item() for x in torch.linspace(0, config.drop_path_rate, sum(config.depths))]
        
        # 構建階段
        self.stages = nn.ModuleList()
        current_resolution = patches_resolution.copy()
        
        for i_stage in range(self.num_stages):
            stage = VisionMambaStage(
                dim=int(config.dims[i_stage]),
                input_resolution=tuple(current_resolution),
                depth=config.depths[i_stage],
                config=config,
                downsample=PatchMerging if (i_stage < self.num_stages - 1) else None,
                drop_path_rates=dpr[sum(config.depths[:i_stage]):sum(config.depths[:i_stage + 1])]
            )
            self.stages.append(stage)
            
            # 更新下一階段的分辨率（考慮padding影響）
            if i_stage < self.num_stages - 1:  # 如果不是最後一階段
                current_resolution = [(r + 1) // 2 if r % 2 == 1 else r // 2 for r in current_resolution]
        
        # 分類頭
        self.norm = nn.LayerNorm(config.dims[-1])
        self.head = nn.Linear(config.dims[-1], config.num_classes) if config.num_classes > 0 else nn.Identity()
        
        # 初始化權重
        self.apply(self._init_weights)
        
    def _init_weights(self, m):
        """初始化權重"""
        if isinstance(m, nn.Linear):
            torch.nn.init.trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward_features(self, x):
        """提取特徵"""
        x = self.patch_embed(x)  # [B, num_patches, embed_dim]
        x = x + self.pos_embed
        x = self.pos_drop(x)
        
        features = []
        for stage in self.stages:
            x = stage(x)
            features.append(x)
        
        x = self.norm(x)  # [B, num_patches, embed_dim]
        return x, features
    
    def forward(self, x):
        """前向傳播"""
        x, features = self.forward_features(x)
        
        # 全局平均池化
        x = x.mean(dim=1)  # [B, embed_dim]
        
        # 分類
        x = self.head(x)
        
        return x
    
    def get_feature_maps(self, x):
        """獲取多尺度特徵圖（用於檢測/分割）"""
        x = self.patch_embed(x)
        x = x + self.pos_embed
        x = self.pos_drop(x)
        
        feature_maps = []
        current_resolution = self.patches_resolution.copy()
        
        for i, stage in enumerate(self.stages):
            x = stage(x)
            
            # 重塑為特徵圖格式
            B, L, C = x.shape
            H, W = current_resolution
            feature_map = x.view(B, H, W, C).permute(0, 3, 1, 2)  # [B, C, H, W]
            feature_maps.append(feature_map)
            
            # 更新分辨率（如果有下採樣）
            if stage.downsample is not None:
                # 考慮padding的影響，奇數尺寸會被pad成偶數再除以2
                current_resolution = [(r + 1) // 2 if r % 2 == 1 else r // 2 for r in current_resolution]
        
        return feature_maps


def create_vision_mamba_tiny(num_classes=1000, **kwargs):
    """創建Vision Mamba Tiny模型"""
    config = VisionMambaConfig(
        depths=[2, 2, 9, 2],
        dims=[96, 192, 384, 768],
        num_classes=num_classes,
        **kwargs
    )
    return VisionMamba(config)


def create_vision_mamba_small(num_classes=1000, **kwargs):
    """創建Vision Mamba Small模型"""
    config = VisionMambaConfig(
        depths=[2, 2, 27, 2],
        dims=[96, 192, 384, 768],
        num_classes=num_classes,
        **kwargs
    )
    return VisionMamba(config)


def create_vision_mamba_base(num_classes=1000, **kwargs):
    """創建Vision Mamba Base模型"""
    config = VisionMambaConfig(
        depths=[2, 2, 27, 2],
        dims=[128, 256, 512, 1024],
        num_classes=num_classes,
        **kwargs
    )
    return VisionMamba(config)


# 模型註冊
VISION_MAMBA_MODELS = {
    'vision_mamba_tiny': create_vision_mamba_tiny,
    'vision_mamba_small': create_vision_mamba_small,
    'vision_mamba_base': create_vision_mamba_base,
}