"""
統一模型格式轉換器模組

基於 convert_from_keras.py 重構，提供多種模型格式之間的轉換功能。
支援 Keras、PyTorch、ONNX 等主流格式的相互轉換。
"""

import os
import h5py
import torch
import numpy as np
import collections
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from abc import ABC, abstractmethod

# 設定日誌
logger = logging.getLogger(__name__)


class BaseModelConverter(ABC):
    """模型轉換器基類"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.skipped_layers = []
        self.converted_keys = []
        
    @abstractmethod
    def convert(self, input_path: str, output_path: str, **kwargs) -> bool:
        """執行模型轉換"""
        pass
    
    def _create_output_dir(self, output_path: str) -> None:
        """創建輸出目錄"""
        os.makedirs(os.path.dirname(os.path.abspath(output_path)) or '.', exist_ok=True)
    
    def _log_info(self, message: str) -> None:
        """日誌記錄"""
        if self.verbose:
            logger.info(message)
            print(message)


class KerasToPyTorchConverter(BaseModelConverter):
    """Keras 到 PyTorch 模型轉換器"""
    
    def __init__(self, verbose: bool = False, model_type: str = "mask_rcnn"):
        super().__init__(verbose)
        self.model_type = model_type
        self.state_dict = collections.OrderedDict()
        
        # 根據模型類型選擇映射規則
        if model_type == "mask_rcnn":
            self.replace_dict = self._get_mask_rcnn_mappings()
        elif model_type == "segmentation":
            self.replace_dict = self._get_segmentation_mappings()
        else:
            self.replace_dict = self._get_general_mappings()
            
        self.replace_exact_dict = self._get_exact_mappings()
    
    def _get_mask_rcnn_mappings(self) -> collections.OrderedDict:
        """獲取 Mask R-CNN 專用的權重映射規則"""
        return collections.OrderedDict([
            # 權重命名約定映射
            ('beta:0', 'bias'),
            ('gamma:0', 'weight'),
            ('moving_mean:0', 'running_mean'),
            ('moving_variance:0', 'running_var'),
            ('bias:0', 'bias'),
            ('kernel:0', 'weight'),
            
            # 模型架構特定映射
            ('mrcnn_mask_', 'mask.'),
            ('mrcnn_mask', 'mask.conv5'),
            ('mrcnn_class_', 'classifier.'),
            ('logits', 'linear_class'),
            ('mrcnn_bbox_fc', 'classifier.linear_bbox'),
            ('rpn_', 'rpn.'),
            ('class_raw', 'conv_class'),
            ('bbox_pred', 'conv_bbox'),
            
            # FPN 層映射
            ('bn_conv1', 'fpn.C1.1'),
            ('bn2a_branch1', 'fpn.C2.0.downsample.1'),
            ('res2a_branch1', 'fpn.C2.0.downsample.0'),
            ('bn3a_branch1', 'fpn.C3.0.downsample.1'),
            ('res3a_branch1', 'fpn.C3.0.downsample.0'),
            ('bn4a_branch1', 'fpn.C4.0.downsample.1'),
            ('res4a_branch1', 'fpn.C4.0.downsample.0'),
            ('bn5a_branch1', 'fpn.C5.0.downsample.1'),
            ('res5a_branch1', 'fpn.C5.0.downsample.0'),
            
            # FPN P 層
            ('fpn_c2p2', 'fpn.P2_conv1'),
            ('fpn_c3p3', 'fpn.P3_conv1'),
            ('fpn_c4p4', 'fpn.P4_conv1'),
            ('fpn_c5p5', 'fpn.P5_conv1'),
            ('fpn_p2', 'fpn.P2_conv2.1'),
            ('fpn_p3', 'fpn.P3_conv2.1'),
            ('fpn_p4', 'fpn.P4_conv2.1'),
            ('fpn_p5', 'fpn.P5_conv2.1'),
            
            # 額外的 FPN 映射
            ('fpn_p2add', 'fpn.P2_add'),
            ('fpn_p3add', 'fpn.P3_add'),
            ('fpn_p4add', 'fpn.P4_add'),
            ('fpn_p3upsampled', 'fpn.P3_upsampled'),
            ('fpn_p4upsampled', 'fpn.P4_upsampled'),
            ('fpn_p5upsampled', 'fpn.P5_upsampled'),
            ('fpn_p6', 'fpn.P6'),
            
            # ROI 層
            ('roi_align_classifier', 'roi_align_classifier'),
            ('roi_align_mask', 'roi_align_mask'),
        ])
    
    def _get_segmentation_mappings(self) -> collections.OrderedDict:
        """獲取分割模型的權重映射規則"""
        return collections.OrderedDict([
            # 基本權重映射
            ('beta:0', 'bias'),
            ('gamma:0', 'weight'),
            ('moving_mean:0', 'running_mean'),
            ('moving_variance:0', 'running_var'),
            ('bias:0', 'bias'),
            ('kernel:0', 'weight'),
            
            # 分割模型特定映射
            ('decoder_', 'decoder.'),
            ('encoder_', 'encoder.'),
            ('upsampling_', 'upsample.'),
            ('conv2d_transpose_', 'conv_transpose.'),
            ('batch_normalization_', 'bn.'),
        ])
    
    def _get_general_mappings(self) -> collections.OrderedDict:
        """獲取通用的權重映射規則"""
        return collections.OrderedDict([
            ('beta:0', 'bias'),
            ('gamma:0', 'weight'),
            ('moving_mean:0', 'running_mean'),
            ('moving_variance:0', 'running_var'),
            ('bias:0', 'bias'),
            ('kernel:0', 'weight'),
        ])
    
    def _get_exact_mappings(self) -> collections.OrderedDict:
        """獲取精確匹配的映射規則"""
        return collections.OrderedDict([
            ('conv1.bias', 'fpn.C1.0.bias'),
            ('conv1.weight', 'fpn.C1.0.weight'),
        ])
    
    def _generate_resnet_mappings(self) -> None:
        """生成 ResNet 塊映射"""
        alphabet = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z']
        
        # C2 塊 (3 塊)
        for block in range(3):
            for branch in range(3):
                self.replace_dict[f'bn2{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C2.{block}.bn{branch+1}'
                self.replace_dict[f'res2{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C2.{block}.conv{branch+1}'
        
        # C3 塊 (4 塊)
        for block in range(4):
            for branch in range(3):
                self.replace_dict[f'bn3{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C3.{block}.bn{branch+1}'
                self.replace_dict[f'res3{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C3.{block}.conv{branch+1}'
        
        # C4 塊 (23 塊)
        for block in range(23):
            for branch in range(3):
                self.replace_dict[f'bn4{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C4.{block}.bn{branch+1}'
                self.replace_dict[f'res4{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C4.{block}.conv{branch+1}'
        
        # C5 塊 (3 塊)
        for block in range(3):
            for branch in range(3):
                self.replace_dict[f'bn5{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C5.{block}.bn{branch+1}'
                self.replace_dict[f'res5{alphabet[block]}_branch2{alphabet[branch]}'] = f'fpn.C5.{block}.conv{branch+1}'
    
    def _load_keras_model(self, keras_path: str) -> h5py.File:
        """載入 Keras 模型"""
        if not os.path.exists(keras_path):
            raise FileNotFoundError(f"Keras model file not found: {keras_path}")
        
        self._log_info(f"Loading Keras model: {keras_path}")
        return h5py.File(keras_path, mode='r')
    
    def _extract_weights(self, h5_file: h5py.File) -> None:
        """從 HDF5 文件中提取權重"""
        self.state_dict.clear()
        self.skipped_layers.clear()
        
        def visitor_func(name, obj):
            if isinstance(obj, h5py.Dataset):
                self._process_dataset(name, obj)
        
        h5_file.visititems(visitor_func)
        self._log_info(f"Loaded {len(self.state_dict)} weight tensors from Keras model")
    
    def _process_dataset(self, name: str, dataset: h5py.Dataset) -> bool:
        """處理 HDF5 數據集並提取權重"""
        self._log_info(f"Processing dataset: {name}")
        try:
            self.state_dict[name] = np.array(dataset)
            return True
        except Exception as e:
            logger.error(f"Error loading dataset {name}: {e}")
            self.skipped_layers.append(name)
            return False
    
    def _convert_layer_names(self) -> None:
        """根據 PyTorch 命名約定轉換層名"""
        if self.model_type == "mask_rcnn":
            self._generate_resnet_mappings()
        
        self.converted_keys.clear()
        not_converted = []
        
        # 應用替換規則
        for orig, repl in self.replace_dict.items():
            for key in list(self.state_dict.keys()):
                if orig in key:
                    new_key = key.replace(orig, repl)
                    self.state_dict[new_key] = self.state_dict[key]
                    self.converted_keys.append((key, new_key))
                    del self.state_dict[key]
        
        # 處理精確匹配
        for orig, repl in self.replace_exact_dict.items():
            for key in list(self.state_dict.keys()):
                if orig == key:
                    self.state_dict[repl] = self.state_dict[key]
                    self.converted_keys.append((key, repl))
                    del self.state_dict[key]
        
        # 檢查未轉換的鍵
        for key in list(self.state_dict.keys()):
            if ':0' in key:
                not_converted.append(key)
        
        if not_converted and self.verbose:
            logger.warning(f"WARNING: {len(not_converted)} weights were not converted:")
            for key in not_converted[:10]:
                logger.warning(f"  - {key}")
    
    def _transpose_weights(self) -> None:
        """轉置權重以匹配 PyTorch 約定"""
        for weight_name in list(self.state_dict.keys()):
            # 卷積層: Keras (h, w, in_c, out_c) -> PyTorch (out_c, in_c, h, w)
            if self.state_dict[weight_name].ndim == 4:
                self.state_dict[weight_name] = self.state_dict[weight_name].transpose((3, 2, 0, 1)).copy(order='C')
            # 全連接層: Keras (in_features, out_features) -> PyTorch (out_features, in_features)
            elif self.state_dict[weight_name].ndim == 2:
                self.state_dict[weight_name] = self.state_dict[weight_name].transpose((1, 0)).copy(order='C')
    
    def _convert_to_tensors(self) -> None:
        """將 numpy 數組轉換為 PyTorch 張量"""
        for weight_name in list(self.state_dict.keys()):
            if isinstance(self.state_dict[weight_name], np.ndarray):
                self.state_dict[weight_name] = torch.from_numpy(self.state_dict[weight_name])
            elif not isinstance(self.state_dict[weight_name], torch.Tensor):
                try:
                    self.state_dict[weight_name] = torch.tensor(self.state_dict[weight_name])
                except Exception as e:
                    logger.error(f"Could not convert {weight_name} to tensor: {e}")
                    self.skipped_layers.append(weight_name)
                    del self.state_dict[weight_name]
    
    def convert(self, input_path: str, output_path: str, **kwargs) -> bool:
        """執行 Keras 到 PyTorch 的轉換"""
        try:
            self._create_output_dir(output_path)
            
            # 載入 Keras 模型
            h5_file = self._load_keras_model(input_path)
            
            # 提取權重
            self._extract_weights(h5_file)
            h5_file.close()
            
            # 轉換層名
            self._convert_layer_names()
            
            # 轉置權重
            self._transpose_weights()
            
            # 轉換為張量
            self._convert_to_tensors()
            
            # 保存 PyTorch 模型
            torch.save(self.state_dict, output_path)
            
            logger.info(f"Conversion completed. Total parameters: {len(self.state_dict)}")
            logger.info(f"PyTorch model saved to {output_path}")
            
            if self.verbose:
                self._print_conversion_report()
            
            return True
            
        except Exception as e:
            logger.error(f"Error during conversion: {e}")
            return False
    
    def _print_conversion_report(self) -> None:
        """打印詳細的轉換報告"""
        print("\nDetailed conversion report:")
        print(f"Total keys converted: {len(self.converted_keys)}")
        print(f"Skipped layers due to errors: {len(self.skipped_layers)}")
        
        # 打印樣本轉換
        if self.converted_keys:
            print("\nSample conversions:")
            for i, (old, new) in enumerate(self.converted_keys[:10]):
                print(f"  {old} -> {new}")
            if len(self.converted_keys) > 10:
                print(f"  ... and {len(self.converted_keys)-10} more")
        
        # 打印跳過的層
        if self.skipped_layers:
            print("\nSkipped layers due to errors:")
            for i, layer in enumerate(self.skipped_layers[:10]):
                print(f"  - {layer}")
            if len(self.skipped_layers) > 10:
                print(f"  ... and {len(self.skipped_layers)-10} more")


class PyTorchToONNXConverter(BaseModelConverter):
    """PyTorch 到 ONNX 模型轉換器"""
    
    def __init__(self, verbose: bool = False):
        super().__init__(verbose)
    
    def convert(self, input_path: str, output_path: str, 
                input_shape: Tuple[int, ...] = (1, 3, 224, 224),
                **kwargs) -> bool:
        """執行 PyTorch 到 ONNX 的轉換"""
        try:
            import torch.onnx
            
            self._create_output_dir(output_path)
            
            # 載入 PyTorch 模型
            model = torch.load(input_path, map_location='cpu')
            if isinstance(model, dict):
                # 如果是 state_dict，需要模型架構
                raise ValueError("Cannot convert state_dict without model architecture")
            
            model.eval()
            
            # 創建虛擬輸入
            dummy_input = torch.randn(*input_shape)
            
            # 導出到 ONNX
            torch.onnx.export(
                model, 
                dummy_input, 
                output_path,
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
            )
            
            logger.info(f"ONNX model saved to {output_path}")
            return True
            
        except ImportError:
            logger.error("torch.onnx not available. Please install PyTorch with ONNX support.")
            return False
        except Exception as e:
            logger.error(f"Error during PyTorch to ONNX conversion: {e}")
            return False


class ModelConverterFactory:
    """模型轉換器工廠"""
    
    _converters = {
        'keras_to_pytorch': KerasToPyTorchConverter,
        'pytorch_to_onnx': PyTorchToONNXConverter,
    }
    
    @classmethod
    def create_converter(cls, converter_type: str, **kwargs) -> BaseModelConverter:
        """創建模型轉換器"""
        if converter_type not in cls._converters:
            raise ValueError(f"Unsupported converter type: {converter_type}")
        
        return cls._converters[converter_type](**kwargs)
    
    @classmethod
    def register_converter(cls, name: str, converter_class: type) -> None:
        """註冊新的轉換器"""
        cls._converters[name] = converter_class
    
    @classmethod
    def list_converters(cls) -> List[str]:
        """列出所有可用的轉換器"""
        return list(cls._converters.keys())


def create_keras_to_pytorch_converter(verbose: bool = False, 
                                    model_type: str = "mask_rcnn") -> KerasToPyTorchConverter:
    """創建 Keras 到 PyTorch 轉換器的工廠函數"""
    return KerasToPyTorchConverter(verbose=verbose, model_type=model_type)


def create_pytorch_to_onnx_converter(verbose: bool = False) -> PyTorchToONNXConverter:
    """創建 PyTorch 到 ONNX 轉換器的工廠函數"""
    return PyTorchToONNXConverter(verbose=verbose)


def convert_model(input_path: str, output_path: str, 
                 converter_type: str, **kwargs) -> bool:
    """統一的模型轉換函數"""
    converter = ModelConverterFactory.create_converter(converter_type, **kwargs)
    return converter.convert(input_path, output_path, **kwargs)


# 向後兼容的函數
def convert_keras_to_pytorch(keras_path: str, pytorch_path: str, 
                           verbose: bool = False, model_type: str = "mask_rcnn") -> bool:
    """向後兼容的 Keras 到 PyTorch 轉換函數"""
    converter = create_keras_to_pytorch_converter(verbose=verbose, model_type=model_type)
    return converter.convert(keras_path, pytorch_path)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert between different model formats')
    parser.add_argument('--input', required=True, help='Input model path')
    parser.add_argument('--output', required=True, help='Output model path')
    parser.add_argument('--type', required=True, choices=ModelConverterFactory.list_converters(),
                       help='Conversion type')
    parser.add_argument('--verbose', action='store_true', help='Print detailed information')
    parser.add_argument('--model_type', default='mask_rcnn', 
                       choices=['mask_rcnn', 'segmentation', 'general'],
                       help='Model type for Keras to PyTorch conversion')
    
    args = parser.parse_args()
    
    success = convert_model(
        input_path=args.input,
        output_path=args.output,
        converter_type=args.type,
        verbose=args.verbose,
        model_type=args.model_type
    )
    
    if success:
        print("Conversion completed successfully!")
    else:
        print("Conversion failed!")
        exit(1)