import math
import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Optional

class PCICalculator:
    """簡易型柔性鋪面PCI計算系統 - 支援長寬面積輸入"""
    
    def __init__(self, sample_area: float = 300.0):
        """
        初始化PCI計算器
        
        Args:
            sample_area: 樣本單位面積，預設300平方公尺
        """
        self.sample_area = sample_area
        
        # DV值係數表 (根據PDF文檔表3)
        self.dv_coefficients = {
            # 龜裂 (單位：m²)
            '1L': {'coeff1': 11.1, 'coeff2': 16.27, 'coeff3': 7.34, 'coeff4': -1.45},
            '1M': {'coeff1': 21.8, 'coeff2': 20.92, 'coeff3': 4.68, 'coeff4': -0.55},
            '1H': {'coeff1': 31.0, 'coeff2': 27.17, 'coeff3': 6.46, 'coeff4': -2.5},
            
            # 縱向及橫向裂縫 (單位：m)
            '2L': {'coeff1': -1.7, 'coeff2': 4.45, 'coeff3': 5.18, 'coeff4': 0},
            '2M': {'coeff1': 2.1, 'coeff2': 11.51, 'coeff3': 4.93, 'coeff4': 0},
            '2H': {'coeff1': 8.3, 'coeff2': 14.06, 'coeff3': 12.96, 'coeff4': 0},
            
            # 坑洞 (單位：m)
            '3L': {'coeff1': 21.2, 'coeff2': 27.15, 'coeff3': 6.41, 'coeff4': 0},
            '3M': {'coeff1': 31.4, 'coeff2': 40.77, 'coeff3': 14.14, 'coeff4': 0},
            '3H': {'coeff1': 52.3, 'coeff2': 43.87, 'coeff3': 10.22, 'coeff4': 0},
            
            # 補綻
            '4L': {'coeff1': 2.1, 'coeff2': 8.06, 'coeff3': 6.15, 'coeff4': 0},
            '4M': {'coeff1': 10.0, 'coeff2': 12.71, 'coeff3': 6.97, 'coeff4': 1.3},
            '4H': {'coeff1': 19.4, 'coeff2': 22.44, 'coeff3': 0, 'coeff4': 9},
            
            # 變形
            '5L': {'coeff1': 2.0, 'coeff2': 4.76, 'coeff3': 4.95, 'coeff4': 1},
            '5M': {'coeff1': 15.0, 'coeff2': 17.16, 'coeff3': 6.25, 'coeff4': 0},
            '5H': {'coeff1': 33.6, 'coeff2': 25.19, 'coeff3': 2.62, 'coeff4': 0},
        }
        
        # 鋪面狀況等級對照表
        self.rating_scale = [
            (85, 100, "令人滿意(Good)"),
            (70, 85, "良好(Satisfactory)"),
            (55, 70, "尚可(Fair)"),
            (40, 55, "差(Poor)"),
            (25, 40, "很差(Very Poor)"),
            (10, 25, "嚴重(Serious)"),
            (0, 10, "不及格(Failed)")
        ]
        
        # 損壞類型名稱對照
        self.damage_types = {
            1: '龜裂',
            2: '縱向及橫向裂縫', 
            3: '坑洞',
            4: '修補、修補變壞或管線回填',
            5: '變形'
        }
        
        # 嚴重程度對照
        self.severity_levels = {
            'L': '輕級',
            'M': '中級',
            'H': '重級'
        }

    def determine_severity_and_quantity(self, length: float, width: float, area: float, damage_type: int) -> Tuple[str, float]:
        """
        根據長寬面積和損壞類型自動判定嚴重程度和計算數量
        
        Args:
            length: 長度 (m)
            width: 寬度 (m)  
            area: 面積 (m²)
            damage_type: 損壞類型 (1-5)
            
        Returns:
            (損壞類型代碼, 計算數量)
        """
        if damage_type == 1:  # 龜裂 (按面積分級，數量用面積)
            if area < 2:
                severity = 'L'
            elif area <= 5:
                severity = 'M'
            else:
                severity = 'H'
            quantity = area
            
        elif damage_type == 2:  # 縱向及橫向裂縫 (按裂縫寬度分級，數量用長度)
            crack_width = width  # 裂縫寬度決定嚴重程度
            if crack_width < 0.06:
                severity = 'L'
            elif crack_width <= 0.2:
                severity = 'M'
            else:
                severity = 'H'
            quantity = length  # 裂縫以長度計算數量
            
        elif damage_type == 3:  # 坑洞 (按長寬分級，數量用長度)
            max_dimension = max(length, width)
            if max_dimension < 0.1:
                severity = 'L'
            elif max_dimension <= 0.2:
                severity = 'M'
            else:
                severity = 'H'
            quantity = max_dimension  # 坑洞以最大長寬計算
            
        elif damage_type == 4:  # 補綻 (固定M級，數量用面積)
            severity = 'M'
            quantity = area
            
        elif damage_type == 5:  # 變形 (按面積分級，數量用面積)
            if area < 2:
                severity = 'L'
            elif area <= 5:
                severity = 'M'
            else:
                severity = 'H'
            quantity = area
            
        else:
            raise ValueError(f"無效的損壞類型: {damage_type}，應為1-5之間")
        
        damage_code = f"{damage_type}{severity}"
        return damage_code, quantity

    def calculate_density(self, quantity: float) -> float:
        """
        計算損壞密度
        
        Args:
            quantity: 損壞數量
            
        Returns:
            密度百分比
        """
        return (quantity / self.sample_area) * 100

    def calculate_dv(self, damage_code: str, quantity: float) -> float:
        """
        計算折減值(DV)
        
        Args:
            damage_code: 損壞類型代碼 (如: '2H', '1M')
            quantity: 損壞數量
            
        Returns:
            折減值
        """
        if damage_code not in self.dv_coefficients:
            raise ValueError(f"未知的損壞類型: {damage_code}")
        
        # 計算密度
        density = self.calculate_density(quantity)
        
        # 計算log(密度)
        if density <= 0:
            return 0
        
        x = math.log10(density)
        
        # 取得係數
        coeffs = self.dv_coefficients[damage_code]
        
        # 計算DV = Coeff1 + Coeff2*x + Coeff3*x² + Coeff4*x³
        dv = (coeffs['coeff1'] + 
              coeffs['coeff2'] * x + 
              coeffs['coeff3'] * x**2 + 
              coeffs['coeff4'] * x**3)
        
        return max(0, dv)  # DV不能為負值

    def calculate_m_value(self, hdv: float) -> int:
        """
        計算最大容許損壞折減值數量(m)
        
        Args:
            hdv: 最大的折減值
            
        Returns:
            m值 (向上取整)
        """
        m = 1 + (9/98) * (100 - hdv)
        return min(10, math.ceil(m))  # 最多只能選取10個折減值

    def calculate_cdv(self, total: float, q: int) -> float:
        """
        計算修正折減值(CDV)
        
        Args:
            total: 折減值總和
            q: 大於2.0的折減值個數
            
        Returns:
            修正折減值
        """
        if q == 1:
            return total
        elif q == 2:
            return -3.6 + 0.91 * total - 0.0017 * total**2
        elif q == 3:
            return -6.4 + 0.82 * total - 0.0013 * total**2
        elif q == 4:
            return -13 + 0.86 * total - 0.0015 * total**2
        elif q == 5:
            return -12 + 0.76 * total - 0.0011 * total**2
        elif q == 6:
            return -14.7 + 0.75 * total - 0.0011 * total**2
        elif q == 7:
            return -18.5 + 0.86 * total - 0.0018 * total**2
        else:
            # 對於q>7的情況，使用q=7的公式
            return -18.5 + 0.86 * total - 0.0018 * total**2

    def get_rating(self, pci: float) -> str:
        """
        根據PCI值獲取鋪面狀況等級
        
        Args:
            pci: PCI值
            
        Returns:
            狀況等級
        """
        for min_val, max_val, rating in self.rating_scale:
            if min_val <= pci <= max_val:
                return rating
        return "未知等級"

    def calculate_pci_from_measurements(self, damage_measurements: List[Dict]) -> Dict:
        """
        根據實地測量數據計算PCI指標
        
        Args:
            damage_measurements: 損壞測量數據列表，每個元素包含:
                {
                    'length': 長度(m),
                    'width': 寬度(m), 
                    'area': 面積(m²),
                    'damage_type': 損壞類型(1-5)
                }
            
        Returns:
            計算結果字典
        """
        # Step 1: 處理測量數據，自動分級並合併同類型損壞
        damage_summary = {}
        measurement_details = []
        
        for measurement in damage_measurements:
            length = measurement.get('length', 0)
            width = measurement.get('width', 0)
            area = measurement.get('area', 0)
            damage_type = measurement['damage_type']
            
            # 如果沒有提供面積但有長寬，自動計算面積
            if area == 0 and length > 0 and width > 0:
                area = length * width
            
            # 自動判定嚴重程度和數量
            damage_code, quantity = self.determine_severity_and_quantity(
                length, width, area, damage_type
            )
            
            # 記錄測量詳情
            measurement_details.append({
                'length': length,
                'width': width,
                'area': area,
                'damage_type': damage_type,
                'damage_name': self.damage_types[damage_type],
                'damage_code': damage_code,
                'severity': self.severity_levels[damage_code[-1]],
                'quantity': quantity
            })
            
            # 合併同類型損壞
            if damage_code in damage_summary:
                damage_summary[damage_code] += quantity
            else:
                damage_summary[damage_code] = quantity
        
        # Step 2: 計算各項折減值
        dv_list = []
        damage_details = []
        
        for damage_code, total_quantity in damage_summary.items():
            dv = self.calculate_dv(damage_code, total_quantity)
            density = self.calculate_density(total_quantity)
            
            dv_list.append(dv)
            damage_details.append({
                'damage_code': damage_code,
                'damage_name': f"{self.damage_types[int(damage_code[0])]}-{self.severity_levels[damage_code[1]]}",
                'total_quantity': total_quantity,
                'density': density,
                'dv': dv
            })
        
        # 按DV值從大到小排序
        dv_list.sort(reverse=True)
        
        if not dv_list:
            return {
                'pci': 100,
                'rating': self.get_rating(100),
                'max_cdv': 0,
                'measurement_details': measurement_details,
                'damage_summary': damage_details,
                'cdv_calculation': []
            }
        
        # Step 3: 計算m值
        hdv = max(dv_list)
        m = self.calculate_m_value(hdv)
        
        # Step 4: 取前m個或全部DV值
        selected_dv = dv_list[:min(m, len(dv_list))]
        
        # Step 5: 計算CDV
        cdv_calculations = []
        max_cdv = 0
        
        # 初始計算
        current_dv = selected_dv.copy()
        
        while True:
            # 計算大於2.0的DV個數
            q = sum(1 for dv in current_dv if dv > 2.0)
            if q == 0:
                break
                
            # 計算總和
            total = sum(current_dv)
            
            # 計算CDV
            cdv = self.calculate_cdv(total, q)
            max_cdv = max(max_cdv, cdv)
            
            cdv_calculations.append({
                'dv_values': current_dv.copy(),
                'total': total,
                'q': q,
                'cdv': cdv
            })
            
            if q == 1:
                break
                
            # 將最後一個大於2.0的值替換為2.0
            for i in range(len(current_dv) - 1, -1, -1):
                if current_dv[i] > 2.0:
                    current_dv[i] = 2.0
                    break
        
        # Step 6: 計算最終PCI
        pci = 100 - max_cdv
        rating = self.get_rating(pci)
        
        return {
            'pci': round(pci, 1),
            'rating': rating,
            'max_cdv': round(max_cdv, 1),
            'hdv': round(hdv, 1),
            'm_value': m,
            'measurement_details': measurement_details,
            'damage_summary': damage_details,
            'cdv_calculation': cdv_calculations
        }

    def create_measurement_table(self, damage_measurements: List[Dict]) -> pd.DataFrame:
        """
        建立測量數據表
        
        Args:
            damage_measurements: 損壞測量數據列表
            
        Returns:
            測量數據表DataFrame
        """
        table_data = []
        
        for i, measurement in enumerate(damage_measurements, 1):
            length = measurement.get('length', 0)
            width = measurement.get('width', 0)
            area = measurement.get('area', 0)
            damage_type = measurement['damage_type']
            
            # 自動計算面積（如果未提供）
            if area == 0 and length > 0 and width > 0:
                area = length * width
            
            # 判定損壞等級
            damage_code, quantity = self.determine_severity_and_quantity(
                length, width, area, damage_type
            )
            
            table_data.append({
                '序號': i,
                '損壞類型': self.damage_types[damage_type],
                '長度(m)': length if length > 0 else '-',
                '寬度(m)': width if width > 0 else '-',
                '面積(m²)': round(area, 2) if area > 0 else '-',
                '嚴重程度': self.severity_levels[damage_code[-1]],
                '損壞代碼': damage_code,
                '計算數量': round(quantity, 2)
            })
        
        return pd.DataFrame(table_data)

    def print_measurement_report(self, damage_measurements: List[Dict]):
        """
        印出完整的測量計算報告
        
        Args:
            damage_measurements: 損壞測量數據列表
        """
        result = self.calculate_pci_from_measurements(damage_measurements)
        
        print("=" * 100)
        print("簡易型柔性鋪面PCI計算報告 (基於實地測量)")
        print("=" * 100)
        
        print(f"\n樣本單位面積: {self.sample_area} m²")
        
        print(f"\n一、實地測量數據:")
        measurement_table = self.create_measurement_table(damage_measurements)
        print(measurement_table.to_string(index=False))
        
        print(f"\n二、損壞類型統計匯總:")
        if result['damage_summary']:
            summary_data = []
            for detail in result['damage_summary']:
                summary_data.append({
                    '損壞類型代碼': detail['damage_code'],
                    '損壞名稱': detail['damage_name'],
                    '總計數量': round(detail['total_quantity'], 2),
                    '密度(%)': round(detail['density'], 2),
                    '折減值(DV)': round(detail['dv'], 1)
                })
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))
        
        print(f"\n三、計算參數:")
        print(f"最大折減值(HDV): {result['hdv']}")
        print(f"最大容許損壞折減值數量(m): {result['m_value']}")
        
        print(f"\n四、修正折減值(CDV)計算過程:")
        for i, calc in enumerate(result['cdv_calculation'], 1):
            dv_str = ' + '.join([f"{dv:.1f}" for dv in calc['dv_values']])
            print(f"第{i}次計算: [{dv_str}] = {calc['total']:.1f}, q={calc['q']}, CDV={calc['cdv']:.1f}")
        
        print(f"\n五、最終結果:")
        print(f"最大修正折減值: {result['max_cdv']}")
        print(f"簡易型PCI指標: {result['pci']}")
        print(f"鋪面狀況等級: {result['rating']}")
        
        print("=" * 100)

    def print_grading_standards(self):
        """印出損壞分級標準"""
        print("=" * 80)
        print("損壞分級標準")
        print("=" * 80)
        
        print("\n損壞類型代碼:")
        for code, name in self.damage_types.items():
            print(f"{code}. {name}")
        
        print(f"\n嚴重程度等級:")
        for code, name in self.severity_levels.items():
            print(f"{code}級: {name}")
        
        print(f"\n分級標準:")
        standards = [
            ("縱橫向裂縫", "長寬<0.06m", "0.06m≤長寬≤0.2m", "0.2m<長寬", "長度(m)"),
            ("龜裂", "面積<2m²", "2m²≤面積≤5m²", "5m²<面積", "面積(m²)"),
            ("坑洞", "長寬<0.1m", "0.1m≤長寬≤0.2m", "0.2m<長寬", "長度(m)"),
            ("補綻", "-", "固定M級", "-", "面積(m²)"),
            ("變形", "面積<2m²", "2m²≤面積≤5m²", "5m²<面積", "面積(m²)")
        ]
        
        df = pd.DataFrame(standards, columns=['損壞類型', 'L級', 'M級', 'H級', '計算單位'])
        print(df.to_string(index=False))
        print("=" * 80)


# 使用範例
if __name__ == "__main__":
    # 建立PCI計算器
    calculator = PCICalculator()
    
    # 顯示分級標準
    calculator.print_grading_standards()
    
    print("\n\n")
    
    # 範例測量數據
    example_measurements = [
        {
            'length': 25.0,      # 25m長的裂縫
            'width': 0.15,       # 0.15m寬 -> M級縱橫向裂縫
            'area': 0,           # 裂縫不用面積
            'damage_type': 2     # 縱橫向裂縫
        },
        {
            'length': 3.0,       # 龜裂區域
            'width': 2.5,        # 
            'area': 7.5,         # 7.5m² -> H級龜裂
            'damage_type': 1     # 龜裂
        },
        {
            'length': 0.8,       # 坑洞
            'width': 0.15,       # 0.15m -> M級坑洞
            'area': 0,
            'damage_type': 3     # 坑洞
        },
        {
            'length': 2.0,       # 補綻區域
            'width': 1.5,
            'area': 3.0,         # 3.0m² 補綻(固定M級)
            'damage_type': 4     # 補綻
        }
    ]
    
    # 計算並顯示報告
    calculator.print_measurement_report(example_measurements)
    
    print("\n\n驗證範例 - 對照Excel數據:")
    # 驗證Excel範例數據
    excel_verification = [
        {
            'length': 5.1,       # 對應Excel中密度1.7%的縱橫向裂縫
            'width': 0.25,       # 0.25m -> H級
            'area': 0,
            'damage_type': 2
        },
        {
            'length': 0,
            'width': 0,
            'area': 7.5,         # 對應Excel中密度2.5%的龜裂 -> M級
            'damage_type': 1
        }
    ]
    
    calculator.print_measurement_report(excel_verification)
