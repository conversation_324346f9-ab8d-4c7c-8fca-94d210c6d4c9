"""
資料前處理共享模組

提供統一的基礎架構，包括：
- BaseTool: 所有工具的基類
- ConfigManager: 統一配置管理
- 文件處理工具
- 異常處理體系
- 日誌工具
"""

from .base_tool import BaseTool
from .config_manager import ConfigManager, BaseConfig
from .exceptions import (
    ProcessingError, ConfigError, ValidationError, 
    ConversionError, FileOperationError
)
from .file_utils import FileUtils, PathUtils
from .logger_utils import StructuredLogger, setup_logger

__all__ = [
    'BaseTool',
    'ConfigManager', 'BaseConfig',
    'ProcessingError', 'ConfigError', 'ValidationError', 
    'ConversionError', 'FileOperationError',
    'FileUtils', 'PathUtils',
    'StructuredLogger', 'setup_logger'
]

__version__ = '1.0.0'