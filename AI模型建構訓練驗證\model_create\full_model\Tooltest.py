import os 
import cv2
import json
import numpy as np
def ReadConfig(config_path): #parameter .cfg
    fdict = {}
    with open(config_path,encoding="utf8") as f:
        key_value = [i.strip().split(',') for i in f.readlines()]
    for i in key_value:
        key_ = i[0]
        value_ = i[1:]
        fdict.update({key_: value_})
    return(fdict)

def MakeMatrix(class_names):
    matrix = dict()
    for i in class_names:
        matrix[i]={}
        for j in class_names:
            matrix[i][j]=0
    return matrix

def ImageResizeShape(image,image_target_size:float):
    output_shape = tuple()
    w,h = image.shape[0], image.shape[1]
    m = max(w, h)
    ratio = image_target_size / m
    new_w, new_h = int(ratio * w), int(ratio *h)
    assert new_w > 0 and new_h > 0
    output_shape = (new_w, new_h)
    return output_shape

def ImageResizeRatio(image,image_target_size:float):
    w,h = image.shape[0], image.shape[1]
    m = max(w, h)
    ratio = image_target_size / m
    return ratio

# def Silhouette()

def TrainingDataset_Silhouette(img_name,train_img_extension:str,img_path,json_path,class_names):
    i_path = os.path.join(img_path, img_name[:-3]+train_img_extension)
    j_path = os.path.join(json_path, img_name[:-3]+"json")
    silhouette = dict()
    
    if os.path.exists(j_path):
        image = cv2.imread(i_path)
        # 建立 class 黑底
        for i in class_names[:]:
            silhouette[i] = np.zeros((image.shape[0], image.shape[1])) 
        with open(j_path, 'r') as reader:
            data = json.loads(reader.read())
            for i in range(0,len(data["shapes"])):
                label = data["shapes"][i]["label"]
                # 確認是否有此類別
                if label in class_names:
                    points = np.array(data["shapes"][i]["points"],dtype=np.int32)
                    # points = points*ratio
                    points = points.astype(int)
                    # 依照class 在黑底畫上 灰色
                    cv2.fillPoly(silhouette[label],[points],255) 
                    # plt.axis('off')
                    # plt.imshow(imageBlack, 'gray')
                    # plt.show()
    return silhouette

def tensor2np(predictions):
        boxes = predictions.pred_boxes if predictions.has("pred_boxes") else None
        scores = predictions.scores if predictions.has("scores") else None
        classes = predictions.pred_classes if predictions.has("pred_classes") else None
        
        if boxes is not None:
            boxes = boxes.tensor.numpy()
        if predictions.has("pred_masks"):
            masks = np.asarray(predictions.pred_masks)
        else:
            masks = None
        if classes is not None:
            classes = classes.data.numpy()
        if scores is not None:
            scores = scores.data.numpy()
        

        output = dict()
        output["boxes"] = boxes
        output["masks"] = masks
        output["classes"] = classes
        output["scores"] = scores

        return output

def str2bool(v):
  return v.lower() in ("yes", "true", "t", "y")