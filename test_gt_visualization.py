#!/usr/bin/env python3
"""
測試GT可視化問題
"""

import sys
from pathlib import Path
import json

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

def create_test_data():
    """創建測試數據"""
    print("🛠️  創建測試數據...")
    
    # 創建測試目錄
    test_dir = Path("test_gt_data")
    test_dir.mkdir(exist_ok=True)
    
    image_dir = test_dir / "image"
    label_dir = test_dir / "label"
    output_dir = test_dir / "output"
    
    image_dir.mkdir(exist_ok=True)
    label_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)
    
    # 創建測試標註文件
    test_annotation = {
        "version": "5.0.1",
        "flags": {},
        "shapes": [
            {
                "label": "linear",
                "points": [[50, 60], [200, 120]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            },
            {
                "label": "alligator",
                "points": [[100, 150], [250, 200]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            }
        ],
        "imagePath": "test_image.jpg",
        "imageData": None,
        "imageHeight": 480,
        "imageWidth": 640
    }
    
    # 保存標註文件
    annotation_path = label_dir / "test_image.json"
    with open(annotation_path, 'w', encoding='utf-8') as f:
        json.dump(test_annotation, f, indent=2, ensure_ascii=False)
    
    # 創建簡單的測試圖像（純色）
    try:
        import numpy as np
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 128  # 灰色圖像
        
        # 添加一些簡單的標記
        test_image[50:120, 50:200] = [255, 0, 0]  # 紅色區域 - linear
        test_image[150:200, 100:250] = [0, 255, 0]  # 綠色區域 - alligator
        
        # 使用PIL保存圖像
        try:
            from PIL import Image
            pil_image = Image.fromarray(test_image)
            image_path = image_dir / "test_image.jpg"
            pil_image.save(image_path)
            print(f"✅ 創建測試圖像: {image_path}")
        except ImportError:
            print("⚠️  PIL不可用，跳過圖像創建")
            # 創建空文件作為佔位符
            image_path = image_dir / "test_image.jpg"
            image_path.touch()
            
    except ImportError:
        print("⚠️  numpy不可用，創建佔位符圖像")
        image_path = image_dir / "test_image.jpg"
        image_path.touch()
    
    print(f"✅ 創建測試標註: {annotation_path}")
    
    return {
        'image_path': str(image_path),
        'annotation_path': str(annotation_path),
        'output_dir': str(output_dir)
    }

def test_gt_loading():
    """測試GT載入功能"""
    print("🧪 測試GT載入功能")
    print("=" * 40)
    
    if not ENHANCED_YOLO_AVAILABLE:
        print("❌ Enhanced YOLO模組不可用")
        return False
    
    # 創建測試數據
    test_data = create_test_data()
    
    try:
        from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
            EnhancedYOLOConfig, 
            create_enhanced_yolo_inference,
            ClassConfig
        )
        
        # 創建類別配置
        class_configs = {
            0: ClassConfig(name="linear", conf_threshold=0.5, color=(255, 0, 0), enabled=True),
            1: ClassConfig(name="alligator", conf_threshold=0.5, color=(0, 255, 0), enabled=True)
        }
        
        # 創建配置
        config = EnhancedYOLOConfig(
            segmentation_model_path="dummy.pt",  # dummy路徑
            device="cpu",
            class_configs=class_configs
        )
        
        # 創建推理器
        inference = create_enhanced_yolo_inference(config)
        
        # 測試GT載入
        print(f"📁 測試文件: {test_data['annotation_path']}")
        
        # 模擬圖像形狀
        image_shape = (480, 640, 3)
        
        gt_annotations = inference._load_gt_annotations(test_data['annotation_path'], image_shape)
        
        print(f"✅ GT載入結果: {len(gt_annotations)} 個標註")
        
        # 顯示詳細信息
        for i, gt in enumerate(gt_annotations):
            print(f"   GT {i+1}:")
            print(f"     類別: {gt['class_name']} (ID: {gt['class_id']})")
            print(f"     邊界框: {gt['bbox']}")
            print(f"     面積: {gt.get('area', 'N/A')}")
        
        # 測試可視化方法（模擬）
        if gt_annotations:
            print("\n🎨 測試GT可視化...")
            
            # 創建模擬圖像
            try:
                import numpy as np
                test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
                
                # 測試繪製方法
                inference._draw_ground_truth(test_image, gt_annotations)
                print("✅ GT繪製測試成功")
                
                # 測試三面板可視化（模擬）
                results = {
                    'ground_truth': gt_annotations,
                    'detection': {'detections': []},
                    'segmentation': {'segments': []}
                }
                
                print("✅ 可視化數據準備完成")
                
                # 檢查結果結構
                print(f"📊 結果結構:")
                print(f"   - ground_truth: {len(results['ground_truth'])} 個")
                print(f"   - detection: {len(results['detection']['detections'])} 個")
                print(f"   - segmentation: {len(results['segmentation']['segments'])} 個")
                
                return True
                
            except ImportError:
                print("⚠️  numpy不可用，跳過可視化測試")
                return len(gt_annotations) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_gt_issue():
    """診斷GT問題"""
    print("\n🔍 GT問題診斷")
    print("=" * 25)
    
    print("📋 檢查清單:")
    
    # 1. 檢查文件是否存在
    test_data = create_test_data()
    annotation_path = Path(test_data['annotation_path'])
    
    if annotation_path.exists():
        print("✅ 1. 標註文件存在")
        
        # 2. 檢查文件內容
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("✅ 2. JSON格式正確")
            
            # 3. 檢查shapes
            shapes = data.get('shapes', [])
            print(f"✅ 3. 包含 {len(shapes)} 個形狀")
            
            # 4. 檢查每個shape的結構
            for i, shape in enumerate(shapes):
                label = shape.get('label', 'unknown')
                shape_type = shape.get('shape_type', 'unknown')
                points = shape.get('points', [])
                print(f"   Shape {i+1}: {label} ({shape_type}) - {len(points)} 個點")
            
        except json.JSONDecodeError as e:
            print(f"❌ 2. JSON格式錯誤: {e}")
        except Exception as e:
            print(f"❌ 2. 文件讀取錯誤: {e}")
    else:
        print(f"❌ 1. 標註文件不存在: {annotation_path}")
    
    # 5. 檢查Enhanced YOLO是否可用
    if ENHANCED_YOLO_AVAILABLE:
        print("✅ 5. Enhanced YOLO模組可用")
    else:
        print("❌ 5. Enhanced YOLO模組不可用")
    
    # 6. 測試GT載入
    success = test_gt_loading()
    if success:
        print("✅ 6. GT載入測試成功")
    else:
        print("❌ 6. GT載入測試失敗")

def main():
    """主函數"""
    print("🔍 GT可視化問題測試工具")
    print("=" * 50)
    
    # 運行診斷
    diagnose_gt_issue()
    
    print("\n💡 建議解決方案:")
    print("1. 確保標註文件格式正確（LabelMe JSON或YOLO TXT）")
    print("2. 檢查標註文件路徑是否正確")
    print("3. 確保類別配置包含標註中的所有類別")
    print("4. 檢查圖像和標註文件名是否一致")
    print("5. 確保Enhanced YOLO模組正確載入")
    
    print(f"\n🔧 如果問題持續，請檢查:")
    print("   - enhanced_yolo_inference.py 中的 _load_gt_annotations 方法")
    print("   - enhanced_yolo_inference.py 中的 _save_three_panel_visualization 方法")
    print("   - 運行時的日誌輸出（logger級別設為DEBUG）")

if __name__ == "__main__":
    main()