# Old Code Archive

本目录包含已被重构、替代或不再使用的旧代码文件。这些文件被保留用于参考目的。

## 文件分类和移动原因

### 1. 已被重构替代的文件

- **labelme_dataset_read.py**: 已被 `model_create/util/dataset.py` 和 `model_create/data/unified_dataset.py` 重构替代
- **pci_calculator.py**: 已移动到 `model_create/analysis/pci_calculator.py`，形成更好的模块化架构
- **test_model_confusionmatrix_v3_testbind3.py**: 已被 `model_create/full_model/test_model_confusionmatrix_v3_testbind3.py` 替代

### 2. 测试和临时文件

- **test.py**: 临时测试文件，包含图像合并和CSV处理代码，已被统一测试框架替代
- **ttttest.py**: 临时测试文件，包含YOLO推理和混淆矩阵计算实验代码
- **modified-code.py**: 包含YOLO推理和CSV处理的修改版代码，功能已被更完善的推理系统替代

### 3. 示例和GUI文件

- **enhanced_yolo_example.py**: 增强YOLO系统使用示例，保留作为参考
- **enhanced_yolo_gui.py**: 增强YOLO的GUI界面，已被更完善的PyQt6界面系统替代
- **sam_finetuning_example.py**: SAM微调示例代码，功能已集成到统一训练系统

### 4. 工具和转换文件

- **convert_csv.py**: CSV转换和采样工具，专门的数据处理工具
- **convert_from_keras.py**: Keras到PyTorch模型转换工具，一次性使用工具
- **revised_yolo_detection_code.py**: 修订版YOLO检测代码，已被更完善的推理系统替代

### 5. 实验性文件

- **0_seg_SAMFine.py**: SAM微调实验代码，功能已集成到统一训练系统

## 重构后的现代化架构

项目现在具备：
- 统一的模型工厂系统 (model_create/core/)
- 配置驱动的架构 (configs/)
- 完整的训练系统 (training/)
- 标准化的推理引擎 (inference/)
- 分布式训练支持 (distributed/)
- 完善的测试框架

## 注意事项

如果需要参考这些旧文件的特定功能或代码片段，它们仍然可以在此目录中找到。但建议使用现代化的重构版本进行新的开发工作。

移动日期: 2025-06-20