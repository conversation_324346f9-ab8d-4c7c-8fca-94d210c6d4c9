# SAM微調訓練配置文件

# 模型配置
model_type: "vit_h"  # vit_h, vit_l, vit_b
sam_checkpoint: "sam_vit_h_4b8939.pth"  # SAM預訓練權重路徑
freeze_image_encoder: false  # 是否凍結圖像編碼器
freeze_prompt_encoder: true   # 是否凍結提示編碼器
freeze_mask_decoder: false   # 是否凍結遮罩解碼器

# LoRA配置
use_lora: true         # 是否使用LoRA微調
lora_rank: 4          # LoRA秩
lora_alpha: 32        # LoRA alpha參數
lora_dropout: 0.1     # LoRA dropout
lora_target_modules:  # 目標模組
  - "qkv"
  - "proj" 
  - "fc1"
  - "fc2"

# 訓練配置
num_epochs: 100
batch_size: 4
learning_rate: 1e-4
weight_decay: 0.01
warmup_epochs: 5

# 損失配置
loss_weights:
  focal: 2.0
  dice: 1.0
  iou: 1.0
use_focal_loss: true
use_dice_loss: true
use_iou_loss: true

# 數據配置
image_size: 1024
prompt_type: "auto"  # point, box, mask, auto
num_points: 5
num_classes: 5

# 優化配置
optimizer: "adamw"    # adamw, adam
scheduler: "cosine"   # cosine, step
gradient_clip: 1.0
accumulation_steps: 1

# 保存和驗證配置
save_every: 10
val_every: 5
early_stopping_patience: 20

# 道路基礎設施檢測專用配置
class_names:
  0: "background"
  1: "crack"         # 裂縫
  2: "pothole"       # 坑洞
  3: "manhole"       # 人孔蓋
  4: "marking"       # 道路標線

# 數據增強配置
augmentation:
  horizontal_flip: 0.5
  vertical_flip: 0.2
  rotation: 15
  brightness: 0.2
  contrast: 0.2
  saturation: 0.2
  hue: 0.1

# 高級配置
advanced:
  mixed_precision: true
  gradient_checkpointing: true
  compile_model: false  # PyTorch 2.0+
  wandb_logging: false
  tensorboard_logging: true