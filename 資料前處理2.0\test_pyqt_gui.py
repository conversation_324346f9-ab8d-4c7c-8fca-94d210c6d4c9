#!/usr/bin/env python3
"""
PyQt GUI 應用程式測試腳本
"""

import sys
import os
import traceback


def test_gui_basic():
    """測試GUI基本功能"""
    try:
        print("開始測試PyQt GUI應用程式...")

        # 檢查PyQt6是否可用
        try:
            from PyQt6.QtWidgets import QApplication
            print("✓ PyQt6已安裝")
        except ImportError:
            print("✗ PyQt6未安裝，請運行: pip install PyQt6")
            return False

        # 檢查qdarkstyle是否可用
        try:
            import qdarkstyle
            print("✓ qdarkstyle已安裝")
        except ImportError:
            print("⚠ qdarkstyle未安裝，黑暗模式將不可用")

        # 檢查核心工具模組是否可用
        modules_to_check = [
            'annotation_converter',
            'annotation_editor',
            'dataset_divider',
            'img_augmenter',
            'panorama_augmenter'
        ]

        missing_modules = []
        for module in modules_to_check:
            try:
                __import__(module)
                print(f"✓ {module}模組可用")
            except ImportError as e:
                print(f"⚠ {module}模組無法導入: {e}")
                missing_modules.append(module)

        if missing_modules:
            print(f"警告：某些功能可能無法使用，缺少模組：{missing_modules}")

        # 創建應用程式實例
        app = QApplication(sys.argv)

        # 導入主窗口
        try:
            from pyqt_gui_application import MainWindow
            print("✓ 主窗口類導入成功")
        except ImportError as e:
            print(f"✗ 無法導入主窗口類: {e}")
            return False

        # 創建主窗口實例
        try:
            window = MainWindow()
            print("✓ 主窗口創建成功")
        except Exception as e:
            print(f"✗ 主窗口創建失敗: {e}")
            print(f"詳細錯誤信息: {traceback.format_exc()}")
            return False

        # 檢查標籤頁是否正確創建
        try:
            tab_count = window.tab_widget.count()
            print(f"✓ 創建了 {tab_count} 個標籤頁")

            for i in range(tab_count):
                tab_text = window.tab_widget.tabText(i)
                print(f"  - 標籤頁 {i+1}: {tab_text}")

        except Exception as e:
            print(f"✗ 檢查標籤頁時出錯: {e}")
            return False

        # 檢查配置管理器
        try:
            config = window.config_manager.config
            print("✓ 配置管理器工作正常")
            print(f"  - 主題: {config.get('theme', 'light')}")
            print(f"  - 窗口大小: {config['window_geometry']}")
        except Exception as e:
            print(f"✗ 配置管理器出錯: {e}")
            return False

        # 測試主題切換功能
        try:
            window.toggle_theme()
            print("✓ 主題切換功能正常")
        except Exception as e:
            print(f"⚠ 主題切換功能出錯: {e}")

        print("\n🎉 基本測試通過！GUI應該可以正常運行。")
        print("提示：要實際測試GUI，請運行: python pyqt_gui_application.py")

        # 不顯示窗口，只測試創建
        window.close()
        app.quit()

        return True

    except Exception as e:
        print(f"✗ 測試過程中發生未捕獲的錯誤: {e}")
        print(f"詳細錯誤信息: {traceback.format_exc()}")
        return False


def test_gui_visual():
    """測試GUI視覺效果（實際顯示窗口）"""
    try:
        print("開始視覺測試...")

        from PyQt6.QtWidgets import QApplication
        from pyqt_gui_application import MainWindow

        app = QApplication(sys.argv)
        window = MainWindow()

        # 顯示窗口
        window.show()

        print("GUI窗口已顯示。")
        print("請檢查以下內容：")
        print("1. 所有5個工具標籤頁是否正確顯示")
        print("2. 主題切換是否工作（視圖菜單 -> 切換主題）")
        print("3. 各個工具界面是否正確顯示")
        print("4. 日誌區域是否可見")
        print("5. 關閉窗口測試完成")

        # 啟動事件循環
        return app.exec()

    except Exception as e:
        print(f"視覺測試失敗: {e}")
        return False


if __name__ == "__main__":
    print("圖像數據集處理工具集 PyQt GUI 測試")
    print("=" * 50)

    # 檢查命令行參數
    if len(sys.argv) > 1 and sys.argv[1] == "--visual":
        # 視覺測試
        test_gui_visual()
    else:
        # 基本測試
        success = test_gui_basic()

        if success:
            print("\n要進行視覺測試，請運行:")
            print("python test_pyqt_gui.py --visual")
            sys.exit(0)
        else:
            print("\n基本測試失敗，請檢查依賴項和代碼。")
            sys.exit(1)
