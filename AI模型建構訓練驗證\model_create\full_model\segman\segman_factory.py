"""
SegMAN Model Factory - Unified Interface for All SegMAN Variants

This factory provides a unified interface to create different SegMAN model variants
including encoders, decoders, and complete segmentation models.

Supported Variants:
- Original SegMAN (segman_encoder.py, segman_decoder.py)
- MambaVision SegMAN (enhanced with NVIDIA MambaVision backbone)
- Efficient SegMAN (lightweight for edge deployment)
- Enhanced SegMAN (bidirectional SSM with advanced features)

Usage:
    factory = SegMANFactory()
    model = factory.create_segmentation_model('enhanced', 'small', num_classes=5)
    encoder = factory.create_encoder('mambavision', 'base')
    decoder = factory.create_decoder('efficient', in_channels=[64, 128, 256, 512])
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
import warnings

# Import all SegMAN variants
try:
    from .segman_encoder import SegMANEncoder
    from .segman_decoder import SegMANDecoder
    from .segman_encoder_mambavision import (
        SegMANMambaVisionEncoder,
        segman_mambavision_tiny,
        segman_mambavision_small, 
        segman_mambavision_base
    )
    from .segman_encoder_efficient import (
        SegMANEfficientEncoder,
        segman_efficient_nano,
        segman_efficient_small,
        segman_efficient_medium
    )
    from .segman_encoder_enhanced import (
        SegMANEnhancedEncoder,
        segman_enhanced_small,
        segman_enhanced_base,
        segman_enhanced_large
    )
except ImportError as e:
    warnings.warn(f"Some SegMAN modules could not be imported: {e}")


@dataclass
class SegMANConfig:
    """Configuration class for SegMAN models"""
    
    # Model variant
    variant: str = 'original'  # 'original', 'mambavision', 'efficient', 'enhanced'
    size: str = 'small'  # 'nano', 'tiny', 'small', 'medium', 'base', 'large'
    
    # Basic parameters
    image_size: int = 224
    in_chans: int = 3
    num_classes: int = 1000
    
    # Architecture parameters
    embed_dims: Optional[List[int]] = None
    depths: Optional[List[int]] = None
    num_heads: Optional[List[int]] = None
    mlp_ratios: Optional[List[float]] = None
    
    # Training parameters
    drop_path_rate: float = 0.1
    dropout: float = 0.1
    
    # Decoder parameters (for segmentation)
    decoder_channels: int = 256
    feat_proj_dim: int = 256
    use_fpn: bool = True
    
    # Efficiency parameters
    use_checkpoint: bool = False
    mixed_precision: bool = True
    
    # Task-specific parameters
    task: str = 'classification'  # 'classification', 'segmentation', 'detection'


class SegMANFactory:
    """
    Factory class for creating SegMAN model variants
    """
    
    def __init__(self):
        self.encoder_registry = {
            'original': {
                'tiny': self._create_original_encoder,
                'small': self._create_original_encoder,
                'base': self._create_original_encoder,
                'large': self._create_original_encoder,
            },
            'mambavision': {
                'tiny': segman_mambavision_tiny,
                'small': segman_mambavision_small,
                'base': segman_mambavision_base,
            },
            'efficient': {
                'nano': segman_efficient_nano,
                'small': segman_efficient_small,
                'medium': segman_efficient_medium,
            },
            'enhanced': {
                'small': segman_enhanced_small,
                'base': segman_enhanced_base,
                'large': segman_enhanced_large,
            }
        }
        
        self.default_configs = self._get_default_configs()
    
    def _get_default_configs(self) -> Dict[str, Dict[str, SegMANConfig]]:
        """Get default configurations for all model variants"""
        
        return {
            'original': {
                'tiny': SegMANConfig(
                    variant='original', size='tiny',
                    embed_dims=[32, 64, 144, 192],
                    depths=[2, 2, 4, 2],
                    num_heads=[1, 2, 4, 8],
                    drop_path_rate=0.05
                ),
                'small': SegMANConfig(
                    variant='original', size='small',
                    embed_dims=[64, 144, 288, 512],
                    depths=[2, 2, 10, 4],
                    num_heads=[2, 4, 8, 16],
                    drop_path_rate=0.1
                ),
                'base': SegMANConfig(
                    variant='original', size='base',
                    embed_dims=[96, 160, 364, 560],
                    depths=[4, 4, 18, 4],
                    num_heads=[4, 8, 13, 20],
                    drop_path_rate=0.15
                ),
                'large': SegMANConfig(
                    variant='original', size='large',
                    embed_dims=[96, 192, 432, 640],
                    depths=[4, 4, 28, 4],
                    num_heads=[4, 8, 12, 20],
                    drop_path_rate=0.2
                ),
            },
            'mambavision': {
                'tiny': SegMANConfig(
                    variant='mambavision', size='tiny',
                    embed_dims=[64, 128, 256, 512],
                    depths=[1, 1, 3, 1],
                    drop_path_rate=0.05
                ),
                'small': SegMANConfig(
                    variant='mambavision', size='small',
                    embed_dims=[96, 192, 384, 768],
                    depths=[2, 2, 6, 2],
                    drop_path_rate=0.1
                ),
                'base': SegMANConfig(
                    variant='mambavision', size='base',
                    embed_dims=[128, 256, 512, 1024],
                    depths=[2, 2, 8, 2],
                    drop_path_rate=0.15
                ),
            },
            'efficient': {
                'nano': SegMANConfig(
                    variant='efficient', size='nano',
                    embed_dims=[32, 64, 128, 256],
                    depths=[1, 1, 2, 1],
                    drop_path_rate=0.02
                ),
                'small': SegMANConfig(
                    variant='efficient', size='small',
                    embed_dims=[64, 128, 256, 512],
                    depths=[1, 2, 3, 1],
                    drop_path_rate=0.05
                ),
                'medium': SegMANConfig(
                    variant='efficient', size='medium',
                    embed_dims=[96, 192, 384, 768],
                    depths=[2, 2, 4, 2],
                    drop_path_rate=0.1
                ),
            },
            'enhanced': {
                'small': SegMANConfig(
                    variant='enhanced', size='small',
                    embed_dims=[96, 192, 384, 768],
                    depths=[2, 2, 6, 2],
                    drop_path_rate=0.1,
                    use_fpn=True
                ),
                'base': SegMANConfig(
                    variant='enhanced', size='base',
                    embed_dims=[128, 256, 512, 1024],
                    depths=[2, 2, 8, 2],
                    drop_path_rate=0.15,
                    use_fpn=True
                ),
                'large': SegMANConfig(
                    variant='enhanced', size='large',
                    embed_dims=[160, 320, 640, 1280],
                    depths=[3, 3, 12, 3],
                    drop_path_rate=0.2,
                    use_fpn=True
                ),
            }
        }
    
    def _create_original_encoder(self, **kwargs):
        """Create original SegMAN encoder"""
        # Map size to original model functions
        size = kwargs.get('size', 'small')
        if size == 'tiny':
            from .segman_encoder import SegMANEncoder_t
            return SegMANEncoder_t(**kwargs)
        elif size == 'small':
            from .segman_encoder import SegMANEncoder_s
            return SegMANEncoder_s(**kwargs)
        elif size == 'base':
            from .segman_encoder import SegMANEncoder_b
            return SegMANEncoder_b(**kwargs)
        elif size == 'large':
            from .segman_encoder import SegMANEncoder_l
            return SegMANEncoder_l(**kwargs)
        else:
            raise ValueError(f"Unknown size for original SegMAN: {size}")
    
    def create_encoder(
        self, 
        variant: str = 'enhanced', 
        size: str = 'small', 
        config: Optional[SegMANConfig] = None,
        **kwargs
    ) -> nn.Module:
        """
        Create SegMAN encoder
        
        Args:
            variant: Model variant ('original', 'mambavision', 'efficient', 'enhanced')
            size: Model size ('nano', 'tiny', 'small', 'medium', 'base', 'large')
            config: Custom configuration
            **kwargs: Additional arguments
            
        Returns:
            SegMAN encoder model
        """
        
        if variant not in self.encoder_registry:
            raise ValueError(f"Unknown variant: {variant}. Available: {list(self.encoder_registry.keys())}")
        
        if size not in self.encoder_registry[variant]:
            raise ValueError(f"Size '{size}' not available for variant '{variant}'. "
                           f"Available sizes: {list(self.encoder_registry[variant].keys())}")
        
        # Get default config and update with custom config
        if config is None:
            config = self.default_configs[variant][size]
        
        # Update config with kwargs
        config_dict = {
            'image_size': config.image_size,
            'in_chans': config.in_chans,
            'num_classes': config.num_classes,
            'drop_path_rate': config.drop_path_rate,
            **kwargs
        }
        
        if config.embed_dims is not None:
            config_dict['embed_dims'] = config.embed_dims
        if config.depths is not None:
            config_dict['depths'] = config.depths
        if config.num_heads is not None:
            config_dict['num_heads'] = config.num_heads
        if config.mlp_ratios is not None:
            config_dict['mlp_ratios'] = config.mlp_ratios
        
        # Create encoder
        encoder_fn = self.encoder_registry[variant][size]
        return encoder_fn(**config_dict)
    
    def create_decoder(
        self,
        variant: str = 'original',
        in_channels: List[int] = [96, 192, 384, 768],
        channels: int = 256,
        **kwargs
    ) -> nn.Module:
        """
        Create SegMAN decoder
        
        Args:
            variant: Decoder variant
            in_channels: Input channels from encoder stages
            channels: Decoder channels
            **kwargs: Additional arguments
            
        Returns:
            SegMAN decoder model
        """
        
        # For now, use the original decoder
        # In the future, we can create specialized decoders for each variant
        
        if variant == 'original':
            return SegMANDecoder(
                in_channels=in_channels,
                channels=channels,
                **kwargs
            )
        else:
            # Use original decoder as fallback for other variants
            warnings.warn(f"Decoder variant '{variant}' not implemented, using original decoder")
            return SegMANDecoder(
                in_channels=in_channels,
                channels=channels,
                **kwargs
            )
    
    def create_segmentation_model(
        self,
        variant: str = 'enhanced',
        size: str = 'small',
        num_classes: int = 5,
        config: Optional[SegMANConfig] = None,
        **kwargs
    ) -> nn.Module:
        """
        Create complete SegMAN segmentation model
        
        Args:
            variant: Model variant
            size: Model size
            num_classes: Number of segmentation classes
            config: Custom configuration
            **kwargs: Additional arguments
            
        Returns:
            Complete segmentation model
        """
        
        # Create encoder
        encoder = self.create_encoder(variant, size, config, num_classes=0, **kwargs)
        
        # Get encoder output dimensions
        if hasattr(encoder, 'embed_dims'):
            in_channels = encoder.embed_dims
        elif hasattr(encoder, 'num_features'):
            # For original SegMAN
            in_channels = [64, 144, 288, 512]  # Default for original
        else:
            # Fallback
            in_channels = [96, 192, 384, 768]
        
        # Create decoder
        decoder = self.create_decoder(
            variant='original',  # Use original decoder for now
            in_channels=in_channels,
            channels=256,
            num_classes=num_classes,
            **kwargs
        )
        
        # Create complete model
        return SegMANSegmentationModel(encoder, decoder)
    
    def list_available_models(self) -> Dict[str, List[str]]:
        """List all available model variants and sizes"""
        return {variant: list(sizes.keys()) for variant, sizes in self.encoder_registry.items()}
    
    def get_model_info(self, variant: str, size: str) -> Dict[str, Any]:
        """Get information about a specific model variant"""
        if variant not in self.default_configs:
            raise ValueError(f"Unknown variant: {variant}")
        
        if size not in self.default_configs[variant]:
            raise ValueError(f"Size '{size}' not available for variant '{variant}'")
        
        config = self.default_configs[variant][size]
        
        # Create a temporary model to get parameter count
        try:
            model = self.create_encoder(variant, size, num_classes=1000)
            param_count = sum(p.numel() for p in model.parameters()) / 1e6
        except Exception as e:
            param_count = "N/A"
        
        return {
            'variant': variant,
            'size': size,
            'embed_dims': config.embed_dims,
            'depths': config.depths,
            'parameters_M': param_count,
            'drop_path_rate': config.drop_path_rate,
        }


class SegMANSegmentationModel(nn.Module):
    """
    Complete SegMAN segmentation model combining encoder and decoder
    """
    
    def __init__(self, encoder: nn.Module, decoder: nn.Module):
        super().__init__()
        self.encoder = encoder
        self.decoder = decoder
    
    def forward(self, x):
        # Extract features from encoder
        if hasattr(self.encoder, 'forward_features'):
            features = self.encoder.forward_features(x)
            if isinstance(features, tuple):  # Handle FPN case
                features = features[0]  # Use original features
        else:
            # For models without forward_features method
            features = []
            # This would need to be implemented for each encoder variant
            raise NotImplementedError("Encoder must implement forward_features method")
        
        # Pass features to decoder
        output = self.decoder(features)
        
        return output
    
    def extract_features(self, x):
        """Extract intermediate features for analysis"""
        return self.encoder.forward_features(x)


def create_segman_model(
    variant: str = 'enhanced',
    size: str = 'small', 
    task: str = 'segmentation',
    num_classes: int = 5,
    **kwargs
) -> nn.Module:
    """
    Convenience function to create SegMAN models
    
    Args:
        variant: Model variant ('original', 'mambavision', 'efficient', 'enhanced')
        size: Model size ('nano', 'tiny', 'small', 'medium', 'base', 'large')  
        task: Task type ('classification', 'segmentation')
        num_classes: Number of classes
        **kwargs: Additional arguments
        
    Returns:
        SegMAN model
    """
    
    factory = SegMANFactory()
    
    if task == 'classification':
        return factory.create_encoder(variant, size, num_classes=num_classes, **kwargs)
    elif task == 'segmentation':
        return factory.create_segmentation_model(variant, size, num_classes=num_classes, **kwargs)
    else:
        raise ValueError(f"Unknown task: {task}. Available: ['classification', 'segmentation']")


if __name__ == "__main__":
    # Test the factory
    factory = SegMANFactory()
    
    print("SegMAN Model Factory Test")
    print("=" * 50)
    
    # List available models
    available_models = factory.list_available_models()
    print("Available models:")
    for variant, sizes in available_models.items():
        print(f"  {variant}: {sizes}")
    
    print("\n" + "=" * 50)
    
    # Test creating different variants
    test_variants = [
        ('mambavision', 'small'),
        ('efficient', 'nano'),
        ('enhanced', 'small'),
    ]
    
    for variant, size in test_variants:
        try:
            print(f"\nTesting {variant}-{size}:")
            
            # Get model info
            info = factory.get_model_info(variant, size)
            print(f"  Parameters: {info['parameters_M']:.2f}M")
            print(f"  Embed dims: {info['embed_dims']}")
            print(f"  Depths: {info['depths']}")
            
            # Test encoder
            encoder = factory.create_encoder(variant, size, num_classes=5)
            
            # Test input
            x = torch.randn(1, 3, 224, 224)
            with torch.no_grad():
                if hasattr(encoder, 'forward_features'):
                    features = encoder.forward_features(x)
                    if isinstance(features, tuple):
                        features = features[0]
                    print(f"  Feature shapes: {[f.shape for f in features]}")
                
                output = encoder(x)
                print(f"  Output shape: {output.shape}")
            
        except Exception as e:
            print(f"  Error: {e}")
    
    print(f"\n" + "=" * 50)
    print("Factory test completed!")
    
    # Example usage
    print(f"\nExample usage:")
    print(f"  # Create enhanced segmentation model")
    print(f"  model = create_segman_model('enhanced', 'small', 'segmentation', num_classes=5)")
    print(f"  ")
    print(f"  # Create efficient classification model")  
    print(f"  model = create_segman_model('efficient', 'nano', 'classification', num_classes=1000)")