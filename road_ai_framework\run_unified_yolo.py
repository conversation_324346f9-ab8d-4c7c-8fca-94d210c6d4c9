#!/usr/bin/env python3
"""
🚀 統一YOLO推理系統 - 簡化使用腳本 (增強版)
整合所有YOLO功能：檢測、分割、智能過濾、三視圖等

🆕 新功能:
- CSV增量更新：每處理一張圖像立即更新CSV報告
- 增強三視圖：自動加載GT標註，完美匹配原始格式
- 智能mask處理：修復維度不匹配問題，支援各種mask格式
"""

from core.import_helper import setup_project_paths
import os
import sys
from pathlib import Path

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

setup_project_paths()

# ==================== 🔧 參數設定區域 ====================
# 只保留高級切片推理、ROI、class_configs、label_aliases、路徑、視覺化、智能過濾等必要參數

# ===== 🎯 基礎配置 =====
# YOLO分割模型路徑 (請替換為實際路徑)
segmentation_model_path = r"D:\4_road_crack\best_1819.pt"
# 運算設備: "auto", "cpu", "cuda", "mps"
device = "cuda"
img_size = 1280                                            # 輸入圖像尺寸
global_conf = 0.1                                        # 全局置信度閾值
iou_threshold = 0.3                                       # IoU閾值
max_det = 1000                                            # 最大檢測數量

# ===== 🚀 高級切片推理配置 =====
# 🆕 啟用高級切片推理（False=使用一般推理）
enable_advanced_slice_inference = False
advanced_slice_height = 320                               # 切片高度
advanced_slice_width = 320                                # 切片寬度
advanced_overlap_ratio = 0.4                              # 重疊比例
fusion_strategy = "largest_object"                                   # 物件融合策略
fusion_iou_threshold = 0.4                                # 融合IoU閾值
fusion_confidence_threshold = 0.1                         # 融合置信度閾值
enable_overall_inference = False                           # 切片後整體圖像二次檢測
enable_adjacent_merge = True                              # 相鄰同類別區域合併
adjacent_distance_threshold = 30.0                        # 相鄰距離閾值(像素)

# ===== 🧠 智能過濾配置 =====
enable_intelligent_filtering = False                       # 啟用智能過濾
enable_detection_merge = True                             # 啟用檢測合併
step1_iou_threshold = 0.0                                 # Step1 IoU閾值
linear_aspect_ratio_threshold = 0.8                       # 長寬比閾值
area_ratio_threshold = 0.4                                # 面積比閾值
step2_iou_threshold = 0.3                                 # Step2 IoU閾值
joint_overlap_threshold = 0.3                             # joint覆蓋閾值

# ===== 🚫 類別排除配置 =====
# 排除的類別ID列表 (例: [8, 9])
excluded_class_ids = []
excluded_class_names = [
    'joint','manhole','dirt',
    'lane_line_linear','patch_square','expansion_joint'
]                           # 排除的類別名稱列表 (例: ["dirt", "noise"])
# 只包含的類別ID列表 (None=包含所有，設定後只保留指定類別)
included_class_ids = None

# ===== 🎨 視覺化配置 =====
save_visualizations = True                               # 保存可視化結果
save_predictions = False                                  # 保存預測結果JSON
save_statistics = True                                    # 保存統計信息
enable_three_view_output = False                           # 啟用三視圖輸出
# 佈局: "horizontal", "vertical"
three_view_layout = "horizontal"
three_view_spacing = 10                                   # 三視圖間距（像素）
font_path = ""                                            # 自定義字體路徑（空則使用默認中文字體）
font_size = 2.0                                           # 字體大小倍數
font_thickness = 4                                        # 字體粗細
font_scale = 3.0                                          # 字體縮放比例
output_image_quality = 85                                 # 輸出圖像質量 (1-100)
line_thickness = 3                                        # 邊框線條粗細
fill_alpha = 0.1                                         # mask填充透明度

# 🆕 Mask渲染模式配置 (解決過暗問題)
# "unified": 統一mask圖層方法，一次性混合（最推薦，徹底解決疊加問題）
# "intelligent": 智能重疊處理，極低alpha值
# "simple": 簡單極低alpha混合
# "outline_only": 只繪製邊框，不填充mask（最不會變暗）
mask_render_mode = "outline_only"

enable_roi_preview = False                                 # 是否啟用ROI和切片預覽
preview_mode = False                                      # 預覽模式：只處理第一張圖像並生成預覽圖

# ===== 🏷️ LabelMe JSON輸出配置 =====
enable_labelme_output = True                              # 啟用LabelMe JSON輸出
# LabelMe JSON輸出目錄名稱（相對於output_path）
labelme_output_dir = "labelme_json"
# Polygon簡化容差（數值越大越簡化，0表示不簡化）
labelme_simplify_tolerance = 8
# 最小polygon點數（少於此數的會被忽略）
labelme_min_polygon_points = 3
# 是否在label中包含confidence分數
labelme_include_confidence = False
# 是否在LabelMe JSON中包含base64編碼的圖像
labelme_include_base64_image = True
labelme_copy_images = True                                # 🆕 是否同時複製圖像到LabelMe目錄
# 🆕 新增參數：是否直接使用縮小後的座標與圖像進行LabelMe輸出
labelme_use_resized_version = True

# ===== 📁 路徑配置 =====
input_path = r"F:\破損\113\0220台61三車道"       # 輸入路徑（圖像或目錄）
labelme_dir = r""      # LabelMe標註目錄（用於GT加載和三視圖顯示）
output_path = r"E:\新破損-圖像\113-0220_model_out"   # 輸出路徑


enable_json_scan = True                                   # 是否在推理前掃描JSON標籤
display_json_summary = True                               # 是否顯示JSON掃描摘要
save_json_scan_report = True                              # 是否保存JSON掃描報告
enable_gt_comparison = True                               # 啟用GT比較
# GT格式: "labelme", "yolo", "coco"
gt_format = "labelme"

# ===== 🏷️ 類別詳細配置 ====

class_configs = {
    0: ["Alligator_crack", "龜裂", [255, 0, 0], 0.1, 0.1, True],
    1: ["deformation", "變形", [0, 255, 0], 0.1, 0.1, True],
    2: ["dirt", "汙垢", [0, 0, 255], 0.1, 0.08, True],
    3: ["expansion_joint", "伸縮縫", [0, 0, 255], 0.1, 0.08, True],
    4: ["joint", "路面接縫", [255, 0, 255], 0.1, 0.1, True],
    5: ["lane_line_linear", "白線裂縫", [0, 255, 255], 0.1, 0.05, True],
    6: ["linear_crack", "裂縫", [128, 0, 128], 0.1, 0.1, True],
    7: ["manhole", "孔蓋", [255, 165, 0], 0.1, 0.1, True],
    8: ["patch", "補綻", [139, 69, 19], 0.1, 0.1, True],
    9: ["patch_square", "補綻_方正", [139, 139, 19], 0.1, 0.1, True],
    10: ["potholes", "坑洞", [0, 128, 255], 0.1, 0.1, True],
    # 10: ["rutting", "車轍", [128, 128, 0], 0.1, 0.1, True]

}

# class_configs = {
#     0: ["expansion_joint", "伸縮縫", [255, 0, 0], 0.1, 0.1, True],
#     1: ["joint", "路面接縫", [0, 255, 0], 0.1, 0.1, True],
#     2: ["linear_crack", "縱向裂縫", [0, 0, 255], 0.1, 0.08, True],
#     3: ["Alligator_crack", "龜裂", [255, 255, 0], 0.1, 0.1, True],
#     4: ["potholes", "坑洞", [255, 0, 255], 0.4, 0.1, True],
#     5: ["patch", "補綻", [0, 255, 255], 0.1, 0.05, True],
#     6: ["manhole", "人孔蓋", [128, 0, 128], 0.1, 0.1, True],
#     7: ["deformation", "變形", [255, 165, 0], 0.1, 0.1, True],
#     8: ["dirt", "污垢", [139, 69, 19], 0.1, 0.1, True],
#     9: ["lane_line_linear", "白線裂縫", [0, 128, 255], 0.1, 0.1, True],
# }

label_aliases = {
    "joint_": "joint",
    "patch_": "patch",
    "linear_crack_": "linear_crack",
    "expansion_joint_": "expansion_joint",
    "Alligator_crack_": "Alligator_crack",
    "potholes_": "potholes",
    "manhole_": "manhole",
    "deformation_": "deformation",
    "dirt_": "dirt",
    "lane_line_linear_": "lane_line_linear",
    "manhole_人孔蓋或排水溝": "manhole",
    "potholes_坑洞": "potholes",
    "linear_crack_裂縫": "linear_crack",
    "expansion_joint_伸縮縫": "expansion_joint",
    "joint_路面接縫": "joint",
    "patch_補綻": "patch",
    "Alligator_crack_龜裂": "Alligator_crack",
    "lane_line_linear_白綫裂縫": "lane_line_linear",
    "deformation_變形": "deformation",
    "dirt_污垢": "dirt",
    "joint_road_seam": "joint",
    "patch_repair": "patch",
    "linear_crack_longitudinal": "linear_crack",
}

# ===== 🎯 ROI 中心區域配置 =====
enable_roi_processing = False  # 是否啟用中心ROI處理
roi_top_ratio = 3.0           # 從中心往上保留的倍數 (1.0=中心, 5.0=邊緣)
roi_bottom_ratio = 2.5        # 從中心往下保留的倍數 (1.0=中心, 5.0=邊緣)
roi_left_ratio = 1.0          # 從中心往左保留的倍數 (1.0=中心, 5.0=邊緣)
roi_right_ratio = 1.0         # 從中心往右保留的倍數 (1.0=中心, 5.0=邊緣)

# ===== 📷 預覽模式配置 =====
preview_only = False                                      # 只生成預覽圖，不執行推理
preview_save_original = False                              # 預覽模式中是否保存原圖

# ===== 🖼️ 圖像尺寸與記憶體管理 =====
# 圖像縮放比例 (1.0 表示不縮放, 0.3 表示縮放為30%)
resize_ratio = 0.3
# 記憶體清理間隔（每處理N張圖像清理一次）- 更積極的清理
memory_cleanup_interval = 3
enable_memory_monitoring = True                              # 啟用記憶體監控和日誌
force_cleanup_on_completion = True                           # 批次處理完成後強制清理記憶體
aggressive_memory_cleanup = True                             # 🆕 啟用積極記憶體清理模式

# 🆕 圖像記憶體釋放配置
enable_image_memory_release = True                           # 啟用主動圖像記憶體釋放
enable_detection_memory_release = True                       # 啟用檢測結果記憶體釋放（mask等）
batch_memory_cleanup_interval = 10                           # 批次處理中每N張圖像執行深度清理
enable_batch_final_cleanup = True                            # 啟用批次處理完成後的最終清理

# ===== ⚙️ 其他高級配置 =====
batch_processing = True                                       # 批次處理模式
auto_create_output_dirs = True                                # 自動創建輸出目錄
include_timestamp = True                                      # 文件名包含時間戳

# ===== 🔄 中斷回復配置 =====
enable_resume = True                                          # 啟用中斷回復功能
resume_checkpoint_interval = 10                               # 每處理N張圖像保存一次檢查點
resume_skip_existing = True                                   # 跳過已處理的圖像文件
resume_progress_file = "processing_progress.json"             # 進度文件名
resume_force_overwrite = False                                # 強制覆蓋已存在的結果文件

# ==================== 🚀 執行函數 ====================


def create_config_from_params():
    """從參數創建配置對象"""
    from models.inference.config_manager import UnifiedYOLOConfigManager

    # 創建配置管理器
    config_manager = UnifiedYOLOConfigManager()

    # 設置基礎配置
    config_manager.model.segmentation_model_path = segmentation_model_path
    config_manager.model.device = device

    # 設置推理配置
    config_manager.inference.img_size = img_size
    config_manager.inference.global_conf = global_conf
    config_manager.inference.iou_threshold = iou_threshold
    config_manager.inference.max_det = max_det
    config_manager.inference.resize_ratio = resize_ratio

    # 🚀 新增：設置物件融合配置
    config_manager.fusion.strategy = fusion_strategy
    config_manager.fusion.iou_threshold = fusion_iou_threshold
    config_manager.fusion.confidence_threshold = fusion_confidence_threshold

    # 🎯 設置ROI配置（透過SAHI配置）
    config_manager.sahi.enable_sahi_roi = enable_roi_processing
    config_manager.sahi.roi_top_ratio = roi_top_ratio
    config_manager.sahi.roi_bottom_ratio = roi_bottom_ratio
    config_manager.sahi.roi_left_ratio = roi_left_ratio
    config_manager.sahi.roi_right_ratio = roi_right_ratio

    # 設置智能過濾配置
    config_manager.filtering.enable_intelligent_filtering = enable_intelligent_filtering
    config_manager.filtering.enable_detection_merge = enable_detection_merge
    config_manager.filtering.step1_iou_threshold = step1_iou_threshold
    config_manager.filtering.linear_aspect_ratio_threshold = linear_aspect_ratio_threshold
    config_manager.filtering.area_ratio_threshold = area_ratio_threshold
    config_manager.filtering.step2_iou_threshold = step2_iou_threshold
    config_manager.filtering.joint_overlap_threshold = joint_overlap_threshold

    # 設置視覺化配置
    config_manager.visualization.save_visualizations = save_visualizations
    config_manager.visualization.save_predictions = save_predictions
    config_manager.visualization.save_statistics = save_statistics
    config_manager.visualization.enable_three_view_output = enable_three_view_output
    config_manager.visualization.three_view_layout = three_view_layout
    config_manager.visualization.three_view_spacing = three_view_spacing
    config_manager.visualization.font_path = font_path
    config_manager.visualization.font_size = font_size
    config_manager.visualization.font_thickness = font_thickness
    config_manager.visualization.font_scale = font_scale
    config_manager.visualization.output_image_quality = output_image_quality
    config_manager.visualization.line_thickness = line_thickness
    config_manager.visualization.fill_alpha = fill_alpha
    config_manager.visualization.enable_roi_preview = enable_roi_preview

    # 設置GT配置
    config_manager.ground_truth.enable_gt_comparison = enable_gt_comparison
    config_manager.ground_truth.gt_format = gt_format
    config_manager.ground_truth.gt_path = labelme_dir

    # 設置路徑配置
    config_manager.paths.input_path = input_path
    config_manager.paths.output_path = output_path
    config_manager.paths.labelme_dir = labelme_dir
    config_manager.paths.auto_create_output_dirs = auto_create_output_dirs
    config_manager.paths.enable_roi_processing = enable_roi_processing  # 🆕 設置ROI開關

    # 設置JSON掃描配置
    config_manager.json_scan.enable_json_scan = enable_json_scan
    config_manager.json_scan.display_json_summary = display_json_summary
    config_manager.json_scan.save_json_scan_report = save_json_scan_report

    # 設置輸出配置
    config_manager.output.include_timestamp = include_timestamp

    # 🆕 設置排除類別配置（確保LabelMe整合器能夠獲取）
    config_manager.excluded_class_ids = excluded_class_ids
    config_manager.excluded_class_names = excluded_class_names
    config_manager.included_class_ids = included_class_ids

    print(f"🔍 配置管理器設定排除類別:")
    print(f"   excluded_class_ids: {excluded_class_ids}")
    print(f"   excluded_class_names: {excluded_class_names}")
    print(f"   included_class_ids: {included_class_ids}")

    # 清空默認類別，添加用戶自定義類別
    config_manager.classes.clear()
    config_manager.label_aliases.clear()

    # 添加類別配置
    for class_id, (name, display_name, color, conf, sahi_conf, enabled) in class_configs.items():
        config_manager.add_class_config(
            class_id=class_id,
            name=name,
            display_name=display_name,
            color=color,
            confidence=conf,
            sahi_confidence=sahi_conf,
            enabled=enabled
        )

    # 添加標籤別名
    config_manager.label_aliases.update(label_aliases)

    # 傳遞 ROI 參數（移除 SAHI 依賴，直接設定到 processing 配置）
    # 注意：不再使用 sahi 配置，改用 processing 或專門的 roi 配置
    if hasattr(config_manager, 'roi'):
        config_manager.roi.enable_roi_processing = enable_roi_processing
        config_manager.roi.roi_top_ratio = roi_top_ratio
        config_manager.roi.roi_bottom_ratio = roi_bottom_ratio
        config_manager.roi.roi_left_ratio = roi_left_ratio
        config_manager.roi.roi_right_ratio = roi_right_ratio
    else:
        # 如果沒有專門的 roi 配置，暫時保存到 processing 中
        config_manager.processing = getattr(
            config_manager, 'processing', type('obj', (object,), {})())
        config_manager.processing.enable_roi_processing = enable_roi_processing
        config_manager.processing.roi_top_ratio = roi_top_ratio
        config_manager.processing.roi_bottom_ratio = roi_bottom_ratio
        config_manager.processing.roi_left_ratio = roi_left_ratio
        config_manager.processing.roi_right_ratio = roi_right_ratio

    # 設置LabelMe JSON輸出配置
    if not hasattr(config_manager, 'labelme_output'):
        config_manager.labelme_output = type('obj', (object,), {})()

    config_manager.labelme_output.enable_labelme_output = enable_labelme_output
    config_manager.labelme_output.labelme_output_dir = labelme_output_dir
    config_manager.labelme_output.labelme_simplify_tolerance = labelme_simplify_tolerance
    config_manager.labelme_output.labelme_min_polygon_points = labelme_min_polygon_points
    config_manager.labelme_output.labelme_include_confidence = labelme_include_confidence
    config_manager.labelme_output.labelme_include_base64_image = labelme_include_base64_image
    config_manager.labelme_output.labelme_copy_images = labelme_copy_images  # 🆕 圖像複製配置
    config_manager.labelme_output.labelme_use_resized_version = labelme_use_resized_version  # 🆕 使用縮小版配置

    return config_manager


def main():
    """主函數"""
    import logging

    # 聲明全域變數
    global labelme_dir, input_path, output_path, enable_json_scan, enable_three_view_output
    global enable_intelligent_filtering, enable_labelme_output, batch_processing, enable_roi_preview

    # 設置日誌級別：顯示重要信息和警告
    logging.basicConfig(level=logging.INFO,
                        format='%(levelname)s: %(message)s')

    # 檢查預覽模式
    if preview_mode:
        print("📷 ROI和切片預覽模式")
        print("=" * 70)
        print("👁️  功能：只處理第一張圖像，生成ROI和切片配置預覽圖")
        print("🎨 顯示：四個方向的ROI邊界 + 切片網格 + 參數標註")
        print("=" * 70)

        # 預覽模式的簡化處理
        try:
            from models.inference.roi_preview_generator_enhanced import create_enhanced_roi_preview_generator

            preview_generator = create_enhanced_roi_preview_generator()

            # 準備ROI和切片配置
            roi_ratios = {
                'top': roi_top_ratio,
                'bottom': roi_bottom_ratio,
                'left': roi_left_ratio,
                'right': roi_right_ratio
            }

            slice_config = {
                'height': advanced_slice_height,
                'width': advanced_slice_width,
                'overlap_ratio': advanced_overlap_ratio
            }

            # 生成增強版預覽圖
            preview_path = preview_generator.generate_first_image_preview(
                input_path, output_path, roi_ratios, slice_config
            )

            if preview_path:
                print(f"✨ Enhanced preview generated: {preview_path}")
                print("\n📝 Enhanced preview features:")
                print("  🔴 Red filled areas - Top excluded region")
                print("  🟢 Green filled areas - Bottom excluded region")
                print("  🔵 Blue filled areas - Left excluded region")
                print("  🟡 Yellow filled areas - Right excluded region")
                print("  🟩 Light green tint - ROI area")
                print("  🔶 Orange filled areas - Overlap regions")
                print("  ⬜ Gray grid lines - Slice boundaries")
                print("  📍 [Row,Col] labels - Slice coordinates")
            else:
                print("❌ Enhanced preview generation failed")

            if preview_only:
                print("\n👁️  Preview mode completed, no inference will be performed")
                return

        except Exception as e:
            print(f"❌ Preview mode error: {e}")
            if preview_only:
                return
            print("Continuing with normal inference...")

    print("🚀 統一YOLO推理系統")
    print("=" * 70)
    print("🌟 高級切片推理模式：6種融合策略、整體二次檢測、相鄰合併")
    print(f"🔀 融合策略: {fusion_strategy}")
    print(f"🧠 整體推理: {'啟用' if enable_overall_inference else '禁用'}")
    print(f"🔗 相鄰合併: {'啟用' if enable_adjacent_merge else '禁用'}")
    if preview_mode and not preview_only:
        print(f"📷 Preview mode: Enabled (Enhanced preview generated)")
    print("=" * 70)

    try:
        # 創建配置
        print("🔧 創建配置...")
        config_manager = create_config_from_params()

        # 驗證配置
        print("🔍 驗證配置...")
        errors = config_manager.validate_config()
        if any(errors.values()):
            print("❌ 配置錯誤:")
            for category, error_list in errors.items():
                if error_list:
                    for error in error_list:
                        print(f"  {category}: {error}")
            return

        print("✅ 配置驗證通過")

        # 顯示配置摘要
        print("\n📋 配置摘要:")
        print(config_manager.get_summary())

        # 檢查輸入路徑
        if not Path(input_path).exists():
            print(f"❌ 輸入路徑不存在: {input_path}")
            print("請修改 input_path 參數")
            return

        # 準備排除類別列表
        excluded_classes = []
        if excluded_class_ids:
            excluded_classes.extend(excluded_class_ids)
        if excluded_class_names:
            excluded_classes.extend(excluded_class_names)

        # 🏷️ 創建LabelMe JSON整合器
        from models.inference.labelme_integration import create_labelme_integration
        labelme_integration = create_labelme_integration(config_manager)

        # 🔍 調試：檢查LabelMe整合器狀態
        print(f"🔍 LabelMe整合器調試信息:")
        print(f"   enabled: {labelme_integration.enabled}")
        print(f"   output_dir: {labelme_integration.output_dir}")
        print(
            f"   config.enable_labelme_output: {getattr(config_manager.labelme_output, 'enable_labelme_output', 'Not Set')}")

        # 🔧 根據配置選擇推理模式
        if enable_advanced_slice_inference:
            # 🚀 高級切片推理模式
            print(f"\n🔧 初始化推理引擎 (高級切片推理模式)...")

            # 創建高級推理系統（在ROI之後運行）
            from models.inference.advanced_slice_inference import create_advanced_slice_inference
            from ultralytics import YOLO

            # 加載YOLO模型
            if segmentation_model_path:
                model = YOLO(segmentation_model_path)
                print(f"📦 已加載分割模型: {segmentation_model_path}")
            else:
                raise ValueError("高級推理模式需要設定 segmentation_model_path")

            # 創建高級推理系統（在ROI之後運行）
            inference = create_advanced_slice_inference(
                model=model,
                slice_height=advanced_slice_height,
                slice_width=advanced_slice_width,
                overlap_ratio=advanced_overlap_ratio,
                fusion_strategy=fusion_strategy,
                fusion_iou_threshold=fusion_iou_threshold,
                fusion_confidence_threshold=fusion_confidence_threshold,
                enable_overall_inference=enable_overall_inference,
                enable_adjacent_merge=enable_adjacent_merge,
                excluded_classes=excluded_classes,
                included_class_ids=included_class_ids,
                log_level="lightweight",
                class_confidence_thresholds={cid: c[3]
                                             for cid, c in class_configs.items()},
                class_display_info={cid: {
                    'name': c[0], 'display_name': c[1], 'color': c[2]} for cid, c in class_configs.items()},
                label_aliases=label_aliases,
                font_size=font_size,
                font_thickness=font_thickness,
                enable_roi_preview=config_manager.visualization.enable_roi_preview,
                fill_alpha=fill_alpha,
                mask_render_mode=mask_render_mode
            )

            # 🎨 設置mask渲染模式
            print(f"🎨 Mask渲染模式: {inference.mask_render_mode}")

            # 為兼容性創建傳統組件
            from models.inference.unified_yolo_inference import FontManager, ThreeViewGenerator

            font_manager = FontManager(
                font_path=config_manager.visualization.font_path,
                font_size=config_manager.visualization.font_size,
                font_thickness=config_manager.visualization.font_thickness,
                font_scale=config_manager.visualization.font_scale
            )

            three_view_generator = ThreeViewGenerator(
                font_manager=font_manager,
                config_manager=config_manager,
                layout=config_manager.visualization.three_view_layout,
                spacing=config_manager.visualization.three_view_spacing,
                excluded_classes=excluded_classes  # 🆕 傳遞排除類別信息
            )

            # 創建高級推理包裝器以兼容現有接口
            from models.inference.advanced_inference_wrapper import AdvancedInferenceWrapper
            inference_wrapper = AdvancedInferenceWrapper(
                advanced_inference=inference,
                config_manager=config_manager,
                font_manager=font_manager,
                three_view_generator=three_view_generator
            )

            inference = inference_wrapper
            print("✅ 高級切片推理引擎初始化完成")

        else:
            # 🔧 一般推理模式
            print(f"\n🔧 初始化推理引擎 (一般推理模式)...")

            from models.inference.unified_yolo_inference import UnifiedYOLOInference

            # 創建一般推理引擎
            inference = UnifiedYOLOInference(
                config_path=None,
                labelme_integration=labelme_integration  # 傳遞LabelMe整合器
            )

            # 手動設置配置管理器以覆蓋預設值
            inference.config_manager = config_manager
            inference.three_view_generator.config_manager = config_manager  # 關鍵修復：確保三視圖生成器使用最新的配置

            # 🎯 為一般推理模式啟用ROI功能
            if enable_roi_preview:
                # 啟用SAHI配置以支援ROI預覽
                inference.config_manager.sahi.enable_sahi = True
                inference.config_manager.sahi.enable_sahi_roi = True
                print(f"✅ 已啟用ROI預覽功能")

            # 設定排除類別
            inference.config_manager.excluded_class_ids = excluded_class_ids
            inference.config_manager.excluded_class_names = excluded_class_names
            inference.config_manager.included_class_ids = included_class_ids

            # 重新加載模型以使用新的配置
            if segmentation_model_path:
                from ultralytics import YOLO
                inference.model = YOLO(segmentation_model_path)
                inference.model.to(config_manager.model.device)
                print(f"📦 已加載分割模型: {segmentation_model_path}")
            else:
                raise ValueError("一般推理模式需要設定 segmentation_model_path")

            # 更新ThreeViewGenerator以包含排除類別
            inference.three_view_generator.excluded_classes = excluded_classes

            # 🧹 設置記憶體管理參數
            inference.memory_cleanup_interval = memory_cleanup_interval

            # 🆕 設置積極記憶體清理模式
            if aggressive_memory_cleanup:
                inference.memory_cleanup_interval = max(
                    1, memory_cleanup_interval // 2)  # 更頻繁的清理
                print(f"⚡ 積極記憶體清理模式已啟用")

            # 🖼️ 設置圖像記憶體釋放參數
            inference.enable_image_memory_release = enable_image_memory_release
            inference.enable_detection_memory_release = enable_detection_memory_release
            inference.batch_memory_cleanup_interval = batch_memory_cleanup_interval
            inference.enable_batch_final_cleanup = enable_batch_final_cleanup

            # 🔄 設置中斷回復功能
            if enable_resume:
                inference.setup_resume(
                    enable_resume=enable_resume,
                    checkpoint_interval=resume_checkpoint_interval,
                    skip_existing=resume_skip_existing,
                    progress_file=resume_progress_file,
                    force_overwrite=resume_force_overwrite
                )
                print(f"🔄 中斷回復功能已啟用:")
                print(f"   📁 進度文件: {resume_progress_file}")
                print(f"   💾 檢查點間隔: 每{resume_checkpoint_interval}張圖像")
                print(f"   ⏭️ 跳過已處理: {'是' if resume_skip_existing else '否'}")
                print(f"   🔄 強制覆蓋: {'是' if resume_force_overwrite else '否'}")

            if enable_memory_monitoring:
                print(f"🧹 記憶體管理已啟用:")
                print(f"   📊 清理間隔: 每{inference.memory_cleanup_interval}張圖像")
                print(
                    f"   ⚡ 積極清理: {'啟用' if aggressive_memory_cleanup else '禁用'}")
                inference.log_memory_usage()  # 記錄初始記憶體使用

            print("✅ 一般推理引擎初始化完成")

        # 創建輸出目錄
        output_dir = Path(output_path)
        if auto_create_output_dirs:
            output_dir.mkdir(parents=True, exist_ok=True)
            (output_dir / "images").mkdir(exist_ok=True)
            (output_dir / "reports").mkdir(exist_ok=True)

        # 執行推理
        print(f"\n🚀 開始推理...")
        print(f"📁 輸入: {input_path}")
        print(f"📁 輸出: {output_path}")

        print(f"🧠 智能過濾: {'啟用' if enable_intelligent_filtering else '禁用'}")
        print(f"🎨 三視圖: {'啟用' if enable_three_view_output else '禁用'}")
        print(f"🏷️ JSON掃描: {'啟用' if enable_json_scan else '禁用'}")

        if batch_processing and Path(input_path).is_dir():
            # 批次處理
            print(f"📂 批次處理模式（逐張LabelMe生成 + 中斷繼續）")
            json_dir_param = labelme_dir if enable_json_scan else None
            # 根據推理模式使用不同的調用方式
            if enable_advanced_slice_inference:
                # 高級切片推理支援額外參數
                results = inference.predict_batch(
                    input_path,
                    output_path,
                    json_dir=json_dir_param,
                    labelme_integration=labelme_integration,
                    enable_resume=enable_resume  # 🔄 傳遞中斷回復參數
                )
            else:
                # 一般推理支援中斷回復參數
                results = inference.predict_batch(
                    input_path,
                    output_path,
                    json_dir=json_dir_param,
                    enable_resume=enable_resume  # 🔄 傳遞中斷回復參數
                )

            print(f"✅ 批次處理完成! 新處理了 {len(results)} 張圖像")

            # 🏷️ LabelMe JSON統計總結（已在逐張處理中完成）
            if labelme_integration and labelme_integration.enabled:
                labelme_dir = Path(labelme_integration.output_dir)
                if labelme_dir.exists():
                    json_files = list(labelme_dir.glob("*.json"))
                    print(f"\n🎉 LabelMe JSON處理總結:")
                    print(f"   📁 輸出目錄: {labelme_integration.output_dir}")
                    print(f"   📊 JSON檔案總數: {len(json_files)} 個")
                    print(f"   🎯 可在LabelMe工具中直接開啟使用")

                    # 顯示最近生成的檔案
                    if json_files:
                        recent_files = sorted(
                            json_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
                        print(f"   📝 最近生成的檔案:")
                        for json_file in recent_files:
                            print(f"      📄 {json_file.name}")
                else:
                    print(
                        f"\n❌ LabelMe JSON目錄不存在: {labelme_integration.output_dir}")
            else:
                print(f"\nℹ️ LabelMe JSON功能未啟用")

            # 📷 ROI和切片預覽：已在第一張圖像處理前生成綜合預覽

            # 顯示實時統計
            print("\n" + inference.get_current_statistics_summary())

            # 顯示CSV報告位置
            csv_dir = Path(output_path) / "reports"
            print(f"\n📊 CSV報告位置:")
            print(f"  📄 圖像級別: {csv_dir}/image_metrics_incremental.csv")
            print(f"  📄 類別級別: {csv_dir}/class_metrics_incremental.csv")
            print(f"  ⚡ 增量更新: 每處理一張圖像立即更新CSV")

            # 🧹 最終記憶體使用報告
            if enable_memory_monitoring:
                print(f"\n🧹 最終記憶體使用報告:")
                inference.log_memory_usage()
                if force_cleanup_on_completion:
                    print(f"  🔄 執行最終記憶體清理...")
                    inference.cleanup_memory(force=True)

        else:
            # 單張圖像處理
            print(f"🖼️  單張圖像處理模式")

            _input_path = input_path  # Create a local copy to avoid UnboundLocalError

            # 單張圖像處理時，如果啟用JSON掃描，先掃描一下
            if enable_json_scan and labelme_dir and Path(labelme_dir).exists():
                print(f"\n🔍 掃描JSON標籤目錄: {labelme_dir}")
                labels_stats = inference.scan_json_labels(labelme_dir)
                if display_json_summary:
                    summary = inference.display_json_labels_summary(
                        labels_stats)
                    print(summary)

            # 確保 _input_path 指向一個圖像文件
            if Path(_input_path).is_dir():
                # 如果 _input_path 是目錄，則取第一個圖像文件
                image_files = list(Path(_input_path).glob(
                    "*.jpg")) + list(Path(_input_path).glob("*.png"))
                if image_files:
                    _input_path = str(image_files[0])
                    print(f"已自動選擇目錄中的第一張圖像進行測試: {_input_path}")
                else:
                    print(f"❌ 輸入目錄 {_input_path} 中沒有找到圖像文件")
                    return

            # 根據推理模式調用不同的方法
            if enable_advanced_slice_inference:
                result = inference.predict_single_image(
                    _input_path, output_path)
            else:
                # 一般推理模式：明確啟用ROI預覽
                result = inference.predict_single_image(
                    _input_path,
                    output_path,
                    save_roi_preview=enable_roi_preview
                )
            print(f"✅ 單張處理完成! 檢測到 {len(result['detections'])} 個目標")

            # 📷 ROI和切片預覽：單張模式不需要預覽（用於快速測試）

            # 顯示檢測結果
            if result['detections']:
                print("\n🎯 檢測結果:")
                for det in result['detections']:
                    class_name = det['class_name']
                    confidence = det['confidence']
                    display_name = config_manager.get_class_display_name(
                        det['class_id'])
                    print(f"  {display_name}: {confidence:.3f}")

            # 顯示統計信息
            if inference.stats['processing_times']:
                avg_time = sum(
                    inference.stats['processing_times']) / len(inference.stats['processing_times'])
                print(f"\n⏱️  平均處理時間: {avg_time:.2f}秒/張")
                print(f"🚀 處理速度: {1/avg_time:.1f} FPS")

            print(f"\n🎉 推理完成! 結果保存至: {output_path}")

            # 功能摘要
            print("\n" + "=" * 70)
            print("🌟 統一YOLO推理系統功能摘要:")
            enabled_classes = sum(1 for _, _, _, _, _,
                                  enabled in class_configs.values() if enabled)
            print(f"  ✅ 類別配置: {enabled_classes}/{len(class_configs)} 個類別啟用")
            print(f"  ✅ 高級切片推理: {'啟用' if True else '禁用'}")
            print(f"  ✅ ROI處理: {'啟用' if enable_roi_processing else '禁用'}")
            print(
                f"  ✅ 智能過濾: {'啟用' if enable_intelligent_filtering else '禁用'}")
            print(f"  ✅ 三視圖輸出: {'啟用' if enable_three_view_output else '禁用'}")
            print(
                f"  ✅ LabelMe JSON輸出: {'啟用' if enable_labelme_output else '禁用'}")
            print(f"  ✅ 字體設定: 大小x{font_size}, 粗細{font_thickness}")
            print(f"  ✅ 圖像質量: {output_image_quality}%")
            print(
                f"  ✅ 記憶體管理: {'啟用' if enable_memory_monitoring else '禁用'} (每{memory_cleanup_interval}張圖像清理)")
            print(
                f"  ✅ 圖像記憶體釋放: {'啟用' if enable_image_memory_release else '禁用'}")
            print(
                f"  ✅ 檢測記憶體釋放: {'啟用' if enable_detection_memory_release else '禁用'}")
            print(
                f"  ✅ 批次深度清理: 每{batch_memory_cleanup_interval}張圖像")
            print(
                f"  ✅ 批次最終清理: {'啟用' if enable_batch_final_cleanup else '禁用'}")
            if enable_intelligent_filtering:
                print(
                    f"  ✅ Step1過濾: 長寬比<{linear_aspect_ratio_threshold}, 面積比<{area_ratio_threshold}")
                print(f"  ✅ Step2過濾: IoU>{step2_iou_threshold}")

            print("\n💡 使用提示:")
            print("  1. 修改頂部參數區域來調整配置")
            print("  2. 類別配置支援獨立的confidence閾值")
            print("  3. 智能過濾可減少linear_crack和其他類別的衝突")
            print("  4. 🆕 三視圖自動加載GT標註，完美匹配原始格式")
            print("  5. 🆕 CSV增量更新，實時查看處理進度")
            print("  6. 🆕 智能mask渲染，解決過暗問題")
            print("  7. 🆕 LabelMe JSON輸出，將預測結果轉換為LabelMe格式")
            print("  8. 🆕 類別排除功能：excluded_class_ids、excluded_class_names")
            print("  9. 🆕 Fusion參數完整支援：IoU和置信度閾值可調")
            print("  10. 🧹 記憶體管理：自動清理CPU/GPU記憶體，避免記憶體洩漏")
            print(
                f"  11. 🔄 中斷回復：{'啟用' if enable_resume else '禁用'} - 支持從中斷點繼續處理")
            print("\n🔧 新功能說明:")
            print("  ⚡ CSV增量更新: 每張圖像處理完立即寫入CSV，不用等批次結束")
            print("  🖼️  增強三視圖: 自動載入GT標註，顯示黃色polygon標記")
            print(f"  🎨 智能mask渲染: {inference.mask_render_mode}模式，避免重疊變暗")
            print("     - unified: 統一mask圖層，一次性混合（最推薦）")
            print("     - intelligent: 智能重疊處理，極低alpha值")
            print("     - simple: 簡單極低alpha混合")
            print("     - outline_only: 只繪製邊框，完全避免變暗")
            if enable_labelme_output:
                labelme_stats = labelme_integration.get_output_stats()
                print(f"  🏷️ LabelMe JSON輸出: 啟用，保存至 {labelme_output_dir}/ 目錄")
                print(
                    f"     - 簡化容差: {labelme_simplify_tolerance} (數值越大polygon越簡化)")
                print(f"     - 最小點數: {labelme_min_polygon_points} (少於此數會被忽略)")
                print(
                    f"     - 包含confidence: {'是' if labelme_include_confidence else '否'}")
            else:
                print("  🏷️ LabelMe JSON輸出: 禁用")

            # 🧹 記憶體管理功能說明
            if enable_memory_monitoring:
                print(f"  🧹 記憶體管理功能:")
                print(f"     - 輕量級清理: 每張圖像處理後執行基礎清理")
                print(f"     - 深度清理間隔: 每{memory_cleanup_interval}張圖像完整清理")
                print(
                    f"     - 積極清理模式: {'啟用' if aggressive_memory_cleanup else '禁用'}")
            else:
                print("  🧹 記憶體管理: 禁用 (可能導致大量圖像處理時記憶體不足)")

            # 🔄 中斷回復功能說明
            if enable_resume:
                print(f"  🔄 中斷回復功能:")
                print(f"     - 進度追蹤: 自動保存處理進度到 {resume_progress_file}")
                print(f"     - 檢查點: 每{resume_checkpoint_interval}張圖像保存一次進度")
                print(
                    f"     - 智能跳過: {'自動跳過已處理文件' if resume_skip_existing else '重新處理所有文件'}")
                print(f"     - 文件檢測: 檢查可視化和三視圖輸出文件是否存在")
                print(f"     - 中斷恢復: Ctrl+C中斷後重新運行即可繼續")
                print(
                    f"     - 進度查看: 查看 {output_path}/{resume_progress_file} 了解處理狀態")
                print(
                    f"     - 覆蓋模式: {'強制覆蓋已存在文件' if resume_force_overwrite else '跳過已存在文件'}")
            else:
                print("  🔄 中斷回復: 禁用 (中斷後需要重新開始處理)")

    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷執行")
        if enable_resume:
            print(f"💾 處理進度已自動保存到: {output_path}/{resume_progress_file}")
            print(f"🔄 重新運行此腳本即可從中斷點繼續處理")
            print(f"📊 當前已處理的圖像不會重複處理")
        else:
            print("💡 建議啟用中斷回復功能 (enable_resume = True) 以支持斷點續傳")
    except Exception as e:
        print(f"\n 執行失敗: {e}")
        import traceback
        traceback.print_exc()
        print("\n 常見問題解決:")
        print("  1. 路徑問題: 確認 input_path, output_path, labelme_dir 路徑正確")
        print("  2. 模型問題: 確認 segmentation_model_path 指向有效的YOLO模型")
        print("  3. GT問題: 確認 labelme_dir 包含與圖像對應的.json標註檔案")
        print("  4. 權限問題: 確認對輸出目錄有寫入權限")
        print("\n 更多幫助請查看: road_ai_framework/MASK_DIMENSION_FIX_SUMMARY.md")


if __name__ == "__main__":
    # ==================== 🎯 配置總結 ====================
    if preview_mode:
        print("📷 Enhanced Preview Mode Configuration:")
        print(f"   👁️  Mode: ROI and Slice Preview (Enhanced)")
        print(f"   📁 Input: {input_path} (First image only)")
        print(f"   📁 Output: {output_path}/preview/")
        print(
            f"   🎯 ROI Config: Top{roi_top_ratio} Bottom{roi_bottom_ratio} Left{roi_left_ratio} Right{roi_right_ratio}")
        print(
            f"   🧩 Slice Size: {advanced_slice_height}x{advanced_slice_width} (Overlap {advanced_overlap_ratio:.1%})")
        if preview_only:
            print("   ⚠️  Preview only mode - No inference will be performed")
    else:
        print("📋 當前配置總結:")
        print(f"   🤖 模型: {segmentation_model_path}")
        print(f"   🚀 推理引擎: 高級切片推理模式")
        print(f"   🔀 融合策略: {fusion_strategy}")
        print(f"   🧩 切片大小: {advanced_slice_height}x{advanced_slice_width}")
        print(f"   🎯 全局閾值: {global_conf}")
        print(
            f"   🏷️  已啟用類別: {sum(1 for _, _, _, _, _, enabled in class_configs.values() if enabled)}/{len(class_configs)}")
        print(f"   📁 輸入: {input_path}")
        print(f"   📁 輸出: {output_path}")
        print(f"   🏷️ LabelMe JSON: {'啟用' if enable_labelme_output else '禁用'}")
        if enable_labelme_output:
            print(f"   📁 JSON輸出: {output_path}/{labelme_output_dir}/")
    print("=" * 70)

    main()
