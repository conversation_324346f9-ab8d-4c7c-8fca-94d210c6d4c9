import os
import shutil
import re
import pandas as pd
from PIL import Image


def extract_timestamp(filename):
    """從檔案名稱中提取最後一個底線後的時間戳記"""
    match = re.search(r'_(\d+)\.jpg$', filename)
    if match:
        return match.group(1)
    return None


def merge_matching_images_and_csv(folder1, folder2, output_folder, high_conf_folder, csv_path, conf_threshold=0.8):
    """尋找並合併基於時間戳記的匹配圖像，同時處理相應的CSV資料"""
    # 創建輸出資料夾（如果不存在）
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(high_conf_folder, exist_ok=True)

    # 讀取CSV檔案
    csv_data = pd.read_csv(csv_path)

    # 從CSV中提取時間戳記並建立映射
    csv_timestamps = {}
    for _, row in csv_data.iterrows():
        filename = str(row['檔案名稱'])
        timestamp = extract_timestamp(filename)
        if timestamp:
            if timestamp not in csv_timestamps:
                csv_timestamps[timestamp] = []
            csv_timestamps[timestamp].append(row)

    # 獲取兩個資料夾中的所有jpg檔案
    files1 = [f for f in os.listdir(folder1) if f.lower().endswith('.jpg')]
    files2 = [f for f in os.listdir(folder2) if f.lower().endswith('.jpg')]

    # 創建時間戳記到檔案名稱的映射字典
    timestamps1 = {extract_timestamp(
        f): f for f in files1 if extract_timestamp(f)}
    timestamps2 = {extract_timestamp(
        f): f for f in files2 if extract_timestamp(f)}

    # 找到共同的時間戳記
    common_timestamps = set(timestamps1.keys()) & set(timestamps2.keys())

    print(f"找到 {len(common_timestamps)} 對匹配的圖像。")

    # 創建新的CSV資料框用於存儲輸出
    output_csv = pd.DataFrame(columns=csv_data.columns)
    high_conf_csv = pd.DataFrame(columns=csv_data.columns)

    # 處理每一對匹配的圖像
    for timestamp in common_timestamps:
        file1 = os.path.join(folder1, timestamps1[timestamp])
        file2 = os.path.join(folder2, timestamps2[timestamp])

        # 檢查是否已存在合併後的圖像
        merged_filename = f"merged_{timestamp}.jpg"
        merged_path = os.path.join(output_folder, merged_filename)

        # 如果合併的圖像不存在，則創建它
        if not os.path.exists(merged_path):
            # 複製檔案到輸出資料夾並加上命名前綴
            output_file1 = os.path.join(
                output_folder, f"1_{timestamps1[timestamp]}")
            output_file2 = os.path.join(
                output_folder, f"2_{timestamps2[timestamp]}")
            shutil.copy2(file1, output_file1)
            shutil.copy2(file2, output_file2)

            # 創建並儲存側邊合併的圖像
            try:
                img1 = Image.open(file1)
                img2 = Image.open(file2)
                # 調整第二張圖像大小
                img2 = img2.resize((img1.width // 2, img1.height))

                # 創建一個新圖像，寬度=寬度之和，高度=最大高度
                merged_width = img1.width + img2.width
                merged_height = max(img1.height, img2.height)
                merged_img = Image.new('RGB', (merged_width, merged_height))

                # 將圖像並排貼上
                merged_img.paste(img1, (0, 0))
                merged_img.paste(img2, (img1.width, 0))

                # 儲存合併後的圖像
                merged_img.save(merged_path)
                print(f"創建合併圖像: {merged_filename}")
            except Exception as e:
                print(f"合併時間戳記為 {timestamp} 的圖像時出錯: {e}")
        else:
            print(f"已存在合併圖像: {merged_filename}，跳過合併步驟")

        # 處理CSV資料
        if timestamp in csv_timestamps:
            timestamp_rows = csv_timestamps[timestamp]

            # 將匹配的行添加到輸出CSV
            for row in timestamp_rows:
                row_df = pd.DataFrame([row])
                output_csv = pd.concat([output_csv, row_df], ignore_index=True)

                # 檢查置信度並添加到高置信度CSV
                if float(row['置信度']) >= conf_threshold:
                    high_conf_csv = pd.concat(
                        [high_conf_csv, row_df], ignore_index=True)

                    # 複製高置信度圖像到高置信度資料夾
                    if os.path.exists(merged_path):
                        high_conf_image_path = os.path.join(
                            high_conf_folder, merged_filename)
                        if not os.path.exists(high_conf_image_path):
                            shutil.copy2(merged_path, high_conf_image_path)

    # 儲存CSV檔案
    if not output_csv.empty:
        output_csv_path = os.path.join(output_folder, "merged_records.csv")
        output_csv.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
        print(f"儲存合併的CSV到: {output_csv_path}")

    # 儲存高置信度CSV檔案
    if not high_conf_csv.empty:
        high_conf_csv_path = os.path.join(
            high_conf_folder, "high_confidence_records.csv")
        high_conf_csv.to_csv(high_conf_csv_path,
                             index=False, encoding='utf-8-sig')
        print(f"儲存高置信度CSV到: {high_conf_csv_path}")
        print(f"找到 {len(high_conf_csv)} 筆高置信度記錄（置信度 >= {conf_threshold}）")


# 使用範例
if __name__ == "__main__":
    # 請將這些替換為您實際的資料夾路徑
    source_folder1 = r"D:\new_model_pipline\yolov11seg_onroad_0516"
    source_folder2 = r"D:\new_model_pipline\0516"
    output_folder = r"D:\new_model_pipline\out-0516"
    high_conf_folder = r"D:\new_model_pipline\high_conf-0516"
    # 請替換為您的CSV檔案路徑
    csv_path = r"D:\new_model_pipline\yolov11seg_onroad_0516\segment_statistics.csv"

    # 設定置信度閾值（預設為0.8）
    confidence_threshold = 0.8

    merge_matching_images_and_csv(source_folder1, source_folder2, output_folder,
                                  high_conf_folder, csv_path, confidence_threshold)
