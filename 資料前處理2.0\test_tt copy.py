import os
import json

def delete_class_from_dataset(dataset_folder, class_to_delete):
    """
    從數據集中刪除指定類別的所有圖像和 JSON 檔案。

    Args:
        dataset_folder (str): 數據集根資料夾的路徑。
        class_to_delete (str): 要刪除的類別名稱。
    """
    print(f"--- 正在準備刪除類別 '{class_to_delete}' ---")
    subfolders = ["train", "val", "test"]
    deleted_count = 0

    for subfolder in subfolders:
        current_path = os.path.join(dataset_folder, subfolder)
        if not os.path.isdir(current_path):
            print(f"警告: 資料夾 '{current_path}' 不存在，將跳過。")
            continue

        print(f"正在掃描 '{current_path}' 以尋找要刪除的檔案...")
        
        # 為了避免在遍歷時修改列表導致問題，先收集要刪除的檔案路徑
        files_to_delete = []

        for filename in os.listdir(current_path):
            if filename.lower().endswith(".json"):
                json_filepath = os.path.join(current_path, filename)
                try:
                    with open(json_filepath, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                        category = json_data.get("shapes")[0].get("label") # 假設 JSON 中有一個 'category' 鍵
                        
                        if category and category == class_to_delete:
                            # 找到要刪除的類別，記錄 JSON 和對應的圖像檔案
                            image_filename = os.path.splitext(filename)[0] + ".jpg" # 假設圖像檔名與 JSON 檔名相同，只是副檔名不同
                            image_filepath = os.path.join(current_path, image_filename)
                            
                            files_to_delete.append((json_filepath, image_filepath))
                            
                except json.JSONDecodeError:
                    print(f"錯誤: 無法解析 JSON 檔案 '{json_filepath}'，將跳過。")
                except Exception as e:
                    print(f"處理檔案 '{json_filepath}' 時發生錯誤: {e}，將跳過。")
        
        # 執行刪除
        for json_file, image_file in files_to_delete:
            try:
                if os.path.exists(json_file):
                    os.remove(json_file)
                    print(f"已刪除 JSON: {json_file}")
                    deleted_count += 1
                if os.path.exists(image_file):
                    os.remove(image_file)
                    print(f"已刪除圖像: {image_file}")
                else:
                    print(f"警告: 圖像檔案 '{image_file}' 不存在，但對應的 JSON 已刪除。")
            except OSError as e:
                print(f"錯誤: 無法刪除檔案 '{json_file}' 或 '{image_file}': {e}")

    print(f"\n--- 刪除完成！共刪除了 {deleted_count} 個 '{class_to_delete}' 類別的 JSON 檔案及其對應圖像。---")
    if deleted_count == 0:
        print(f"未找到類別 '{class_to_delete}' 的任何檔案進行刪除。請檢查類別名稱是否正確。")

# --- 使用範例 ---
if __name__ == "__main__":
    # 請將 'path/to/your/datset_folder' 替換為你的數據集資料夾的實際路徑
    dataset_root = r"D:\5_Hole_cover\train_20250522_151822\4_augmented_data"

    # 請設定你要刪除的類別名稱
    class_to_remove = "retaining seat"

    if dataset_root == "請_替_換_為_你_的_資_料_夾_路_徑" or class_to_remove == "要_刪_除_的_類_別_名_稱":
        print("請將程式碼中的 '請_替_換_為_你_的_資_料_夾_路_徑' 和 '要_刪_除_的_類_別_名_稱' 替換為實際值，然後再次運行。")
    elif not os.path.isdir(dataset_root):
        print(f"錯誤: 指定的數據集資料夾 '{dataset_root}' 不存在。請檢查路徑。")
    else:
        # 強烈建議在執行刪除前確認
        confirm = input(f"警告: 這將從 '{dataset_root}' 中刪除所有類別為 '{class_to_remove}' 的圖像和 JSON 檔案。您確定要繼續嗎？ (y/N): ").lower()
        if confirm == 'y':
            delete_class_from_dataset(dataset_root, class_to_remove)
            print("\n建議運行之前的統計程式，以確認刪除結果。")
        else:
            print("刪除操作已取消。")