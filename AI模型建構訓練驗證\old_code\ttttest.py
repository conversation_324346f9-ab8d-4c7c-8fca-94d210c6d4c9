import os
import logging
import torch
from ultralytics import YOLO
import glob
import matplotlib.pyplot as plt
import cv2
import numpy as np
import time
import pandas as pd
from collections import defaultdict
from PIL import Image, ImageDraw, ImageFont
import json
from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

"""設置日誌"""
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('YOLO_Metrics')

# 設定參數 - 請根據您的實際路徑進行調整
best_weight_path = r'run_data\yolov12x_20250506_115958\weights\best.pt'  # 已訓練好的權重檔案

# 置信度閾值設定（可以在這裡修改）
CONFIDENCE_THRESHOLD = 0.1  # 預設值通常是 0.25，您可以設定為 0.1 到 0.9 之間的任何值
IOU_THRESHOLD = 0.6  # IoU 閾值，用於決定檢測框與真實框是否匹配

# 使用您確認存在的路徑
test_dir = r'D:\5_Hole_cover\image_label_20250506_103929\3_divided_dataset\object_detect\test'

test_images_dir = os.path.join(test_dir, 'images')
test_labels_dir = os.path.join(test_dir, 'labels')

output_dir = r'run_data\yolov12x_20250506_115958\metrics_evaluation'  # 保存評估結果的目錄

# 確保輸出目錄存在
os.makedirs(output_dir, exist_ok=True)

# 類別名稱映射
class_mapping = {'bird': 0, 'block': 1,
                 'retaining seat': 2, 'treefish': 3, 'tpe': 4}
# class_names = {0: 'bird', 1: 'block', 2: 'retaining seat', 3: 'treefish', 4: 'tpe'}
class_names = {0: 'face', 1: 'license_plate', 2: 'mask'}


# 從英文到中文的完整映射
en_to_cn_mapping = {
    'bird': '鳥',
    'block': '方塊',
    'retaining seat': '擋土座',
    'treefish': '樹魚',
    'tpe': '台北蓋水特色框蓋'
}


def verify_test_dir():
    """驗證測試目錄結構並顯示資訊"""
    logger.info(f"檢查測試目錄: {test_dir}")

    if os.path.exists(test_dir):
        logger.info(f"✓ 測試目錄存在")

        # 檢查 images 子目錄
        if os.path.exists(test_images_dir):
            logger.info(f"✓ images 子目錄存在")

            # 檢查圖像文件
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                image_files.extend(
                    glob.glob(os.path.join(test_images_dir, ext)))

            if image_files:
                logger.info(f"✓ 找到 {len(image_files)} 個圖像文件")
                for i in range(min(5, len(image_files))):
                    logger.info(f"  - {os.path.basename(image_files[i])}")
                if len(image_files) > 5:
                    logger.info(f"  - ... 等 {len(image_files)} 個文件")
            else:
                logger.warning(f"✗ 沒有找到任何圖像文件")
        else:
            logger.warning(f"✗ images 子目錄不存在")

        # 檢查 labels 子目錄
        if os.path.exists(test_labels_dir):
            logger.info(f"✓ labels 子目錄存在")

            # 檢查標籤文件
            label_files = glob.glob(os.path.join(test_labels_dir, '*.txt'))
            if label_files:
                logger.info(f"✓ 找到 {len(label_files)} 個標籤文件")

                # 檢查標籤文件內容
                for i in range(min(3, len(label_files))):
                    with open(label_files[i], 'r') as f:
                        content = f.read().strip()
                        logger.info(f"  - {os.path.basename(label_files[i])} 內容: {content[:50]}..." if len(
                            content) > 50 else f"  - {os.path.basename(label_files[i])} 內容: {content}")
            else:
                logger.warning(f"✗ 沒有找到任何標籤文件")
        else:
            logger.warning(f"✗ labels 子目錄不存在")
    else:
        logger.error(f"✗ 測試目錄不存在")
        return False

    return True


def load_model():
    """載入訓練好的模型"""
    logger.info(f"載入權重檔案: {best_weight_path}")

    try:
        # 載入模型
        model = YOLO(best_weight_path)
        logger.info(f"模型載入成功")
        return model
    except Exception as e:
        logger.error(f"模型載入失敗: {str(e)}")
        return None


def calculate_iou(box1, box2):
    """計算兩個框的IoU (Intersection over Union)"""
    # 轉換格式為 [x1, y1, x2, y2]
    if len(box1) == 4 and len(box2) == 4:
        # 計算相交區域
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])

        # 計算相交面積
        if x2 < x1 or y2 < y1:
            return 0.0  # 沒有相交
        intersection = (x2 - x1) * (y2 - y1)

        # 計算各自面積
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])

        # 計算IoU
        iou = intersection / (area1 + area2 - intersection)
        return iou
    else:
        return 0.0


def read_yolo_labels(label_file, img_width, img_height):
    """讀取YOLO格式的標籤文件"""
    boxes = []

    if not os.path.exists(label_file):
        return boxes

    try:
        with open(label_file, 'r') as f:
            lines = f.readlines()

        for line in lines:
            parts = line.strip().split()
            if len(parts) >= 5:  # class_id, x_center, y_center, width, height
                class_id = int(parts[0])
                x_center = float(parts[1]) * img_width
                y_center = float(parts[2]) * img_height
                width = float(parts[3]) * img_width
                height = float(parts[4]) * img_height

                # 轉換為 [x1, y1, x2, y2] 格式
                x1 = x_center - width / 2
                y1 = y_center - height / 2
                x2 = x_center + width / 2
                y2 = y_center + height / 2

                boxes.append({
                    'class_id': class_id,
                    'bbox': [x1, y1, x2, y2]
                })
    except Exception as e:
        logger.error(f"讀取標籤文件時出錯: {str(e)}")

    return boxes


def evaluate_metrics(all_predictions, all_groundtruths, num_classes):
    """計算各種評估指標"""
    # 初始化指標字典
    metrics = {
        'overall': {
            'TP': 0, 'FP': 0, 'FN': 0, 'TN': 0,
            'IoU': 0, 'precision': 0, 'recall': 0, 'specificity': 0, 'f1': 0
        },
        'per_class': {},
        'no_background': {
            'TP': 0, 'FP': 0, 'FN': 0, 'TN': 0,
            'IoU': 0, 'precision': 0, 'recall': 0, 'specificity': 0, 'f1': 0
        }
    }

    # 初始化每個類別的指標
    for i in range(num_classes):
        metrics['per_class'][i] = {
            'TP': 0, 'FP': 0, 'FN': 0, 'TN': 0,
            'IoU': 0, 'precision': 0, 'recall': 0, 'specificity': 0, 'f1': 0,
            'IoU_values': []
        }

    # 遍歷所有圖像
    total_images = len(all_groundtruths)
    if total_images == 0:
        logger.error("沒有找到任何標籤文件")
        return metrics

    # 統計各類別的數量
    gt_class_counts = defaultdict(int)
    pred_class_counts = defaultdict(int)

    # 逐個圖像評估
    for img_id in all_groundtruths.keys():
        if img_id not in all_predictions:
            continue

        gt_boxes = all_groundtruths[img_id]
        pred_boxes = all_predictions[img_id]

        # 標記已匹配的真實框和預測框
        matched_gt = [False] * len(gt_boxes)
        matched_pred = [False] * len(pred_boxes)

        # 記錄每個類別的真實框
        for i, gt in enumerate(gt_boxes):
            class_id = gt['class_id']
            gt_class_counts[class_id] += 1

        # 記錄每個類別的預測框
        for i, pred in enumerate(pred_boxes):
            class_id = pred['class_id']
            pred_class_counts[class_id] += 1

        # 計算每個預測框和每個真實框之間的IoU
        for i, pred in enumerate(pred_boxes):
            pred_class = pred['class_id']
            pred_bbox = pred['bbox']

            best_iou = -1
            best_gt_idx = -1

            # 尋找最匹配的真實框
            for j, gt in enumerate(gt_boxes):
                gt_class = gt['class_id']
                gt_bbox = gt['bbox']

                # 如果類別相同，計算IoU
                if gt_class == pred_class:
                    iou = calculate_iou(pred_bbox, gt_bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = j

            # 如果IoU超過閾值，則匹配成功
            if best_iou >= IOU_THRESHOLD and not matched_gt[best_gt_idx]:
                matched_gt[best_gt_idx] = True
                matched_pred[i] = True

                # 真陽性 (TP) - 整體
                metrics['overall']['TP'] += 1
                # 真陽性 (TP) - 對應類別
                metrics['per_class'][pred_class]['TP'] += 1
                # 記錄IoU值
                metrics['per_class'][pred_class]['IoU_values'].append(best_iou)

                # 非背景類別
                if pred_class > 0:
                    metrics['no_background']['TP'] += 1

        # 計算假陽性 (FP)
        for i, matched in enumerate(matched_pred):
            if not matched:
                pred_class = pred_boxes[i]['class_id']
                metrics['overall']['FP'] += 1
                metrics['per_class'][pred_class]['FP'] += 1

                if pred_class > 0:
                    metrics['no_background']['FP'] += 1

        # 計算假陰性 (FN)
        for i, matched in enumerate(matched_gt):
            if not matched:
                gt_class = gt_boxes[i]['class_id']
                metrics['overall']['FN'] += 1
                metrics['per_class'][gt_class]['FN'] += 1

                if gt_class > 0:
                    metrics['no_background']['FN'] += 1

    # 計算真陰性 (TN) - 對於目標檢測任務，這是較為複雜的
    # 簡化：真陰性可以估計為其他類別的正確拒絕
    for class_id in range(num_classes):
        # 每個類別的TN是其他所有類別的TP + 其他所有類別未檢測到的真實框 (其他類別的FN)
        metrics['per_class'][class_id]['TN'] = 0
        for other_class in range(num_classes):
            if other_class != class_id:
                metrics['per_class'][class_id]['TN'] += (metrics['per_class'][other_class]['TP'] +
                                                         metrics['per_class'][other_class]['FN'])

    # 計算整體TN
    metrics['overall']['TN'] = sum(
        metrics['per_class'][i]['TN'] for i in range(num_classes)) // (num_classes - 1)

    # 計算非背景類別的TN
    bg_tn = metrics['per_class'][0]['TN'] if 0 in metrics['per_class'] else 0
    metrics['no_background']['TN'] = bg_tn

    # 計算各種指標 - 整體
    tp = metrics['overall']['TP']
    fp = metrics['overall']['FP']
    fn = metrics['overall']['FN']
    tn = metrics['overall']['TN']

    if tp + fp > 0:
        metrics['overall']['precision'] = tp / (tp + fp)
    if tp + fn > 0:
        metrics['overall']['recall'] = tp / (tp + fn)
    if tn + fp > 0:
        metrics['overall']['specificity'] = tn / (tn + fp)
    if tp + fp + fn > 0:
        metrics['overall']['IoU'] = tp / (tp + fp + fn)
    if tp + fp + fn + tn > 0:
        metrics['overall']['accuracy'] = (tp + tn) / (tp + fp + fn + tn)
    if metrics['overall']['precision'] + metrics['overall']['recall'] > 0:
        metrics['overall']['f1'] = 2 * metrics['overall']['precision'] * \
            metrics['overall']['recall'] / \
            (metrics['overall']['precision'] + metrics['overall']['recall'])

    # 計算各種指標 - 每個類別
    for class_id in range(num_classes):
        tp = metrics['per_class'][class_id]['TP']
        fp = metrics['per_class'][class_id]['FP']
        fn = metrics['per_class'][class_id]['FN']
        tn = metrics['per_class'][class_id]['TN']

        if tp + fp > 0:
            metrics['per_class'][class_id]['precision'] = tp / (tp + fp)
        if tp + fn > 0:
            metrics['per_class'][class_id]['recall'] = tp / (tp + fn)
        if tn + fp > 0:
            metrics['per_class'][class_id]['specificity'] = tn / (tn + fp)
        if tp + fp + fn > 0:
            metrics['per_class'][class_id]['IoU'] = tp / (tp + fp + fn)
        if tp + fp + fn + tn > 0:
            metrics['per_class'][class_id]['accuracy'] = (
                tp + tn) / (tp + fp + fn + tn)
        if metrics['per_class'][class_id]['precision'] + metrics['per_class'][class_id]['recall'] > 0:
            metrics['per_class'][class_id]['f1'] = 2 * metrics['per_class'][class_id]['precision'] * \
                metrics['per_class'][class_id]['recall'] / (
                    metrics['per_class'][class_id]['precision'] + metrics['per_class'][class_id]['recall'])

        # 平均IoU
        if metrics['per_class'][class_id]['IoU_values']:
            metrics['per_class'][class_id]['mean_IoU'] = sum(
                metrics['per_class'][class_id]['IoU_values']) / len(metrics['per_class'][class_id]['IoU_values'])

    # 計算mIoU
    class_ious = []
    for class_id in range(num_classes):
        if metrics['per_class'][class_id]['IoU'] > 0:
            class_ious.append(metrics['per_class'][class_id]['IoU'])

    metrics['overall']['mIoU'] = sum(
        class_ious) / len(class_ious) if class_ious else 0

    # 計算各種指標 - 非背景類別
    tp = metrics['no_background']['TP']
    fp = metrics['no_background']['FP']
    fn = metrics['no_background']['FN']
    tn = metrics['no_background']['TN']

    if tp + fp > 0:
        metrics['no_background']['precision'] = tp / (tp + fp)
    if tp + fn > 0:
        metrics['no_background']['recall'] = tp / (tp + fn)
    if tn + fp > 0:
        metrics['no_background']['specificity'] = tn / (tn + fp)
    if tp + fp + fn > 0:
        metrics['no_background']['IoU'] = tp / (tp + fp + fn)
    if tp + fp + fn + tn > 0:
        metrics['no_background']['accuracy'] = (tp + tn) / (tp + fp + fn + tn)
    if metrics['no_background']['precision'] + metrics['no_background']['recall'] > 0:
        metrics['no_background']['f1'] = 2 * metrics['no_background']['precision'] * \
            metrics['no_background']['recall'] / \
            (metrics['no_background']['precision'] +
             metrics['no_background']['recall'])

    # 添加類別統計
    metrics['class_counts'] = {
        'groundtruth': dict(gt_class_counts),
        'prediction': dict(pred_class_counts)
    }

    return metrics


def run_metrics_evaluation(model, num_images=None):
    """執行指標評估"""
    logger.info("開始進行指標評估...")

    # 獲取測試圖像
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(os.path.join(test_images_dir, ext)))

    if not image_files:
        logger.error(f"在 {test_images_dir} 找不到任何圖像檔案")
        return

    # 限制樣本數量
    if num_images is not None and num_images < len(image_files):
        sample_images = image_files[:num_images]
        logger.info(f"限制為前 {num_images} 張圖像")
    else:
        sample_images = image_files
        logger.info(f"使用全部 {len(sample_images)} 張圖像")

    # 創建評估結果目錄
    eval_dir = os.path.join(output_dir, 'evaluation')
    os.makedirs(eval_dir, exist_ok=True)

    # 初始化收集預測和真實標籤的字典
    all_predictions = {}  # 存儲所有預測結果
    all_groundtruths = {}  # 存儲所有真實標籤

    # 逐一處理圖像
    for i, img_path in enumerate(sample_images):
        # 獲取圖像ID（不含副檔名）
        img_id = os.path.splitext(os.path.basename(img_path))[0]

        # 讀取原始圖像，獲取寬高
        img = cv2.imread(img_path)
        if img is None:
            logger.error(f"無法讀取圖像: {img_path}")
            continue

        img_height, img_width = img.shape[:2]

        # 獲取對應的標籤文件路徑
        label_path = os.path.join(test_labels_dir, f"{img_id}.txt")

        # 讀取真實標籤
        gt_boxes = read_yolo_labels(label_path, img_width, img_height)
        all_groundtruths[img_id] = gt_boxes

        # 進行預測
        results = model.predict(
            source=img_path,
            conf=CONFIDENCE_THRESHOLD,
            iou=IOU_THRESHOLD,
            verbose=False
        )

        # 收集預測框
        result = results[0]
        pred_boxes = []

        if hasattr(result, 'boxes') and result.boxes is not None:
            boxes = result.boxes

            for j in range(len(boxes)):
                cls_id = int(boxes.cls[j].item())
                conf = float(boxes.conf[j].item())
                x1, y1, x2, y2 = boxes.xyxy[j].tolist()

                pred_boxes.append({
                    'class_id': cls_id,
                    'conf': conf,
                    'bbox': [x1, y1, x2, y2]
                })

        all_predictions[img_id] = pred_boxes

        # 繪製檢測結果與真實標籤的對比
        vis_img = img.copy()

        # 先繪製真實標籤 (綠色)
        for box in gt_boxes:
            x1, y1, x2, y2 = box['bbox']
            class_id = box['class_id']
            class_name = class_names.get(class_id, f'類別{class_id}')

            cv2.rectangle(vis_img, (int(x1), int(y1)),
                          (int(x2), int(y2)), (0, 255, 0), 2)
            cv2.putText(vis_img, f"GT:{class_name}", (int(x1), int(y1) - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 再繪製預測框 (紅色)
        for box in pred_boxes:
            x1, y1, x2, y2 = box['bbox']
            class_id = box['class_id']
            conf = box['conf']
            class_name = class_names.get(class_id, f'類別{class_id}')

            cv2.rectangle(vis_img, (int(x1), int(y1)),
                          (int(x2), int(y2)), (0, 0, 255), 2)
            cv2.putText(vis_img, f"PR:{class_name} {conf:.2f}", (int(x1), int(y1) - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

        # 保存可視化結果
        vis_path = os.path.join(eval_dir, f"compare_{img_id}.jpg")
        cv2.imwrite(vis_path, vis_img)

        # 報告進度
        if (i + 1) % 10 == 0:
            logger.info(f"已處理 {i+1}/{len(sample_images)} 張圖像")

    # 計算所有指標
    metrics = evaluate_metrics(
        all_predictions, all_groundtruths, len(class_names))

    # 保存指標結果
    metrics_path = os.path.join(output_dir, 'all_metrics.json')
    with open(metrics_path, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, indent=2, ensure_ascii=False)

    # 生成指標報告（文字格式）
    report_path = os.path.join(output_dir, 'metrics_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("===== YOLOv12 模型評估指標報告 =====\n\n")
        f.write(f"模型: {os.path.basename(best_weight_path)}\n")
        f.write(f"置信度閾值: {CONFIDENCE_THRESHOLD}\n")
        f.write(f"IoU閾值: {IOU_THRESHOLD}\n")
        f.write(f"測試圖像數量: {len(sample_images)}\n\n")

        # 輸出整體指標
        f.write("===== 整體指標 =====\n")
        f.write(f"TP (真陽性): {metrics['overall']['TP']}\n")
        f.write(f"FP (假陽性): {metrics['overall']['FP']}\n")
        f.write(f"FN (假陰性): {metrics['overall']['FN']}\n")
        f.write(f"TN (真陰性): {metrics['overall']['TN']}\n")
        f.write(f"IoU: {metrics['overall']['IoU']:.4f}\n")
        f.write(f"mIoU: {metrics['overall']['mIoU']:.4f}\n")
        f.write(f"Precision (精確率): {metrics['overall']['precision']:.4f}\n")
        f.write(
            f"Recall/Sensitivity (召回率/敏感度): {metrics['overall']['recall']:.4f}\n")
        f.write(
            f"Specificity (特異度): {metrics['overall']['specificity']:.4f}\n")
        f.write(f"F1 Score (F1分數): {metrics['overall']['f1']:.4f}\n")
        if 'accuracy' in metrics['overall']:
            f.write(f"Accuracy (準確率): {metrics['overall']['accuracy']:.4f}\n")
        f.write("\n")

        # 輸出非背景類別指標
        f.write("===== 非背景類別指標 =====\n")
        f.write(f"TP (真陽性): {metrics['no_background']['TP']}\n")
        f.write(f"FP (假陽性): {metrics['no_background']['FP']}\n")
        f.write(f"FN (假陰性): {metrics['no_background']['FN']}\n")
        f.write(f"TN (真陰性): {metrics['no_background']['TN']}\n")
        f.write(f"IoU: {metrics['no_background']['IoU']:.4f}\n")
        f.write(
            f"Precision (精確率): {metrics['no_background']['precision']:.4f}\n")
        f.write(
            f"Recall/Sensitivity (召回率/敏感度): {metrics['no_background']['recall']:.4f}\n")
        f.write(
            f"Specificity (特異度): {metrics['no_background']['specificity']:.4f}\n")
        f.write(f"F1 Score (F1分數): {metrics['no_background']['f1']:.4f}\n")
        if 'accuracy' in metrics['no_background']:
            f.write(
                f"Accuracy (準確率): {metrics['no_background']['accuracy']:.4f}\n")
        f.write("\n")

        # 輸出每個類別的指標
        f.write("===== 各類別指標 =====\n\n")
        for class_id in range(len(class_names)):
            class_name = class_names.get(class_id, f'類別{class_id}')
            f.write(f"--- {class_name} ---\n")
            f.write(f"TP (真陽性): {metrics['per_class'][class_id]['TP']}\n")
            f.write(f"FP (假陽性): {metrics['per_class'][class_id]['FP']}\n")
            f.write(f"FN (假陰性): {metrics['per_class'][class_id]['FN']}\n")
            f.write(f"TN (真陰性): {metrics['per_class'][class_id]['TN']}\n")
            f.write(f"IoU: {metrics['per_class'][class_id]['IoU']:.4f}\n")
            if 'mean_IoU' in metrics['per_class'][class_id]:
                f.write(
                    f"Mean IoU: {metrics['per_class'][class_id]['mean_IoU']:.4f}\n")
            f.write(
                f"Precision (精確率): {metrics['per_class'][class_id]['precision']:.4f}\n")
            f.write(
                f"Recall/Sensitivity (召回率/敏感度): {metrics['per_class'][class_id]['recall']:.4f}\n")
            f.write(
                f"Specificity (特異度): {metrics['per_class'][class_id]['specificity']:.4f}\n")
            f.write(
                f"F1 Score (F1分數): {metrics['per_class'][class_id]['f1']:.4f}\n")
            if 'accuracy' in metrics['per_class'][class_id]:
                f.write(
                    f"Accuracy (準確率): {metrics['per_class'][class_id]['accuracy']:.4f}\n")
            f.write("\n")

        # 輸出類別統計
        f.write("===== 類別數量統計 =====\n")
        f.write("真實標籤 (Ground Truth):\n")
        for class_id, count in metrics['class_counts']['groundtruth'].items():
            class_name = class_names.get(int(class_id), f'類別{class_id}')
            f.write(f"  {class_name}: {count}\n")

        f.write("\n預測結果 (Prediction):\n")
        for class_id, count in metrics['class_counts']['prediction'].items():
            class_name = class_names.get(int(class_id), f'類別{class_id}')
            f.write(f"  {class_name}: {count}\n")

    # 生成CSV格式的報告
    csv_path = os.path.join(output_dir, 'metrics_summary.csv')
    with open(csv_path, 'w', encoding='utf-8-sig', newline='') as f:
        import csv
        writer = csv.writer(f)

        # 寫入標題
        writer.writerow(['類別', 'TP', 'FP', 'FN', 'TN', 'IoU', 'Precision',
                        'Recall/Sensitivity', 'Specificity', 'F1 Score', 'Accuracy'])

        # 寫入整體指標
        writer.writerow([
            '整體',
            metrics['overall']['TP'],
            metrics['overall']['FP'],
            metrics['overall']['FN'],
            metrics['overall']['TN'],
            f"{metrics['overall']['IoU']:.4f}",
            f"{metrics['overall']['precision']:.4f}",
            f"{metrics['overall']['recall']:.4f}",
            f"{metrics['overall']['specificity']:.4f}",
            f"{metrics['overall']['f1']:.4f}",
            f"{metrics['overall'].get('accuracy', 0):.4f}"
        ])

        # 寫入非背景類別指標
        writer.writerow([
            '非背景類別',
            metrics['no_background']['TP'],
            metrics['no_background']['FP'],
            metrics['no_background']['FN'],
            metrics['no_background']['TN'],
            f"{metrics['no_background']['IoU']:.4f}",
            f"{metrics['no_background']['precision']:.4f}",
            f"{metrics['no_background']['recall']:.4f}",
            f"{metrics['no_background']['specificity']:.4f}",
            f"{metrics['no_background']['f1']:.4f}",
            f"{metrics['no_background'].get('accuracy', 0):.4f}"
        ])

        # 寫入每個類別的指標
        for class_id in range(len(class_names)):
            class_name = class_names.get(class_id, f'類別{class_id}')
            writer.writerow([
                class_name,
                metrics['per_class'][class_id]['TP'],
                metrics['per_class'][class_id]['FP'],
                metrics['per_class'][class_id]['FN'],
                metrics['per_class'][class_id]['TN'],
                f"{metrics['per_class'][class_id]['IoU']:.4f}",
                f"{metrics['per_class'][class_id]['precision']:.4f}",
                f"{metrics['per_class'][class_id]['recall']:.4f}",
                f"{metrics['per_class'][class_id]['specificity']:.4f}",
                f"{metrics['per_class'][class_id]['f1']:.4f}",
                f"{metrics['per_class'][class_id].get('accuracy', 0):.4f}"
            ])

    # 繪製指標視覺化圖表
    # 1. 各類別精確率、召回率和F1分數對比
    plt.figure(figsize=(12, 8))
    class_ids = list(range(len(class_names)))
    class_names_list = [class_names.get(i, f'類別{i}') for i in class_ids]

    precision_values = [metrics['per_class'][i]['precision']
                        for i in class_ids]
    recall_values = [metrics['per_class'][i]['recall'] for i in class_ids]
    f1_values = [metrics['per_class'][i]['f1'] for i in class_ids]

    x = np.arange(len(class_ids))
    width = 0.25

    plt.bar(x - width, precision_values, width, label='Precision')
    plt.bar(x, recall_values, width, label='Recall')
    plt.bar(x + width, f1_values, width, label='F1')

    plt.xlabel('類別')
    plt.ylabel('分數')
    plt.title('各類別精確率、召回率和F1分數')
    plt.xticks(x, class_names_list, rotation=45)
    plt.ylim(0, 1.1)
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'metrics_comparison.png'), dpi=300)
    plt.close()

    # 2. 混淆矩陣
    # 構建混淆矩陣
    conf_matrix = np.zeros((len(class_names), len(class_names)), dtype=int)

    # 逐個圖像處理
    for img_id in all_groundtruths.keys():
        if img_id not in all_predictions:
            continue

        gt_boxes = all_groundtruths[img_id]
        pred_boxes = all_predictions[img_id]

        # 標記已匹配的真實框和預測框
        matched_gt = [False] * len(gt_boxes)
        matched_pred = [False] * len(pred_boxes)

        # 計算每個預測框和每個真實框之間的IoU
        for i, pred in enumerate(pred_boxes):
            pred_class = pred['class_id']
            pred_bbox = pred['bbox']

            best_iou = -1
            best_gt_idx = -1
            best_gt_class = -1

            # 尋找最匹配的真實框
            for j, gt in enumerate(gt_boxes):
                gt_class = gt['class_id']
                gt_bbox = gt['bbox']

                # 計算IoU
                iou = calculate_iou(pred_bbox, gt_bbox)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
                    best_gt_class = gt_class

            # 如果IoU超過閾值，則匹配成功，否則為假陽性
            if best_iou >= IOU_THRESHOLD:
                matched_gt[best_gt_idx] = True
                matched_pred[i] = True
                conf_matrix[best_gt_class][pred_class] += 1
            else:
                # 假陽性 - 實際是背景（或其他）但預測為某類別
                conf_matrix[0][pred_class] += 1  # 假設 0 為背景類別

        # 處理未匹配的真實框（假陰性）
        for j, matched in enumerate(matched_gt):
            if not matched:
                gt_class = gt_boxes[j]['class_id']
                # 假陰性 - 實際是某類別但預測為背景
                conf_matrix[gt_class][0] += 1  # 假設 0 為背景類別

    # 繪製混淆矩陣
    plt.figure(figsize=(10, 8))
    plt.imshow(conf_matrix, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('混淆矩陣')
    plt.colorbar()

    tick_marks = np.arange(len(class_names))
    plt.xticks(tick_marks, class_names_list, rotation=45)
    plt.yticks(tick_marks, class_names_list)

    # 在混淆矩陣中顯示數值
    thresh = conf_matrix.max() / 2.0
    for i in range(conf_matrix.shape[0]):
        for j in range(conf_matrix.shape[1]):
            plt.text(j, i, format(conf_matrix[i, j], 'd'),
                     horizontalalignment="center",
                     color="white" if conf_matrix[i, j] > thresh else "black")

    plt.ylabel('真實類別')
    plt.xlabel('預測類別')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'), dpi=300)
    plt.close()

    # 3. IoU 分布
    plt.figure(figsize=(10, 6))
    for class_id in range(len(class_names)):
        if metrics['per_class'][class_id]['IoU_values']:
            plt.hist(metrics['per_class'][class_id]['IoU_values'], alpha=0.5, bins=10,
                     label=class_names.get(class_id, f'類別{class_id}'))

    plt.xlabel('IoU')
    plt.ylabel('數量')
    plt.title('各類別IoU分布')
    plt.legend()
    plt.savefig(os.path.join(output_dir, 'iou_distribution.png'), dpi=300)
    plt.close()

    # 4. 預測框大小與置信度關係圖
    plt.figure(figsize=(10, 6))
    for img_id, boxes in all_predictions.items():
        for box in boxes:
            x1, y1, x2, y2 = box['bbox']
            width = x2 - x1
            height = y2 - y1
            area = width * height
            conf = box['conf']
            class_id = box['class_id']

            plt.scatter(area, conf, alpha=0.6, c=class_id, cmap='viridis')

    plt.xlabel('面積 (像素^2)')
    plt.ylabel('置信度')
    plt.title('預測框面積與置信度關係')
    plt.colorbar(label='類別')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'area_vs_confidence.png'), dpi=300)
    plt.close()

    logger.info(f"指標評估完成！結果已保存至: {output_dir}")

    return metrics