#!/usr/bin/env python3
"""
🎯 Enhanced ROI Preview Generator
Generate visual preview of ROI regions and slice configurations with improved visualization

Features:
- Filled excluded areas with transparent colors
- Enhanced grid and overlap visualization
- English annotations and labels
- Intuitive slice configuration display
"""

import cv2
import numpy as np
import os
from pathlib import Path
from typing import Dict, Tuple, Optional, List


class EnhancedROIPreviewGenerator:
    """Enhanced ROI and slice configuration preview generator"""
    
    def __init__(self):
        """Initialize enhanced preview generator"""
        # Excluded area colors (BGR format) - filled areas
        self.exclude_colors = {
            'top': (0, 0, 255),      # Red - Top excluded area
            'bottom': (0, 255, 0),   # Green - Bottom excluded area
            'left': (255, 0, 0),     # Blue - Left excluded area
            'right': (0, 255, 255)   # Yellow - Right excluded area
        }
        
        # ROI border colors (darker versions for clear boundaries)
        self.roi_border_colors = {
            'top': (0, 0, 180),      # Dark red
            'bottom': (0, 180, 0),   # Dark green
            'left': (180, 0, 0),     # Dark blue
            'right': (0, 180, 180)   # Dark yellow
        }
        
        # Visual settings
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.7
        self.font_thickness = 2
        self.text_color = (255, 255, 255)  # White text
        self.text_bg_color = (0, 0, 0)     # Black background
        
        # Enhanced visual settings
        self.border_thickness = 4
        self.grid_thickness = 2
        self.exclude_alpha = 0.3           # Transparency for excluded areas
        self.overlap_alpha = 0.7           # Transparency for overlap areas
        self.grid_color = (180, 180, 180)  # Light gray for grid
        self.overlap_color = (0, 200, 255) # Orange for overlap areas
        self.roi_area_color = (100, 255, 100) # Light green for ROI area
        
    def calculate_roi_coordinates(self, image_shape: Tuple[int, int], roi_ratios: Dict[str, float]) -> Dict[str, int]:
        """
        Calculate ROI region coordinates
        
        Args:
            image_shape: Image shape (height, width)
            roi_ratios: ROI ratio dict {'top': 3.0, 'bottom': 2.8, 'left': 1.3, 'right': 1.7}
            
        Returns:
            ROI coordinates dict
        """
        h, w = image_shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # Calculate actual pixel distance based on ratio (ratio/10 as percentage)
        roi_top = max(0, center_y - int(h * roi_ratios.get('top', 3.0) / 10))
        roi_bottom = min(h, center_y + int(h * roi_ratios.get('bottom', 2.8) / 10))
        roi_left = max(0, center_x - int(w * roi_ratios.get('left', 1.3) / 10))
        roi_right = min(w, center_x + int(w * roi_ratios.get('right', 1.7) / 10))
        
        return {
            'top': roi_top,
            'bottom': roi_bottom,
            'left': roi_left,
            'right': roi_right,
            'center_x': center_x,
            'center_y': center_y
        }
    
    def draw_excluded_areas(self, image: np.ndarray, roi_coords: Dict[str, int]) -> np.ndarray:
        """
        Draw filled excluded areas outside ROI
        
        Args:
            image: Input image
            roi_coords: ROI coordinates
            
        Returns:
            Image with filled excluded areas
        """
        preview_image = image.copy()
        h, w = image.shape[:2]
        
        roi_top = roi_coords['top']
        roi_bottom = roi_coords['bottom']
        roi_left = roi_coords['left']
        roi_right = roi_coords['right']
        
        # Create overlay for excluded areas
        overlay = preview_image.copy()
        
        # Fill excluded areas
        # Top excluded area
        if roi_top > 0:
            cv2.rectangle(overlay, (0, 0), (w, roi_top), self.exclude_colors['top'], -1)
        
        # Bottom excluded area
        if roi_bottom < h:
            cv2.rectangle(overlay, (0, roi_bottom), (w, h), self.exclude_colors['bottom'], -1)
        
        # Left excluded area
        if roi_left > 0:
            cv2.rectangle(overlay, (0, roi_top), (roi_left, roi_bottom), self.exclude_colors['left'], -1)
        
        # Right excluded area
        if roi_right < w:
            cv2.rectangle(overlay, (roi_right, roi_top), (w, roi_bottom), self.exclude_colors['right'], -1)
        
        # Blend with original image
        preview_image = cv2.addWeighted(preview_image, 1-self.exclude_alpha, overlay, self.exclude_alpha, 0)
        
        # Draw ROI area with light tint
        roi_overlay = preview_image.copy()
        cv2.rectangle(roi_overlay, (roi_left, roi_top), (roi_right, roi_bottom), self.roi_area_color, -1)
        preview_image = cv2.addWeighted(preview_image, 0.9, roi_overlay, 0.1, 0)
        
        # Draw ROI borders
        cv2.rectangle(preview_image, (roi_left, roi_top), (roi_right, roi_bottom), 
                     (255, 255, 255), self.border_thickness)
        
        # Draw center point
        center_x = roi_coords['center_x']
        center_y = roi_coords['center_y']
        cv2.circle(preview_image, (center_x, center_y), 8, (255, 255, 255), -1)
        cv2.circle(preview_image, (center_x, center_y), 8, (0, 0, 0), 3)
        cv2.putText(preview_image, "CENTER", (center_x - 35, center_y - 15), 
                   self.font, 0.5, (255, 255, 255), 2)
        
        return preview_image
    
    def draw_enhanced_slice_grid(self, image: np.ndarray, roi_coords: Dict[str, int], 
                               slice_config: Dict[str, float]) -> np.ndarray:
        """
        Draw enhanced slice grid with clear overlap visualization
        
        Args:
            image: Input image
            roi_coords: ROI coordinates
            slice_config: Slice configuration
            
        Returns:
            Image with enhanced slice grid
        """
        preview_image = image.copy()
        
        roi_top = roi_coords['top']
        roi_bottom = roi_coords['bottom']
        roi_left = roi_coords['left']
        roi_right = roi_coords['right']
        
        roi_height = roi_bottom - roi_top
        roi_width = roi_right - roi_left
        
        slice_h = slice_config.get('height', 320)
        slice_w = slice_config.get('width', 320)
        overlap_ratio = slice_config.get('overlap_ratio', 0.2)
        
        # Calculate step size (considering overlap)
        step_h = int(slice_h * (1 - overlap_ratio))
        step_w = int(slice_w * (1 - overlap_ratio))
        
        # Calculate overlap size
        overlap_h = int(slice_h * overlap_ratio)
        overlap_w = int(slice_w * overlap_ratio)
        
        # Create overlay for overlap areas
        overlap_overlay = preview_image.copy()
        
        # Draw slice rectangles and overlaps
        slice_positions = []
        
        # Calculate all slice positions
        y = roi_top
        row_idx = 0
        while y < roi_bottom:
            x = roi_left
            col_idx = 0
            while x < roi_right:
                # Calculate slice rectangle
                slice_right = min(x + slice_w, roi_right)
                slice_bottom = min(y + slice_h, roi_bottom)
                
                slice_positions.append({
                    'left': x, 'top': y, 'right': slice_right, 'bottom': slice_bottom,
                    'row': row_idx, 'col': col_idx
                })
                
                x += step_w
                col_idx += 1
            y += step_h
            row_idx += 1
        
        # Draw overlap areas first (so they appear behind grid lines)
        for i, slice_pos in enumerate(slice_positions):
            # Horizontal overlap (with next slice in same row)
            next_horizontal = next((s for s in slice_positions 
                                  if s['row'] == slice_pos['row'] and s['col'] == slice_pos['col'] + 1), None)
            if next_horizontal:
                overlap_left = slice_pos['right'] - overlap_w
                overlap_right = next_horizontal['left'] + overlap_w
                overlap_top = slice_pos['top']
                overlap_bottom = slice_pos['bottom']
                cv2.rectangle(overlap_overlay, (overlap_left, overlap_top), 
                            (overlap_right, overlap_bottom), self.overlap_color, -1)
            
            # Vertical overlap (with slice below)
            next_vertical = next((s for s in slice_positions 
                                if s['col'] == slice_pos['col'] and s['row'] == slice_pos['row'] + 1), None)
            if next_vertical:
                overlap_left = slice_pos['left']
                overlap_right = slice_pos['right']
                overlap_top = slice_pos['bottom'] - overlap_h
                overlap_bottom = next_vertical['top'] + overlap_h
                cv2.rectangle(overlap_overlay, (overlap_left, overlap_top), 
                            (overlap_right, overlap_bottom), self.overlap_color, -1)
        
        # Blend overlap areas
        preview_image = cv2.addWeighted(preview_image, 1-self.overlap_alpha, overlap_overlay, self.overlap_alpha, 0)
        
        # Draw slice grid lines and labels
        for slice_pos in slice_positions:
            # Draw slice rectangle border
            cv2.rectangle(preview_image, (slice_pos['left'], slice_pos['top']), 
                         (slice_pos['right'], slice_pos['bottom']), self.grid_color, self.grid_thickness)
            
            # Add slice index labels
            label = f"[{slice_pos['row']},{slice_pos['col']}]"
            label_size = cv2.getTextSize(label, self.font, 0.5, 1)[0]
            label_x = slice_pos['left'] + 5
            label_y = slice_pos['top'] + 20
            
            # Draw label background
            cv2.rectangle(preview_image, (label_x-2, label_y-15), 
                         (label_x + label_size[0]+2, label_y+3), self.text_bg_color, -1)
            cv2.putText(preview_image, label, (label_x, label_y), 
                       self.font, 0.5, self.text_color, 1)
        
        return preview_image
    
    def add_enhanced_annotations(self, image: np.ndarray, roi_coords: Dict[str, int], 
                               roi_ratios: Dict[str, float], slice_config: Dict[str, float]) -> np.ndarray:
        """
        Add enhanced parameter annotations in English
        
        Args:
            image: Input image
            roi_coords: ROI coordinates
            roi_ratios: ROI ratios
            slice_config: Slice configuration
            
        Returns:
            Image with enhanced annotations
        """
        preview_image = image.copy()
        h, w = image.shape[:2]
        
        # Prepare annotation text
        annotations = [
            "🎯 ROI & Slice Configuration Preview",
            "",
            "ROI Settings:",
            f"  Top Ratio: {roi_ratios.get('top', 3.0):.1f}",
            f"  Bottom Ratio: {roi_ratios.get('bottom', 2.8):.1f}",
            f"  Left Ratio: {roi_ratios.get('left', 1.3):.1f}",
            f"  Right Ratio: {roi_ratios.get('right', 1.7):.1f}",
            "",
            "Slice Settings:",
            f"  Slice Height: {slice_config.get('height', 320)}px",
            f"  Slice Width: {slice_config.get('width', 320)}px",
            f"  Overlap Ratio: {slice_config.get('overlap_ratio', 0.2):.1%}",
            "",
            "Color Legend:",
            "  🔴 Red Fill - Top Excluded Area",
            "  🟢 Green Fill - Bottom Excluded Area", 
            "  🔵 Blue Fill - Left Excluded Area",
            "  🟡 Yellow Fill - Right Excluded Area",
            "  🟩 Light Green Tint - ROI Area",
            "  🔶 Orange Fill - Overlap Areas",
            "  ⬜ Gray Lines - Slice Grid"
        ]
        
        # Calculate text area
        line_height = 25
        text_area_height = len(annotations) * line_height + 20
        text_area_width = 400
        
        # Choose text position (avoid ROI area)
        roi_left = roi_coords['left']
        roi_right = roi_coords['right']
        
        if roi_left > text_area_width + 20:
            # Place on left side
            text_x = 10
        elif w - roi_right > text_area_width + 20:
            # Place on right side
            text_x = w - text_area_width - 10
        else:
            # Place at bottom
            text_x = 10
        
        text_y = 30
        
        # Draw enhanced text background with border
        cv2.rectangle(preview_image, (text_x - 15, text_y - 25), 
                     (text_x + text_area_width + 5, text_y + text_area_height), 
                     (40, 40, 40), -1)
        cv2.rectangle(preview_image, (text_x - 15, text_y - 25), 
                     (text_x + text_area_width + 5, text_y + text_area_height), 
                     (255, 255, 255), 3)
        
        # Draw text with enhanced formatting
        for i, text in enumerate(annotations):
            if text.strip():  # Skip empty lines
                y_pos = text_y + i * line_height
                
                # Highlight section headers
                if text.endswith(":") and not text.startswith(" "):
                    cv2.putText(preview_image, text, (text_x, y_pos), 
                              self.font, self.font_scale, (0, 255, 255), self.font_thickness)
                else:
                    cv2.putText(preview_image, text, (text_x, y_pos), 
                              self.font, self.font_scale, self.text_color, self.font_thickness)
        
        # Add ROI size annotation in ROI area
        roi_width = roi_coords['right'] - roi_coords['left']
        roi_height = roi_coords['bottom'] - roi_coords['top']
        
        roi_size_text = f"ROI: {roi_width} x {roi_height} px"
        text_size = cv2.getTextSize(roi_size_text, self.font, self.font_scale, self.font_thickness)[0]
        text_x = roi_coords['center_x'] - text_size[0] // 2
        text_y = roi_coords['bottom'] - 20
        
        cv2.rectangle(preview_image, (text_x - 10, text_y - 25), 
                     (text_x + text_size[0] + 10, text_y + 5), (0, 0, 0), -1)
        cv2.rectangle(preview_image, (text_x - 10, text_y - 25), 
                     (text_x + text_size[0] + 10, text_y + 5), (255, 255, 255), 2)
        cv2.putText(preview_image, roi_size_text, (text_x, text_y), 
                   self.font, self.font_scale, self.text_color, self.font_thickness)
        
        return preview_image
    
    def generate_enhanced_preview(self, image: np.ndarray, roi_ratios: Dict[str, float], 
                                slice_config: Dict[str, float], save_path: Optional[str] = None) -> np.ndarray:
        """
        Generate enhanced ROI and slice configuration preview
        
        Args:
            image: Input image
            roi_ratios: ROI ratio configuration
            slice_config: Slice configuration
            save_path: Save path (optional)
            
        Returns:
            Enhanced preview image
        """
        # 1. Calculate ROI coordinates
        roi_coords = self.calculate_roi_coordinates(image.shape, roi_ratios)
        
        # 2. Draw excluded areas with filled colors
        preview_image = self.draw_excluded_areas(image, roi_coords)
        
        # 3. Draw enhanced slice grid with overlap visualization
        preview_image = self.draw_enhanced_slice_grid(preview_image, roi_coords, slice_config)
        
        # 4. Add enhanced annotations in English
        preview_image = self.add_enhanced_annotations(preview_image, roi_coords, roi_ratios, slice_config)
        
        # 5. Save image if path specified
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            cv2.imwrite(save_path, preview_image)
            print(f"📸 Enhanced ROI preview saved: {save_path}")
        
        return preview_image
    
    def generate_first_image_preview(self, input_dir: str, output_dir: str, 
                                   roi_ratios: Dict[str, float], slice_config: Dict[str, float]) -> Optional[str]:
        """
        Generate enhanced preview for first image in directory
        
        Args:
            input_dir: Input directory
            output_dir: Output directory
            roi_ratios: ROI ratio configuration
            slice_config: Slice configuration
            
        Returns:
            Preview image path if successful
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            print(f"❌ Input directory not found: {input_dir}")
            return None
        
        # Find first image
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"❌ No image files found in {input_dir}")
            return None
        
        # Sort and select first image
        image_files.sort()
        first_image_path = image_files[0]
        
        print(f"📷 Selected first image for preview: {first_image_path.name}")
        
        # Read image
        image = cv2.imread(str(first_image_path))
        if image is None:
            print(f"❌ Failed to read image: {first_image_path}")
            return None
        
        # Generate enhanced preview
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        preview_filename = f"{first_image_path.stem}_enhanced_roi_preview.jpg"
        preview_path = output_path / "preview" / preview_filename
        
        self.generate_enhanced_preview(image, roi_ratios, slice_config, str(preview_path))
        
        return str(preview_path)


# Factory function
def create_enhanced_roi_preview_generator() -> EnhancedROIPreviewGenerator:
    """Create enhanced ROI preview generator factory function"""
    return EnhancedROIPreviewGenerator()


if __name__ == "__main__":
    print("🎯 Enhanced ROI Preview Generator")
    print("Generate enhanced visual preview of ROI regions and slice configurations")