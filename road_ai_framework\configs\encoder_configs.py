"""
編碼器配置：定義所有編碼器變體的配置
包含CSP_IFormer、IFormer、MobileNet等系列的所有變體
"""

from typing import Dict, Any, List


def get_csp_iformer_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取CSP_IFormer配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "encoder_type": "csp_iformer",
        "input_channels": 3,
        "mode": "classification",
        "img_size": 256,
        "enable_channel_shuffle": False,
        "enable_dropkey": False,
        "enable_efficient_ffn": False,
        "channel_shuffle_groups": 8,
        "channel_shuffle_times": 1,
        "dropkey_rate": 0.1,
        "part_ratio": 0.5,
        "skip_stages": [],
        "depths": [3, 3, 9, 3],
        "embed_dims": [96, 192, 320, 384],
        "num_heads": [3, 6, 10, 12],
        "attention_heads": [3, 6, 10, 12],
        "mlp_ratios": [4, 4, 4, 4],
        "qkv_bias": True,
        "drop_rate": 0.0,
        "attn_drop_rate": 0.0,
        "drop_path_rate": 0.1
    }
    
    variants = {
        "default": {
            # 對應CSP_IFormer_0.py
        },
        
        "channel_shuffle": {
            # 對應CSP_IFormer_0_CS.py
            "enable_channel_shuffle": True,
        },
        
        "channel_shuffle_dropkey": {
            # 對應CSP_IFormer_0_CS_DK.py
            "enable_channel_shuffle": True,
            "enable_dropkey": True,
            "channel_shuffle_times": 2,
        },
        
        "segmentation": {
            # 對應CSP_IFormer_0_SegMode.py
            "mode": "segmentation",
        },
        
        "segmentation_cs": {
            # 對應CSP_IFormer_0_CS_SegMode.py
            "mode": "segmentation",
            "enable_channel_shuffle": True,
        },
        
        "segmentation_high_res": {
            # 對應CSP_IFormer_0_SegMode_1.py
            "mode": "segmentation",
            "img_size": 768,
        },
        
        "simplified_1": {
            # 對應CSP_IFormer_1.py - 跳過第1階段
            "depths": [0, 3, 9, 3],
            "embed_dims": [None, 192, 320, 384],
            "num_heads": [None, 6, 10, 12],
            "attention_heads": [None, 6, 10, 12],
            "mlp_ratios": [None, 4, 4, 4],
            "skip_stages": [0],
        },
        
        "simplified_2": {
            # 對應CSP_IFormer_2.py - 只有stage3+4
            "depths": [0, 0, 9, 3],
            "embed_dims": [None, None, 320, 384],
            "num_heads": [None, None, 10, 12],
            "attention_heads": [None, None, 10, 12],
            "mlp_ratios": [None, None, 4, 4],
            "skip_stages": [0, 1],
        },
        
        "simplified_3": {
            # 對應CSP_IFormer_3.py - 只有stage4
            "depths": [0, 0, 0, 3],
            "embed_dims": [None, None, None, 384],
            "num_heads": [None, None, None, 12],
            "attention_heads": [None, None, None, 12],
            "mlp_ratios": [None, None, None, 4],
            "skip_stages": [0, 1, 2],
        },
        
        "final_classification": {
            # 對應CSP_IFormer_final_ClsMode.py
            "enable_channel_shuffle": True,
            "enable_dropkey": True,
            "enable_efficient_ffn": True,
        },
        
        "final_segmentation": {
            # 對應CSP_IFormer_final_SegMode.py
            "mode": "segmentation",
            "enable_channel_shuffle": True,
            "enable_dropkey": True,
        },
    }
    
    # 合併基礎配置和變體配置
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["encoder_variant"] = variant
    else:
        raise ValueError(f"未知的CSP_IFormer變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def get_iformer_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取IFormer配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "encoder_type": "iformer",
        "input_channels": 3,
        "mode": "classification",
        "img_size": 224,
        "enable_channel_shuffle": False,
        "enable_dropkey": False,
        "depths": [3, 3, 9, 3],
        "embed_dims": [64, 128, 256, 512],
        "num_heads": [2, 4, 8, 16],
        "attention_heads": [2, 4, 8, 16],
        "mlp_ratios": [4, 4, 4, 4],
        "qkv_bias": True,
        "drop_rate": 0.0,
        "attn_drop_rate": 0.0,
        "drop_path_rate": 0.1
    }
    
    variants = {
        "default": {
            # 對應IFormer_0.py
        },
        
        "variant_1": {
            # 對應IFormer_1.py
            "embed_dims": [96, 192, 320, 384],
            "num_heads": [3, 6, 10, 12],
            "attention_heads": [3, 6, 10, 12],
        },
        
        "variant_2": {
            # 對應IFormer_2.py
            "depths": [2, 2, 6, 2],
            "embed_dims": [48, 96, 192, 384],
            "num_heads": [1, 3, 6, 12],
            "attention_heads": [1, 3, 6, 12],
        },
        
        "variant_3": {
            # 對應IFormer_3.py
            "depths": [2, 2, 18, 2],
            "embed_dims": [32, 64, 160, 256],
            "num_heads": [1, 2, 5, 8],
            "attention_heads": [1, 2, 5, 8],
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["encoder_variant"] = variant
    else:
        raise ValueError(f"未知的IFormer變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def get_mobilenet_config(variant: str = "v3_small") -> Dict[str, Any]:
    """
    獲取MobileNet配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "encoder_type": "mobilenet",
        "input_channels": 3,
        "mode": "classification",
    }
    
    variants = {
        "v3_small": {
            # 對應Mobilenet.py
            "version": "v3_small",
            "output_channels": [16, 24, 40, 96],
            "width_mult": 1.0,
        },
        
        "v3_large": {
            # 對應Mobilenet_1.py
            "version": "v3_large", 
            "output_channels": [24, 40, 80, 160],
            "width_mult": 1.0,
        },
        
        "csp_small": {
            # 對應CSP_Mobilenet.py
            "version": "csp_v3_small",
            "output_channels": [16, 24, 40, 96],
            "csp_ratio": 0.5,
            "enable_csp": True,
        },
        
        "csp_large": {
            # 對應CSP_Mobilenet_1.py
            "version": "csp_v3_large",
            "output_channels": [24, 40, 80, 160],
            "csp_ratio": 0.5,
            "enable_csp": True,
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["encoder_variant"] = variant
    else:
        raise ValueError(f"未知的MobileNet變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def get_unet_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取UNet編碼器配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "encoder_type": "unet",
        "input_channels": 3,
        "mode": "segmentation",
        "output_channels": [64, 128, 256, 512],
        "use_batchnorm": True,
        "use_dropout": True,
        "dropout_rate": 0.1,
    }
    
    variants = {
        "default": {
            # 標準UNet編碼器
        },
        
        "lightweight": {
            # 輕量級版本
            "output_channels": [32, 64, 128, 256],
            "dropout_rate": 0.05,
        },
        
        "deep": {
            # 深層版本
            "output_channels": [64, 128, 256, 512, 1024],
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["encoder_variant"] = variant
    else:
        raise ValueError(f"未知的UNet變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def list_encoder_configs() -> Dict[str, List[str]]:
    """列出所有可用的編碼器配置"""
    return {
        "csp_iformer": [
            "default", "channel_shuffle", "channel_shuffle_dropkey",
            "segmentation", "segmentation_cs", "segmentation_high_res",
            "simplified_1", "simplified_2", "simplified_3",
            "final_classification", "final_segmentation"
        ],
        "iformer": [
            "default", "variant_1", "variant_2", "variant_3"
        ],
        "mobilenet": [
            "v3_small", "v3_large", "csp_small", "csp_large"
        ],
        "unet": [
            "default", "lightweight", "deep"
        ]
    }


def get_encoder_config(encoder_type: str, variant: str = "default") -> Dict[str, Any]:
    """
    統一的編碼器配置獲取函數
    
    Args:
        encoder_type: 編碼器類型
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    config_getters = {
        "csp_iformer": get_csp_iformer_config,
        "iformer": get_iformer_config,
        "mobilenet": get_mobilenet_config,
        "unet": get_unet_config,
    }
    
    if encoder_type not in config_getters:
        raise ValueError(f"未知的編碼器類型: {encoder_type}. 可用類型: {list(config_getters.keys())}")
    
    return config_getters[encoder_type](variant)