"""
統一訓練器

整合基礎訓練函數和優化訓練函數，提供統一的訓練接口
"""

import torch
import torch.nn as nn
import torch.cuda.amp as amp
from torch.utils.data import DataLoader
from dataclasses import dataclass
from typing import Dict, Any, Optional, Callable, List
import logging
from pathlib import Path

# 重用現有的模組
from ..util.checkpoint import CheckpointManager
from ..util.losses import LossFactory
from ..util.metrics import get_metrics

@dataclass
class TrainingConfig:
    """訓練配置"""
    # 基礎設置
    epochs: int = 100
    device: str = "cuda"
    
    # 優化設置
    enable_mixed_precision: bool = True
    gradient_accumulation_steps: int = 1
    max_grad_norm: float = 1.0
    
    # 早停設置
    enable_early_stopping: bool = True
    early_stopping_patience: int = 10
    early_stopping_min_delta: float = 1e-4
    
    # 檢查點設置
    checkpoint_frequency: int = 5
    save_best_only: bool = True
    max_checkpoints: int = 5
    
    # 記憶體管理
    enable_memory_cleanup: bool = True
    memory_cleanup_frequency: int = 10
    
    # 日誌設置
    log_frequency: int = 100
    validation_frequency: int = 1


class UnifiedTrainer:
    """統一訓練器，整合基礎版和優化版功能"""
    
    def __init__(self, 
                 model: nn.Module,
                 optimizer: torch.optim.Optimizer,
                 scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
                 loss_fn: Optional[nn.Module] = None,
                 config: Optional[TrainingConfig] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化統一訓練器
        
        Args:
            model: 要訓練的模型
            optimizer: 優化器
            scheduler: 學習率調度器
            loss_fn: 損失函數
            config: 訓練配置
            logger: 日誌記錄器
        """
        self.model = model
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.loss_fn = loss_fn
        self.config = config or TrainingConfig()
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化組件
        self._setup_training_components()
        
        # 統計信息
        self.stats = {
            'epoch': 0,
            'best_metric': float('inf'),
            'train_losses': [],
            'val_losses': [],
            'learning_rates': []
        }
        
        # 控制標誌
        self.should_stop = False
    
    def _setup_training_components(self):
        """設置訓練組件"""
        # 混合精度
        if self.config.enable_mixed_precision and torch.cuda.is_available():
            self.scaler = amp.GradScaler()
            self.use_amp = True
        else:
            self.scaler = None
            self.use_amp = False
        
        # 檢查點管理器
        self.checkpoint_manager = CheckpointManager(
            checkpoint_dir="checkpoints",
            max_checkpoints=self.config.max_checkpoints
        )
        
        # 設備配置
        self.device = torch.device(self.config.device if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        self.logger.info(f"訓練器初始化完成 - 設備: {self.device}, AMP: {self.use_amp}")
    
    def train_epoch(self, 
                   train_loader: DataLoader, 
                   epoch: int) -> Dict[str, float]:
        """
        訓練一個epoch
        
        Args:
            train_loader: 訓練數據加載器
            epoch: 當前epoch
            
        Returns:
            Dict[str, float]: 訓練指標
        """
        self.model.train()
        total_loss = 0.0
        num_batches = len(train_loader)
        
        # 重置梯度累積
        accumulated_loss = 0.0
        
        for batch_idx, (data, targets) in enumerate(train_loader):
            if self.should_stop:
                break
                
            data, targets = data.to(self.device), targets.to(self.device)
            
            # 前向傳播
            if self.use_amp:
                loss = self._forward_pass_amp(data, targets)
            else:
                loss = self._forward_pass_basic(data, targets)
            
            # 梯度累積
            loss = loss / self.config.gradient_accumulation_steps
            accumulated_loss += loss.item()
            
            # 反向傳播
            if self.use_amp:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # 梯度更新
            if (batch_idx + 1) % self.config.gradient_accumulation_steps == 0:
                if self.use_amp:
                    self._update_weights_amp()
                else:
                    self._update_weights_basic()
                
                total_loss += accumulated_loss
                accumulated_loss = 0.0
                
                # 學習率調度
                if self.scheduler and hasattr(self.scheduler, 'step_batch'):
                    self.scheduler.step()
            
            # 日誌記錄
            if batch_idx % self.config.log_frequency == 0:
                self._log_batch_progress(epoch, batch_idx, num_batches, loss.item())
            
            # 記憶體清理
            if (self.config.enable_memory_cleanup and 
                batch_idx % self.config.memory_cleanup_frequency == 0):
                self._cleanup_memory()
        
        # Epoch級別的學習率調度
        if self.scheduler and not hasattr(self.scheduler, 'step_batch'):
            self.scheduler.step()
        
        avg_loss = total_loss / (num_batches // self.config.gradient_accumulation_steps)
        current_lr = self.optimizer.param_groups[0]['lr']
        
        return {
            'train_loss': avg_loss,
            'learning_rate': current_lr
        }
    
    def validate_epoch(self, 
                      val_loader: DataLoader, 
                      epoch: int,
                      metrics_fn: Optional[Callable] = None) -> Dict[str, float]:
        """
        驗證一個epoch
        
        Args:
            val_loader: 驗證數據加載器
            epoch: 當前epoch
            metrics_fn: 自定義指標計算函數
            
        Returns:
            Dict[str, float]: 驗證指標
        """
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_idx, (data, targets) in enumerate(val_loader):
                if self.should_stop:
                    break
                    
                data, targets = data.to(self.device), targets.to(self.device)
                
                # 前向傳播
                if self.use_amp:
                    with amp.autocast():
                        outputs = self.model(data)
                        if self.loss_fn:
                            loss = self.loss_fn(outputs, targets)
                        else:
                            loss = torch.tensor(0.0)
                else:
                    outputs = self.model(data)
                    if self.loss_fn:
                        loss = self.loss_fn(outputs, targets)
                    else:
                        loss = torch.tensor(0.0)
                
                total_loss += loss.item()
                
                # 收集預測結果用於指標計算
                all_predictions.append(outputs.cpu())
                all_targets.append(targets.cpu())
        
        avg_loss = total_loss / len(val_loader)
        
        # 計算自定義指標
        metrics = {'val_loss': avg_loss}
        if metrics_fn and all_predictions:
            predictions = torch.cat(all_predictions)
            targets = torch.cat(all_targets)
            custom_metrics = metrics_fn(predictions, targets)
            metrics.update(custom_metrics)
        
        return metrics
    
    def fit(self, 
           train_loader: DataLoader,
           val_loader: Optional[DataLoader] = None,
           metrics_fn: Optional[Callable] = None) -> Dict[str, List[float]]:
        """
        完整訓練流程
        
        Args:
            train_loader: 訓練數據加載器
            val_loader: 驗證數據加載器
            metrics_fn: 自定義指標計算函數
            
        Returns:
            Dict[str, List[float]]: 訓練歷史
        """
        self.logger.info(f"開始訓練 - 總epoch數: {self.config.epochs}")
        
        best_metric = float('inf')
        patience_counter = 0
        
        for epoch in range(self.config.epochs):
            if self.should_stop:
                self.logger.info("收到停止信號，提前結束訓練")
                break
            
            self.stats['epoch'] = epoch
            
            # 訓練
            train_metrics = self.train_epoch(train_loader, epoch)
            self.stats['train_losses'].append(train_metrics['train_loss'])
            self.stats['learning_rates'].append(train_metrics['learning_rate'])
            
            # 驗證
            if val_loader and epoch % self.config.validation_frequency == 0:
                val_metrics = self.validate_epoch(val_loader, epoch, metrics_fn)
                self.stats['val_losses'].append(val_metrics['val_loss'])
                
                # 早停檢查
                current_metric = val_metrics['val_loss']
                if self.config.enable_early_stopping:
                    if current_metric < best_metric - self.config.early_stopping_min_delta:
                        best_metric = current_metric
                        patience_counter = 0
                        is_best = True
                    else:
                        patience_counter += 1
                        is_best = False
                    
                    if patience_counter >= self.config.early_stopping_patience:
                        self.logger.info(f"早停觸發 - epoch {epoch}")
                        break
                else:
                    is_best = current_metric < best_metric
                    if is_best:
                        best_metric = current_metric
            else:
                is_best = False
            
            # 保存檢查點
            if epoch % self.config.checkpoint_frequency == 0:
                checkpoint_data = {
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
                    'best_metric': best_metric,
                    'config': self.config
                }
                
                self.checkpoint_manager.save_checkpoint(
                    checkpoint_data, 
                    epoch, 
                    {'loss': best_metric},
                    is_best=is_best and self.config.save_best_only
                )
            
            # 記錄epoch結果
            self._log_epoch_results(epoch, train_metrics, 
                                  val_metrics if val_loader else None)
        
        self.logger.info("訓練完成")
        return {
            'train_losses': self.stats['train_losses'],
            'val_losses': self.stats['val_losses'],
            'learning_rates': self.stats['learning_rates']
        }
    
    def _forward_pass_amp(self, data: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """混合精度前向傳播"""
        with amp.autocast():
            outputs = self.model(data)
            if self.loss_fn:
                loss = self.loss_fn(outputs, targets)
            else:
                # 如果沒有提供損失函數，返回零損失
                loss = torch.tensor(0.0, device=data.device)
        return loss
    
    def _forward_pass_basic(self, data: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """基礎前向傳播"""
        outputs = self.model(data)
        if self.loss_fn:
            loss = self.loss_fn(outputs, targets)
        else:
            loss = torch.tensor(0.0, device=data.device)
        return loss
    
    def _update_weights_amp(self):
        """混合精度權重更新"""
        # 梯度裁剪
        if self.config.max_grad_norm > 0:
            self.scaler.unscale_(self.optimizer)
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
        
        self.scaler.step(self.optimizer)
        self.scaler.update()
        self.optimizer.zero_grad()
    
    def _update_weights_basic(self):
        """基礎權重更新"""
        # 梯度裁剪
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
        
        self.optimizer.step()
        self.optimizer.zero_grad()
    
    def _cleanup_memory(self):
        """清理記憶體"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    def _log_batch_progress(self, epoch: int, batch_idx: int, num_batches: int, loss: float):
        """記錄批次進度"""
        if batch_idx % self.config.log_frequency == 0:
            progress = 100.0 * batch_idx / num_batches
            lr = self.optimizer.param_groups[0]['lr']
            self.logger.info(
                f"Epoch {epoch} [{batch_idx}/{num_batches} ({progress:.1f}%)] "
                f"Loss: {loss:.6f}, LR: {lr:.6f}"
            )
    
    def _log_epoch_results(self, epoch: int, train_metrics: Dict[str, float], 
                          val_metrics: Optional[Dict[str, float]] = None):
        """記錄epoch結果"""
        log_msg = f"Epoch {epoch} - Train Loss: {train_metrics['train_loss']:.6f}"
        
        if val_metrics:
            log_msg += f", Val Loss: {val_metrics['val_loss']:.6f}"
            
            # 記錄額外的驗證指標
            for key, value in val_metrics.items():
                if key != 'val_loss':
                    log_msg += f", {key}: {value:.6f}"
        
        log_msg += f", LR: {train_metrics['learning_rate']:.6f}"
        self.logger.info(log_msg)
    
    def stop_training(self):
        """停止訓練"""
        self.should_stop = True
        self.logger.info("設置停止標誌")
    
    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """載入檢查點"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint.get('scheduler_state_dict'):
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.stats['epoch'] = checkpoint.get('epoch', 0)
        self.stats['best_metric'] = checkpoint.get('best_metric', float('inf'))
        
        self.logger.info(f"已載入檢查點: {checkpoint_path}")
        return checkpoint
    
    def get_model_summary(self) -> Dict[str, Any]:
        """獲取模型摘要"""
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'non_trainable_parameters': total_params - trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # 假設float32
            'device': str(self.device),
            'mixed_precision': self.use_amp
        }