#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轉換策略模組

定義標註格式轉換的策略接口和基礎實現
"""

import json
import xml.etree.ElementTree as ET
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
from PIL import Image
import numpy as np

from shared.exceptions import ConversionError, ValidationError
from shared.logger_utils import StructuredLogger


class ConversionStrategy(ABC):
    """
    轉換策略抽象基類
    
    定義所有格式轉換器必須實現的接口
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化轉換策略
        
        Args:
            logger: 日誌記錄器
        """
        self.logger = logger or StructuredLogger.create_logger(self.__class__.__name__)
        self.stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    @abstractmethod
    def convert_file(self, 
                    input_file: Path, 
                    output_dir: Path,
                    **kwargs) -> bool:
        """
        轉換單個文件
        
        Args:
            input_file: 輸入文件路徑
            output_dir: 輸出目錄路徑
            **kwargs: 額外參數
            
        Returns:
            bool: 轉換是否成功
        """
        pass
    
    @abstractmethod
    def validate_input(self, input_file: Path) -> bool:
        """
        驗證輸入文件格式
        
        Args:
            input_file: 輸入文件路徑
            
        Returns:
            bool: 文件格式是否有效
        """
        pass
    
    def get_stats(self) -> Dict[str, int]:
        """
        獲取轉換統計信息
        
        Returns:
            Dict[str, int]: 統計信息
        """
        return self.stats.copy()
    
    def reset_stats(self):
        """重置統計信息"""
        self.stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }


class LabelMeToYOLOConverter(ConversionStrategy):
    """
    LabelMe到YOLO格式轉換器
    """
    
    def __init__(self, class_mapping: Optional[Dict[str, int]] = None, logger: Optional[logging.Logger] = None):
        """
        初始化LabelMe到YOLO轉換器
        
        Args:
            class_mapping: 類別名稱到ID的映射
            logger: 日誌記錄器
        """
        super().__init__(logger)
        self.class_mapping = class_mapping or {}
        self.auto_class_id = 0
    
    def convert_file(self, input_file: Path, output_dir: Path, **kwargs) -> bool:
        """
        轉換LabelMe文件到YOLO格式
        
        Args:
            input_file: LabelMe JSON文件路徑
            output_dir: 輸出目錄路徑
            **kwargs: 額外參數
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            self.stats['processed'] += 1
            
            if not self.validate_input(input_file):
                self.logger.warning(f"跳過無效文件: {input_file}")
                self.stats['skipped'] += 1
                return False
            
            # 讀取LabelMe文件
            with open(input_file, 'r', encoding='utf-8') as f:
                labelme_data = json.load(f)
            
            # 獲取圖像尺寸
            image_width = labelme_data.get('imageWidth')
            image_height = labelme_data.get('imageHeight')
            
            if not image_width or not image_height:
                # 嘗試從圖像文件獲取尺寸
                image_path = self._find_image_file(input_file, labelme_data.get('imagePath'))
                if image_path and image_path.exists():
                    with Image.open(image_path) as img:
                        image_width, image_height = img.size
                else:
                    raise ConversionError(f"無法獲取圖像尺寸: {input_file}")
            
            # 轉換標註
            yolo_annotations = []
            for shape in labelme_data.get('shapes', []):
                yolo_line = self._convert_shape_to_yolo(shape, image_width, image_height)
                if yolo_line:
                    yolo_annotations.append(yolo_line)
            
            # 保存YOLO文件
            output_file = output_dir / f"{input_file.stem}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))
            
            self.logger.debug(f"轉換成功: {input_file} -> {output_file}")
            self.stats['success'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"轉換失敗: {input_file}, 錯誤: {e}")
            self.stats['failed'] += 1
            return False
    
    def validate_input(self, input_file: Path) -> bool:
        """
        驗證LabelMe文件格式
        
        Args:
            input_file: 輸入文件路徑
            
        Returns:
            bool: 文件格式是否有效
        """
        try:
            if input_file.suffix.lower() != '.json':
                return False
            
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 檢查必需欄位
            required_fields = ['shapes']
            return all(field in data for field in required_fields)
            
        except Exception:
            return False
    
    def _convert_shape_to_yolo(self, shape: Dict[str, Any], img_width: int, img_height: int) -> Optional[str]:
        """
        將LabelMe shape轉換為YOLO格式
        
        Args:
            shape: LabelMe shape數據
            img_width: 圖像寬度
            img_height: 圖像高度
            
        Returns:
            Optional[str]: YOLO格式標註行
        """
        try:
            label = shape.get('label', '')
            points = shape.get('points', [])
            shape_type = shape.get('shape_type', '')
            
            if not points or not label:
                return None
            
            # 獲取類別ID
            class_id = self._get_class_id(label)
            
            # 轉換不同形狀類型
            if shape_type == 'rectangle':
                return self._rectangle_to_yolo(points, class_id, img_width, img_height)
            elif shape_type == 'polygon':
                return self._polygon_to_yolo(points, class_id, img_width, img_height)
            else:
                self.logger.warning(f"不支援的形狀類型: {shape_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"轉換shape失敗: {e}")
            return None
    
    def _rectangle_to_yolo(self, points: List[List[float]], class_id: int, img_width: int, img_height: int) -> str:
        """
        將矩形轉換為YOLO格式
        
        Args:
            points: 矩形點座標 [[x1,y1], [x2,y2]]
            class_id: 類別ID
            img_width: 圖像寬度
            img_height: 圖像高度
            
        Returns:
            str: YOLO格式標註行
        """
        # 計算邊界框
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        # 轉換為YOLO格式（中心點 + 寬高，歸一化）
        center_x = (x_min + x_max) / 2 / img_width
        center_y = (y_min + y_max) / 2 / img_height
        width = (x_max - x_min) / img_width
        height = (y_max - y_min) / img_height
        
        return f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"
    
    def _polygon_to_yolo(self, points: List[List[float]], class_id: int, img_width: int, img_height: int) -> str:
        """
        將多邊形轉換為YOLO格式（作為邊界框）
        
        Args:
            points: 多邊形點座標
            class_id: 類別ID
            img_width: 圖像寬度
            img_height: 圖像高度
            
        Returns:
            str: YOLO格式標註行
        """
        # 計算多邊形的邊界框
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        # 轉換為YOLO格式
        center_x = (x_min + x_max) / 2 / img_width
        center_y = (y_min + y_max) / 2 / img_height
        width = (x_max - x_min) / img_width
        height = (y_max - y_min) / img_height
        
        return f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"
    
    def _get_class_id(self, label: str) -> int:
        """
        獲取類別ID
        
        Args:
            label: 類別標籤
            
        Returns:
            int: 類別ID
        """
        if label in self.class_mapping:
            return self.class_mapping[label]
        else:
            # 自動分配新的類別ID
            self.class_mapping[label] = self.auto_class_id
            self.auto_class_id += 1
            self.logger.info(f"新增類別映射: {label} -> {self.class_mapping[label]}")
            return self.class_mapping[label]
    
    def _find_image_file(self, json_file: Path, image_path: Optional[str]) -> Optional[Path]:
        """
        查找對應的圖像文件
        
        Args:
            json_file: JSON文件路徑
            image_path: 圖像路徑（可能是相對路徑）
            
        Returns:
            Optional[Path]: 圖像文件路徑
        """
        if image_path:
            # 嘗試相對路徑
            abs_path = json_file.parent / image_path
            if abs_path.exists():
                return abs_path
            
            # 嘗試同目錄下的同名圖像
            image_name = Path(image_path).name
            same_dir_path = json_file.parent / image_name
            if same_dir_path.exists():
                return same_dir_path
        
        # 嘗試同名圖像文件
        common_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        for ext in common_extensions:
            image_file = json_file.parent / f"{json_file.stem}{ext}"
            if image_file.exists():
                return image_file
        
        return None
    
    def save_class_mapping(self, output_dir: Path):
        """
        保存類別映射到classes.txt文件
        
        Args:
            output_dir: 輸出目錄
        """
        if not self.class_mapping:
            return
        
        # 按ID排序
        sorted_classes = sorted(self.class_mapping.items(), key=lambda x: x[1])
        
        classes_file = output_dir / "classes.txt"
        with open(classes_file, 'w', encoding='utf-8') as f:
            for class_name, class_id in sorted_classes:
                f.write(f"{class_name}\n")
        
        self.logger.info(f"類別映射已保存到: {classes_file}")


class VOCToLabelMeConverter(ConversionStrategy):
    """
    VOC到LabelMe格式轉換器
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化VOC到LabelMe轉換器
        
        Args:
            logger: 日誌記錄器
        """
        super().__init__(logger)
    
    def convert_file(self, input_file: Path, output_dir: Path, **kwargs) -> bool:
        """
        轉換VOC文件到LabelMe格式
        
        Args:
            input_file: VOC XML文件路徑
            output_dir: 輸出目錄路徑
            **kwargs: 額外參數
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            self.stats['processed'] += 1
            
            if not self.validate_input(input_file):
                self.logger.warning(f"跳過無效文件: {input_file}")
                self.stats['skipped'] += 1
                return False
            
            # 解析VOC XML
            tree = ET.parse(input_file)
            root = tree.getroot()
            
            # 獲取圖像信息
            filename = root.find('filename').text
            size = root.find('size')
            img_width = int(size.find('width').text)
            img_height = int(size.find('height').text)
            
            # 創建LabelMe數據結構
            labelme_data = {
                "version": "5.0.1",
                "flags": {},
                "shapes": [],
                "imagePath": filename,
                "imageData": None,
                "imageHeight": img_height,
                "imageWidth": img_width
            }
            
            # 轉換標註
            for obj in root.findall('object'):
                shape = self._convert_voc_object_to_shape(obj)
                if shape:
                    labelme_data['shapes'].append(shape)
            
            # 保存LabelMe文件
            output_file = output_dir / f"{input_file.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"轉換成功: {input_file} -> {output_file}")
            self.stats['success'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"轉換失敗: {input_file}, 錯誤: {e}")
            self.stats['failed'] += 1
            return False
    
    def validate_input(self, input_file: Path) -> bool:
        """
        驗證VOC文件格式
        
        Args:
            input_file: 輸入文件路徑
            
        Returns:
            bool: 文件格式是否有效
        """
        try:
            if input_file.suffix.lower() != '.xml':
                return False
            
            tree = ET.parse(input_file)
            root = tree.getroot()
            
            # 檢查VOC格式特徵
            return (root.tag == 'annotation' and 
                   root.find('filename') is not None and
                   root.find('size') is not None)
            
        except Exception:
            return False
    
    def _convert_voc_object_to_shape(self, obj_element) -> Optional[Dict[str, Any]]:
        """
        將VOC object轉換為LabelMe shape
        
        Args:
            obj_element: VOC object XML元素
            
        Returns:
            Optional[Dict[str, Any]]: LabelMe shape數據
        """
        try:
            name = obj_element.find('name').text
            bndbox = obj_element.find('bndbox')
            
            if bndbox is None:
                return None
            
            # 獲取邊界框座標
            xmin = float(bndbox.find('xmin').text)
            ymin = float(bndbox.find('ymin').text)
            xmax = float(bndbox.find('xmax').text)
            ymax = float(bndbox.find('ymax').text)
            
            # 創建LabelMe shape
            shape = {
                "label": name,
                "points": [[xmin, ymin], [xmax, ymax]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            }
            
            return shape
            
        except Exception as e:
            self.logger.error(f"轉換VOC object失敗: {e}")
            return None


class YOLOToLabelMeConverter(ConversionStrategy):
    """
    YOLO到LabelMe格式轉換器
    """
    
    def __init__(self, classes: Optional[List[str]] = None, logger: Optional[logging.Logger] = None):
        """
        初始化YOLO到LabelMe轉換器
        
        Args:
            classes: 類別名稱列表
            logger: 日誌記錄器
        """
        super().__init__(logger)
        self.classes = classes or []
    
    def convert_file(self, input_file: Path, output_dir: Path, **kwargs) -> bool:
        """
        轉換YOLO文件到LabelMe格式
        
        Args:
            input_file: YOLO TXT文件路徑
            output_dir: 輸出目錄路徑
            **kwargs: 額外參數（應包含圖像尺寸信息）
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            self.stats['processed'] += 1
            
            if not self.validate_input(input_file):
                self.logger.warning(f"跳過無效文件: {input_file}")
                self.stats['skipped'] += 1
                return False
            
            # 獲取圖像尺寸
            img_width = kwargs.get('img_width')
            img_height = kwargs.get('img_height')
            
            if not img_width or not img_height:
                # 嘗試從對應的圖像文件獲取尺寸
                image_file = self._find_image_file(input_file)
                if image_file and image_file.exists():
                    with Image.open(image_file) as img:
                        img_width, img_height = img.size
                else:
                    raise ConversionError(f"無法獲取圖像尺寸: {input_file}")
            
            # 讀取YOLO標註
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 創建LabelMe數據結構
            labelme_data = {
                "version": "5.0.1",
                "flags": {},
                "shapes": [],
                "imagePath": f"{input_file.stem}.jpg",  # 預設jpg格式
                "imageData": None,
                "imageHeight": img_height,
                "imageWidth": img_width
            }
            
            # 轉換標註
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                shape = self._convert_yolo_line_to_shape(line, img_width, img_height)
                if shape:
                    labelme_data['shapes'].append(shape)
            
            # 保存LabelMe文件
            output_file = output_dir / f"{input_file.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"轉換成功: {input_file} -> {output_file}")
            self.stats['success'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"轉換失敗: {input_file}, 錯誤: {e}")
            self.stats['failed'] += 1
            return False
    
    def validate_input(self, input_file: Path) -> bool:
        """
        驗證YOLO文件格式
        
        Args:
            input_file: 輸入文件路徑
            
        Returns:
            bool: 文件格式是否有效
        """
        try:
            if input_file.suffix.lower() != '.txt':
                return False
            
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 檢查YOLO格式
            for line in lines[:3]:  # 檢查前3行
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) < 5:
                    return False
                
                try:
                    class_id = int(parts[0])
                    coords = [float(x) for x in parts[1:5]]
                    
                    # 檢查座標範圍
                    if not all(0 <= coord <= 1 for coord in coords):
                        return False
                        
                except ValueError:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _convert_yolo_line_to_shape(self, line: str, img_width: int, img_height: int) -> Optional[Dict[str, Any]]:
        """
        將YOLO標註行轉換為LabelMe shape
        
        Args:
            line: YOLO標註行
            img_width: 圖像寬度
            img_height: 圖像高度
            
        Returns:
            Optional[Dict[str, Any]]: LabelMe shape數據
        """
        try:
            parts = line.split()
            if len(parts) < 5:
                return None
            
            class_id = int(parts[0])
            center_x = float(parts[1])
            center_y = float(parts[2])
            width = float(parts[3])
            height = float(parts[4])
            
            # 轉換為像素座標
            center_x_px = center_x * img_width
            center_y_px = center_y * img_height
            width_px = width * img_width
            height_px = height * img_height
            
            # 計算邊界框座標
            xmin = center_x_px - width_px / 2
            ymin = center_y_px - height_px / 2
            xmax = center_x_px + width_px / 2
            ymax = center_y_px + height_px / 2
            
            # 獲取類別名稱
            label = self._get_class_name(class_id)
            
            # 創建LabelMe shape
            shape = {
                "label": label,
                "points": [[xmin, ymin], [xmax, ymax]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            }
            
            return shape
            
        except Exception as e:
            self.logger.error(f"轉換YOLO行失敗: {e}")
            return None
    
    def _get_class_name(self, class_id: int) -> str:
        """
        獲取類別名稱
        
        Args:
            class_id: 類別ID
            
        Returns:
            str: 類別名稱
        """
        if 0 <= class_id < len(self.classes):
            return self.classes[class_id]
        else:
            return f"class_{class_id}"
    
    def _find_image_file(self, txt_file: Path) -> Optional[Path]:
        """
        查找對應的圖像文件
        
        Args:
            txt_file: YOLO TXT文件路徑
            
        Returns:
            Optional[Path]: 圖像文件路徑
        """
        common_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        for ext in common_extensions:
            image_file = txt_file.parent / f"{txt_file.stem}{ext}"
            if image_file.exists():
                return image_file
        return None
    
    def load_classes_from_file(self, classes_file: Path):
        """
        從classes.txt文件載入類別名稱
        
        Args:
            classes_file: classes.txt文件路徑
        """
        try:
            with open(classes_file, 'r', encoding='utf-8') as f:
                self.classes = [line.strip() for line in f.readlines() if line.strip()]
            
            self.logger.info(f"載入了 {len(self.classes)} 個類別")
            
        except Exception as e:
            self.logger.error(f"載入類別文件失敗: {e}")


class COCOToLabelMeConverter(ConversionStrategy):
    """
    COCO到LabelMe格式轉換器
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化COCO到LabelMe轉換器
        
        Args:
            logger: 日誌記錄器
        """
        super().__init__(logger)
        self.coco_data = None
        self.categories = {}
    
    def convert_file(self, input_file: Path, output_dir: Path, **kwargs) -> bool:
        """
        轉換COCO文件到LabelMe格式
        
        Args:
            input_file: COCO JSON文件路徑
            output_dir: 輸出目錄路徑
            **kwargs: 額外參數
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            if not self.validate_input(input_file):
                self.logger.warning(f"跳過無效文件: {input_file}")
                self.stats['skipped'] += 1
                return False
            
            # 載入COCO數據
            if self.coco_data is None:
                self._load_coco_data(input_file)
            
            # 轉換每個圖像的標註
            success_count = 0
            for image_info in self.coco_data['images']:
                try:
                    if self._convert_coco_image(image_info, output_dir):
                        success_count += 1
                    self.stats['processed'] += 1
                except Exception as e:
                    self.logger.error(f"轉換圖像失敗: {image_info['file_name']}, 錯誤: {e}")
                    self.stats['failed'] += 1
            
            self.stats['success'] += success_count
            self.logger.info(f"COCO轉換完成: 成功 {success_count} 個圖像")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"轉換COCO文件失敗: {input_file}, 錯誤: {e}")
            self.stats['failed'] += 1
            return False
    
    def validate_input(self, input_file: Path) -> bool:
        """
        驗證COCO文件格式
        
        Args:
            input_file: 輸入文件路徑
            
        Returns:
            bool: 文件格式是否有效
        """
        try:
            if input_file.suffix.lower() != '.json':
                return False
            
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 檢查COCO格式必需欄位
            required_fields = ['images', 'annotations', 'categories']
            return all(field in data for field in required_fields)
            
        except Exception:
            return False
    
    def _load_coco_data(self, coco_file: Path):
        """
        載入COCO數據
        
        Args:
            coco_file: COCO JSON文件路徑
        """
        with open(coco_file, 'r', encoding='utf-8') as f:
            self.coco_data = json.load(f)
        
        # 建立類別映射
        for category in self.coco_data['categories']:
            self.categories[category['id']] = category['name']
        
        self.logger.info(f"載入COCO數據: {len(self.coco_data['images'])} 個圖像, {len(self.coco_data['annotations'])} 個標註")
    
    def _convert_coco_image(self, image_info: Dict[str, Any], output_dir: Path) -> bool:
        """
        轉換單個COCO圖像的標註
        
        Args:
            image_info: COCO圖像信息
            output_dir: 輸出目錄
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            image_id = image_info['id']
            file_name = image_info['file_name']
            img_width = image_info['width']
            img_height = image_info['height']
            
            # 創建LabelMe數據結構
            labelme_data = {
                "version": "5.0.1",
                "flags": {},
                "shapes": [],
                "imagePath": file_name,
                "imageData": None,
                "imageHeight": img_height,
                "imageWidth": img_width
            }
            
            # 獲取該圖像的所有標註
            image_annotations = [ann for ann in self.coco_data['annotations'] if ann['image_id'] == image_id]
            
            # 轉換標註
            for annotation in image_annotations:
                shape = self._convert_coco_annotation_to_shape(annotation)
                if shape:
                    labelme_data['shapes'].append(shape)
            
            # 保存LabelMe文件
            output_file = output_dir / f"{Path(file_name).stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"轉換COCO圖像失敗: {e}")
            return False
    
    def _convert_coco_annotation_to_shape(self, annotation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        將COCO標註轉換為LabelMe shape
        
        Args:
            annotation: COCO標註數據
            
        Returns:
            Optional[Dict[str, Any]]: LabelMe shape數據
        """
        try:
            category_id = annotation['category_id']
            label = self.categories.get(category_id, f"category_{category_id}")
            
            # 處理邊界框
            if 'bbox' in annotation:
                bbox = annotation['bbox']
                x, y, width, height = bbox
                
                # COCO bbox格式: [x, y, width, height]
                points = [[x, y], [x + width, y + height]]
                
                shape = {
                    "label": label,
                    "points": points,
                    "group_id": None,
                    "shape_type": "rectangle",
                    "flags": {}
                }
                
                return shape
            
            # 處理分割標註（轉換為多邊形）
            elif 'segmentation' in annotation and annotation['segmentation']:
                segmentation = annotation['segmentation']
                
                # 處理RLE格式或多邊形格式
                if isinstance(segmentation, list) and len(segmentation) > 0:
                    # 多邊形格式
                    polygon = segmentation[0]  # 取第一個多邊形
                    if len(polygon) >= 6:  # 至少3個點
                        points = [[polygon[i], polygon[i+1]] for i in range(0, len(polygon), 2)]
                        
                        shape = {
                            "label": label,
                            "points": points,
                            "group_id": None,
                            "shape_type": "polygon",
                            "flags": {}
                        }
                        
                        return shape
            
            return None
            
        except Exception as e:
            self.logger.error(f"轉換COCO標註失敗: {e}")
            return None