# 圖像數據集處理工具集 v2.0

一個專為計算機視覺任務設計的完整數據集處理解決方案，支持圖形化界面和命令行操作。

![Version](https://img.shields.io/badge/version-2.0-blue)
![Python](https://img.shields.io/badge/python-3.7+-green)
![License](https://img.shields.io/badge/license-MIT-orange)

## ✨ 主要功能

### 🖥️ GUI應用程式
- **統一的圖形化界面**，操作簡單直觀
- **實時處理日誌**，進度一目了然
- **配置管理**，設置保存和載入
- **可視化預覽**，結果即時查看

### 🔧 核心工具
1. **標籤格式轉換器** - 轉換各種標籤格式為LabelMe格式
2. **標籤編輯器** - 合併、重命名、刪除標籤
3. **數據集分割器** - 智能分割訓練/驗證/測試集
4. **圖像增強器** - 區域融合式數據增強
5. **全景圖像擴增器** ⭐ - 專業全景圖像處理，支持外方位參數

## 🚀 快速開始

### 1. 安裝依賴
```bash
pip install -r requirements.txt
```

### 2. 三種啟動方式

#### 方式一：快速啟動（推薦新手）
```bash
python start.py
```
提供選單式界面，輕鬆選擇所需工具。

#### 方式二：GUI應用程式（推薦）
```bash
python gui_application.py
```
直接啟動圖形化界面，功能完整且易於使用。

#### 方式三：命令行工具
```bash
# 全景圖像擴增示例
python panorama_augmenter.py \
    --input your_panorama.jpg \
    --output results \
    --omega 15.0 --phi -10.0 --kappa 45.0 \
    --methods orientation \
    --visualize
```

## 🎯 特色功能：全景圖像擴增器

### 📋 支持的擴增方法
- **orientation** - 基於外方位的視角擴增（推薦）
- **rotation** - 隨機旋轉擴增
- **perspective** - 隨機視角變換  
- **tilt** - 傾斜擴增
- **multi_angle** - 多角度擴增

### 🔢 外方位參數支持
- **Omega** - 俯仰角（Pitch）
- **Phi** - 橫滾角（Roll）
- **Kappa** - 偏航角（Yaw）
- **Excel批量輸入** - 支持批量處理多張圖像

### 🖼️ 可視化功能
- **實時預覽** - 處理完成後自動顯示結果
- **網格布局** - 原始圖像和擴增結果對比顯示
- **保存功能** - 可視化結果保存為PNG文件

## 📁 項目結構

```
資料前處理2.0/
├── 🚀 start.py                 # 快速啟動腳本
├── 🖥️ gui_application.py       # GUI應用程式
├── 🔧 panorama_augmenter.py    # 全景圖像擴增器
├── 📊 annotation_converter.py  # 標籤格式轉換器
├── ✏️ annotation_editor.py     # 標籤編輯器
├── 📂 dataset_divider.py       # 數據集分割器
├── 🔄 img_augmenter.py         # 圖像增強器
├── ⚙️ img_dataset_pipeline.py  # 整合流程
├── 📖 使用手冊.md              # 詳細使用說明
├── 📋 requirements.txt         # 依賴清單
└── 🗂️ config.json             # 配置文件範例
```

## 📖 使用手冊

詳細的使用說明請參考 [使用手冊.md](使用手冊.md)，包含：
- 📥 安裝指南
- ⚡ 快速開始
- 🖥️ GUI使用說明
- 💻 命令行參數
- ⚙️ 配置文件格式
- 🔧 故障排解
- ❓ 常見問題

## 🔧 系統要求

- **Python**: 3.7+
- **操作系統**: Windows / macOS / Linux
- **依賴庫**: OpenCV, NumPy, Matplotlib, Pandas 等
- **GUI支持**: tkinter（Python內建）

## 💡 使用示例

### GUI模式處理全景圖像
1. 啟動GUI：`python gui_application.py`
2. 切換到"全景圖像擴增器"標籤
3. 選擇圖像文件和設置外方位參數
4. 選擇擴增方法（推薦使用"orientation"）
5. 點擊"開始處理"
6. 查看日誌區域的處理進度
7. 處理完成後查看可視化結果

### 命令行批量處理
```bash
# 準備外方位Excel文件，包含image_name, omega, phi, kappa列
python panorama_augmenter.py \
    --input input_directory \
    --output results \
    --excel orientations.xlsx \
    --methods orientation perspective \
    --batch
```

## 🛠️ 技術特點

- **標準化設計** - 遵循攝影測量標準OPK旋轉矩陣
- **精確轉換** - 球面座標系統確保標籤轉換準確性
- **多線程支持** - 提升大數據集處理效率
- **模塊化架構** - 工具可獨立使用或組合使用
- **配置驅動** - 豐富的配置選項滿足不同需求

## 📝 更新日誌

### v2.0 (2024-12)
- ✨ 新增GUI應用程式
- ✨ 新增全景圖像擴增器
- ✨ 新增可視化功能
- ✨ 新增快速啟動腳本
- 📖 完善使用手冊

### v1.0
- 🔧 基礎命令行工具集
- 📊 標籤格式轉換
- ✏️ 標籤編輯功能
- 📂 數據集分割
- 🔄 圖像增強

## 🤝 貢獻

歡迎提交Issue和Pull Request來幫助改進這個項目！

## 📄 許可證

本項目採用MIT許可證 - 詳見 [LICENSE](LICENSE) 文件

## 📞 支持

- 📖 查看 [使用手冊.md](使用手冊.md)
- 🐛 遇到問題請檢查日誌輸出
- 💬 歡迎提交Issue討論

---

⭐ 如果這個項目對你有幫助，請給個Star！

💡 建議優先使用GUI版本，功能完整且操作簡單！