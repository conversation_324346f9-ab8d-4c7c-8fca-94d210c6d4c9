#!/usr/bin/env python3
"""
🎯 ROI預覽生成器 (ROI Preview Generator)
用於生成ROI區域和切片配置的可視化預覽圖

功能：
- 顯示ROI區域，四個方向用不同顏色標示
- 在ROI區域內顯示切片配置網格
- 添加詳細的參數標註文字
- 支援預覽模式，只處理第一張圖像
"""

import cv2
import numpy as np
import os
from pathlib import Path
from typing import Dict, Tuple, Optional, List


class ROIPreviewGenerator:
    """ROI和切片配置預覽生成器"""
    
    def __init__(self):
        """初始化預覽生成器"""
        # 四個方向的顏色 (BGR格式)
        self.colors = {
            'top': (0, 0, 255),      # 紅色 - 上方
            'bottom': (0, 255, 0),   # 綠色 - 下方  
            'left': (255, 0, 0),     # 藍色 - 左方
            'right': (0, 255, 255)   # 黃色 - 右方
        }
        
        # 字體設定
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.6
        self.font_thickness = 2
        self.text_color = (255, 255, 255)  # 白色文字
        self.text_bg_color = (0, 0, 0)     # 黑色背景
        
        # 邊框設定
        self.border_thickness = 3
        self.grid_thickness = 1
        
    def calculate_roi_coordinates(self, image_shape: Tuple[int, int], roi_ratios: Dict[str, float]) -> Dict[str, int]:
        """
        計算ROI區域座標
        
        Args:
            image_shape: 圖像形狀 (height, width)
            roi_ratios: ROI比例字典 {'top': 3.0, 'bottom': 2.8, 'left': 1.3, 'right': 1.7}
            
        Returns:
            ROI座標字典
        """
        h, w = image_shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # 根據ratio計算實際像素距離 (ratio/10 作為百分比)
        roi_top = max(0, center_y - int(h * roi_ratios.get('top', 3.0) / 10))
        roi_bottom = min(h, center_y + int(h * roi_ratios.get('bottom', 2.8) / 10))
        roi_left = max(0, center_x - int(w * roi_ratios.get('left', 1.3) / 10))
        roi_right = min(w, center_x + int(w * roi_ratios.get('right', 1.7) / 10))
        
        return {
            'top': roi_top,
            'bottom': roi_bottom,
            'left': roi_left,
            'right': roi_right,
            'center_x': center_x,
            'center_y': center_y
        }
    
    def draw_roi_borders(self, image: np.ndarray, roi_coords: Dict[str, int]) -> np.ndarray:
        """
        繪製ROI邊框，四個方向用不同顏色標示
        
        Args:
            image: 輸入圖像
            roi_coords: ROI座標
            
        Returns:
            繪製了ROI邊框的圖像
        """
        preview_image = image.copy()
        
        roi_top = roi_coords['top']
        roi_bottom = roi_coords['bottom']
        roi_left = roi_coords['left']
        roi_right = roi_coords['right']
        
        # 繪製四個方向的邊框
        # 上邊框 - 紅色
        cv2.line(preview_image, (roi_left, roi_top), (roi_right, roi_top), 
                self.colors['top'], self.border_thickness)
        
        # 下邊框 - 綠色
        cv2.line(preview_image, (roi_left, roi_bottom), (roi_right, roi_bottom), 
                self.colors['bottom'], self.border_thickness)
        
        # 左邊框 - 藍色
        cv2.line(preview_image, (roi_left, roi_top), (roi_left, roi_bottom), 
                self.colors['left'], self.border_thickness)
        
        # 右邊框 - 黃色
        cv2.line(preview_image, (roi_right, roi_top), (roi_right, roi_bottom), 
                self.colors['right'], self.border_thickness)
        
        # 繪製中心點
        center_x = roi_coords['center_x']
        center_y = roi_coords['center_y']
        cv2.circle(preview_image, (center_x, center_y), 5, (255, 255, 255), -1)
        cv2.circle(preview_image, (center_x, center_y), 5, (0, 0, 0), 2)
        
        return preview_image
    
    def draw_slice_grid(self, image: np.ndarray, roi_coords: Dict[str, int], 
                       slice_config: Dict[str, float]) -> np.ndarray:
        """
        在ROI區域內繪製切片網格
        
        Args:
            image: 輸入圖像
            roi_coords: ROI座標
            slice_config: 切片配置 {'height': 320, 'width': 320, 'overlap_ratio': 0.2}
            
        Returns:
            繪製了切片網格的圖像
        """
        preview_image = image.copy()
        
        roi_top = roi_coords['top']
        roi_bottom = roi_coords['bottom']
        roi_left = roi_coords['left']
        roi_right = roi_coords['right']
        
        roi_height = roi_bottom - roi_top
        roi_width = roi_right - roi_left
        
        slice_h = slice_config.get('height', 320)
        slice_w = slice_config.get('width', 320)
        overlap_ratio = slice_config.get('overlap_ratio', 0.2)
        
        # 計算步長 (考慮重疊)
        step_h = int(slice_h * (1 - overlap_ratio))
        step_w = int(slice_w * (1 - overlap_ratio))
        
        # 繪製垂直網格線
        x = roi_left
        slice_index = 0
        while x < roi_right:
            if x > roi_left:  # 不繪製左邊界線，已經有ROI邊框
                cv2.line(preview_image, (x, roi_top), (x, roi_bottom), 
                        (128, 128, 128), self.grid_thickness)
            
            # 標註切片索引
            if x + slice_w <= roi_right:
                text = f"X{slice_index}"
                text_size = cv2.getTextSize(text, self.font, self.font_scale * 0.5, 1)[0]
                text_x = x + 5
                text_y = roi_top + 20
                cv2.rectangle(preview_image, (text_x-2, text_y-15), 
                            (text_x + text_size[0]+2, text_y+5), self.text_bg_color, -1)
                cv2.putText(preview_image, text, (text_x, text_y), 
                          self.font, self.font_scale * 0.5, self.text_color, 1)
            
            x += step_w
            slice_index += 1
        
        # 繪製水平網格線
        y = roi_top
        slice_index = 0
        while y < roi_bottom:
            if y > roi_top:  # 不繪製上邊界線，已經有ROI邊框
                cv2.line(preview_image, (roi_left, y), (roi_right, y), 
                        (128, 128, 128), self.grid_thickness)
            
            # 標註切片索引
            if y + slice_h <= roi_bottom:
                text = f"Y{slice_index}"
                text_size = cv2.getTextSize(text, self.font, self.font_scale * 0.5, 1)[0]
                text_x = roi_left + 5
                text_y = y + 35
                cv2.rectangle(preview_image, (text_x-2, text_y-15), 
                            (text_x + text_size[0]+2, text_y+5), self.text_bg_color, -1)
                cv2.putText(preview_image, text, (text_x, text_y), 
                          self.font, self.font_scale * 0.5, self.text_color, 1)
            
            y += step_h
            slice_index += 1
        
        # 繪製重疊區域標示（半透明）
        overlay = preview_image.copy()
        
        # 標示overlap區域
        if overlap_ratio > 0:
            overlap_h = int(slice_h * overlap_ratio)
            overlap_w = int(slice_w * overlap_ratio)
            
            # 垂直重疊區域
            x = roi_left + step_w
            while x < roi_right:
                if x + overlap_w <= roi_right:
                    cv2.rectangle(overlay, (x - overlap_w//2, roi_top), 
                                (x + overlap_w//2, roi_bottom), (255, 255, 0), -1)
                x += step_w
            
            # 水平重疊區域
            y = roi_top + step_h
            while y < roi_bottom:
                if y + overlap_h <= roi_bottom:
                    cv2.rectangle(overlay, (roi_left, y - overlap_h//2), 
                                (roi_right, y + overlap_h//2), (255, 255, 0), -1)
                y += step_h
        
        # 混合重疊區域
        alpha = 0.3
        preview_image = cv2.addWeighted(preview_image, 1-alpha, overlay, alpha, 0)
        
        return preview_image
    
    def add_parameter_annotations(self, image: np.ndarray, roi_coords: Dict[str, int], 
                                roi_ratios: Dict[str, float], slice_config: Dict[str, float]) -> np.ndarray:
        """
        添加參數標註文字
        
        Args:
            image: 輸入圖像
            roi_coords: ROI座標
            roi_ratios: ROI比例
            slice_config: 切片配置
            
        Returns:
            添加了標註的圖像
        """
        preview_image = image.copy()
        h, w = image.shape[:2]
        
        # 準備標註文字
        annotations = [
            "🎯 ROI & 切片配置預覽",
            "",
            "ROI 配置:",
            f"  上方保留倍數: {roi_ratios.get('top', 3.0):.1f}",
            f"  下方保留倍數: {roi_ratios.get('bottom', 2.8):.1f}",
            f"  左方保留倍數: {roi_ratios.get('left', 1.3):.1f}",
            f"  右方保留倍數: {roi_ratios.get('right', 1.7):.1f}",
            "",
            "切片配置:",
            f"  切片高度: {slice_config.get('height', 320)}px",
            f"  切片寬度: {slice_config.get('width', 320)}px",
            f"  重疊比例: {slice_config.get('overlap_ratio', 0.2):.1%}",
            "",
            "顏色說明:",
            "  🔴 紅色 - 上方邊界",
            "  🟢 綠色 - 下方邊界", 
            "  🔵 藍色 - 左方邊界",
            "  🟡 黃色 - 右方邊界",
            "  🟨 半透明黃色 - 重疊區域"
        ]
        
        # 計算文字區域
        line_height = 25
        text_area_height = len(annotations) * line_height + 20
        text_area_width = 350
        
        # 選擇文字位置（避免遮擋ROI區域）
        roi_left = roi_coords['left']
        roi_right = roi_coords['right']
        
        if roi_left > text_area_width + 20:
            # 放在左側
            text_x = 10
        elif w - roi_right > text_area_width + 20:
            # 放在右側
            text_x = w - text_area_width - 10
        else:
            # 放在底部
            text_x = 10
        
        text_y = 30
        
        # 繪製文字背景
        cv2.rectangle(preview_image, (text_x - 10, text_y - 20), 
                     (text_x + text_area_width, text_y + text_area_height), 
                     (0, 0, 0), -1)
        cv2.rectangle(preview_image, (text_x - 10, text_y - 20), 
                     (text_x + text_area_width, text_y + text_area_height), 
                     (255, 255, 255), 2)
        
        # 繪製文字
        for i, text in enumerate(annotations):
            if text.strip():  # 跳過空行
                y_pos = text_y + i * line_height
                cv2.putText(preview_image, text, (text_x, y_pos), 
                          self.font, self.font_scale, self.text_color, self.font_thickness)
        
        # 在ROI區域添加尺寸標註
        roi_width = roi_coords['right'] - roi_coords['left']
        roi_height = roi_coords['bottom'] - roi_coords['top']
        
        # ROI尺寸標註
        roi_size_text = f"ROI: {roi_width} x {roi_height} px"
        text_size = cv2.getTextSize(roi_size_text, self.font, self.font_scale, self.font_thickness)[0]
        text_x = roi_coords['center_x'] - text_size[0] // 2
        text_y = roi_coords['top'] - 10
        
        if text_y < 30:  # 如果太靠近頂部，放到ROI內部
            text_y = roi_coords['top'] + 30
        
        cv2.rectangle(preview_image, (text_x - 5, text_y - 20), 
                     (text_x + text_size[0] + 5, text_y + 5), self.text_bg_color, -1)
        cv2.putText(preview_image, roi_size_text, (text_x, text_y), 
                   self.font, self.font_scale, self.text_color, self.font_thickness)
        
        return preview_image
    
    def generate_preview(self, image: np.ndarray, roi_ratios: Dict[str, float], 
                        slice_config: Dict[str, float], save_path: Optional[str] = None) -> np.ndarray:
        """
        生成完整的ROI和切片配置預覽圖
        
        Args:
            image: 輸入圖像
            roi_ratios: ROI比例配置
            slice_config: 切片配置
            save_path: 保存路徑（可選）
            
        Returns:
            預覽圖像
        """
        # 1. 計算ROI座標
        roi_coords = self.calculate_roi_coordinates(image.shape, roi_ratios)
        
        # 2. 繪製ROI邊框
        preview_image = self.draw_roi_borders(image, roi_coords)
        
        # 3. 繪製切片網格
        preview_image = self.draw_slice_grid(preview_image, roi_coords, slice_config)
        
        # 4. 添加參數標註
        preview_image = self.add_parameter_annotations(preview_image, roi_coords, roi_ratios, slice_config)
        
        # 5. 保存圖像（如果指定了路徑）
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            cv2.imwrite(save_path, preview_image)
            print(f"📸 ROI預覽圖已保存: {save_path}")
        
        return preview_image
    
    def generate_first_image_preview(self, input_dir: str, output_dir: str, 
                                   roi_ratios: Dict[str, float], slice_config: Dict[str, float]) -> Optional[str]:
        """
        生成資料夾中第一張圖像的預覽
        
        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            roi_ratios: ROI比例配置
            slice_config: 切片配置
            
        Returns:
            預覽圖像路徑（如果成功）
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            print(f"❌ 輸入目錄不存在: {input_dir}")
            return None
        
        # 尋找第一張圖像
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"❌ 在 {input_dir} 中沒有找到圖像文件")
            return None
        
        # 排序並選擇第一張
        image_files.sort()
        first_image_path = image_files[0]
        
        print(f"📷 選擇第一張圖像進行預覽: {first_image_path.name}")
        
        # 讀取圖像
        image = cv2.imread(str(first_image_path))
        if image is None:
            print(f"❌ 無法讀取圖像: {first_image_path}")
            return None
        
        # 生成預覽圖
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        preview_filename = f"{first_image_path.stem}_roi_slice_preview.jpg"
        preview_path = output_path / "preview" / preview_filename
        
        self.generate_preview(image, roi_ratios, slice_config, str(preview_path))
        
        return str(preview_path)


# 工廠函數
def create_roi_preview_generator() -> ROIPreviewGenerator:
    """創建ROI預覽生成器的工廠函數"""
    return ROIPreviewGenerator()


if __name__ == "__main__":
    print("🎯 ROI預覽生成器")
    print("用於生成ROI區域和切片配置的可視化預覽圖")