# 資料前處理模組漸進式重構指南

## 📋 重構概述

### 重構目標
1. **提升代碼可維護性**：消除重複代碼，建立統一的架構基礎
2. **改善錯誤處理**：建立統一的異常處理和日誌記錄機制
3. **增強模組化程度**：將大型類別拆分為職責單一的小類別
4. **統一配置管理**：建立統一的配置系統
5. **保持向後兼容**：確保現有API和工作流程不受影響

### 重構原則
- **漸進式改進**：分階段執行，每個階段都可獨立驗證
- **向後兼容**：保持現有API接口不變
- **最小風險**：優先重構風險最低的部分
- **測試驅動**：每個階段都要有充分的測試

---

## 🎯 重構階段規劃

### 階段1：基礎設施建立（1-2週）
**目標：建立統一的基礎架構，不影響現有功能**

#### 1.1 創建共享模組結構
```
資料前處理/
├── shared/                    # 新增：共享基礎模組
│   ├── __init__.py
│   ├── base_tool.py          # 工具基類
│   ├── config_manager.py     # 統一配置管理
│   ├── file_utils.py         # 文件處理工具
│   ├── path_utils.py         # 路徑處理工具
│   ├── thread_utils.py       # 多線程工具
│   ├── logger_utils.py       # 日誌工具
│   └── exceptions.py         # 自定義異常
├── 重構階段指南.md           # 本文件
└── ... (現有文件保持不變)
```

#### 1.2 基礎類別設計

**shared/base_tool.py**
```python
import logging
import os
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

class BaseTool(ABC):
    """所有工具的統一基類"""
    
    def __init__(self, 
                 input_dir: Optional[str] = None,
                 output_dir: Optional[str] = None,
                 logger: Optional[logging.Logger] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        基礎工具初始化
        
        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            logger: 日誌記錄器
            config: 配置字典
        """
        self.input_dir = Path(input_dir) if input_dir else None
        self.output_dir = Path(output_dir) if output_dir else None
        self.logger = logger or self._create_default_logger()
        self.config = config or {}
        
        # 統計信息
        self.stats = {
            'start_time': None,
            'end_time': None,
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0
        }
        
        # 執行狀態
        self.is_running = False
        self.should_stop = False
    
    def _create_default_logger(self) -> logging.Logger:
        """創建默認日誌記錄器"""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def validate_inputs(self) -> bool:
        """
        驗證輸入參數
        
        Returns:
            bool: 驗證是否通過
        """
        pass
    
    @abstractmethod
    def _process_single_item(self, item_path: Path) -> bool:
        """
        處理單個項目的抽象方法
        
        Args:
            item_path: 項目路徑
            
        Returns:
            bool: 處理是否成功
        """
        pass
    
    def run(self, **kwargs) -> Dict[str, Any]:
        """
        執行主要邏輯
        
        Returns:
            Dict[str, Any]: 執行結果統計
        """
        if not self.validate_inputs():
            raise ValueError("輸入驗證失敗")
        
        self.stats['start_time'] = datetime.now()
        self.is_running = True
        
        try:
            self.logger.info(f"開始執行 {self.__class__.__name__}")
            result = self._execute_main_logic(**kwargs)
            self.logger.info(f"執行完成 {self.__class__.__name__}")
            return result
        except Exception as e:
            self.logger.error(f"執行過程中發生錯誤: {e}")
            raise
        finally:
            self.stats['end_time'] = datetime.now()
            self.is_running = False
    
    def _execute_main_logic(self, **kwargs) -> Dict[str, Any]:
        """執行主要邏輯的默認實現"""
        # 子類可以重寫此方法實現自定義邏輯
        return self.stats
    
    def stop(self):
        """停止執行"""
        self.should_stop = True
        self.logger.info(f"收到停止信號: {self.__class__.__name__}")
    
    def get_progress(self) -> float:
        """獲取處理進度"""
        if self.stats['total_files'] == 0:
            return 0.0
        return (self.stats['processed_files'] + self.stats['failed_files']) / self.stats['total_files']
```

**shared/config_manager.py**
```python
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Union
from dataclasses import dataclass, asdict
from .exceptions import ConfigError

@dataclass
class BaseConfig:
    """基礎配置類"""
    max_workers: int = None
    log_level: str = "INFO"
    backup_original: bool = True
    
    def __post_init__(self):
        if self.max_workers is None:
            self.max_workers = min(32, (os.cpu_count() or 1) * 2)
        self.validate()
    
    def validate(self):
        """驗證配置參數"""
        if self.max_workers <= 0:
            raise ConfigError("max_workers must be positive")
        
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            raise ConfigError(f"log_level must be one of {valid_log_levels}")

class ConfigManager:
    """統一配置管理器"""
    
    def __init__(self, config_file: Union[str, Path] = "config.yaml"):
        self.config_file = Path(config_file)
        self.config_data = {}
        
        if self.config_file.exists():
            self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """載入配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.json':
                    self.config_data = json.load(f)
                elif self.config_file.suffix.lower() in ['.yaml', '.yml']:
                    self.config_data = yaml.safe_load(f)
                else:
                    raise ConfigError(f"不支援的配置文件格式: {self.config_file.suffix}")
            
            return self.config_data
        except Exception as e:
            raise ConfigError(f"載入配置文件失敗: {e}")
    
    def save_config(self, config_data: Dict[str, Any] = None):
        """保存配置文件"""
        data = config_data or self.config_data
        
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.json':
                    json.dump(data, f, indent=2, ensure_ascii=False)
                elif self.config_file.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            raise ConfigError(f"保存配置文件失敗: {e}")
    
    def get(self, key: str, default=None):
        """獲取配置值"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """設置配置值"""
        keys = key.split('.')
        data = self.config_data
        
        for k in keys[:-1]:
            if k not in data:
                data[k] = {}
            data = data[k]
        
        data[keys[-1]] = value
```

**shared/exceptions.py**
```python
class ProcessingError(Exception):
    """處理過程中的基礎異常"""
    def __init__(self, message: str, file_path: str = None, details: dict = None):
        super().__init__(message)
        self.file_path = file_path
        self.details = details or {}

class ConfigError(ProcessingError):
    """配置相關錯誤"""
    pass

class ValidationError(ProcessingError):
    """驗證錯誤"""
    pass

class ConversionError(ProcessingError):
    """轉換錯誤"""
    pass

class FileOperationError(ProcessingError):
    """文件操作錯誤"""
    pass
```

#### 1.3 第一階段實施步驟

1. **創建shared目錄和基礎文件**
2. **實現基礎類別和工具函數**
3. **創建配置模板**
4. **編寫基礎測試**

#### 1.4 驗證標準
- [ ] shared模組可以正常導入
- [ ] BaseTool基類功能正常
- [ ] ConfigManager可以正確處理配置文件
- [ ] 異常類別正常工作
- [ ] 不影響現有功能

---

### 階段2：核心類別重構（2-3週）
**目標：重構最複雜的AnnotationConverter類**

#### 2.1 分析AnnotationConverter職責
當前問題：
- 類別過大（1800+行）
- 職責混亂（格式檢測、轉換、圖像處理、統計）
- 難以測試和維護

#### 2.2 重構設計

**新的類別設計：**
```python
# annotation_converter_v2.py (新版本)
from .shared.base_tool import BaseTool
from .shared.exceptions import ConversionError
from typing import Dict, List, Protocol

class FormatDetector:
    """專責格式檢測"""
    
    def detect_format(self, file_path: Path) -> str:
        """檢測標註格式"""
        if file_path.suffix.lower() == '.json':
            return self._detect_json_format(file_path)
        elif file_path.suffix.lower() == '.xml':
            return 'voc'
        elif file_path.suffix.lower() == '.txt':
            return 'yolo'
        else:
            raise ConversionError(f"不支援的文件格式: {file_path.suffix}")
    
    def _detect_json_format(self, file_path: Path) -> str:
        """檢測JSON格式（LabelMe vs COCO）"""
        # 實現檢測邏輯
        pass

class ConversionStrategy(Protocol):
    """轉換策略接口"""
    
    def convert(self, input_file: Path, output_dir: Path) -> bool:
        """轉換文件"""
        pass

class LabelMeToYOLOConverter(ConversionStrategy):
    """LabelMe到YOLO轉換器"""
    
    def __init__(self, class_mapping: Dict[str, int] = None):
        self.class_mapping = class_mapping or {}
    
    def convert(self, input_file: Path, output_dir: Path) -> bool:
        """執行LabelMe到YOLO的轉換"""
        try:
            # 實現轉換邏輯
            return True
        except Exception as e:
            raise ConversionError(f"轉換失敗: {e}", str(input_file))

class ImageProcessor:
    """專責圖像處理"""
    
    def __init__(self, resize_params: Dict = None, quality: int = 75):
        self.resize_params = resize_params or {}
        self.quality = quality
    
    def process_image(self, image_path: Path, output_path: Path) -> bool:
        """處理圖像"""
        # 實現圖像處理邏輯
        pass

class AnnotationConverterV2(BaseTool):
    """重構後的標註轉換器"""
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 input_format: str = 'auto',
                 output_format: str = 'yolo',
                 **kwargs):
        super().__init__(input_dir, output_dir, **kwargs)
        
        self.input_format = input_format
        self.output_format = output_format
        
        # 組合模式：注入依賴
        self.format_detector = FormatDetector()
        self.image_processor = ImageProcessor(
            resize_params=self.config.get('resize_params'),
            quality=self.config.get('quality', 75)
        )
        
        # 策略模式：根據格式選擇轉換器
        self.converter_strategy = self._create_converter_strategy()
    
    def _create_converter_strategy(self) -> ConversionStrategy:
        """創建轉換策略"""
        strategy_map = {
            ('labelme', 'yolo'): LabelMeToYOLOConverter,
            ('labelme', 'voc'): LabelMeToVOCConverter,
            ('voc', 'yolo'): VOCToYOLOConverter,
            # 添加更多轉換策略
        }
        
        key = (self.input_format, self.output_format)
        if key in strategy_map:
            return strategy_map[key](self.config.get('class_mapping', {}))
        else:
            raise ConversionError(f"不支援的轉換: {self.input_format} -> {self.output_format}")
    
    def validate_inputs(self) -> bool:
        """驗證輸入"""
        if not self.input_dir.exists():
            self.logger.error(f"輸入目錄不存在: {self.input_dir}")
            return False
        
        if self.input_format == 'auto':
            # 自動檢測格式
            sample_files = list(self.input_dir.glob('*.json'))[:3]
            if sample_files:
                self.input_format = self.format_detector.detect_format(sample_files[0])
                self.logger.info(f"自動檢測到格式: {self.input_format}")
            else:
                self.logger.error("無法自動檢測格式")
                return False
        
        return True
    
    def _process_single_item(self, item_path: Path) -> bool:
        """處理單個標註文件"""
        try:
            # 轉換標註
            success = self.converter_strategy.convert(item_path, self.output_dir)
            
            # 處理對應的圖像
            image_path = self._find_corresponding_image(item_path)
            if image_path and image_path.exists():
                output_image_path = self.output_dir / f"{item_path.stem}{image_path.suffix}"
                self.image_processor.process_image(image_path, output_image_path)
            
            return success
            
        except Exception as e:
            self.logger.error(f"處理文件失敗: {item_path}, 錯誤: {e}")
            return False
```

#### 2.3 向後兼容適配器

```python
# annotation_converter.py (保持原API)
from .annotation_converter_v2 import AnnotationConverterV2

class AnnotationConverter:
    """原始API的向後兼容適配器"""
    
    def __init__(self, 
                 input_dir,
                 output_dir,
                 input_format='auto',
                 output_format='yolo',
                 logger=None,
                 **kwargs):
        
        # 使用新的實現
        self._converter = AnnotationConverterV2(
            input_dir=input_dir,
            output_dir=output_dir,
            input_format=input_format,
            output_format=output_format,
            logger=logger,
            config=kwargs
        )
    
    def convert_format(self) -> Dict[str, Any]:
        """保持原始API"""
        return self._converter.run()
    
    # 其他原始方法保持不變，委託給新實現
    def get_stats(self):
        return self._converter.stats
```

#### 2.4 實施步驟

1. **創建新的轉換器類別**
2. **實施向後兼容適配器**
3. **編寫單元測試**
4. **逐步遷移使用新API**
5. **驗證功能完整性**

#### 2.5 驗證標準
- [ ] 新轉換器功能完整
- [ ] 向後兼容性測試通過
- [ ] 性能不降低
- [ ] 單元測試覆蓋率 > 80%

---

### 階段3：其他工具類重構（2-3週）
**目標：重構其餘工具類，統一架構**

#### 3.1 重構列表
1. **AnnotationEditor** - 繼承BaseTool
2. **DatasetDivider** - 繼承BaseTool  
3. **ImageAugmenter** - 繼承BaseTool
4. **PanoramaAugmenter** - 繼承BaseTool

#### 3.2 重構模式

```python
# 以DatasetDivider為例
class DatasetDividerV2(BaseTool):
    """重構後的數據集分割器"""
    
    def __init__(self, 
                 input_dir: str,
                 output_dir: str,
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 test_ratio: float = 0.15,
                 **kwargs):
        super().__init__(input_dir, output_dir, **kwargs)
        
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        
        # 驗證比例
        if not abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6:
            raise ValidationError("分割比例總和必須為1.0")
    
    def validate_inputs(self) -> bool:
        """驗證輸入"""
        # 實現驗證邏輯
        return True
    
    def _process_single_item(self, item_path: Path) -> bool:
        """處理單個項目"""
        # 實現處理邏輯
        return True
```

---

### 階段4：GUI重構（3-4週）
**目標：分離GUI和業務邏輯**

#### 4.1 當前問題
- MainWindow類過大（2000+行）
- GUI邏輯與業務邏輯混合
- 難以單獨測試業務邏輯

#### 4.2 重構設計

```python
# gui/controllers/base_controller.py
class BaseController:
    """控制器基類"""
    
    def __init__(self, tool_class, gui_widget):
        self.tool_class = tool_class
        self.gui_widget = gui_widget
        self.worker_thread = None
    
    def start_processing(self, params: Dict[str, Any]):
        """開始處理"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.gui_widget.show_error("任務已在運行中")
            return
        
        # 創建工作線程
        self.worker_thread = ProcessingThread(self.tool_class, params)
        self.worker_thread.progress.connect(self.gui_widget.update_progress)
        self.worker_thread.finished.connect(self.on_finished)
        self.worker_thread.error.connect(self.on_error)
        self.worker_thread.start()
    
    def on_finished(self, result):
        """處理完成"""
        self.gui_widget.show_success(f"處理完成: {result}")
    
    def on_error(self, error):
        """處理錯誤"""
        self.gui_widget.show_error(f"處理失敗: {error}")

# gui/widgets/base_widget.py
class BaseToolWidget(QWidget):
    """工具組件基類"""
    
    def __init__(self, controller_class, tool_class):
        super().__init__()
        self.controller = controller_class(tool_class, self)
        self.create_ui()
    
    def create_ui(self):
        """創建UI（子類實現）"""
        pass
    
    def get_parameters(self) -> Dict[str, Any]:
        """獲取參數（子類實現）"""
        pass
    
    def start_processing(self):
        """開始處理"""
        params = self.get_parameters()
        if self.validate_parameters(params):
            self.controller.start_processing(params)
```

#### 4.3 實施步驟

1. **創建控制器基類**
2. **重構各個工具組件**
3. **實施MVP模式**
4. **單元測試控制器邏輯**

---

### 階段5：整合與優化（1-2週）
**目標：最終整合和性能優化**

#### 5.1 整合任務
1. **統一配置系統**
2. **日誌系統優化**
3. **錯誤處理統一**
4. **文檔更新**

#### 5.2 性能優化
1. **內存使用優化**
2. **並發處理優化**
3. **緩存機制**

---

## 🛠️ 實施指導

### 開發環境準備
```bash
# 1. 創建開發分支
git checkout -b refactor-stage1

# 2. 安裝開發依賴
pip install pytest pytest-cov black flake8 mypy

# 3. 設置pre-commit
pip install pre-commit
pre-commit install
```

### 代碼質量標準
```python
# .flake8
[flake8]
max-line-length = 88
ignore = E203, E266, E501, W503
max-complexity = 10

# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
```

### 測試策略
```python
# tests/test_base_tool.py
import pytest
from shared.base_tool import BaseTool

class MockTool(BaseTool):
    def validate_inputs(self):
        return True
    
    def _process_single_item(self, item_path):
        return True

def test_base_tool_initialization():
    tool = MockTool()
    assert tool.stats['total_files'] == 0
    assert not tool.is_running

def test_base_tool_run():
    tool = MockTool()
    result = tool.run()
    assert 'start_time' in result
    assert 'end_time' in result
```

---

## 📊 進度追蹤

### 階段1檢查清單
- [ ] shared目錄創建完成
- [ ] BaseTool基類實現
- [ ] ConfigManager實現
- [ ] 異常類別定義
- [ ] 基礎測試通過
- [ ] 文檔更新

### 階段2檢查清單
- [ ] AnnotationConverter重構完成
- [ ] 向後兼容適配器實現
- [ ] 單元測試覆蓋率達標
- [ ] 性能測試通過
- [ ] 集成測試通過

### 階段3-5檢查清單
- [ ] 所有工具類重構完成
- [ ] GUI業務邏輯分離
- [ ] 統一配置系統
- [ ] 性能優化完成
- [ ] 文檔完整更新

---

## ⚠️ 風險控制

### 回滾策略
1. **保留原始代碼**：所有原始文件保持不變
2. **分支管理**：每個階段使用獨立分支
3. **漸進式部署**：先在測試環境驗證

### 驗證標準
1. **功能測試**：所有現有功能正常
2. **性能測試**：性能不降低
3. **兼容性測試**：現有API保持兼容
4. **壓力測試**：大數據集處理正常

### 應急預案
如果重構遇到問題：
1. **立即停止**：停止當前階段重構
2. **問題分析**：分析失敗原因
3. **回滾代碼**：回到上一個穩定版本
4. **重新評估**：調整重構策略

---

## 📚 參考資料

### 設計模式
- 策略模式（Strategy Pattern）
- 適配器模式（Adapter Pattern）
- 工廠模式（Factory Pattern）
- 觀察者模式（Observer Pattern）

### 重構原則
- SOLID原則
- DRY原則（Don't Repeat Yourself）
- KISS原則（Keep It Simple, Stupid）
- YAGNI原則（You Aren't Gonna Need It）

### 測試策略
- 單元測試（Unit Testing）
- 集成測試（Integration Testing）
- 端到端測試（E2E Testing）
- 回歸測試（Regression Testing）

---

## 📞 支持與協助

如有任何問題，請參考：
1. **代碼示例**：參考shared目錄中的基礎實現
2. **測試用例**：參考tests目錄中的測試示例
3. **文檔說明**：參考各階段的詳細說明

重構是一個持續的過程，建議定期回顧和調整策略，確保重構目標的達成。