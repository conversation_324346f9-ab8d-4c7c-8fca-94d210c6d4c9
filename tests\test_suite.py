"""
集中式測試套件

整合所有模組的測試，支援test_image作為測試資料源
"""

import unittest
import sys
from pathlib import Path

# 添加專案路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.base_test import BaseTestCase, ModelTestMixin, DistributedTestMixin, DataProcessingTestMixin


class DataProcessingTest(BaseTestCase, DataProcessingTestMixin):
    """資料前處理測試"""
    
    def test_annotation_converter(self):
        """測試標註格式轉換"""
        try:
            from 資料前處理.annotation_converter_compat import AnnotationConverter
            
            converter = AnnotationConverter()
            self.assertIsNotNone(converter)
            
            # 使用test_image進行轉換測試
            image, annotation, key = self.get_image_annotation_pair()
            self.assert_image_valid(image)
            self.assert_annotation_valid(annotation)
            
        except ImportError as e:
            self.skipTest(f"無法導入標註轉換器: {e}")
    
    def test_image_augmentation(self):
        """測試圖像增強"""
        try:
            image, key = self.get_sample_image()
            self.assert_image_valid(image)
            
            # 測試基本增強操作
            from 資料前處理.img_augmenter import ImageAugmenter
            augmenter = ImageAugmenter()
            
            # 測試水平翻轉
            flipped = augmenter.horizontal_flip(image)
            self.assert_image_valid(flipped)
            self.assertEqual(image.shape, flipped.shape)
            
        except ImportError as e:
            self.skipTest(f"無法導入圖像增強器: {e}")
    
    def test_panorama_processing(self):
        """測試全景圖像處理"""
        try:
            from 資料前處理.panorama_augmenter import PanoramaAugmenter
            
            augmenter = PanoramaAugmenter()
            image, key = self.get_sample_image()
            
            # 測試全景圖像處理
            result = augmenter.process_image(image)
            self.assertIsNotNone(result)
            
        except ImportError as e:
            self.skipTest(f"無法導入全景處理器: {e}")


class ModelArchitectureTest(BaseTestCase, ModelTestMixin):
    """AI模型架構測試"""
    
    def test_csp_iformer_encoder(self):
        """測試CSP-IFormer編碼器"""
        try:
            from AI模型建構訓練驗證.model_create.encoder.VIT.CSP_IFormer_final_SegMode import CSPIFormerEncoder
            
            encoder = CSPIFormerEncoder()
            image, key = self.get_sample_image()
            
            # 準備輸入張量
            import torch
            input_tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0).float()
            
            # 測試前向傳播
            with torch.no_grad():
                output = encoder(input_tensor)
                self.assertIsNotNone(output)
            
        except ImportError as e:
            self.skipTest(f"無法導入CSP-IFormer: {e}")
    
    def test_mobilenet_encoder(self):
        """測試MobileNet編碼器"""
        try:
            from AI模型建構訓練驗證.model_create.encoder.CNN.CSP_Mobilenet import CSPMobileNet
            
            encoder = CSPMobileNet()
            image, key = self.get_sample_image()
            
            import torch
            input_tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0).float()
            
            with torch.no_grad():
                output = encoder(input_tensor)
                self.assertIsNotNone(output)
            
        except ImportError as e:
            self.skipTest(f"無法導入MobileNet: {e}")
    
    def test_unified_trainer(self):
        """測試統一訓練器"""
        try:
            from AI模型建構訓練驗證.model_create.training import UnifiedTrainer, TrainingConfig
            
            config = TrainingConfig()
            self.assertIsNotNone(config)
            
        except ImportError as e:
            self.skipTest(f"無法導入統一訓練器: {e}")


class DistributedFrameworkTest(BaseTestCase, DistributedTestMixin):
    """分散式框架測試"""
    
    def setUp(self):
        super().setUp()
        self.setUp_distributed()
    
    def tearDown(self):
        super().tearDown()
        self.tearDown_distributed()
    
    def test_ray_basic_functionality(self):
        """測試Ray基礎功能"""
        self.skip_if_no_ray()
        
        import ray
        
        @ray.remote
        def test_task(x):
            return x * 2
        
        # 測試遠程任務
        future = test_task.remote(5)
        result = ray.get(future)
        self.assertEqual(result, 10)
    
    def test_ray_data_processing(self):
        """測試Ray資料處理"""
        self.skip_if_no_ray()
        
        try:
            import ray
            
            # 創建測試資料集
            images = list(self.test_images.values())[:3]  # 取前3張圖像
            
            # 使用Ray處理資料
            ds = ray.data.from_items([{"image": img} for img in images])
            processed = ds.map(lambda x: {"processed": True, "shape": x["image"].shape})
            
            results = processed.take()
            self.assertGreater(len(results), 0)
            
        except ImportError as e:
            self.skipTest(f"Ray Data不可用: {e}")
    
    def test_ray_model_serving(self):
        """測試Ray模型服務（模擬）"""
        self.skip_if_no_ray()
        
        try:
            # 模擬Ray模型服務功能
            image, key = self.get_sample_image()
            
            # 模擬模型服務部署
            service_config = {
                "model_path": "./test_model.pth",
                "num_replicas": 2,
                "batch_size": 4
            }
            
            # 驗證配置有效性
            self.assertIn("model_path", service_config)
            self.assertGreater(service_config["num_replicas"], 0)
            
        except Exception as e:
            self.skipTest(f"Ray模型服務測試失敗: {e}")


class IntegrationTest(BaseTestCase):
    """整合測試"""
    
    def test_end_to_end_pipeline(self):
        """端到端管線測試"""
        # 1. 載入測試資料
        image, annotation, key = self.get_image_annotation_pair()
        self.assert_image_valid(image)
        self.assert_annotation_valid(annotation)
        
        # 2. 測試資料前處理
        try:
            from 資料前處理.shared import BaseTool
            self.assertIsNotNone(BaseTool)
        except ImportError:
            self.skipTest("資料前處理模組不可用")
        
        # 3. 測試模型創建
        try:
            from AI模型建構訓練驗證.model_create.core import ModelFactory
            self.assertIsNotNone(ModelFactory)
        except ImportError:
            self.skipTest("模型創建模組不可用")
    
    def test_config_management(self):
        """測試配置管理"""
        try:
            from AI模型建構訓練驗證.model_create.core import ConfigManager
            from 資料前處理.shared import ConfigManager as DataConfigManager
            
            # 測試兩個ConfigManager的兼容性
            self.assertIsNotNone(ConfigManager)
            self.assertIsNotNone(DataConfigManager)
            
        except ImportError as e:
            self.skipTest(f"配置管理模組不可用: {e}")


def create_test_suite():
    """創建完整測試套件"""
    suite = unittest.TestSuite()
    
    # 添加所有測試類
    test_classes = [
        DataProcessingTest,
        ModelArchitectureTest,
        DistributedFrameworkTest,
        IntegrationTest
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    return suite


def run_all_tests():
    """運行所有測試"""
    print("🧪 開始運行基礎測試套件...")
    suite = create_test_suite()
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print(f"\n📊 基礎測試結果: {result.testsRun} 個測試運行")
    print(f"✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 失敗: {len(result.failures)}")
    print(f"⚠️ 錯誤: {len(result.errors)}")
    print(f"⏭️ 跳過: {len(result.skipped)}")
    
    return result.wasSuccessful()


def run_comprehensive_tests():
    """運行全面測試（包括基礎測試和全面測試）"""
    print("🚀 運行完整測試套件...")
    print("=" * 60)
    
    # 先運行基礎測試
    print("📋 階段1: 基礎測試")
    basic_success = run_all_tests()
    
    print("\n" + "=" * 60)
    print("📋 階段2: 全面測試")
    
    # 運行全面測試
    try:
        from test_comprehensive import run_comprehensive_tests as run_comp_tests
        comp_success = run_comp_tests()
    except ImportError as e:
        print(f"無法載入全面測試: {e}")
        comp_success = True  # 如果無法載入，不影響基礎測試結果
    
    print("\n" + "=" * 60)
    print("🏁 最終結果:")
    print(f"基礎測試: {'✅ 通過' if basic_success else '❌ 失敗'}")
    print(f"全面測試: {'✅ 通過' if comp_success else '❌ 失敗'}")
    
    overall_success = basic_success and comp_success
    print(f"整體結果: {'✅ 所有測試通過' if overall_success else '❌ 有測試失敗'}")
    
    return overall_success


if __name__ == "__main__":
    # 檢查是否要運行全面測試
    if len(sys.argv) > 1 and sys.argv[1] == "--comprehensive":
        success = run_comprehensive_tests()
    else:
        success = run_all_tests()
        print("\n💡 提示: 使用 'python test_suite.py --comprehensive' 運行全面測試")
    
    sys.exit(0 if success else 1)