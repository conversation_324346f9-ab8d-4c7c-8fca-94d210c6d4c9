import os
import shutil

def copy_images_to_json_folder(json_folder, image_folder):
    # 獲取 JSON 資料夾中的所有 JSON 文件
    json_files = [f for f in os.listdir(json_folder) if f.endswith('.json')]
    
    # 獲取圖像資料夾中的所有圖像文件
    image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif')
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(image_extensions)]
    
    # 建立圖像檔名到完整路徑的映射（不區分大小寫）
    image_map = {os.path.splitext(f)[0].lower(): f for f in image_files}
    
    copied_count = 0
    
    for json_file in json_files:
        # 獲取 JSON 文件的 basename (不含副檔名)
        base_name = os.path.splitext(json_file)[0].lower()
        
        # 檢查是否有對應的圖像文件
        if base_name in image_map:
            image_file = image_map[base_name]
            src_path = os.path.join(image_folder, image_file)
            dst_path = os.path.join(json_folder, image_file)
            
            # 複製文件
            shutil.copy2(src_path, dst_path)
            copied_count += 1
            print(f"已複製: {image_file} -> {dst_path}")
        else:
            print(f"警告: 找不到 {json_file} 的對應圖像文件")
    
    print(f"\n完成! 共複製了 {copied_count} 個圖像文件。")

def main():
    # 直接在這裡輸入兩個資料夾的路徑
    json_folder = r'E:\新破損-圖像\112-0711_model_out\labelme_json'
    image_folder = r'F:\破損\112\0711台61主線內車道(36371)1512後開始失焦'
    
    # 檢查路徑是否存在
    if not os.path.isdir(json_folder):
        print(f"錯誤: {json_folder} 不是有效的資料夾路徑")
        return
    
    if not os.path.isdir(image_folder):
        print(f"錯誤: {image_folder} 不是有效的資料夾路徑")
        return
    
    copy_images_to_json_folder(json_folder, image_folder)

if __name__ == "__main__":
    main()