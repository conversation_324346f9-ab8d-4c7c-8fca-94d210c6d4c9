import os 
import re
import cv2
import json
import numpy as np
from PIL import Image, ImageDraw, ImageFont
# SOAP API
from requests import Session
from zeep import Client
from zeep.transports import Transport
class CheckImage(object):
    def __init__(self, img):
        try:
            with open(img, "rb") as f:
                f.seek(-2, 2)
                self.img_text = f.read()
                f.close()
        except Exception as e:
            self.img_text = None
            print(img + ' Exception when open')
    def check_jpg_jpeg(self):
        if self.img_text is None:
            return False
        else:
            """檢測JPG圖片完整性，完整Return True，不完整Return False"""
            buf = self.img_text
            return buf.endswith(b'\xff\xd9')

def ReadConfig(config_path): #parameter .cfg
    fdict = {}
    with open(config_path,encoding="utf-8") as f:
        key_value = [i.strip().split(',') for i in f.readlines()]
    for i in key_value:
        key_ = i[0]
        value_ = i[1:]
        fdict.update({key_: value_})
    return(fdict)

def SOAP_API():
    session = Session()
    transport = Transport(session=session)
    client = Client('https://www.srgeo.com.tw/API_FMO_SOAP/InsCar_v1/service.php?wsdl',transport=transport)
    return client

def branches_parameters(mtxs_path,pixcel_translate):
    # pixcel_m：像素長寬對應實際(m)、mtx：正射矩陣、xy：gps距影像中間下面距離
    branches_parameters = dict()
    xy_data = dict()
    pixcel_data = dict()
    with open(pixcel_translate,encoding="utf-8") as f:
        key_value = [re.split(r':|,|\n+',i.strip()) for i in f.readlines()]
    for i in key_value:
        key_ = i[0]
        value_ = float(i[1])
        pixcel_data.update({key_: value_})
        xy_data.update({key_: (float(i[2]), float(i[3]))})

    branches_parameters["pixcel_m"] = pixcel_data
    branches_parameters["mtx"] = dict()
    branches_parameters["xy"] = xy_data
    # 正射矩陣
    for i in os.listdir(mtxs_path):
        key_value = i.strip().split('_')
        for file_name in key_value:
            if file_name in branches_parameters["pixcel_m"]:
                branches_parameters["mtx"][file_name]=np.load(os.path.join(mtxs_path,i))
    return branches_parameters

def image_time(image_name):
    image_name = image_name.replace(".jpg","")
    data = image_name.split("_")

    data1 = list(data[1])
    data1.insert(4,"-")
    data1.insert(7,"-")
    data1.insert(10," ")

    data2 = list(data[2])
    data2.insert(2,":")
    data2.insert(5,":")
    data2.insert(8,".")

    final = data1+data2
    time = "".join(final)
    return time
    
def random_colors(N):
    np.random.seed(1)  # seed() 中相同的數 之後random值會相同
    colors = [tuple([np.random.randint(0, 255) for _ in range(3)]) for x in range(N)]
    return colors

def change_cv2_draw(image, strs, x, y, sizes, colour):
    cv2img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    pilimg = Image.fromarray(cv2img)
    draw = ImageDraw.Draw(pilimg)  # 圖片上打印
    font = ImageFont.truetype(r"C:\\Windows\\Fonts\\kaiu.ttf", sizes, encoding="utf-8")

    draw.text((x, y-100), strs,
                (colour[2], colour[1], colour[0]), font=font)
    image = cv2.cvtColor(np.array(pilimg), cv2.COLOR_RGB2BGR)
    return image

def save_json(image_name,crack_detail,save_folder):
    data = dict()
    data['image_name'] = image_name
    data['crack_detail'] = crack_detail
    with open(os.path.join(save_folder, image_name.replace(".jpg", "_detection.json")), 'w',encoding='utf-8') as f:
        json.dump(data, f)
