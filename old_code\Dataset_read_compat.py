"""
Dataset_read.py 向後兼容包裝文件
提供與原始 Dataset_read.py 相同的導入接口
建議使用新的 dataset.py 模組
"""

import warnings

# 發出棄用警告
warnings.warn(
    "Dataset_read.py 已被棄用，請使用新的 dataset.py 模組。"
    "請參考 model_create/util/遷移指南.md 進行更新。",
    DeprecationWarning,
    stacklevel=2
)

# 從新模組導入所有功能
from .dataset import (
    YOLODataset,
    LabelmeDataset,
    convert_labelme_to_yolo,
    denormalize,
    DatasetConfig,
    ImageProcessor,
    FontManager,
    FileFinder,
    BaseVisionDataset,
)

# 保持與原始模組相同的導出
__all__ = [
    'YOLODataset',
    'LabelmeDataset', 
    'convert_labelme_to_yolo',
    'denormalize',
]