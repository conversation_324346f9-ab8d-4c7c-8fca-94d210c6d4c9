import os
import numpy as np
import matplotlib.pyplot as plt
import torch
import matplotlib.cm as cm
import cv2
from PIL import Image, ImageDraw, ImageFont

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

# 確保輸出目錄存在
def ensure_dir(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)


def denormalize(img):
    """反歸一化圖像以進行視覺化
    
    Args:
        img: 歸一化後的圖像張量或數組
        
    Returns:
        反歸一化後的圖像 (0-255 範圍)
    """
    if isinstance(img, torch.Tensor):
        img = img.clone().detach().cpu().numpy()
        
    # 如果是 CHW 格式，轉換為 HWC
    if img.ndim == 3 and img.shape[0] == 3:
        img = np.transpose(img, (1, 2, 0))
    
    # 反歸一化 (使用與 A.Normalize() 相同但反向的參數)
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    
    img = img * std + mean  # 反歸一化
    img = np.clip(img, 0, 1)  # 確保值在 [0, 1] 範圍內
    img = (img * 255).astype(np.uint8)  # 轉換到 [0, 255] 範圍
    
    return img


# 顯示訓練中的圖像、標籤和預測結果
def show_img(epoch, image, label, pred, path_img, phase, 
            save=True, figsize=(15, 5), return_fig=False, filename=None, class_names=None):
    """
    顯示和儲存訓練/驗證/測試中的圖像、標籤和預測結果，簡化為三張圖：
    原始圖片、真實標籤疊加和預測結果疊加

    參數:
        epoch (int): 當前訓練的輪次
        image (torch.Tensor): 圖像張量
        label (torch.Tensor): 標籤張量
        pred (torch.Tensor): 預測張量
        path_img (str): 圖像保存路徑
        phase (str): 訓練階段 ('train', 'valid', 'test')
        save (bool): 是否保存圖像
        figsize (tuple): 圖形大小
        return_fig (bool): 是否返回圖形對象
        filename (str): 原始圖像的檔名（如果有）
        class_names (dict): 類別名稱字典，例如 {0: '背景', 1: '裂縫', 2: '坑洞'}
    """
    import os
    import numpy as np
    import matplotlib.pyplot as plt
    import torch
    import matplotlib.cm as cm
    import cv2
    from PIL import Image, ImageDraw, ImageFont
    
    # 確保目錄存在
    if not os.path.exists(path_img):
        os.makedirs(path_img)

    # 創建圖形 (簡化為3個子圖)
    fig, axes = plt.subplots(1, 3, figsize=figsize)

    # 處理圖像
    if isinstance(image, torch.Tensor):
        # 將張量轉換為 NumPy 陣列
        if image.dim() == 4:  # 如果有批次維度
            image = image.squeeze(0)

        if image.dim() == 3 and image.shape[0] == 3:  # 如果是 CHW 格式
            image = image.permute(1, 2, 0).cpu().numpy()
        else:
            image = image.cpu().numpy()

    # 處理標籤和預測
    if isinstance(label, torch.Tensor):
        label = label.cpu().numpy()

    if isinstance(pred, torch.Tensor):
        pred = pred.cpu().numpy()

    # 反歸一化
    def denormalize(img):
        """反歸一化圖像以進行視覺化"""
        if isinstance(img, torch.Tensor):
            img = img.clone().detach().cpu().numpy()
            
        # 如果是 CHW 格式，轉換為 HWC
        if img.ndim == 3 and img.shape[0] == 3:
            img = np.transpose(img, (1, 2, 0))
        
        # 反歸一化 (使用與 A.Normalize() 相同但反向的參數)
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        
        img = img * std + mean  # 反歸一化
        img = np.clip(img, 0, 1)  # 確保值在 [0, 1] 範圍內
        img = (img * 255).astype(np.uint8)  # 轉換到 [0, 255] 範圍
        
        return img

    # 確保值在有效範圍內
    if np.max(image) <= 1.0:
        image = (image * 255).astype(np.uint8)

    # 顯示原圖
    image_denorm = denormalize(image)
    axes[0].imshow(image_denorm)
    title = '原始圖片'
    if filename:
        title += f': {filename}'
    axes[0].set_title(title)
    axes[0].axis('off')

    # 產生顏色映射，確保每個類別顏色固定
    np.random.seed(42)  # 保持顏色一致
    num_classes = max(np.max(label) if np.max(label) > 0 else 0, 
                     np.max(pred) if np.max(pred) > 0 else 0) + 1
    class_colors = {}
    
    # 找出所有存在的類別
    unique_classes_true = set(np.unique(label).astype(int))
    unique_classes_pred = set(np.unique(pred).astype(int))
    # 合併真實標籤和預測中的所有類別
    all_classes = sorted(unique_classes_true.union(unique_classes_pred))
    all_classes = [c for c in all_classes if c > 0]  # 排除背景類別
    
    # 為每個類別分配顏色
    for cls in all_classes:
        class_colors[cls] = np.random.randint(0, 255, 3).tolist()

    # 函數：使用PIL繪製中文文字
    def draw_chinese_text(img, text, position, font_size=20, text_color=(0, 0, 0)):
        # 將OpenCV圖像轉換為PIL圖像
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 嘗試載入系統字體
        try:
            # 嘗試多種字體，以確保至少有一個可用
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',         # macOS
                '/usr/share/fonts/truetype/arphic/uming.ttc', # Ubuntu
                'C:/Windows/Fonts/msyh.ttc',                  # Windows
                'C:/Windows/Fonts/mingliu.ttc',               # Windows
                'C:/Windows/Fonts/simsun.ttc',                # Windows
                'C:/Windows/Fonts/kaiu.ttf',                  # Windows
                '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
            ]
            
            font = None
            for path in font_paths:
                try:
                    font = ImageFont.truetype(path, font_size)
                    break
                except IOError:
                    continue
            
            # 如果沒有找到支援中文的字體，使用默認字體
            if font is None:
                font = ImageFont.load_default()
                
        except Exception as e:
            # 如果載入字體失敗，使用默認字體
            print(f"載入字體失敗: {e}")
            font = ImageFont.load_default()
        
        # 繪製文字
        draw.text(position, text, font=font, fill=text_color)
        
        # 將PIL圖像轉回OpenCV格式
        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)

    # 創建疊加圖像 - 真實標籤疊加在原圖上
    overlap_true = image_denorm.copy()
    colored_mask_true = np.zeros_like(overlap_true)
    
    # 為每個類別著色
    for cls in all_classes:
        if cls in unique_classes_true:
            # 為真實標籤中存在的類別著色
            colored_mask_true[label == cls] = class_colors[cls]
    
    # 使用 alpha 混合
    alpha = 0.5
    true_mask = label > 0  # 非背景像素
    overlap_true = cv2.addWeighted(
        overlap_true, 1.0, 
        colored_mask_true, alpha, 
        0
    )
    
    # 添加圖例
    if all_classes:
        # 繪製半透明的圖例背景
        legend_x = overlap_true.shape[1] - 150
        legend_y = 20
        legend_height = 25 * len(all_classes)
        
        # 半透明白色背景
        cv2.rectangle(
            overlap_true,
            (legend_x - 10, legend_y - 10),
            (legend_x + 140, legend_y + legend_height),
            (255, 255, 255),
            -1
        )
        # 黑色邊框
        cv2.rectangle(
            overlap_true,
            (legend_x - 10, legend_y - 10),
            (legend_x + 140, legend_y + legend_height),
            (0, 0, 0),
            1
        )
        
        # 繪製每個類別的顏色方塊和名稱
        for i, cls in enumerate(sorted(all_classes)):
            # 顏色方塊
            cv2.rectangle(
                overlap_true,
                (legend_x, legend_y + i * 25),
                (legend_x + 20, legend_y + i * 25 + 20),
                class_colors[cls],
                -1
            )
            
            # 類別名稱/編號
            if class_names and cls in class_names:
                class_text = f"{class_names[cls]}"
            else:
                class_text = f"類別 {cls}"
            
            # 使用PIL繪製中文
            overlap_true = draw_chinese_text(
                overlap_true, 
                class_text, 
                (legend_x + 30, legend_y + i * 25), 
                font_size=16
            )
    
    axes[1].imshow(overlap_true)
    axes[1].set_title('真實標籤疊加')
    axes[1].axis('off')

    # 創建疊加圖像 - 預測結果疊加在原圖上
    overlap_pred = image_denorm.copy()
    colored_mask_pred = np.zeros_like(overlap_pred)
    
    # 為每個類別著色
    for cls in all_classes:
        if cls in unique_classes_pred:
            # 為預測結果中存在的類別著色
            colored_mask_pred[pred == cls] = class_colors[cls]
    
    # 使用 alpha 混合
    pred_mask = pred > 0  # 非背景像素
    overlap_pred = cv2.addWeighted(
        overlap_pred, 1.0, 
        colored_mask_pred, alpha, 
        0
    )
    
    # 添加圖例 (與真實標籤相同的圖例)
    if all_classes:
        # 繪製半透明的圖例背景
        legend_x = overlap_pred.shape[1] - 150
        legend_y = 20
        legend_height = 25 * len(all_classes)
        
        # 半透明白色背景
        cv2.rectangle(
            overlap_pred,
            (legend_x - 10, legend_y - 10),
            (legend_x + 140, legend_y + legend_height),
            (255, 255, 255),
            -1
        )
        # 黑色邊框
        cv2.rectangle(
            overlap_pred,
            (legend_x - 10, legend_y - 10),
            (legend_x + 140, legend_y + legend_height),
            (0, 0, 0),
            1
        )
        
        # 繪製每個類別的顏色方塊和名稱
        for i, cls in enumerate(sorted(all_classes)):
            # 顏色方塊
            cv2.rectangle(
                overlap_pred,
                (legend_x, legend_y + i * 25),
                (legend_x + 20, legend_y + i * 25 + 20),
                class_colors[cls],
                -1
            )
            
            # 類別名稱/編號
            if class_names and cls in class_names:
                class_text = f"{class_names[cls]}"
            else:
                class_text = f"類別 {cls}"
            
            # 使用PIL繪製中文
            overlap_pred = draw_chinese_text(
                overlap_pred, 
                class_text, 
                (legend_x + 30, legend_y + i * 25), 
                font_size=16
            )
    
    axes[2].imshow(overlap_pred)
    axes[2].set_title('預測結果疊加')
    axes[2].axis('off')

    plt.tight_layout()
    
    # 決定保存的檔名
    if filename and save:
        base_filename = os.path.splitext(filename)[0]
        save_path = os.path.join(path_img, f'{phase}_{base_filename}.png')
    elif phase == 'train' or phase == 'valid' and epoch % 10 == 0 and save:
        save_path = os.path.join(path_img, f'{phase}_epoch_{epoch:04d}.png')
    elif phase == 'test' and save:
        save_path = os.path.join(path_img, f'{phase}_epoch_{epoch:04d}.png')
    else:
        save_path = None
    
    # 保存圖像
    if save_path:
        plt.savefig(save_path, bbox_inches='tight')

    plt.show()
    if return_fig:
        return fig
    else:
        plt.close(fig)
        return None