#!/usr/bin/env python3
"""
CSP_IFormer使用示例
展示如何使用原創的CSP_IFormer架構
包含Channel Shuffle、DropKey等創新技術
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, CSP_IFORMER_AVAILABLE
setup_project_paths()

def main():
    """CSP_IFormer使用示例主函數"""
    
    print("🏭 CSP_IFormer使用示例")
    print("=" * 50)
    print("原創架構：CSP + IFormer + Channel Shuffle + DropKey")
    print()
    
    # ===================================================================
    # 📋 參數設定區域 - 請根據您的需求修改以下參數
    # ===================================================================
    
    # 🎯 模型配置
    model_variant = "final_segmentation"           # 模型變體: "final_segmentation", "final_classification"
    num_classes = 5                                # 類別數量
    img_size = 512                                 # 輸入圖像大小 (分割建議512+)
    
    # 🏗️ 架構配置
    enable_channel_shuffle = True                  # 啟用Channel Shuffle機制
    enable_dropkey = True                          # 啟用DropKey正則化
    dropout_rate = 0.3                             # Dropout比例
    use_csp_connection = True                      # 使用CSP連接
    
    # 🎛️ IFormer配置
    iformer_layers = 4                             # IFormer層數
    embed_dim = 256                                # 嵌入維度
    num_heads = 8                                  # 注意力頭數
    mlp_ratio = 4.0                                # MLP擴展比例
    
    # 🔧 訓練配置
    batch_size = 4                                 # 批次大小 (分割任務記憶體需求較大)
    learning_rate = 1e-4                           # 學習率
    weight_decay = 1e-5                            # 權重衰減
    
    # 📁 路徑配置
    input_data_path = "./test_image"               # 輸入數據路徑
    output_model_path = "./output/csp_iformer_model.pth"  # 模型保存路徑
    output_results_path = "./output/csp_iformer_results"  # 結果保存路徑
    config_save_path = "./output/csp_iformer_config.yaml"  # 配置保存路徑
    
    # 🎨 可視化配置
    visualize_architecture = True                  # 可視化架構
    save_attention_maps = False                    # 保存注意力圖
    generate_feature_maps = False                  # 生成特徵圖
    
    # ===================================================================
    # 🔍 執行檢查和驗證
    # ===================================================================
    
    if not CSP_IFORMER_AVAILABLE:
        print("❌ CSP_IFormer模組不可用")
        print("請檢查encoder/VIT/目錄和CSP_IFormer相關文件")
        return
    
    print("✅ CSP_IFormer模組可用")
    
    # 檢查輸入路徑
    if not Path(input_data_path).exists():
        print(f"⚠️  警告: 輸入路徑不存在: {input_data_path}")
        print("將創建虛擬數據進行架構演示")
    
    # 創建輸出目錄
    Path(output_results_path).mkdir(parents=True, exist_ok=True)
    Path(output_model_path).parent.mkdir(parents=True, exist_ok=True)
    
    # ===================================================================
    # 🚀 執行CSP_IFormer模型創建和使用
    # ===================================================================
    
    try:
        print("🔧 導入CSP_IFormer模組...")
        
        # 導入CSP_IFormer相關模組
        if model_variant == "final_segmentation":
            from AI模型建構訓練驗證.model_create.encoder.VIT.CSP_IFormer_final_SegMode import (
                CSP_IFormer_SegMode
            )
            model_class = CSP_IFormer_SegMode
            task_type = "segmentation"
            
        elif model_variant == "final_classification":
            from AI模型建構訓練驗證.model_create.encoder.VIT.CSP_IFormer_final_ClsMode import (
                CSP_IFormer_ClsMode
            )
            model_class = CSP_IFormer_ClsMode
            task_type = "classification"
        else:
            raise ValueError(f"不支援的模型變體: {model_variant}")
        
        print("✅ CSP_IFormer模組導入成功")
        
        # ===============================================================
        # 1. 🏗️ 模型創建
        # ===============================================================
        
        print("\\n🏗️ 步驟1: CSP_IFormer模型創建")
        print("-" * 40)
        
        # 創建模型配置
        model_config = {
            'num_classes': num_classes,
            'img_size': img_size,
            'enable_channel_shuffle': enable_channel_shuffle,
            'enable_dropkey': enable_dropkey,
            'dropout_rate': dropout_rate,
            'use_csp_connection': use_csp_connection,
            'iformer_layers': iformer_layers,
            'embed_dim': embed_dim,
            'num_heads': num_heads,
            'mlp_ratio': mlp_ratio
        }
        
        print(f"🎯 模型變體: {model_variant}")
        print(f"📐 輸入大小: {img_size}x{img_size}")
        print(f"🎯 類別數: {num_classes}")
        print(f"🔄 Channel Shuffle: {'啟用' if enable_channel_shuffle else '禁用'}")
        print(f"🎲 DropKey: {'啟用' if enable_dropkey else '禁用'}")
        print(f"🔗 CSP連接: {'啟用' if use_csp_connection else '禁用'}")
        
        # 創建模型實例
        try:
            model = model_class(**model_config)
            print("✅ CSP_IFormer模型創建成功")
        except Exception as e:
            print(f"⚠️  模型創建失敗，使用簡化配置: {e}")
            # 使用簡化配置重試
            simplified_config = {
                'num_classes': num_classes
            }
            model = model_class(**simplified_config)
            print("✅ CSP_IFormer模型 (簡化配置) 創建成功")
        
        # ===============================================================
        # 2. 📊 模型分析
        # ===============================================================
        
        print("\\n📊 步驟2: 模型架構分析")
        print("-" * 40)
        
        try:
            import torch
            TORCH_AVAILABLE = True
        except ImportError:
            TORCH_AVAILABLE = False
            print("⚠️  PyTorch未安裝，將跳過模型分析")
        
        if TORCH_AVAILABLE:
            try:
                # 設定模型為評估模式
                model.eval()
                
                # 計算模型參數
                total_params = sum(p.numel() for p in model.parameters())
                trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
                
                print("🔍 架構分析:")
                print(f"   🏗️  總參數: {total_params:,}")
                print(f"   🎓 可訓練參數: {trainable_params:,}")
                print(f"   📏 模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
                
                # 創建虛擬輸入進行前向推理測試
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                model = model.to(device)
                
                print(f"🎮 使用設備: {device}")
                
                # 前向推理測試
                dummy_input = torch.randn(1, 3, img_size, img_size).to(device)
                
                with torch.no_grad():
                    output = model(dummy_input)
                    
                if task_type == "segmentation":
                    print(f"✅ 分割前向推理成功! 輸出形狀: {output.shape}")
                    print(f"   🎯 預期形狀: (1, {num_classes}, {img_size}, {img_size})")
                elif task_type == "classification":
                    print(f"✅ 分類前向推理成功! 輸出形狀: {output.shape}")
                    print(f"   🎯 預期形狀: (1, {num_classes})")
                
                # ============================================================
                # 3. 🎨 架構特色演示
                # ============================================================
                
                print("\\n🎨 步驟3: 架構特色演示")
                print("-" * 40)
                
                print("🔥 CSP_IFormer創新技術:")
                
                if enable_channel_shuffle:
                    print("   🔄 Channel Shuffle: 提升通道間信息交互")
                    print("      - 減少計算複雜度")
                    print("      - 增強特徵表達能力")
                
                if enable_dropkey:
                    print("   🎲 DropKey機制: 創新正則化技術")
                    print("      - 隨機丟棄注意力鍵值")
                    print("      - 防止過擬合")
                    print("      - 提升模型泛化能力")
                
                if use_csp_connection:
                    print("   🔗 CSP連接: Cross Stage Partial架構")
                    print("      - 減少參數數量")
                    print("      - 保持信息流動")
                    print("      - 提升訓練穩定性")
                
                print("   🏗️  IFormer塊: Inception風格Transformer")
                print("      - 多尺度特徵提取")
                print("      - 高效自注意力機制")
                print("      - 適合密集預測任務")
                
                # ============================================================
                # 4. 💾 模型保存和配置
                # ============================================================
                
                print("\\n💾 步驟4: 模型保存")
                print("-" * 40)
                
                # 保存模型權重
                model_save_dict = {
                    'model_state_dict': model.state_dict(),
                    'model_config': model_config,
                    'model_info': {
                        'variant': model_variant,
                        'task_type': task_type,
                        'total_params': total_params,
                        'trainable_params': trainable_params,
                        'input_size': img_size,
                        'num_classes': num_classes
                    },
                    'architecture_features': {
                        'channel_shuffle': enable_channel_shuffle,
                        'dropkey': enable_dropkey,
                        'csp_connection': use_csp_connection
                    }
                }
                
                torch.save(model_save_dict, output_model_path)
                print(f"✅ 模型權重保存至: {output_model_path}")
                
                # 保存配置文件
                import yaml
                config_dict = {
                    'model': model_config,
                    'architecture_features': {
                        'channel_shuffle': enable_channel_shuffle,
                        'dropkey': enable_dropkey,
                        'csp_connection': use_csp_connection
                    },
                    'training': {
                        'batch_size': batch_size,
                        'learning_rate': learning_rate,
                        'weight_decay': weight_decay
                    }
                }
                
                with open(config_save_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, indent=2, allow_unicode=True)
                print(f"✅ 配置文件保存至: {config_save_path}")
                
                # ============================================================
                # 5. 📈 性能基準測試
                # ============================================================
                
                print("\\n📈 步驟5: 性能基準測試")
                print("-" * 40)
                
                # 推理速度測試
                import time
                warmup_iterations = 10
                test_iterations = 100
                
                print("⏱️  推理速度測試...")
                
                # 預熱
                for _ in range(warmup_iterations):
                    with torch.no_grad():
                        _ = model(dummy_input)
                
                # 正式測試
                torch.cuda.synchronize() if device.type == 'cuda' else None
                start_time = time.time()
                
                for _ in range(test_iterations):
                    with torch.no_grad():
                        _ = model(dummy_input)
                
                torch.cuda.synchronize() if device.type == 'cuda' else None
                end_time = time.time()
                
                avg_time = (end_time - start_time) / test_iterations
                fps = 1.0 / avg_time
                
                print(f"✅ 性能測試結果:")
                print(f"   ⏱️  平均推理時間: {avg_time*1000:.2f} ms")
                print(f"   🚀 推理速度 (FPS): {fps:.1f}")
                print(f"   📊 輸入大小: {img_size}x{img_size}")
                print(f"   🎮 設備: {device}")
                
            except Exception as e:
                print(f"⚠️  模型分析過程中出現錯誤: {e}")
                print("模型創建成功，但分析測試失敗")
        
        # ===============================================================
        # 📚 使用建議和對比
        # ===============================================================
        
        print("\\n📚 使用建議:")
        print("1. 🎯 道路分割: 推薦final_segmentation變體，精度更高")
        print("2. 🏃 實時分類: 推薦final_classification變體，速度更快")
        print("3. 🧠 學術研究: CSP_IFormer具備論文發表價值")
        print("4. 🔬 創新實驗: 可基於此架構進行進一步創新")
        
        print("\\n🔗 與其他架構對比:")
        print("   🆚 Vision Mamba: CSP_IFormer O(n²) vs Vision Mamba O(n)")
        print("   🆚 傳統CNN: 更強的全局建模能力")
        print("   🆚 純Transformer: 更少的參數和計算量")
        print("   🤝 混合架構: 可與Vision Mamba組合使用")
        
        print("\\n📊 CSP_IFormer家族 (11個變體):")
        print("   ⭐⭐⭐⭐⭐ CSP_IFormer_final_SegMode: 分割模式最終版")
        print("   ⭐⭐⭐⭐⭐ CSP_IFormer_final_ClsMode: 分類模式最終版")
        print("   🔬 CSP_IFormer_v2024_mamba: Mamba整合版")
        print("   🧪 其他8個迭代版本: 研究和實驗用途")
        
        print(f"\\n🎉 CSP_IFormer演示完成! 結果保存至: {output_results_path}")
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保CSP_IFormer相關依賴已安裝:")
        print("   pip install torch torchvision")
        print("   pip install numpy")
        print("   pip install pyyaml")
        
    except Exception as e:
        print(f"❌ CSP_IFormer演示失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()