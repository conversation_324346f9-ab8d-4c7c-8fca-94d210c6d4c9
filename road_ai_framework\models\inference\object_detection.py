"""
基礎設施檢測器

基於 revised_yolo_detection_code.py 重構，提供專業化的基礎設施目標檢測功能。
支援多種基礎設施元素檢測：孔蓋、交通標誌、道路元件等。
"""

import os
import time
import logging
import pandas as pd
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from PIL import Image, ImageDraw, ImageFont
import torch
from dataclasses import dataclass
import argparse

# 設定日誌
logger = logging.getLogger(__name__)


@dataclass
class DetectionConfig:
    """檢測配置類"""
    confidence_threshold: float = 0.7
    iou_threshold: float = 0.5
    device: str = 'auto'  # 'auto', 'cpu', 'cuda'
    batch_size: int = 1
    save_format: str = 'jpg'  # 'jpg', 'png'
    font_size: int = 20
    box_thickness: int = 2


class ChineseFontManager:
    """中文字體管理器"""
    
    def __init__(self, font_size: int = 20):
        self.font_size = font_size
        self.font = self._load_chinese_font()
    
    def _load_chinese_font(self) -> ImageFont.ImageFont:
        """載入中文字體"""
        font_paths = [
            'C:/Windows/Fonts/msyh.ttc',  # 微軟雅黑
            'C:/Windows/Fonts/mingliub.ttc',  # 細明體
            'C:/Windows/Fonts/simsun.ttc',  # 宋體
            '/System/Library/Fonts/PingFang.ttc',  # macOS
            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',  # Linux
            '/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc',  # Linux Noto
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    return ImageFont.truetype(font_path, self.font_size)
                except Exception as e:
                    logger.warning(f"Failed to load font {font_path}: {e}")
                    continue
        
        logger.warning("No Chinese font found, using default font")
        return ImageFont.load_default()
    
    def set_font_size(self, size: int):
        """設定字體大小"""
        self.font_size = size
        self.font = self._load_chinese_font()


class InfrastructureClassifier:
    """基礎設施分類器"""
    
    # 預定義的類別映射
    DEFAULT_CLASS_MAPPINGS = {
        'manhole_cover': {
            'en': {0: 'bird', 1: 'block', 2: 'retaining_seat', 3: 'treefish', 4: 'tpe_cover'},
            'cn': {0: '鳥', 1: '方塊', 2: '擋土座', 3: '樹魚', 4: '台北蓋水特色框蓋'},
            'fallback': '孔蓋下地'
        },
        'road_damage': {
            'en': {0: 'expansion_joint', 1: 'joint', 2: 'linear_crack', 3: 'alligator_crack', 4: 'pothole', 5: 'patch'},
            'cn': {0: '伸縮縫', 1: '路面接縫', 2: '裂縫', 3: '龜裂', 4: '坑洞', 5: '補綻'},
            'fallback': '無損壞'
        },
        'traffic_signs': {
            'en': {0: 'stop_sign', 1: 'speed_limit', 2: 'warning_sign', 3: 'direction_sign'},
            'cn': {0: '停車標誌', 1: '速限標誌', 2: '警告標誌', 3: '指向標誌'},
            'fallback': '無標誌'
        }
    }
    
    def __init__(self, task_type: str = 'manhole_cover', custom_mappings: Optional[Dict] = None):
        self.task_type = task_type
        
        if custom_mappings:
            self.class_mappings = custom_mappings
        elif task_type in self.DEFAULT_CLASS_MAPPINGS:
            self.class_mappings = self.DEFAULT_CLASS_MAPPINGS[task_type]
        else:
            raise ValueError(f"Unsupported task type: {task_type}")
        
        # 建立雙向映射
        self.en_to_cn = self.class_mappings['cn']
        self.cn_to_en = {v: k for k, v in self.en_to_cn.items()}
        self.fallback_class = self.class_mappings['fallback']
    
    def get_class_name(self, class_id: int, language: str = 'cn') -> str:
        """獲取類別名稱"""
        if language == 'cn':
            return self.en_to_cn.get(class_id, f'類別{class_id}')
        else:
            return self.class_mappings['en'].get(class_id, f'class{class_id}')
    
    def get_all_classes(self, language: str = 'cn') -> Dict[int, str]:
        """獲取所有類別"""
        if language == 'cn':
            return self.en_to_cn
        else:
            return self.class_mappings['en']


class ImageProcessor:
    """圖像處理器"""
    
    def __init__(self, valid_extensions: List[str] = None):
        self.valid_extensions = valid_extensions or ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']
    
    def get_image_files(self, directory: str, recursive: bool = True) -> List[str]:
        """獲取目錄中的所有圖像文件"""
        image_files = []
        
        def collect_images(dir_path: str):
            try:
                for item in os.listdir(dir_path):
                    item_path = os.path.join(dir_path, item)
                    
                    if os.path.isdir(item_path) and recursive:
                        # 優先處理 images 子目錄
                        if item == 'images':
                            for img_file in os.listdir(item_path):
                                img_path = os.path.join(item_path, img_file)
                                if self._is_valid_image(img_path):
                                    image_files.append(img_path)
                        else:
                            collect_images(item_path)
                    elif os.path.isfile(item_path) and self._is_valid_image(item_path):
                        image_files.append(item_path)
            except Exception as e:
                logger.error(f"Error accessing directory {dir_path}: {e}")
        
        collect_images(directory)
        return sorted(image_files)
    
    def _is_valid_image(self, file_path: str) -> bool:
        """檢查是否為有效的圖像文件"""
        return os.path.splitext(file_path)[1].lower() in [ext.lower() for ext in self.valid_extensions]
    
    def validate_image(self, image_path: str) -> bool:
        """驗證圖像文件是否可讀取"""
        try:
            img = cv2.imread(image_path)
            return img is not None
        except Exception:
            return False


class DetectionVisualizer:
    """檢測結果可視化器"""
    
    def __init__(self, font_manager: ChineseFontManager, config: DetectionConfig):
        self.font_manager = font_manager
        self.config = config
        self.colors = [
            (255, 0, 0),    # 紅色
            (0, 255, 0),    # 綠色 
            (0, 0, 255),    # 藍色
            (255, 255, 0),  # 黃色
            (255, 0, 255),  # 品紅
            (0, 255, 255),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]
    
    def draw_detections(self, image: np.ndarray, boxes: List[Dict], 
                       classifier: InfrastructureClassifier) -> np.ndarray:
        """在圖像上繪製檢測框和標籤"""
        if not boxes:
            return image
        
        # 轉換為 PIL 圖像以支援中文
        img_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        for box in boxes:
            # 獲取檢測框資訊
            x1, y1, x2, y2 = box['bbox']
            class_id = box['class_id']
            confidence = box.get('confidence', 1.0)
            
            # 選擇顏色
            color = self.colors[class_id % len(self.colors)]
            
            # 繪製檢測框
            draw.rectangle([x1, y1, x2, y2], outline=color, width=self.config.box_thickness)
            
            # 準備標籤文字
            class_name = classifier.get_class_name(class_id, 'cn')
            label = f"{class_name} {confidence:.2f}"
            
            # 計算文字大小
            text_bbox = draw.textbbox((x1, y1), label, font=self.font_manager.font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # 繪製文字背景
            draw.rectangle([x1, y1 - text_height, x1 + text_width, y1], fill=color)
            
            # 繪製文字
            draw.text((x1, y1 - text_height), label, fill=(0, 0, 0), font=self.font_manager.font)
        
        # 轉換回 OpenCV 格式
        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    
    def draw_fallback_label(self, image: np.ndarray, 
                           classifier: InfrastructureClassifier) -> np.ndarray:
        """在圖像上繪製回退類別標籤"""
        img_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 計算文字位置（置中）
        img_width, img_height = img_pil.size
        text = classifier.fallback_class
        
        # 使用較大的字體
        large_font = ImageFont.truetype(self.font_manager.font.path, self.config.font_size * 2) \
                    if hasattr(self.font_manager.font, 'path') else self.font_manager.font
        
        text_bbox = draw.textbbox((0, 0), text, font=large_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        position = ((img_width - text_width) // 2, (img_height - text_height) // 2)
        
        # 繪製文字背景
        background_color = (255, 0, 0)  # 紅色背景
        padding = 10
        draw.rectangle([
            position[0] - padding, 
            position[1] - padding,
            position[0] + text_width + padding, 
            position[1] + text_height + padding
        ], fill=background_color)
        
        # 繪製文字
        draw.text(position, text, font=large_font, fill=(255, 255, 255))
        
        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)


class InfrastructureDetector:
    """基礎設施檢測器主類"""
    
    def __init__(self, 
                 model_path: str,
                 task_type: str = 'manhole_cover',
                 config: Optional[DetectionConfig] = None,
                 custom_class_mappings: Optional[Dict] = None):
        self.model_path = model_path
        self.task_type = task_type
        self.config = config or DetectionConfig()
        
        # 初始化組件
        self.classifier = InfrastructureClassifier(task_type, custom_class_mappings)
        self.font_manager = ChineseFontManager(self.config.font_size)
        self.image_processor = ImageProcessor()
        self.visualizer = DetectionVisualizer(self.font_manager, self.config)
        
        # 載入模型
        self.model = self._load_model()
        
        # 統計資訊
        self.stats = {
            'total_processed': 0,
            'total_detections': 0,
            'class_counts': {},
            'processing_time': 0
        }
    
    def _load_model(self):
        """載入 YOLO 模型"""
        try:
            from ultralytics import YOLO
            logger.info(f"Loading model from: {self.model_path}")
            model = YOLO(self.model_path)
            logger.info("Model loaded successfully")
            return model
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def predict_single_image(self, 
                           image_path: str,
                           save_result: bool = True,
                           output_dir: Optional[str] = None) -> Dict[str, Any]:
        """對單張圖像進行檢測"""
        if not self.image_processor.validate_image(image_path):
            raise ValueError(f"Invalid image file: {image_path}")
        
        # 載入圖像
        image = cv2.imread(image_path)
        filename = os.path.splitext(os.path.basename(image_path))[0]
        
        # 執行檢測
        start_time = time.time()
        results = self.model.predict(
            source=image_path,
            conf=self.config.confidence_threshold,
            iou=self.config.iou_threshold,
            device=self.config.device,
            verbose=False
        )
        inference_time = time.time() - start_time
        
        # 處理檢測結果
        result = results[0]
        detections = []
        detected_classes = []
        
        if hasattr(result, 'boxes') and result.boxes is not None and len(result.boxes) > 0:
            # 有檢測到物體
            for box in result.boxes:
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                class_id = int(box.cls.item())
                confidence = float(box.conf.item())
                
                detection = {
                    'bbox': [x1, y1, x2, y2],
                    'class_id': class_id,
                    'confidence': confidence,
                    'class_name': self.classifier.get_class_name(class_id, 'cn')
                }
                detections.append(detection)
                
                if detection['class_name'] not in detected_classes:
                    detected_classes.append(detection['class_name'])
            
            # 繪製檢測框
            result_image = self.visualizer.draw_detections(image, detections, self.classifier)
            output_filename = f"{filename}_{'_'.join(detected_classes)}_DETECT"
            
        else:
            # 沒有檢測到物體，使用回退類別
            result_image = self.visualizer.draw_fallback_label(image, self.classifier)
            output_filename = f"{filename}_{self.classifier.fallback_class}_DETECT"
            detected_classes = [self.classifier.fallback_class]
        
        # 保存結果
        if save_result and output_dir:
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(
                output_dir, 
                f"{output_filename}.{self.config.save_format}"
            )
            cv2.imwrite(output_path, result_image)
        
        # 更新統計
        self.stats['total_processed'] += 1
        self.stats['total_detections'] += len(detections)
        self.stats['processing_time'] += inference_time
        
        for class_name in detected_classes:
            self.stats['class_counts'][class_name] = self.stats['class_counts'].get(class_name, 0) + 1
        
        return {
            'filename': filename,
            'detections': detections,
            'detected_classes': detected_classes,
            'result_image': result_image,
            'inference_time': inference_time,
            'output_filename': output_filename
        }
    
    def process_batch(self, 
                     input_dirs: List[str],
                     output_dir: str,
                     generate_csv: bool = True) -> Dict[str, Any]:
        """批次處理多個目錄中的圖像"""
        logger.info(f"Starting batch processing for {len(input_dirs)} directories")
        
        # 收集所有圖像文件
        all_image_files = []
        for input_dir in input_dirs:
            if os.path.exists(input_dir):
                image_files = self.image_processor.get_image_files(input_dir)
                all_image_files.extend(image_files)
                logger.info(f"Found {len(image_files)} images in {input_dir}")
            else:
                logger.warning(f"Directory does not exist: {input_dir}")
        
        if not all_image_files:
            logger.error("No images found in any directory")
            return {}
        
        logger.info(f"Total images to process: {len(all_image_files)}")
        
        # 創建輸出目錄
        images_output_dir = os.path.join(output_dir, '圖像')
        os.makedirs(images_output_dir, exist_ok=True)
        
        # 處理圖像並收集結果
        results = []
        csv_data = []
        serial_number = 0
        
        start_time = time.time()
        
        for i, image_path in enumerate(all_image_files):
            try:
                # 顯示進度
                progress = (i + 1) / len(all_image_files) * 100
                logger.info(f"Processing: {progress:.1f}% ({i+1}/{len(all_image_files)})")
                
                # 處理單張圖像
                result = self.predict_single_image(
                    image_path=image_path,
                    save_result=True,
                    output_dir=images_output_dir
                )
                
                results.append(result)
                
                # 準備 CSV 數據
                if result['detections']:
                    # 有檢測結果
                    for detection in result['detections']:
                        csv_data.append({
                            '序號': serial_number,
                            '照片原始檔名': result['filename'],
                            '原始檔名_DETECT': result['output_filename'],
                            '類別': detection['class_name'],
                            '置信度': f"{detection['confidence']:.3f}"
                        })
                        serial_number += 1
                else:
                    # 無檢測結果，使用回退類別
                    csv_data.append({
                        '序號': serial_number,
                        '照片原始檔名': result['filename'],
                        '原始檔名_DETECT': result['output_filename'],
                        '類別': self.classifier.fallback_class,
                        '置信度': '1.000'
                    })
                    serial_number += 1
                
                logger.info(f"Processed: {os.path.basename(image_path)} -> {result['output_filename']}")
                
            except Exception as e:
                logger.error(f"Error processing {image_path}: {e}")
                continue
        
        total_time = time.time() - start_time
        
        # 保存 CSV 結果
        if generate_csv and csv_data:
            df = pd.DataFrame(csv_data)
            csv_path = os.path.join(output_dir, f'{self.task_type}_檢測結果.csv')
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logger.info(f"CSV saved to: {csv_path}")
        
        # 生成統計報告
        stats_report = self._generate_stats_report(total_time, len(all_image_files))
        
        logger.info("Batch processing completed!")
        logger.info(f"Images saved to: {images_output_dir}")
        
        return {
            'total_processed': len(results),
            'results': results,
            'csv_data': csv_data,
            'stats': stats_report,
            'output_dirs': {
                'images': images_output_dir,
                'csv': csv_path if generate_csv and csv_data else None
            }
        }
    
    def _generate_stats_report(self, total_time: float, total_files: int) -> Dict[str, Any]:
        """生成統計報告"""
        avg_time_per_image = total_time / total_files if total_files > 0 else 0
        fps = 1 / avg_time_per_image if avg_time_per_image > 0 else 0
        
        return {
            'total_processing_time': total_time,
            'total_files': total_files,
            'average_time_per_image': avg_time_per_image,
            'fps': fps,
            'total_detections': self.stats['total_detections'],
            'class_distribution': dict(self.stats['class_counts'])
        }
    
    def save_stats_report(self, output_dir: str, stats: Dict[str, Any]) -> str:
        """保存統計報告"""
        report_path = os.path.join(output_dir, f'{self.task_type}_statistics.txt')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write(f"{self.task_type.title()} 檢測統計報告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"模型路徑: {self.model_path}\n")
            f.write(f"置信度閾值: {self.config.confidence_threshold}\n")
            f.write(f"IoU閾值: {self.config.iou_threshold}\n\n")
            
            f.write("處理統計:\n")
            f.write(f"  總處理時間: {stats['total_processing_time']:.2f} 秒\n")
            f.write(f"  總檔案數: {stats['total_files']}\n")
            f.write(f"  平均處理時間: {stats['average_time_per_image']:.3f} 秒/張\n")
            f.write(f"  處理速度: {stats['fps']:.2f} FPS\n")
            f.write(f"  總檢測數: {stats['total_detections']}\n\n")
            
            f.write("類別分佈:\n")
            for class_name, count in stats['class_distribution'].items():
                f.write(f"  {class_name}: {count}\n")
        
        return report_path


# 工廠函數
def create_infrastructure_detector(model_path: str,
                                 task_type: str = 'manhole_cover',
                                 confidence_threshold: float = 0.7,
                                 custom_class_mappings: Optional[Dict] = None) -> InfrastructureDetector:
    """創建基礎設施檢測器"""
    config = DetectionConfig(confidence_threshold=confidence_threshold)
    return InfrastructureDetector(
        model_path=model_path,
        task_type=task_type,
        config=config,
        custom_class_mappings=custom_class_mappings
    )


def create_manhole_detector(model_path: str, confidence_threshold: float = 0.7) -> InfrastructureDetector:
    """創建孔蓋檢測器"""
    return create_infrastructure_detector(
        model_path=model_path,
        task_type='manhole_cover',
        confidence_threshold=confidence_threshold
    )


def create_road_damage_detector(model_path: str, confidence_threshold: float = 0.7) -> InfrastructureDetector:
    """創建道路損壞檢測器"""
    return create_infrastructure_detector(
        model_path=model_path,
        task_type='road_damage', 
        confidence_threshold=confidence_threshold
    )


def main():
    """命令行介面"""
    parser = argparse.ArgumentParser(description='基礎設施檢測器')
    parser.add_argument('--model', type=str, required=True, help='模型權重檔案路徑')
    parser.add_argument('--input_dirs', nargs='+', required=True, help='輸入目錄路徑，可指定多個')
    parser.add_argument('--output_dir', type=str, default='檢測結果', help='輸出目錄路徑')
    parser.add_argument('--task_type', type=str, default='manhole_cover', 
                       choices=['manhole_cover', 'road_damage', 'traffic_signs'],
                       help='檢測任務類型')
    parser.add_argument('--conf', type=float, default=0.7, help='置信度閾值')
    parser.add_argument('--iou', type=float, default=0.5, help='IoU閾值')
    parser.add_argument('--device', type=str, default='auto', help='計算設備')
    
    args = parser.parse_args()
    
    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("=" * 60)
    logger.info(f"開始 {args.task_type} 檢測")
    logger.info("=" * 60)
    logger.info(f"模型路徑: {args.model}")
    logger.info(f"輸入目錄: {args.input_dirs}")
    logger.info(f"輸出目錄: {args.output_dir}")
    logger.info(f"置信度閾值: {args.conf}")
    
    try:
        # 創建檢測器
        config = DetectionConfig(
            confidence_threshold=args.conf,
            iou_threshold=args.iou,
            device=args.device
        )
        
        detector = InfrastructureDetector(
            model_path=args.model,
            task_type=args.task_type,
            config=config
        )
        
        # 執行批次檢測
        start_time = time.time()
        results = detector.process_batch(
            input_dirs=args.input_dirs,
            output_dir=args.output_dir,
            generate_csv=True
        )
        
        # 保存統計報告
        if results.get('stats'):
            stats_path = detector.save_stats_report(args.output_dir, results['stats'])
            logger.info(f"統計報告已保存: {stats_path}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("檢測完成!")
        logger.info(f"總耗時: {total_time:.2f} 秒")
        logger.info(f"處理檔案數: {results.get('total_processed', 0)}")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"檢測過程發生錯誤: {e}")
        raise


if __name__ == "__main__":
    main()