"""
訓練回調系統

提供標準化的訓練回調功能，包括早停、檢查點保存、學習率調度等
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import logging
from pathlib import Path
import numpy as np


class BaseCallback(ABC):
    """回調基類"""
    
    def __init__(self):
        self.trainer = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def set_trainer(self, trainer):
        """設置訓練器引用"""
        self.trainer = trainer
    
    @abstractmethod
    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """訓練開始時調用"""
        pass
    
    @abstractmethod
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """訓練結束時調用"""
        pass
    
    @abstractmethod
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch開始時調用"""
        pass
    
    @abstractmethod
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch結束時調用"""
        pass
    
    def on_batch_begin(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """批次開始時調用（可選實現）"""
        pass
    
    def on_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """批次結束時調用（可選實現）"""
        pass


class EarlyStopping(BaseCallback):
    """早停回調"""
    
    def __init__(self, 
                 monitor: str = 'val_loss',
                 patience: int = 10,
                 min_delta: float = 1e-4,
                 mode: str = 'min',
                 restore_best_weights: bool = True,
                 verbose: bool = True):
        """
        初始化早停回調
        
        Args:
            monitor: 監控的指標名稱
            patience: 耐心值（停止前等待的epoch數）
            min_delta: 最小改善閾值
            mode: 'min' 或 'max'，指標的優化方向
            restore_best_weights: 是否在停止時恢復最佳權重
            verbose: 是否輸出詳細信息
        """
        super().__init__()
        self.monitor = monitor
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.restore_best_weights = restore_best_weights
        self.verbose = verbose
        
        # 內部狀態
        self.wait = 0
        self.best_weights = None
        self.stopped_epoch = 0
        
        if mode == 'min':
            self.monitor_op = np.less
            self.min_delta *= -1
            self.best = np.inf
        else:
            self.monitor_op = np.greater
            self.min_delta *= 1
            self.best = -np.inf
    
    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """訓練開始時重置狀態"""
        self.wait = 0
        self.stopped_epoch = 0
        if self.mode == 'min':
            self.best = np.inf
        else:
            self.best = -np.inf
        
        if self.verbose:
            self.logger.info(f"早停回調已激活 - 監控: {self.monitor}, 耐心值: {self.patience}")
    
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """訓練結束時的處理"""
        if self.stopped_epoch > 0 and self.verbose:
            self.logger.info(f"Epoch {self.stopped_epoch + 1}: 早停")
            
        if self.restore_best_weights and self.best_weights is not None:
            if self.verbose:
                self.logger.info("恢復最佳權重")
            self.trainer.model.load_state_dict(self.best_weights)
    
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch開始時的處理"""
        pass
    
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch結束時檢查早停條件"""
        if logs is None:
            return
        
        current = logs.get(self.monitor)
        if current is None:
            self.logger.warning(f"早停監控指標 '{self.monitor}' 不存在")
            return
        
        if self.monitor_op(current - self.min_delta, self.best):
            self.best = current
            self.wait = 0
            
            # 保存最佳權重
            if self.restore_best_weights:
                self.best_weights = self.trainer.model.state_dict().copy()
                
            if self.verbose:
                self.logger.info(f"Epoch {epoch + 1}: {self.monitor} 改善到 {current:.6f}")
        else:
            self.wait += 1
            if self.verbose:
                self.logger.info(f"Epoch {epoch + 1}: {self.monitor} 沒有改善，等待 {self.wait}/{self.patience}")
            
            if self.wait >= self.patience:
                self.stopped_epoch = epoch
                self.trainer.stop_training()


class ModelCheckpoint(BaseCallback):
    """模型檢查點回調"""
    
    def __init__(self,
                 filepath: str,
                 monitor: str = 'val_loss',
                 save_best_only: bool = False,
                 save_weights_only: bool = False,
                 mode: str = 'min',
                 save_frequency: int = 1,
                 verbose: bool = True):
        """
        初始化檢查點回調
        
        Args:
            filepath: 保存路徑模板（支持格式化）
            monitor: 監控的指標名稱
            save_best_only: 是否只保存最佳模型
            save_weights_only: 是否只保存權重
            mode: 'min' 或 'max'，指標的優化方向
            save_frequency: 保存頻率（epoch）
            verbose: 是否輸出詳細信息
        """
        super().__init__()
        self.filepath = filepath
        self.monitor = monitor
        self.save_best_only = save_best_only
        self.save_weights_only = save_weights_only
        self.mode = mode
        self.save_frequency = save_frequency
        self.verbose = verbose
        
        # 確保保存目錄存在
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        if mode == 'min':
            self.monitor_op = np.less
            self.best = np.inf
        else:
            self.monitor_op = np.greater
            self.best = -np.inf
    
    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """訓練開始時重置最佳值"""
        if self.mode == 'min':
            self.best = np.inf
        else:
            self.best = -np.inf
    
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """訓練結束時的處理"""
        pass
    
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch開始時的處理"""
        pass
    
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch結束時檢查是否需要保存"""
        if (epoch + 1) % self.save_frequency != 0:
            return
        
        if logs is None:
            logs = {}
        
        current = logs.get(self.monitor)
        
        # 決定是否保存
        save_model = False
        if self.save_best_only:
            if current is not None and self.monitor_op(current, self.best):
                self.best = current
                save_model = True
                if self.verbose:
                    self.logger.info(f"Epoch {epoch + 1}: {self.monitor} 改善到 {current:.6f}")
        else:
            save_model = True
        
        if save_model:
            # 格式化文件路徑
            filepath = self.filepath.format(epoch=epoch + 1, **logs)
            
            # 準備保存數據
            if self.save_weights_only:
                torch.save(self.trainer.model.state_dict(), filepath)
            else:
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': self.trainer.model.state_dict(),
                    'optimizer_state_dict': self.trainer.optimizer.state_dict(),
                    'best_metric': self.best,
                    'logs': logs
                }
                
                if self.trainer.scheduler:
                    checkpoint['scheduler_state_dict'] = self.trainer.scheduler.state_dict()
                
                torch.save(checkpoint, filepath)
            
            if self.verbose:
                self.logger.info(f"Epoch {epoch + 1}: 模型已保存到 {filepath}")


class LRScheduler(BaseCallback):
    """學習率調度器回調"""
    
    def __init__(self, 
                 scheduler: torch.optim.lr_scheduler._LRScheduler,
                 monitor: Optional[str] = None,
                 verbose: bool = True):
        """
        初始化學習率調度器回調
        
        Args:
            scheduler: PyTorch學習率調度器
            monitor: 監控的指標名稱（用於ReduceLROnPlateau等）
            verbose: 是否輸出詳細信息
        """
        super().__init__()
        self.scheduler = scheduler
        self.monitor = monitor
        self.verbose = verbose
        
        # 檢查調度器類型
        self.requires_metric = hasattr(scheduler, 'step') and monitor is not None
    
    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """訓練開始時的處理"""
        if self.verbose:
            initial_lr = self.trainer.optimizer.param_groups[0]['lr']
            self.logger.info(f"學習率調度器已激活 - 初始LR: {initial_lr:.6f}")
    
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """訓練結束時的處理"""
        if self.verbose:
            final_lr = self.trainer.optimizer.param_groups[0]['lr']
            self.logger.info(f"訓練結束 - 最終LR: {final_lr:.6f}")
    
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch開始時的處理"""
        pass
    
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch結束時更新學習率"""
        old_lr = self.trainer.optimizer.param_groups[0]['lr']
        
        if self.requires_metric:
            if logs and self.monitor in logs:
                metric_value = logs[self.monitor]
                self.scheduler.step(metric_value)
            else:
                self.logger.warning(f"監控指標 '{self.monitor}' 不存在，跳過學習率更新")
                return
        else:
            self.scheduler.step()
        
        new_lr = self.trainer.optimizer.param_groups[0]['lr']
        
        if self.verbose and abs(new_lr - old_lr) > 1e-8:
            self.logger.info(f"Epoch {epoch + 1}: 學習率從 {old_lr:.6f} 調整到 {new_lr:.6f}")


class CallbackManager:
    """回調管理器"""
    
    def __init__(self, callbacks: Optional[List[BaseCallback]] = None):
        """
        初始化回調管理器
        
        Args:
            callbacks: 回調列表
        """
        self.callbacks = callbacks or []
        self.trainer = None
        self.logger = logging.getLogger(__name__)
    
    def set_trainer(self, trainer):
        """設置訓練器引用"""
        self.trainer = trainer
        for callback in self.callbacks:
            callback.set_trainer(trainer)
    
    def add_callback(self, callback: BaseCallback):
        """添加回調"""
        callback.set_trainer(self.trainer)
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: BaseCallback):
        """移除回調"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """訓練開始時調用所有回調"""
        for callback in self.callbacks:
            try:
                callback.on_train_begin(logs)
            except Exception as e:
                self.logger.error(f"回調 {callback.__class__.__name__} 在 on_train_begin 中出錯: {e}")
    
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """訓練結束時調用所有回調"""
        for callback in self.callbacks:
            try:
                callback.on_train_end(logs)
            except Exception as e:
                self.logger.error(f"回調 {callback.__class__.__name__} 在 on_train_end 中出錯: {e}")
    
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch開始時調用所有回調"""
        for callback in self.callbacks:
            try:
                callback.on_epoch_begin(epoch, logs)
            except Exception as e:
                self.logger.error(f"回調 {callback.__class__.__name__} 在 on_epoch_begin 中出錯: {e}")
    
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch結束時調用所有回調"""
        for callback in self.callbacks:
            try:
                callback.on_epoch_end(epoch, logs)
            except Exception as e:
                self.logger.error(f"回調 {callback.__class__.__name__} 在 on_epoch_end 中出錯: {e}")
    
    def on_batch_begin(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """批次開始時調用所有回調"""
        for callback in self.callbacks:
            try:
                callback.on_batch_begin(batch, logs)
            except Exception as e:
                self.logger.error(f"回調 {callback.__class__.__name__} 在 on_batch_begin 中出錯: {e}")
    
    def on_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """批次結束時調用所有回調"""
        for callback in self.callbacks:
            try:
                callback.on_batch_end(batch, logs)
            except Exception as e:
                self.logger.error(f"回調 {callback.__class__.__name__} 在 on_batch_end 中出錯: {e}")


class MetricsLogger(BaseCallback):
    """指標記錄回調"""
    
    def __init__(self, log_dir: str = "logs", log_frequency: int = 1):
        """
        初始化指標記錄回調
        
        Args:
            log_dir: 日誌目錄
            log_frequency: 記錄頻率（epoch）
        """
        super().__init__()
        self.log_dir = Path(log_dir)
        self.log_frequency = log_frequency
        self.history = {
            'epoch': [],
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
        
        # 確保日誌目錄存在
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """訓練開始時初始化"""
        self.history = {
            'epoch': [],
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
    
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """訓練結束時保存歷史"""
        # 保存為JSON文件
        import json
        history_file = self.log_dir / "training_history.json"
        with open(history_file, 'w') as f:
            json.dump(self.history, f, indent=2)
        
        self.logger.info(f"訓練歷史已保存到 {history_file}")
    
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch開始時的處理"""
        pass
    
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch結束時記錄指標"""
        if (epoch + 1) % self.log_frequency != 0:
            return
        
        if logs is None:
            return
        
        self.history['epoch'].append(epoch + 1)
        
        # 記錄各種指標
        for key, value in logs.items():
            if key not in self.history:
                self.history[key] = []
            self.history[key].append(value)
        
        # 記錄學習率
        if self.trainer and self.trainer.optimizer:
            lr = self.trainer.optimizer.param_groups[0]['lr']
            if 'learning_rate' not in self.history:
                self.history['learning_rate'] = []
            self.history['learning_rate'].append(lr)