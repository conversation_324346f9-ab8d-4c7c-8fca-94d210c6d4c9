<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StandardEquirectRotate 使用教學</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f8fafc;
            --accent-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --text-color: #1e293b;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.7;
            color: var(--text-color);
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.2);
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            background: white;
            border-radius: 1rem;
            padding: 2.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin: 2rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
        }

        h3 {
            color: var(--accent-color);
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem 0;
        }

        h4 {
            color: var(--text-color);
            font-size: 1.2rem;
            margin: 1rem 0 0.5rem 0;
        }

        .icon {
            margin-right: 0.5rem;
            font-size: 1.2em;
        }

        p {
            margin-bottom: 1rem;
            color: #475569;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
            color: #475569;
        }

        code {
            background: #f1f5f9;
            padding: 0.2rem 0.4rem;
            border-radius: 0.3rem;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 0.9em;
            color: var(--primary-color);
        }

        pre {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 0.8rem;
            overflow-x: auto;
            margin: 1rem 0;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }

        .highlight {
            background: linear-gradient(120deg, #fef3c7 0%, #fde68a 100%);
            padding: 1.5rem;
            border-radius: 0.8rem;
            border-left: 4px solid var(--warning-color);
            margin: 1.5rem 0;
        }

        .info-box {
            background: linear-gradient(120deg, #dbeafe 0%, #bfdbfe 100%);
            padding: 1.5rem;
            border-radius: 0.8rem;
            border-left: 4px solid var(--primary-color);
            margin: 1.5rem 0;
        }

        .success-box {
            background: linear-gradient(120deg, #dcfce7 0%, #bbf7d0 100%);
            padding: 1.5rem;
            border-radius: 0.8rem;
            border-left: 4px solid var(--accent-color);
            margin: 1.5rem 0;
        }

        .danger-box {
            background: linear-gradient(120deg, #fee2e2 0%, #fecaca 100%);
            padding: 1.5rem;
            border-radius: 0.8rem;
            border-left: 4px solid var(--danger-color);
            margin: 1.5rem 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
            border-radius: 0.8rem;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--secondary-color);
            font-weight: 600;
            color: var(--text-color);
        }

        tr:hover {
            background: #f8fafc;
        }

        .nav {
            background: white;
            padding: 1rem;
            border-radius: 0.8rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .nav ul {
            list-style: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .nav a {
            color: var(--primary-color);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .nav a:hover {
            background: var(--primary-color);
            color: white;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-weight: bold;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }

        .comparison-card {
            padding: 1.5rem;
            border-radius: 0.8rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .old-way {
            background: linear-gradient(120deg, #fee2e2 0%, #fecaca 100%);
            border-left: 4px solid var(--danger-color);
        }

        .new-way {
            background: linear-gradient(120deg, #dcfce7 0%, #bbf7d0 100%);
            border-left: 4px solid var(--accent-color);
        }

        .star-rating {
            color: #fbbf24;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 1.5rem;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            pre {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎯 StandardEquirectRotate 使用教學</h1>
            <p class="subtitle">高效能全景圖像旋轉工具完整指南</p>
        </header>

        <nav class="nav">
            <ul>
                <li><a href="#overview">概述</a></li>
                <li><a href="#quickstart">快速開始</a></li>
                <li><a href="#parameters">參數說明</a></li>
                <li><a href="#examples">應用範例</a></li>
                <li><a href="#advanced">高階設定</a></li>
                <li><a href="#troubleshooting">問題排解</a></li>
                <li><a href="#performance">性能對比</a></li>
            </ul>
        </nav>

        <div class="content">
            <h2 id="overview"><span class="icon">📖</span>概述</h2>
            <p><code>StandardEquirectRotate</code> 是一個專為全景圖像（Equirectangular格式）設計的旋轉工具，支援攝影測量標準的OPK角度系統，並可以一次性執行多個連續旋轉操作，大幅提升處理效率。</p>
            
            <div class="highlight">
                <strong>💡 核心優勢</strong>
                <ul>
                    <li>一次性完成多重旋轉，避免重複插值損失</li>
                    <li>支援攝影測量標準OPK角度系統</li>
                    <li>多種插值方法，平衡速度與質量</li>
                    <li>靈活的API設計，適應不同使用場景</li>
                </ul>
            </div>
        </div>

        <div class="content">
            <h2 id="quickstart"><span class="icon">🚀</span>快速開始</h2>
            
            <h3>基本使用流程</h3>
            
            <div class="info-box">
                <strong>5步驟完成全景圖像旋轉</strong>
            </div>

            <p><span class="step-number">1</span><strong>載入圖像</strong></p>
            <pre><code>import cv2
import numpy as np
from your_module import StandardEquirectRotate  # 替換為實際的模組名稱

# 載入圖像
img = cv2.imread('your_panorama.jpg')
h, w = img.shape[:2]</code></pre>

            <p><span class="step-number">2</span><strong>創建實例</strong></p>
            <pre><code># 只需要指定圖像尺寸
rotator = StandardEquirectRotate(h, w)</code></pre>

            <p><span class="step-number">3</span><strong>定義旋轉參數</strong></p>
            <pre><code># 第一次旋轉的角度（度）
rotation1 = (omega, phi, kappa)  
# 第二次旋轉的角度（度）
rotation2 = (90, -heading, 0)    </code></pre>

            <p><span class="step-number">4</span><strong>執行旋轉</strong></p>
            <pre><code># 一次性完成多個旋轉
result = rotator.rotate(img, [
    [rotation1, True],   # 第一次旋轉，使用逆矩陣
    [rotation2, False]   # 第二次旋轉，正向變換
])</code></pre>

            <p><span class="step-number">5</span><strong>保存結果</strong></p>
            <pre><code># 保存旋轉後的圖像
cv2.imwrite('rotated_panorama.jpg', result)</code></pre>
        </div>

        <div class="content">
            <h2 id="parameters"><span class="icon">📋</span>詳細參數說明</h2>
            
            <h3>初始化參數</h3>
            <pre><code>rotator = StandardEquirectRotate(height, width)</code></pre>
            
            <table>
                <thead>
                    <tr>
                        <th>參數</th>
                        <th>說明</th>
                        <th>要求</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>height</code></td>
                        <td>圖像高度（像素）</td>
                        <td>正整數</td>
                    </tr>
                    <tr>
                        <td><code>width</code></td>
                        <td>圖像寬度（像素）</td>
                        <td>必須是高度的2倍</td>
                    </tr>
                </tbody>
            </table>

            <h3>旋轉參數格式</h3>
            <p>支援多種輸入格式，選擇最適合你的使用場景：</p>

            <h4>格式1：列表格式（推薦）</h4>
            <pre><code>rotations = [
    [(omega1, phi1, kappa1), use_inverse1],
    [(omega2, phi2, kappa2), use_inverse2],
    # 可以添加更多旋轉...
]</code></pre>

            <h4>格式2：元組格式</h4>
            <pre><code>rotations = [
    (omega1, phi1, kappa1, use_inverse1),
    (omega2, phi2, kappa2, use_inverse2),
]</code></pre>

            <h4>格式3：字典格式（最清晰）</h4>
            <pre><code>rotations = [
    {"rotation": (omega1, phi1, kappa1), "use_inverse": True},
    {"rotation": (omega2, phi2, kappa2), "use_inverse": False},
]</code></pre>

            <h3>角度參數說明</h3>
            <table>
                <thead>
                    <tr>
                        <th>角度</th>
                        <th>名稱</th>
                        <th>旋轉軸</th>
                        <th>說明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>omega</code> (ω)</td>
                        <td>俯仰角（Pitch）</td>
                        <td>X軸</td>
                        <td>前後傾斜</td>
                    </tr>
                    <tr>
                        <td><code>phi</code> (φ)</td>
                        <td>橫滾角（Roll）</td>
                        <td>Y軸</td>
                        <td>左右傾斜</td>
                    </tr>
                    <tr>
                        <td><code>kappa</code> (κ)</td>
                        <td>偏航角（Yaw）</td>
                        <td>Z軸</td>
                        <td>水平旋轉</td>
                    </tr>
                </tbody>
            </table>

            <h3>use_inverse 參數</h3>
            <div class="comparison-grid">
                <div class="comparison-card success-box">
                    <h4>✅ True - 逆矩陣變換</h4>
                    <ul>
                        <li><strong>用途</strong>：校正相機姿態</li>
                        <li><strong>效果</strong>：將傾斜的圖像"拉正"</li>
                        <li><strong>典型場景</strong>：從Metashape等軟體獲得的外方位元素</li>
                    </ul>
                </div>
                <div class="comparison-card info-box">
                    <h4>📐 False - 正向變換</h4>
                    <ul>
                        <li><strong>用途</strong>：主動旋轉圖像</li>
                        <li><strong>效果</strong>：轉到指定方向</li>
                        <li><strong>典型場景</strong>：調整觀看視角、對齊特定方向</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content">
            <h2 id="examples"><span class="icon">🎯</span>實際應用範例</h2>
            
            <h3>範例1：校正相機姿態</h3>
            <pre><code># 從Metashape獲得的外方位元素
camera_rotation = (12.5, -8.3, 15.2)  # (omega, phi, kappa)

# 校正相機傾斜
rotator = StandardEquirectRotate(h, w)
corrected_img = rotator.rotate(img, [
    [camera_rotation, True]  # 使用逆矩陣校正
])</code></pre>

            <h3>範例2：兩步驟旋轉處理</h3>
            <pre><code># 步驟1：校正相機姿態
camera_rotation = (omega, phi, kappa)

# 步驟2：調整到北向
heading = calculate_heading(omega, phi)  # 計算航向角
north_alignment = (90, -heading, 0)

# 一次性完成兩個步驟
rotator = StandardEquirectRotate(h, w)
final_result = rotator.rotate(img, [
    [camera_rotation, True],    # 校正姿態
    [north_alignment, False]    # 對齊北向
])</code></pre>

            <h3>範例3：批量處理不同視角</h3>
            <pre><code># 定義多個觀看視角
viewpoints = [
    (0, 0, 0),      # 原始視角
    (0, 0, 90),     # 向右轉90度
    (0, 0, 180),    # 向後轉180度
    (45, 0, 0),     # 向上傾斜45度
]

rotator = StandardEquirectRotate(h, w)
results = []

for i, viewpoint in enumerate(viewpoints):
    result = rotator.rotate(img, [
        [viewpoint, False]  # 正向旋轉到指定視角
    ])
    results.append(result)
    cv2.imwrite(f'viewpoint_{i}.jpg', result)</code></pre>
        </div>

        <div class="content">
            <h2 id="advanced"><span class="icon">⚙️</span>高階設定</h2>
            
            <h3>插值方法選擇</h3>
            <pre><code># 不同質量等級的插值
result_fast = rotator.rotate(img, rotations, cv2.INTER_LINEAR)     # 快速
result_good = rotator.rotate(img, rotations, cv2.INTER_CUBIC)      # 推薦
result_best = rotator.rotate(img, rotations, cv2.INTER_LANCZOS4)   # 最高質量</code></pre>

            <h3>插值方法比較</h3>
            <table>
                <thead>
                    <tr>
                        <th>方法</th>
                        <th>速度</th>
                        <th>質量</th>
                        <th>推薦用途</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>INTER_LINEAR</code></td>
                        <td><span class="star-rating">⭐⭐⭐⭐</span></td>
                        <td><span class="star-rating">⭐⭐⭐</span></td>
                        <td>預覽、實時處理</td>
                    </tr>
                    <tr>
                        <td><code>INTER_CUBIC</code></td>
                        <td><span class="star-rating">⭐⭐⭐</span></td>
                        <td><span class="star-rating">⭐⭐⭐⭐</span></td>
                        <td>一般輸出（<strong>默認推薦</strong>）</td>
                    </tr>
                    <tr>
                        <td><code>INTER_LANCZOS4</code></td>
                        <td><span class="star-rating">⭐⭐</span></td>
                        <td><span class="star-rating">⭐⭐⭐⭐⭐</span></td>
                        <td>最終輸出、印刷品質</td>
                    </tr>
                </tbody>
            </table>

            <h3>便利方法</h3>
            <pre><code># 單次旋轉
result = rotator.rotate_single(img, (omega, phi, kappa), use_inverse=True)

# 多次旋轉（簡化格式）
rotation_list = [
    [(omega1, phi1, kappa1), True],
    [(omega2, phi2, kappa2), False]
]
result = rotator.rotate_sequence(img, rotation_list)</code></pre>
        </div>

        <div class="content">
            <h2 id="troubleshooting"><span class="icon">🐛</span>常見問題與解決方案</h2>
            
            <h3>Q1: 圖片比例錯誤</h3>
            <div class="danger-box">
                <strong>錯誤訊息：</strong><br>
                <code>AssertionError: 圖片比例不正確！高度:1024, 寬度:1024, 應為 2:1 比例</code>
            </div>
            <div class="success-box">
                <strong>解決方案：</strong><br>
                確保輸入圖像寬度是高度的2倍，這是等距圓柱投影的標準比例。
            </div>

            <h3>Q2: 旋轉方向不對</h3>
            <div class="info-box">
                <strong>解決方案：</strong><br>
                檢查 <code>use_inverse</code> 參數：
                <ul>
                    <li>如果圖像轉反了，嘗試將 <code>True</code> 改為 <code>False</code>，或反之</li>
                    <li>相機校正通常使用 <code>True</code></li>
                    <li>主動旋轉通常使用 <code>False</code></li>
                </ul>
            </div>

            <h3>Q3: 角度單位錯誤</h3>
            <div class="highlight">
                <strong>重要提醒：</strong><br>
                確保所有角度都是<strong>度</strong>（degrees），不是弧度（radians）。
            </div>

            <h3>Q4: 性能優化</h3>
            <div class="success-box">
                <strong>優化建議：</strong>
                <ul>
                    <li>使用 <code>INTER_LINEAR</code> 進行快速預覽</li>
                    <li>最終輸出時才使用 <code>INTER_CUBIC</code> 或 <code>INTER_LANCZOS4</code></li>
                    <li>避免分別執行多次旋轉，改用一次性多重旋轉</li>
                </ul>
            </div>
        </div>

        <div class="content">
            <h2 id="performance"><span class="icon">📊</span>性能對比</h2>
            
            <h3>傳統方式 vs 新方式</h3>
            <div class="comparison-grid">
                <div class="comparison-card old-way">
                    <h4>❌ 傳統方式（低效）</h4>
                    <pre><code># 需要兩次重映射，效率低
rotator1 = StandardEquirectRotate(h, w, rotation1, use_inverse=True)
temp_result = rotator1.rotate(img)

rotator2 = StandardEquirectRotate(h, w, rotation2, use_inverse=False)
final_result = rotator2.rotate(temp_result)</code></pre>
                </div>
                <div class="comparison-card new-way">
                    <h4>✅ 新方式（高效）</h4>
                    <pre><code># 只需一次重映射，效率高
rotator = StandardEquirectRotate(h, w)
final_result = rotator.rotate(img, [
    [rotation1, True],
    [rotation2, False]
])</code></pre>
                </div>
            </div>

            <h3>性能提升統計</h3>
            <table>
                <thead>
                    <tr>
                        <th>項目</th>
                        <th>傳統方式</th>
                        <th>新方式</th>
                        <th>改善幅度</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>計算時間</td>
                        <td>100%</td>
                        <td>~50%</td>
                        <td><span style="color: var(--accent-color);">減少50%</span></td>
                    </tr>
                    <tr>
                        <td>記憶體使用</td>
                        <td>100%</td>
                        <td>~67%</td>
                        <td><span style="color: var(--accent-color);">減少33%</span></td>
                    </tr>
                    <tr>
                        <td>圖像質量</td>
                        <td>多次插值損失</td>
                        <td>單次插值</td>
                        <td><span style="color: var(--accent-color);">質量提升</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="content">
            <h2><span class="icon">📝</span>總結</h2>
            <p><code>StandardEquirectRotate</code> 提供了一個高效、靈活的全景圖像旋轉解決方案：</p>
            
            <div class="success-box">
                <ul>
                    <li>✅ <strong>高效能</strong>: 一次性完成多重旋轉</li>
                    <li>✅ <strong>高品質</strong>: 支援多種插值方法</li>
                    <li>✅ <strong>易使用</strong>: 直觀的API設計</li>
                    <li>✅ <strong>標準化</strong>: 符合攝影測量OPK標準</li>
                    <li>✅ <strong>靈活性</strong>: 支援多種輸入格式</li>
                </ul>
            </div>
            
            <p>無論是相機姿態校正、視角調整，還是批量處理，這個工具都能滿足你的需求！</p>
        </div>
    </div>

    <script>
        // 平滑滾動到錨點
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 高亮當前章節
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('h2[id]');
            const navLinks = document.querySelectorAll('.nav a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    <style>
        .nav a.active {
            background: var(--primary-color);
            color: white;
        }
    </style>
</body>
</html>