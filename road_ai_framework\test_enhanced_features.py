#!/usr/bin/env python3
"""
🧪 測試增強功能
測試CSV增量更新和三視圖GT加載功能
"""

import os
import sys
from pathlib import Path

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.import_helper import setup_project_paths
setup_project_paths()

def test_enhanced_features():
    """測試增強功能"""
    print("🧪 測試增強功能")
    print("=" * 60)
    
    # 測試用路徑（基於用戶提供的參考圖像）
    test_image_path = "/mnt/d/image/5_test_image_test/test_6448/images/1845.jpg"
    test_output_dir = "/mnt/d/image/5_test_image_test/test_6448_enhanced_output"
    test_labelme_dir = "/mnt/d/image/5_test_image_test/test_6448/labels"
    test_model_path = "/mnt/d/4_road_crack/best.pt"
    
    # 檢查路徑是否存在
    print("🔍 檢查測試路徑...")
    if not Path(test_image_path).exists():
        print(f"❌ 測試圖像不存在: {test_image_path}")
        print("💡 請修改 test_image_path 為您的測試圖像路徑")
        return
    
    if not Path(test_model_path).exists():
        print(f"❌ 模型不存在: {test_model_path}")
        print("💡 請修改 test_model_path 為您的YOLO模型路徑")
        return
    
    print("✅ 路徑檢查通過")
    
    try:
        # 創建配置
        print("\n🔧 創建增強配置...")
        from models.inference.config_manager import UnifiedYOLOConfigManager
        
        config_manager = UnifiedYOLOConfigManager()
        
        # 基礎設置
        config_manager.model.segmentation_model_path = test_model_path
        config_manager.model.device = "cuda"
        config_manager.inference.global_conf = 0.05
        
        # 啟用所有增強功能
        config_manager.visualization.enable_three_view_output = True
        config_manager.visualization.save_visualizations = True
        config_manager.visualization.save_predictions = True
        config_manager.visualization.save_statistics = True
        
        # 設置路徑
        config_manager.paths.labelme_dir = test_labelme_dir
        config_manager.paths.output_path = test_output_dir
        
        # 創建增強推理引擎
        print("🚀 初始化增強推理引擎...")
        from models.inference.unified_yolo_inference import UnifiedYOLOInference
        
        inference = UnifiedYOLOInference()
        inference.config_manager = config_manager
        inference._load_model()
        
        # 重新初始化組件
        from models.inference.unified_yolo_inference import FontManager, ThreeViewGenerator, IntelligentFilter, SAHIEnhanced
        
        inference.font_manager = FontManager(
            font_size=config_manager.visualization.font_size,
            font_thickness=config_manager.visualization.font_thickness,
            font_scale=config_manager.visualization.font_scale
        )
        
        inference.three_view_generator = ThreeViewGenerator(
            font_manager=inference.font_manager,
            config_manager=config_manager,
            layout=config_manager.visualization.three_view_layout,
            spacing=config_manager.visualization.three_view_spacing
        )
        
        inference.intelligent_filter = IntelligentFilter(config_manager)
        inference.sahi_enhanced = SAHIEnhanced(config_manager)
        
        print("✅ 初始化完成")
        
        # 創建輸出目錄
        output_path = Path(test_output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        (output_path / "images").mkdir(exist_ok=True)
        (output_path / "reports").mkdir(exist_ok=True)
        
        # 測試增強功能
        print(f"\n🧪 測試增強功能...")
        print(f"📁 測試圖像: {test_image_path}")
        print(f"📁 GT目錄: {test_labelme_dir}")
        print(f"📁 輸出目錄: {test_output_dir}")
        
        # 執行推理
        print("\n🎯 執行推理...")
        result = inference.predict_single_image(test_image_path, test_output_dir)
        
        # 檢查結果
        image_name = Path(test_image_path).stem
        output_files = []
        
        # 檢查生成的文件
        images_dir = output_path / "images"
        reports_dir = output_path / "reports"
        
        # 檢查可視化圖像
        vis_file = images_dir / f"{image_name}_vis.jpg"
        if vis_file.exists():
            output_files.append(("可視化圖像", str(vis_file)))
        
        # 檢查三視圖
        three_view_file = images_dir / f"{image_name}_three_view.jpg"
        if three_view_file.exists():
            output_files.append(("增強三視圖", str(three_view_file)))
        
        # 檢查CSV文件
        image_csv = reports_dir / "image_metrics_incremental.csv"
        class_csv = reports_dir / "class_metrics_incremental.csv"
        if image_csv.exists():
            output_files.append(("圖像級別CSV", str(image_csv)))
        if class_csv.exists():
            output_files.append(("類別級別CSV", str(class_csv)))
        
        # 顯示測試結果
        print(f"\n✅ 測試完成! 檢測到 {len(result['detections'])} 個目標")
        
        if result['detections']:
            print("\n🎯 檢測結果:")
            for det in result['detections']:
                class_name = det['class_name']
                confidence = det['confidence']
                print(f"  • {class_name}: {confidence:.3f}")
        
        print(f"\n📁 生成的文件:")
        for file_type, file_path in output_files:
            print(f"  • {file_type}: {file_path}")
        
        # 功能測試結果
        print(f"\n🧪 功能測試結果:")
        print(f"  ✅ CSV增量更新: {'通過' if image_csv.exists() else '失敗'}")
        print(f"  ✅ 三視圖生成: {'通過' if three_view_file.exists() else '失敗'}")
        print(f"  ✅ GT標註加載: {'已嘗試' if test_labelme_dir else '跳過'}")
        print(f"  ✅ 可視化圖像: {'通過' if vis_file.exists() else '失敗'}")
        
        # 檢查GT加載是否成功
        if Path(test_labelme_dir).exists():
            gt_json = Path(test_labelme_dir) / f"{image_name}.json"
            if gt_json.exists():
                print(f"  ✅ GT文件存在: {gt_json}")
                print(f"  📄 三視圖應包含GT標註顯示")
            else:
                print(f"  ⚠️ GT文件不存在: {gt_json}")
                print(f"  💡 三視圖將顯示兩視圖（原圖+預測）")
        
        print(f"\n🎉 增強功能測試完成!")
        print(f"💡 請檢查三視圖文件，確認是否與參考圖像格式一致")
        print(f"💡 CSV文件支持增量更新，每處理一張圖像立即寫入")
        
        return True
        
    except Exception as e:
        print(f"\n💥 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        
        print(f"\n🛠️ 故障排除建議:")
        print(f"  1. 確認模型路徑正確且可訪問")
        print(f"  2. 確認輸出目錄有寫入權限")
        print(f"  3. 確認CUDA環境配置正確")
        print(f"  4. 檢查圖像文件是否可讀取")
        
        return False


def main():
    """主函數"""
    print("🚀 增強功能測試套件")
    print("測試CSV增量更新和三視圖GT加載功能")
    print("=" * 60)
    
    # 執行測試
    success = test_enhanced_features()
    
    if success:
        print("\n✅ 所有測試通過!")
        print("\n📋 功能說明:")
        print("  🔄 CSV增量更新: 每張圖像處理完立即更新CSV文件")
        print("  🖼️ 增強三視圖: 自動加載GT標註，生成三視圖對比")
        print("  🎭 智能mask處理: 支持各種mask格式，修復維度不匹配")
        print("  📊 實時統計: 提供實時處理進度和統計信息")
    else:
        print("\n❌ 測試失敗，請檢查配置和環境")
    
    print(f"\n💡 如需自定義測試參數，請編輯此文件的路徑配置")
    
    return success


if __name__ == "__main__":
    main()