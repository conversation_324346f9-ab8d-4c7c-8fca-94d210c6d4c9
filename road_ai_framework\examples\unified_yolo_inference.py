#!/usr/bin/env python3
"""
統一YOLO推理工具 (Unified YOLO Inference Tool)
整合Enhanced YOLO和Simple Model Labeling Tool的優勢功能

結合功能：
✅ Enhanced YOLO: 雙模型(YOLO12+YOLO11) + 完整SAHI參數 + LabelMe自動化
✅ Simple Tool: 雙模型協同 + 類別特定閾值 + 智能過濾 + 高級系統管理
✅ 新增功能: GT對比可視化 + 智能label計數 + 統一配置系統
"""

from pathlib import Path
import sys
import json
import logging
import time
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import torch
import cv2
import matplotlib.pyplot as plt

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

# 設置matplotlib中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

# 設置日誌
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('UnifiedYOLO')


class TaskType(Enum):
    """任務類型枚舉"""
    DETECTION = "detection"
    SEGMENTATION = "segmentation"
    BOTH = "both"


class OutputFormat(Enum):
    """輸出格式枚舉"""
    BBOX = "bbox"
    MASK = "mask"
    BOTH = "both"


class VisualizationMode(Enum):
    """可視化模式枚舉"""
    STANDARD = "standard"          # 原圖+檢測+分割+統計
    COMPARISON = "comparison"      # 原圖+GT+預測對比
    COMBINED = "combined"          # 完整6面板展示
    

@dataclass
class ModelConfig:
    """模型配置類 - 整合兩個工具的模型功能"""
    
    # === Enhanced YOLO模型配置 ===
    detection_model_path: str = ""                    # YOLO12檢測模型路徑
    segmentation_model_path: str = r"D:\4_road_crack\best.pt"                 # YOLO11分割模型路徑
    device: str = "cuda"                              # 設備選擇
    
    # === Simple Tool模型配置 ===
    secondary_model_path: Optional[str] = None        # 雙模型協同 - 副模型
    enable_consensus: bool = True                     # 雙模型共識機制
    consensus_threshold: float = 0.3                  # 共識閾值
    
    # === 圖像縮放與通用推理參數 ===
    resize_ratio: float = 0.3                         # 圖像縮放比例 (1.0=不縮放)
    img_size: int = 640                               # 圖像大小 (縮放後)
    global_conf: float = 0.05                        # 全局置信度閾值
    iou_threshold: float = 0.45                       # IoU閾值
    max_det: int = 1000                               # 最大檢測數量
    
    # === 類別特定配置 (Simple Tool優勢) ===
    class_thresholds: Optional[Dict[int, float]] = None  # 類別特定閾值
    enable_class_filtering: bool = True               # 類別間智能過濾
    enable_detection_merge: bool = True               # 檢測合併
    iou_merge_threshold: float = 0.3                  # 合併IoU閾值


@dataclass 
class SAHIConfig:
    """SAHI配置類 - 完整23個參數"""
    
    # === 核心SAHI控制 ===
    enable_sahi: bool = False                         # 是否啟用SAHI
    
    # === 切片參數 ===
    slice_height: int = 640                           # 切片高度
    slice_width: int = 640                            # 切片寬度
    overlap_height_ratio: float = 0.2                 # 高度重疊比例
    overlap_width_ratio: float = 0.2                  # 寬度重疊比例
    
    # === 進階SAHI參數 ===
    auto_slice_resolution: bool = True                # 自動切片解析度
    perform_standard_pred: bool = True                # 執行標準預測
    roi_ratio: Tuple[float, float, float, float] = (0.0, 0.0, 1.0, 1.0)  # ROI區域
    
    # === 後處理參數 ===
    postprocess_type: str = "GREEDYNMM"              # 後處理類型
    postprocess_match_threshold: float = 0.1          # 後處理匹配閾值
    postprocess_class_agnostic: bool = False          # 類別無關後處理
    
    # === 類別過濾參數 ===
    exclude_classes_by_name: List[str] = field(default_factory=list)  # 按名稱排除
    exclude_classes_by_id: List[int] = field(default_factory=list)    # 按ID排除
    
    # === 輸出控制 ===
    no_standard_prediction: bool = False              # 禁用標準預測
    no_sliced_prediction: bool = False                # 禁用切片預測
    export_pickle: bool = False                       # 導出pickle
    export_crop: bool = False                         # 導出裁剪圖像


@dataclass
class ProcessingConfig:
    """處理配置類 - 整合輸出和可視化功能"""
    
    # === 輸出格式控制 ===
    output_format: OutputFormat = OutputFormat.BOTH  # 輸出格式
    save_visualizations: bool = True                  # 保存可視化
    save_predictions: bool = True                     # 保存預測結果
    save_statistics: bool = True                      # 保存統計信息
    
    # === 可視化配置 ===
    visualization_mode: VisualizationMode = VisualizationMode.COMBINED  # 可視化模式
    visualization_size: Tuple[int, int] = (1200, 900)  # 可視化尺寸
    show_masks: bool = True                            # 顯示遮罩
    mask_alpha: float = 0.35                          # 遮罩透明度
    
    # === Simple Tool優勢功能 ===
    target_classes: Optional[List[int]] = None        # 指定保存類別
    skip_empty: bool = True                           # 跳過空結果
    include_base64_image: bool = False                # 是否在LabelMe JSON中包含base64編碼的圖像
    
    # === 批次處理配置 ===
    batch_size: int = 10                             # 批次大小
    smart_label_counting: bool = True                 # 智能label計數


@dataclass
class AnnotationConfig:
    """標註配置類 - LabelMe和GT處理"""
    
    # === LabelMe自動配置 (Enhanced YOLO優勢) ===
    labelme_dir: str = r"D:\image\5_test_image_test\test1\label"                           # LabelMe標註目錄
    auto_generate_classes: bool = True               # 自動生成類別配置
    auto_convert_annotations: bool = True            # 自動轉換標註
    
    # === GT對比功能 (新增) ===
    enable_gt_comparison: bool = True                # 啟用GT對比
    gt_annotation_format: str = "auto"              # GT標註格式檢測
    
    # === 類別映射 ===
    class_names: Dict[int, str] = field(default_factory=dict)  # 類別名稱映射


@dataclass
class UnifiedYOLOConfig:
    """統一YOLO配置 - 整合所有配置類"""
    
    model_config: ModelConfig = field(default_factory=ModelConfig)
    sahi_config: SAHIConfig = field(default_factory=SAHIConfig)
    processing_config: ProcessingConfig = field(default_factory=ProcessingConfig)
    annotation_config: AnnotationConfig = field(default_factory=AnnotationConfig)
    
    def to_dict(self) -> Dict:
        """轉換為字典格式"""
        return {
            'model_config': self.model_config.__dict__,
            'sahi_config': self.sahi_config.__dict__,
            'processing_config': self.processing_config.__dict__,
            'annotation_config': self.annotation_config.__dict__
        }
    
    def save_config(self, path: str):
        """保存配置到文件"""
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_config(cls, path: str) -> 'UnifiedYOLOConfig':
        """從文件加載配置"""
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        config = cls()
        config.model_config = ModelConfig(**data.get('model_config', {}))
        config.sahi_config = SAHIConfig(**data.get('sahi_config', {}))
        config.processing_config = ProcessingConfig(**data.get('processing_config', {}))
        config.annotation_config = AnnotationConfig(**data.get('annotation_config', {}))
        
        return config


class UnifiedYOLOInference:
    """統一YOLO推理類 - 整合所有優勢功能"""
    
    def __init__(self, config: UnifiedYOLOConfig):
        """初始化統一推理器"""
        self.config = config
        self.logger = logger
        
        # 初始化統計
        self.inference_stats = {
            "total_images": 0,
            "total_detections": 0,
            "class_counts": {},
            "processing_times": [],
            "failed_images": []
        }
        
        # 初始化組件
        self._init_models()
        self._init_visualization()
        
        logger.info("統一YOLO推理器初始化完成")
    
    def _init_models(self):
        """初始化模型組件"""
        logger.info("初始化模型組件...")
        
        # 這裡會整合Enhanced YOLO和Simple Tool的模型初始化邏輯
        # 實際實現時需要導入相應的模型類
        self.enhanced_yolo = None
        self.simple_tool = None
        
        logger.info("模型組件初始化完成")
    
    def _init_visualization(self):
        """初始化可視化組件"""
        logger.info("初始化可視化組件...")
        
        # 整合show_img.py的GT對比功能
        self.visualization_enabled = True
        
        logger.info("可視化組件初始化完成")
    
    def predict_single_image(self, 
                           image_path: str,
                           annotation_path: Optional[str] = None,
                           output_dir: str = "./output",
                           task_type: TaskType = TaskType.BOTH) -> Dict:
        """
        單張圖像推理 - 整合所有功能
        
        Args:
            image_path: 圖像路徑
            annotation_path: 標註路徑 (用於GT對比)
            output_dir: 輸出目錄
            task_type: 任務類型
            
        Returns:
            推理結果字典
        """
        start_time = time.time()
        image_name = Path(image_path).stem
        
        logger.info(f"處理圖像: {image_name}")
        
        # 1. 加載圖像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"無法加載圖像: {image_path}")

        # 2. 根據比例調整圖像尺寸
        if self.config.model_config.resize_ratio != 1.0:
            original_shape = image.shape
            new_width = int(image.shape[1] * self.config.model_config.resize_ratio)
            new_height = int(image.shape[0] * self.config.model_config.resize_ratio)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            self.logger.info(f"圖像尺寸已從 {original_shape[1]}x{original_shape[0]} 調整為 {new_width}x{new_height}")
        
        # 3. 執行推理 (整合Enhanced YOLO和Simple Tool邏輯)
        results = self._unified_inference(image, task_type, image.shape[1], image.shape[0])
        
        # 3. 後處理 (應用Simple Tool的智能過濾)
        if self.config.model_config.enable_class_filtering:
            results = self._apply_intelligent_filtering(results)
        
        # 4. GT對比處理
        gt_data = None
        if annotation_path and self.config.annotation_config.enable_gt_comparison:
            gt_data = self._load_ground_truth(annotation_path)
        
        # 5. 可視化
        self._create_unified_visualization(
            image, results, gt_data, 
            output_dir, image_name
        )
        
        # 6. 保存結果
        output_data = self._save_unified_results(
            results, output_dir, image_name, 
            image.shape[:2], image_path
        )
        
        # 7. 更新統計
        processing_time = time.time() - start_time
        self._update_statistics(results, processing_time)
        
        logger.info(f"圖像處理完成: {image_name} ({processing_time:.2f}s)")
        
        return output_data
    
    def _unified_inference(self, image: np.ndarray, task_type: TaskType, width: int, height: int) -> Dict:
        """統一推理邏輯 - 整合Enhanced YOLO和Simple Tool"""
        results = {}
        
        # Enhanced YOLO推理邏輯
        if self.enhanced_yolo:
            enhanced_results = self._enhanced_yolo_inference(image, task_type)
            results.update(enhanced_results)
        
        # Simple Tool推理邏輯
        if self.simple_tool:
            simple_results = self._simple_tool_inference(image)
            
            # 雙模型協同 (Simple Tool優勢)
            if self.config.model_config.enable_consensus:
                results = self._merge_consensus_results(results, simple_results)
            else:
                results.update(simple_results)
        
        return results
    
    def _enhanced_yolo_inference(self, image: np.ndarray, task_type: TaskType) -> Dict:
        """Enhanced YOLO推理邏輯"""
        # 實際實現時會調用Enhanced YOLO的推理方法
        return {"enhanced_detections": [], "enhanced_segmentations": []}
    
    def _simple_tool_inference(self, image: np.ndarray) -> Dict:
        """Simple Tool推理邏輯"""
        # 實際實現時會調用Simple Tool的推理方法
        return {"simple_detections": []}
    
    def _merge_consensus_results(self, enhanced_results: Dict, simple_results: Dict) -> Dict:
        """合併雙模型共識結果 (Simple Tool優勢功能)"""
        # 實現雙模型共識機制
        return enhanced_results  # 暫時返回enhanced結果
    
    def _apply_intelligent_filtering(self, results: Dict) -> Dict:
        """應用智能過濾 (Simple Tool優勢功能)"""
        # 實現類別間智能過濾 (linear vs alligator, linear vs joint)
        return results
    
    def _load_ground_truth(self, annotation_path: str) -> Optional[Dict]:
        """加載GT標註數據"""
        if not Path(annotation_path).exists():
            return None
        
        # 支援多種標註格式
        if annotation_path.endswith('.json'):
            with open(annotation_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        # 可以擴展支援其他格式
        
        return None
    
    def _create_unified_visualization(self, 
                                    image: np.ndarray,
                                    results: Dict,
                                    gt_data: Optional[Dict],
                                    output_dir: str,
                                    image_name: str):
        """創建統一可視化 - 整合所有可視化功能"""
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        vis_mode = self.config.processing_config.visualization_mode
        
        if vis_mode == VisualizationMode.STANDARD:
            self._create_standard_visualization(image, results, output_path, image_name)
        elif vis_mode == VisualizationMode.COMPARISON and gt_data:
            self._create_comparison_visualization(image, results, gt_data, output_path, image_name)
        elif vis_mode == VisualizationMode.COMBINED:
            self._create_combined_visualization(image, results, gt_data, output_path, image_name)
    
    def _create_standard_visualization(self, image, results, output_path, image_name):
        """創建標準可視化 (Enhanced YOLO風格)"""
        # 實現2x2網格: 原圖+檢測+分割+統計
        pass
    
    def _create_comparison_visualization(self, image, results, gt_data, output_path, image_name):
        """創建對比可視化 (整合show_img.py功能)"""
        # 實現1x3對比: 原圖+GT+預測
        pass
    
    def _create_combined_visualization(self, image, results, gt_data, output_path, image_name):
        """創建組合可視化 (完整6面板)"""
        # 實現2x3網格: 原圖+GT+預測+檢測+分割+統計
        pass
    
    import base64 # 新增導入

    def _save_unified_results(self, results, output_dir, image_name, image_shape, image_path):
        """保存統一結果 - 支援多種格式"""
        output_data = {
            'image_name': image_name,
            'image_path': image_path,
            'image_shape': image_shape,
            'results': results,
            'timestamp': time.time()
        }
        
        # 如果啟用base64圖像，則讀取圖像並編碼
        if self.config.processing_config.include_base64_image:
            try:
                with open(image_path, 'rb') as f:
                    image_bytes = f.read()
                output_data['imageData'] = base64.b64encode(image_bytes).decode('utf-8')
            except Exception as e:
                self.logger.warning(f"無法將圖像 {image_path} 編碼為base64: {e}")
                output_data['imageData'] = None
        
        # 保存JSON格式
        if self.config.processing_config.save_predictions:
            json_path = Path(output_dir) / f"{image_name}_results.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        return output_data
    
    def _update_statistics(self, results: Dict, processing_time: float):
        """更新統計資料"""
        self.inference_stats["total_images"] += 1
        self.inference_stats["processing_times"].append(processing_time)
        
        # 統計檢測數量
        total_detections = 0
        for key, value in results.items():
            if isinstance(value, list):
                total_detections += len(value)
        
        self.inference_stats["total_detections"] += total_detections
    
    def batch_predict(self,
                     input_dir: str,
                     output_dir: str,
                     task_type: TaskType = TaskType.BOTH) -> Dict:
        """
        批次推理 - 整合智能label計數功能
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 智能檔案掃描 (整合Enhanced YOLO邏輯)
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        annotation_extensions = ['.json', '.txt', '.xml']
        
        # 找到所有圖像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        # 找到所有標註文件
        annotation_files = []
        for ext in annotation_extensions:
            annotation_files.extend(input_path.glob(f"*{ext}"))
            annotation_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        has_labels = len(annotation_files) > 0
        
        logger.info(f"找到 {len(image_files)} 個圖像文件")
        if has_labels:
            logger.info(f"找到 {len(annotation_files)} 個標註文件")
        
        # 批次處理
        batch_results = {}
        failed_files = []
        
        for image_file in image_files:
            try:
                # 尋找對應標註文件
                annotation_file = None
                for ext in annotation_extensions:
                    ann_path = input_path / f"{image_file.stem}{ext}"
                    if ann_path.exists():
                        annotation_file = str(ann_path)
                        break
                
                # 執行推理
                result = self.predict_single_image(
                    image_path=str(image_file),
                    annotation_path=annotation_file,
                    output_dir=str(output_path),
                    task_type=task_type
                )
                
                batch_results[image_file.name] = result
                
            except Exception as e:
                logger.error(f"處理 {image_file.name} 失敗: {e}")
                failed_files.append(str(image_file))
        
        # 智能計數顯示 (整合enhanced_yolo_usage.py的修改)
        processed_count = len(batch_results)
        if self.config.processing_config.smart_label_counting and has_labels and processed_count > 0:
            display_count = processed_count // 2
            logger.info(f"批次處理完成! 處理了 {display_count} 組數據 (圖像+標註)")
        else:
            logger.info(f"批次處理完成! 處理了 {processed_count} 張圖像")
        
        # 保存批次統計
        batch_stats = {
            'total_processed': len(image_files),
            'successful': processed_count,
            'failed': len(failed_files),
            'failed_files': failed_files,
            'has_labels': has_labels,
            'inference_stats': self.inference_stats
        }
        
        stats_path = output_path / "unified_batch_statistics.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(batch_stats, f, indent=2, ensure_ascii=False)
        
        return batch_results
    
    def get_unified_stats(self) -> Dict:
        """獲取統一統計資料"""
        stats = self.inference_stats.copy()
        
        if stats["processing_times"]:
            stats["avg_processing_time"] = np.mean(stats["processing_times"])
            stats["total_processing_time"] = np.sum(stats["processing_times"])
        
        return stats


def create_unified_inference(config: UnifiedYOLOConfig) -> UnifiedYOLOInference:
    """創建統一推理器的工廠函數"""
    return UnifiedYOLOInference(config)


def main():
    """主函數 - 統一YOLO推理示例"""
    
    print("🚀 統一YOLO推理工具 (Unified YOLO Inference Tool)")
    print("=" * 80)
    print("整合Enhanced YOLO和Simple Model Labeling Tool的所有優勢功能")
    print("=" * 80)
    
    # ===================================================================
    # 📋 統一配置區域 - 整合所有參數
    # ===================================================================
    
    # 創建統一配置
    config = UnifiedYOLOConfig()
    
    # === 模型配置 ===
    config.model_config.detection_model_path = ""  # YOLO12檢測模型
    config.model_config.segmentation_model_path = r"D:\4_road_crack\best.pt"  # YOLO11分割模型
    config.model_config.device = "cuda"
    config.model_config.img_size = 640
    config.model_config.global_conf = 0.05
    config.model_config.iou_threshold = 0.45
    
    # === SAHI配置 (23個完整參數) ===
    config.sahi_config.enable_sahi = False
    config.sahi_config.slice_height = 512
    config.sahi_config.slice_width = 512
    config.sahi_config.overlap_height_ratio = 0.2
    config.sahi_config.overlap_width_ratio = 0.2
    
    # === 處理配置 ===
    config.processing_config.visualization_mode = VisualizationMode.COMBINED
    config.processing_config.smart_label_counting = True
    config.processing_config.target_classes = None  # 保存所有類別
    
    # === 標註配置 ===
    config.annotation_config.labelme_dir = r"D:\image\5_test_image_test\test1\label"
    config.annotation_config.enable_gt_comparison = True
    config.annotation_config.auto_generate_classes = True
    
    # === 輸入輸出路徑 ===
    input_path = r"D:\image\5_test_image_test\test1\image"
    output_path = r"D:\image\5_test_image_test\test1_unified_output"
    
    # ===================================================================
    # 🚀 執行統一推理
    # ===================================================================
    
    try:
        print("🔧 創建統一推理器...")
        inference = create_unified_inference(config)
        print("✅ 統一推理器創建成功")
        
        # 保存配置
        config_path = Path(output_path) / "unified_config.json"
        config.save_config(str(config_path))
        print(f"📄 配置已保存: {config_path}")
        
        print(f"📁 輸入: {input_path}")
        print(f"📁 輸出: {output_path}")
        
        # 檢查輸入路徑
        if not Path(input_path).exists():
            print(f"❌ 錯誤: 輸入路徑不存在: {input_path}")
            return
        
        print("\\n🚀 開始統一推理...")
        
        if Path(input_path).is_dir():
            # 批次處理
            print(f"📂 批次處理目錄: {input_path}")
            results = inference.batch_predict(
                input_dir=input_path,
                output_dir=output_path,
                task_type=TaskType.BOTH
            )
        else:
            # 單張圖像處理
            print(f"🖼️  處理單張圖像: {input_path}")
            results = inference.predict_single_image(
                image_path=input_path,
                output_dir=output_path,
                task_type=TaskType.BOTH
            )
        
        # 顯示統計信息
        stats = inference.get_unified_stats()
        print("\\n📊 統一統計信息:")
        print(f"  🎯 總圖像數: {stats['total_images']}")
        print(f"  🔍 總檢測數: {stats['total_detections']}")
        if 'avg_processing_time' in stats:
            print(f"  ⏱️  平均處理時間: {stats['avg_processing_time']:.2f}s")
        
        print(f"\\n🎉 統一推理完成! 結果保存至: {output_path}")
        
    except Exception as e:
        print(f"❌ 統一推理失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()


if __name__ == "__main__":
    main()