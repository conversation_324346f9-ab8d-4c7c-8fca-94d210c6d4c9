"""
基礎數據集類和共用工具
減少Dataset_read.py中的代碼重複
"""

import os
import numpy as np
import cv2
import json
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, List, Tuple, Union, Any
from pathlib import Path
import torch
from torch.utils.data import Dataset
from PIL import Image, ImageDraw, ImageFont
import albumentations as A
from albumentations.pytorch import ToTensorV2

logger = logging.getLogger(__name__)

class DatasetConfig:
    """數據集配置類"""
    
    def __init__(self,
                 data_dir: str,
                 size: int = 224,
                 split: str = 'train',
                 log_enabled: bool = True,
                 image_extensions: List[str] = None,
                 label_extensions: List[str] = None):
        """
        Args:
            data_dir: 數據目錄
            size: 圖像大小
            split: 數據分割
            log_enabled: 是否啟用日誌
            image_extensions: 支援的圖像格式
            label_extensions: 支援的標籤格式
        """
        self.data_dir = Path(data_dir)
        self.size = size
        self.split = split
        self.log_enabled = log_enabled
        self.image_extensions = image_extensions or ['.jpg', '.jpeg', '.png', '.bmp']
        self.label_extensions = label_extensions or ['.txt', '.json', '.xml']


class ImageProcessor:
    """圖像處理工具類"""
    
    @staticmethod
    def denormalize(img: Union[np.ndarray, torch.Tensor],
                   mean: Tuple[float, float, float] = (0.485, 0.456, 0.406),
                   std: Tuple[float, float, float] = (0.229, 0.224, 0.225)) -> np.ndarray:
        """
        反歸一化圖像
        
        Args:
            img: 輸入圖像
            mean: 歸一化均值
            std: 歸一化標準差
            
        Returns:
            反歸一化後的圖像 (0-255)
        """
        if isinstance(img, torch.Tensor):
            img = img.clone().detach().cpu().numpy()
        
        # 轉換為HWC格式
        if img.ndim == 3 and img.shape[0] == 3:
            img = np.transpose(img, (1, 2, 0))
        
        # 反歸一化
        mean = np.array(mean)
        std = np.array(std)
        img = img * std + mean
        img = np.clip(img, 0, 1)
        img = (img * 255).astype(np.uint8)
        
        return img
    
    @staticmethod
    def resize_with_aspect_ratio(image: np.ndarray, 
                               target_size: int,
                               interpolation: int = cv2.INTER_LINEAR) -> np.ndarray:
        """
        保持寬高比的圖像縮放
        
        Args:
            image: 輸入圖像
            target_size: 目標大小
            interpolation: 插值方法
            
        Returns:
            縮放後的圖像
        """
        h, w = image.shape[:2]
        scale = target_size / max(h, w)
        new_w, new_h = int(w * scale), int(h * scale)
        
        resized = cv2.resize(image, (new_w, new_h), interpolation=interpolation)
        
        # 填充到目標大小
        result = np.zeros((target_size, target_size, image.shape[2]), dtype=image.dtype)
        start_y = (target_size - new_h) // 2
        start_x = (target_size - new_w) // 2
        result[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        
        return result


class FontManager:
    """字體管理器 - 處理中文字體顯示"""
    
    _font_cache: Dict[Tuple[str, int], ImageFont.FreeTypeFont] = {}
    
    @classmethod
    def get_font(cls, font_size: int = 20) -> ImageFont.FreeTypeFont:
        """
        獲取支援中文的字體
        
        Args:
            font_size: 字體大小
            
        Returns:
            字體對象
        """
        cache_key = ('default', font_size)
        if cache_key in cls._font_cache:
            return cls._font_cache[cache_key]
        
        # 嘗試載入系統字體
        font_paths = [
            '/System/Library/Fonts/PingFang.ttc',         # macOS
            '/usr/share/fonts/truetype/arphic/uming.ttc', # Ubuntu
            'C:/Windows/Fonts/msyh.ttc',                  # Windows 微軟雅黑
            'C:/Windows/Fonts/mingliu.ttc',               # Windows 細明體
            'C:/Windows/Fonts/simsun.ttc',                # Windows 宋體
            'C:/Windows/Fonts/kaiu.ttf',                  # Windows 標楷體
            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
        ]
        
        font = None
        for path in font_paths:
            try:
                if os.path.exists(path):
                    font = ImageFont.truetype(path, font_size)
                    break
            except Exception:
                continue
        
        # 如果沒有找到字體，使用默認字體
        if font is None:
            try:
                font = ImageFont.load_default()
            except Exception:
                font = ImageFont.load_default()
        
        cls._font_cache[cache_key] = font
        return font
    
    @classmethod
    def draw_chinese_text(cls,
                         img: np.ndarray,
                         text: str,
                         position: Tuple[int, int],
                         font_size: int = 20,
                         color: Tuple[int, int, int] = (0, 0, 0)) -> np.ndarray:
        """
        在圖像上繪製中文文字
        
        Args:
            img: 輸入圖像
            text: 要繪製的文字
            position: 文字位置
            font_size: 字體大小
            color: 文字顏色
            
        Returns:
            繪製文字後的圖像
        """
        # 轉換為PIL格式
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 獲取字體
        font = cls.get_font(font_size)
        
        # 繪製文字
        draw.text(position, text, font=font, fill=color)
        
        # 轉換回OpenCV格式
        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)


class BaseVisionDataset(Dataset, ABC):
    """
    基礎視覺數據集類
    提供通用的數據集功能
    """
    
    def __init__(self, config: DatasetConfig, transform: Optional[A.Compose] = None):
        """
        Args:
            config: 數據集配置
            transform: 數據變換
        """
        self.config = config
        self.transform = transform
        self.use_tensor = self._check_tensor_transform()
        
        # 初始化文件列表
        self.image_files: List[str] = []
        self.label_files: List[str] = []
        
        # 用於視覺化的最後處理結果
        self.last_processed: Optional[Dict[str, Any]] = None
        
        if config.log_enabled:
            logger.info(f"初始化 {config.split} 數據集")
        
        # 子類需要實現具體的文件查找邏輯
        self._find_files()
        
        if config.log_enabled:
            logger.info(f"找到 {len(self.image_files)} 筆數據")
    
    def _check_tensor_transform(self) -> bool:
        """檢查是否使用張量轉換"""
        if self.transform is None:
            return False
        
        for transform in self.transform.transforms:
            if isinstance(transform, ToTensorV2):
                if self.config.log_enabled:
                    logger.info("檢測到ToTensorV2轉換")
                return True
        return False
    
    @abstractmethod
    def _find_files(self):
        """查找圖像和標籤文件 - 子類必須實現"""
        pass
    
    @abstractmethod
    def _load_label(self, label_path: str, image_shape: Tuple[int, int]) -> Tuple[np.ndarray, List, List]:
        """載入標籤 - 子類必須實現"""
        pass
    
    def __len__(self) -> int:
        return len(self.image_files)
    
    def __getitem__(self, idx: int) -> Tuple[Union[torch.Tensor, np.ndarray], 
                                           Union[torch.Tensor, np.ndarray], 
                                           str]:
        """
        獲取數據項
        
        Args:
            idx: 索引
            
        Returns:
            (image, mask/label, filename)
        """
        if self.config.log_enabled:
            logger.debug(f"讀取第 {idx} 筆數據")
        
        # 獲取文件路徑
        image_path = self.image_files[idx]
        label_path = self.label_files[idx] if idx < len(self.label_files) else None
        filename = os.path.basename(image_path)
        
        # 讀取圖像
        image = np.array(Image.open(image_path).convert('RGB'))
        original_height, original_width = image.shape[:2]
        
        # 讀取標籤
        if label_path and os.path.exists(label_path):
            mask, boxes, labels = self._load_label(label_path, (original_width, original_height))
        else:
            mask = np.zeros((original_height, original_width), dtype=np.uint8)
            boxes, labels = [], []
        
        # 保存原始數據用於視覺化
        self.last_processed = {
            'image': image.copy(),
            'mask': mask.copy(),
            'original_image': image.copy(),
            'boxes': boxes.copy() if boxes else [],
            'labels': labels.copy() if labels else [],
            'filename': filename
        }
        
        # 應用變換
        if self.transform:
            image, mask, boxes, labels = self._apply_transform(image, mask, boxes, labels)
        else:
            image, mask = self._default_transform(image, mask)
        
        # 更新處理後的數據
        self._update_processed_data(image, mask, boxes, labels)
        
        return image, mask, filename
    
    def _apply_transform(self, 
                        image: np.ndarray, 
                        mask: np.ndarray,
                        boxes: List,
                        labels: List) -> Tuple:
        """應用數據變換"""
        try:
            if boxes and len(boxes) > 0:
                # 有邊界框的變換
                transformed = self.transform(
                    image=image,
                    mask=mask,
                    bboxes=boxes,
                    category_ids=labels
                )
                return (
                    transformed["image"],
                    transformed["mask"],
                    transformed.get("bboxes", []),
                    transformed.get("category_ids", [])
                )
            else:
                # 只有圖像和掩碼的變換
                transformed = self.transform(image=image, mask=mask)
                return transformed["image"], transformed["mask"], [], []
        
        except Exception as e:
            logger.error(f"數據變換失敗: {e}")
            return self._default_transform(image, mask) + (boxes, labels)
    
    def _default_transform(self, image: np.ndarray, mask: np.ndarray) -> Tuple:
        """默認變換（調整大小並轉換為張量）"""
        # 調整大小
        resized_image = cv2.resize(image, (self.config.size, self.config.size))
        resized_mask = cv2.resize(mask, (self.config.size, self.config.size), 
                                interpolation=cv2.INTER_NEAREST)
        
        # 轉換為張量
        image_tensor = torch.from_numpy(resized_image.transpose(2, 0, 1)).float() / 255.0
        mask_tensor = torch.from_numpy(resized_mask).long()
        
        return image_tensor, mask_tensor
    
    def _update_processed_data(self, image, mask, boxes, labels):
        """更新處理後的數據"""
        if self.use_tensor:
            # 張量轉換為NumPy用於視覺化
            if isinstance(image, torch.Tensor):
                self.last_processed['processed_image'] = image.permute(1, 2, 0).cpu().numpy()
            if isinstance(mask, torch.Tensor):
                self.last_processed['processed_mask'] = mask.cpu().numpy()
        else:
            self.last_processed['processed_image'] = image
            self.last_processed['processed_mask'] = mask
        
        self.last_processed['processed_boxes'] = boxes
        self.last_processed['processed_labels'] = labels
    
    def visualize(self, 
                 idx: int,
                 figsize: Tuple[int, int] = (15, 5),
                 class_names: Optional[Dict[int, str]] = None) -> None:
        """
        視覺化數據項
        
        Args:
            idx: 數據索引
            figsize: 圖形大小
            class_names: 類別名稱映射
        """
        import matplotlib.pyplot as plt
        
        # 獲取數據
        if self.last_processed is None or idx != getattr(self, '_last_visualized_idx', None):
            self[idx]
            self._last_visualized_idx = idx
        
        # 獲取圖像和掩碼數據
        original_image = self.last_processed['original_image']
        processed_image = self.last_processed.get('processed_image', original_image)
        mask = self.last_processed.get('processed_mask', self.last_processed['mask'])
        filename = self.last_processed['filename']
        
        # 處理張量格式
        if isinstance(processed_image, torch.Tensor):
            processed_image = processed_image.permute(1, 2, 0).cpu().numpy()
        if isinstance(mask, torch.Tensor):
            mask = mask.cpu().numpy()
        
        # 反歸一化
        processed_image = ImageProcessor.denormalize(processed_image)
        
        # 創建子圖
        fig, axes = plt.subplots(1, 3, figsize=figsize)
        
        # 原始圖像
        axes[0].imshow(original_image)
        axes[0].set_title(f'原始圖像: {filename}')
        axes[0].axis('off')
        
        # 標籤掩碼
        unique_classes = np.unique(mask)
        unique_classes = [c for c in unique_classes if c > 0]
        
        if unique_classes:
            # 創建彩色掩碼
            colored_mask = self._create_colored_mask(mask, unique_classes)
            axes[1].imshow(colored_mask)
            axes[1].set_title('標籤掩碼')
        else:
            axes[1].imshow(np.zeros_like(processed_image))
            axes[1].set_title('無標籤')
        axes[1].axis('off')
        
        # 疊加圖像
        overlap = self._create_overlay_image(processed_image, mask, unique_classes, class_names)
        axes[2].imshow(overlap)
        axes[2].set_title('標籤疊加')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def _create_colored_mask(self, mask: np.ndarray, unique_classes: List[int]) -> np.ndarray:
        """創建彩色掩碼"""
        np.random.seed(42)  # 保持顏色一致
        colored_mask = np.zeros((*mask.shape, 3), dtype=np.uint8)
        
        for cls in unique_classes:
            color = np.random.randint(0, 255, 3)
            colored_mask[mask == cls] = color
        
        return colored_mask
    
    def _create_overlay_image(self, 
                            image: np.ndarray,
                            mask: np.ndarray,
                            unique_classes: List[int],
                            class_names: Optional[Dict[int, str]]) -> np.ndarray:
        """創建疊加圖像"""
        overlay = image.copy()
        
        if not unique_classes:
            return overlay
        
        # 創建彩色掩碼
        colored_mask = self._create_colored_mask(mask, unique_classes)
        
        # 疊加
        alpha = 0.5
        overlay = cv2.addWeighted(overlay, 1, colored_mask, alpha, 0)
        
        # 添加圖例
        if unique_classes:
            overlay = self._add_legend(overlay, unique_classes, class_names)
        
        return overlay
    
    def _add_legend(self, 
                   image: np.ndarray,
                   unique_classes: List[int],
                   class_names: Optional[Dict[int, str]]) -> np.ndarray:
        """添加圖例"""
        np.random.seed(42)
        legend_x = image.shape[1] - 150
        legend_y = 20
        legend_height = 25 * len(unique_classes)
        
        # 繪製背景
        cv2.rectangle(image, (legend_x - 10, legend_y - 10),
                     (legend_x + 140, legend_y + legend_height),
                     (255, 255, 255), -1)
        cv2.rectangle(image, (legend_x - 10, legend_y - 10),
                     (legend_x + 140, legend_y + legend_height),
                     (0, 0, 0), 1)
        
        # 繪製圖例項目
        for i, cls in enumerate(sorted(unique_classes)):
            # 顏色方塊
            color = np.random.randint(0, 255, 3).tolist()
            cv2.rectangle(image, (legend_x, legend_y + i * 25),
                         (legend_x + 20, legend_y + i * 25 + 20),
                         color, -1)
            
            # 類別名稱
            if class_names and cls in class_names:
                class_text = class_names[cls]
            else:
                class_text = f"類別 {cls}"
            
            # 使用字體管理器繪製中文
            image = FontManager.draw_chinese_text(
                image, class_text,
                (legend_x + 30, legend_y + i * 25),
                font_size=16
            )
        
        return image


class FileFinder:
    """文件查找工具類"""
    
    @staticmethod
    def find_image_label_pairs(data_dir: Path,
                              split: str,
                              image_extensions: List[str],
                              label_extensions: List[str]) -> Tuple[List[str], List[str]]:
        """
        查找圖像和標籤文件對
        
        Args:
            data_dir: 數據目錄
            split: 數據分割
            image_extensions: 圖像格式
            label_extensions: 標籤格式
            
        Returns:
            (image_files, label_files)
        """
        image_files = []
        label_files = []
        
        # 可能的圖像目錄
        image_dirs = [
            data_dir / 'images' / split,
            data_dir / 'JPEGImages',
            data_dir / split,
            data_dir
        ]
        
        # 可能的標籤目錄
        label_dirs = [
            data_dir / 'labels' / split,
            data_dir / 'labels',
            data_dir / 'Annotations',
            data_dir / split,
            data_dir
        ]
        
        # 檢查分割文件
        split_file = FileFinder._find_split_file(data_dir, split)
        
        if split_file:
            image_files, label_files = FileFinder._load_from_split_file(
                split_file, image_dirs, label_dirs, image_extensions, label_extensions
            )
        else:
            image_files, label_files = FileFinder._scan_directories(
                image_dirs, label_dirs, image_extensions, label_extensions
            )
        
        return image_files, label_files
    
    @staticmethod
    def _find_split_file(data_dir: Path, split: str) -> Optional[Path]:
        """查找分割文件"""
        possible_paths = [
            data_dir / f'images/{split}.txt',
            data_dir / f'ImageSets/Main/{split}.txt',
            data_dir / f'{split}.txt'
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        return None
    
    @staticmethod
    def _load_from_split_file(split_file: Path,
                             image_dirs: List[Path],
                             label_dirs: List[Path],
                             image_extensions: List[str],
                             label_extensions: List[str]) -> Tuple[List[str], List[str]]:
        """從分割文件載入文件列表"""
        image_files = []
        label_files = []
        
        with open(split_file, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            img_name = line.strip()
            
            # 查找圖像文件
            img_file = FileFinder._find_file(img_name, image_dirs, image_extensions)
            label_file = FileFinder._find_file(img_name, label_dirs, label_extensions)
            
            if img_file and label_file:
                image_files.append(str(img_file))
                label_files.append(str(label_file))
        
        return image_files, label_files
    
    @staticmethod
    def _scan_directories(image_dirs: List[Path],
                         label_dirs: List[Path],
                         image_extensions: List[str],
                         label_extensions: List[str]) -> Tuple[List[str], List[str]]:
        """掃描目錄查找文件"""
        image_files = []
        label_files = []
        
        for image_dir in image_dirs:
            if not image_dir.exists():
                continue
            
            for file_path in image_dir.iterdir():
                if file_path.suffix.lower() in image_extensions:
                    base_name = file_path.stem
                    
                    # 查找對應的標籤文件
                    label_file = None
                    for label_dir in label_dirs:
                        if not label_dir.exists():
                            continue
                        
                        for ext in label_extensions:
                            possible_label = label_dir / f"{base_name}{ext}"
                            if possible_label.exists():
                                label_file = str(possible_label)
                                break
                        
                        if label_file:
                            break
                    
                    if label_file:
                        image_files.append(str(file_path))
                        label_files.append(label_file)
        
        return image_files, label_files
    
    @staticmethod
    def _find_file(base_name: str, 
                   dirs: List[Path], 
                   extensions: List[str]) -> Optional[Path]:
        """在多個目錄中查找文件"""
        for dir_path in dirs:
            if not dir_path.exists():
                continue
            
            for ext in extensions:
                file_path = dir_path / f"{base_name}{ext}"
                if file_path.exists():
                    return file_path
        
        return None