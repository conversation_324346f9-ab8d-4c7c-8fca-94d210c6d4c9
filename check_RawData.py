from typing import Dict, List
import json
import os
import sys
from collections import Counter, defaultdict
import logging
import random
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np
from PIL import Image, ImageDraw, ImageFont


class LabelmeConverter:
    def __init__(self, folder_path: str, merge_rules: Dict[str, List[str]],
                 class_mapping: Dict[str, str], simulate: bool = True,
                 save_mode: str = "overwrite", remove_image_data: bool = True):
        self.folder_path = folder_path
        self.merge_rules = merge_rules
        self.class_mapping = class_mapping
        self.simulate = simulate
        self.save_mode = save_mode
        self.remove_image_data = remove_image_data

        self.reverse_map = {old: new for new, olds in merge_rules.items() for old in olds}
        self.target_folder = self.folder_path.rstrip("\\/") + "_modified" if save_mode == "copy" else self.folder_path
        if not simulate and save_mode == "copy":
            os.makedirs(self.target_folder, exist_ok=True)

        log_file_path = os.path.join(self.target_folder, "labelme_conversion_log.txt")
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s",
            handlers=[
                logging.FileHandler(log_file_path, mode='w', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

        self.original_class_counter = Counter()
        self.updated_class_counter = Counter()
        self.changed_class_counter = Counter()
        self.unmapped_labels = Counter()
        self.unknown_labels = set()

        self.total_count = 0
        self.modified_count = 0
        self.deleted_files = []
        self.image_data_removed_files = []
        self.image_path_corrected_files = []

        self.single_class_image_counter = Counter()
        self.multi_class_image_counter = Counter()
        self.multi_class_combinations = Counter()
        self.multi_class_details = defaultdict(dict)
        self.single_class_total = 0
        self.multi_class_total = 0

        self.label_to_files = defaultdict(list)

        # 新增：用來記錄被刪除的空標註 (shapes 為空) 的 JSON
        self.empty_shapes_deleted = []

    def preview_labels(self):
        label_counter = Counter()
        
        # 1. 統計各 label -> 哪些 JSON 檔
        for file in os.listdir(self.folder_path):
            if file.endswith('.json'):
                try:
                    with open(os.path.join(self.folder_path, file), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        for shape in data.get("shapes", []):
                            label = shape.get("label", "")
                            label_counter[label] += 1
                            self.label_to_files[label].append(file)
                except:
                    continue
        
        print("\n🔍 預掃描類別分佈：")
        for label, count in label_counter.most_common():
            zh = self.class_mapping.get(label, "")
            print(f"- {label}{f'（{zh}）' if zh else ''}：{count} 筆")

        # 2. 在這裡加入互動：輸入 OK 繼續處理，或輸入 label 顯示 3 張圖
        while True:
            resp = input("\n👉 輸入 OK 繼續處理，或輸入某個類別名稱查看 3 張隨機圖像：").strip()
            if resp.lower() == "ok":
                break
            elif resp in self.label_to_files:
                sample_files = random.sample(
                    self.label_to_files[resp],
                    min(5, len(self.label_to_files[resp]))
                )
                print(f"📷 顯示類別 '{resp}' 的隨機圖像樣本：")

                for fname in sample_files:
                    json_path = os.path.join(self.folder_path, fname)
                    if not os.path.exists(json_path):
                        print(f"❌ 找不到對應 JSON：{json_path}")
                        continue

                    # 讀取 JSON
                    try:
                        with open(json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                    except Exception as e:
                        print(f"❌ 無法讀取 JSON：{json_path}, 錯誤：{e}")
                        continue

                    # 尋找對應圖片
                    base = os.path.splitext(fname)[0]
                    img_path = None
                    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                        potential_path = os.path.join(self.folder_path, base + ext)
                        if os.path.exists(potential_path):
                            img_path = potential_path
                            break

                    if not img_path:
                        print(f"❌ 找不到對應圖片：{base}.*")
                        continue

                    # 用 PIL 讀圖
                    try:
                        img_pil = Image.open(img_path).convert("RGB")
                    except Exception as e:
                        print(f"❌ PIL 無法讀取：{img_path}, 錯誤：{e}")
                        continue

                    # 建立可繪製物件
                    draw = ImageDraw.Draw(img_pil)

                    # 疊加該 JSON 檔中所有 shape 的外框與標籤
                    for shape in data.get("shapes", []):
                        shape_label = shape.get("label", "")
                        points = shape.get("points", [])
                        polygon_points = [(p[0], p[1]) for p in points]

                        # 隨機顏色
                        color = (
                            random.randint(0, 255),
                            random.randint(0, 255),
                            random.randint(0, 255)
                        )
                        # 畫出 polygon 的邊框
                        draw.polygon(polygon_points, outline=color, width=10)
                        
                        # 在多邊形中心放文字（英文字，字體大一點）
                        if polygon_points:
                            # 載入英文字型
                            font_size = 40
                            font = ImageFont.truetype("arial.ttf", font_size)
                        
                            # 計算中心點
                            avg_x = sum(p[0] for p in polygon_points) / len(polygon_points)
                            avg_y = sum(p[1] for p in polygon_points) / len(polygon_points)
                        
                            # 計算文字寬高（使用 textbbox）
                            bbox = draw.textbbox((0, 0), shape_label, font=font)
                            text_w = bbox[2] - bbox[0]
                            text_h = bbox[3] - bbox[1]
                        
                            # 文字置中顯示
                            text_pos = (avg_x - text_w / 2, avg_y - text_h / 2)
                            draw.text(text_pos, shape_label, font=font, fill=color)

                    
                    # 顯示圖像
                    plt.figure(figsize=(16, 12))
                    plt.imshow(img_pil)
                    plt.title(f"{fname} (Overlay by PIL)")
                    plt.axis("off")
                    plt.show()

            else:
                print("⚠️ 類別不存在，請重新輸入或輸入 OK 繼續。")
        if resp == "end":
            print("⛔ 已中止，請確認類別設定")
            sys.exit()

    def convert(self):
        self.before_json_count = len([f for f in os.listdir(self.folder_path) if f.endswith('.json')])
        self.before_img_count = len([f for f in os.listdir(self.folder_path) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))])

        for file in os.listdir(self.folder_path):
            if not file.endswith('.json'):
                continue

            self.total_count += 1
            full_path = os.path.join(self.folder_path, file)
            json_basename = os.path.splitext(file)[0]

            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            except Exception as e:
                self.logger.error(f"❌ 無法讀取 {file}：{e}")
                self._delete_pair(file, json_basename)
                continue

            if not isinstance(data.get("shapes"), list) or not all(k in data for k in ["imagePath", "imageHeight", "imageWidth"]):
                self.logger.warning(f"❌ 結構異常，刪除：{file}")
                self._delete_pair(file, json_basename)
                continue

            # 新增判斷：若 shapes 為空則視為無標註
            if isinstance(data.get("shapes"), list) and len(data["shapes"]) == 0:
                self.logger.warning(f"⚠️ 空標註，刪除：{file}")
                self.empty_shapes_deleted.append(file)
                self._delete_pair(file, json_basename)
                continue

            image_path = data.get("imagePath", "")
            true_image_name = None
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                potential_image_path = os.path.join(self.folder_path, json_basename + ext)
                if os.path.exists(potential_image_path):
                    # 檢查檔案是否為 0KB
                    if os.path.getsize(potential_image_path) == 0:
                        self.logger.warning(f"⚠️ 檢測到壞圖（0KB）：{potential_image_path}")
                        self._delete_pair(file, json_basename)
                        true_image_name = None
                        break
                    # 使用 PIL 驗證圖片是否為有效格式
                    try:
                        with Image.open(potential_image_path) as img:
                            img.verify()
                    except Exception as e:
                        self.logger.warning(f"⚠️ 檢測到壞圖（PIL 無法讀取）：{potential_image_path}，錯誤：{e}")
                        self._delete_pair(file, json_basename)
                        true_image_name = None
                        break
            
                    true_image_name = os.path.basename(potential_image_path)
                    break


            if true_image_name:
                if image_path != true_image_name:
                    data["imagePath"] = true_image_name
                    self.image_path_corrected_files.append(f"{file} -> {true_image_name}")
            else:
                self._delete_pair(file, json_basename)
                continue

            changed = False
            class_counter = Counter()
            for shape in data.get("shapes", []):
                label = shape.get("label", "")
                self.original_class_counter[label] += 1
                class_counter[label] += 1

                if label in self.reverse_map:
                    new_label = self.reverse_map[label]
                    shape["label"] = new_label
                    changed = True
                    self.changed_class_counter[f"{label} ➜ {new_label}"] += 1
                    self.updated_class_counter[new_label] += 1
                elif label in self.merge_rules:
                    self.updated_class_counter[label] += 1
                else:
                    self.updated_class_counter[label] += 1
                    self.unmapped_labels[label] += 1
                    if label not in self.class_mapping:
                        self.unknown_labels.add(label)

            unique_classes = sorted(set(class_counter.elements()))
            if len(unique_classes) == 1:
                self.single_class_total += 1
                self.single_class_image_counter[unique_classes[0]] += 1
            elif len(unique_classes) > 1:
                self.multi_class_total += 1
                comb_key = tuple(sorted(set(class_counter)))
                self.multi_class_combinations[comb_key] += 1
                self.multi_class_image_counter.update(comb_key)
                self.multi_class_details[file] = dict(class_counter)

            if changed:
                self.modified_count += 1

            if self.remove_image_data and "imageData" in data:
                del data["imageData"]
                self.image_data_removed_files.append(file)

            if not self.simulate:
                save_path = os.path.join(self.target_folder, file)
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

        self._final_report()

    def _delete_pair(self, json_file, json_basename):
        self.deleted_files.append(json_file)
        if not self.simulate:
            try:
                os.remove(os.path.join(self.folder_path, json_file))
            except:
                pass
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                try:
                    os.remove(os.path.join(self.folder_path, json_basename + ext))
                except:
                    pass

    def _final_report(self):
        self.logger.info("\n====== Labelme 類別轉換報告 ======")
        self.logger.info(f"📁 處理路徑：{self.folder_path}")
        self.logger.info(f"📦 JSON 數量：{self.total_count}")
        self.logger.info(f"🛠️ 修改類別標籤數：{self.modified_count}")
        self.logger.info(f"🗑️ 刪除 JSON 數（含結構錯誤/無對應圖片）：{len(self.deleted_files)}")
        self.logger.info(f"🈳 空標註被刪除數量（shapes 為空）：{len(self.empty_shapes_deleted)}")
        self.logger.info(f"⚙️ 模擬模式：{'是' if self.simulate else '否'}")
        self.logger.info(f"🧹 自動清除 imageData：{'是' if self.remove_image_data else '否'}")

        for msg in self.image_path_corrected_files:
            self.logger.info(f"✏️ 修正 imagePath：{msg}")

        for desc, count in self.changed_class_counter.items():
            self.logger.info(f"🔁 {desc}：{count} 筆")

        for label, count in self.original_class_counter.items():
            self.logger.info(f"📊 原始類別 - {label}：{count} 筆")

        for label, count in self.updated_class_counter.items():
            zh = self.class_mapping.get(label, "")
            zh_str = f"（{zh}）" if zh else ""
            self.logger.info(f"📊 最終類別 - {label}{zh_str}：{count} 筆")

        self.logger.info(f"📊 單類別圖像：{self.single_class_total} 張")
        for label, count in self.single_class_image_counter.items():
            self.logger.info(f"  - {label}：{count} 張")

        self.logger.info(f"📊 多類別圖像：{self.multi_class_total} 張")
        for combo, count in self.multi_class_combinations.items():
            combo_str = ', '.join(combo)
            self.logger.info(f"  - [{combo_str}]：{count} 張")

        for filename, class_count in self.multi_class_details.items():
            detail_str = ', '.join([f"{k}×{v}" for k, v in class_count.items()])
            self.logger.info(f"📄 {filename}：{detail_str}")

        if self.unmapped_labels:
            for label, count in self.unmapped_labels.items():
                self.logger.warning(f"⚠️ 未在 merge_rules 中的類別：{label}：{count} 筆")

        if self.unknown_labels:
            for label in sorted(self.unknown_labels):
                self.logger.error(f"🚨 不在 class_mapping 中的未知類別：{label}")

        self.logger.info("✅ 處理完成，Log 已自動寫入")


if __name__ == '__main__':
    class_mapping = {
        "lane_line_linear": "白線裂縫",
        "expansion_joint": "伸縮縫",
        "joint": "路面接縫",
        "linear_crack": "裂縫",
        "Alligator_crack": "龜裂",
        "rutting": "車轍",
        "slippage": "表層滑動",
        "corrugation": "波浪紋",
        "upheaval": "隆起",
        "depression": "沉陷",
        "potholes": "坑洞",
        "bleeding": "冒油",
        "separation": "薄層剝離",
        "patch": "補綻",
        "side-shoving": "側擠",
        "faulting": "段差",
        "deformation": "變形",
        "manhole_ditch": "人孔蓋或排水溝",
        "dirt_spot":"污垢",
        "other": "其它"
    }

    merge_rules = {
        "deformation": ["side-shoving", "faulting", "slippage", "corrugation", "upheaval", "rutting", "depression", "bleeding"],
        "linear_crack": ["lane_line_linear", "Horizontal_line_crack", "crack_lane_line","crack_vertical","crack_horizontal","crack","crack_light","crack_lane_line"],
        "patch": ["patch_circle_lane", "patch_circle", "patch_square_lane", "patch_square"],
        "manhole_ditch": ["manhole_sides", "manhole_circle", "manhole_square", "manhole_water", "manhole_square_small","manhole",],
        "dirt_spot": ["dirt_linear"],
        "Alligator_crack": ["alligator_crack_heavy", "alligator_crack_light","alligator_crack"],
    }

    converter = LabelmeConverter(
        folder_path=r"D:\路面缺陷資料集\公司内部資料集\other_dataset",
        merge_rules=merge_rules,
        class_mapping=class_mapping,
        simulate=False, #True 為模擬
        save_mode="overwrite",
        remove_image_data=True
    )

    converter.preview_labels()
    converter.convert()
