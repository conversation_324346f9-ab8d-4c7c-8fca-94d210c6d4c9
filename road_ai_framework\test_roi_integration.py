#!/usr/bin/env python3
"""
測試ROI功能整合
驗證一般推理模式下的ROI裁切和LabelMe座標轉換功能
"""

import sys
from pathlib import Path
import numpy as np
import cv2

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_roi_configuration():
    """測試ROI配置是否正確"""
    print("🔧 測試ROI配置...")
    
    try:
        from models.inference.config_manager import UnifiedYOLOConfigManager
        
        # 創建配置管理器
        config_manager = UnifiedYOLOConfigManager()
        
        # 檢查SAHI配置中的ROI參數
        sahi_config = config_manager.sahi
        print(f"   enable_sahi_roi: {sahi_config.enable_sahi_roi}")
        print(f"   roi_top_ratio: {sahi_config.roi_top_ratio}")
        print(f"   roi_bottom_ratio: {sahi_config.roi_bottom_ratio}")
        print(f"   roi_left_ratio: {sahi_config.roi_left_ratio}")
        print(f"   roi_right_ratio: {sahi_config.roi_right_ratio}")
        
        return True
    except Exception as e:
        print(f"❌ 配置測試失敗: {e}")
        return False

def test_roi_functions():
    """測試ROI函數實現"""
    print("\n🎯 測試ROI函數實現...")
    
    try:
        from models.inference.unified_yolo_inference import UnifiedYOLOInference
        from models.inference.config_manager import UnifiedYOLOConfigManager
        
        # 創建測試配置
        config_manager = UnifiedYOLOConfigManager()
        config_manager.sahi.enable_sahi_roi = True
        config_manager.sahi.roi_top_ratio = 3.0
        config_manager.sahi.roi_bottom_ratio = 2.8
        config_manager.sahi.roi_left_ratio = 5.0
        config_manager.sahi.roi_right_ratio = 5.0
        
        # 創建推理實例（不加載模型，只測試ROI函數）
        inference = UnifiedYOLOInference(config_manager)
        
        # 測試ROI檢查函數
        roi_enabled = inference._check_roi_enabled()
        print(f"   ✅ _check_roi_enabled(): {roi_enabled}")
        
        # 創建測試圖像
        test_image = np.zeros((1000, 1500, 3), dtype=np.uint8)  # 1500x1000的測試圖像
        
        # 測試ROI裁切函數
        cropped_image, roi_offset = inference._apply_roi_cropping(test_image)
        
        print(f"   ✅ 原圖尺寸: {test_image.shape[:2]}")
        print(f"   ✅ 裁切後尺寸: {cropped_image.shape[:2]}")
        print(f"   ✅ ROI偏移: {roi_offset}")
        
        # 驗證裁切結果
        if roi_offset and cropped_image.shape[:2] != test_image.shape[:2]:
            print(f"   ✅ ROI裁切成功，偏移量: {roi_offset}")
            return True
        else:
            print(f"   ❌ ROI裁切未生效")
            return False
            
    except Exception as e:
        print(f"❌ ROI函數測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_from_params():
    """測試參數配置創建"""
    print("\n⚙️ 測試參數配置創建...")
    
    try:
        # 模擬run_unified_yolo.py中的參數
        import sys
        sys.path.append('/mnt/d/99_AI_model/road_ai_framework')
        
        # 設置模擬參數
        global enable_roi_processing, roi_top_ratio, roi_bottom_ratio, roi_left_ratio, roi_right_ratio
        enable_roi_processing = True
        roi_top_ratio = 3.0
        roi_bottom_ratio = 2.8  
        roi_left_ratio = 5.0
        roi_right_ratio = 5.0
        
        # 簡化版本的create_config_from_params
        from models.inference.config_manager import UnifiedYOLOConfigManager
        config_manager = UnifiedYOLOConfigManager()
        
        # 設置ROI配置
        config_manager.sahi.enable_sahi_roi = enable_roi_processing
        config_manager.sahi.roi_top_ratio = roi_top_ratio
        config_manager.sahi.roi_bottom_ratio = roi_bottom_ratio
        config_manager.sahi.roi_left_ratio = roi_left_ratio
        config_manager.sahi.roi_right_ratio = roi_right_ratio
        
        print(f"   ✅ ROI配置設置成功:")
        print(f"      enable_sahi_roi: {config_manager.sahi.enable_sahi_roi}")
        print(f"      roi_top_ratio: {config_manager.sahi.roi_top_ratio}")
        print(f"      roi_bottom_ratio: {config_manager.sahi.roi_bottom_ratio}")
        print(f"      roi_left_ratio: {config_manager.sahi.roi_left_ratio}")
        print(f"      roi_right_ratio: {config_manager.sahi.roi_right_ratio}")
        
        return True
        
    except Exception as e:
        print(f"❌ 參數配置測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_roi_coordinate_calculation():
    """測試ROI座標計算邏輯"""
    print("\n📐 測試ROI座標計算邏輯...")
    
    try:
        # 模擬原圖尺寸
        h, w = 1000, 1500
        
        # ROI參數
        roi_top_ratio = 3.0
        roi_bottom_ratio = 2.8
        roi_left_ratio = 5.0
        roi_right_ratio = 5.0
        
        # 計算ROI邊界（與unified_yolo_inference.py中相同的邏輯）
        crop_factor_top = (roi_top_ratio - 1) / 8
        crop_factor_bottom = (roi_bottom_ratio - 1) / 8
        crop_factor_left = (roi_left_ratio - 1) / 8
        crop_factor_right = (roi_right_ratio - 1) / 8
        
        y1_roi = int(h * crop_factor_top)
        y2_roi = int(h - h * crop_factor_bottom)
        x1_roi = int(w * crop_factor_left)
        x2_roi = int(w - w * crop_factor_right)
        
        print(f"   ✅ 原圖尺寸: {w}x{h}")
        print(f"   ✅ ROI邊界: ({x1_roi}, {y1_roi}) -> ({x2_roi}, {y2_roi})")
        print(f"   ✅ ROI尺寸: {x2_roi-x1_roi}x{y2_roi-y1_roi}")
        print(f"   ✅ 裁切比例: {(x2_roi-x1_roi)/w:.2%} x {(y2_roi-y1_roi)/h:.2%}")
        
        # 驗證ROI是否有效
        if y2_roi > y1_roi and x2_roi > x1_roi:
            print(f"   ✅ ROI座標計算正確")
            return True
        else:
            print(f"   ❌ ROI座標計算錯誤")
            return False
            
    except Exception as e:
        print(f"❌ ROI座標計算測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 ROI功能整合測試")
    print("=" * 60)
    
    tests = [
        test_roi_configuration,
        test_roi_functions,
        test_config_from_params,
        test_roi_coordinate_calculation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 測試總結:")
    print(f"   總測試數: {len(tests)}")
    print(f"   通過測試: {sum(results)}")
    print(f"   失敗測試: {len(tests) - sum(results)}")
    
    if all(results):
        print("\n🎉 所有測試通過！ROI功能整合成功")
        print("\n✅ ROI功能現在支援:")
        print("   - 一般推理模式下的ROI裁切")
        print("   - 座標轉換和偏移處理")
        print("   - LabelMe JSON輸出ROI座標系")
        print("   - 配置參數正確傳遞")
    else:
        print("\n❌ 部分測試失敗，需要進一步修復")
    
    print("=" * 60)

if __name__ == "__main__":
    main()