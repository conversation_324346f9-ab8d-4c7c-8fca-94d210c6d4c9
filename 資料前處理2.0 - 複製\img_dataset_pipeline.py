import os
import argparse
import json
import logging
import sys
from pathlib import Path
from datetime import datetime

# 導入所有模組
from annotation_converter import AnnotationConverter
from annotation_editor import AnnotationEditor
from dataset_divider import DatasetDivider
from img_augmenter import ImageAugmenter

import matplotlib.pyplot as plt
"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False


def setup_logging(log_path, level=logging.INFO):
    """設置日誌記錄"""
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_path, mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ],
        force=True
    )
    return logging.getLogger("ImageDatasetProcessor")


def parse_resize_param(resize_str):
    """解析resize參數"""
    if not resize_str:
        return None

    # 嘗試解析為 寬x高 格式
    if 'x' in resize_str:
        try:
            w, h = map(int, resize_str.split('x'))
            return (w, h)
        except:
            pass

    # 嘗試解析為浮點數縮放比例
    try:
        scale = float(resize_str)
        if 0 < scale < 1:
            return scale
    except:
        pass

    # 無法解析
    return None


def load_config(config_path):
    """加載配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"無法加載配置文件: {e}")
        return {}


def save_config(config, output_path):
    """保存配置文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"無法保存配置文件: {e}")
        return False


def step1_convert_annotations(config, output_dir, logger):
    """步驟1: 標籤格式轉換與圖像處理"""
    logger.info("====== 步驟1: 標籤格式轉換與圖像處理 ======")

    # 獲取參數
    source_format = config.get("source_format", "auto")
    target_shape = config.get("target_shape", "polygon")
    input_dirs = config.get("input_dirs", [])
    resize = config.get("resize", 0.3)
    quality = config.get("quality", 75)

    if not input_dirs:
        logger.error("未指定輸入目錄")
        return False

    # 創建輸出目錄
    labelme_dir = output_dir / "1_labelme_format"
    labelme_dir.mkdir(parents=True, exist_ok=True)

    # 創建轉換器
    converter = AnnotationConverter(logger)

    # 處理每個輸入目錄
    for input_dir in input_dirs:
        logger.info(f"處理輸入目錄: {input_dir}")

        # 創建特定輸出目錄
        dir_name = Path(input_dir).name
        output_subdir = labelme_dir / dir_name
        output_subdir.mkdir(exist_ok=True)

        # 執行轉換
        stats = converter.convert_format(
            input_path=input_dir,
            output_path=output_subdir,
            source_format=source_format,
            target_shape=target_shape,
            resize=resize,
            quality=quality
        )

        logger.info(
            f"目錄 {dir_name} 轉換完成，成功: {stats['success']}, 失敗: {stats['failed']}, 跳過: {stats.get('skipped', 0)}")

    logger.info("步驟1完成: 所有標籤已轉換為LabelMe格式並處理圖像")
    return labelme_dir


def step2_edit_annotations(config, input_dir, output_dir, logger):
    """步驟2: 標籤編輯處理"""
    logger.info("====== 步驟2: 標籤編輯處理 ======")

    # 確認輸入目錄
    if not input_dir or not input_dir.exists():
        logger.error("找不到步驟1的輸出目錄")
        return False

    # 創建輸出目錄
    edited_dir = output_dir / "2_edited_labels"
    edited_dir.mkdir(parents=True, exist_ok=True)

    # 創建處理器
    processor = AnnotationEditor(
        input_dir=input_dir,
        output_dir=edited_dir,  # 直接使用編號命名的目錄作為輸出
        logger=logger,
        recursive=True
    )

    # 載入數據
    processor.load_data()

    # 檢查是否需要互動式處理
    interactive_mode = config.get("interactive_mode", True)

    if interactive_mode:
        # 啟動互動式會話
        logger.info("啟動互動式標籤處理會話")
        processor.interactive_session()
    else:
        # 非互動式處理
        logger.info("使用非互動式處理")

        # 執行標籤預覽
        processor.preview_labels()

        # 添加操作
        if "merge_rules" in config:
            processor.add_merge_operation(config["merge_rules"])

        if "rename_rules" in config:
            processor.add_rename_operation(config["rename_rules"])

        if "delete_labels" in config:
            processor.add_delete_operation(config["delete_labels"])

        # 執行操作
        if processor.pending_operations:
            processor.execute_pending_operations()

        # 保存結果
        processor.save_results()

    logger.info("步驟2完成: 標籤編輯處理")
    return edited_dir


def step3_divide_dataset(config, input_dir, output_dir, logger):
    """步驟3: 數據集分割為訓練、驗證、測試集"""
    logger.info("====== 步驟3: 數據集分割 ======")

    # 確認輸入目錄
    if not input_dir or not input_dir.exists():
        logger.error("找不到步驟2的輸出目錄")
        return False

    # 獲取參數
    train_ratio = config.get("train_ratio", 0.7)
    val_ratio = config.get("val_ratio", 0.15)
    test_ratio = config.get("test_ratio", 0.15)
    class_limits = config.get("class_limits", {})

    # 添加這一行來檢查互動模式設定
    interactive_mode = config.get("divide_interactive", True)

    # 創建輸出目錄
    split_dir = output_dir / "3_divided_dataset"
    split_dir.mkdir(parents=True, exist_ok=True)

    # 創建分割器
    divider = DatasetDivider(
        input_dir=input_dir,
        output_dir=split_dir,
        train_ratio=train_ratio,
        val_ratio=val_ratio,
        test_ratio=test_ratio,
        class_limits=class_limits,
        logger=logger
    )

    # 修改這部分來支援互動模式
    if interactive_mode:
        logger.info("啟動互動式數據集分割配置")
        # 臨時保存原始類別限制
        original_limits = divider.class_limits.copy() if divider.class_limits else {}

        # 先收集樣本獲取類別數量
        divider._collect_samples()
        class_counts = divider.class_counts

        # 互動式設定
        print("\n===== 互動式數據集分割設定 =====")

        # 輸入分割比例
        while True:
            try:
                print("\n請輸入資料集分割比例 (三個數字總和須為 1.0)")
                new_train_ratio = float(
                    input("訓練集比例 [{}]: ".format(train_ratio)) or train_ratio)
                new_val_ratio = float(
                    input("驗證集比例 [{}]: ".format(val_ratio)) or val_ratio)
                new_test_ratio = float(
                    input("測試集比例 [{}]: ".format(test_ratio)) or test_ratio)

                total = new_train_ratio + new_val_ratio + new_test_ratio
                if abs(total - 1.0) <= 0.001:
                    # 更新比例
                    divider.train_ratio = new_train_ratio
                    divider.val_ratio = new_val_ratio
                    divider.test_ratio = new_test_ratio
                    break
                else:
                    print(f"錯誤: 比例總和為 {total}，須為 1.0")
            except ValueError:
                print("請輸入有效的數字")

        # 輸入類別限制
        print("\n請為每個類別設定數量限制 (enter鍵表示不限制)")

        class_names = [name for name in class_counts.keys()
                       if name != "multi_class"]

        if "multi_class" in class_counts:
            print(
                f"多類別樣本數: {int(class_counts['multi_class']/2)} (不需要設定限制，會自動優先使用)")

        for class_name in class_names:
            try:
                # 計算圖片數量（不是json數量）
                total_img_count = class_counts[class_name]

                # 計算該類別中的標籤物件數量
                class_objects_count = 0
                # 只使用一個任務來計算，避免重複計算
                task_samples = divider._collect_class_samples(
                    Path(input_dir) / class_name, class_name, "segment")

                # 計算所有樣本中的標籤總數
                for sample in task_samples:
                    if sample["distribution"]:
                        class_objects_count += sum(
                            sample["distribution"].values())

                # 取原先設置的限制或默認為0
                current_limit = original_limits.get(class_name, 0)

                # 顯示類別的圖片數量和物件數量
                limit_str = input(
                    f"類別 '{class_name}' 的最大樣本數 [目前: {current_limit}] (總共有: {int(total_img_count)}張圖片, {class_objects_count}個類別物件): ")
                if limit_str:
                    divider.class_limits[class_name] = int(limit_str)
            except ValueError:
                print(
                    f"為類別 '{class_name}' 使用目前值: {original_limits.get(class_name, '不限制')}")
            except Exception as e:
                logger.error(f"計算類別 '{class_name}' 的物件數量時出錯: {e}")
                print(
                    f"為類別 '{class_name}' 使用目前值: {original_limits.get(class_name, '不限制')}")

    # 執行分割
    stats = divider.run()

    logger.info(f"步驟3完成: 分割了 {stats['samples']['total']} 個樣本")
    logger.info(f"訓練集: {stats['samples']['train']} 個樣本")
    logger.info(f"驗證集: {stats['samples']['val']} 個樣本")
    logger.info(f"測試集: {stats['samples']['test']} 個樣本")

    return split_dir


def step4_augment_data(config, split_dir, output_dir, logger):
    """步驟4: 數據增強 (可選)"""
    logger.info("====== 步驟4: 數據增強 (可選) ======")

    # 確認輸入目錄
    if not split_dir or not split_dir.exists():
        logger.error("找不到步驟3的輸出目錄")
        return False

    # 獲取參數
    source_dirs = config.get("augment_source_dirs", [])
    num_generations = config.get("num_generations", 100)
    resize = config.get("resize", 0.3)
    quality = config.get("quality", 75)
    class_targets = config.get("class_targets", {})
    task_type = config.get("task_type", "both")

    # 優先使用 segment 的訓練集作為來源和目標目錄
    segment_train_dir = split_dir / "segment" / "train"

    # 如果沒有指定來源目錄，使用分割目錄中的類別子目錄或 segment 訓練集
    if not source_dirs:
        if segment_train_dir.exists():
            logger.info(f"使用 segment 訓練集作為來源目錄")
            source_dirs.append(str(segment_train_dir))
        else:
            # 備選方案：使用分割目錄中的類別子目錄
            for class_dir in split_dir.iterdir():
                if class_dir.is_dir() and class_dir.name != "multi_class" and class_dir.name != "overlap":
                    source_dirs.append(str(class_dir))
            logger.info(f"自動添加 {len(source_dirs)} 個來源目錄")

    # 使用 segment 訓練集作為目標目錄
    target_dir = segment_train_dir

    # 如果 segment 訓練集不存在，嘗試其他備選路徑
    if not target_dir.exists():
        # 嘗試找 object_detect 訓練集
        target_dir = split_dir / "object_detect" / "train"

        # 如果還是不存在，嘗試簡化路徑
        if not target_dir.exists():
            target_dir = split_dir / "train"

    if not target_dir.exists():
        logger.error("找不到訓練集目錄")
        return False

    # 創建輸出目錄
    augmented_dir = output_dir / "4_augmented_data"
    augmented_dir.mkdir(parents=True, exist_ok=True)

    # 創建增強器
    augmenter = ImageAugmenter(
        source_dirs=source_dirs,
        target_dir=target_dir,
        output_dir=augmented_dir,
        resize=resize,
        quality=quality,
        class_targets=class_targets,
        task_type=task_type,
        logger=logger
    )

    # 檢查是否使用互動模式
    if config.get("interactive", True):
        logger.info("啟動互動式數據增強配置")
        stats = augmenter.run_interactive(
            num_generations=num_generations,
            regions_per_image=(2, 5),
            scale_range=(0.6, 1.2),
            margins=(0, 0, 0, 0),
            grid_placement=True,
            grid_size=4,
            max_placement_attempts=50,
            avoid_similar_positions=True,
            avoid_overlap=True,
            iou_threshold=0.1
        )
    else:
        # 執行增強
        stats = augmenter.run(
            num_generations=num_generations,
            regions_per_image=(2, 5),
            scale_range=(0.6, 1.2),
            margins=(0, 0, 0, 0),
            grid_placement=True,
            grid_size=4,
            max_placement_attempts=50,
            avoid_similar_positions=True,
            avoid_overlap=True,
            iou_threshold=0.1,
            visualize_region=True
        )

    if stats:
        logger.info(f"步驟4完成: 生成了 {stats['generated']} 張增強圖像")
    else:
        logger.warning("數據增強可能被取消或發生錯誤")

    return augmented_dir


def main():
    # 解析命令行參數
    parser = argparse.ArgumentParser(description="圖像數據集處理工具")

    # 基本參數
    parser.add_argument("--config", type=str, help="配置文件路徑")
    parser.add_argument("--output", type=str, default="output", help="輸出根目錄")
    parser.add_argument(
        "--step", type=int, choices=[1, 2, 3, 4], help="執行特定步驟 (1:轉換, 2:編輯, 3:分割, 4:增強)")
    parser.add_argument("--steps", type=str, help="指定要執行的步驟，例如 '1,2,3'")
    parser.add_argument("--all", action="store_true", help="執行所有步驟")
    parser.add_argument("--interactive", action="store_true", help="啟用互動式處理")

    # 通用參數
    parser.add_argument("--resize", type=str, default="0.3",
                        help="調整圖像大小，格式: 寬x高 或 縮放比例(0-1)，默認0.3")
    parser.add_argument("--quality", type=int, default=75, help="JPEG品質")

    # 步驟1特定參數: 標籤格式轉換
    parser.add_argument("--input", nargs="+", help="輸入目錄列表")
    parser.add_argument("--format", type=str, choices=[
                        "auto", "voc", "yolo", "coco", "labelme"], default="auto", help="輸入格式")
    parser.add_argument(
        "--shape", type=str, choices=["box", "polygon"], default="polygon", help="目標形狀")

    # 步驟2特定參數: 標籤編輯
    parser.add_argument(
        "--merge", help="合併規則，格式為 'target1:old1,old2;target2:old3,old4'")
    parser.add_argument("--rename", help="重命名規則，格式為 'old1:new1;old2:new2'")
    parser.add_argument("--delete", help="要刪除的標籤，格式為 'label1,label2,label3'")

    # 步驟3特定參數: 數據集分割
    parser.add_argument("--train-ratio", type=float, default=0.7, help="訓練集比例")
    parser.add_argument("--val-ratio", type=float, default=0.15, help="驗證集比例")
    parser.add_argument("--test-ratio", type=float, default=0.15, help="測試集比例")
    parser.add_argument("--class-limits", help="類別限制JSON文件")

    # 步驟4特定參數: 數據增強
    parser.add_argument("--augment-source", nargs="+", help="增強來源目錄")
    parser.add_argument("--num-gen", type=int, default=100, help="生成圖像數量")
    parser.add_argument("--class-targets", help="類別目標計數JSON文件")
    parser.add_argument("--task-type", choices=["seg", "det", "both"],
                        default="both", help="增強任務類型 (seg:分割, det:檢測, both:兩者)")

    args = parser.parse_args()

    # 創建時間戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 創建輸出目錄（加上時間戳記）
    output_dir = Path(f"{args.output}_{timestamp}")
    output_dir.mkdir(exist_ok=True, parents=True)

    # 設置日誌
    log_file = output_dir / f"process_log.txt"
    logger = setup_logging(log_file)

    logger.info(f"開始處理圖像數據集，時間: {timestamp}")

    # 加載配置
    config = {}
    if args.config:
        config = load_config(args.config)
        logger.info(f"已加載配置文件: {args.config}")

    # 更新配置
    # 通用設置
    config["resize"] = parse_resize_param(args.resize)
    config["quality"] = args.quality

    # 步驟1特定配置: 標籤格式轉換
    if args.input:
        config["input_dirs"] = args.input
    config["source_format"] = args.format
    config["target_shape"] = args.shape

    # 步驟2特定配置: 標籤編輯
    config["interactive_mode"] = args.interactive

    if args.merge:
        merge_rules = {}
        for pair in args.merge.split(";"):
            if ":" in pair:
                target, sources = pair.split(":")
                source_labels = [s.strip() for s in sources.split(",")]
                merge_rules[target.strip()] = source_labels
        config["merge_rules"] = merge_rules

    if args.rename:
        rename_rules = {}
        for pair in args.rename.split(";"):
            if ":" in pair:
                old, new = pair.split(":")
                rename_rules[old.strip()] = new.strip()
        config["rename_rules"] = rename_rules

    if args.delete:
        config["delete_labels"] = [label.strip()
                                   for label in args.delete.split(",") if label.strip()]

    # 步驟3特定配置: 數據集分割
    config["train_ratio"] = args.train_ratio
    config["val_ratio"] = args.val_ratio
    config["test_ratio"] = args.test_ratio
    config["interactive"] = args.interactive  # 新增此行

    if args.class_limits:
        try:
            with open(args.class_limits, 'r', encoding='utf-8') as f:
                config["class_limits"] = json.load(f)
        except Exception as e:
            logger.error(f"無法讀取類別限制文件: {e}")

    # 步驟4特定配置: 數據增強
    if args.augment_source:
        config["augment_source_dirs"] = args.augment_source

    config["num_generations"] = args.num_gen
    config["interactive"] = args.interactive
    config["task_type"] = args.task_type

    if args.class_targets:
        try:
            with open(args.class_targets, 'r', encoding='utf-8') as f:
                config["class_targets"] = json.load(f)
        except Exception as e:
            logger.error(f"無法讀取類別目標文件: {e}")

    # 保存完整配置
    config_file = output_dir / f"config_{timestamp}.json"
    save_config(config, config_file)
    logger.info(f"已保存完整配置到: {config_file}")

    # 決定要執行的步驟
    steps_to_run = []
    if args.all:
        steps_to_run = [1, 2, 3, 4]
    elif args.step:
        steps_to_run = [args.step]
    elif args.steps:
        try:
            steps_to_run = [int(s.strip())
                            for s in args.steps.split(',') if s.strip()]
        except ValueError:
            logger.error("無效的步驟格式，請使用逗號分隔的數字，例如 '1,2,3'")
            return
    else:
        logger.info("未指定要執行的步驟，將執行所有步驟")
        steps_to_run = [1, 2, 3, 4]

    # 執行步驟
    step_results = {}

    # 步驟1: 標籤格式轉換
    if 1 in steps_to_run:
        logger.info("開始執行步驟1: 標籤格式轉換")
        try:
            step_results[1] = step1_convert_annotations(
                config, output_dir, logger)
            if step_results[1]:
                logger.info("步驟1執行成功")
            else:
                logger.error("步驟1執行失敗")
                if 2 in steps_to_run or 3 in steps_to_run or 4 in steps_to_run:
                    logger.warning("由於步驟1失敗，後續步驟可能無法正常執行")
        except Exception as e:
            logger.exception(f"步驟1執行出錯: {e}")
            step_results[1] = False

    # 步驟2: 標籤編輯
    if 2 in steps_to_run:
        logger.info("開始執行步驟2: 標籤編輯")
        try:
            # 如果步驟1執行成功，使用其結果作為輸入
            input_dir = step_results.get(1, False)
            if not input_dir and (1 not in steps_to_run):
                # 如果沒有執行步驟1，嘗試使用默認路徑
                input_dir = output_dir / "1_labelme_format"
                if not input_dir.exists():
                    logger.error("找不到步驟1的輸出目錄，無法執行步驟2")
                    step_results[2] = False
                    return

            step_results[2] = step2_edit_annotations(
                config, input_dir, output_dir, logger)
            if step_results[2]:
                logger.info("步驟2執行成功")
            else:
                logger.error("步驟2執行失敗")
                if 3 in steps_to_run or 4 in steps_to_run:
                    logger.warning("由於步驟2失敗，後續步驟可能無法正常執行")
        except Exception as e:
            logger.exception(f"步驟2執行出錯: {e}")
            step_results[2] = False

    # 步驟3: 數據集分割
    if 3 in steps_to_run:
        logger.info("開始執行步驟3: 數據集分割")
        try:
            # 如果步驟2執行成功，使用其結果作為輸入
            input_dir = step_results.get(2, False)
            if not input_dir and (2 not in steps_to_run):
                # 如果沒有執行步驟2，嘗試使用 2_edited_labels 目錄
                input_dir = output_dir / "2_edited_labels"
                if not input_dir.exists():
                    logger.error("找不到步驟2的輸出目錄，無法執行步驟3")
                    step_results[3] = False
                    return

            step_results[3] = step3_divide_dataset(
                config, input_dir, output_dir, logger)
            if step_results[3]:
                logger.info("步驟3執行成功")
            else:
                logger.error("步驟3執行失敗")
                if 4 in steps_to_run:
                    logger.warning("由於步驟3失敗，數據增強可能無法正常執行")
        except Exception as e:
            logger.exception(f"步驟3執行出錯: {e}")
            step_results[3] = False

    # 步驟4: 數據增強 (可選)
    if 4 in steps_to_run:
        logger.info("開始執行步驟4: 數據增強")
        try:
            # 如果步驟3執行成功，使用其結果作為輸入
            split_dir = step_results.get(3, False)
            if not split_dir and (3 not in steps_to_run):
                # 如果沒有執行步驟3，嘗試使用默認路徑
                split_dir = output_dir / "3_divided_dataset"
                if not split_dir.exists():
                    logger.error("找不到步驟3的輸出目錄，無法執行數據增強")
                    step_results[4] = False
                    return

            step_results[4] = step4_augment_data(
                config, split_dir, output_dir, logger)
            if step_results[4]:
                logger.info("步驟4執行成功")
            else:
                logger.info("步驟4執行完成")  # 數據增強可能被用戶取消
        except Exception as e:
            logger.exception(f"步驟4執行出錯: {e}")
            step_results[4] = False

    # 總結
    logger.info("=== 處理完成摘要 ===")
    for step in sorted(steps_to_run):
        step_name = {
            1: "標籤格式轉換",
            2: "標籤編輯",
            3: "數據集分割",
            4: "數據增強"
        }.get(step, f"步驟{step}")

        result = step_results.get(step)
        if result is True or (isinstance(result, Path) and result.exists()):
            logger.info(f"步驟{step} ({step_name}): 成功")
        else:
            logger.info(f"步驟{step} ({step_name}): 失敗或跳過")

    logger.info(f"處理完成！詳細日誌請查看: {log_file}")
    print(f"\n處理完成！詳細日誌請查看: {log_file}")


if __name__ == "__main__":
    main()
