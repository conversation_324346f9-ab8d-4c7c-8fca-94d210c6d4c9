#!/usr/bin/env python3
"""
增強YOLO推理系統
支持YOLO11分割+YOLO12檢測，自動轉換，SAHI功能等
"""

# 使用統一導入管理
import sys
from pathlib import Path

current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, DATA_PROCESSING_AVAILABLE, setup_matplotlib
setup_project_paths()
# 統一設置matplotlib，減少字體查找debug信息
setup_matplotlib()

import os
import json
import yaml
import logging
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import random
import colorsys 
import numpy as np
from collections import defaultdict

# 基本導入（用於類型註釋）
try:
    pass
except ImportError:
    # 創建模擬的numpy模組用於類型註釋
    class MockNumpy:
        class ndarray:
            pass
    np = MockNumpy()

try:
    import cv2
    import torch
    import matplotlib.pyplot as plt
    import seaborn as sns
    from tqdm import tqdm
    CV_AVAILABLE = True
    # 重新導入真實的numpy
    import numpy as np
except ImportError:
    CV_AVAILABLE = False

try:
    from ultralytics import YOLO
    from ultralytics.utils.plotting import Annotator, colors
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False

try:
    from sahi import AutoDetectionModel
    from sahi.predict import get_sliced_prediction
    from sahi.utils.cv import read_image
    from sahi.utils.file import download_from_url
    SAHI_AVAILABLE = True
except ImportError:
    SAHI_AVAILABLE = False

try:
    from sklearn.metrics import (
        precision_score, recall_score, f1_score, 
        average_precision_score, confusion_matrix
    )
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

# CSV doesn't need extra dependencies, always available

if DATA_PROCESSING_AVAILABLE:
    try:
        from 資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2
        from 資料前處理.tools.format_detector import FormatDetector
        CONVERTER_AVAILABLE = True
    except ImportError:
        CONVERTER_AVAILABLE = False
else:
    CONVERTER_AVAILABLE = False


# 固定類別順序（與模型訓練順序一致）- 解決類別ID不匹配問題
CLASS_NAMES = [
    "expansion_joint_伸縮縫",      # ID: 0
    "joint_路面接縫",               # ID: 1
    "linear_crack_裂縫",        # ID: 2
    "Alligator_crack_龜裂",     # ID: 3
    "potholes_坑洞",            # ID: 4
    "patch_補綻",               # ID: 5
    "manhole_人孔蓋或排水溝",             # ID: 6
    "deformation_變形",         # ID: 7
    "dirt_污垢",                # ID: 8
    "lane_line_linear_白綫裂縫"     # ID: 9
]

# 額外的標籤別名支援（用於智能匹配）
LABEL_ALIASES = {
    'manhole': 'manhole_人孔蓋或排水溝',
    'potholes': 'potholes_坑洞', 
    'linear_crack': 'linear_crack_裂縫',
    'linear_crack_': 'linear_crack_裂縫',
    'dirt': 'dirt_污垢',
    'expansion_joint': 'expansion_joint_伸縮縫',
    'expansion_joint_': 'expansion_joint_伸縮縫',
    'joint': 'joint_路面接縫',
    'joint_': 'joint_路面接縫',
    'deformation': 'deformation_變形',
    'patch': 'patch_補綻',
    'Alligator_crack': 'Alligator_crack_龜裂',
    'lane_line_linear': 'lane_line_linear_白綫裂縫'
}

def validate_class_order(model_path: str, class_configs: Dict[int, 'ClassConfig']) -> bool:
    """驗證類別配置是否與模型訓練順序一致"""
    try:
        from ultralytics import YOLO
        model = YOLO(model_path)
        
        if hasattr(model, 'names') and model.names:
            model_names = [model.names[i] for i in sorted(model.names.keys())]
            config_names = [class_configs[i].name for i in sorted(class_configs.keys())]
            
            if model_names != config_names:
                print(f"⚠️  類別順序不匹配警告:")
                print(f"   模型順序: {model_names}")
                print(f"   配置順序: {config_names}")
                print(f"   這可能導致預測與GT不匹配！")
                return False
            else:
                print(f"✅ 類別順序驗證通過")
                return True
    except Exception as e:
        print(f"⚠️  類別順序驗證失敗: {e}")
        return False


def scan_labelme_annotations(labelme_dir: str) -> Dict[str, Any]:
    """
    掃描LabelMe標註文件，提取所有類別資訊
    
    Args:
        labelme_dir: LabelMe標註文件目錄
    
    Returns:
        包含類別統計的字典
    """
    labelme_path = Path(labelme_dir)
    json_files = list(labelme_path.glob("*.json"))
    
    if not json_files:
        raise ValueError(f"在 {labelme_dir} 中未找到LabelMe JSON文件")
    
    class_stats = {}
    all_labels = set()
    total_annotations = 0
    
    for json_file in tqdm(json_files, desc="掃描LabelMe標註"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'shapes' in data:
                for shape in data['shapes']:
                    original_label = shape.get('label', '')
                    if original_label:
                        # 使用 LABEL_ALIASES 進行標籤轉換
                        label = LABEL_ALIASES.get(original_label, original_label)
                        all_labels.add(label)
                        if label not in class_stats:
                            class_stats[label] = {
                                'count': 0,
                                'shape_types': set(),
                                'files': set()
                            }
                        
                        class_stats[label]['count'] += 1
                        class_stats[label]['shape_types'].add(shape.get('shape_type', 'unknown'))
                        class_stats[label]['files'].add(json_file.name)
                        total_annotations += 1
                        
        except Exception as e:
            logging.warning(f"讀取文件失敗 {json_file}: {e}")
    
    # 轉換set為list以便JSON序列化
    for label in class_stats:
        class_stats[label]['shape_types'] = list(class_stats[label]['shape_types'])
        class_stats[label]['files'] = list(class_stats[label]['files'])
    
    # 使用固定的CLASS_NAMES順序，而不是字母順序
    # 這確保類別ID與模型訓練順序一致
    ordered_labels = []
    for class_name in CLASS_NAMES:
        if class_name in all_labels:
            ordered_labels.append(class_name)
    
    # 檢查是否有在CLASS_NAMES中未定義的標籤，並嘗試智能匹配
    def find_matching_class_name(label: str) -> str:
        """智能匹配標籤到CLASS_NAMES"""
        # 直接匹配
        if label in LABEL_ALIASES:
            return LABEL_ALIASES[label]
        
        # 模糊匹配：檢查是否包含關鍵字
        for class_name in CLASS_NAMES:
            if '_' in class_name:
                english_part = class_name.split('_')[0]
                if english_part.lower() == label.lower():
                    return class_name
        
        return label  # 如果找不到匹配，返回原標籤
    
    # 處理未識別的標籤
    unrecognized_labels = all_labels - set(CLASS_NAMES)
    if unrecognized_labels:
        print(f"⚠️  發現未在CLASS_NAMES中定義的標籤: {unrecognized_labels}")
        
        # 嘗試智能匹配
        matched_labels = {}
        still_unrecognized = set()
        
        for label in unrecognized_labels:
            matched = find_matching_class_name(label)
            if matched in CLASS_NAMES:
                matched_labels[label] = matched
                print(f"   ✅ 自動匹配: '{label}' -> '{matched}'")
            else:
                still_unrecognized.add(label)
        
        if still_unrecognized:
            print(f"   ⚠️  仍無法匹配的標籤: {still_unrecognized}")
            print(f"   這些標籤將被忽略，或請更新CLASS_NAMES常量")
        
        # 更新ordered_labels，使用匹配後的標籤
        for i, label in enumerate(ordered_labels):
            if label in matched_labels:
                ordered_labels[i] = matched_labels[label]
    
    return {
        'labels': ordered_labels,  # 使用固定順序，不是字母順序
        'class_stats': class_stats,
        'total_files': len(json_files),
        'total_annotations': total_annotations,
        'unique_classes': len(all_labels),
        'unrecognized_labels': list(unrecognized_labels)
    }


def generate_distinct_colors(num_colors: int, seed: int = 42) -> List[Tuple[int, int, int]]:
    """
    生成視覺上區別明顯的顏色 (使用seed確保一致性)
    
    Args:
        num_colors: 需要的顏色數量
        seed: 隨機種子，確保顏色生成一致性
    
    Returns:
        RGB顏色列表
    """
    # 設置隨機種子確保顏色一致性
    random.seed(seed)
    colors = []
    
    # 預定義一些常用顏色
    predefined_colors = [
        (255, 0, 0),     # 紅色
        (0, 255, 0),     # 綠色
        (0, 0, 255),     # 藍色
        (255, 255, 0),   # 黃色
        (255, 0, 255),   # 洋紅色
        (0, 255, 255),   # 青色
        (255, 165, 0),   # 橙色
        (128, 0, 128),   # 紫色
        (255, 192, 203), # 粉色
        (128, 128, 128), # 灰色
        (165, 42, 42),   # 棕色
        (0, 128, 0),     # 深綠色
        (0, 0, 128),     # 深藍色
        (128, 128, 0),   # 橄欖色
        (128, 0, 0),     # 暗紅色
    ]
    
    # 使用預定義顏色
    for i in range(min(num_colors, len(predefined_colors))):
        colors.append(predefined_colors[i])
    
    # 如果需要更多顏色，使用黃金比例生成 (Simple Tool方式)
    if num_colors > len(predefined_colors):
        remaining = num_colors - len(predefined_colors)
        h = random.random()  # 隨機起始色調
        phi = 0.618033988749  # 黃金比例
        
        for i in range(remaining):
            h = (h + phi) % 1.0
            saturation = 0.7 + random.random() * 0.3  # 0.7-1.0
            value = 0.8 + random.random() * 0.2       # 0.8-1.0
            
            rgb = colorsys.hsv_to_rgb(h, saturation, value)
            colors.append(tuple(int(c * 255) for c in rgb))
    
    # 重置隨機種子
    random.seed()
    return colors



def generate_class_configs_from_labelme(labelme_dir: str, default_conf: float = 0.5) -> Dict[int, 'ClassConfig']:
    """
    從LabelMe標註自動生成類別配置（使用固定順序）
    Args:
        labelme_dir: LabelMe標註目錄
        default_conf: 預設置信度閾值
    Returns:
        類別配置字典
    """
    scan_result = scan_labelme_annotations(labelme_dir)
    class_stats = scan_result['class_stats']
    colors = generate_distinct_colors(len(CLASS_NAMES), seed=hash(str(CLASS_NAMES)) % 10000)
    class_configs = {}
    for i, label in enumerate(CLASS_NAMES):
        count = class_stats[label]['count'] if label in class_stats else 0
        class_configs[i] = ClassConfig(
            name=label,
            conf_threshold=default_conf,
            color=colors[i],
            enabled=True,
            description=f"自動從LabelMe生成，共{count}個標註"
        )
    return class_configs


@dataclass
class ClassConfig:
    """類別配置"""
    name: str
    conf_threshold: float = 0.5
    color: Tuple[int, int, int] = (255, 0, 0)
    enabled: bool = True
    description: str = ""


@dataclass
class EnhancedYOLOConfig:
    """增強YOLO配置"""
    
    # 模型配置
    detection_model_path: str = ""  # YOLO12檢測模型
    segmentation_model_path: str = ""  # YOLO11分割模型
    device: str = "auto"
    
    # 推理配置
    img_size: int = 640
    global_conf: float = 0.25
    iou_threshold: float = 0.45
    max_det: int = 1000
    
    # 類別配置
    class_configs: Dict[int, ClassConfig] = field(default_factory=dict)
    labelme_dir: str = ""  # LabelMe標註目錄，用於自動生成類別配置
    auto_generate_classes: bool = False  # 是否從LabelMe自動生成類別
    
    # SAHI配置 - 完整23個參數
    enable_sahi: bool = False
    slice_height: int = 512
    slice_width: int = 512
    overlap_height_ratio: float = 0.2
    overlap_width_ratio: float = 0.2
    
    # SAHI進階參數
    auto_slice_resolution: bool = True
    perform_standard_pred: bool = True
    roi_ratio: Tuple[float, float, float, float] = (0.0, 0.0, 1.0, 1.0)
    
    # SAHI後處理參數
    postprocess_type: str = "GREEDYNMM"  # "GREEDYNMM", "NMM", "NMS"
    postprocess_match_threshold: float = 0.1
    postprocess_class_agnostic: bool = False
    
    # SAHI類別過濾參數
    exclude_classes_by_name: List[str] = field(default_factory=list)
    exclude_classes_by_id: List[int] = field(default_factory=list)
    
    # SAHI輸出控制
    no_standard_prediction: bool = False
    no_sliced_prediction: bool = False
    export_pickle: bool = False
    export_crop: bool = False
    
    # Simple Tool功能整合 - 雙模型協同
    secondary_detection_model_path: str = ""
    secondary_segmentation_model_path: str = ""
    enable_dual_model_consensus: bool = False
    consensus_threshold: float = 0.3
    consensus_iou_threshold: float = 0.3
    
    # Simple Tool功能整合 - 智能檢測過濾
    enable_intelligent_filtering: bool = False
    enable_detection_merge: bool = False
    iou_merge_threshold: float = 0.3
    
    # Simple Tool功能整合 - 選擇性類別保存
    target_classes: Optional[List[int]] = None
    save_all_when_target_found: bool = True
    skip_empty_results: bool = True
    
    # 輸出配置
    save_visualizations: bool = True
    save_predictions: bool = True
    save_statistics: bool = True
    output_format: str = "both"  # detection, segmentation, both
    
    # 轉換配置
    auto_convert_annotations: bool = True
    input_annotation_format: str = "auto"  # auto, labelme, yolo, coco, voc
    
    # 高級功能
    enable_tracking: bool = False
    enable_pose_estimation: bool = False
    enable_classification: bool = False
    enable_batch_processing: bool = True
    
    def __post_init__(self):
        # 如果指定了LabelMe目錄且啟用自動生成，從LabelMe生成類別配置
        if self.auto_generate_classes and self.labelme_dir and os.path.exists(self.labelme_dir):
            try:
                self.class_configs = generate_class_configs_from_labelme(
                    self.labelme_dir, 
                    default_conf=self.global_conf
                )
                logging.info(f"從LabelMe自動生成了 {len(self.class_configs)} 個類別配置")
            except Exception as e:
                logging.warning(f"從LabelMe生成類別配置失敗: {e}")
                self._set_default_classes()
        elif not self.class_configs:
            self._set_default_classes()
    
    def _set_default_classes(self):
        """設置預設類別配置"""
        self.class_configs = {
            0: ClassConfig("裂縫", 0.5, (255, 0, 0), True, "道路表面裂縫"),
            1: ClassConfig("坑洞", 0.4, (0, 255, 0), True, "路面坑洞"),
            2: ClassConfig("人孔蓋", 0.6, (0, 0, 255), True, "下水道人孔蓋"),
            3: ClassConfig("標線", 0.3, (255, 255, 0), True, "道路標線"),
            4: ClassConfig("其他", 0.5, (255, 0, 255), True, "其他基礎設施")
        }
    
    def generate_classes_from_labelme(self, labelme_dir: str, default_conf: float = None) -> Dict[str, Any]:
        """
        手動從LabelMe目錄生成類別配置
        
        Args:
            labelme_dir: LabelMe標註目錄
            default_conf: 預設置信度閾值
        
        Returns:
            生成結果統計
        """
        if default_conf is None:
            default_conf = self.global_conf
        
        try:
            # 掃描LabelMe標註
            scan_result = scan_labelme_annotations(labelme_dir)
            
            # 生成類別配置
            self.class_configs = generate_class_configs_from_labelme(labelme_dir, default_conf)
            
            return {
                'success': True,
                'generated_classes': len(self.class_configs),
                'scan_result': scan_result
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_class_config(self, output_path: str):
        """導出類別配置到YAML文件"""
        config_data = {
            'class_configs': {}
        }
        
        for class_id, class_config in self.class_configs.items():
            config_data['class_configs'][class_id] = {
                'name': class_config.name,
                'conf_threshold': class_config.conf_threshold,
                'color': list(class_config.color),
                'enabled': class_config.enabled,
                'description': class_config.description
            }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
    
    def load_class_config(self, config_path: str):
        """從YAML文件載入類別配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        if 'class_configs' in config_data:
            self.class_configs = {}
            for class_id, class_data in config_data['class_configs'].items():
                self.class_configs[int(class_id)] = ClassConfig(
                    name=class_data['name'],
                    conf_threshold=class_data['conf_threshold'],
                    color=tuple(class_data['color']),
                    enabled=class_data['enabled'],
                    description=class_data.get('description', '')
                )


class EnhancedYOLOInference:
    """增強YOLO推理系統"""
    
    def __init__(self, config: Union[EnhancedYOLOConfig, str, Dict]):
        """初始化推理系統"""
        
        if isinstance(config, str):
            with open(config, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            self.config = EnhancedYOLOConfig(**config_dict)
        elif isinstance(config, dict):
            self.config = EnhancedYOLOConfig(**config)
        else:
            self.config = config
        
        self.logger = logging.getLogger(__name__)
        # 臨時啟用DEBUG級別以調試路徑查找問題
        self.logger.setLevel(logging.DEBUG)
        
        # 添加控制台處理器以便看到DEBUG信息
        if not self.logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s')
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        if not ULTRALYTICS_AVAILABLE:
            raise ImportError("Ultralytics YOLO not available. Please install: pip install ultralytics")
        
        # 初始化模型
        self.detection_model = None
        self.segmentation_model = None
        self._load_models()
        
        # 初始化雙模型支持 (Simple Tool功能)
        self.secondary_detection_model = None
        self.secondary_segmentation_model = None
        if self.config.enable_dual_model_consensus:
            self._load_secondary_models()
        
        # 初始化SAHI
        self.sahi_model = None
        if self.config.enable_sahi and SAHI_AVAILABLE:
            self._setup_sahi()
        
        # 初始化轉換器
        self.converter = None
        if self.config.auto_convert_annotations and CONVERTER_AVAILABLE:
            self._setup_converter()
        
        # 統計資料
        self.inference_stats = {
            "total_images": 0,
            "total_detections": 0,
            "processing_time": 0,
            "class_counts": {cls_id: 0 for cls_id in self.config.class_configs.keys()}
        }
    
    def _load_secondary_models(self):
        """載入副模型 (Simple Tool功能)"""
        
        # 載入副檢測模型
        if self.config.secondary_detection_model_path and os.path.exists(self.config.secondary_detection_model_path):
            try:
                self.secondary_detection_model = YOLO(self.config.secondary_detection_model_path)
                self.logger.info(f"載入副檢測模型: {self.config.secondary_detection_model_path}")
            except Exception as e:
                self.logger.error(f"載入副檢測模型失敗: {e}")
        
        # 載入副分割模型
        if self.config.secondary_segmentation_model_path and os.path.exists(self.config.secondary_segmentation_model_path):
            try:
                self.secondary_segmentation_model = YOLO(self.config.secondary_segmentation_model_path)
                self.logger.info(f"載入副分割模型: {self.config.secondary_segmentation_model_path}")
            except Exception as e:
                self.logger.error(f"載入副分割模型失敗: {e}")
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU (Simple Tool功能)"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # 計算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # 計算聯集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0
    
    def _apply_class_thresholds(self, predictions: List[Dict]) -> List[Dict]:
        """應用類別特定閾值 (Simple Tool功能)"""
        filtered_predictions = []

        for pred in predictions:
            cls_id = pred['class_id']
            confidence = pred['confidence']
            
            # 使用class_configs中的閾值，或全局閾值作為後備
            if cls_id in self.config.class_configs:
                threshold = self.config.class_configs[cls_id].conf_threshold
            else:
                threshold = self.config.global_conf

            if confidence >= threshold:
                filtered_predictions.append(pred)

        return filtered_predictions
    
    def _apply_intelligent_filtering(self, predictions: List[Dict]) -> List[Dict]:
        """應用智能檢測過濾 (Simple Tool功能)"""
        if not self.config.enable_intelligent_filtering:
            return predictions
        
        # 獲取類別名稱映射
        class_names = {cls_id: config.name for cls_id, config in self.config.class_configs.items()}
        
        # Step 1: linear_crack vs Alligator_crack (面積小者移除)
        filtered_predictions = []
        keep_indices = list(range(len(predictions)))

        for i, pred_i in enumerate(predictions):
            if i not in keep_indices:
                continue

            cls_i = pred_i['class_id']
            name_i = class_names.get(cls_i, str(cls_i)).split('_')[0].lower()

            if name_i == 'linear':
                box_i = pred_i['bbox']
                area_i = pred_i['area']

                for j, pred_j in enumerate(predictions):
                    if j <= i or j not in keep_indices:
                        continue

                    cls_j = pred_j['class_id']
                    name_j = class_names.get(cls_j, str(cls_j)).split('_')[0].lower()

                    if name_j == 'alligator':
                        box_j = pred_j['bbox']
                        area_j = pred_j['area']
                        iou = self._calculate_iou(box_i, box_j)

                        if iou > 0.0:
                            # 移除面積較小者
                            if area_j < area_i:
                                keep_indices.remove(j)
                            else:
                                keep_indices.remove(i)
                                break

        # 保留第一步過濾後的結果
        step1_predictions = [predictions[i] for i in keep_indices]

        # Step 2: linear_crack vs joint (mask 或 IoU 覆蓋 > 0.3 時刪 linear)
        final_keep_indices = list(range(len(step1_predictions)))

        for i, pred_i in enumerate(step1_predictions):
            if i not in final_keep_indices:
                continue

            cls_i = pred_i['class_id']
            name_i = class_names.get(cls_i, str(cls_i)).split('_')[0].lower()

            if name_i == 'linear':
                for j, pred_j in enumerate(step1_predictions):
                    if j == i or j not in final_keep_indices:
                        continue

                    cls_j = pred_j['class_id']
                    name_j = class_names.get(cls_j, str(cls_j)).split('_')[0].lower()

                    if name_j == 'joint':
                        # 如果有 mask，使用 mask 計算覆蓋率
                        if pred_i.get('mask') is not None and pred_j.get('mask') is not None:
                            mask_i = pred_i['mask']
                            mask_j = pred_j['mask']
                            inter = np.logical_and(mask_i, mask_j).sum()
                            total_linear = mask_i.sum()

                            if total_linear > 0 and (inter / total_linear) > 0.3:
                                final_keep_indices.remove(i)
                                break
                        else:
                            # 使用 IoU 近似
                            iou = self._calculate_iou(pred_i['bbox'], pred_j['bbox'])
                            if iou > 0.3:
                                final_keep_indices.remove(i)
                                break

        return [step1_predictions[i] for i in final_keep_indices]
    
    def _merge_detections(self, predictions: List[Dict]) -> List[Dict]:
        """合併相同類別的重疊檢測 (Simple Tool功能)"""
        if not predictions or not self.config.enable_detection_merge:
            return predictions

        # 按類別分組
        class_groups = defaultdict(list)
        for i, pred in enumerate(predictions):
            class_groups[pred['class_id']].append((i, pred))

        merged_predictions = []
        used_indices = set()

        # 對每個類別獨立處理
        for cls_id, group in class_groups.items():
            indices = [item[0] for item in group]
            preds = [item[1] for item in group]

            i = 0
            while i < len(indices):
                idx_i = indices[i]
                if idx_i in used_indices:
                    i += 1
                    continue

                pred_i = preds[i]
                merged_box = pred_i['bbox'].copy()
                merged_mask = pred_i.get('mask')
                best_conf = pred_i['confidence']

                used_indices.add(idx_i)

                # 找出與當前檢測重疊的其他檢測
                for j in range(i + 1, len(indices)):
                    idx_j = indices[j]
                    if idx_j in used_indices:
                        continue

                    pred_j = preds[j]
                    iou = self._calculate_iou(merged_box, pred_j['bbox'])

                    if iou > self.config.iou_merge_threshold:
                        used_indices.add(idx_j)

                        # 合併 bbox
                        merged_box[0] = min(merged_box[0], pred_j['bbox'][0])
                        merged_box[1] = min(merged_box[1], pred_j['bbox'][1])
                        merged_box[2] = max(merged_box[2], pred_j['bbox'][2])
                        merged_box[3] = max(merged_box[3], pred_j['bbox'][3])

                        # 合併置信度
                        best_conf = max(best_conf, pred_j['confidence'])

                        # 合併 mask
                        if merged_mask is not None and pred_j.get('mask') is not None:
                            merged_mask = np.logical_or(merged_mask, pred_j['mask'])
                        elif merged_mask is None and pred_j.get('mask') is not None:
                            merged_mask = pred_j['mask'].copy()

                # 添加合併後的檢測
                merged_pred = {
                    'class_id': cls_id,
                    'confidence': best_conf,
                    'bbox': merged_box,
                    'area': (merged_box[2] - merged_box[0]) * (merged_box[3] - merged_box[1]),
                }
                
                # 保留其他屬性
                for key in pred_i:
                    if key not in merged_pred:
                        merged_pred[key] = pred_i[key]
                
                if merged_mask is not None:
                    merged_pred['mask'] = merged_mask
                    
                merged_predictions.append(merged_pred)

                i += 1

        return merged_predictions
    
    def _filter_by_target_classes(self, predictions: List[Dict]) -> List[Dict]:
        """選擇性類別保存 (Simple Tool功能)"""
        if not self.config.target_classes:
            return predictions
        
        # 過濾指定類別
        filtered_predictions = []
        for pred in predictions:
            if pred['class_id'] in self.config.target_classes:
                filtered_predictions.append(pred)
        
        # 如果沒有找到目標類別且設置了save_all_when_target_found=False，返回空結果
        if not filtered_predictions and not self.config.save_all_when_target_found:
            return []
        
        # 如果找到目標類別且save_all_when_target_found=True，返回所有結果
        if filtered_predictions and self.config.save_all_when_target_found:
            return predictions
        
        # 如果沒有找到目標類別但save_all_when_target_found=True，根據skip_empty_results決定
        if not filtered_predictions and self.config.save_all_when_target_found:
            return [] if self.config.skip_empty_results else predictions
        
        return filtered_predictions
    
    def _apply_simple_tool_processing(self, predictions: List[Dict]) -> List[Dict]:
        """應用Simple Tool的所有處理步驟"""
        if not predictions:
            return predictions
        
        # 1. 應用類別特定閾值
        predictions = self._apply_class_thresholds(predictions)
        
        # 2. 應用智能檢測過濾
        predictions = self._apply_intelligent_filtering(predictions)
        
        # 3. 檢測合併
        predictions = self._merge_detections(predictions)
        
        # 4. 選擇性類別保存
        predictions = self._filter_by_target_classes(predictions)
        
        return predictions
    
    def _load_gt_annotations(self, annotation_path: str, image_shape: Tuple[int, int, int]) -> List[Dict]:
        """
        改進的GT標註載入方法，增強調試和錯誤處理
        
        Args:
            annotation_path: 標註文件路徑
            image_shape: 圖像形狀 (H, W, C)
        
        Returns:
            GT標註列表
        """
        # 基本驗證
        if not annotation_path:
            self.logger.warning("GT標註路徑為空")
            return []
        
        if not os.path.exists(annotation_path):
            self.logger.warning(f"GT標註文件不存在: {annotation_path}")
            return []
        
        # 圖像尺寸
        h, w = image_shape[:2]
        gt_annotations = []
        
        self.logger.info(f"🔍 開始載入GT標註: {annotation_path}")
        self.logger.info(f"📐 圖像尺寸: {w}x{h}")
        
        try:
            file_ext = Path(annotation_path).suffix.lower()
            self.logger.info(f"📄 文件類型: {file_ext}")
            
            if file_ext == '.json':  # LabelMe格式
                self.logger.info("🏷️  處理LabelMe JSON格式")
                
                with open(annotation_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 詳細日誌
                shapes = data.get('shapes', [])
                self.logger.info(f"📊 JSON包含 {len(shapes)} 個shapes")
                
                if 'imageWidth' in data and 'imageHeight' in data:
                    json_w, json_h = data['imageWidth'], data['imageHeight']
                    self.logger.info(f"📐 JSON中的圖像尺寸: {json_w}x{json_h}")
                    
                    if json_w != w or json_h != h:
                        self.logger.warning(f"⚠️  圖像尺寸不匹配! 實際: {w}x{h}, JSON: {json_w}x{json_h}")
                
                # 處理每個shape
                for i, shape in enumerate(shapes):
                    shape_type = shape.get('shape_type', '')
                    original_class_name = shape.get('label', '')
                    
                    # 使用 LABEL_ALIASES 進行標籤轉換
                    class_name = LABEL_ALIASES.get(original_class_name, original_class_name)
                    if class_name != original_class_name:
                        self.logger.debug(f"🎯 標籤轉換: '{original_class_name}' -> '{class_name}'")
                    
                    points = shape.get('points', [])
                    
                    self.logger.debug(f"   Shape {i+1}: {class_name} ({shape_type}) - {len(points)} 個點")
                    self.logger.debug(f"     原始點數據: {points}")
                    
                    bbox = None
                    
                    # 處理矩形
                    if shape_type == 'rectangle' and len(points) >= 2:
                        try:
                            x1, y1 = points[0]
                            x2, y2 = points[1]
                            
                            # 確保坐標正確
                            x1, x2 = min(x1, x2), max(x1, x2)
                            y1, y2 = min(y1, y2), max(y1, y2)
                            
                            # 邊界檢查
                            x1 = max(0, min(x1, w-1))
                            y1 = max(0, min(y1, h-1))
                            x2 = max(0, min(x2, w-1))
                            y2 = max(0, min(y2, h-1))
                            
                            bbox = [x1, y1, x2, y2]
                            self.logger.debug(f"     計算矩形邊界框: {bbox}")
                            
                        except (ValueError, IndexError, TypeError) as e:
                            self.logger.error(f"     ❌ 矩形點解析錯誤: {e}")
                            continue
                    
                    # 處理多邊形
                    elif shape_type == 'polygon' and len(points) >= 3:
                        try:
                            # 判斷點格式
                            if isinstance(points[0], list):
                                # 格式: [[x1,y1], [x2,y2], ...]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                            else:
                                # 格式: [x1, y1, x2, y2, ...]
                                xs = points[::2]
                                ys = points[1::2]
                            
                            if xs and ys:
                                x1, x2 = min(xs), max(xs)
                                y1, y2 = min(ys), max(ys)
                                
                                # 邊界檢查
                                x1 = max(0, min(x1, w-1))
                                y1 = max(0, min(y1, h-1))
                                x2 = max(0, min(x2, w-1))
                                y2 = max(0, min(y2, h-1))
                                
                                bbox = [x1, y1, x2, y2]
                                self.logger.debug(f"     計算多邊形邊界框: {bbox}")
                            
                        except (ValueError, IndexError, TypeError) as e:
                            self.logger.error(f"     ❌ 多邊形點解析錯誤: {e}")
                            continue
                    
                    else:
                        self.logger.warning(f"     ⚠️  不支持的形狀類型或點數不足: {shape_type}, {len(points)} 個點")
                        continue
                    
                    # 如果成功計算出邊界框
                    if bbox and bbox[2] > bbox[0] and bbox[3] > bbox[1]:  # 確保有效邊界框
                        # 尋找類別ID
                        class_id = self._find_class_id(class_name)
                        
                        if class_id >= 0:
                            annotation = {
                                'id': i,
                                'class_id': class_id,
                                'class_name': class_name,
                                'bbox': bbox,
                                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                                'shape_type': shape_type,
                                'points': points,  # 保存原始點數據
                                'confidence': 1.0  # GT標註置信度為1
                            }
                            
                            gt_annotations.append(annotation)
                            self.logger.info(f"     ✅ 成功加入GT: {class_name} (ID: {class_id}) - {bbox}")
                        else:
                            self.logger.warning(f"     ⚠️  找不到類別: '{class_name}'")
                    else:
                        self.logger.warning(f"     ❌ 無效邊界框: {bbox}")
            
            elif file_ext == '.txt':  # YOLO格式
                self.logger.info("🎯 處理YOLO TXT格式")
                
                with open(annotation_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                self.logger.info(f"📊 TXT包含 {len(lines)} 行")
                
                for i, line in enumerate(lines):
                    line = line.strip()
                    if not line:
                        continue
                    
                    self.logger.debug(f"   行 {i+1}: {line}")
                    
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            class_id = int(parts[0])
                            cx, cy, bw, bh = map(float, parts[1:5])
                            
                            # 轉換為絕對坐標
                            x1 = (cx - bw/2) * w
                            y1 = (cy - bh/2) * h
                            x2 = (cx + bw/2) * w
                            y2 = (cy + bh/2) * h
                            
                            # 邊界檢查
                            x1 = max(0, min(x1, w-1))
                            y1 = max(0, min(y1, h-1))
                            x2 = max(0, min(x2, w-1))
                            y2 = max(0, min(y2, h-1))
                            
                            bbox = [x1, y1, x2, y2]
                            
                            # 獲取類別名稱
                            class_name = self._get_class_name(class_id)
                            
                            # YOLO格式構建矩形點
                            rect_points = [[x1, y1], [x2, y1], [x2, y2], [x1, y2]]
                            
                            annotation = {
                                'id': i,
                                'class_id': class_id,
                                'class_name': class_name,
                                'bbox': bbox,
                                'area': (x2 - x1) * (y2 - y1),
                                'shape_type': 'rectangle',
                                'points': rect_points,  # 保存矩形點
                                'confidence': 1.0
                            }
                            
                            gt_annotations.append(annotation)
                            self.logger.info(f"     ✅ 成功加入YOLO GT: {class_name} (ID: {class_id}) - [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
                            
                        except (ValueError, IndexError) as e:
                            self.logger.error(f"     ❌ YOLO格式解析錯誤 行{i+1}: {e}")
                            continue
                    else:
                        self.logger.warning(f"     ⚠️  YOLO格式錯誤 行{i+1}: 需要至少5個值，得到 {len(parts)} 個")
            
            else:
                self.logger.error(f"❌ 不支持的標註格式: {file_ext}")
                return []
        
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON解析錯誤: {e}")
            return []
        except UnicodeDecodeError as e:
            self.logger.error(f"❌ 文件編碼錯誤: {e}")
            return []
        except Exception as e:
            self.logger.error(f"❌ GT載入失敗: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())
            return []
        
        # 最終結果
        self.logger.info(f"🎉 GT載入完成: 成功載入 {len(gt_annotations)} 個標註")
        
        if gt_annotations:
            self.logger.info("📋 GT標註摘要:")
            class_counts = {}
            for gt in gt_annotations:
                class_name = gt['class_name']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            for class_name, count in class_counts.items():
                self.logger.info(f"   - {class_name}: {count} 個")
        else:
            self.logger.warning("⚠️  未載入任何GT標註，請檢查:")
            self.logger.warning("   1. 標註文件格式是否正確")
            self.logger.warning("   2. 類別配置是否包含標註中的類別")
            self.logger.warning("   3. 標註坐標是否有效")
        
        return gt_annotations
    
    def _find_class_id(self, class_name: str) -> int:
        """尋找類別ID，包含精確匹配和模糊匹配"""
        if not class_name:
            self.logger.debug(f"類別名稱為空，返回-1")
            return -1
        
        self.logger.debug(f"🔍 尋找類別 '{class_name}' 的ID")
        self.logger.debug(f"   可用類別配置: {list(self.config.class_configs.keys())}")
        
        # 方法1: 精確匹配
        for cid, config in self.config.class_configs.items():
            if config.name == class_name:
                self.logger.debug(f"   ✅ 精確匹配: '{class_name}' -> ID: {cid}")
                return cid
        
        # 方法2: 模糊匹配 (忽略大小寫和空格)
        normalized_input = class_name.lower().replace(' ', '').replace('_', '').replace('-', '')
        for cid, config in self.config.class_configs.items():
            normalized_config = config.name.lower().replace(' ', '').replace('_', '').replace('-', '')
            if normalized_config == normalized_input:
                self.logger.debug(f"模糊匹配: '{class_name}' -> '{config.name}' (ID: {cid})")
                return cid
        
        # 方法3: 智能映射匹配
        if class_name in LABEL_ALIASES:
            target_name = LABEL_ALIASES[class_name]
            for cid, config in self.config.class_configs.items():
                if config.name == target_name:
                    self.logger.info(f"🎯 智能映射匹配: '{class_name}' -> '{target_name}' (ID: {cid})")
                    return cid
        
        # 方法4: 包含匹配
        for cid, config in self.config.class_configs.items():
            if class_name.lower() in config.name.lower() or config.name.lower() in class_name.lower():
                self.logger.debug(f"包含匹配: '{class_name}' -> '{config.name}' (ID: {cid})")
                return cid
        
        # 方法5: 如果沒有配置類別，創建新類別
        if not self.config.class_configs:
            # 使用哈希創建穩定的ID
            class_id = hash(class_name) % 1000
            self.logger.info(f"創建新類別: '{class_name}' -> ID: {class_id}")
            return class_id
        
        # 記錄可用類別
        available_classes = [config.name for config in self.config.class_configs.values()]
        self.logger.warning(f"找不到類別匹配: '{class_name}', 可用類別: {available_classes}")
        return -1
    
    def _get_class_name(self, class_id: int) -> str:
        """根據類別ID獲取類別名稱"""
        if class_id in self.config.class_configs:
            return self.config.class_configs[class_id].name
        else:
            return f'class_{class_id}'
    
    def _calculate_metrics(self, predictions: List[Dict], gt_annotations: List[Dict]) -> Dict[str, Any]:
        """計算評估指標 (Simple Tool功能)"""
        
        # 調試信息
        self.logger.info(f"📊 開始計算Metrics:")
        self.logger.info(f"   預測數量: {len(predictions)}")
        self.logger.info(f"   GT數量: {len(gt_annotations)}")
        self.logger.info(f"   SKLEARN可用: {SKLEARN_AVAILABLE}")
        
        if not predictions or not gt_annotations:
            self.logger.warning("   ⚠️  預測或GT為空，返回空Metrics")
            return {
                'overall': {
                    'precision': 0.0,
                    'recall': 0.0,
                    'f1_score': 0.0,
                    'tp': 0,
                    'fp': len(predictions),
                    'fn': len(gt_annotations),
                    'total_predictions': len(predictions),
                    'total_ground_truth': len(gt_annotations)
                },
                'per_class': {},
                'matches': []
            }
        
        # 準備數據
        pred_boxes = [pred['bbox'] for pred in predictions]
        pred_classes = [pred['class_id'] for pred in predictions]
        pred_scores = [pred['confidence'] for pred in predictions]
        
        gt_boxes = [gt['bbox'] for gt in gt_annotations]
        gt_classes = [gt['class_id'] for gt in gt_annotations]
        
        self.logger.debug(f"   預測類別: {pred_classes}")
        self.logger.debug(f"   GT類別: {gt_classes}")
        
        # 計算IoU匹配
        matches = self._match_predictions_to_gt(pred_boxes, pred_classes, pred_scores, 
                                              gt_boxes, gt_classes, iou_threshold=0.5)
        
        # 提取匹配結果
        tp = sum(1 for match in matches if match['matched'])
        fp = len(predictions) - tp
        fn = len(gt_annotations) - tp
        
        self.logger.info(f"   匹配結果: TP={tp}, FP={fp}, FN={fn}")
        
        # 計算基本指標
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        self.logger.info(f"   整體指標: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}")
        
        # 按類別計算指標 - 只處理 CLASS_NAMES 中的類別
        class_metrics = {}
        all_classes = set(pred_classes + gt_classes)
        
        # 過濾只保留 CLASS_NAMES 中的類別
        for class_id in all_classes:
            class_name = self.config.class_configs.get(class_id, ClassConfig(f'class_{class_id}')).name
            if class_name not in CLASS_NAMES:
                self.logger.debug(f"跳過不在 CLASS_NAMES 中的類別: {class_name}")
                continue
            
            # 收集該類別的預測和GT
            class_predictions = [pred for pred in predictions if pred['class_id'] == class_id]
            class_gt = [gt for gt in gt_annotations if gt['class_id'] == class_id]
            
            # 使用與圖像級別相同的計算邏輯
            if not class_predictions and not class_gt:
                continue  # 跳過沒有預測也沒有GT的類別
                
            if not class_predictions:
                # 沒有預測，全部為FN
                class_tp, class_fp, class_fn = 0, 0, len(class_gt)
            elif not class_gt:
                # 沒有GT，全部為FP
                class_tp, class_fp, class_fn = 0, len(class_predictions), 0
            else:
                # 使用改進的匹配算法計算（與圖像級別相同）
                class_pred_boxes = [pred['bbox'] for pred in class_predictions]
                class_pred_classes = [pred['class_id'] for pred in class_predictions]
                class_pred_scores = [pred['confidence'] for pred in class_predictions]
                
                class_gt_boxes = [gt['bbox'] for gt in class_gt]
                class_gt_classes = [gt['class_id'] for gt in class_gt]
                
                # 使用improved匹配算法
                class_matches = self._improved_match_predictions_to_gt(
                    class_pred_boxes, class_pred_classes, class_pred_scores,
                    class_gt_boxes, class_gt_classes, iou_threshold=0.5
                )
                
                # 計算TP, FP, FN
                class_tp = sum(1 for match in class_matches if match['matched'])
                class_fp = len(class_predictions) - class_tp
                class_fn = len(class_gt) - class_tp
            
            # 計算指標
            class_precision = class_tp / (class_tp + class_fp) if (class_tp + class_fp) > 0 else 0.0
            class_recall = class_tp / (class_tp + class_fn) if (class_tp + class_fn) > 0 else 0.0
            class_f1 = 2 * class_precision * class_recall / (class_precision + class_recall) if (class_precision + class_recall) > 0 else 0.0
            
            class_metrics[class_name] = {
                'precision': class_precision,
                'recall': class_recall,
                'f1_score': class_f1,
                'tp': class_tp,
                'fp': class_fp,
                'fn': class_fn,
                'gt_count': len(class_gt),
                'pred_count': len(class_predictions)
            }
        
        metrics_result = {
            'overall': {
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'total_predictions': len(predictions),
                'total_ground_truth': len(gt_annotations)
            },
            'per_class': class_metrics,
            'matches': matches
        }
        
        self.logger.info(f"✅ Metrics計算完成，返回結果")
        return metrics_result
    
    def _match_predictions_to_gt(self, pred_boxes: List[List[float]], pred_classes: List[int], pred_scores: List[float],
                                gt_boxes: List[List[float]], gt_classes: List[int], iou_threshold: float = 0.5) -> List[Dict]:
        """匹配預測到GT標註"""
        matches = []
        used_gt = set()
        
        # 按置信度排序
        sorted_indices = sorted(range(len(pred_scores)), key=lambda i: pred_scores[i], reverse=True)
        
        for pred_idx in sorted_indices:
            pred_box = pred_boxes[pred_idx]
            pred_class = pred_classes[pred_idx]
            pred_score = pred_scores[pred_idx]
            
            best_iou = 0.0
            best_gt_idx = -1
            
            for gt_idx, (gt_box, gt_class) in enumerate(zip(gt_boxes, gt_classes)):
                if gt_idx in used_gt or gt_class != pred_class:
                    continue
                
                iou = self._calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            matched = best_iou >= iou_threshold and best_gt_idx >= 0
            if matched:
                used_gt.add(best_gt_idx)
            
            matches.append({
                'pred_idx': pred_idx,
                'gt_idx': best_gt_idx if matched else -1,
                'iou': best_iou,
                'matched': matched,
                'class_id': pred_class,
                'confidence': pred_score
            })
        
        return matches
    
    def _improved_match_predictions_to_gt(self, pred_boxes: List[List[float]], pred_classes: List[int], pred_scores: List[float],
                                         gt_boxes: List[List[float]], gt_classes: List[int], iou_threshold: float = 0.5) -> List[Dict]:
        """改進的預測到GT匹配算法 - 適用於大數量、多類別情況"""
        
        # 處理邊界情況
        if not pred_boxes or not gt_boxes:
            return [{'pred_idx': i, 'gt_idx': -1, 'iou': 0.0, 'matched': False, 
                    'class_id': pred_classes[i], 'confidence': pred_scores[i]} 
                   for i in range(len(pred_boxes))]
        
        matches = []
        used_gt = set()
        
        # 計算所有預測與GT之間的IoU矩陣（只有相同類別的才計算）
        iou_matrix = []
        valid_pairs = []
        
        for pred_idx, (pred_box, pred_class, pred_score) in enumerate(zip(pred_boxes, pred_classes, pred_scores)):
            pred_ious = []
            pred_pairs = []
            
            for gt_idx, (gt_box, gt_class) in enumerate(zip(gt_boxes, gt_classes)):
                if pred_class == gt_class:  # 只考慮相同類別
                    iou = self._calculate_iou(pred_box, gt_box)
                    pred_ious.append(iou)
                    pred_pairs.append(gt_idx)
                else:
                    pred_ious.append(0.0)
                    pred_pairs.append(-1)
            
            iou_matrix.append(pred_ious)
            valid_pairs.append(pred_pairs)
        
        # 按置信度排序進行貪心匹配（但考慮IoU閾值）
        sorted_indices = sorted(range(len(pred_scores)), key=lambda i: pred_scores[i], reverse=True)
        
        for pred_idx in sorted_indices:
            pred_class = pred_classes[pred_idx]
            pred_score = pred_scores[pred_idx]
            
            best_iou = 0.0
            best_gt_idx = -1
            
            # 找到最佳匹配的GT（相同類別且未被使用）
            for gt_idx in range(len(gt_boxes)):
                if (gt_idx in used_gt or 
                    gt_classes[gt_idx] != pred_class or
                    valid_pairs[pred_idx][gt_idx] == -1):
                    continue
                
                iou = iou_matrix[pred_idx][gt_idx]
                if iou > best_iou and iou >= iou_threshold:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            # 記錄匹配結果
            matched = best_gt_idx >= 0
            if matched:
                used_gt.add(best_gt_idx)
            
            matches.append({
                'pred_idx': pred_idx,
                'gt_idx': best_gt_idx if matched else -1,
                'iou': best_iou,
                'matched': matched,
                'class_id': pred_class,
                'confidence': pred_score
            })
        
        # 記錄匹配統計
        total_matches = sum(1 for m in matches if m['matched'])
        self.logger.debug(f"   匹配統計: {total_matches}/{len(pred_boxes)} 個預測成功匹配到GT")
        
        return matches
    
    def _image_to_base64(self, image_path: str) -> Optional[str]:
        """圖像轉base64 (Simple Tool功能)"""
        try:
            import base64
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            self.logger.error(f"轉換base64失敗 {image_path}: {e}")
            return None
    
    def _mask_to_polygon(self, mask: np.ndarray) -> List[List[float]]:
        """將 mask 轉換為多邊形點列表 (Simple Tool功能)"""
        try:
            # 使用 cv2.findContours 找到輪廓
            if mask.dtype != np.uint8:
                mask_uint8 = (mask * 255).astype(np.uint8)
            else:
                mask_uint8 = mask.astype(np.uint8)

            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return []

            # 取最大的輪廓
            largest_contour = max(contours, key=cv2.contourArea)

            # 簡化輪廓，減少點數
            epsilon = 0.005 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

            # 轉換為點列表
            points = []
            for point in simplified_contour:
                x, y = point[0]
                points.append([float(x), float(y)])

            return points

        except Exception as e:
            self.logger.warning(f"Mask 轉換多邊形失敗: {e}, 使用 bbox 替代")
            return []
    
    def _generate_mask_from_bbox(self, bbox: List[float], img_width: int, img_height: int) -> List[List[float]]:
        """從邊界框生成mask多邊形點 (Simple Tool功能)"""
        x1, y1, x2, y2 = bbox

        # 應用膨脹
        expansion = 5  # 預設擴展像素
        x1 = max(0, x1 - expansion)
        y1 = max(0, y1 - expansion)
        x2 = min(img_width, x2 + expansion)
        y2 = min(img_height, y2 + expansion)

        # 生成矩形mask的四個角點
        points = [
            [float(x1), float(y1)],  # 左上
            [float(x2), float(y1)],  # 右上
            [float(x2), float(y2)],  # 右下
            [float(x1), float(y2)]   # 左下
        ]

        return points
    
    def _load_models(self):
        """載入YOLO模型"""
        
        # 載入檢測模型
        if self.config.detection_model_path and os.path.exists(self.config.detection_model_path):
            try:
                self.detection_model = YOLO(self.config.detection_model_path)
                self.logger.info(f"載入檢測模型: {self.config.detection_model_path}")
            except Exception as e:
                self.logger.error(f"載入檢測模型失敗: {e}")
        
        # 載入分割模型
        if self.config.segmentation_model_path and os.path.exists(self.config.segmentation_model_path):
            try:
                self.segmentation_model = YOLO(self.config.segmentation_model_path)
                self.logger.info(f"載入分割模型: {self.config.segmentation_model_path}")
            except Exception as e:
                self.logger.error(f"載入分割模型失敗: {e}")
        
        # 至少需要一個模型
        if not self.detection_model and not self.segmentation_model:
            raise ValueError("至少需要指定一個有效的模型路徑")
        
        # 驗證類別順序一致性
        self.logger.info("🔍 驗證類別順序一致性...")
        model_to_validate = self.detection_model or self.segmentation_model
        if model_to_validate:
            model_path = self.config.detection_model_path or self.config.segmentation_model_path
            is_valid = validate_class_order(model_path, self.config.class_configs)
            if not is_valid:
                self.logger.warning("⚠️  類別順序驗證失敗！這可能導致預測與GT不匹配。")
                self.logger.warning("   建議檢查CLASS_NAMES是否與模型訓練時的類別順序一致。")
            else:
                self.logger.info("✅ 類別順序驗證通過")
    
    def _setup_sahi(self):
        """設置SAHI - 優先使用分割模型以支持mask"""
        if not SAHI_AVAILABLE:
            self.logger.warning("SAHI不可用，跳過SAHI設置")
            return
        
        # 優先使用分割模型以獲得mask支持，然後是檢測模型
        model_path = self.config.segmentation_model_path or self.config.detection_model_path
        model_type = "分割" if self.config.segmentation_model_path else "檢測"
        
        # 計算最低confidence閾值作為SAHI初始閾值
        min_conf = min([config.conf_threshold for config in self.config.class_configs.values()]) if self.config.class_configs else self.config.global_conf
        
        try:
            self.sahi_model = AutoDetectionModel.from_pretrained(
                model_type='ultralytics',
                model_path=model_path,
                confidence_threshold=min_conf,  # 使用最低閾值，在後處理中按類別過濾
                device=self.config.device
            )
            self.logger.info(f"SAHI模型初始化完成 (使用{model_type}模型: {Path(model_path).name})")
            self.logger.info(f"SAHI初始confidence閾值: {min_conf:.3f} (將在後處理中按類別調整)")
        except Exception as e:
            self.logger.error(f"SAHI設置失敗: {e}")
            self.config.enable_sahi = False
    
    def _setup_converter(self):
        """設置標註轉換器"""
        if not CONVERTER_AVAILABLE:
            self.logger.warning("標註轉換器不可用")
            return
        
        self.converter = AnnotationConverterV2(
            input_dir="",  # 動態設置
            output_dir="",  # 動態設置
            input_format="auto",
            output_format="yolo"
        )
        self.format_detector = FormatDetector()
    
    def predict_single_image(self, 
                           image_path: str,
                           annotation_path: str = None,
                           output_dir: str = None,
                           task_type: str = "both") -> Dict[str, Any]:
        """
        單張圖像推理
        
        Args:
            image_path: 圖像路徑
            annotation_path: 標註路徑（可選）
            output_dir: 輸出目錄
            task_type: 任務類型 ("detection", "segmentation", "both")
        
        Returns:
            推理結果字典
        """
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"圖像不存在: {image_path}")
        
        # 載入圖像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"無法載入圖像: {image_path}")
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        results = {}
        
        # 處理標註轉換和載入GT
        gt_annotations = []
        if annotation_path and os.path.exists(annotation_path):
            converted_annotations = self._convert_annotations(annotation_path, image_path)
            results['converted_annotations'] = converted_annotations
            
            # 載入GT標註用於Metrics計算
            gt_annotations = self._load_gt_annotations(annotation_path, image.shape)
            results['ground_truth'] = gt_annotations
        
        # 檢測任務
        if task_type in ["detection", "both"] and self.detection_model:
            detection_results = self._run_detection(image_path, image)
            results['detection'] = detection_results
        
        # 分割任務
        if task_type in ["segmentation", "both"] and self.segmentation_model:
            segmentation_results = self._run_segmentation(image_path, image)
            results['segmentation'] = segmentation_results
        
        # SAHI推理
        if self.config.enable_sahi:
            sahi_results = self._run_sahi(image_path)
            results['sahi'] = sahi_results
        
        # 計算Metrics(如果有GT)
        self.logger.info(f"📈 檢查Metrics計算條件:")
        self.logger.info(f"   GT標註數量: {len(gt_annotations)}")
        
        if gt_annotations:
            # 獲取預測結果用於Metrics計算 - 使用統一收集邏輯
            all_predictions = self._collect_all_predictions(results)
            
            if all_predictions:
                self.logger.info(f"🎯 計算Metrics: {len(all_predictions)} 個預測 vs {len(gt_annotations)} 個GT")
                metrics = self._calculate_metrics(all_predictions, gt_annotations)
                results['metrics'] = metrics
                
                # 詳細的Metrics輸出
                if metrics and 'overall' in metrics:
                    overall = metrics['overall']
                    self.logger.info(f"📊 整體Metrics結果:")
                    self.logger.info(f"   Precision: {overall.get('precision', 0):.3f}")
                    self.logger.info(f"   Recall: {overall.get('recall', 0):.3f}")
                    self.logger.info(f"   F1-Score: {overall.get('f1_score', 0):.3f}")
                    self.logger.info(f"   TP: {overall.get('tp', 0)}, FP: {overall.get('fp', 0)}, FN: {overall.get('fn', 0)}")
                else:
                    self.logger.warning("⚠️  Metrics計算失敗或返回空結果")
            else:
                self.logger.warning("⚠️  沒有預測結果，無法計算Metrics")
        
        # 保存結果
        if output_dir:
            self._save_results(results, image_path, output_dir, annotation_path)
        
        # 更新統計
        self._update_stats(results)
        
        # 如果是單張圖像處理且有輸出目錄，生成CSV報告
        if output_dir and hasattr(self, 'batch_image_data') and self.batch_image_data:
            self.logger.info("📊 生成單張圖像CSV報告...")
            self.generate_csv_reports(output_dir)
        
        return results
    
    def _run_detection(self, image_path: str, image: np.ndarray) -> Dict[str, Any]:
        """運行檢測"""
        
        results = self.detection_model(
            image_path,
            conf=self.config.global_conf,
            iou=self.config.iou_threshold,
            imgsz=self.config.img_size,
            max_det=self.config.max_det,
            device=self.config.device
        )[0]
        
        detections = []
        
        if results.boxes is not None:
            boxes = results.boxes.xyxy.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            
            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                if cls_id in self.config.class_configs:
                    class_config = self.config.class_configs[cls_id]
                    
                    # 檢查類別特定閾值
                    if conf >= class_config.conf_threshold and class_config.enabled:
                        detection = {
                            'id': i,
                            'class_id': int(cls_id),
                            'class_name': class_config.name,
                            'confidence': float(conf),
                            'bbox': box.tolist(),
                            'area': float((box[2] - box[0]) * (box[3] - box[1])),
                            'mask': None  # 檢測模式下沒有mask
                        }
                        detections.append(detection)
        
        # 應用Simple Tool的處理步驟
        detections = self._apply_simple_tool_processing(detections)
        
        return {
            'detections': detections,
            'image_shape': image.shape,
            'model_info': {
                'name': self.detection_model.model_name if hasattr(self.detection_model, 'model_name') else 'YOLO',
                'task': 'detection'
            }
        }
    
    def _run_segmentation(self, image_path: str, image: np.ndarray) -> Dict[str, Any]:
        """運行分割"""
        
        results = self.segmentation_model(
            image_path,
            conf=self.config.global_conf,
            iou=self.config.iou_threshold,
            imgsz=self.config.img_size,
            max_det=self.config.max_det,
            device=self.config.device
        )[0]
        
        segments = []
        
        if results.masks is not None:
            masks = results.masks.data.cpu().numpy()
            boxes = results.boxes.xyxy.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            
            for i, (mask, box, conf, cls_id) in enumerate(zip(masks, boxes, confidences, class_ids)):
                if cls_id in self.config.class_configs:
                    class_config = self.config.class_configs[cls_id]
                    
                    if conf >= class_config.conf_threshold and class_config.enabled:
                        # 計算遮罩面積
                        mask_area = np.sum(mask)
                        
                        segment = {
                            'id': i,
                            'class_id': int(cls_id),
                            'class_name': class_config.name,
                            'confidence': float(conf),
                            'bbox': box.tolist(),
                            'mask': mask.astype(np.uint8),
                            'mask_area': float(mask_area),
                            'area': float(mask_area),  # 統一area字段供Simple Tool使用
                            'bbox_area': float((box[2] - box[0]) * (box[3] - box[1]))
                        }
                        segments.append(segment)
        
        # 應用Simple Tool的處理步驟
        segments = self._apply_simple_tool_processing(segments)
        
        return {
            'segments': segments,
            'image_shape': image.shape,
            'model_info': {
                'name': self.segmentation_model.model_name if hasattr(self.segmentation_model, 'model_name') else 'YOLO',
                'task': 'segmentation'
            }
        }
    
    def _run_sahi(self, image_path: str) -> Dict[str, Any]:
        """運行SAHI切片推理 - 支持mask檢測"""
        
        if not self.sahi_model:
            return {}
        
        try:
            # 載入原始圖像用於mask處理
            original_image = cv2.imread(image_path)
            if original_image is None:
                self.logger.error(f"無法載入SAHI圖像: {image_path}")
                return {}
            
            # 運行SAHI檢測
            result = get_sliced_prediction(
                image=image_path,
                detection_model=self.sahi_model,
                slice_height=self.config.slice_height,
                slice_width=self.config.slice_width,
                overlap_height_ratio=self.config.overlap_height_ratio,
                overlap_width_ratio=self.config.overlap_width_ratio,
                postprocess_match_threshold=self.config.postprocess_match_threshold,
                auto_slice_resolution=self.config.auto_slice_resolution,
                perform_standard_pred=self.config.perform_standard_pred,
                postprocess_type=self.config.postprocess_type,
                postprocess_class_agnostic=self.config.postprocess_class_agnostic,
                exclude_classes_by_name=self.config.exclude_classes_by_name,
                exclude_classes_by_id=self.config.exclude_classes_by_id,
                no_standard_prediction=self.config.no_standard_prediction,
                no_sliced_prediction=self.config.no_sliced_prediction,
                export_pickle=self.config.export_pickle,
                export_crop=self.config.export_crop
            )
            
            sahi_detections = []
            for detection in result.object_prediction_list:
                # 獲取基本檢測信息
                class_id = detection.category.id
                class_name = detection.category.name
                confidence = detection.score.value
                bbox = detection.bbox.to_xyxy()
                
                # 檢查類別配置和閾值
                if class_id in self.config.class_configs:
                    class_config = self.config.class_configs[class_id]
                    if confidence < class_config.conf_threshold or not class_config.enabled:
                        continue  # 跳過不符合類別特定閾值的檢測
                    class_name = class_config.name  # 使用配置中的名稱
                
                sahi_detection = {
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': confidence,
                    'bbox': bbox,
                    'area': detection.bbox.area,
                    'source': 'sahi'
                }
                
                # 如果有分割模型，為SAHI檢測生成mask
                if self.segmentation_model and hasattr(self, 'segmentation_model'):
                    try:
                        mask = self._generate_sahi_mask(original_image, bbox, class_id)
                        if mask is not None:
                            sahi_detection['mask'] = mask
                            sahi_detection['mask_area'] = float(np.sum(mask))
                            self.logger.debug(f"✅ SAHI生成mask: {class_name}")
                    except Exception as e:
                        self.logger.debug(f"⚠️ SAHI mask生成失敗: {e}")
                
                # 嘗試獲取SAHI原生mask (如果存在)
                elif hasattr(detection, 'mask') and detection.mask is not None:
                    try:
                        # 將SAHI的mask轉換為我們的格式
                        mask = detection.mask.bool_mask
                        if mask is not None:
                            sahi_detection['mask'] = mask.astype(np.uint8)
                            sahi_detection['mask_area'] = float(np.sum(mask))
                            self.logger.debug(f"✅ SAHI原生mask: {class_name}")
                    except Exception as e:
                        self.logger.debug(f"⚠️ SAHI原生mask處理失敗: {e}")
                
                sahi_detections.append(sahi_detection)
            
            self.logger.info(f"✅ SAHI處理完成: {len(sahi_detections)} 個檢測結果")
            
            return {
                'sahi_detections': sahi_detections,
                'slice_info': {
                    'slice_height': self.config.slice_height,
                    'slice_width': self.config.slice_width,
                    'overlap_height_ratio': self.config.overlap_height_ratio,
                    'overlap_width_ratio': self.config.overlap_width_ratio,
                    'total_detections': len(sahi_detections)
                }
            }
            
        except Exception as e:
            self.logger.error(f"SAHI推理失敗: {e}")
            import traceback
            self.logger.debug(f"詳細錯誤: {traceback.format_exc()}")
            return {}
    
    def _generate_sahi_mask(self, image: np.ndarray, bbox: List[float], class_id: int) -> Optional[np.ndarray]:
        """為SAHI檢測結果生成mask
        
        Args:
            image: 原始圖像
            bbox: 檢測框 [x1, y1, x2, y2]
            class_id: 類別ID
            
        Returns:
            mask: 二值mask或None
        """
        try:
            if not self.segmentation_model:
                return None
            
            # 擴展bbox以獲得更好的分割效果
            x1, y1, x2, y2 = bbox
            h, w = image.shape[:2]
            
            # 添加邊距，但不超出圖像邊界
            margin = 20
            x1 = max(0, int(x1) - margin)
            y1 = max(0, int(y1) - margin)
            x2 = min(w, int(x2) + margin)
            y2 = min(h, int(y2) + margin)
            
            # 裁剪圖像區域
            roi = image[y1:y2, x1:x2]
            if roi.size == 0:
                return None
            
            # 使用分割模型對ROI進行推理
            results = self.segmentation_model(
                roi,
                conf=self.config.global_conf,
                iou=self.config.iou_threshold,
                imgsz=self.config.img_size,
                device=self.config.device,
                verbose=False
            )[0]
            
            # 創建全圖大小的mask
            full_mask = np.zeros((h, w), dtype=np.uint8)
            
            if results.masks is not None:
                masks = results.masks.data.cpu().numpy()
                classes = results.boxes.cls.cpu().numpy().astype(int)
                confidences = results.boxes.conf.cpu().numpy()
                
                for i, (mask, cls, conf) in enumerate(zip(masks, classes, confidences)):
                    # 只使用匹配的類別和高置信度的mask
                    if cls == class_id and conf > self.config.global_conf:
                        # 將mask調整到ROI尺寸
                        roi_h, roi_w = roi.shape[:2]
                        resized_mask = cv2.resize(mask.astype(np.float32), (roi_w, roi_h))
                        
                        # 二值化mask
                        binary_mask = (resized_mask > 0.5).astype(np.uint8)
                        
                        # 將ROI mask映射回全圖
                        full_mask[y1:y2, x1:x2] = np.maximum(
                            full_mask[y1:y2, x1:x2], 
                            binary_mask
                        )
                        
                        self.logger.debug(f"✅ 為SAHI檢測生成mask: class={class_id}, conf={conf:.3f}")
                        break
            
            # 檢查mask是否有效
            if np.sum(full_mask) > 0:
                return full_mask
            else:
                return None
                
        except Exception as e:
            self.logger.debug(f"⚠️ SAHI mask生成失敗: {e}")
            return None
    
    def _convert_annotations(self, annotation_path: str, image_path: str) -> Dict[str, Any]:
        """轉換標註格式"""
        
        if not self.converter:
            return {}
        
        try:
            # 檢測標註格式
            detected_format = self.format_detector.detect_format(str(Path(annotation_path).parent))
            
            # 創建臨時目錄進行轉換
            temp_dir = Path("./temp_conversion")
            temp_dir.mkdir(exist_ok=True)
            
            # 複製文件到臨時目錄
            temp_annotation = temp_dir / Path(annotation_path).name
            temp_image = temp_dir / Path(image_path).name
            
            shutil.copy2(annotation_path, temp_annotation)
            shutil.copy2(image_path, temp_image)
            
            # 設置轉換器
            self.converter.input_dir = temp_dir
            self.converter.output_dir = temp_dir / "converted"
            self.converter.input_format = detected_format
            
            # 執行轉換
            result = self.converter.run()
            
            # 讀取轉換結果
            converted_data = {}
            converted_dir = Path(self.converter.output_dir)
            
            if converted_dir.exists():
                for converted_file in converted_dir.glob("*"):
                    if converted_file.suffix in ['.txt', '.json', '.xml']:
                        with open(converted_file, 'r', encoding='utf-8') as f:
                            converted_data[converted_file.name] = f.read()
            
            # 清理臨時目錄
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return {
                'original_format': detected_format,
                'converted_format': 'yolo',
                'conversion_result': result,
                'converted_data': converted_data
            }
            
        except Exception as e:
            self.logger.error(f"標註轉換失敗: {e}")
            return {}
    
    def _save_results(self, results: Dict[str, Any], image_path: str, output_dir: str, annotation_path: str = None):
        """保存推理結果到分離的圖像和報告文件夾
        
        Args:
            results: 推理結果字典
            image_path: 圖像路徑
            output_dir: 輸出目錄
            annotation_path: 標註文件路徑(可選)
        """
        
        output_path = Path(output_dir)
        
        # 創建分離的子目錄
        images_dir = output_path / "images"
        reports_dir = output_path / "reports"
        images_dir.mkdir(parents=True, exist_ok=True)
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        image_name = Path(image_path).stem
        
        # 保存詳細報告
        if self.config.save_predictions:
            self._save_detailed_report(results, image_path, reports_dir, image_name, annotation_path)
        
        # 保存可視化結果到images文件夾
        if self.config.save_visualizations:
            self._save_three_panel_visualization(results, image_path, images_dir, image_name, annotation_path)
    
    def _save_detailed_report(self, results: Dict[str, Any], image_path: str, reports_dir: Path, image_name: str, annotation_path: str = None):
        """保存詳細的Excel報告數據"""
        
        # 收集圖像級別的詳細數據
        image_data = self._collect_image_data(results, image_path, image_name, annotation_path)
        
        # 保存到實例變量中，用於批次處理時彙總
        if not hasattr(self, 'batch_image_data'):
            self.batch_image_data = []
        if not hasattr(self, 'batch_class_data'):
            self.batch_class_data = {}
        
        self.batch_image_data.append(image_data)
        
        # 收集類別級別數據
        if 'metrics' in results and results['metrics']:
            self._accumulate_class_data(results['metrics'], image_name)
        
        self.logger.info(f"📊 數據已收集: {image_name}")
    
    def _collect_image_data(self, results: Dict[str, Any], image_path: str, image_name: str, annotation_path: str) -> Dict[str, Any]:
        """收集圖像級別的詳細數據"""
        
        # 獲取圖像基本信息
        image = cv2.imread(image_path)
        if image is not None:
            height, width = image.shape[:2]
        else:
            height, width = 0, 0
        
        # 基本信息
        image_data = {
            'image_name': image_name,
            'image_path': image_path,
            'annotation_path': annotation_path or '',
            'image_width': width,
            'image_height': height,
            'image_area': width * height,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 總體統計
        total_detections = len(results.get('detection', {}).get('detections', []))
        total_segments = len(results.get('segmentation', {}).get('segments', []))
        total_gt = len(results.get('ground_truth', []))
        
        image_data.update({
            'total_detections': total_detections,
            'total_segmentations': total_segments,
            'total_ground_truth': total_gt,
            'total_predictions': total_detections + total_segments
        })
        
        # Metrics信息
        if 'metrics' in results and results['metrics']:
            overall = results['metrics']['overall']
            image_data.update({
                'precision': overall.get('precision', 0),
                'recall': overall.get('recall', 0),
                'f1_score': overall.get('f1_score', 0),
                'tp': overall.get('tp', 0),
                'fp': overall.get('fp', 0),
                'fn': overall.get('fn', 0),
                'accuracy': overall.get('tp', 0) / (overall.get('tp', 0) + overall.get('fp', 0) + overall.get('fn', 0)) if (overall.get('tp', 0) + overall.get('fp', 0) + overall.get('fn', 0)) > 0 else 0
            })
        else:
            image_data.update({
                'precision': 0, 'recall': 0, 'f1_score': 0,
                'tp': 0, 'fp': 0, 'fn': 0, 'accuracy': 0
            })
        
        # 各類別詳細信息
        class_details = {}
        
        # 處理檢測結果 - 只記錄 CLASS_NAMES 中的類別
        for det in results.get('detection', {}).get('detections', []):
            class_name = det['class_name']
            # 只處理 CLASS_NAMES 中定義的類別
            if class_name not in CLASS_NAMES:
                continue
            
            if class_name not in class_details:
                class_details[class_name] = {
                    'detection_count': 0, 'segmentation_count': 0,
                    'avg_confidence': 0, 'total_area': 0,
                    'avg_width': 0, 'avg_height': 0,
                    'confidences': [], 'areas': [], 'widths': [], 'heights': []
                }
            
            bbox = det['bbox']
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            area = width * height
            
            class_details[class_name]['detection_count'] += 1
            class_details[class_name]['confidences'].append(det['confidence'])
            class_details[class_name]['areas'].append(area)
            class_details[class_name]['widths'].append(width)
            class_details[class_name]['heights'].append(height)
        
        # 處理分割結果 - 只記錄 CLASS_NAMES 中的類別
        for seg in results.get('segmentation', {}).get('segments', []):
            class_name = seg['class_name']
            # 只處理 CLASS_NAMES 中定義的類別
            if class_name not in CLASS_NAMES:
                continue
                
            if class_name not in class_details:
                class_details[class_name] = {
                    'detection_count': 0, 'segmentation_count': 0,
                    'avg_confidence': 0, 'total_area': 0,
                    'avg_width': 0, 'avg_height': 0,
                    'confidences': [], 'areas': [], 'widths': [], 'heights': []
                }
            
            bbox = seg['bbox']
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            area = width * height
            
            class_details[class_name]['segmentation_count'] += 1
            class_details[class_name]['confidences'].append(seg['confidence'])
            class_details[class_name]['areas'].append(area)
            class_details[class_name]['widths'].append(width)
            class_details[class_name]['heights'].append(height)
        
        # 計算平均值
        for class_name, details in class_details.items():
            if details['confidences']:
                details['avg_confidence'] = np.mean(details['confidences'])
                details['total_area'] = np.sum(details['areas'])
                details['avg_width'] = np.mean(details['widths'])
                details['avg_height'] = np.mean(details['heights'])
                details['min_confidence'] = np.min(details['confidences'])
                details['max_confidence'] = np.max(details['confidences'])
            
            # 清理列表數據以節省空間
            del details['confidences'], details['areas'], details['widths'], details['heights']
        
        image_data['class_details'] = class_details
        
        # 保存完整的results數據用於confusion matrix計算
        image_data['detailed_results'] = results
        
        return image_data
    
    def _accumulate_class_data(self, metrics: Dict[str, Any], image_name: str):
        """累積類別級別的數據"""
        
        if 'per_class' not in metrics:
            return
        
        for class_name, class_metrics in metrics['per_class'].items():
            if class_name not in self.batch_class_data:
                self.batch_class_data[class_name] = {
                    'total_images': 0,
                    'total_gt': 0,
                    'total_pred': 0,
                    'total_tp': 0,
                    'total_fp': 0,
                    'total_fn': 0,
                    'precision_sum': 0,
                    'recall_sum': 0,
                    'f1_sum': 0,
                    'images_with_class': []
                }
            
            self.batch_class_data[class_name]['total_images'] += 1
            self.batch_class_data[class_name]['total_gt'] += class_metrics.get('gt_count', 0)
            self.batch_class_data[class_name]['total_pred'] += class_metrics.get('pred_count', 0)
            self.batch_class_data[class_name]['total_tp'] += class_metrics.get('tp', 0)
            self.batch_class_data[class_name]['total_fp'] += class_metrics.get('fp', 0)
            self.batch_class_data[class_name]['total_fn'] += class_metrics.get('fn', 0)
            self.batch_class_data[class_name]['precision_sum'] += class_metrics.get('precision', 0)
            self.batch_class_data[class_name]['recall_sum'] += class_metrics.get('recall', 0)
            self.batch_class_data[class_name]['f1_sum'] += class_metrics.get('f1_score', 0)
            self.batch_class_data[class_name]['images_with_class'].append(image_name)
    
    def generate_csv_reports(self, output_dir: str):
        """生成最終的CSV報告"""
        
        if not hasattr(self, 'batch_image_data') or not self.batch_image_data:
            self.logger.warning("沒有圖像數據可以生成CSV報告")
            return
        
        output_path = Path(output_dir) / "reports"
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 生成兩個固定名稱的CSV文件
        image_csv_path = output_path / "image_metrics.csv"
        class_csv_path = output_path / "class_metrics.csv"
        
        self._create_image_metrics_csv(image_csv_path)
        self._create_class_metrics_csv(class_csv_path)
        
        self.logger.info(f"📊 CSV報告已生成:")
        self.logger.info(f"   圖像指標: {image_csv_path}")
        self.logger.info(f"   類別指標: {class_csv_path}")
    
    def _create_image_metrics_csv(self, csv_path: Path):
        """創建圖像級別指標CSV"""
        
        csv_lines = []
        csv_lines.append("圖像名稱,圖像路徑,類別,類別的長,類別的寬,TP,FP,FN")
        
        # 處理方式：如果文件已存在，讀取現有數據並合併
        existing_data = set()
        if csv_path.exists():
            try:
                with open(csv_path, 'r', encoding='utf-8-sig') as f:
                    lines = f.readlines()
                    for line in lines[1:]:  # 跳過標題行
                        if line.strip():
                            # 提取圖像名稱作為唯一標識
                            parts = line.strip().split(',')
                            if len(parts) >= 3:
                                existing_data.add((parts[0], parts[2]))  # (圖像名稱, 類別)
            except Exception as e:
                self.logger.warning(f"讀取現有CSV失敗: {e}")
        
        # 收集新數據
        new_data = []
        for img_data in self.batch_image_data:
            image_name = img_data['image_name']
            image_path = img_data['image_path']
            
            # 獲取該圖像的類別詳細信息
            class_details = img_data.get('class_details', {})
            
            if class_details:
                for class_name, details in class_details.items():
                    # 檢查是否已存在
                    if (image_name, class_name) not in existing_data:
                        # 計算該類別在該圖像中的TP/FP/FN
                        class_tp, class_fp, class_fn = self._calculate_class_confusion_matrix(
                            img_data, class_name
                        )
                        
                        new_data.append(f"{image_name},{image_path},{class_name},"
                                      f"{details['avg_width']:.1f},{details['avg_height']:.1f},"
                                      f"{class_tp},{class_fp},{class_fn}")
            else:
                # 沒有檢測結果的圖像
                if (image_name, '無檢測結果') not in existing_data:
                    new_data.append(f"{image_name},{image_path},無檢測結果,0,0,0,0,{img_data.get('fn', 0)}")
        
        # 如果文件已存在，讀取並追加新數據
        if csv_path.exists():
            try:
                with open(csv_path, 'r', encoding='utf-8-sig') as f:
                    existing_lines = f.readlines()
                csv_lines = [line.rstrip() for line in existing_lines if line.strip()]
                csv_lines.extend(new_data)
            except Exception as e:
                self.logger.warning(f"合併現有數據失敗: {e}")
                csv_lines.extend(new_data)
        else:
            csv_lines.extend(new_data)
        
        # 寫入更新後的CSV文件
        with open(csv_path, 'w', encoding='utf-8-sig', newline='') as f:
            f.write('\n'.join(csv_lines))
        
        self.logger.info(f"📊 圖像指標已更新，新增 {len(new_data)} 條記錄")
    
    def _create_class_metrics_csv(self, csv_path: Path):
        """創建類別級別指標CSV"""
        
        csv_lines = []
        csv_lines.append("各類別名稱,TP,FP,FN,Precision,Recall,F1,類別總數,誤判率,漏判率")
        
        # 調試信息：顯示累積的類別數據
        self.logger.info("📊 Class Metrics生成 - 使用已累積的batch_class_data:")
        self.logger.info(f"   累積的類別數量: {len(self.batch_class_data)}")
        for class_name, data in self.batch_class_data.items():
            self.logger.info(f"   {class_name}: TP={data['total_tp']}, FP={data['total_fp']}, FN={data['total_fn']}")
        
        # 只處理 CLASS_NAMES 中定義的類別
        self.logger.info(f"📊 只處理 CLASS_NAMES 中的類別: {CLASS_NAMES}")
        
        # 初始化 class_stats，只包含 CLASS_NAMES 中的類別
        class_stats = {}
        for class_name in CLASS_NAMES:
            if class_name in self.batch_class_data:
                # 使用已累積的數據
                data = self.batch_class_data[class_name]
                class_stats[class_name] = {
                    'tp': data['total_tp'],
                    'fp': data['total_fp'], 
                    'fn': data['total_fn']
                }
                self.logger.info(f"   ✅ {class_name}: TP={data['total_tp']}, FP={data['total_fp']}, FN={data['total_fn']}")
            else:
                # CLASS_NAMES中的類別但從未被預測，計算純FN
                pure_fn = 0
                for img_data in self.batch_image_data:
                    if 'detailed_results' in img_data and 'ground_truth' in img_data['detailed_results']:
                        gt_count = sum(1 for gt in img_data['detailed_results']['ground_truth'] 
                                     if gt.get('class_name') == class_name)
                        pure_fn += gt_count
                
                class_stats[class_name] = {
                    'tp': 0,
                    'fp': 0,
                    'fn': pure_fn
                }
                if pure_fn > 0:
                    self.logger.info(f"   📍 從未被預測的類別 {class_name}: 純FN={pure_fn}")
                else:
                    self.logger.info(f"   ⭕ 未出現的類別 {class_name}: 全零")
        
        for class_name, stats in class_stats.items():
            tp = stats['tp']
            fp = stats['fp']
            fn = stats['fn']
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            total_count = tp + fn  # 實際存在的該類別數量
            false_positive_rate = fp / (fp + tp) if (fp + tp) > 0 else 0  # 誤判率
            false_negative_rate = fn / (fn + tp) if (fn + tp) > 0 else 0  # 漏判率
            
            csv_lines.append(f"{class_name},{tp},{fp},{fn},"
                           f"{precision:.3f},{recall:.3f},{f1:.3f},"
                           f"{total_count},{false_positive_rate:.3f},{false_negative_rate:.3f}")
        
        # 添加整體統計
        if class_stats:
            total_tp = sum(stats['tp'] for stats in class_stats.values())
            total_fp = sum(stats['fp'] for stats in class_stats.values())
            total_fn = sum(stats['fn'] for stats in class_stats.values())
            
            overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
            overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
            overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
            
            overall_total = total_tp + total_fn
            overall_fpr = total_fp / (total_fp + total_tp) if (total_fp + total_tp) > 0 else 0
            overall_fnr = total_fn / (total_fn + total_tp) if (total_fn + total_tp) > 0 else 0
            
            csv_lines.append(f"整體平均,{total_tp},{total_fp},{total_fn},"
                           f"{overall_precision:.3f},{overall_recall:.3f},{overall_f1:.3f},"
                           f"{overall_total},{overall_fpr:.3f},{overall_fnr:.3f}")
        
        # 每次都完全重寫類別指標文件（因為是累積統計）
        with open(csv_path, 'w', encoding='utf-8-sig', newline='') as f:
            f.write('\n'.join(csv_lines))
        
        self.logger.info(f"📊 類別指標已更新，包含 {len(class_stats)} 個類別")
    
    def _calculate_class_confusion_matrix(self, img_data: Dict[str, Any], class_name: str) -> Tuple[int, int, int]:
        """計算特定類別在特定圖像中的TP/FP/FN - 改進版本"""
        
        # 獲取該圖像的預測和GT數據
        predictions = []
        ground_truths = []
        
        # 收集預測數據（檢測+分割）
        for result_type in ['detection', 'segmentation']:
            if result_type in img_data and 'detailed_results' in img_data:
                results = img_data['detailed_results'].get(result_type, {})
                if result_type == 'detection' and 'detections' in results:
                    predictions.extend([p for p in results['detections'] if p['class_name'] == class_name])
                elif result_type == 'segmentation' and 'segments' in results:
                    predictions.extend([p for p in results['segments'] if p['class_name'] == class_name])
        
        # 收集GT數據
        ground_truths = []
        if 'detailed_results' in img_data:
            detailed_results = img_data['detailed_results']
            
            # 檢查GT數據位置
            if 'ground_truth' in detailed_results:
                all_gt = detailed_results['ground_truth']
                ground_truths = [gt for gt in all_gt if gt.get('class_name') == class_name]
                self.logger.debug(f"   找到 {len(ground_truths)} 個 {class_name} 的GT標註")
            else:
                self.logger.debug(f"   detailed_results 中沒有 ground_truth 鍵，可用鍵: {list(detailed_results.keys())}")
        else:
            self.logger.debug(f"   img_data 中沒有 detailed_results")
        
        # 處理邊界情況
        if not predictions and not ground_truths:
            return 0, 0, 0
        
        if not predictions:
            return 0, 0, len(ground_truths)  # 沒有預測，全部為FN
        
        if not ground_truths:
            return 0, len(predictions), 0  # 沒有GT，全部為FP
        
        # 改進版本：使用實際IoU匹配
        try:
            # 準備數據進行匹配
            pred_boxes = [pred['bbox'] for pred in predictions]
            pred_classes = [pred['class_id'] for pred in predictions]
            pred_scores = [pred['confidence'] for pred in predictions]
            
            gt_boxes = [gt['bbox'] for gt in ground_truths]
            gt_classes = [gt['class_id'] for gt in ground_truths]
            
            # 調試信息：檢查類別ID匹配
            if pred_classes and gt_classes:
                self.logger.debug(f"   類別匹配檢查 - {class_name}:")
                self.logger.debug(f"     預測class_ids: {set(pred_classes)}")
                self.logger.debug(f"     GT class_ids: {set(gt_classes)}")
                
                # 檢查是否有ID不匹配
                all_pred_ids = set(pred_classes)
                all_gt_ids = set(gt_classes)
                if all_pred_ids != all_gt_ids:
                    self.logger.warning(f"     ⚠️  類別ID不匹配: pred={all_pred_ids}, gt={all_gt_ids}")
            
            # 使用改進的匹配算法
            matches = self._improved_match_predictions_to_gt(
                pred_boxes, pred_classes, pred_scores,
                gt_boxes, gt_classes, iou_threshold=0.5
            )
            
            # 計算TP, FP, FN
            tp = sum(1 for match in matches if match['matched'])
            fp = len(predictions) - tp
            fn = len(ground_truths) - tp
            
            return tp, fp, fn
            
        except Exception as e:
            self.logger.warning(f"IoU匹配失敗，使用基本計算: {e}")
            # 如果匹配失敗，回退到基本方法
            pred_count = len(predictions)
            gt_count = len(ground_truths)
            
            # 更保守的估算
            tp = min(pred_count, gt_count)
            fp = max(0, pred_count - gt_count)
            fn = max(0, gt_count - pred_count)
            
            return tp, fp, fn
    
    def _calculate_class_overall_stats(self) -> Dict[str, Dict[str, int]]:
        """計算各類別的整體統計"""
        
        class_stats = {}
        
        # 初始化類別統計
        for class_name in self.batch_class_data.keys():
            class_stats[class_name] = {'tp': 0, 'fp': 0, 'fn': 0}
        
        # 遍歷所有圖像，累積各類別的統計
        for img_data in self.batch_image_data:
            class_details = img_data.get('class_details', {})
            
            for class_name in class_stats.keys():
                tp, fp, fn = self._calculate_class_confusion_matrix(img_data, class_name)
                class_stats[class_name]['tp'] += tp
                class_stats[class_name]['fp'] += fp
                class_stats[class_name]['fn'] += fn
        
        return class_stats
    
    def _make_serializable(self, obj):
        """將對象轉換為JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(v) for v in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        else:
            return obj
    
    def _save_visualizations(self, results: Dict[str, Any], image_path: str, 
                           output_path: Path, image_name: str):
        """保存可視化結果"""
        
        # 載入原始圖像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 創建可視化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'YOLO推理結果: {image_name}', fontsize=16)
        
        # 原始圖像
        axes[0, 0].imshow(image_rgb)
        axes[0, 0].set_title('原始圖像')
        axes[0, 0].axis('off')
        
        # 檢測結果
        if 'detection' in results:
            det_img = image_rgb.copy()
            self._draw_detections(det_img, results['detection']['detections'])
            axes[0, 1].imshow(det_img)
            axes[0, 1].set_title(f"檢測結果 ({len(results['detection']['detections'])} 個物件)")
            axes[0, 1].axis('off')
        else:
            axes[0, 1].axis('off')
        
        # 分割結果
        if 'segmentation' in results:
            seg_img = image_rgb.copy()
            self._draw_segmentations(seg_img, results['segmentation']['segments'])
            axes[1, 0].imshow(seg_img)
            axes[1, 0].set_title(f"分割結果 ({len(results['segmentation']['segments'])} 個區域)")
            axes[1, 0].axis('off')
        else:
            axes[1, 0].axis('off')
        
        # 統計圖表
        self._draw_statistics(axes[1, 1], results)
        
        plt.tight_layout()
        viz_path = output_path / f"{image_name}_visualization.png"
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _draw_detections(self, image: np.ndarray, detections: List[Dict]):
        """繪製檢測結果 (備用方法，現在由_draw_predictions統一處理)"""
        self._draw_predictions(image, detections)
    
    def _draw_segmentations(self, image: np.ndarray, segments: List[Dict]):
        """繪製分割結果 (備用方法，現在由_draw_predictions統一處理)"""
        self._draw_predictions(image, segments)
    
    def _draw_ground_truth(self, image: np.ndarray, gt_annotations: List[Dict]):
        """繪製Ground Truth標註（邊界框+多邊形） - 只顯示 CLASS_NAMES 中的類別"""
        for gt in gt_annotations:
            bbox = gt['bbox']
            class_name = gt['class_name']
            class_id = gt['class_id']
            shape_type = gt.get('shape_type', 'unknown')
            points = gt.get('points', [])
            
            # 只繪製 CLASS_NAMES 中定義的類別
            if class_name not in CLASS_NAMES:
                continue
            
            # 使用與預測相同的類別顏色，但稍作區分
            if class_id in self.config.class_configs:
                base_color = self.config.class_configs[class_id].color
                # GT使用稍暗的版本以區分預測
                color = tuple(max(0, min(255, int(c * 0.9))) for c in base_color)
                polygon_color = tuple(max(0, min(255, int(c * 0.8))) for c in base_color)
            else:
                # 如果沒有配置，使用預設綠色
                color = (0, 255, 0)  # 綠色
                polygon_color = (0, 200, 0)  # 稍暗的綠色用於多邊形
            
            # 1. 繪製原始多邊形（如果有點數據）
            if points and len(points) >= 2:
                try:
                    # 統一處理點格式轉換
                    if isinstance(points[0], list):
                        # 格式: [[x1,y1], [x2,y2], ...]
                        polygon_points = np.array([[int(p[0]), int(p[1])] for p in points], dtype=np.int32)
                    else:
                        # 格式: [x1, y1, x2, y2, ...]
                        if len(points) >= 4 and len(points) % 2 == 0:
                            polygon_points = np.array([[int(points[i]), int(points[i+1])] 
                                                     for i in range(0, len(points), 2)], dtype=np.int32)
                        else:
                            self.logger.warning(f"     ⚠️  點數據格式錯誤: {points}")
                            polygon_points = None
                    
                    if polygon_points is not None and len(polygon_points) >= 2:
                        # 繪製多邊形輪廓
                        cv2.polylines(image, [polygon_points], True, polygon_color, 2)
                        
                        # 繪製半透明填充（只對多邊形）
                        if shape_type == 'polygon' and len(polygon_points) >= 3:
                            overlay = image.copy()
                            cv2.fillPoly(overlay, [polygon_points], polygon_color)
                            alpha = 0.15  # 更淡的填充
                            cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
                        
                        self.logger.debug(f"     🔸 繪製{shape_type}: {class_name}, {len(polygon_points)} 個點")
                
                except Exception as e:
                    self.logger.warning(f"     ⚠️  繪製多邊形失敗: {e}")
            
            # 2. 繪製邊界框 (更粗的線條)
            cv2.rectangle(image, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         color, 3)  # 更粗的線條用於GT邊界框
            
            # 3. 繪製標籤
            label = f"GT: {class_name}"
            if shape_type != 'unknown':
                label += f" ({shape_type})"
            
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 2)[0]
            
            # 標籤背景
            cv2.rectangle(image,
                         (int(bbox[0]), int(bbox[1]) - label_size[1] - 10),
                         (int(bbox[0]) + label_size[0] + 10, int(bbox[1])),
                         color, -1)
            
            # 標籤文字
            cv2.putText(image, label,
                       (int(bbox[0]) + 5, int(bbox[1]) - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
    
    def _draw_predictions(self, image: np.ndarray, predictions: List[Dict]):
        """繪製預測結果 (統一處理檢測和分割) - 只顯示 CLASS_NAMES 中的類別"""
        for pred in predictions:
            bbox = pred['bbox']
            class_id = pred['class_id']
            conf = pred['confidence']
            class_name = pred['class_name']
            
            # 只繪製 CLASS_NAMES 中定義的類別
            if class_name not in CLASS_NAMES:
                continue
            
            # 獲取類別顏色
            if class_id in self.config.class_configs:
                color = self.config.class_configs[class_id].color
            else:
                color = (255, 0, 0)  # 預設紅色
            
            # 繪製邊界框
            cv2.rectangle(image, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         color, 2)
            
            # 繪製遮罩 (如果有)
            if pred.get('mask') is not None:
                mask = pred['mask']
                if mask.shape[:2] != image.shape[:2]:
                    mask = cv2.resize(mask.astype(np.uint8), (image.shape[1], image.shape[0]))
                
                # 應用半透明遮罩
                mask_colored = np.zeros_like(image)
                mask_colored[mask > 0] = color
                alpha = 0.3
                image[mask > 0] = image[mask > 0] * (1 - alpha) + mask_colored[mask > 0] * alpha
            
            # 繪製標籤 (增大字體以保持與GT一致)
            source = pred.get('source', 'unknown')
            if source == 'sahi':
                label = f"SAHI-{class_name}: {conf:.2f}"
            else:
                label = f"{class_name}: {conf:.2f}"
            
            # 統一使用較大字體 (與GT保持一致)
            font_scale = 1.2  # 提升字體大小
            thickness = 2
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
            
            # 標籤背景
            cv2.rectangle(image,
                         (int(bbox[0]), int(bbox[1]) - label_size[1] - 10),
                         (int(bbox[0]) + label_size[0] + 10, int(bbox[1])),
                         color, -1)
            
            # 標籤文字
            cv2.putText(image, label,
                       (int(bbox[0]) + 5, int(bbox[1]) - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), thickness)
    
    def _collect_all_predictions(self, results: Dict[str, Any]) -> List[Dict]:
        """統一收集所有預測結果，優先使用SAHI結果
        
        Args:
            results: 包含各種預測結果的字典
            
        Returns:
            List[Dict]: 統一格式的預測結果列表
        """
        all_predictions = []
        
        # 優先級順序：SAHI > 普通檢測/分割
        # 如果啟用了SAHI且有結果，則主要使用SAHI結果
        if self.config.enable_sahi and 'sahi' in results and results['sahi'].get('sahi_detections'):
            self.logger.info("🎯 使用SAHI預測結果進行可視化")
            sahi_detections = results['sahi']['sahi_detections']
            
            for det in sahi_detections:
                # 統一SAHI結果格式
                if det.get('class_name') in CLASS_NAMES:  # 只包含CLASS_NAMES中的類別
                    prediction = {
                        'class_id': det.get('class_id', -1),
                        'class_name': det.get('class_name', ''),
                        'confidence': det.get('confidence', 0.0),
                        'bbox': det.get('bbox', []),
                        'source': 'sahi'  # 標記來源
                    }
                    all_predictions.append(prediction)
            
            self.logger.info(f"✅ 收集到 {len(all_predictions)} 個SAHI預測結果")
        
        else:
            # 如果沒有SAHI結果，使用普通檢測和分割結果
            self.logger.info("📝 使用普通檢測/分割結果進行可視化")
            
            # 普通檢測結果
            if 'detection' in results and results['detection'].get('detections'):
                filtered_detections = [det for det in results['detection']['detections'] 
                                     if det.get('class_name') in CLASS_NAMES]
                for det in filtered_detections:
                    det['source'] = 'detection'  # 標記來源
                all_predictions.extend(filtered_detections)
                self.logger.info(f"✅ 收集到 {len(filtered_detections)} 個檢測結果")
            
            # 普通分割結果
            if 'segmentation' in results and results['segmentation'].get('segments'):
                filtered_segments = [seg for seg in results['segmentation']['segments'] 
                                   if seg.get('class_name') in CLASS_NAMES]
                for seg in filtered_segments:
                    seg['source'] = 'segmentation'  # 標記來源
                all_predictions.extend(filtered_segments)
                self.logger.info(f"✅ 收集到 {len(filtered_segments)} 個分割結果")
        
        self.logger.info(f"🎯 總共收集到 {len(all_predictions)} 個預測結果用於可視化")
        return all_predictions
    
    def _save_three_panel_visualization(self, results: Dict[str, Any], image_path: str, 
                                       output_path: Path, image_name: str, annotation_path: str = None):
        """保存三面板可視化: 原圖+GT+預測結果"""
        
        # 載入原始圖像
        image = cv2.imread(image_path)
        if image is None:
            self.logger.error(f"無法載入圖像: {image_path}")
            return
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        h, w = image.shape[:2]
        
        # 設置中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 創建三面板圖形
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 面板1: 原始圖像
        axes[0].imshow(image_rgb)
        axes[0].set_title('原始圖像', fontsize=14, fontweight='bold')
        axes[0].axis('off')
        
        # 面板2: Ground Truth (如果有標註)
        gt_img = image_rgb.copy()
        if 'ground_truth' in results and results['ground_truth']:
            self._draw_ground_truth(gt_img, results['ground_truth'])
            gt_count = len(results['ground_truth'])
            axes[1].set_title(f'Ground Truth ({gt_count} 個標註)', fontsize=14, fontweight='bold')
        else:
            axes[1].set_title('Ground Truth (無標註)', fontsize=14, fontweight='bold')
        
        axes[1].imshow(gt_img)
        axes[1].axis('off')
        
        # 面板3: 預測結果
        pred_img = image_rgb.copy()
        total_predictions = 0
        
        # 收集所有預測結果，優先使用SAHI結果
        all_predictions = self._collect_all_predictions(results)
        
        if all_predictions:
            self._draw_predictions(pred_img, all_predictions)
            total_predictions = len(all_predictions)
        
        # 確定預測來源並構建標題
        pred_source = "混合" if not all_predictions else all_predictions[0].get('source', 'unknown')
        if all_predictions and len(set(p.get('source', 'unknown') for p in all_predictions)) == 1:
            source_map = {
                'sahi': 'SAHI',
                'detection': '檢測', 
                'segmentation': '分割'
            }
            pred_source = source_map.get(pred_source, pred_source)
        
        # 加入Metrics資訊(如果有)
        metrics_text = ""
        pred_title = f'預測結果-{pred_source} ({total_predictions} 個檢測)'
        
        if 'metrics' in results and results['metrics']:
            metrics = results['metrics']['overall']
            precision = metrics.get('precision', 0)
            recall = metrics.get('recall', 0)
            f1_score = metrics.get('f1_score', 0)
            tp = metrics.get('tp', 0)
            fp = metrics.get('fp', 0)
            fn = metrics.get('fn', 0)
            
            # 詳細的metrics顯示
            metrics_text = f"\nP:{precision:.3f} R:{recall:.3f} F1:{f1_score:.3f}\nTP:{tp} FP:{fp} FN:{fn}"
            pred_title += metrics_text
            
            self.logger.info(f"🎯 三視圖中顯示Metrics: P={precision:.3f}, R={recall:.3f}, F1={f1_score:.3f}")
        else:
            self.logger.warning("⚠️  沒有Metrics數據可顯示")
        
        axes[2].set_title(pred_title, fontsize=12, fontweight='bold')
        axes[2].imshow(pred_img)
        axes[2].axis('off')
        
        # 調整布局
        plt.tight_layout()
        
        # 使用原圖檔名保存(不加後綴)
        original_filename = Path(image_path).name  # 保留原始副檔名
        viz_path = output_path / original_filename
        
        plt.savefig(viz_path, dpi=200, bbox_inches='tight', facecolor='white')
        plt.close()
        
        self.logger.info(f"可視化結果已保存: {viz_path}")
    
    def _draw_statistics(self, ax, results: Dict[str, Any]):
        """繪製統計圖表 - 使用統一的預測收集邏輯"""
        
        # 使用統一的預測收集邏輯
        all_predictions = self._collect_all_predictions(results)
        
        class_counts = {}
        for pred in all_predictions:
            class_name = pred['class_name']
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        if class_counts:
            classes = list(class_counts.keys())
            counts = list(class_counts.values())
            
            bars = ax.bar(classes, counts)
            ax.set_title('類別統計')
            ax.set_ylabel('數量')
            
            # 設置顏色
            for i, (cls, bar) in enumerate(zip(classes, bars)):
                for class_id, config in self.config.class_configs.items():
                    if config.name == cls:
                        bar.set_color([c/255.0 for c in config.color])
                        break
            
            ax.tick_params(axis='x', rotation=45)
        else:
            ax.text(0.5, 0.5, '無檢測結果', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('類別統計')
    
    def _update_stats(self, results: Dict[str, Any]):
        """更新統計資料 - 使用統一的預測收集邏輯"""
        
        self.inference_stats["total_images"] += 1
        
        # 使用統一的預測收集邏輯
        all_predictions = self._collect_all_predictions(results)
        self.inference_stats["total_detections"] += len(all_predictions)
        
        # 統計各類別數量
        for pred in all_predictions:
            class_id = pred.get('class_id', -1)
            if class_id in self.inference_stats["class_counts"]:
                self.inference_stats["class_counts"][class_id] += 1
    
    def batch_predict(self, 
                     input_dir: str,
                     output_dir: str,
                     image_extensions: List[str] = None,
                     task_type: str = "both") -> Dict[str, Any]:
        """批次推理"""
        
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 找到所有圖像文件 (支持多種目錄結構)
        image_files = []
        
        # 方法1: 直接在輸入目錄中查找
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        # 方法2: 在image子目錄中查找
        if not image_files:
            image_dir = input_path / "image"
            if image_dir.exists():
                for ext in image_extensions:
                    image_files.extend(image_dir.glob(f"*{ext}"))
                    image_files.extend(image_dir.glob(f"*{ext.upper()}"))
        
        # 方法3: 在images子目錄中查找
        if not image_files:
            images_dir = input_path / "images"
            if images_dir.exists():
                for ext in image_extensions:
                    image_files.extend(images_dir.glob(f"*{ext}"))
                    image_files.extend(images_dir.glob(f"*{ext.upper()}"))
        
        if not image_files:
            raise ValueError(f"在 {input_dir} 及其子目錄中未找到圖像文件")
        
        batch_results = {}
        failed_files = []
        
        # 進度條
        with tqdm(total=len(image_files), desc="批次推理") as pbar:
            for image_file in image_files:
                try:
                    # 尋找對應的標註文件 (支持多種目錄結構)
                    annotation_file = None
                    
                    # 调试信息：显示当前的路径信息
                    self.logger.debug(f"🔍 調試路徑信息:")
                    self.logger.debug(f"   圖像文件: {image_file}")
                    self.logger.debug(f"   圖像父目錄: {image_file.parent}")
                    self.logger.debug(f"   輸入路徑: {input_path}")
                    self.logger.debug(f"   是否在根目錄: {image_file.parent == input_path}")
                    
                    # 優先在label目錄中查找（JSON優先）
                    search_paths = [
                        input_path / "label",      # 最優先：根目錄下的label文件夾
                        input_path / "labels",     # 次優先：根目錄下的labels文件夾  
                        image_file.parent,         # 圖像同目錄
                        input_path                 # 根目錄
                    ]
                    
                    for search_dir in search_paths:
                        if search_dir.exists():
                            self.logger.debug(f"   搜索目錄: {search_dir}")
                            for ext in ['.json', '.txt', '.xml']:  # JSON優先
                                ann_path = search_dir / f"{image_file.stem}{ext}"
                                self.logger.debug(f"     檢查文件: {ann_path}")
                                if ann_path.exists():
                                    annotation_file = str(ann_path)
                                    self.logger.debug(f"     ✅ 找到標註文件: {annotation_file}")
                                    break
                            
                            if annotation_file:
                                break
                    
                    # 記錄找到的標註文件
                    if annotation_file:
                        self.logger.info(f"🏷️  為圖像 {image_file.name} 找到標註文件: {annotation_file}")
                    else:
                        self.logger.warning(f"⚠️  圖像 {image_file.name} 未找到標註文件")
                    
                    # 執行推理
                    result = self.predict_single_image(
                        image_path=str(image_file),
                        annotation_path=annotation_file,
                        output_dir=str(output_path),
                        task_type=task_type
                    )
                    
                    batch_results[image_file.name] = result
                    
                except Exception as e:
                    self.logger.error(f"處理 {image_file.name} 失敗: {e}")
                    failed_files.append(str(image_file))
                
                pbar.update(1)
        
        # 保存批次統計
        batch_stats = {
            'total_processed': len(image_files),
            'successful': len(batch_results),
            'failed': len(failed_files),
            'failed_files': failed_files,
            'inference_stats': self.inference_stats
        }
        
        stats_path = output_path / "batch_statistics.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(batch_stats, f, indent=2, ensure_ascii=False)
        
        # 生成CSV報告
        self.logger.info("📊 開始生成CSV報告...")
        self.generate_csv_reports(str(output_path.parent))
        
        self.logger.info(f"批次推理完成: {len(batch_results)}/{len(image_files)} 成功")
        
        return batch_results
    
    def get_model_info(self) -> Dict[str, Any]:
        """獲取模型資訊"""
        
        info = {
            'detection_model': None,
            'segmentation_model': None,
            'sahi_enabled': self.config.enable_sahi,
            'converter_enabled': self.config.auto_convert_annotations,
            'class_configs': {
                cls_id: {
                    'name': config.name,
                    'conf_threshold': config.conf_threshold,
                    'color': config.color,
                    'enabled': config.enabled
                }
                for cls_id, config in self.config.class_configs.items()
            }
        }
        
        if self.detection_model:
            info['detection_model'] = {
                'path': self.config.detection_model_path,
                'device': str(self.detection_model.device),
                'task': 'detection'
            }
        
        if self.segmentation_model:
            info['segmentation_model'] = {
                'path': self.config.segmentation_model_path,
                'device': str(self.segmentation_model.device),
                'task': 'segmentation'
            }
        
        return info


def create_enhanced_yolo_inference(config: Union[EnhancedYOLOConfig, str, Dict]) -> EnhancedYOLOInference:
    """創建增強YOLO推理器"""
    return EnhancedYOLOInference(config)


# 預設配置生成器
def create_default_config(detection_model: str = "", 
                         segmentation_model: str = "",
                         enable_sahi: bool = False) -> EnhancedYOLOConfig:
    """創建預設配置"""
    
    return EnhancedYOLOConfig(
        detection_model_path=detection_model,
        segmentation_model_path=segmentation_model,
        enable_sahi=enable_sahi,
        save_visualizations=True,
        save_predictions=True,
        auto_convert_annotations=True
    )


# 主函數接口 - 直接參數設定模式
def main():
    """
    主函數 - 直接在代碼中設定參數，不使用命令行參數
    修改以下參數以適應您的需求
    """
    
    # ==================== 參數設定區域 ====================
    # 模型路徑設定
    detection_model_path = ""      # 檢測模型路徑 (.pt文件)
    segmentation_model_path = ""   # 分割模型路徑 (.pt文件)
    
    # 輸入輸出設定
    input_path = ""               # 輸入圖像或目錄路徑
    output_path = ""              # 輸出目錄路徑
    
    # 任務設定
    task_type = "both"            # 任務類型: "detection", "segmentation", "both"
    enable_sahi = False           # 是否啟用SAHI切片推理
    batch_processing = True       # 是否批次處理（自動檢測輸入是文件還是目錄）
    
    # 進階配置（可選）
    config_file_path = ""         # 配置文件路徑（如果有的話）
    
    # 自動類別生成設定
    labelme_dir = ""              # LabelMe標註目錄（用於自動生成類別配置）
    auto_generate_classes = False # 是否從LabelMe自動生成類別
    
    # 推理參數設定
    img_size = 640               # 圖像大小
    global_conf = 0.25           # 全局置信度閾值
    iou_threshold = 0.45         # IoU閾值
    max_det = 1000               # 最大檢測數量
    
    # SAHI參數設定
    slice_height = 512           # 切片高度
    slice_width = 512            # 切片寬度
    overlap_height_ratio = 0.2   # 高度重疊比例
    overlap_width_ratio = 0.2    # 寬度重疊比例
    
    # 輸出設定
    save_visualizations = True   # 保存可視化結果
    save_predictions = True      # 保存預測結果JSON
    save_statistics = True       # 保存統計資料
    
    # ==================== 參數驗證 ====================
    # 檢查必要參數
    if not input_path:
        raise ValueError("請設定 input_path 參數")
    if not output_path:
        raise ValueError("請設定 output_path 參數")
    if not detection_model_path and not segmentation_model_path:
        raise ValueError("請至少設定一個模型路徑 (detection_model_path 或 segmentation_model_path)")
    
    # 檢查文件存在性
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
    
    if detection_model_path and not os.path.exists(detection_model_path):
        raise FileNotFoundError(f"檢測模型不存在: {detection_model_path}")
    
    if segmentation_model_path and not os.path.exists(segmentation_model_path):
        raise FileNotFoundError(f"分割模型不存在: {segmentation_model_path}")
    
    # ==================== 配置創建 ====================
    # 從配置文件載入或創建默認配置
    if config_file_path and os.path.exists(config_file_path):
        print(f"從配置文件載入設定: {config_file_path}")
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        config = EnhancedYOLOConfig(**config_dict)
    else:
        print("使用代碼中的參數設定創建配置")
        config = EnhancedYOLOConfig(
            # 模型配置
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            
            # 推理配置
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            
            # SAHI配置
            enable_sahi=enable_sahi,
            slice_height=slice_height,
            slice_width=slice_width,
            overlap_height_ratio=overlap_height_ratio,
            overlap_width_ratio=overlap_width_ratio,
            
            # 輸出配置
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics,
            
            # 類別配置
            labelme_dir=labelme_dir,
            auto_generate_classes=auto_generate_classes,
            
            # 其他配置
            auto_convert_annotations=True
        )
    
    # ==================== 執行推理 ====================
    try:
        # 創建推理器
        print("初始化增強YOLO推理器...")
        inference = create_enhanced_yolo_inference(config)
        
        # 顯示模型資訊
        model_info = inference.get_model_info()
        print("模型資訊:")
        if model_info['detection_model']:
            print(f"  檢測模型: {model_info['detection_model']['path']}")
        if model_info['segmentation_model']:
            print(f"  分割模型: {model_info['segmentation_model']['path']}")
        print(f"  SAHI啟用: {model_info['sahi_enabled']}")
        print(f"  類別數量: {len(model_info['class_configs'])}")
        
        # 執行推理
        print(f"開始推理...")
        print(f"  輸入: {input_path}")
        print(f"  輸出: {output_path}")
        print(f"  任務類型: {task_type}")
        
        if batch_processing or os.path.isdir(input_path):
            print("執行批次推理...")
            results = inference.batch_predict(input_path, output_path, task_type=task_type)
            print(f"批次推理完成: 處理了 {len(results)} 個圖像")
        else:
            print("執行單張圖像推理...")
            results = inference.predict_single_image(input_path, output_dir=output_path, task_type=task_type)
            print("單張圖像推理完成")
        
        # 顯示統計資訊
        stats = inference.inference_stats
        print("\n推理統計:")
        print(f"  總圖像數: {stats['total_images']}")
        print(f"  總檢測數: {stats['total_detections']}")
        print("  各類別統計:")
        for class_id, count in stats['class_counts'].items():
            if count > 0 and class_id in config.class_configs:
                class_name = config.class_configs[class_id].name
                print(f"    {class_name}: {count}")
        
        print(f"\n推理完成！結果保存在: {output_path}")
        
    except Exception as e:
        print(f"推理過程中發生錯誤: {e}")
        raise


# ===================================================================
# 🔧 用戶友好的類別配置輔助函數
# ===================================================================

def create_manual_class_configs(class_definitions: List[Dict[str, Any]]) -> Dict[int, ClassConfig]:
    """
    用戶友好的手動類別配置創建函數
    
    Args:
        class_definitions: 類別定義列表，每個字典包含：
            - name: 類別名稱 (必須)
            - conf_threshold: 置信度閾值 (可選，默認0.5)
            - color: RGB顏色 (可選，自動生成)
            - enabled: 是否啟用 (可選，默認True)
            - description: 描述 (可選)
    
    Returns:
        類別配置字典
        
    Example:
        class_configs = create_manual_class_configs([
            {"name": "linear_crack_裂縫", "conf_threshold": 0.3},
            {"name": "potholes_坑洞", "conf_threshold": 0.6},
            {"name": "joint_路面接縫", "conf_threshold": 0.4}
        ])
    """
    colors = generate_distinct_colors(len(class_definitions))
    class_configs = {}
    
    for i, class_def in enumerate(class_definitions):
        class_configs[i] = ClassConfig(
            name=class_def["name"],
            conf_threshold=class_def.get("conf_threshold", 0.5),
            color=class_def.get("color", colors[i]),
            enabled=class_def.get("enabled", True),
            description=class_def.get("description", f"手動配置的{class_def['name']}")
        )
    
    return class_configs


def create_quick_class_configs(class_names: List[str], 
                              conf_thresholds: List[float] = None) -> Dict[int, ClassConfig]:
    """
    快速創建類別配置（僅需類別名稱和置信度）
    
    Args:
        class_names: 類別名稱列表
        conf_thresholds: 置信度閾值列表（可選，默認全為0.5）
    
    Returns:
        類別配置字典
    
    Example:
        class_configs = create_quick_class_configs(
            ["裂縫", "坑洞", "人孔蓋"],
            [0.3, 0.6, 0.4]
        )
    """
    if conf_thresholds is None:
        conf_thresholds = [0.5] * len(class_names)
    
    if len(conf_thresholds) != len(class_names):
        raise ValueError("置信度閾值數量必須與類別名稱數量相同")
    
    colors = generate_distinct_colors(len(class_names))
    class_configs = {}
    
    for i, (name, conf) in enumerate(zip(class_names, conf_thresholds)):
        class_configs[i] = ClassConfig(
            name=name,
            conf_threshold=conf,
            color=colors[i],
            enabled=True,
            description=f"快速配置的{name}"
        )
    
    return class_configs


def modify_class_confidence(class_configs: Dict[int, ClassConfig], 
                           confidence_updates: Dict[str, float]) -> Dict[int, ClassConfig]:
    """
    修改現有類別配置的置信度閾值
    
    Args:
        class_configs: 現有類別配置
        confidence_updates: 置信度更新字典，格式為 {類別名稱: 新置信度}
    
    Returns:
        更新後的類別配置字典
    
    Example:
        # 修改特定類別的置信度
        class_configs = modify_class_confidence(class_configs, {
            "linear_crack_裂縫": 0.25,
            "potholes_坑洞": 0.7
        })
    """
    updated_configs = class_configs.copy()
    
    for class_id, config in updated_configs.items():
        if config.name in confidence_updates:
            updated_configs[class_id] = ClassConfig(
                name=config.name,
                conf_threshold=confidence_updates[config.name],
                color=config.color,
                enabled=config.enabled,
                description=config.description
            )
            print(f"✅ 更新 {config.name} 置信度: {confidence_updates[config.name]}")
    
    return updated_configs


def validate_and_display_class_configs(class_configs: Dict[int, ClassConfig], 
                                      model_path: str = None) -> bool:
    """
    驗證並顯示類別配置信息
    
    Args:
        class_configs: 類別配置字典
        model_path: 模型路徑（可選，用於驗證類別匹配）
    
    Returns:
        配置是否有效
    """
    print("📊 類別配置驗證和總結:")
    print("=" * 50)
    
    if not class_configs:
        print("❌ 錯誤: 沒有配置任何類別")
        return False
    
    print(f"✅ 總共配置了 {len(class_configs)} 個類別:")
    print()
    
    enabled_count = 0
    for class_id, config in class_configs.items():
        status = "✅ 啟用" if config.enabled else "❌ 禁用"
        if config.enabled:
            enabled_count += 1
            
        print(f"   類別 {class_id}: {config.name}")
        print(f"      置信度閾值: {config.conf_threshold}")
        print(f"      顏色 (RGB): {config.color}")
        print(f"      狀態: {status}")
        if config.description:
            print(f"      描述: {config.description}")
        print()
    
    print(f"📈 啟用類別數: {enabled_count}/{len(class_configs)}")
    
    # 檢查置信度閾值範圍
    enabled_configs = [config for config in class_configs.values() if config.enabled]
    if enabled_configs:
        thresholds = [config.conf_threshold for config in enabled_configs]
        min_thresh, max_thresh = min(thresholds), max(thresholds)
        avg_thresh = sum(thresholds) / len(thresholds)
        
        print(f"📊 置信度閾值統計:")
        print(f"   最低: {min_thresh:.3f}")
        print(f"   最高: {max_thresh:.3f}")
        print(f"   平均: {avg_thresh:.3f}")
        print()
        
        # 提供建議
        if min_thresh < 0.1:
            print("⚠️  警告: 某些類別的置信度閾值很低（< 0.1），可能產生過多誤檢")
        if max_thresh > 0.9:
            print("⚠️  警告: 某些類別的置信度閾值很高（> 0.9），可能導致漏檢")
        if max_thresh - min_thresh > 0.6:
            print("💡 建議: 類別間置信度差異較大，建議檢查各類別檢測難度是否匹配")
    
    return True


def create_balanced_class_configs(class_names: List[str], 
                                 difficulty_levels: List[str] = None) -> Dict[int, ClassConfig]:
    """
    根據檢測難度創建平衡的類別配置
    
    Args:
        class_names: 類別名稱列表
        difficulty_levels: 難度等級列表 ["easy", "medium", "hard"]，可選
    
    Returns:
        平衡的類別配置字典
        
    Example:
        class_configs = create_balanced_class_configs(
            ["linear_crack_裂縫", "potholes_坑洞", "joint_路面接縫"],
            ["hard", "easy", "medium"]
        )
    """
    # 難度對應的建議置信度閾值
    difficulty_thresholds = {
        "easy": 0.6,     # 容易檢測的類別使用較高閾值
        "medium": 0.4,   # 中等難度使用中等閾值
        "hard": 0.25     # 困難檢測的類別使用較低閾值
    }
    
    if difficulty_levels is None:
        difficulty_levels = ["medium"] * len(class_names)
    
    if len(difficulty_levels) != len(class_names):
        raise ValueError("難度等級數量必須與類別名稱數量相同")
    
    colors = generate_distinct_colors(len(class_names))
    class_configs = {}
    
    for i, (name, difficulty) in enumerate(zip(class_names, difficulty_levels)):
        if difficulty not in difficulty_thresholds:
            print(f"⚠️  未知難度等級 '{difficulty}'，使用預設值 'medium'")
            difficulty = "medium"
            
        threshold = difficulty_thresholds[difficulty]
        
        class_configs[i] = ClassConfig(
            name=name,
            conf_threshold=threshold,
            color=colors[i],
            enabled=True,
            description=f"根據{difficulty}難度自動配置的{name}"
        )
        
        print(f"✅ {name}: 難度={difficulty}, 閾值={threshold}")
    
    return class_configs


if __name__ == "__main__":
    main()