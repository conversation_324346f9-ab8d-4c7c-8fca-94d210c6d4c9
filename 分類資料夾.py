import os
import shutil
import numpy as np
from PIL import Image
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models, transforms
from datetime import datetime

def classify_images_with_model(image_dir, model_path, output_dir, confidence_threshold=0.5):
    """
    使用模型分類圖像並根據置信度處理未知類別
    
    參數:
        image_dir (str): 包含待分類圖像的目錄
        model_path (str): 模型文件路徑
        output_dir (str): 輸出目錄
        confidence_threshold (float): 置信度閾值，低於此值歸為unknown
    """
    # 創建輸出目錄
    os.makedirs(output_dir, exist_ok=True)
    unknown_dir = os.path.join(output_dir, 'unknown')
    os.makedirs(unknown_dir, exist_ok=True)
    
    # 加載模型 (這是需要修改的部分)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 1. 先初始化模型結構 (類別數要與訓練時相同)
    model = HighAccuracyClassifier(num_classes=8)  # 請確認你的類別數量
    
    # 2. 加載訓練好的權重
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 3. 將模型移到設備並設為評估模式
    model = model.to(device)
    model.eval()
    
    # 圖像預處理
    transform = transforms.Compose([
        transforms.Resize((448, 448)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 初始化報告數據
    report = {
        'total_images': 0,
        'classified': 0,
        'unknown': 0,
        'class_distribution': {},
        'confidence_threshold': confidence_threshold,
        'timestamp': datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    }
    
    # 假設我們有類別名稱映射 (根據您的實際情況調整)
    class_names = {
        0: "new_bird",
        1: "new_square",
        2: "new_treefish",
        3: "old_bird",
        4: "old_square",
        5: "old_treefish",
        6: "retaining_seat",
        7: "tpe"
    }
    
    # 初始化類別分佈
    for class_id in class_names.keys():
        report['class_distribution'][class_id] = 0
        os.makedirs(os.path.join(output_dir, class_names[class_id]), exist_ok=True)
    
    # 處理每張圖像
    for root, _, files in os.walk(image_dir):
        for img_file in files:
            if not img_file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
                continue
            
            img_path = os.path.join(root, img_file)
            try:
                img = Image.open(img_path).convert('RGB')
                input_tensor = transform(img).unsqueeze(0).to(device)
                
                with torch.no_grad():
                    outputs = model(input_tensor)
                    probabilities = torch.nn.functional.softmax(outputs, dim=1)
                    confidence, pred_class = torch.max(probabilities, 1)
                    confidence = confidence.item()
                    pred_class = pred_class.item()
                    
                report['total_images'] += 1
                
                # 檢查置信度
                if confidence >= confidence_threshold and pred_class in class_names:
                    # 分類到已知類別
                    target_dir = os.path.join(output_dir, class_names[pred_class])
                    report['class_distribution'][pred_class] += 1
                    report['classified'] += 1
                else:
                    # 分類到unknown
                    target_dir = unknown_dir
                    report['unknown'] += 1
                    
                # 複製文件到目標目錄
                shutil.copy2(img_path, os.path.join(target_dir, img_file))
                
            except Exception as e:
                print(f"處理 {img_file} 時出錯: {str(e)}")
                continue
    
    # 生成報告
    generate_classification_report(report, output_dir)
    
    return report

class HighAccuracyClassifier(nn.Module):
    """
    高精度分類器
    基於torchvision中accuracy最高的預訓練模型
    """
    
    def __init__(self, 
                 num_classes: int,
                 model_name: str = "efficientnet_v2_l",
                 pretrained: bool = True,
                 dropout_rate: float = 0.3):
        """
        初始化分類器
        
        Args:
            num_classes: 類別數量
            model_name: 模型名稱
            pretrained: 是否使用預訓練權重
            dropout_rate: Dropout比率
        """
        super().__init__()
        
        self.num_classes = num_classes
        self.model_name = model_name
        
        # 創建骨幹網路
        self.backbone = self._create_backbone(model_name, pretrained)
        
        # 獲取特徵維度
        self.feature_dim = self._get_feature_dim()
        
        # 創建分類頭
        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(self.feature_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(512, num_classes)
        )
        
        # 如果使用預訓練模型，凍結前面的層
        if pretrained:
            self._freeze_backbone(freeze_ratio=0.8)
    
    def _create_backbone(self, model_name: str, pretrained: bool) -> nn.Module:
        """創建骨幹網路"""
        try:
            if model_name == "efficientnet_v2_l":
                model = models.efficientnet_v2_l(weights='DEFAULT' if pretrained else None)
                # 移除分類層
                model.classifier = nn.Identity()
                return model
            elif model_name == "convnext_large":
                model = models.convnext_large(weights='DEFAULT' if pretrained else None)
                model.classifier = nn.Identity()
                return model
            elif model_name == "swin_v2_t":
                model = models.swin_v2_t(weights='DEFAULT' if pretrained else None)
                model.head = nn.Identity()
                return model
            else:
                # 備用方案：使用ResNet152
                # logger.warning(f"不支援的模型 {model_name}，使用 ResNet152")
                model = models.resnet152(weights='DEFAULT' if pretrained else None)
                model.fc = nn.Identity()
                return model
        except Exception as e:
            # logger.error(f"創建模型失敗: {e}")
            # 最終備用方案
            # logger.info("使用 ResNet50 作為備用方案")
            model = models.resnet50(weights='DEFAULT' if pretrained else None)
            model.fc = nn.Identity()
            return model
    
    def _get_feature_dim(self) -> int:
        """獲取特徵維度"""
        dummy_input = torch.randn(1, 3, 224, 224)
        with torch.no_grad():
            features = self.backbone(dummy_input)
            return features.shape[-1] if features.dim() == 2 else features.shape[1]
    
    def _freeze_backbone(self, freeze_ratio: float = 0.8):
        """凍結骨幹網路的部分層"""
        params = list(self.backbone.parameters())
        freeze_num = int(len(params) * freeze_ratio)
        
        for param in params[:freeze_num]:
            param.requires_grad = False
        
        # logger.info(f"凍結了 {freeze_num}/{len(params)} 個參數層")
    
    def unfreeze_all(self):
        """解凍所有層"""
        for param in self.parameters():
            param.requires_grad = True
        # logger.info("解凍所有層")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        features = self.backbone(x)
        if features.dim() > 2:
            features = F.adaptive_avg_pool2d(features, (1, 1))
            features = features.flatten(1)
        return self.classifier(features)

# 保持其他函數不變
def generate_classification_report(report, output_dir):
    """生成並保存分類報告文件"""
    report_path = os.path.join(output_dir, 'classification_report.txt')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("=== 圖像分類報告 ===\n")
        f.write(f"生成時間: {report['timestamp']}\n")
        f.write(f"總圖像數: {report['total_images']}\n")
        f.write(f"分類圖像數: {report['classified']}\n")
        f.write(f"未知圖像數: {report['unknown']}\n")
        f.write(f"置信度閾值: {report['confidence_threshold']}\n")
        
        f.write("\n類別分佈:\n")
        for class_id, count in report['class_distribution'].items():
            class_name = f"類別{class_id}"  # 替換為您的實際類別名稱
            f.write(f"{class_name}: {count} 張\n")
        
        f.write("\n分類統計:\n")
        if report['total_images'] > 0:
            f.write(f"已知類別比例: {report['classified']/report['total_images']*100:.2f}%\n")
            f.write(f"未知類別比例: {report['unknown']/report['total_images']*100:.2f}%\n")
        else:
            f.write("已知類別比例: 0.00%\n")
            f.write("未知類別比例: 0.00%\n")
            f.write("\n警告：未處理任何圖像，請檢查輸入目錄是否正確且包含圖像文件。\n")

    print(f"分類報告已生成: {report_path}")

# 使用示例
if __name__ == "__main__":
    # 使用者設定
    image_directory = r"D:\5_Hole_cover\total_image_近照"  # 替換為您的圖像目錄
    model_path = r"D:\5_Hole_cover\classification\ai_result\best_model.pth"    # 替換為您的模型路徑
    output_directory = r"D:\5_Hole_cover\test"      # 替換為您的輸出目錄
    confidence_threshold = 0.5               # 置信度閾值
    
    # 執行分類
    report = classify_images_with_model(
        image_dir=image_directory,
        model_path=model_path,
        output_dir=output_directory,
        confidence_threshold=confidence_threshold
    )