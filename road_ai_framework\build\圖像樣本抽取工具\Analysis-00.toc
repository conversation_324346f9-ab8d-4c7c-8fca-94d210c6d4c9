(['D:\\99_AI_model\\road_ai_framework\\image_sample_extractor.py'],
 ['D:\\99_AI_model\\road_ai_framework'],
 [],
 [('C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('build_exe.py', 'D:\\99_AI_model\\road_ai_framework\\build_exe.py', 'DATA'),
  ('check_labelme_syntax.py',
   'D:\\99_AI_model\\road_ai_framework\\check_labelme_syntax.py',
   'DATA'),
  ('classifier_usage_example.py',
   'D:\\99_AI_model\\road_ai_framework\\classifier_usage_example.py',
   'DATA'),
  ('coordinate_fix_verification.py',
   'D:\\99_AI_model\\road_ai_framework\\coordinate_fix_verification.py',
   'DATA'),
  ('debug_labelme_issue.py',
   'D:\\99_AI_model\\road_ai_framework\\debug_labelme_issue.py',
   'DATA'),
  ('debug_labelme_output.py',
   'D:\\99_AI_model\\road_ai_framework\\debug_labelme_output.py',
   'DATA'),
  ('fix_labelme_output.py',
   'D:\\99_AI_model\\road_ai_framework\\fix_labelme_output.py',
   'DATA'),
  ('high_accuracy_classifier.py',
   'D:\\99_AI_model\\road_ai_framework\\high_accuracy_classifier.py',
   'DATA'),
  ('image_sample_extractor.py',
   'D:\\99_AI_model\\road_ai_framework\\image_sample_extractor.py',
   'DATA'),
  ('model_train.py',
   'D:\\99_AI_model\\road_ai_framework\\model_train.py',
   'DATA'),
  ('model_train_1.py',
   'D:\\99_AI_model\\road_ai_framework\\model_train_1.py',
   'DATA'),
  ('quick_build.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_build.py',
   'DATA'),
  ('quick_config_test.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_config_test.py',
   'DATA'),
  ('quick_debug_labelme.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_debug_labelme.py',
   'DATA'),
  ('quick_format_test.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_format_test.py',
   'DATA'),
  ('quick_test_fixes.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_test_fixes.py',
   'DATA'),
  ('run_enhanced_yolo.py',
   'D:\\99_AI_model\\road_ai_framework\\run_enhanced_yolo.py',
   'DATA'),
  ('run_simplified_yolo.py',
   'D:\\99_AI_model\\road_ai_framework\\run_simplified_yolo.py',
   'DATA'),
  ('run_unified_yolo.py',
   'D:\\99_AI_model\\road_ai_framework\\run_unified_yolo.py',
   'DATA'),
  ('simple_classifier_example.py',
   'D:\\99_AI_model\\road_ai_framework\\simple_classifier_example.py',
   'DATA'),
  ('test_advanced_system.py',
   'D:\\99_AI_model\\road_ai_framework\\test_advanced_system.py',
   'DATA'),
  ('test_allocation_logic.py',
   'D:\\99_AI_model\\road_ai_framework\\test_allocation_logic.py',
   'DATA'),
  ('test_base64_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_base64_fix.py',
   'DATA'),
  ('test_bbox_mask_and.py',
   'D:\\99_AI_model\\road_ai_framework\\test_bbox_mask_and.py',
   'DATA'),
  ('test_classifier_setup.py',
   'D:\\99_AI_model\\road_ai_framework\\test_classifier_setup.py',
   'DATA'),
  ('test_config_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_config_fix.py',
   'DATA'),
  ('test_coordinate_scaling.py',
   'D:\\99_AI_model\\road_ai_framework\\test_coordinate_scaling.py',
   'DATA'),
  ('test_enhanced_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_enhanced_features.py',
   'DATA'),
  ('test_enhanced_preview.py',
   'D:\\99_AI_model\\road_ai_framework\\test_enhanced_preview.py',
   'DATA'),
  ('test_enhanced_save_messages.py',
   'D:\\99_AI_model\\road_ai_framework\\test_enhanced_save_messages.py',
   'DATA'),
  ('test_filtering_and_labelme.py',
   'D:\\99_AI_model\\road_ai_framework\\test_filtering_and_labelme.py',
   'DATA'),
  ('test_fixed_issues.py',
   'D:\\99_AI_model\\road_ai_framework\\test_fixed_issues.py',
   'DATA'),
  ('test_format_comparison.py',
   'D:\\99_AI_model\\road_ai_framework\\test_format_comparison.py',
   'DATA'),
  ('test_fusion_strategy_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_fusion_strategy_fix.py',
   'DATA'),
  ('test_general_inference_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_general_inference_features.py',
   'DATA'),
  ('test_import_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_import_fix.py',
   'DATA'),
  ('test_inference_mode.py',
   'D:\\99_AI_model\\road_ai_framework\\test_inference_mode.py',
   'DATA'),
  ('test_json_mapping.py',
   'D:\\99_AI_model\\road_ai_framework\\test_json_mapping.py',
   'DATA'),
  ('test_labelme_base64.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_base64.py',
   'DATA'),
  ('test_labelme_image_copy.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_image_copy.py',
   'DATA'),
  ('test_labelme_json_output.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_json_output.py',
   'DATA'),
  ('test_labelme_simple.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_simple.py',
   'DATA'),
  ('test_labelme_small_batch.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_small_batch.py',
   'DATA'),
  ('test_mask_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_mask_fix.py',
   'DATA'),
  ('test_new_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_new_features.py',
   'DATA'),
  ('test_perfect_solution.py',
   'D:\\99_AI_model\\road_ai_framework\\test_perfect_solution.py',
   'DATA'),
  ('test_preview.py',
   'D:\\99_AI_model\\road_ai_framework\\test_preview.py',
   'DATA'),
  ('test_roi_integration.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_integration.py',
   'DATA'),
  ('test_roi_preview.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_preview.py',
   'DATA'),
  ('test_roi_preview_simple.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_preview_simple.py',
   'DATA'),
  ('test_roi_simple.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_simple.py',
   'DATA'),
  ('test_three_view_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_three_view_fix.py',
   'DATA'),
  ('test_unified_yolo_fixes.py',
   'D:\\99_AI_model\\road_ai_framework\\test_unified_yolo_fixes.py',
   'DATA'),
  ('test_v2_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_v2_features.py',
   'DATA'),
  ('test_variable_scope_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_variable_scope_fix.py',
   'DATA'),
  ('test_visualization_labelme_consistency.py',
   'D:\\99_AI_model\\road_ai_framework\\test_visualization_labelme_consistency.py',
   'DATA'),
  ('universal_detector.py',
   'D:\\99_AI_model\\road_ai_framework\\universal_detector.py',
   'DATA'),
  ('validate_config.py',
   'D:\\99_AI_model\\road_ai_framework\\validate_config.py',
   'DATA'),
  ('分類資料夾.py', 'D:\\99_AI_model\\road_ai_framework\\分類資料夾.py', 'DATA'),
  ('計算訓練驗證測試類別數量.py',
   'D:\\99_AI_model\\road_ai_framework\\計算訓練驗證測試類別數量.py',
   'DATA')],
 '3.12.9 | packaged by Anaconda, Inc. | (main, Feb  6 2025, 18:49:16) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('image_sample_extractor',
   'D:\\99_AI_model\\road_ai_framework\\image_sample_extractor.py',
   'PYSOURCE')],
 [('pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\zipimport.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\contextlib.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\tempfile.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\gettext.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\quopri.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\csv.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\token.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\bz2.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\struct.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_strptime.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\inspect.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\ast.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('PyQt6',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\typing.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\pathlib.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\gzip.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\bisect.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\signal.py',
   'PYMODULE')],
 [('python312.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\python312.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\VCRUNTIME140.dll',
   'BINARY'),
  ('zlib.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\python3.dll',
   'BINARY'),
  ('MSVCP140_2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\MSVCP140_2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\ucrtbase.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('build_exe.py', 'D:\\99_AI_model\\road_ai_framework\\build_exe.py', 'DATA'),
  ('check_labelme_syntax.py',
   'D:\\99_AI_model\\road_ai_framework\\check_labelme_syntax.py',
   'DATA'),
  ('classifier_usage_example.py',
   'D:\\99_AI_model\\road_ai_framework\\classifier_usage_example.py',
   'DATA'),
  ('coordinate_fix_verification.py',
   'D:\\99_AI_model\\road_ai_framework\\coordinate_fix_verification.py',
   'DATA'),
  ('debug_labelme_issue.py',
   'D:\\99_AI_model\\road_ai_framework\\debug_labelme_issue.py',
   'DATA'),
  ('debug_labelme_output.py',
   'D:\\99_AI_model\\road_ai_framework\\debug_labelme_output.py',
   'DATA'),
  ('fix_labelme_output.py',
   'D:\\99_AI_model\\road_ai_framework\\fix_labelme_output.py',
   'DATA'),
  ('high_accuracy_classifier.py',
   'D:\\99_AI_model\\road_ai_framework\\high_accuracy_classifier.py',
   'DATA'),
  ('image_sample_extractor.py',
   'D:\\99_AI_model\\road_ai_framework\\image_sample_extractor.py',
   'DATA'),
  ('model_train.py',
   'D:\\99_AI_model\\road_ai_framework\\model_train.py',
   'DATA'),
  ('model_train_1.py',
   'D:\\99_AI_model\\road_ai_framework\\model_train_1.py',
   'DATA'),
  ('quick_build.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_build.py',
   'DATA'),
  ('quick_config_test.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_config_test.py',
   'DATA'),
  ('quick_debug_labelme.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_debug_labelme.py',
   'DATA'),
  ('quick_format_test.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_format_test.py',
   'DATA'),
  ('quick_test_fixes.py',
   'D:\\99_AI_model\\road_ai_framework\\quick_test_fixes.py',
   'DATA'),
  ('run_enhanced_yolo.py',
   'D:\\99_AI_model\\road_ai_framework\\run_enhanced_yolo.py',
   'DATA'),
  ('run_simplified_yolo.py',
   'D:\\99_AI_model\\road_ai_framework\\run_simplified_yolo.py',
   'DATA'),
  ('run_unified_yolo.py',
   'D:\\99_AI_model\\road_ai_framework\\run_unified_yolo.py',
   'DATA'),
  ('simple_classifier_example.py',
   'D:\\99_AI_model\\road_ai_framework\\simple_classifier_example.py',
   'DATA'),
  ('test_advanced_system.py',
   'D:\\99_AI_model\\road_ai_framework\\test_advanced_system.py',
   'DATA'),
  ('test_allocation_logic.py',
   'D:\\99_AI_model\\road_ai_framework\\test_allocation_logic.py',
   'DATA'),
  ('test_base64_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_base64_fix.py',
   'DATA'),
  ('test_bbox_mask_and.py',
   'D:\\99_AI_model\\road_ai_framework\\test_bbox_mask_and.py',
   'DATA'),
  ('test_classifier_setup.py',
   'D:\\99_AI_model\\road_ai_framework\\test_classifier_setup.py',
   'DATA'),
  ('test_config_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_config_fix.py',
   'DATA'),
  ('test_coordinate_scaling.py',
   'D:\\99_AI_model\\road_ai_framework\\test_coordinate_scaling.py',
   'DATA'),
  ('test_enhanced_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_enhanced_features.py',
   'DATA'),
  ('test_enhanced_preview.py',
   'D:\\99_AI_model\\road_ai_framework\\test_enhanced_preview.py',
   'DATA'),
  ('test_enhanced_save_messages.py',
   'D:\\99_AI_model\\road_ai_framework\\test_enhanced_save_messages.py',
   'DATA'),
  ('test_filtering_and_labelme.py',
   'D:\\99_AI_model\\road_ai_framework\\test_filtering_and_labelme.py',
   'DATA'),
  ('test_fixed_issues.py',
   'D:\\99_AI_model\\road_ai_framework\\test_fixed_issues.py',
   'DATA'),
  ('test_format_comparison.py',
   'D:\\99_AI_model\\road_ai_framework\\test_format_comparison.py',
   'DATA'),
  ('test_fusion_strategy_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_fusion_strategy_fix.py',
   'DATA'),
  ('test_general_inference_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_general_inference_features.py',
   'DATA'),
  ('test_import_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_import_fix.py',
   'DATA'),
  ('test_inference_mode.py',
   'D:\\99_AI_model\\road_ai_framework\\test_inference_mode.py',
   'DATA'),
  ('test_json_mapping.py',
   'D:\\99_AI_model\\road_ai_framework\\test_json_mapping.py',
   'DATA'),
  ('test_labelme_base64.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_base64.py',
   'DATA'),
  ('test_labelme_image_copy.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_image_copy.py',
   'DATA'),
  ('test_labelme_json_output.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_json_output.py',
   'DATA'),
  ('test_labelme_simple.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_simple.py',
   'DATA'),
  ('test_labelme_small_batch.py',
   'D:\\99_AI_model\\road_ai_framework\\test_labelme_small_batch.py',
   'DATA'),
  ('test_mask_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_mask_fix.py',
   'DATA'),
  ('test_new_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_new_features.py',
   'DATA'),
  ('test_perfect_solution.py',
   'D:\\99_AI_model\\road_ai_framework\\test_perfect_solution.py',
   'DATA'),
  ('test_preview.py',
   'D:\\99_AI_model\\road_ai_framework\\test_preview.py',
   'DATA'),
  ('test_roi_integration.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_integration.py',
   'DATA'),
  ('test_roi_preview.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_preview.py',
   'DATA'),
  ('test_roi_preview_simple.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_preview_simple.py',
   'DATA'),
  ('test_roi_simple.py',
   'D:\\99_AI_model\\road_ai_framework\\test_roi_simple.py',
   'DATA'),
  ('test_three_view_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_three_view_fix.py',
   'DATA'),
  ('test_unified_yolo_fixes.py',
   'D:\\99_AI_model\\road_ai_framework\\test_unified_yolo_fixes.py',
   'DATA'),
  ('test_v2_features.py',
   'D:\\99_AI_model\\road_ai_framework\\test_v2_features.py',
   'DATA'),
  ('test_variable_scope_fix.py',
   'D:\\99_AI_model\\road_ai_framework\\test_variable_scope_fix.py',
   'DATA'),
  ('test_visualization_labelme_consistency.py',
   'D:\\99_AI_model\\road_ai_framework\\test_visualization_labelme_consistency.py',
   'DATA'),
  ('universal_detector.py',
   'D:\\99_AI_model\\road_ai_framework\\universal_detector.py',
   'DATA'),
  ('validate_config.py',
   'D:\\99_AI_model\\road_ai_framework\\validate_config.py',
   'DATA'),
  ('分類資料夾.py', 'D:\\99_AI_model\\road_ai_framework\\分類資料夾.py', 'DATA'),
  ('計算訓練驗證測試類別數量.py',
   'D:\\99_AI_model\\road_ai_framework\\計算訓練驗證測試類別數量.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\night1\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\99_AI_model\\road_ai_framework\\build\\圖像樣本抽取工具\\base_library.zip',
   'DATA')])
