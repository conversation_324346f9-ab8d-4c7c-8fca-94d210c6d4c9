"""
文件處理工具

提供統一的文件和路徑處理功能
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Optional, Union, Generator, Dict, Any
import mimetypes
import tempfile

from .exceptions import FileOperationError


class FileUtils:
    """文件處理工具類"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """
        確保目錄存在
        
        Args:
            path: 目錄路徑
            
        Returns:
            Path: 目錄路徑對象
        """
        path = Path(path)
        try:
            path.mkdir(parents=True, exist_ok=True)
            return path
        except Exception as e:
            raise FileOperationError(f"創建目錄失敗: {e}", file_path=str(path), operation="mkdir")
    
    @staticmethod
    def safe_copy(src: Union[str, Path], dst: Union[str, Path], 
                  overwrite: bool = False) -> bool:
        """
        安全複製文件
        
        Args:
            src: 源文件路徑
            dst: 目標文件路徑
            overwrite: 是否覆蓋現有文件
            
        Returns:
            bool: 是否成功複製
        """
        src, dst = Path(src), Path(dst)
        
        if not src.exists():
            raise FileOperationError(f"源文件不存在: {src}", file_path=str(src), operation="copy")
        
        if dst.exists() and not overwrite:
            return False
        
        try:
            # 確保目標目錄存在
            FileUtils.ensure_dir(dst.parent)
            
            # 執行複製
            shutil.copy2(src, dst)
            return True
            
        except Exception as e:
            raise FileOperationError(f"複製文件失敗: {e}", file_path=str(src), operation="copy")
    
    @staticmethod
    def safe_move(src: Union[str, Path], dst: Union[str, Path], 
                  overwrite: bool = False) -> bool:
        """
        安全移動文件
        
        Args:
            src: 源文件路徑
            dst: 目標文件路徑
            overwrite: 是否覆蓋現有文件
            
        Returns:
            bool: 是否成功移動
        """
        src, dst = Path(src), Path(dst)
        
        if not src.exists():
            raise FileOperationError(f"源文件不存在: {src}", file_path=str(src), operation="move")
        
        if dst.exists() and not overwrite:
            return False
        
        try:
            # 確保目標目錄存在
            FileUtils.ensure_dir(dst.parent)
            
            # 執行移動
            shutil.move(str(src), str(dst))
            return True
            
        except Exception as e:
            raise FileOperationError(f"移動文件失敗: {e}", file_path=str(src), operation="move")
    
    @staticmethod
    def safe_delete(path: Union[str, Path]) -> bool:
        """
        安全刪除文件或目錄
        
        Args:
            path: 文件或目錄路徑
            
        Returns:
            bool: 是否成功刪除
        """
        path = Path(path)
        
        if not path.exists():
            return True  # 文件不存在視為成功
        
        try:
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
            return True
            
        except Exception as e:
            raise FileOperationError(f"刪除失敗: {e}", file_path=str(path), operation="delete")
    
    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """
        計算文件哈希值
        
        Args:
            file_path: 文件路徑
            algorithm: 哈希算法 ('md5', 'sha1', 'sha256')
            
        Returns:
            str: 哈希值
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileOperationError(f"文件不存在: {file_path}", file_path=str(file_path), operation="hash")
        
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
            
        except Exception as e:
            raise FileOperationError(f"計算哈希失敗: {e}", file_path=str(file_path), operation="hash")
    
    @staticmethod
    def find_files(directory: Union[str, Path], 
                   pattern: str = "*",
                   recursive: bool = True,
                   file_types: Optional[List[str]] = None) -> Generator[Path, None, None]:
        """
        查找文件
        
        Args:
            directory: 搜索目錄
            pattern: 文件名模式
            recursive: 是否遞歸搜索
            file_types: 文件擴展名列表 (e.g., ['.jpg', '.png'])
            
        Yields:
            Path: 匹配的文件路徑
        """
        directory = Path(directory)
        
        if not directory.exists():
            raise FileOperationError(f"目錄不存在: {directory}", file_path=str(directory), operation="find")
        
        try:
            if recursive:
                files = directory.rglob(pattern)
            else:
                files = directory.glob(pattern)
            
            for file_path in files:
                if file_path.is_file():
                    if file_types is None or file_path.suffix.lower() in file_types:
                        yield file_path
                        
        except Exception as e:
            raise FileOperationError(f"搜索文件失敗: {e}", file_path=str(directory), operation="find")
    
    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        獲取文件信息
        
        Args:
            file_path: 文件路徑
            
        Returns:
            Dict[str, Any]: 文件信息字典
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileOperationError(f"文件不存在: {file_path}", file_path=str(file_path), operation="info")
        
        try:
            stat = file_path.stat()
            mime_type, _ = mimetypes.guess_type(str(file_path))
            
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'extension': file_path.suffix,
                'mime_type': mime_type,
                'is_image': mime_type and mime_type.startswith('image/'),
                'is_text': mime_type and mime_type.startswith('text/'),
            }
            
        except Exception as e:
            raise FileOperationError(f"獲取文件信息失敗: {e}", file_path=str(file_path), operation="info")


class PathUtils:
    """路徑處理工具類"""
    
    @staticmethod
    def is_chinese_path(path: Union[str, Path]) -> bool:
        """
        檢查路徑是否包含中文字符
        
        Args:
            path: 路徑
            
        Returns:
            bool: 是否包含中文字符
        """
        path_str = str(path)
        return any('\u4e00' <= char <= '\u9fff' for char in path_str)
    
    @staticmethod
    def sanitize_filename(filename: str, replacement: str = "_") -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            replacement: 替換字符
            
        Returns:
            str: 清理後的文件名
        """
        # Windows非法字符
        illegal_chars = '<>:"/\\|?*'
        
        for char in illegal_chars:
            filename = filename.replace(char, replacement)
        
        # 移除前後空格和點
        filename = filename.strip(' .')
        
        # 確保不為空
        if not filename:
            filename = "unnamed"
        
        return filename
    
    @staticmethod
    def get_unique_path(path: Union[str, Path], max_attempts: int = 1000) -> Path:
        """
        獲取唯一路徑（如果文件存在則添加數字後綴）
        
        Args:
            path: 原始路徑
            max_attempts: 最大嘗試次數
            
        Returns:
            Path: 唯一路徑
        """
        path = Path(path)
        
        if not path.exists():
            return path
        
        # 分離文件名和擴展名
        stem = path.stem
        suffix = path.suffix
        parent = path.parent
        
        for i in range(1, max_attempts + 1):
            new_name = f"{stem}_{i}{suffix}"
            new_path = parent / new_name
            
            if not new_path.exists():
                return new_path
        
        raise FileOperationError(f"無法生成唯一路徑: {path}", file_path=str(path), operation="unique_path")
    
    @staticmethod
    def normalize_path(path: Union[str, Path]) -> Path:
        """
        標準化路徑
        
        Args:
            path: 原始路徑
            
        Returns:
            Path: 標準化路徑
        """
        path = Path(path)
        
        # 解析符號鏈接和相對路徑
        try:
            return path.resolve()
        except Exception:
            # 如果解析失敗，返回絕對路徑
            return path.absolute()
    
    @staticmethod
    def get_relative_path(path: Union[str, Path], base: Union[str, Path]) -> Path:
        """
        獲取相對路徑
        
        Args:
            path: 目標路徑
            base: 基準路徑
            
        Returns:
            Path: 相對路徑
        """
        path = Path(path).resolve()
        base = Path(base).resolve()
        
        try:
            return path.relative_to(base)
        except ValueError:
            # 如果無法計算相對路徑，返回絕對路徑
            return path
    
    @staticmethod
    def create_temp_dir(prefix: str = "preprocessing_", suffix: str = "") -> Path:
        """
        創建臨時目錄
        
        Args:
            prefix: 前綴
            suffix: 後綴
            
        Returns:
            Path: 臨時目錄路徑
        """
        try:
            temp_dir = tempfile.mkdtemp(prefix=prefix, suffix=suffix)
            return Path(temp_dir)
        except Exception as e:
            raise FileOperationError(f"創建臨時目錄失敗: {e}", operation="temp_dir")
    
    @staticmethod
    def get_available_space(path: Union[str, Path]) -> int:
        """
        獲取路徑可用空間（字節）
        
        Args:
            path: 路徑
            
        Returns:
            int: 可用空間字節數
        """
        path = Path(path)
        
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(str(path)), 
                    ctypes.pointer(free_bytes), 
                    None, 
                    None
                )
                return free_bytes.value
            else:  # Unix/Linux
                statvfs = os.statvfs(path)
                return statvfs.f_frsize * statvfs.f_available
                
        except Exception as e:
            raise FileOperationError(f"獲取可用空間失敗: {e}", file_path=str(path), operation="disk_space")