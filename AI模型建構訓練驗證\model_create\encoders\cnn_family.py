"""
CNN編碼器族：統一實現所有CNN架構
整合MobileNet、UNet、ResNet等系列
"""

from typing import Callable, List, Optional, Dict, Any, Tuple
import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial

from ..core.base_encoder import CNNEncoder, register_encoder


def _make_divisible(ch, divisor=8, min_ch=None):
    """確保通道數能被divisor整除"""
    if min_ch is None:
        min_ch = divisor
    new_ch = max(min_ch, int(ch + divisor / 2) // divisor * divisor)
    if new_ch < 0.9 * ch:
        new_ch += divisor
    return new_ch


class ConvBNActivation(nn.Sequential):
    """卷積+批歸一化+激活函數組合"""
    
    def __init__(self,
                 in_planes: int,
                 out_planes: int,
                 kernel_size: int = 3,
                 stride: int = 1,
                 groups: int = 1,
                 norm_layer: Optional[Callable[..., nn.Module]] = None,
                 activation_layer: Optional[Callable[..., nn.Module]] = None):
        padding = (kernel_size - 1) // 2
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        if activation_layer is None:
            activation_layer = nn.ReLU6
        
        super(ConvBNActivation, self).__init__(
            nn.Conv2d(in_channels=in_planes,
                     out_channels=out_planes,
                     kernel_size=kernel_size,
                     stride=stride,
                     padding=padding,
                     groups=groups,
                     bias=False),
            norm_layer(out_planes),
            activation_layer(inplace=True)
        )


class SqueezeExcitation(nn.Module):
    """SE注意力模組"""
    
    def __init__(self, input_c: int, squeeze_factor: int = 4):
        super(SqueezeExcitation, self).__init__()
        squeeze_c = _make_divisible(input_c // squeeze_factor, 8)
        self.fc1 = nn.Conv2d(input_c, squeeze_c, 1)
        self.fc2 = nn.Conv2d(squeeze_c, input_c, 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        scale = F.adaptive_avg_pool2d(x, output_size=(1, 1))
        scale = self.fc1(scale)
        scale = F.relu(scale, inplace=True)
        scale = self.fc2(scale)
        scale = F.hardsigmoid(scale, inplace=True)
        return scale * x


class InvertedResidualConfig:
    """倒殘差塊配置"""
    
    def __init__(self,
                 input_c: int,
                 kernel: int,
                 expanded_c: int,
                 out_c: int,
                 use_se: bool,
                 activation: str,
                 stride: int,
                 width_multi: float):
        self.input_c = self.adjust_channels(input_c, width_multi)
        self.kernel = kernel
        self.expanded_c = self.adjust_channels(expanded_c, width_multi)
        self.out_c = self.adjust_channels(out_c, width_multi)
        self.use_se = use_se
        self.use_hs = activation == "HS"  # 是否使用h-swish激活
        self.stride = stride

    @staticmethod
    def adjust_channels(channels: int, width_multi: float):
        return _make_divisible(channels * width_multi, 8)


class InvertedResidual(nn.Module):
    """倒殘差塊"""
    
    def __init__(self,
                 cnf: InvertedResidualConfig,
                 norm_layer: Callable[..., nn.Module]):
        super(InvertedResidual, self).__init__()

        if cnf.stride not in [1, 2]:
            raise ValueError("非法的步長值")

        self.use_res_connect = (cnf.stride == 1 and cnf.input_c == cnf.out_c)

        layers: List[nn.Module] = []
        activation_layer = nn.Hardswish if cnf.use_hs else nn.ReLU

        # 展開層
        if cnf.expanded_c != cnf.input_c:
            layers.append(ConvBNActivation(cnf.input_c,
                                         cnf.expanded_c,
                                         kernel_size=1,
                                         norm_layer=norm_layer,
                                         activation_layer=activation_layer))

        # 深度卷積
        layers.append(ConvBNActivation(cnf.expanded_c,
                                     cnf.expanded_c,
                                     kernel_size=cnf.kernel,
                                     stride=cnf.stride,
                                     groups=cnf.expanded_c,
                                     norm_layer=norm_layer,
                                     activation_layer=activation_layer))

        # SE模組
        if cnf.use_se:
            layers.append(SqueezeExcitation(cnf.expanded_c))

        # 點卷積
        layers.append(ConvBNActivation(cnf.expanded_c,
                                     cnf.out_c,
                                     kernel_size=1,
                                     norm_layer=norm_layer,
                                     activation_layer=nn.Identity))

        self.block = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        result = self.block(x)
        if self.use_res_connect:
            result += x
        return result


class CSPModule(nn.Module):
    """CSP (Cross Stage Partial) 模組"""
    
    def __init__(self, in_channels: int, out_channels: int, 
                 num_blocks: int, ratio: float = 0.5):
        super().__init__()
        
        hidden_channels = int(out_channels * ratio)
        
        # 分支1：直接連接
        self.branch1 = nn.Conv2d(in_channels, hidden_channels, 1, bias=False)
        
        # 分支2：經過多個塊處理
        self.branch2_conv1 = nn.Conv2d(in_channels, hidden_channels, 1, bias=False)
        
        # 中間處理塊
        blocks = []
        for _ in range(num_blocks):
            blocks.append(nn.Conv2d(hidden_channels, hidden_channels, 3, padding=1, bias=False))
            blocks.append(nn.BatchNorm2d(hidden_channels))
            blocks.append(nn.ReLU(inplace=True))
        self.branch2_blocks = nn.Sequential(*blocks)
        
        self.branch2_conv2 = nn.Conv2d(hidden_channels, hidden_channels, 1, bias=False)
        
        # 融合層
        self.fusion = nn.Conv2d(hidden_channels * 2, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 分支1
        branch1 = self.branch1(x)
        
        # 分支2
        branch2 = self.branch2_conv1(x)
        branch2 = self.branch2_blocks(branch2)
        branch2 = self.branch2_conv2(branch2)
        
        # 融合
        out = torch.cat([branch1, branch2], dim=1)
        out = self.fusion(out)
        out = self.bn(out)
        out = self.act(out)
        
        return out


@register_encoder("mobilenet")
class MobileNetEncoder(CNNEncoder):
    """統一的MobileNet編碼器實現"""
    
    def __init__(self,
                 input_channels: int = 3,
                 output_channels: List[int] = None,
                 variant: str = "v3_small",
                 version: str = "v3_small",
                 width_mult: float = 1.0,
                 enable_csp: bool = False,
                 csp_ratio: float = 0.5,
                 **kwargs):
        
        self.version = version
        self.width_mult = width_mult
        self.enable_csp = enable_csp
        self.csp_ratio = csp_ratio
        
        # 設置輸出通道
        if output_channels is None:
            if "small" in version:
                output_channels = [16, 24, 40, 96]
            else:  # large
                output_channels = [24, 40, 80, 160]
        
        super().__init__(input_channels=input_channels,
                        output_channels=output_channels, **kwargs)
    
    def _build_encoder(self):
        """構建MobileNet編碼器"""
        norm_layer = partial(nn.BatchNorm2d, eps=0.001, momentum=0.01)
        
        # 獲取倒殘差塊配置
        inverted_residual_setting = self._get_inverted_residual_setting()
        
        # 第一層
        firstconv_output_c = inverted_residual_setting[0].input_c
        self.first_conv = ConvBNActivation(self.input_channels,
                                         firstconv_output_c,
                                         kernel_size=3,
                                         stride=2,
                                         norm_layer=norm_layer,
                                         activation_layer=nn.Hardswish)
        
        # 構建特徵層
        self.features = self._build_features(inverted_residual_setting, norm_layer)
        
        # 設置特徵信息
        self._setup_feature_info()
    
    def _get_inverted_residual_setting(self) -> List[InvertedResidualConfig]:
        """獲取倒殘差塊配置"""
        
        if self.version == "v3_small":
            # MobileNetV3-Small配置
            inverted_residual_setting = [
                # input_c, kernel, expanded_c, out_c, use_se, activation, stride
                InvertedResidualConfig(16, 3, 16, 16, True, "RE", 2, self.width_mult),
                InvertedResidualConfig(16, 3, 72, 24, False, "RE", 2, self.width_mult),
                InvertedResidualConfig(24, 3, 88, 24, False, "RE", 1, self.width_mult),
                InvertedResidualConfig(24, 5, 96, 40, True, "HS", 2, self.width_mult),
                InvertedResidualConfig(40, 5, 240, 40, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(40, 5, 240, 40, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(40, 5, 120, 48, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(48, 5, 144, 48, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(48, 5, 288, 96, True, "HS", 2, self.width_mult),
                InvertedResidualConfig(96, 5, 576, 96, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(96, 5, 576, 96, True, "HS", 1, self.width_mult),
            ]
        else:  # v3_large
            # MobileNetV3-Large配置  
            inverted_residual_setting = [
                InvertedResidualConfig(16, 3, 16, 16, False, "RE", 1, self.width_mult),
                InvertedResidualConfig(16, 3, 64, 24, False, "RE", 2, self.width_mult),
                InvertedResidualConfig(24, 3, 72, 24, False, "RE", 1, self.width_mult),
                InvertedResidualConfig(24, 5, 72, 40, True, "RE", 2, self.width_mult),
                InvertedResidualConfig(40, 5, 120, 40, True, "RE", 1, self.width_mult),
                InvertedResidualConfig(40, 5, 120, 40, True, "RE", 1, self.width_mult),
                InvertedResidualConfig(40, 3, 240, 80, False, "HS", 2, self.width_mult),
                InvertedResidualConfig(80, 3, 200, 80, False, "HS", 1, self.width_mult),
                InvertedResidualConfig(80, 3, 184, 80, False, "HS", 1, self.width_mult),
                InvertedResidualConfig(80, 3, 184, 80, False, "HS", 1, self.width_mult),
                InvertedResidualConfig(80, 3, 480, 112, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(112, 3, 672, 112, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(112, 5, 672, 160, True, "HS", 2, self.width_mult),
                InvertedResidualConfig(160, 5, 960, 160, True, "HS", 1, self.width_mult),
                InvertedResidualConfig(160, 5, 960, 160, True, "HS", 1, self.width_mult),
            ]
        
        return inverted_residual_setting
    
    def _build_features(self, inverted_residual_setting, norm_layer):
        """構建特徵提取層"""
        features = nn.ModuleList()
        
        for cnf in inverted_residual_setting:
            if self.enable_csp:
                # CSP增強版本
                block = CSPModule(cnf.input_c, cnf.out_c, 
                                num_blocks=2, ratio=self.csp_ratio)
            else:
                # 標準倒殘差塊
                block = InvertedResidual(cnf, norm_layer)
            
            features.append(block)
        
        return features
    
    def _setup_feature_info(self):
        """設置特徵層信息"""
        strides = [4, 8, 16, 32]  # 相對於輸入的步長
        
        for i, (channels, stride) in enumerate(zip(self.output_channels, strides)):
            self.feature_info.append({
                'num_chs': channels,
                'reduction': stride,
                'module': f'features.{i}'
            })
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """前向傳播"""
        # 第一層卷積
        x = self.first_conv(x)
        
        outputs = []
        feature_indices = self._get_feature_indices()
        
        # 通過所有特徵層
        for i, feature in enumerate(self.features):
            x = feature(x)
            
            if i in feature_indices:
                outputs.append(x)
        
        return outputs
    
    def _get_feature_indices(self) -> List[int]:
        """獲取要輸出的特徵層索引"""
        if self.version == "v3_small":
            # 對應不同解析度的特徵層
            return [1, 3, 6, 8]  # 1/4, 1/8, 1/16, 1/32
        else:  # v3_large
            return [2, 4, 7, 12]  # 1/4, 1/8, 1/16, 1/32


@register_encoder("csp_mobilenet")
class CSPMobileNetEncoder(MobileNetEncoder):
    """CSP增強的MobileNet編碼器"""
    
    def __init__(self, **kwargs):
        # 強制啟用CSP
        kwargs['enable_csp'] = True
        super().__init__(**kwargs)


@register_encoder("unet")
class UNetEncoder(CNNEncoder):
    """UNet編碼器實現"""
    
    def __init__(self,
                 input_channels: int = 3,
                 output_channels: List[int] = None,
                 variant: str = "default",
                 use_batchnorm: bool = True,
                 use_dropout: bool = True,
                 dropout_rate: float = 0.1,
                 **kwargs):
        
        self.use_batchnorm = use_batchnorm
        self.use_dropout = use_dropout
        self.dropout_rate = dropout_rate
        
        if output_channels is None:
            output_channels = [64, 128, 256, 512]
        
        super().__init__(input_channels=input_channels,
                        output_channels=output_channels, **kwargs)
    
    def _build_encoder(self):
        """構建UNet編碼器"""
        self.down_blocks = nn.ModuleList()
        self.pools = nn.ModuleList()
        
        in_channels = self.input_channels
        
        for out_channels in self.output_channels:
            # 下採樣塊
            down_block = self._make_down_block(in_channels, out_channels)
            self.down_blocks.append(down_block)
            
            # 池化層（除了最後一層）
            if out_channels != self.output_channels[-1]:
                self.pools.append(nn.MaxPool2d(2))
            
            in_channels = out_channels
        
        # 設置特徵信息
        self._setup_feature_info()
    
    def _make_down_block(self, in_channels: int, out_channels: int) -> nn.Module:
        """創建下採樣塊"""
        layers = []
        
        # 第一個卷積
        layers.append(nn.Conv2d(in_channels, out_channels, 3, padding=1))
        if self.use_batchnorm:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU(inplace=True))
        
        # 第二個卷積
        layers.append(nn.Conv2d(out_channels, out_channels, 3, padding=1))
        if self.use_batchnorm:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU(inplace=True))
        
        # Dropout
        if self.use_dropout:
            layers.append(nn.Dropout2d(self.dropout_rate))
        
        return nn.Sequential(*layers)
    
    def _setup_feature_info(self):
        """設置特徵層信息"""
        strides = [2**i for i in range(1, len(self.output_channels) + 1)]
        
        for i, (channels, stride) in enumerate(zip(self.output_channels, strides)):
            self.feature_info.append({
                'num_chs': channels,
                'reduction': stride,
                'module': f'down_blocks.{i}'
            })
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """前向傳播"""
        outputs = []
        
        for i, (down_block, pool) in enumerate(zip(self.down_blocks, self.pools)):
            x = down_block(x)
            outputs.append(x)
            x = pool(x)
        
        # 最後一個塊（沒有池化）
        if len(self.down_blocks) > len(self.pools):
            x = self.down_blocks[-1](x)
            outputs.append(x)
        
        return outputs


@register_encoder("resnet")
class ResNetEncoder(CNNEncoder):
    """ResNet編碼器實現"""
    
    def __init__(self,
                 input_channels: int = 3,
                 output_channels: List[int] = None,
                 variant: str = "resnet50",
                 **kwargs):
        
        self.variant = variant
        
        if output_channels is None:
            output_channels = [64, 128, 256, 512]
        
        super().__init__(input_channels=input_channels,
                        output_channels=output_channels, **kwargs)
    
    def _build_encoder(self):
        """構建ResNet編碼器"""
        # TODO: 實現ResNet架構
        # 這裡可以添加ResNet的具體實現
        pass
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """前向傳播"""
        # TODO: 實現ResNet前向傳播
        return [x]