#!/usr/bin/env python3
"""
🧪 LabelMe JSON輸出功能測試腳本
測試從YOLO模型預測結果生成LabelMe JSON檔案的功能

測試項目：
- LabelMe JSON生成器基本功能
- Mask轉Polygon轉換
- JSON格式驗證
- 檔案保存功能
- 整合器功能測試
"""

import json
import numpy as np
import cv2
from pathlib import Path
import tempfile
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')


def create_test_mask(width: int = 100, height: int = 100) -> np.ndarray:
    """創建測試用的二進制mask"""
    mask = np.zeros((height, width), dtype=np.uint8)
    # 創建一個簡單的矩形區域
    mask[20:80, 30:70] = 255
    return mask


def test_labelme_json_generator():
    """測試LabelMe JSON生成器基本功能"""
    print("🧪 測試LabelMe JSON生成器...")
    
    try:
        from models.inference.labelme_json_generator import create_labelme_json_generator
        
        # 創建生成器
        generator = create_labelme_json_generator(simplify_tolerance=2.0)
        print("✅ 生成器創建成功")
        
        # 測試mask轉polygon
        test_mask = create_test_mask()
        polygon = generator.mask_to_polygon(test_mask)
        
        if polygon and len(polygon) >= 3:
            print(f"✅ Mask轉Polygon成功: {len(polygon)} 個點")
        else:
            print("❌ Mask轉Polygon失敗")
            return False
        
        # 測試shape物件創建
        shape = generator.create_shape_object("test_class", polygon)
        expected_keys = ['label', 'points', 'group_id', 'shape_type', 'flags']
        
        if all(key in shape for key in expected_keys):
            print("✅ Shape物件創建成功")
        else:
            print("❌ Shape物件格式錯誤")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ LabelMe JSON生成器測試失敗: {e}")
        return False


def test_json_format_validation():
    """測試生成的JSON格式驗證"""
    print("\n🧪 測試JSON格式驗證...")
    
    try:
        from models.inference.labelme_json_generator import create_labelme_json_generator
        
        generator = create_labelme_json_generator()
        
        # 準備測試數據
        test_detections = [
            {
                'class_id': 0,
                'confidence': 0.85,
                'mask': create_test_mask(80, 60)
            },
            {
                'class_id': 1,
                'confidence': 0.72,
                'mask': create_test_mask(90, 70)
            }
        ]
        
        class_names = {0: 'linear_crack', 1: 'joint'}
        image_shape = (100, 120)
        image_path = 'test_image.jpg'
        
        # 生成JSON
        labelme_json = generator.generate_labelme_json(
            detections=test_detections,
            image_path=image_path,
            image_shape=image_shape,
            class_names=class_names
        )
        
        # 驗證JSON結構
        required_keys = ['version', 'flags', 'shapes', 'imagePath', 'imageData', 'imageHeight', 'imageWidth']
        
        if all(key in labelme_json for key in required_keys):
            print("✅ JSON結構正確")
        else:
            print("❌ JSON結構缺少必要字段")
            return False
        
        # 驗證shapes內容
        if len(labelme_json['shapes']) == 2:
            print("✅ 檢測結果轉換正確")
        else:
            print(f"❌ 檢測結果數量錯誤: 期望2個，實際{len(labelme_json['shapes'])}個")
            return False
        
        # 驗證圖像信息
        if (labelme_json['imageHeight'] == 100 and 
            labelme_json['imageWidth'] == 120 and
            labelme_json['imagePath'] == 'test_image.jpg'):
            print("✅ 圖像信息正確")
        else:
            print("❌ 圖像信息錯誤")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ JSON格式驗證失敗: {e}")
        return False


def test_file_operations():
    """測試檔案操作功能"""
    print("\n🧪 測試檔案操作...")
    
    try:
        from models.inference.labelme_json_generator import create_labelme_json_generator
        
        generator = create_labelme_json_generator()
        
        # 創建臨時目錄
        with tempfile.TemporaryDirectory() as temp_dir:
            # 準備測試數據
            test_detection = {
                'class_id': 2,
                'confidence': 0.90,
                'mask': create_test_mask()
            }
            
            class_names = {2: 'potholes'}
            
            # 創建測試圖像
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            test_image_path = Path(temp_dir) / "test_image.jpg"
            cv2.imwrite(str(test_image_path), test_image)
            
            # 測試process_image_results方法
            json_path = generator.process_image_results(
                image_path=str(test_image_path),
                results=[test_detection],
                output_dir=temp_dir,
                class_names=class_names
            )
            
            if json_path and Path(json_path).exists():
                print("✅ 檔案保存成功")
                
                # 驗證保存的JSON檔案
                with open(json_path, 'r', encoding='utf-8') as f:
                    saved_json = json.load(f)
                
                if saved_json and 'shapes' in saved_json and len(saved_json['shapes']) > 0:
                    print("✅ 保存的JSON檔案格式正確")
                else:
                    print("❌ 保存的JSON檔案格式錯誤")
                    return False
            else:
                print("❌ 檔案保存失敗")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 檔案操作測試失敗: {e}")
        return False


def test_integration_module():
    """測試整合模組功能"""
    print("\n🧪 測試LabelMe整合模組...")
    
    try:
        # 創建模擬配置管理器
        class MockConfig:
            def __init__(self):
                self.labelme_output = MockLabelMeConfig()
                self.paths = MockPathsConfig()
                self.classes = {
                    0: MockClassConfig('linear_crack'),
                    1: MockClassConfig('joint'),
                    2: MockClassConfig('potholes')
                }
        
        class MockLabelMeConfig:
            def __init__(self):
                self.enable_labelme_output = True
                self.labelme_output_dir = 'test_labelme_json'
                self.labelme_simplify_tolerance = 2.0
                self.labelme_min_polygon_points = 3
                self.labelme_include_confidence = False
        
        class MockPathsConfig:
            def __init__(self):
                self.output_path = tempfile.mkdtemp()
        
        class MockClassConfig:
            def __init__(self, name):
                self.name = name
        
        # 創建整合器
        from models.inference.labelme_integration import create_labelme_integration
        
        config = MockConfig()
        integration = create_labelme_integration(config)
        
        if integration.enabled:
            print("✅ 整合器初始化成功")
        else:
            print("❌ 整合器初始化失敗")
            return False
        
        # 測試輸出目錄創建
        if integration.output_dir.exists():
            print("✅ 輸出目錄創建成功")
        else:
            print("❌ 輸出目錄創建失敗")
            return False
        
        # 測試統計信息
        stats = integration.get_output_stats()
        if stats.get('enabled') and 'output_directory' in stats:
            print("✅ 統計信息獲取成功")
        else:
            print("❌ 統計信息獲取失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 整合模組測試失敗: {e}")
        return False


def run_all_tests():
    """運行所有測試"""
    print("🧪 LabelMe JSON輸出功能測試")
    print("=" * 60)
    
    tests = [
        ("LabelMe JSON生成器", test_labelme_json_generator),
        ("JSON格式驗證", test_json_format_validation),
        ("檔案操作", test_file_operations),
        ("整合模組", test_integration_module)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通過")
            else:
                print(f"❌ {test_name}: 失敗")
        except Exception as e:
            print(f"❌ {test_name}: 異常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 測試結果: {passed}/{total} 個測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！LabelMe JSON輸出功能正常")
        print("\n📝 功能摘要:")
        print("  ✅ Mask到Polygon轉換")
        print("  ✅ LabelMe JSON格式生成")
        print("  ✅ 檔案保存和讀取")
        print("  ✅ 批次處理支援")
        print("  ✅ 配置管理整合")
        print("\n🚀 使用方法:")
        print("  1. 在run_unified_yolo.py中設定 enable_labelme_output = True")
        print("  2. 調整 labelme_simplify_tolerance 控制polygon簡化程度")
        print("  3. 運行推理，JSON檔案將保存到 output_path/labelme_json/ 目錄")
    else:
        print("❌ 部分測試失敗，請檢查相關功能")
    
    return passed == total


if __name__ == "__main__":
    run_all_tests()