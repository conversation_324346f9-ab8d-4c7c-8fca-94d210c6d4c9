"""
Enhanced Model Factory - 統一工廠系統整合所有新架構

整合所有實現的架構變體：
- CSP_IFormer 2024 變體 (Efficient, Mamba, Enhanced)
- SegMAN 變體 (MambaVision, Efficient, Enhanced)
- 原有的模型系統

提供統一的創建接口和配置管理
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Union, List, Tuple
from dataclasses import dataclass
import warnings
from pathlib import Path

from .model_factory import ModelFactory as BaseModelFactory
from .registry import register_encoder, register_decoder, register_model
from .config_manager import ConfigManager


@dataclass 
class EnhancedModelConfig:
    """增強模型配置類"""
    
    # 模型基本信息
    model_family: str = 'csp_iformer'  # 'csp_iformer', 'segman', 'hybrid'
    variant: str = 'enhanced'  # 'original', 'efficient', 'mamba', 'enhanced'
    size: str = 'small'  # 'nano', 'tiny', 'small', 'medium', 'base', 'large'
    
    # 任務配置
    task: str = 'classification'  # 'classification', 'segmentation', 'detection'
    num_classes: int = 1000
    
    # 輸入配置
    image_size: int = 224
    in_chans: int = 3
    
    # 架構參數
    embed_dims: Optional[List[int]] = None
    depths: Optional[List[int]] = None
    num_heads: Optional[List[int]] = None
    mlp_ratios: Optional[List[float]] = None
    
    # 訓練參數
    drop_path_rate: float = 0.1
    dropout: float = 0.1
    
    # 效率優化
    use_checkpoint: bool = False
    mixed_precision: bool = True
    
    # 特殊功能
    use_channel_shuffle: bool = True
    use_dropkey: bool = True
    use_pyramid_attn: bool = False  # For enhanced variants
    use_rope: bool = False  # For enhanced variants
    csp_ratio: float = 0.5
    
    # Mamba特定參數
    d_state: int = 16
    expansion_ratio: float = 2.0
    
    # 解碼器參數（分割任務）
    decoder_channels: int = 256
    feat_proj_dim: int = 256
    use_fpn: bool = True


class EnhancedModelFactory(BaseModelFactory):
    """增強模型工廠，整合所有新架構"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        super().__init__(config_manager)
        
        # 註冊所有新架構
        self._register_new_architectures()
        
        # 預定義配置
        self.predefined_configs = self._get_predefined_configs()
    
    def _register_new_architectures(self):
        """註冊所有新實現的架構"""
        
        # CSP_IFormer 2024 變體
        self._register_csp_iformer_variants()
        
        # SegMAN 變體
        self._register_segman_variants()
        
        # 混合架構
        self._register_hybrid_architectures()
    
    def _register_csp_iformer_variants(self):
        """註冊CSP_IFormer 2024變體"""
        
        # 2024 Efficient variants
        try:
            from ..encoder.VIT.CSP_IFormer_v2024_efficient import (
                csp_iformer_efficient_tiny,
                csp_iformer_efficient_small,
                csp_iformer_efficient_base
            )
            
            @register_encoder('csp_iformer_efficient_tiny')
            class CSP_IFormer_Efficient_Tiny(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_efficient_tiny(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
            @register_encoder('csp_iformer_efficient_small')
            class CSP_IFormer_Efficient_Small(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_efficient_small(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
            @register_encoder('csp_iformer_efficient_base')
            class CSP_IFormer_Efficient_Base(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_efficient_base(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
        except ImportError as e:
            warnings.warn(f"Could not register CSP_IFormer efficient variants: {e}")
        
        # 2024 Mamba variants
        try:
            from ..encoder.VIT.CSP_IFormer_v2024_mamba import (
                csp_iformer_mamba_tiny,
                csp_iformer_mamba_small,
                csp_iformer_mamba_base
            )
            
            @register_encoder('csp_iformer_mamba_tiny')
            class CSP_IFormer_Mamba_Tiny(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_mamba_tiny(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
            @register_encoder('csp_iformer_mamba_small')
            class CSP_IFormer_Mamba_Small(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_mamba_small(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
            @register_encoder('csp_iformer_mamba_base')
            class CSP_IFormer_Mamba_Base(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_mamba_base(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
        except ImportError as e:
            warnings.warn(f"Could not register CSP_IFormer mamba variants: {e}")
        
        # 2024 Enhanced variants
        try:
            from ..encoder.VIT.CSP_IFormer_v2024_enhanced import (
                csp_iformer_enhanced_small,
                csp_iformer_enhanced_base,
                csp_iformer_enhanced_large
            )
            
            @register_encoder('csp_iformer_enhanced_small')
            class CSP_IFormer_Enhanced_Small(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_enhanced_small(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
            @register_encoder('csp_iformer_enhanced_base')
            class CSP_IFormer_Enhanced_Base(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_enhanced_base(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
            @register_encoder('csp_iformer_enhanced_large')
            class CSP_IFormer_Enhanced_Large(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = csp_iformer_enhanced_large(**kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    return self.model.forward_features(x)
            
        except ImportError as e:
            warnings.warn(f"Could not register CSP_IFormer enhanced variants: {e}")
    
    def _register_segman_variants(self):
        """註冊SegMAN變體"""
        
        try:
            from ..full_model.segman.segman_factory import (
                SegMANFactory, create_segman_model
            )
            
            # 註冊SegMAN工廠包裝器
            @register_model('segman_factory')
            class SegMANFactoryWrapper(nn.Module):
                def __init__(self, variant='enhanced', size='small', **kwargs):
                    super().__init__()
                    self.factory = SegMANFactory()
                    self.variant = variant
                    self.size = size
                    self.kwargs = kwargs
                
                def create_encoder(self, **override_kwargs):
                    merged_kwargs = {**self.kwargs, **override_kwargs}
                    return self.factory.create_encoder(self.variant, self.size, **merged_kwargs)
                
                def create_segmentation_model(self, **override_kwargs):
                    merged_kwargs = {**self.kwargs, **override_kwargs}
                    return self.factory.create_segmentation_model(self.variant, self.size, **merged_kwargs)
            
            # 註冊直接的SegMAN變體
            @register_encoder('segman_mambavision_small')
            class SegMAN_MambaVision_Small(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = create_segman_model('mambavision', 'small', 'classification', **kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    if hasattr(self.model, 'forward_features'):
                        return self.model.forward_features(x)
                    return self.model(x)
            
            @register_encoder('segman_efficient_small')
            class SegMAN_Efficient_Small(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = create_segman_model('efficient', 'small', 'classification', **kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    if hasattr(self.model, 'forward_features'):
                        return self.model.forward_features(x)
                    return self.model(x)
            
            @register_encoder('segman_enhanced_small')
            class SegMAN_Enhanced_Small(nn.Module):
                def __init__(self, **kwargs):
                    super().__init__()
                    self.model = create_segman_model('enhanced', 'small', 'classification', **kwargs)
                
                def forward(self, x):
                    return self.model(x)
                
                def forward_features(self, x):
                    if hasattr(self.model, 'forward_features'):
                        return self.model.forward_features(x)
                    return self.model(x)
                
        except ImportError as e:
            warnings.warn(f"Could not register SegMAN variants: {e}")
    
    def _register_hybrid_architectures(self):
        """註冊混合架構"""
        
        @register_model('csp_iformer_segman_hybrid')
        class CSP_IFormer_SegMAN_Hybrid(nn.Module):
            """CSP_IFormer編碼器 + SegMAN解碼器的混合架構"""
            
            def __init__(self, 
                         encoder_variant='enhanced',
                         encoder_size='small',
                         decoder_variant='enhanced',
                         num_classes=5,
                         **kwargs):
                super().__init__()
                
                # 創建CSP_IFormer編碼器
                factory = EnhancedModelFactory()
                self.encoder = factory.create_enhanced_encoder(
                    family='csp_iformer',
                    variant=encoder_variant,
                    size=encoder_size,
                    num_classes=0,  # 不要分類頭
                    **kwargs
                )
                
                # 創建SegMAN解碼器
                try:
                    from ..full_model.segman.segman_factory import SegMANFactory
                    segman_factory = SegMANFactory()
                    
                    # 推斷編碼器輸出通道
                    if hasattr(self.encoder, 'embed_dims'):
                        in_channels = self.encoder.embed_dims
                    elif hasattr(self.encoder, 'model') and hasattr(self.encoder.model, 'embed_dim'):
                        # 單一embed_dim的情況
                        dim = self.encoder.model.embed_dim
                        in_channels = [dim//4, dim//2, dim, dim]
                    else:
                        in_channels = [96, 192, 384, 768]  # 默認
                    
                    self.decoder = segman_factory.create_decoder(
                        variant=decoder_variant,
                        in_channels=in_channels,
                        num_classes=num_classes
                    )
                    
                except ImportError:
                    warnings.warn("SegMAN decoder not available, using identity")
                    self.decoder = nn.Identity()
            
            def forward(self, x):
                # 提取特徵
                if hasattr(self.encoder, 'forward_features'):
                    features = self.encoder.forward_features(x)
                    if not isinstance(features, (list, tuple)):
                        # 如果是單一特徵，創建多尺度特徵
                        features = [features] * 4
                else:
                    features = self.encoder(x)
                    if not isinstance(features, (list, tuple)):
                        features = [features] * 4
                
                # 解碼
                output = self.decoder(features)
                return output
    
    def _get_predefined_configs(self) -> Dict[str, Dict[str, EnhancedModelConfig]]:
        """獲取預定義配置"""
        
        return {
            'csp_iformer': {
                'efficient_tiny': EnhancedModelConfig(
                    model_family='csp_iformer',
                    variant='efficient',
                    size='tiny',
                    embed_dims=[192, 192, 192, 192],
                    depths=[6, 6, 6, 6],
                    num_heads=[6, 6, 6, 6],
                    drop_path_rate=0.02
                ),
                'efficient_small': EnhancedModelConfig(
                    model_family='csp_iformer',
                    variant='efficient',
                    size='small',
                    embed_dims=[384, 384, 384, 384],
                    depths=[8, 8, 8, 8],
                    num_heads=[8, 8, 8, 8],
                    drop_path_rate=0.05
                ),
                'mamba_tiny': EnhancedModelConfig(
                    model_family='csp_iformer',
                    variant='mamba',
                    size='tiny',
                    embed_dims=[384, 384, 384, 384],
                    depths=[6, 6, 6, 6],
                    d_state=8,
                    expansion_ratio=1.5,
                    drop_path_rate=0.05
                ),
                'mamba_small': EnhancedModelConfig(
                    model_family='csp_iformer',
                    variant='mamba',
                    size='small',
                    embed_dims=[512, 512, 512, 512],
                    depths=[8, 8, 8, 8],
                    d_state=16,
                    expansion_ratio=2.0,
                    drop_path_rate=0.1
                ),
                'enhanced_small': EnhancedModelConfig(
                    model_family='csp_iformer',
                    variant='enhanced',
                    size='small',
                    embed_dims=[384, 384, 384, 384],
                    depths=[8, 8, 8, 8],
                    num_heads=[8, 8, 8, 8],
                    use_pyramid_attn=True,
                    use_rope=True,
                    drop_path_rate=0.1
                ),
                'enhanced_base': EnhancedModelConfig(
                    model_family='csp_iformer',
                    variant='enhanced',
                    size='base',
                    embed_dims=[768, 768, 768, 768],
                    depths=[12, 12, 12, 12],
                    num_heads=[12, 12, 12, 12],
                    use_pyramid_attn=True,
                    use_rope=True,
                    drop_path_rate=0.15
                )
            },
            'segman': {
                'mambavision_small': EnhancedModelConfig(
                    model_family='segman',
                    variant='mambavision',
                    size='small',
                    embed_dims=[96, 192, 384, 768],
                    depths=[2, 2, 6, 2],
                    drop_path_rate=0.1
                ),
                'efficient_nano': EnhancedModelConfig(
                    model_family='segman',
                    variant='efficient',
                    size='nano',
                    embed_dims=[32, 64, 128, 256],
                    depths=[1, 1, 2, 1],
                    drop_path_rate=0.02
                ),
                'enhanced_small': EnhancedModelConfig(
                    model_family='segman',
                    variant='enhanced',
                    size='small',
                    embed_dims=[96, 192, 384, 768],
                    depths=[2, 2, 6, 2],
                    use_fpn=True,
                    drop_path_rate=0.1
                )
            },
            'hybrid': {
                'csp_segman_small': EnhancedModelConfig(
                    model_family='hybrid',
                    variant='csp_iformer_segman',
                    size='small',
                    task='segmentation',
                    use_fpn=True,
                    drop_path_rate=0.1
                )
            }
        }
    
    def create_enhanced_encoder(self,
                              family: str = 'csp_iformer',
                              variant: str = 'enhanced',
                              size: str = 'small',
                              config: Optional[EnhancedModelConfig] = None,
                              **kwargs) -> nn.Module:
        """
        創建增強編碼器
        
        Args:
            family: 模型系列 ('csp_iformer', 'segman')
            variant: 變體 ('efficient', 'mamba', 'enhanced')
            size: 尺寸 ('tiny', 'small', 'base', 'large')
            config: 自定義配置
            **kwargs: 額外參數
            
        Returns:
            編碼器模型
        """
        
        # 構建編碼器名稱
        encoder_name = f"{family}_{variant}_{size}"
        
        # 獲取配置
        if config is None:
            config_key = f"{variant}_{size}"
            if family in self.predefined_configs and config_key in self.predefined_configs[family]:
                config = self.predefined_configs[family][config_key]
            else:
                config = EnhancedModelConfig(
                    model_family=family,
                    variant=variant,
                    size=size
                )
        
        # 準備參數
        model_kwargs = {
            'num_classes': config.num_classes,
            'drop_path_rate': config.drop_path_rate,
            'use_channel_shuffle': config.use_channel_shuffle,
            'use_dropkey': config.use_dropkey,
            **kwargs
        }
        
        # 添加特定參數
        if config.embed_dims:
            model_kwargs['embed_dims'] = config.embed_dims
        if config.depths:
            model_kwargs['depths'] = config.depths
        if config.num_heads:
            model_kwargs['num_heads'] = config.num_heads
        
        # Mamba特定參數
        if variant == 'mamba':
            model_kwargs.update({
                'd_state': config.d_state,
                'expansion_ratio': config.expansion_ratio
            })
        
        # Enhanced特定參數
        if variant == 'enhanced':
            model_kwargs.update({
                'use_pyramid_attn': config.use_pyramid_attn,
                'use_rope': config.use_rope,
                'csp_ratio': config.csp_ratio
            })
        
        # 創建編碼器
        try:
            from .registry import get_encoder
            encoder_class = get_encoder(encoder_name)
            return encoder_class(**model_kwargs)
        except KeyError:
            raise ValueError(f"編碼器 {encoder_name} 未註冊。可用編碼器: {self.list_available_encoders()}")
    
    def create_enhanced_model(self,
                            family: str = 'csp_iformer',
                            variant: str = 'enhanced',
                            size: str = 'small',
                            task: str = 'classification',
                            num_classes: int = 1000,
                            config: Optional[EnhancedModelConfig] = None,
                            **kwargs) -> nn.Module:
        """
        創建完整增強模型
        
        Args:
            family: 模型系列
            variant: 變體
            size: 尺寸
            task: 任務類型
            num_classes: 類別數
            config: 自定義配置
            **kwargs: 額外參數
            
        Returns:
            完整模型
        """
        
        if task == 'classification':
            # 創建分類模型（編碼器）
            return self.create_enhanced_encoder(
                family=family,
                variant=variant,
                size=size,
                config=config,
                num_classes=num_classes,
                **kwargs
            )
        
        elif task == 'segmentation':
            # 創建分割模型
            if family == 'segman':
                # 直接使用SegMAN工廠
                try:
                    from ..full_model.segman.segman_factory import create_segman_model
                    return create_segman_model(
                        variant=variant,
                        size=size,
                        task='segmentation',
                        num_classes=num_classes,
                        **kwargs
                    )
                except ImportError:
                    raise ImportError("SegMAN factory not available")
            
            elif family == 'hybrid':
                # 創建混合模型
                try:
                    from .registry import get_model
                    hybrid_class = get_model('csp_iformer_segman_hybrid')
                    return hybrid_class(
                        encoder_variant=variant,
                        encoder_size=size,
                        num_classes=num_classes,
                        **kwargs
                    )
                except KeyError:
                    raise ValueError("Hybrid model not available")
            
            else:
                # 為其他系列創建編碼器-解碼器組合
                encoder = self.create_enhanced_encoder(
                    family=family,
                    variant=variant,
                    size=size,
                    config=config,
                    num_classes=0,  # 無分類頭
                    **kwargs
                )
                
                # 簡單解碼器 (可擴展)
                decoder = nn.Sequential(
                    nn.AdaptiveAvgPool2d(1),
                    nn.Flatten(),
                    nn.Linear(encoder.model.embed_dim, num_classes) if hasattr(encoder, 'model') and hasattr(encoder.model, 'embed_dim') else nn.Linear(768, num_classes)
                )
                
                return nn.Sequential(encoder, decoder)
        
        else:
            raise ValueError(f"不支持的任務類型: {task}")
    
    def list_available_encoders(self) -> List[str]:
        """列出所有可用編碼器"""
        from .registry import list_encoders
        return list_encoders()
    
    def list_available_models(self) -> List[str]:
        """列出所有可用模型"""
        from .registry import list_models
        return list_models()
    
    def get_model_info(self, 
                      family: str,
                      variant: str,
                      size: str) -> Dict[str, Any]:
        """獲取模型信息"""
        
        try:
            # 創建模型並計算參數
            model = self.create_enhanced_encoder(family, variant, size, num_classes=1000)
            
            param_count = sum(p.numel() for p in model.parameters()) / 1e6
            
            # 測試前向傳播
            test_input = torch.randn(1, 3, 224, 224)
            with torch.no_grad():
                try:
                    output = model(test_input)
                    output_shape = output.shape
                    
                    if hasattr(model, 'forward_features'):
                        features = model.forward_features(test_input)
                        if isinstance(features, (list, tuple)):
                            feature_shapes = [f.shape for f in features]
                        else:
                            feature_shapes = [features.shape]
                    else:
                        feature_shapes = ["N/A"]
                        
                except Exception as e:
                    output_shape = f"Error: {e}"
                    feature_shapes = ["Error"]
            
            config_key = f"{variant}_{size}"
            config = None
            if family in self.predefined_configs and config_key in self.predefined_configs[family]:
                config = self.predefined_configs[family][config_key]
            
            return {
                'family': family,
                'variant': variant,
                'size': size,
                'parameters_M': f"{param_count:.2f}",
                'output_shape': output_shape,
                'feature_shapes': feature_shapes,
                'embed_dims': config.embed_dims if config else "N/A",
                'depths': config.depths if config else "N/A",
                'drop_path_rate': config.drop_path_rate if config else "N/A"
            }
            
        except Exception as e:
            return {
                'family': family,
                'variant': variant,
                'size': size,
                'error': str(e)
            }


def create_enhanced_model(
    family: str = 'csp_iformer',
    variant: str = 'enhanced', 
    size: str = 'small',
    task: str = 'classification',
    num_classes: int = 1000,
    **kwargs
) -> nn.Module:
    """
    便捷函數：創建增強模型
    
    Args:
        family: 模型系列
        variant: 變體
        size: 尺寸
        task: 任務
        num_classes: 類別數
        **kwargs: 額外參數
        
    Returns:
        模型實例
    """
    factory = EnhancedModelFactory()
    return factory.create_enhanced_model(
        family=family,
        variant=variant,
        size=size,
        task=task,
        num_classes=num_classes,
        **kwargs
    )


if __name__ == "__main__":
    # 測試增強工廠
    factory = EnhancedModelFactory()
    
    print("Enhanced Model Factory Test")
    print("=" * 60)
    
    # 測試編碼器列表
    print(f"Available encoders: {len(factory.list_available_encoders())}")
    print(f"Available models: {len(factory.list_available_models())}")
    
    # 測試不同架構
    test_configs = [
        ('csp_iformer', 'efficient', 'small'),
        ('csp_iformer', 'mamba', 'small'), 
        ('csp_iformer', 'enhanced', 'small'),
        ('segman', 'mambavision', 'small'),
        ('segman', 'enhanced', 'small'),
    ]
    
    print("\nTesting different architectures:")
    print("-" * 60)
    
    for family, variant, size in test_configs:
        try:
            print(f"\nTesting {family}-{variant}-{size}:")
            
            # 獲取模型信息
            info = factory.get_model_info(family, variant, size)
            print(f"  Parameters: {info.get('parameters_M', 'N/A')}M")
            print(f"  Output shape: {info.get('output_shape', 'N/A')}")
            print(f"  Feature shapes: {info.get('feature_shapes', 'N/A')}")
            
        except Exception as e:
            print(f"  Error: {e}")
    
    print(f"\n" + "=" * 60)
    print("Enhanced factory test completed!")