#!/usr/bin/env python3
"""
處理器使用示例
展示如何使用資料集分割和圖像處理功能
"""

from pathlib import Path
import sys

# 添加模組路徑
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.processors.dataset_divider import DatasetDivider

def dataset_split_example():
    """資料集分割示例"""
    
    print("📊 資料集分割示例")
    print("=" * 50)
    
    # 設定分割參數
    divider = DatasetDivider(
        input_dir="./dataset",
        output_dir="./split_dataset",
        train_ratio=0.8,
        val_ratio=0.1,
        test_ratio=0.1,
        random_seed=42
    )
    
    try:
        # 執行分割
        result = divider.split()
        
        print("✅ 分割完成！")
        print(f"🏷️ 訓練集: {result['train_count']} 個文件")
        print(f"🔍 驗證集: {result['val_count']} 個文件") 
        print(f"🧪 測試集: {result['test_count']} 個文件")
        
    except Exception as e:
        print(f"❌ 分割失敗: {e}")

def batch_processing_example():
    """批次處理示例"""
    
    print("\n🔄 批次處理示例")
    print("=" * 50)
    
    # 批次轉換多個資料夾
    folders = [
        {'input': './data1', 'output': './output1', 'format': 'labelme_to_yolo'},
        {'input': './data2', 'output': './output2', 'format': 'yolo_to_coco'},
        {'input': './data3', 'output': './output3', 'format': 'voc_to_labelme'}
    ]
    
    for i, folder_config in enumerate(folders, 1):
        print(f"\n📁 處理資料夾 {i}/3: {folder_config['input']}")
        print(f"🔄 轉換格式: {folder_config['format']}")
        
        # 這裡可以加入實際的轉換邏輯
        print("✅ 處理完成")

def main():
    """主要示例函數"""
    
    print("🚀 資料處理器示例")
    print("=" * 60)
    
    # 執行各種示例
    dataset_split_example()
    batch_processing_example()
    
    print("\n🎯 所有示例執行完成！")
    print("\n💡 提示：")
    print("- 修改路徑參數以適應您的資料")
    print("- 查看各個處理器的詳細配置選項")
    print("- 使用GUI界面進行互動式操作")

if __name__ == "__main__":
    main()