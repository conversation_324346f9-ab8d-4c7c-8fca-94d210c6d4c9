#!/usr/bin/env python3
"""
簡化版GT標註讀取調試
"""

import sys
import json
from pathlib import Path

def analyze_annotation_file(file_path):
    """分析標註文件內容"""
    print(f"\n📁 分析文件: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        if file_path.endswith('.json'):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("✅ JSON文件解析成功")
            print(f"📊 基本信息:")
            print(f"   - 圖像路徑: {data.get('imagePath', 'N/A')}")
            print(f"   - 圖像尺寸: {data.get('imageWidth', 'N/A')} x {data.get('imageHeight', 'N/A')}")
            print(f"   - 形狀數量: {len(data.get('shapes', []))}")
            
            # 分析每個形狀
            shapes = data.get('shapes', [])
            for i, shape in enumerate(shapes):
                label = shape.get('label', 'unknown')
                shape_type = shape.get('shape_type', 'unknown')
                points = shape.get('points', [])
                
                print(f"   形狀 {i+1}: {label} ({shape_type})")
                print(f"     點數據: {points}")
                
                # 嘗試計算邊界框
                if shape_type == "rectangle" and len(points) == 2:
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    bbox = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
                    print(f"     計算邊界框: {bbox}")
                elif shape_type == "polygon" and points:
                    if isinstance(points[0], list):
                        # 格式: [[x1,y1], [x2,y2], ...]
                        xs = [p[0] for p in points]
                        ys = [p[1] for p in points]
                    else:
                        # 格式: [x1, y1, x2, y2, ...]
                        xs = points[::2]
                        ys = points[1::2]
                    
                    if xs and ys:
                        bbox = [min(xs), min(ys), max(xs), max(ys)]
                        print(f"     計算邊界框: {bbox}")
                        
        elif file_path.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print("✅ TXT文件讀取成功")
            print(f"📊 行數: {len(lines)}")
            
            for i, line in enumerate(lines[:5]):  # 顯示前5行
                line = line.strip()
                if line:
                    parts = line.split()
                    print(f"   行 {i+1}: {line}")
                    if len(parts) >= 5:
                        try:
                            class_id = int(parts[0])
                            cx, cy, bw, bh = map(float, parts[1:5])
                            print(f"     類別ID: {class_id}, 中心: ({cx:.3f}, {cy:.3f}), 尺寸: ({bw:.3f}, {bh:.3f})")
                        except ValueError as e:
                            print(f"     ❌ 解析錯誤: {e}")
                    else:
                        print(f"     ❌ 格式錯誤: 需要至少5個值")
            
            if len(lines) > 5:
                print(f"   ... 還有 {len(lines) - 5} 行")
                
    except Exception as e:
        print(f"❌ 文件分析失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    print("🔍 GT標註文件調試工具")
    print("=" * 40)
    
    # 測試路徑設定
    test_dirs = [
        r"D:\image\5_test_image_test\test1\label",
        r"D:\image\5_test_image_test\test1",
        "./test_label",
        "./label"
    ]
    
    found_files = []
    
    # 搜索標註文件
    for test_dir in test_dirs:
        dir_path = Path(test_dir)
        if dir_path.exists():
            print(f"✅ 找到目錄: {dir_path}")
            
            # 搜索JSON和TXT文件
            json_files = list(dir_path.glob("*.json"))
            txt_files = list(dir_path.glob("*.txt"))
            
            print(f"   📄 JSON文件: {len(json_files)} 個")
            print(f"   📄 TXT文件: {len(txt_files)} 個")
            
            found_files.extend(json_files)
            found_files.extend(txt_files)
            
            # 顯示文件列表
            for file in (json_files + txt_files)[:3]:  # 顯示前3個
                print(f"   - {file.name}")
            
        else:
            print(f"❌ 目錄不存在: {dir_path}")
    
    # 分析找到的文件
    if found_files:
        print(f"\n🔬 分析找到的 {len(found_files)} 個標註文件")
        
        # 分析前幾個文件
        for file_path in found_files[:2]:  # 分析前2個文件
            analyze_annotation_file(str(file_path))
            
    else:
        print("\n❌ 未找到任何標註文件")
        print("💡 請檢查以下可能的原因:")
        print("   1. 路徑設定是否正確")
        print("   2. 標註文件是否存在")
        print("   3. 文件權限是否正確")
        
        # 創建示例文件進行測試
        print("\n🛠️  創建示例標註文件...")
        
        # 創建示例LabelMe文件
        example_labelme = {
            "version": "5.0.1",
            "flags": {},
            "shapes": [
                {
                    "label": "linear",
                    "points": [[100, 100], [300, 200]],
                    "group_id": None,
                    "shape_type": "rectangle",
                    "flags": {}
                },
                {
                    "label": "alligator",
                    "points": [[50, 50], [150, 60], [140, 150], [60, 140]],
                    "group_id": None,
                    "shape_type": "polygon",
                    "flags": {}
                }
            ],
            "imagePath": "test.jpg",
            "imageData": None,
            "imageHeight": 480,
            "imageWidth": 640
        }
        
        # 保存示例文件
        example_json_path = Path("example_labelme.json")
        with open(example_json_path, 'w', encoding='utf-8') as f:
            json.dump(example_labelme, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 創建示例LabelMe文件: {example_json_path}")
        
        # 創建示例YOLO文件  
        example_yolo_content = """0 0.312 0.416 0.312 0.208
1 0.156 0.312 0.156 0.208
0 0.750 0.500 0.200 0.300"""
        
        example_txt_path = Path("example_yolo.txt")
        with open(example_txt_path, 'w', encoding='utf-8') as f:
            f.write(example_yolo_content)
        
        print(f"✅ 創建示例YOLO文件: {example_txt_path}")
        
        # 分析剛創建的示例文件
        print("\n🧪 測試示例文件:")
        analyze_annotation_file(str(example_json_path))
        analyze_annotation_file(str(example_txt_path))

if __name__ == "__main__":
    main()