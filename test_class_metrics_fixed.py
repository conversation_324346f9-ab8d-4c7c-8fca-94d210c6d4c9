#!/usr/bin/env python3
"""
測試新的 class_metrics 計算方法
"""

def test_new_class_metrics_logic():
    """測試新的class_metrics計算邏輯（模擬圖像級別方法）"""
    
    # 模擬預測數據
    predictions = [
        {'class_id': 0, 'bbox': [10, 10, 50, 50], 'confidence': 0.9, 'class_name': 'class_0'},
        {'class_id': 0, 'bbox': [100, 100, 140, 140], 'confidence': 0.8, 'class_name': 'class_0'},  # 這個會是FP
        {'class_id': 1, 'bbox': [200, 200, 240, 240], 'confidence': 0.7, 'class_name': 'class_1'},
    ]
    
    # 模擬GT數據
    gt_annotations = [
        {'class_id': 0, 'bbox': [12, 12, 52, 52], 'class_name': 'class_0'},  # 與第1個預測匹配
        {'class_id': 1, 'bbox': [202, 202, 242, 242], 'class_name': 'class_1'},  # 與第3個預測匹配
        {'class_id': 0, 'bbox': [300, 300, 340, 340], 'class_name': 'class_0'},  # 沒有匹配的預測，會是FN
    ]
    
    print("=== 測試數據 ===")
    print(f"預測數量: {len(predictions)}")
    print(f"GT數量: {len(gt_annotations)}")
    print()
    
    def calculate_iou(box1, box2):
        """計算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def match_predictions_to_gt(pred_boxes, pred_classes, pred_scores, gt_boxes, gt_classes, iou_threshold=0.5):
        """簡化的匹配算法"""
        matches = []
        used_gt = set()
        
        # 按置信度排序
        sorted_indices = sorted(range(len(pred_scores)), key=lambda i: pred_scores[i], reverse=True)
        
        for pred_idx in sorted_indices:
            pred_box = pred_boxes[pred_idx]
            pred_class = pred_classes[pred_idx]
            
            best_iou = 0.0
            best_gt_idx = -1
            
            for gt_idx, (gt_box, gt_class) in enumerate(zip(gt_boxes, gt_classes)):
                if gt_idx in used_gt or gt_class != pred_class:
                    continue
                
                iou = calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            matched = best_iou >= iou_threshold and best_gt_idx >= 0
            if matched:
                used_gt.add(best_gt_idx)
            
            matches.append({
                'pred_idx': pred_idx,
                'gt_idx': best_gt_idx if matched else -1,
                'iou': best_iou,
                'matched': matched,
                'class_id': pred_class
            })
        
        return matches
    
    # 計算class_metrics
    class_metrics = {}
    pred_classes = [pred['class_id'] for pred in predictions]
    gt_classes = [gt['class_id'] for gt in gt_annotations]
    all_classes = set(pred_classes + gt_classes)
    
    print("=== 新方法計算 ===")
    for class_id in all_classes:
        class_name = f'class_{class_id}'
        
        # 收集該類別的預測和GT
        class_predictions = [pred for pred in predictions if pred['class_id'] == class_id]
        class_gt = [gt for gt in gt_annotations if gt['class_id'] == class_id]
        
        print(f"\n--- {class_name} ---")
        print(f"類別預測數: {len(class_predictions)}")
        print(f"類別GT數: {len(class_gt)}")
        
        # 使用與圖像級別相同的計算邏輯
        if not class_predictions and not class_gt:
            continue
            
        if not class_predictions:
            # 沒有預測，全部為FN
            class_tp, class_fp, class_fn = 0, 0, len(class_gt)
        elif not class_gt:
            # 沒有GT，全部為FP
            class_tp, class_fp, class_fn = 0, len(class_predictions), 0
        else:
            # 使用匹配算法計算
            class_pred_boxes = [pred['bbox'] for pred in class_predictions]
            class_pred_classes = [pred['class_id'] for pred in class_predictions]
            class_pred_scores = [pred['confidence'] for pred in class_predictions]
            
            class_gt_boxes = [gt['bbox'] for gt in class_gt]
            class_gt_classes = [gt['class_id'] for gt in class_gt]
            
            # 使用匹配算法
            class_matches = match_predictions_to_gt(
                class_pred_boxes, class_pred_classes, class_pred_scores,
                class_gt_boxes, class_gt_classes, iou_threshold=0.5
            )
            
            print(f"匹配結果: {class_matches}")
            
            # 計算TP, FP, FN
            class_tp = sum(1 for match in class_matches if match['matched'])
            class_fp = len(class_predictions) - class_tp
            class_fn = len(class_gt) - class_tp
        
        print(f"TP: {class_tp}, FP: {class_fp}, FN: {class_fn}")
        
        # 計算指標
        class_precision = class_tp / (class_tp + class_fp) if (class_tp + class_fp) > 0 else 0.0
        class_recall = class_tp / (class_tp + class_fn) if (class_tp + class_fn) > 0 else 0.0
        class_f1 = 2 * class_precision * class_recall / (class_precision + class_recall) if (class_precision + class_recall) > 0 else 0.0
        
        print(f"Precision: {class_precision:.3f}, Recall: {class_recall:.3f}, F1: {class_f1:.3f}")
        
        class_metrics[class_name] = {
            'precision': class_precision,
            'recall': class_recall,
            'f1_score': class_f1,
            'tp': class_tp,
            'fp': class_fp,
            'fn': class_fn,
            'gt_count': len(class_gt),
            'pred_count': len(class_predictions)
        }
    
    print("\n=== 最終結果 ===")
    for class_name, metrics in class_metrics.items():
        print(f"{class_name}: TP={metrics['tp']}, FP={metrics['fp']}, FN={metrics['fn']}")
        print(f"  P={metrics['precision']:.3f}, R={metrics['recall']:.3f}, F1={metrics['f1_score']:.3f}")
    
    # 驗證結果
    print(f"\n=== 驗證 ===")
    # 期望結果：
    # class_0: 1個TP (第1個預測匹配), 1個FP (第2個預測無匹配), 1個FN (第3個GT無匹配)
    # class_1: 1個TP (第3個預測匹配), 0個FP, 0個FN
    
    expected_class_0 = {'tp': 1, 'fp': 1, 'fn': 1}
    expected_class_1 = {'tp': 1, 'fp': 0, 'fn': 0}
    
    class_0_correct = (class_metrics['class_0']['tp'] == expected_class_0['tp'] and 
                      class_metrics['class_0']['fp'] == expected_class_0['fp'] and 
                      class_metrics['class_0']['fn'] == expected_class_0['fn'])
    
    class_1_correct = (class_metrics['class_1']['tp'] == expected_class_1['tp'] and 
                      class_metrics['class_1']['fp'] == expected_class_1['fp'] and 
                      class_metrics['class_1']['fn'] == expected_class_1['fn'])
    
    print(f"Class_0 預期: {expected_class_0}")
    print(f"Class_0 實際: {{'tp': {class_metrics['class_0']['tp']}, 'fp': {class_metrics['class_0']['fp']}, 'fn': {class_metrics['class_0']['fn']}}}")
    print(f"Class_0 正確: {class_0_correct}")
    
    print(f"Class_1 預期: {expected_class_1}")
    print(f"Class_1 實際: {{'tp': {class_metrics['class_1']['tp']}, 'fp': {class_metrics['class_1']['fp']}, 'fn': {class_metrics['class_1']['fn']}}}")
    print(f"Class_1 正確: {class_1_correct}")
    
    if class_0_correct and class_1_correct:
        print("\n✅ 新方法修正成功！現在使用與圖像級別相同的正確計算邏輯")
    else:
        print("\n❌ 新方法仍有問題")

if __name__ == "__main__":
    test_new_class_metrics_logic()