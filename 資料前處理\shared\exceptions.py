"""
統一異常處理體系

定義所有資料前處理工具使用的異常類型
"""

from typing import Optional, Dict, Any


class ProcessingError(Exception):
    """處理過程中的基礎異常"""
    
    def __init__(self, 
                 message: str, 
                 file_path: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None,
                 original_exception: Optional[Exception] = None):
        """
        初始化處理異常
        
        Args:
            message: 錯誤消息
            file_path: 相關文件路徑
            details: 詳細信息字典
            original_exception: 原始異常
        """
        super().__init__(message)
        self.file_path = file_path
        self.details = details or {}
        self.original_exception = original_exception
        
    def __str__(self) -> str:
        """格式化錯誤信息"""
        parts = [super().__str__()]
        
        if self.file_path:
            parts.append(f"文件路徑: {self.file_path}")
            
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            parts.append(f"詳細信息: {details_str}")
            
        if self.original_exception:
            parts.append(f"原始異常: {self.original_exception}")
            
        return " | ".join(parts)


class ConfigError(ProcessingError):
    """配置相關錯誤"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.config_key = config_key


class ValidationError(ProcessingError):
    """驗證錯誤"""
    
    def __init__(self, message: str, validation_type: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.validation_type = validation_type


class ConversionError(ProcessingError):
    """轉換錯誤"""
    
    def __init__(self, 
                 message: str, 
                 source_format: Optional[str] = None,
                 target_format: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.source_format = source_format
        self.target_format = target_format


class FileOperationError(ProcessingError):
    """文件操作錯誤"""
    
    def __init__(self, 
                 message: str, 
                 operation: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.operation = operation


class FormatDetectionError(ProcessingError):
    """格式檢測錯誤"""
    
    def __init__(self, message: str, attempted_formats: Optional[list] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.attempted_formats = attempted_formats or []


class ImageProcessingError(ProcessingError):
    """圖像處理錯誤"""
    
    def __init__(self, 
                 message: str, 
                 image_size: Optional[tuple] = None,
                 processing_step: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.image_size = image_size
        self.processing_step = processing_step


class AnnotationError(ProcessingError):
    """標註處理錯誤"""
    
    def __init__(self, 
                 message: str, 
                 annotation_type: Optional[str] = None,
                 class_name: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.annotation_type = annotation_type
        self.class_name = class_name


class DatasetError(ProcessingError):
    """數據集處理錯誤"""
    
    def __init__(self, 
                 message: str, 
                 dataset_type: Optional[str] = None,
                 split_name: Optional[str] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.dataset_type = dataset_type
        self.split_name = split_name


class AugmentationError(ProcessingError):
    """數據增強錯誤"""
    
    def __init__(self, 
                 message: str, 
                 augmentation_method: Optional[str] = None,
                 parameters: Optional[Dict[str, Any]] = None,
                 **kwargs):
        super().__init__(message, **kwargs)
        self.augmentation_method = augmentation_method
        self.augmentation_parameters = parameters


# 異常工廠函數
def create_processing_error(error_type: str, 
                          message: str, 
                          **kwargs) -> ProcessingError:
    """
    創建特定類型的處理異常
    
    Args:
        error_type: 錯誤類型
        message: 錯誤消息
        **kwargs: 其他參數
        
    Returns:
        ProcessingError: 對應的異常實例
    """
    error_classes = {
        'config': ConfigError,
        'validation': ValidationError,
        'conversion': ConversionError,
        'file_operation': FileOperationError,
        'format_detection': FormatDetectionError,
        'image_processing': ImageProcessingError,
        'annotation': AnnotationError,
        'dataset': DatasetError,
        'augmentation': AugmentationError,
    }
    
    error_class = error_classes.get(error_type, ProcessingError)
    return error_class(message, **kwargs)


# 異常處理裝飾器
def handle_processing_errors(logger=None):
    """
    異常處理裝飾器
    
    Args:
        logger: 日誌記錄器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ProcessingError:
                # 重新拋出處理異常
                raise
            except Exception as e:
                # 包裝其他異常
                if logger:
                    logger.error(f"未處理的異常: {e}")
                raise ProcessingError(
                    f"未預期的錯誤: {e}",
                    original_exception=e
                ) from e
        return wrapper
    return decorator