"""
統一的數據集模組
整合原始Dataset_read.py和base_dataset.py的功能
提供完整的計算機視覺數據集處理能力
"""

import os
import numpy as np
import cv2
import json
import logging
import shutil
from abc import ABC, abstractmethod
from typing import Optional, Dict, List, Tuple, Union, Any
from pathlib import Path
from collections import defaultdict
import torch
from torch.utils.data import Dataset
from PIL import Image, ImageDraw, ImageFont
import albumentations as A
from albumentations.pytorch import ToTensorV2
import matplotlib.pyplot as plt

# 設置繪圖樣式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class DatasetConfig:
    """數據集配置類"""
    
    def __init__(self,
                 data_dir: str,
                 size: int = 224,
                 split: str = 'train',
                 log_enabled: bool = True,
                 image_extensions: List[str] = None,
                 label_extensions: List[str] = None):
        """
        Args:
            data_dir: 數據目錄
            size: 圖像大小
            split: 數據分割
            log_enabled: 是否啟用日誌
            image_extensions: 支援的圖像格式
            label_extensions: 支援的標籤格式
        """
        self.data_dir = Path(data_dir)
        self.size = size
        self.split = split
        self.log_enabled = log_enabled
        self.image_extensions = image_extensions or ['.jpg', '.jpeg', '.png', '.bmp']
        self.label_extensions = label_extensions or ['.txt', '.json', '.xml']


class ImageProcessor:
    """圖像處理工具類"""
    
    @staticmethod
    def denormalize(img: Union[np.ndarray, torch.Tensor],
                   mean: Tuple[float, float, float] = (0.485, 0.456, 0.406),
                   std: Tuple[float, float, float] = (0.229, 0.224, 0.225)) -> np.ndarray:
        """
        反歸一化圖像
        
        Args:
            img: 輸入圖像
            mean: 歸一化均值
            std: 歸一化標準差
            
        Returns:
            反歸一化後的圖像 (0-255)
        """
        if isinstance(img, torch.Tensor):
            img = img.clone().detach().cpu().numpy()
        
        # 轉換為HWC格式
        if img.ndim == 3 and img.shape[0] == 3:
            img = np.transpose(img, (1, 2, 0))
        
        # 反歸一化
        mean = np.array(mean)
        std = np.array(std)
        img = img * std + mean
        img = np.clip(img, 0, 1)
        img = (img * 255).astype(np.uint8)
        
        return img
    
    @staticmethod
    def resize_with_aspect_ratio(image: np.ndarray, 
                               target_size: int,
                               interpolation: int = cv2.INTER_LINEAR) -> np.ndarray:
        """
        保持寬高比的圖像縮放
        
        Args:
            image: 輸入圖像
            target_size: 目標大小
            interpolation: 插值方法
            
        Returns:
            縮放後的圖像
        """
        h, w = image.shape[:2]
        scale = target_size / max(h, w)
        new_w, new_h = int(w * scale), int(h * scale)
        
        resized = cv2.resize(image, (new_w, new_h), interpolation=interpolation)
        
        # 填充到目標大小
        result = np.zeros((target_size, target_size, image.shape[2]), dtype=image.dtype)
        start_y = (target_size - new_h) // 2
        start_x = (target_size - new_w) // 2
        result[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        
        return result


class FontManager:
    """字體管理器 - 處理中文字體顯示"""
    
    _font_cache: Dict[Tuple[str, int], ImageFont.FreeTypeFont] = {}
    
    @classmethod
    def get_font(cls, font_size: int = 20) -> ImageFont.FreeTypeFont:
        """
        獲取支援中文的字體
        
        Args:
            font_size: 字體大小
            
        Returns:
            字體對象
        """
        cache_key = ('default', font_size)
        if cache_key in cls._font_cache:
            return cls._font_cache[cache_key]
        
        # 嘗試載入系統字體
        font_paths = [
            '/System/Library/Fonts/PingFang.ttc',         # macOS
            '/usr/share/fonts/truetype/arphic/uming.ttc', # Ubuntu
            'C:/Windows/Fonts/msyh.ttc',                  # Windows 微軟雅黑
            'C:/Windows/Fonts/mingliu.ttc',               # Windows 細明體
            'C:/Windows/Fonts/simsun.ttc',                # Windows 宋體
            'C:/Windows/Fonts/kaiu.ttf',                  # Windows 標楷體
            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
        ]
        
        font = None
        for path in font_paths:
            try:
                if os.path.exists(path):
                    font = ImageFont.truetype(path, font_size)
                    break
            except Exception:
                continue
        
        # 如果沒有找到字體，使用默認字體
        if font is None:
            try:
                font = ImageFont.load_default()
            except Exception:
                font = ImageFont.load_default()
        
        cls._font_cache[cache_key] = font
        return font
    
    @classmethod
    def draw_chinese_text(cls,
                         img: np.ndarray,
                         text: str,
                         position: Tuple[int, int],
                         font_size: int = 20,
                         color: Tuple[int, int, int] = (0, 0, 0)) -> np.ndarray:
        """
        在圖像上繪製中文文字
        
        Args:
            img: 輸入圖像
            text: 要繪製的文字
            position: 文字位置
            font_size: 字體大小
            color: 文字顏色
            
        Returns:
            繪製文字後的圖像
        """
        # 轉換為PIL格式
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 獲取字體
        font = cls.get_font(font_size)
        
        # 繪製文字
        draw.text(position, text, font=font, fill=color)
        
        # 轉換回OpenCV格式
        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)


class FileFinder:
    """文件查找工具類"""
    
    @staticmethod
    def find_image_label_pairs(data_dir: Path,
                              split: str,
                              image_extensions: List[str],
                              label_extensions: List[str]) -> Tuple[List[str], List[str]]:
        """
        查找圖像和標籤文件對
        
        Args:
            data_dir: 數據目錄
            split: 數據分割
            image_extensions: 圖像格式
            label_extensions: 標籤格式
            
        Returns:
            (image_files, label_files)
        """
        image_files = []
        label_files = []
        
        # 可能的圖像目錄
        image_dirs = [
            data_dir / 'images' / split,
            data_dir / 'JPEGImages',
            data_dir / split,
            data_dir
        ]
        
        # 可能的標籤目錄
        label_dirs = [
            data_dir / 'labels' / split,
            data_dir / 'labels',
            data_dir / 'Annotations',
            data_dir / split,
            data_dir
        ]
        
        # 檢查分割文件
        split_file = FileFinder._find_split_file(data_dir, split)
        
        if split_file:
            image_files, label_files = FileFinder._load_from_split_file(
                split_file, image_dirs, label_dirs, image_extensions, label_extensions
            )
        else:
            image_files, label_files = FileFinder._scan_directories(
                image_dirs, label_dirs, image_extensions, label_extensions
            )
        
        return image_files, label_files
    
    @staticmethod
    def _find_split_file(data_dir: Path, split: str) -> Optional[Path]:
        """查找分割文件"""
        possible_paths = [
            data_dir / f'images/{split}.txt',
            data_dir / f'ImageSets/Main/{split}.txt',
            data_dir / f'{split}.txt'
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        return None
    
    @staticmethod
    def _load_from_split_file(split_file: Path,
                             image_dirs: List[Path],
                             label_dirs: List[Path],
                             image_extensions: List[str],
                             label_extensions: List[str]) -> Tuple[List[str], List[str]]:
        """從分割文件載入文件列表"""
        image_files = []
        label_files = []
        
        with open(split_file, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            img_name = line.strip()
            
            # 查找圖像文件
            img_file = FileFinder._find_file(img_name, image_dirs, image_extensions)
            label_file = FileFinder._find_file(img_name, label_dirs, label_extensions)
            
            if img_file and label_file:
                image_files.append(str(img_file))
                label_files.append(str(label_file))
        
        return image_files, label_files
    
    @staticmethod
    def _scan_directories(image_dirs: List[Path],
                         label_dirs: List[Path],
                         image_extensions: List[str],
                         label_extensions: List[str]) -> Tuple[List[str], List[str]]:
        """掃描目錄查找文件"""
        image_files = []
        label_files = []
        
        for image_dir in image_dirs:
            if not image_dir.exists():
                continue
            
            for file_path in image_dir.iterdir():
                if file_path.suffix.lower() in image_extensions:
                    base_name = file_path.stem
                    
                    # 查找對應的標籤文件
                    label_file = None
                    for label_dir in label_dirs:
                        if not label_dir.exists():
                            continue
                        
                        for ext in label_extensions:
                            possible_label = label_dir / f"{base_name}{ext}"
                            if possible_label.exists():
                                label_file = str(possible_label)
                                break
                        
                        if label_file:
                            break
                    
                    if label_file:
                        image_files.append(str(file_path))
                        label_files.append(label_file)
        
        return image_files, label_files
    
    @staticmethod
    def _find_file(base_name: str, 
                   dirs: List[Path], 
                   extensions: List[str]) -> Optional[Path]:
        """在多個目錄中查找文件"""
        for dir_path in dirs:
            if not dir_path.exists():
                continue
            
            for ext in extensions:
                file_path = dir_path / f"{base_name}{ext}"
                if file_path.exists():
                    return file_path
        
        return None


class BaseVisionDataset(Dataset, ABC):
    """
    基礎視覺數據集類
    提供通用的數據集功能
    """
    
    def __init__(self, config: DatasetConfig, transform: Optional[A.Compose] = None):
        """
        Args:
            config: 數據集配置
            transform: 數據變換
        """
        self.config = config
        self.transform = transform
        self.use_tensor = self._check_tensor_transform()
        
        # 初始化文件列表
        self.image_files: List[str] = []
        self.label_files: List[str] = []
        
        # 用於視覺化的最後處理結果
        self.last_processed: Optional[Dict[str, Any]] = None
        
        if config.log_enabled:
            logger.info(f"初始化 {config.split} 數據集")
        
        # 子類需要實現具體的文件查找邏輯
        self._find_files()
        
        if config.log_enabled:
            logger.info(f"找到 {len(self.image_files)} 筆數據")
    
    def _check_tensor_transform(self) -> bool:
        """檢查是否使用張量轉換"""
        if self.transform is None:
            return False
        
        for transform in self.transform.transforms:
            if isinstance(transform, ToTensorV2):
                if self.config.log_enabled:
                    logger.info("檢測到ToTensorV2轉換")
                return True
        return False
    
    @abstractmethod
    def _find_files(self):
        """查找圖像和標籤文件 - 子類必須實現"""
        pass
    
    @abstractmethod
    def _load_label(self, label_path: str, image_shape: Tuple[int, int]) -> Tuple[np.ndarray, List, List]:
        """載入標籤 - 子類必須實現"""
        pass
    
    def __len__(self) -> int:
        return len(self.image_files)
    
    def __getitem__(self, idx: int) -> Tuple[Union[torch.Tensor, np.ndarray], 
                                           Union[torch.Tensor, np.ndarray], 
                                           str]:
        """
        獲取數據項
        
        Args:
            idx: 索引
            
        Returns:
            (image, mask/label, filename)
        """
        if self.config.log_enabled:
            logger.debug(f"讀取第 {idx} 筆數據")
        
        # 獲取文件路徑
        image_path = self.image_files[idx]
        label_path = self.label_files[idx] if idx < len(self.label_files) else None
        filename = os.path.basename(image_path)
        
        # 讀取圖像
        image = np.array(Image.open(image_path).convert('RGB'))
        original_height, original_width = image.shape[:2]
        
        # 讀取標籤
        if label_path and os.path.exists(label_path):
            mask, boxes, labels = self._load_label(label_path, (original_width, original_height))
        else:
            mask = np.zeros((original_height, original_width), dtype=np.uint8)
            boxes, labels = [], []
        
        # 保存原始數據用於視覺化
        self.last_processed = {
            'image': image.copy(),
            'mask': mask.copy(),
            'original_image': image.copy(),
            'boxes': boxes.copy() if boxes else [],
            'labels': labels.copy() if labels else [],
            'filename': filename
        }
        
        # 應用變換
        if self.transform:
            image, mask, boxes, labels = self._apply_transform(image, mask, boxes, labels)
        else:
            image, mask = self._default_transform(image, mask)
        
        # 更新處理後的數據
        self._update_processed_data(image, mask, boxes, labels)
        
        return image, mask, filename
    
    def _apply_transform(self, 
                        image: np.ndarray, 
                        mask: np.ndarray,
                        boxes: List,
                        labels: List) -> Tuple:
        """應用數據變換"""
        try:
            if boxes and len(boxes) > 0:
                # 有邊界框的變換
                transformed = self.transform(
                    image=image,
                    mask=mask,
                    bboxes=boxes,
                    category_ids=labels
                )
                return (
                    transformed["image"],
                    transformed["mask"],
                    transformed.get("bboxes", []),
                    transformed.get("category_ids", [])
                )
            else:
                # 只有圖像和掩碼的變換
                transformed = self.transform(image=image, mask=mask)
                return transformed["image"], transformed["mask"], [], []
        
        except Exception as e:
            logger.error(f"數據變換失敗: {e}")
            return self._default_transform(image, mask) + (boxes, labels)
    
    def _default_transform(self, image: np.ndarray, mask: np.ndarray) -> Tuple:
        """默認變換（調整大小並轉換為張量）"""
        # 調整大小
        resized_image = cv2.resize(image, (self.config.size, self.config.size))
        resized_mask = cv2.resize(mask, (self.config.size, self.config.size), 
                                interpolation=cv2.INTER_NEAREST)
        
        # 轉換為張量
        image_tensor = torch.from_numpy(resized_image.transpose(2, 0, 1)).float() / 255.0
        mask_tensor = torch.from_numpy(resized_mask).long()
        
        return image_tensor, mask_tensor
    
    def _update_processed_data(self, image, mask, boxes, labels):
        """更新處理後的數據"""
        if self.use_tensor:
            # 張量轉換為NumPy用於視覺化
            if isinstance(image, torch.Tensor):
                self.last_processed['processed_image'] = image.permute(1, 2, 0).cpu().numpy()
            if isinstance(mask, torch.Tensor):
                self.last_processed['processed_mask'] = mask.cpu().numpy()
        else:
            self.last_processed['processed_image'] = image
            self.last_processed['processed_mask'] = mask
        
        self.last_processed['processed_boxes'] = boxes
        self.last_processed['processed_labels'] = labels
    
    def visualize(self, 
                 idx: int,
                 figsize: Tuple[int, int] = (15, 5),
                 class_names: Optional[Dict[int, str]] = None) -> None:
        """
        視覺化數據項
        
        Args:
            idx: 數據索引
            figsize: 圖形大小
            class_names: 類別名稱映射
        """
        # 獲取數據
        if self.last_processed is None or idx != getattr(self, '_last_visualized_idx', None):
            self[idx]
            self._last_visualized_idx = idx
        
        # 獲取圖像和掩碼數據
        original_image = self.last_processed['original_image']
        processed_image = self.last_processed.get('processed_image', original_image)
        mask = self.last_processed.get('processed_mask', self.last_processed['mask'])
        filename = self.last_processed['filename']
        
        # 處理張量格式
        if isinstance(processed_image, torch.Tensor):
            processed_image = processed_image.permute(1, 2, 0).cpu().numpy()
        if isinstance(mask, torch.Tensor):
            mask = mask.cpu().numpy()
        
        # 反歸一化
        processed_image = ImageProcessor.denormalize(processed_image)
        
        # 創建子圖
        fig, axes = plt.subplots(1, 3, figsize=figsize)
        
        # 原始圖像
        axes[0].imshow(original_image)
        axes[0].set_title(f'原始圖像: {filename}')
        axes[0].axis('off')
        
        # 標籤掩碼
        unique_classes = np.unique(mask)
        unique_classes = [c for c in unique_classes if c > 0]
        
        if unique_classes:
            # 創建彩色掩碼
            colored_mask = self._create_colored_mask(mask, unique_classes)
            axes[1].imshow(colored_mask)
            axes[1].set_title('標籤掩碼')
        else:
            axes[1].imshow(np.zeros_like(processed_image))
            axes[1].set_title('無標籤')
        axes[1].axis('off')
        
        # 疊加圖像
        overlap = self._create_overlay_image(processed_image, mask, unique_classes, class_names)
        axes[2].imshow(overlap)
        axes[2].set_title('標籤疊加')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def _create_colored_mask(self, mask: np.ndarray, unique_classes: List[int]) -> np.ndarray:
        """創建彩色掩碼"""
        np.random.seed(42)  # 保持顏色一致
        colored_mask = np.zeros((*mask.shape, 3), dtype=np.uint8)
        
        for cls in unique_classes:
            color = np.random.randint(0, 255, 3)
            colored_mask[mask == cls] = color
        
        return colored_mask
    
    def _create_overlay_image(self, 
                            image: np.ndarray,
                            mask: np.ndarray,
                            unique_classes: List[int],
                            class_names: Optional[Dict[int, str]]) -> np.ndarray:
        """創建疊加圖像"""
        overlay = image.copy()
        
        if not unique_classes:
            return overlay
        
        # 創建彩色掩碼
        colored_mask = self._create_colored_mask(mask, unique_classes)
        
        # 疊加
        alpha = 0.5
        overlay = cv2.addWeighted(overlay, 1, colored_mask, alpha, 0)
        
        # 添加圖例
        if unique_classes:
            overlay = self._add_legend(overlay, unique_classes, class_names)
        
        return overlay
    
    def _add_legend(self, 
                   image: np.ndarray,
                   unique_classes: List[int],
                   class_names: Optional[Dict[int, str]]) -> np.ndarray:
        """添加圖例"""
        np.random.seed(42)
        legend_x = image.shape[1] - 150
        legend_y = 20
        legend_height = 25 * len(unique_classes)
        
        # 繪製背景
        cv2.rectangle(image, (legend_x - 10, legend_y - 10),
                     (legend_x + 140, legend_y + legend_height),
                     (255, 255, 255), -1)
        cv2.rectangle(image, (legend_x - 10, legend_y - 10),
                     (legend_x + 140, legend_y + legend_height),
                     (0, 0, 0), 1)
        
        # 繪製圖例項目
        for i, cls in enumerate(sorted(unique_classes)):
            # 顏色方塊
            color = np.random.randint(0, 255, 3).tolist()
            cv2.rectangle(image, (legend_x, legend_y + i * 25),
                         (legend_x + 20, legend_y + i * 25 + 20),
                         color, -1)
            
            # 類別名稱
            if class_names and cls in class_names:
                class_text = class_names[cls]
            else:
                class_text = f"類別 {cls}"
            
            # 使用字體管理器繪製中文
            image = FontManager.draw_chinese_text(
                image, class_text,
                (legend_x + 30, legend_y + i * 25),
                font_size=16
            )
        
        return image


class YOLODataset(BaseVisionDataset):
    """YOLO格式數據集"""
    
    def _find_files(self):
        """查找YOLO格式的圖像和標籤文件"""
        self.image_files, self.label_files = FileFinder.find_image_label_pairs(
            self.config.data_dir,
            self.config.split,
            self.config.image_extensions,
            ['.txt']  # YOLO使用txt格式
        )
    
    def _load_label(self, label_path: str, image_shape: Tuple[int, int]) -> Tuple[np.ndarray, List, List]:
        """載入YOLO格式標籤"""
        img_width, img_height = image_shape
        mask = np.zeros((img_height, img_width), dtype=np.uint8)
        boxes = []
        labels = []
        
        try:
            with open(label_path, 'r') as f:
                lines = f.readlines()
                
            for line in lines:
                parts = line.strip().split()
                if not parts:
                    continue
                    
                class_id = int(parts[0])
                
                # 檢查是否為邊界框格式 (class_id x_center y_center width height)
                if len(parts) == 5:
                    # YOLO 邊界框格式
                    x_center = float(parts[1]) * img_width
                    y_center = float(parts[2]) * img_height
                    width = float(parts[3]) * img_width
                    height = float(parts[4]) * img_height
                    
                    # 轉換為 [x_min, y_min, x_max, y_max] 格式
                    x_min = max(0, x_center - width / 2)
                    y_min = max(0, y_center - height / 2)
                    x_max = min(img_width, x_center + width / 2)
                    y_max = min(img_height, y_center + height / 2)
                    
                    # 添加到列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                    
                    # 在掩碼上繪製框
                    cv2.rectangle(
                        mask,
                        (int(x_min), int(y_min)),
                        (int(x_max), int(y_max)),
                        class_id + 1,  # 加 1，因為 0 通常保留給背景
                        -1  # 填充矩形
                    )
                    
                # 檢查是否為多邊形格式 (class_id x1 y1 x2 y2 ... xn yn)
                elif len(parts) > 5 and len(parts) % 2 == 1:
                    # YOLO 多邊形格式
                    coords = [float(p) for p in parts[1:]]
                    
                    # 確保坐標數量是偶數
                    if len(coords) % 2 != 0:
                        coords = coords[:-1]
                        
                    # 將坐標轉換為多邊形點
                    points = []
                    for i in range(0, len(coords), 2):
                        x = coords[i] * img_width
                        y = coords[i + 1] * img_height
                        points.append([int(x), int(y)])
                        
                    # 在掩碼上繪製多邊形
                    points_array = np.array(points, dtype=np.int32)
                    cv2.fillPoly(mask, [points_array], class_id + 1)
                    
                    # 計算多邊形的邊界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min = max(0, min(x_coords))
                    y_min = max(0, min(y_coords))
                    x_max = min(img_width, max(x_coords))
                    y_max = min(img_height, max(y_coords))
                    
                    # 添加到列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                    
        except Exception as e:
            if self.config.log_enabled:
                logger.error(f"讀取YOLO標籤檔案 {label_path} 錯誤: {e}")
        
        return mask, boxes, labels


class LabelmeDataset(BaseVisionDataset):
    """Labelme格式數據集"""
    
    def __init__(self, config: DatasetConfig, transform: Optional[A.Compose] = None, class_mapping: Optional[Dict[str, int]] = None):
        """
        Args:
            config: 數據集配置
            transform: 數據變換
            class_mapping: 類別名稱到ID的映射
        """
        self.class_mapping = class_mapping
        super().__init__(config, transform)
        
        # 如果沒有提供類別映射，自動創建
        if self.class_mapping is None:
            self.class_mapping = self._create_class_mapping()
    
    def _create_class_mapping(self) -> Dict[str, int]:
        """從所有JSON文件自動創建類別映射"""
        class_names = set()
        
        # 遍歷所有JSON文件收集類別
        for json_path in self.label_files:
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                for shape in data.get('shapes', []):
                    class_names.add(shape.get('label', ''))
            except Exception as e:
                if self.config.log_enabled:
                    logger.error(f"讀取JSON文件 {json_path} 時發生錯誤: {e}")
        
        # 移除空標籤
        if '' in class_names:
            class_names.remove('')
        
        # 按字母順序排序並分配數字ID
        sorted_names = sorted(list(class_names))
        class_mapping = {name: idx for idx, name in enumerate(sorted_names)}
        
        if self.config.log_enabled:
            logger.info(f"自動生成類別映射: {class_mapping}")
        
        return class_mapping
    
    def _find_files(self):
        """查找Labelme格式的圖像和標籤文件"""
        # 先找到所有JSON文件
        split_dir = self._get_split_directory()
        
        if not split_dir.exists():
            logger.warning(f"找不到目錄: {split_dir}")
            return
        
        for json_path in split_dir.glob('*.json'):
            try:
                # 讀取JSON文件以獲取對應的圖像路徑
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 獲取圖像文件路徑
                img_path = data.get('imagePath', '')
                
                # 處理相對路徑或絕對路徑
                if img_path:
                    if os.path.isabs(img_path):
                        # 絕對路徑
                        if os.path.exists(img_path):
                            img_file = img_path
                        else:
                            continue
                    else:
                        # 相對路徑
                        img_file = split_dir / os.path.basename(img_path)
                        if not img_file.exists():
                            # 嘗試在相同目錄下尋找
                            base_name = json_path.stem
                            img_file = None
                            for ext in self.config.image_extensions:
                                test_path = split_dir / f"{base_name}{ext}"
                                if test_path.exists():
                                    img_file = test_path
                                    break
                            if img_file is None:
                                continue
                else:
                    # 如果JSON未指定圖像路徑，嘗試尋找同名圖像文件
                    base_name = json_path.stem
                    img_file = None
                    for ext in self.config.image_extensions:
                        test_path = split_dir / f"{base_name}{ext}"
                        if test_path.exists():
                            img_file = test_path
                            break
                    if img_file is None:
                        continue
                
                self.image_files.append(str(img_file))
                self.label_files.append(str(json_path))
                
            except Exception as e:
                if self.config.log_enabled:
                    logger.error(f"處理JSON文件 {json_path} 時發生錯誤: {e}")
    
    def _get_split_directory(self) -> Path:
        """獲取對應分割的目錄路徑"""
        # 嘗試找到分割對應的目錄
        possible_dirs = [
            self.config.data_dir / self.config.split,
            self.config.data_dir / 'images' / self.config.split,
            self.config.data_dir  # 如果沒有分割子目錄，則使用主目錄
        ]
        
        for dir_path in possible_dirs:
            if dir_path.exists():
                return dir_path
        
        # 如果都找不到，返回主目錄
        return self.config.data_dir
    
    def _load_label(self, label_path: str, image_shape: Tuple[int, int]) -> Tuple[np.ndarray, List, List]:
        """載入Labelme JSON格式標籤（增強版，支援更多形狀）"""
        img_width, img_height = image_shape
        mask = np.zeros((img_height, img_width), dtype=np.uint8)
        polygons = []
        boxes = []
        labels = []
        
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            shapes = data.get('shapes', [])
            for i, shape in enumerate(shapes):
                label = shape.get('label', '')
                shape_type = shape.get('shape_type', '')
                points = shape.get('points', [])
                
                # 跳過無效標籤
                if not label or label not in self.class_mapping:
                    if self.config.log_enabled:
                        logger.warning(f"跳過未知標籤 '{label}' 在檔案 {label_path}")
                    continue
                    
                class_id = self.class_mapping[label]
                
                # 處理矩形
                if shape_type == 'rectangle' and len(points) == 2:
                    # labelme 矩形格式：[[x1, y1], [x2, y2]]
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    
                    # 確保坐標順序正確（左上到右下）
                    xmin = min(x1, x2)
                    ymin = min(y1, y2)
                    xmax = max(x1, x2)
                    ymax = max(y1, y2)
                    
                    # 添加到邊界框列表
                    boxes.append([xmin, ymin, xmax, ymax])
                    labels.append(class_id)
                    
                    # 在掩碼上繪製框
                    cv2.rectangle(
                        mask,
                        (int(xmin), int(ymin)),
                        (int(xmax), int(ymax)),
                        class_id + 1,  # 加 1，因為 0 通常保留給背景
                        -1  # 填充矩形
                    )
                    
                # 處理多邊形
                elif shape_type == 'polygon' and len(points) >= 3:
                    # 將坐標轉換為整數點陣列
                    poly_points = np.array(points, dtype=np.int32)
                    
                    # 在掩碼上繪製多邊形
                    cv2.fillPoly(mask, [poly_points], class_id + 1)
                    
                    # 計算多邊形的邊界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min = max(0, min(x_coords))
                    y_min = max(0, min(y_coords))
                    x_max = min(img_width, max(x_coords))
                    y_max = min(img_height, max(y_coords))
                    
                    # 添加到邊界框列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                
                # 處理圓形
                elif shape_type == 'circle' and len(points) == 2:
                    # labelme 圓形格式：[[center_x, center_y], [radius_point_x, radius_point_y]]
                    center_x, center_y = points[0]
                    radius_point_x, radius_point_y = points[1]
                    radius = int(np.sqrt((radius_point_x - center_x)**2 + (radius_point_y - center_y)**2))
                    
                    # 在掩碼上繪製圓形
                    cv2.circle(mask, (int(center_x), int(center_y)), radius, class_id + 1, -1)
                    
                    # 計算圓形的邊界框
                    x_min = max(0, center_x - radius)
                    y_min = max(0, center_y - radius)
                    x_max = min(img_width, center_x + radius)
                    y_max = min(img_height, center_y + radius)
                    
                    # 添加到邊界框列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                
                # 處理線段
                elif shape_type == 'line' and len(points) >= 2:
                    # labelme 線段格式：[[x1, y1], [x2, y2], ...] 可能有多個點
                    points_array = np.array(points, dtype=np.int32)
                    cv2.polylines(mask, [points_array], False, class_id + 1, 5)
                    
                    # 計算線段的邊界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min = max(0, min(x_coords))
                    y_min = max(0, min(y_coords))
                    x_max = min(img_width, max(x_coords))
                    y_max = min(img_height, max(y_coords))
                    
                    # 添加到邊界框列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                
                # 處理點
                elif shape_type == 'point' and points:
                    # labelme 點格式：[[x, y], ...] 可能有多個點
                    for point in points:
                        cv2.circle(mask, (int(point[0]), int(point[1])), 5, class_id + 1, -1)
                    
                    # 對於點，創建一個小的邊界框
                    if len(points) == 1:
                        x, y = points[0]
                        boxes.append([max(0, x-5), max(0, y-5), min(img_width, x+5), min(img_height, y+5)])
                        labels.append(class_id)
                    else:
                        # 多個點的情況，計算整體邊界框
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        x_min = max(0, min(x_coords) - 5)
                        y_min = max(0, min(y_coords) - 5)
                        x_max = min(img_width, max(x_coords) + 5)
                        y_max = min(img_height, max(y_coords) + 5)
                        
                        boxes.append([x_min, y_min, x_max, y_max])
                        labels.append(class_id)
                        
                else:
                    if self.config.log_enabled:
                        logger.warning(f"不支援的形狀類型 '{shape_type}' 在檔案 {label_path}")
                    
        except Exception as e:
            if self.config.log_enabled:
                logger.error(f"讀取Labelme JSON檔案 {label_path} 錯誤: {e}")
        
        return mask, boxes, labels


# 標註格式轉換工具函數
def convert_labelme_to_yolo(input_dir: str, 
                           output_root: Optional[str] = None, 
                           class_mapping: Optional[Dict[str, int]] = None, 
                           copy_images: bool = False, 
                           log_enabled: bool = True) -> Tuple[int, Dict[str, int]]:
    """
    將labelme JSON格式的標註檔案轉換為YOLO格式
    
    Args:
        input_dir: 輸入目錄，包含labelme json檔案和圖像
        output_root: 輸出根目錄，將在此目錄下建立'labels'和'images'子目錄
        class_mapping: 類別名稱到數字ID的映射
        copy_images: 是否複製圖像檔案到輸出目錄
        log_enabled: 是否啟用日誌輸出
        
    Returns:
        (成功轉換的檔案數量, 類別映射字典)
    """
    if log_enabled:
        logger.info(f"開始轉換 {input_dir} 中的Labelme標註")
    
    if not os.path.exists(input_dir):
        raise FileNotFoundError(f"找不到輸入目錄: {input_dir}")
    
    # 設定輸出目錄
    if output_root is None:
        output_root = input_dir
    
    # 檢查輸入目錄中的子目錄
    subdirs = [d for d in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, d))]
    
    # 檢測輸入目錄結構 - 看是否已經有train/val/test等子目錄
    has_splits = any(d in ['train', 'val', 'test'] for d in subdirs)
    
    if has_splits:
        if log_enabled:
            logger.info(f"檢測到資料集分割: {[d for d in subdirs if d in ['train', 'val', 'test']]}")
        
        # 對每個分割處理
        splits = [d for d in subdirs if d in ['train', 'val', 'test']]
        total_success = 0
        all_class_mapping = None
        
        for split in splits:
            split_input_dir = os.path.join(input_dir, split)
            split_output_dir = os.path.join(output_root, split)
            
            # 確保輸出目錄存在
            os.makedirs(os.path.join(split_output_dir, 'labels'), exist_ok=True)
            if copy_images:
                os.makedirs(os.path.join(split_output_dir, 'images'), exist_ok=True)
            
            # 處理此分割
            success_count, split_class_mapping = _convert_labelme_dir(
                split_input_dir, 
                os.path.join(split_output_dir, 'labels'),
                os.path.join(split_output_dir, 'images') if copy_images else None,
                class_mapping,
                log_enabled
            )
            
            total_success += success_count
            
            # 保存所有類別映射
            if all_class_mapping is None:
                all_class_mapping = split_class_mapping
            elif split_class_mapping:
                # 合併字典
                for k, v in split_class_mapping.items():
                    if k not in all_class_mapping:
                        all_class_mapping[k] = v
        
        # 將類別映射保存為JSON檔案
        mapping_path = os.path.join(output_root, 'class_mapping.json')
        with open(mapping_path, 'w', encoding='utf-8') as f:
            json.dump(all_class_mapping, f, ensure_ascii=False, indent=2)
        
        return total_success, all_class_mapping
    else:
        # 沒有分割，直接處理整個目錄
        labels_dir = os.path.join(output_root, 'labels')
        images_dir = os.path.join(output_root, 'images') if copy_images else None
        
        # 確保輸出目錄存在
        os.makedirs(labels_dir, exist_ok=True)
        if copy_images:
            os.makedirs(images_dir, exist_ok=True)
        
        return _convert_labelme_dir(input_dir, labels_dir, images_dir, class_mapping, log_enabled)


def _convert_labelme_dir(input_dir: str, 
                        labels_dir: str, 
                        images_dir: Optional[str] = None, 
                        class_mapping: Optional[Dict[str, int]] = None, 
                        log_enabled: bool = True) -> Tuple[int, Dict[str, int]]:
    """
    處理單個目錄中的labelme JSON檔案
    
    Args:
        input_dir: 輸入目錄，包含labelme json檔案和圖像
        labels_dir: 輸出標籤目錄
        images_dir: 輸出圖像目錄，若為None則不複製圖像
        class_mapping: 類別名稱到數字ID的映射
        log_enabled: 是否啟用日誌輸出
        
    Returns:
        (成功轉換的檔案數量, 類別映射字典)
    """
    # 如果沒有提供類別映射，自動生成
    if class_mapping is None:
        # 先掃描所有JSON檔案收集類別
        class_names = set()
        for filename in os.listdir(input_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(input_dir, filename), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    for shape in data.get('shapes', []):
                        class_names.add(shape.get('label', ''))
                except Exception as e:
                    if log_enabled:
                        logger.error(f"讀取JSON檔案 {filename} 時發生錯誤: {e}")
        
        # 移除空標籤
        if '' in class_names:
            class_names.remove('')
        
        # 按字母順序排序並分配數字ID
        sorted_names = sorted(list(class_names))
        class_mapping = {name: idx for idx, name in enumerate(sorted_names)}
        
        if log_enabled:
            logger.info(f"自動生成類別映射: {class_mapping}")
    
    # 統計每個類別的數量
    class_counts = defaultdict(int)
    success_count = 0
    
    # 處理每個JSON檔案
    for filename in os.listdir(input_dir):
        if not filename.endswith('.json'):
            continue
        
        try:
            json_path = os.path.join(input_dir, filename)
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            image_width = data.get('imageWidth', 0)
            image_height = data.get('imageHeight', 0)
            
            # 確保有有效的圖像尺寸
            if image_width <= 0 or image_height <= 0:
                # 如果JSON中沒有寬高信息，嘗試從對應的圖像檔案獲取
                img_filename = data.get('imagePath', '').split('/')[-1]
                img_path = os.path.join(input_dir, img_filename)
                
                if os.path.exists(img_path):
                    with Image.open(img_path) as img:
                        image_width, image_height = img.size
                else:
                    if log_enabled:
                        logger.warning(f"無法獲取圖像尺寸: {filename}")
                    continue
            
            # 建立YOLO格式的輸出檔名
            base_name = os.path.splitext(filename)[0]
            output_filename = f"{base_name}.txt"
            output_path = os.path.join(labels_dir, output_filename)
            
            # 收集YOLO格式的標註
            yolo_annotations = []
            for shape in data.get('shapes', []):
                label = shape.get('label', '')
                shape_type = shape.get('shape_type', '')
                points = shape.get('points', [])
                
                # 跳過無效標籤
                if not label or label not in class_mapping:
                    if log_enabled:
                        logger.warning(f"跳過未知標籤 '{label}' 在檔案 {filename}")
                    continue
                
                class_id = class_mapping[label]
                class_counts[label] += 1
                
                # 處理矩形（轉換為YOLO格式的邊界框）
                if shape_type == 'rectangle' and len(points) == 2:
                    # labelme 矩形格式：[[x1, y1], [x2, y2]]
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    
                    # 確保坐標順序正確（左上到右下）
                    xmin = min(x1, x2)
                    ymin = min(y1, y2)
                    xmax = max(x1, x2)
                    ymax = max(y1, y2)
                    
                    # 計算YOLO格式的中心點和寬高
                    x_center = (xmin + xmax) / 2 / image_width
                    y_center = (ymin + ymax) / 2 / image_height
                    width = (xmax - xmin) / image_width
                    height = (ymax - ymin) / image_height
                    
                    # 限制在[0, 1]範圍內
                    x_center = max(0, min(1, x_center))
                    y_center = max(0, min(1, y_center))
                    width = max(0, min(1, width))
                    height = max(0, min(1, height))
                    
                    # 添加YOLO格式的標註
                    yolo_annotations.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
                
                # 處理多邊形
                elif shape_type == 'polygon' and len(points) >= 3:
                    # YOLO格式的多邊形標註：class_id x1 y1 x2 y2 ... xn yn
                    yolo_polygon = [f"{class_id}"]
                    
                    for x, y in points:
                        # 歸一化座標
                        x_norm = max(0, min(1, x / image_width))
                        y_norm = max(0, min(1, y / image_height))
                        yolo_polygon.extend([f"{x_norm:.6f}", f"{y_norm:.6f}"])
                    
                    yolo_annotations.append(" ".join(yolo_polygon))
            
            # 寫入YOLO格式檔案
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))
            
            success_count += 1
            
            # 如果指定了圖像目錄，也可以複製圖像
            if images_dir and data.get('imagePath'):
                img_filename = os.path.basename(data.get('imagePath'))
                img_src = os.path.join(input_dir, img_filename)
                img_dst = os.path.join(images_dir, img_filename)
                
                if os.path.exists(img_src) and not os.path.exists(img_dst):
                    shutil.copy(img_src, img_dst)
        
        except Exception as e:
            if log_enabled:
                logger.error(f"處理檔案 {filename} 時發生錯誤: {e}")
    
    # 輸出統計信息
    if log_enabled:
        logger.info(f"成功轉換 {success_count} 個標註檔案")
        logger.info(f"類別統計: {dict(class_counts)}")
    
    # 將類別映射保存為JSON檔案
    mapping_path = os.path.join(labels_dir, 'class_mapping.json')
    with open(mapping_path, 'w', encoding='utf-8') as f:
        json.dump(class_mapping, f, ensure_ascii=False, indent=2)
    
    return success_count, class_mapping


# 向後兼容的函數
denormalize = ImageProcessor.denormalize


# 使用示例
if __name__ == "__main__":
    # 定義轉換
    transform = A.Compose([
        A.Resize(height=224, width=224),
        A.HorizontalFlip(p=0.5),
        A.RandomBrightnessContrast(p=0.2),
        ToTensorV2(),  # 轉換為PyTorch張量
    ], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))
    
    # 創建數據集配置
    config = DatasetConfig(
        data_dir='./AI_train_dataset',
        size=224,
        split='train',
        log_enabled=True
    )
    
    # 創建YOLO數據集
    yolo_dataset = YOLODataset(config, transform)
    
    # 創建Labelme數據集
    # labelme_dataset = LabelmeDataset(config, transform)
    
    # 視覺化示例
    print(f"YOLO數據集大小: {len(yolo_dataset)}")
    
    # 獲取並視覺化一個數據項
    if len(yolo_dataset) > 0:
        idx = 0
        image, mask, filename = yolo_dataset[idx]
        print(f"圖片類型: {type(image)}")
        if isinstance(image, torch.Tensor):
            print(f"圖片 shape: {image.shape}")
        else:
            print(f"圖片 shape: {image.shape}")
        
        # 視覺化
        yolo_dataset.visualize(idx)