"""
配置系統：管理所有模型變體的配置
支持編碼器、解碼器和完整模型的配置驅動創建
"""

from .encoder_configs import *
from .decoder_configs import *
from .model_configs import *

__all__ = [
    # 編碼器配置
    'get_csp_iformer_config',
    'get_iformer_config', 
    'get_mobilenet_config',
    'get_unet_config',
    
    # 解碼器配置
    'get_fpn_config',
    'get_unet_decoder_config',
    'get_segmentation_head_config',
    
    # 模型配置
    'get_road_damage_model_config',
    'get_segmentation_model_config',
    'get_detection_model_config',
    
    # 工具函數
    'list_all_configs',
    'save_custom_config',
    'load_config_from_file'
]