import os
import json
import shutil
import random
import logging
import copy
from pathlib import Path
from collections import Counter, defaultdict
from tqdm import tqdm

class DatasetDivider:
    """
    數據集分割器
    將數據集分割為訓練、驗證和測試集，支持類別平衡
    """
    
    def __init__(self,
                 input_dir,
                 output_dir,
                 train_ratio=0.7,
                 val_ratio=0.15,
                 test_ratio=0.15,
                 class_limits=None,
                 logger=None):
        """
        初始化分割器
        
        參數:
            input_dir: 輸入目錄路徑
            output_dir: 輸出目錄
            train_ratio: 訓練集比例
            val_ratio: 驗證集比例
            test_ratio: 測試集比例
            class_limits: 類別數量限制 {類別: 最大數量}
            logger: 日誌記錄器，如果為None則創建新的
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.class_limits = class_limits or {}
        self.logger = logger or logging.getLogger(__name__)
        
        # 分割名稱
        self.splits = ["train", "val", "test"]
        
        # 任務類型
        self.tasks = ["object_detect", "segment"]
        
        # 初始化統計和結果存儲
        self.stats = {
            "samples": {
                "total": 0,
                "train": 0,
                "val": 0,
                "test": 0
            },
            "class_distribution": defaultdict(lambda: {"total": 0, "train": 0, "val": 0, "test": 0})
        }
        
        # 創建輸出目錄結構
        self._create_directories()
    
    def run(self):
        # 1. 收集所有樣本
        self.logger.info("收集樣本...")
        samples = self._collect_samples()
        self.logger.info(f"收集到 {len(samples)} 個樣本")
        
        # 2. 根據類別限制篩選樣本
        self.logger.info("篩選樣本...")
        filtered_samples = self._filter_samples(samples)
        self.logger.info(f"篩選後剩餘 {len(filtered_samples)} 個樣本")
        
        # 3. 分割數據集 - 確保樣本一致性
        self.logger.info("分割數據集...")
        splits = self._split_dataset_with_consistency(filtered_samples)
        
        # 4. 處理和複製文件，修改為遍歷每個分割
        self.logger.info("處理和複製檔案...")
        for split in self.splits:
            # 對每個分割處理segment和object_detect兩個任務
            for task in self.tasks:
                self._process_split_for_task(splits[split], split, task)
        
        # 5. 更新總樣本數
        self.stats["samples"]["total"] = self.stats["samples"]["train"] + self.stats["samples"]["val"] + self.stats["samples"]["test"]
        
        # 6. 生成統計報告
        self._generate_statistics()
        
        # 7. 驗證分割後的數據集
        self._verify_dataset()
        
        # 8. 輸出最終統計
        self._print_final_statistics()
        
        return self.stats

    def _collect_samples(self):
        """從輸入目錄收集樣本"""
        all_samples = []
        class_counts = {}
        
        # 掃描所有子資料夾作為類別
        for class_dir in self.input_dir.iterdir():
            if not class_dir.is_dir():
                continue
            
            class_name = class_dir.name
            # 跳過 overlap 資料夾
            if class_name == "overlap":
                self.logger.info(f"跳過 overlap 資料夾")
                continue
                
            self.logger.info(f"從類別 {class_name} 收集樣本")
            
            # 收集該類別的樣本，現在只處理segment
            class_samples = []
            
            # 只收集segment task的樣本，後續會同時生成兩種任務的檔案
            task_samples = self._collect_class_samples(class_dir, class_name, "segment")
            
            # 對於multi_class資料夾，需要特殊處理
            if class_name == "multi_class":
                task_samples = self._collect_multi_class_samples(class_dir, "segment")
            
            class_samples.extend(task_samples)
            
            self.logger.info(f"從類別 {class_name} 收集到 {len(class_samples)} 個樣本")
            class_counts[class_name] = len(class_samples)
            all_samples.extend(class_samples)
        
        # 保存類別計數，以便後續使用
        self.class_counts = class_counts
        
        return all_samples
    
    def _collect_class_samples(self, class_dir, class_name, task):
        """從單個類別目錄收集樣本，確保只計算真實圖片"""
        samples = []
        
        # 跳過 overlap 資料夾
        if class_dir.name == "overlap":
            return samples
        
        # 檢查是否是直接存放圖像和標籤的結構
        img_files = []
        img_file_map = {}
        
        # 收集所有圖片文件並建立映射
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            found_imgs = list(class_dir.glob(f"*{ext}"))
            img_files.extend(found_imgs)
            # 建立圖片stem到圖片路徑的映射
            for img in found_imgs:
                img_file_map[img.stem] = img
        
        json_files = list(class_dir.glob("*.json"))
        
        if img_files and json_files:
            # 直接處理當前目錄中的文件
            for json_file in json_files:
                stem = json_file.stem
                
                # 使用映射直接查找匹配的圖像文件
                img_file = img_file_map.get(stem)
                
                # 如果沒有找到對應的圖片，跳過此JSON文件
                if not img_file:
                    continue
                
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        annotation_data = json.load(f)
                    
                    # 檢查標註
                    shapes_key = "shapes" if "shapes" in annotation_data else "annotations"
                    if shapes_key not in annotation_data or not annotation_data[shapes_key]:
                        continue
                    
                    # 計算標籤分佈
                    distribution = Counter()
                    for shape in annotation_data[shapes_key]:
                        if shapes_key == "shapes":
                            label = shape.get("label", "unknown")
                        else:
                            label = shape.get("label", "unknown")
                        
                        distribution[label] += 1
                    
                    if not distribution:
                        continue
                    
                    sample = {
                        "stem": stem,
                        "task": task,
                        "class": class_name,
                        "distribution": distribution,
                        "annotation_file": json_file,
                        "img_file": img_file,
                        "shapes_key": shapes_key
                    }
                    
                    samples.append(sample)
                except Exception as e:
                    self.logger.warning(f"讀取 {json_file} 失敗: {e}")
        else:
            # 檢查傳統目錄結構
            label_dir = class_dir / f"{task}_label" / "labelme_format"
            
            if not label_dir.exists():
                return samples
            
            # 圖像目錄
            img_dir = class_dir / "original"
            
            # 預先建立圖片映射，提高查找效率
            img_file_map = {}
            if img_dir.exists():
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    found_imgs = list(img_dir.glob(f"*{ext}"))
                    for img in found_imgs:
                        img_file_map[img.stem] = img
            
            for annotation_file in label_dir.glob("*.json"):
                stem = annotation_file.stem
                
                # 使用映射直接查找對應的圖片
                img_file = img_file_map.get(stem)
                
                # 如果映射中沒有找到，嘗試傳統方法查找
                if not img_file:
                    img_file = self._find_image_file(img_dir, stem)
                
                # 如果仍未找到圖片，跳過此標註
                if not img_file:
                    continue
                
                try:
                    with open(annotation_file, 'r', encoding='utf-8') as f:
                        annotation_data = json.load(f)
                    
                    # 檢查標註
                    shapes_key = "shapes" if "shapes" in annotation_data else "annotations"
                    if shapes_key not in annotation_data or not annotation_data[shapes_key]:
                        continue
                    
                    # 計算標籤分佈
                    distribution = Counter()
                    for shape in annotation_data[shapes_key]:
                        if shapes_key == "shapes":
                            label = shape.get("label", "unknown")
                        else:
                            label = shape.get("label", "unknown")
                        
                        distribution[label] += 1
                    
                    if not distribution:
                        continue
                    
                    sample = {
                        "stem": stem,
                        "task": task,
                        "class": class_name,
                        "distribution": distribution,
                        "annotation_file": annotation_file,
                        "img_file": img_file,
                        "shapes_key": shapes_key
                    }
                    
                    samples.append(sample)
                except Exception as e:
                    self.logger.warning(f"讀取 {annotation_file} 失敗: {e}")
        
        return samples

    def _find_image_file(self, img_dir, stem):
        """查找對應的圖像文件，支援中文路徑"""
        try:
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                img_file = img_dir / f"{stem}{ext}"
                if img_file.exists():
                    return img_file
        except UnicodeEncodeError:
            # 處理中文路徑問題
            img_dir_str = str(img_dir)
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                img_path = os.path.join(img_dir_str, f"{stem}{ext}")
                if os.path.exists(img_path):
                    return Path(img_path)
        return None
    
    def _filter_samples(self, samples):
        """根據類別限制篩選樣本"""
        # 如果沒有類別限制，直接返回
        if not self.class_limits:
            return samples
        
        # 統計每個類別的樣本數
        class_counts = defaultdict(int)
        for sample in samples:
            class_counts[sample["class"]] += 1
        
        # 顯示原始類別統計
        self.logger.info("原始類別統計:")
        for label, count in sorted(class_counts.items(), key=lambda x: -x[1]):
            self.logger.info(f"  {label}: {count}")
        
        # 將樣本分為多類別和單類別
        multi_class_samples = [s for s in samples if s["class"] == "multi_class"]
        single_class_samples = [s for s in samples if s["class"] != "multi_class"]
        
        # 初始化結果和類別計數
        filtered_samples = []
        current_counts = defaultdict(int)
        
        # 先處理多類別樣本
        self.logger.info(f"優先處理 multi_class 資料夾中的 {len(multi_class_samples)} 個樣本")
        for sample in multi_class_samples:
            # 判斷這個樣本是否對任何未達到限制的類別有貢獻
            is_useful = False
            for label in sample["distribution"]:
                class_name = sample["class"]  # 這裡使用樣本的類別名稱
                max_count = self.class_limits.get(class_name, float('inf'))
                if current_counts[class_name] < max_count:
                    is_useful = True
                    break
            
            if is_useful:
                filtered_samples.append(sample)
                # 更新計數
                class_name = sample["class"]
                current_counts[class_name] += 1
                
        # 顯示多類別處理後的統計
        self.logger.info(f"處理完 multi_class 後選取了 {len(filtered_samples)} 個樣本")
        for label, count in sorted(current_counts.items(), key=lambda x: -x[1]):
            max_count = self.class_limits.get(label, float('inf'))
            self.logger.info(f"  {label}: {count}/{max_count}")
        
        # 按類別分組單類別樣本
        samples_by_class = defaultdict(list)
        for sample in single_class_samples:
            samples_by_class[sample["class"]].append(sample)
        
        # 從單類別樣本中選取
        for class_name, class_samples in samples_by_class.items():
            # 跳過 multi_class
            if class_name == "multi_class":
                continue
                
            max_count = self.class_limits.get(class_name, float('inf'))
            remaining = max_count - current_counts[class_name]
            
            if remaining <= 0:
                self.logger.info(f"類別 {class_name} 已達到限制 {max_count}，不再選取")
                continue
            
            # 確保 remaining 是整數，若是 float('inf')，則使用所有樣本
            if remaining == float('inf'):
                remaining = len(class_samples)
            else:
                remaining = int(remaining)  
            
            # 隨機打亂樣本
            random.shuffle(class_samples)
            
            # 選取所需數量的樣本
            selected = class_samples[:remaining]
            filtered_samples.extend(selected)
            
            self.logger.info(f"類別 {class_name}: 追加選取 {len(selected)} 個樣本 (總計: {current_counts[class_name] + len(selected)})")
        
        return filtered_samples
    
    def _split_dataset(self, samples):
        """
        將樣本分割為訓練、驗證和測試集
        """
        # 隨機打亂
        random.shuffle(samples)
        
        # 分割樣本
        n_samples = len(samples)
        n_train = int(n_samples * self.train_ratio)
        n_val = int(n_samples * self.val_ratio)
        n_test = n_samples - n_train - n_val
        
        splits = {
            "train": samples[:n_train],
            "val": samples[n_train:n_train+n_val],
            "test": samples[n_train+n_val:]
        }
        
        # 更新統計
        for split, split_samples in splits.items():
            self.stats["samples"][split] = len(split_samples)
        
        self.logger.info(f"分割結果: 訓練集 {len(splits['train'])}，"
                        f"驗證集 {len(splits['val'])}，"
                        f"測試集 {len(splits['test'])}")
        
        return splits
    
    def _process_split_for_task(self, samples, split_name, task):
        """處理特定分割的特定任務的樣本"""
        self.logger.info(f"處理 {split_name} 分割的 {task} 任務")
        
        output_dir = self.output_dir / task / split_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 對分割中每個樣本進行處理
        for sample in tqdm(samples, desc=f"處理 {task} {split_name}"):
            stem = sample["stem"]
            img_file = sample["img_file"]
            annotation_file = sample["annotation_file"]
            class_name = sample["class"]
            
            # 複製圖像，處理中文路徑
            try:
                dst_img = output_dir / img_file.name
                shutil.copy2(img_file, dst_img)
            except UnicodeEncodeError:
                # 處理中文路徑問題
                src_path = str(img_file)
                dst_path = os.path.join(str(output_dir), img_file.name)
                shutil.copy2(src_path, dst_path)
            
            # 讀取原始標註
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    annotation_data = json.load(f)
                
                # 根據任務類型處理標註
                if task == "object_detect":
                    # 將多邊形轉換為矩形框
                    shapes_key = sample["shapes_key"]
                    converted_data = self._convert_to_boxes(annotation_data, shapes_key)
                    
                    # 保存修改後的標註文件，處理中文路徑
                    try:
                        dst_annotation = output_dir / f"{stem}.json"
                        with open(dst_annotation, 'w', encoding='utf-8') as f:
                            json.dump(converted_data, f, indent=2, ensure_ascii=False)
                    except UnicodeEncodeError:
                        # 處理中文路徑問題
                        dst_path = os.path.join(str(output_dir), f"{stem}.json")
                        with open(dst_path, 'w', encoding='utf-8') as f:
                            json.dump(converted_data, f, indent=2, ensure_ascii=False)
                else:
                    # segment任務保持原始標註
                    dst_annotation = output_dir / f"{stem}.json"
                    shutil.copy2(annotation_file, dst_annotation)
                
                # 更新類別分佈統計
                task_class_key = f"{task}_{class_name}"
                self.stats["class_distribution"][task_class_key][split_name] += 1
                self.stats["class_distribution"][task_class_key]["total"] += 1
                
            except Exception as e:
                self.logger.error(f"處理 {annotation_file} 失敗: {e}")
                # 刪除已複製的圖像
                if dst_img.exists():
                    dst_img.unlink()
    
    def _convert_to_boxes(self, annotation_data, shapes_key):
        """
        將多邊形標註轉換為矩形框
        
        參數:
            annotation_data: 原始標註數據
            shapes_key: 標註中形狀的鍵名（shapes 或 annotations）
            
        返回:
            轉換後的標註數據
        """
        # 創建一個深拷貝以避免修改原始數據
        converted_data = copy.deepcopy(annotation_data)
        
        # 處理每個形狀
        for shape in converted_data.get(shapes_key, []):
            shape_type = shape.get("shape_type", "polygon")
            points = shape.get("points", [])
            
            # 只處理多邊形
            if shape_type == "polygon" and len(points) >= 3:
                # 計算邊界框
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]
                xmin, xmax = min(xs), max(xs)
                ymin, ymax = min(ys), max(ys)
                
                # 設置為矩形
                shape["shape_type"] = "rectangle"
                shape["points"] = [[xmin, ymin], [xmax, ymax]]
            
        return converted_data
    
    def _split_dataset_with_consistency(self, samples):
        """
        將樣本分割為訓練、驗證和測試集，確保樣本一致性
        """
        # 按照圖像名稱(stem)分組
        samples_by_stem = defaultdict(list)
        for sample in samples:
            samples_by_stem[sample["stem"]].append(sample)
        
        # 獲取所有樣本的 stem
        all_stems = list(samples_by_stem.keys())
        
        self.logger.info(f"保留 {len(all_stems)} 個樣本")
        
        # 隨機排序 stem
        random.shuffle(all_stems)
        
        # 根據比例計算每個分割的樣本數量
        n_stems = len(all_stems)
        n_train = int(n_stems * self.train_ratio)
        n_val = int(n_stems * self.val_ratio)
        n_test = n_stems - n_train - n_val
        
        # 分割樣本
        train_stems = all_stems[:n_train]
        val_stems = all_stems[n_train:n_train+n_val]
        test_stems = all_stems[n_train+n_val:]
        
        # 創建分割
        splits = {
            "train": [],
            "val": [],
            "test": []
        }
        
        # 將樣本添加到相應的分割
        for stem in train_stems:
            splits["train"].extend(samples_by_stem[stem])
        
        for stem in val_stems:
            splits["val"].extend(samples_by_stem[stem])
            
        for stem in test_stems:
            splits["test"].extend(samples_by_stem[stem])
        
        # 更新統計
        self.stats["samples"]["train"] = len(train_stems)
        self.stats["samples"]["val"] = len(val_stems)
        self.stats["samples"]["test"] = len(test_stems)
        
        self.logger.info(f"分割結果: 訓練集 {len(train_stems)} 圖像，"
                        f"驗證集 {len(val_stems)} 圖像，"
                        f"測試集 {len(test_stems)} 圖像")
        
        return splits
        
    def _generate_statistics(self):
        """生成分割統計報告"""
        # 保存JSON格式統計
        stats_file = self.output_dir / "split_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            # 轉換defaultdict為普通字典
            stats_dict = {
                "samples": dict(self.stats["samples"]),
                "class_distribution": {k: dict(v) for k, v in self.stats["class_distribution"].items()},
                "settings": {
                    "train_ratio": self.train_ratio,
                    "val_ratio": self.val_ratio,
                    "test_ratio": self.test_ratio,
                    "class_limits": self.class_limits
                }
            }
            json.dump(stats_dict, f, indent=2, ensure_ascii=False)
        
        # 生成人類可讀的報告
        report_file = self.output_dir / "split_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("===== 數據集分割報告 =====\n\n")
            
            f.write("樣本統計:\n")
            for k, v in self.stats["samples"].items():
                f.write(f"  {k}: {v}\n")
            
            f.write("\n類別分佈:\n")
            for task in self.tasks:
                f.write(f"\n  {task} 任務:\n")
                
                task_stats = {k: v for k, v in self.stats["class_distribution"].items() if k.startswith(f"{task}_")}
                
                for key, counts in sorted(task_stats.items(), key=lambda x: x[0]):
                    # 提取類別名稱
                    class_name = key.split("_", 1)[1]
                    
                    f.write(f"    {class_name}: 總計={counts['total']} "
                           f"(訓練={counts['train']}, 驗證={counts['val']}, 測試={counts['test']})\n")
        
        self.logger.info(f"統計報告已保存至 {report_file}")
    
    def _verify_dataset(self):
        """驗證分割後的數據集完整性"""
        self.logger.info("驗證數據集完整性...")
        
        for task in self.tasks:
            for split in self.splits:
                split_dir = self.output_dir / task / split
                
                # 圖像和標籤文件在同一目錄
                image_files = set()
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    image_files.update({f.stem for f in split_dir.glob(f"*{ext}") if f.is_file()})
                
                label_files = {f.stem for f in split_dir.glob("*.json") if f.is_file()}
                
                missing_labels = image_files - label_files
                extra_labels = label_files - image_files
                
                if missing_labels:
                    self.logger.warning(f"{task} {split}: {len(missing_labels)} 個圖像缺少標籤")
                
                if extra_labels:
                    self.logger.warning(f"{task} {split}: {len(extra_labels)} 個標籤缺少圖像")
                
                self.logger.info(f"{task} {split}: {len(image_files)} 圖像, {len(label_files)} 標籤")

    def _create_directories(self):
        """創建必要的輸出目錄結構"""
        # 創建主輸出目錄
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            
            # 為每個任務創建目錄
            for task in self.tasks:
                for split in self.splits:
                    # 每個任務的分割目錄
                    task_split_dir = self.output_dir / task / split
                    task_split_dir.mkdir(parents=True, exist_ok=True)
        except UnicodeEncodeError:
            # 處理中文路徑問題
            output_dir_str = str(self.output_dir)
            os.makedirs(output_dir_str, exist_ok=True)
            
            for task in self.tasks:
                for split in self.splits:
                    task_split_path = os.path.join(output_dir_str, task, split)
                    os.makedirs(task_split_path, exist_ok=True)

    def _collect_multi_class_samples(self, multi_class_dir, task):
        """收集多類別資料夾中的樣本，識別每個圖像中包含的類別"""
        samples = []
        
        # 檢查是否是直接存放圖像和標籤的結構
        img_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            img_files.extend(list(multi_class_dir.glob(f"*{ext}")))
        
        json_files = list(multi_class_dir.glob("*.json"))
        
        if img_files and json_files:
            # 直接處理當前目錄中的文件
            for json_file in json_files:
                stem = json_file.stem
                img_file = None
                
                # 查找匹配的圖像文件
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    potential_img = multi_class_dir / f"{stem}{ext}"
                    if potential_img.exists():
                        img_file = potential_img
                        break
                
                if not img_file:
                    continue
                
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        annotation_data = json.load(f)
                    
                    # 檢查標註
                    shapes_key = "shapes" if "shapes" in annotation_data else "annotations"
                    if shapes_key not in annotation_data or not annotation_data[shapes_key]:
                        continue
                    
                    # 計算每個類別的分佈
                    distribution = Counter()
                    for shape in annotation_data[shapes_key]:
                        if shapes_key == "shapes":
                            label = shape.get("label", "unknown")
                        else:
                            label = shape.get("label", "unknown")
                        
                        # 從標籤中提取實際類別名稱
                        class_name = self._extract_class_from_label(label)
                        distribution[class_name] += 1
                    
                    if not distribution:
                        continue
                    
                    # 創建樣本，添加多類別標記
                    sample = {
                        "stem": stem,
                        "task": task,
                        "class": "multi_class",  # 標記為多類別
                        "distribution": distribution,  # 包含所有類別的分佈
                        "annotation_file": json_file,
                        "img_file": img_file,
                        "shapes_key": shapes_key
                    }
                    
                    samples.append(sample)
                except Exception as e:
                    self.logger.warning(f"讀取 {json_file} 失敗: {e}")
        else:
            # 檢查傳統目錄結構
            label_dir = multi_class_dir / f"{task}_label" / "labelme_format"
            
            if not label_dir.exists():
                return samples
            
            for annotation_file in label_dir.glob("*.json"):
                img_dir = multi_class_dir / "original"
                img_file = self._find_image_file(img_dir, annotation_file.stem)
                
                if not img_file:
                    continue
                
                try:
                    with open(annotation_file, 'r', encoding='utf-8') as f:
                        annotation_data = json.load(f)
                    
                    # 檢查標註
                    shapes_key = "shapes" if "shapes" in annotation_data else "annotations"
                    if shapes_key not in annotation_data or not annotation_data[shapes_key]:
                        continue
                    
                    # 計算每個類別的分佈
                    distribution = Counter()
                    for shape in annotation_data[shapes_key]:
                        if shapes_key == "shapes":
                            label = shape.get("label", "unknown")
                        else:
                            label = shape.get("label", "unknown")
                        
                        # 從標籤中提取實際類別名稱
                        class_name = self._extract_class_from_label(label)
                        distribution[class_name] += 1
                    
                    if not distribution:
                        continue
                    
                    sample = {
                        "stem": annotation_file.stem,
                        "task": task,
                        "class": "multi_class",  # 標記為多類別
                        "distribution": distribution,  # 包含所有類別的分佈
                        "annotation_file": annotation_file,
                        "img_file": img_file,
                        "shapes_key": shapes_key
                    }
                    
                    samples.append(sample)
                except Exception as e:
                    self.logger.warning(f"讀取 {annotation_file} 失敗: {e}")
        
        return samples
        
    def _extract_class_from_label(self, label):
        """從標籤中提取實際的類別名稱"""
        # 嘗試從標籤中提取類別名稱
        # 標籤可能像 "expansion_joint_伸縮縫" 或 "expansion_joint" 或其他格式
        
        # 先檢查是否有底線分隔
        if "_" in label:
            parts = label.split("_")
            # 如果有至少兩部分，取第一部分作為英文類別名
            if len(parts) >= 2:
                return parts[0]
        
        # 如果無法解析，返回原始標籤
        return label

    def _print_final_statistics(self):
        """輸出最終統計信息"""
        self.logger.info("\n========= 最終統計 =========")
        
        # 輸出各分割集的圖像數量
        self.logger.info(f"訓練集: {self.stats['samples']['train']} 圖像")
        self.logger.info(f"驗證集: {self.stats['samples']['val']} 圖像")
        self.logger.info(f"測試集: {self.stats['samples']['test']} 圖像")
        self.logger.info(f"總計: {self.stats['samples']['total']} 圖像")
        
        # 輸出各類別統計
        self.logger.info("\n各類別分佈:")
        # 按任務分組
        for task in self.tasks:
            self.logger.info(f"\n{task} 任務:")
            
            # 找出屬於這個任務的類別
            task_classes = {}
            for key, counts in self.stats["class_distribution"].items():
                if key.startswith(f"{task}_"):
                    class_name = key[len(task)+1:]  # 移除 'task_' 前綴
                    task_classes[class_name] = counts
            
            # 輸出每個類別的統計
            for class_name, counts in sorted(task_classes.items()):
                self.logger.info(f"  {class_name}: 總計={counts['total']} "
                            f"(訓練={counts['train']}, 驗證={counts['val']}, 測試={counts['test']})")
        
        print("\n========= 最終統計 =========")
        print(f"訓練集: {self.stats['samples']['train']} 圖像")
        print(f"驗證集: {self.stats['samples']['val']} 圖像")
        print(f"測試集: {self.stats['samples']['test']} 圖像")
        print(f"總計: {self.stats['samples']['total']} 圖像")


# 如果作為獨立腳本運行
if __name__ == "__main__":
    import argparse
    
    # 設置命令行參數
    parser = argparse.ArgumentParser(description="數據集分割工具")
    parser.add_argument("--input", required=True, help="輸入目錄")
    parser.add_argument("--output", required=True, help="輸出目錄")
    parser.add_argument("--interactive", action="store_true", help="互動式設定類別限制和分割比例")
    
    args = parser.parse_args()
    
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("dataset_splitter.log", mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("DatasetDivider")
    
    # 創建分割器實例以獲取類別計數
    temp_splitter = DatasetDivider(
        input_dir=args.input,
        output_dir=args.output,
        logger=logger
    )
    
    # 收集樣本以獲取類別數量
    temp_splitter._collect_samples()
    class_counts = temp_splitter.class_counts
    
    # 獲取類別列表，排除multi_class
    class_names = [name for name in class_counts.keys() if name != "multi_class"]
    
    logger.info(f"發現以下類別: {', '.join(class_names)}")
    if "multi_class" in class_counts:
        logger.info(f"發現多類別資料夾，包含 {class_counts['multi_class']} 個樣本")
    
    # 互動式設定
    train_ratio = 0.7
    val_ratio = 0.15
    test_ratio = 0.15
    class_limits = {}
    
    if args.interactive:
        print("\n===== 互動式設定 =====")
        
        # 輸入分割比例
        while True:
            try:
                print("\n請輸入資料集分割比例 (三個數字總和須為 1.0)")
                train_ratio = float(input("訓練集比例 [0.7]: ") or "0.7")
                val_ratio = float(input("驗證集比例 [0.15]: ") or "0.15")
                test_ratio = float(input("測試集比例 [0.15]: ") or "0.15")
                
                total = train_ratio + val_ratio + test_ratio
                if abs(total - 1.0) <= 0.001:
                    break
                else:
                    print(f"錯誤: 比例總和為 {total}，須為 1.0")
            except ValueError:
                print("請輸入有效的數字")
        
    # 輸入類別限制，並顯示類別總數 (不包括multi_class)
    print("\n請為每個類別設定數量限制 (enter鍵表示不限制)")
    print("注意: 優先從多類別樣本中提取，然後才使用單類別樣本")

    if "multi_class" in class_counts:
        print(f"多類別樣本數: {class_counts['multi_class']} (不需要設定限制，會自動優先使用)")

    for class_name in class_names:
        try:
            total_count = class_counts[class_name]
            # 計算這個類別中的標籤數量，而非樣本數量
            try:
                class_labels_count = sum(sample["distribution"].total() for sample in temp_splitter._collect_class_samples(Path(args.input) / class_name, class_name, "object_detect"))
            except:
                class_labels_count = "未知"
                
            limit = input(f"類別 '{class_name}' 的最大樣本數 [enter=不限制] (總共有: {int(total_count/2)}張圖片, {class_labels_count}個類別物件): ")
            
            # 當用戶按下 Enter 鍵時，不設置限制（不添加到 class_limits 字典中）
            if limit.strip():  # 確保只有當輸入不為空時才設置限制
                class_limits[class_name] = int(limit)
        except ValueError:
            print(f"為類別 '{class_name}' 使用預設值 (不限制)")
    
    # 創建分割器
    splitter = DatasetDivider(
        input_dir=args.input,
        output_dir=args.output,
        train_ratio=train_ratio,
        val_ratio=val_ratio,
        test_ratio=test_ratio,
        class_limits=class_limits,
        logger=logger
    )
    
    # 執行分割
    stats = splitter.run()
    
    print(f"分割完成！詳見報告: {os.path.join(args.output, 'split_report.txt')}")