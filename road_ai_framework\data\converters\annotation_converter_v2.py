#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重構後的標註轉換器

實現策略模式的標註格式轉換器，職責分離，提升可維護性
"""

import os
import json
import time
from pathlib import Path
from typing import Dict, List, Union, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from shared.base_tool import BaseTool
from shared.exceptions import ConversionError, ValidationError, FormatDetectionError
from shared.config_manager import ConfigManager
from shared.file_utils import FileUtils

from .format_detector import FormatDetector
from .conversion_strategy import (
    ConversionStrategy, 
    LabelMeToYOLOConverter,
    VOCToLabelMeConverter, 
    YOLOToLabelMeConverter,
    COCOToLabelMeConverter
)
from .image_processor import ImageProcessor


class AnnotationConverterV2(BaseTool):
    """
    重構後的標註轉換器
    
    特點：
    - 繼承BaseTool統一接口
    - 策略模式支援多種格式轉換
    - 職責分離：格式檢測、轉換、圖像處理分離
    - 配置驅動的參數管理
    - 完整的錯誤處理和日誌記錄
    """
    
    def __init__(self,
                 input_dir: Optional[str] = None,
                 output_dir: Optional[str] = None,
                 input_format: str = 'auto',
                 output_format: str = 'labelme',
                 config: Optional[Dict[str, Any]] = None,
                 **kwargs):
        """
        初始化標註轉換器
        
        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            input_format: 輸入格式 ('auto', 'labelme', 'voc', 'yolo', 'coco')
            output_format: 輸出格式 ('labelme', 'yolo')
            config: 配置字典
            **kwargs: 其他參數
        """
        super().__init__(input_dir, output_dir, config=config, **kwargs)
        
        self.input_format = input_format
        self.output_format = output_format
        
        # 載入配置
        self._load_config()
        
        # 初始化組件
        self.format_detector = FormatDetector(self.logger)
        self.image_processor = ImageProcessor(self.logger)
        
        # 轉換策略
        self.converter_strategy = None
        
        # 支援的格式
        self.supported_input_formats = ['labelme', 'voc', 'yolo', 'coco']
        self.supported_output_formats = ['labelme', 'yolo']
        
        # 處理狀態
        self.checkpoint_file = None
        self.stats_lock = threading.Lock()
        
        # 額外的轉換統計
        self.conversion_stats = {
            'format_detection_time': 0.0,
            'conversion_time': 0.0,
            'image_processing_time': 0.0,
            'formats_found': {}
        }
    
    def validate_inputs(self) -> bool:
        """
        驗證輸入參數
        
        Returns:
            bool: 驗證是否通過
        """
        try:
            # 檢查輸入目錄
            if not self.input_dir or not self.input_dir.exists():
                self.logger.error(f"輸入目錄不存在: {self.input_dir}")
                return False
            
            # 檢查輸出目錄（自動創建）
            if self.output_dir:
                self.output_dir.mkdir(parents=True, exist_ok=True)
            else:
                self.logger.error("未指定輸出目錄")
                return False
            
            # 檢查格式支援
            if self.input_format != 'auto' and self.input_format not in self.supported_input_formats:
                self.logger.error(f"不支援的輸入格式: {self.input_format}")
                return False
            
            if self.output_format not in self.supported_output_formats:
                self.logger.error(f"不支援的輸出格式: {self.output_format}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"輸入驗證失敗: {e}")
            return False
    
    def _execute_main_logic(self, **kwargs) -> Dict[str, Any]:
        """
        執行主要轉換邏輯
        
        Args:
            **kwargs: 額外參數
            
        Returns:
            Dict[str, Any]: 轉換結果
        """
        try:
            # 處理參數
            resize = kwargs.get('resize', self.config.get('resize', None))
            quality = kwargs.get('quality', self.config.get('quality', 75))
            batch_size = kwargs.get('batch_size', self.config.get('batch_size', 10))
            resume = kwargs.get('resume', self.config.get('resume', True))
            
            # 設置檢查點
            self.checkpoint_file = self.output_dir / ".converter_checkpoint.json"
            checkpoint = self._load_checkpoint() if resume else None
            
            # 格式檢測
            start_time = time.time()
            if self.input_format == 'auto':
                self.input_format = self._detect_input_format()
            self.conversion_stats['format_detection_time'] = time.time() - start_time
            
            # 創建轉換策略
            self.converter_strategy = self._create_converter_strategy()
            
            # 查找輸入文件
            input_files = self._find_input_files()
            self.stats['total_files'] = len(input_files)
            
            if not input_files:
                self.logger.warning("未找到需要轉換的文件")
                return self.stats
            
            # 執行轉換
            conversion_start = time.time()
            if self.config.get('enable_parallel', True):
                self._convert_files_parallel(input_files, resize, quality, checkpoint, batch_size)
            else:
                self._convert_files_sequential(input_files, resize, quality, checkpoint)
            self.conversion_stats['conversion_time'] = time.time() - conversion_start
            
            # 後處理
            self._post_process()
            
            # 清理檢查點
            if self.checkpoint_file and self.checkpoint_file.exists():
                self.checkpoint_file.unlink()
            
            # 合併統計信息
            result = self.stats.copy()
            result.update(self.conversion_stats)
            
            return result
            
        except Exception as e:
            self.logger.error(f"轉換執行失敗: {e}")
            raise ConversionError(f"轉換執行失敗: {e}")
    
    def _load_config(self):
        """載入配置"""
        default_config = {
            'resize': None,
            'quality': 75,
            'batch_size': 10,
            'resume': True,
            'enable_parallel': True,
            'max_workers': min(32, os.cpu_count() * 2),
            'enable_image_processing': True,
            'class_mapping': {},
            'target_shape': 'polygon'
        }
        
        # 合併配置
        if self.config:
            default_config.update(self.config)
        self.config = default_config
    
    def _detect_input_format(self) -> str:
        """
        檢測輸入格式
        
        Returns:
            str: 檢測到的格式
        """
        try:
            detected_format = self.format_detector.detect_format(self.input_dir)
            self.logger.info(f"檢測到輸入格式: {detected_format}")
            return detected_format
            
        except FormatDetectionError as e:
            raise ConversionError(f"格式檢測失敗: {e}")
    
    def _create_converter_strategy(self) -> ConversionStrategy:
        """
        創建轉換策略
        
        Returns:
            ConversionStrategy: 轉換策略實例
        """
        strategy_map = {
            ('labelme', 'yolo'): lambda: LabelMeToYOLOConverter(
                class_mapping=self.config.get('class_mapping', {}),
                logger=self.logger
            ),
            ('voc', 'labelme'): lambda: VOCToLabelMeConverter(logger=self.logger),
            ('yolo', 'labelme'): lambda: YOLOToLabelMeConverter(
                classes=self._load_yolo_classes(),
                logger=self.logger
            ),
            ('coco', 'labelme'): lambda: COCOToLabelMeConverter(logger=self.logger),
        }
        
        key = (self.input_format, self.output_format)
        if key in strategy_map:
            return strategy_map[key]()
        else:
            raise ConversionError(f"不支援的轉換: {self.input_format} -> {self.output_format}")
    
    def _find_input_files(self) -> List[Path]:
        """
        查找輸入文件
        
        Returns:
            List[Path]: 輸入文件列表
        """
        extension_map = {
            'labelme': '*.json',
            'voc': '*.xml',
            'yolo': '*.txt',
            'coco': '*.json'
        }
        
        pattern = extension_map.get(self.input_format, '*')
        files = list(self.input_dir.glob(pattern))
        
        # 過濾有效文件
        valid_files = []
        for file_path in files:
            if self.converter_strategy.validate_input(file_path):
                valid_files.append(file_path)
            else:
                self.logger.debug(f"跳過無效文件: {file_path}")
        
        return valid_files
    
    def _convert_files_parallel(self,
                               input_files: List[Path],
                               resize: Any,
                               quality: int,
                               checkpoint: Optional[Dict],
                               batch_size: int):
        """
        並行轉換文件
        
        Args:
            input_files: 輸入文件列表
            resize: 調整大小參數
            quality: 圖像品質
            checkpoint: 檢查點數據
            batch_size: 批次大小
        """
        max_workers = self.config.get('max_workers', min(32, os.cpu_count() * 2))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任務
            future_to_file = {}
            for file_path in input_files:
                if checkpoint and str(file_path) in checkpoint.get('completed', []):
                    self.stats['skipped_files'] += 1
                    continue
                
                future = executor.submit(
                    self._convert_single_file,
                    file_path, resize, quality
                )
                future_to_file[future] = file_path
            
            # 收集結果
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    success = future.result()
                    with self.stats_lock:
                        if success:
                            self.stats['processed_files'] += 1
                        else:
                            self.stats['failed_files'] += 1
                        
                        # 定期保存檢查點
                        if (self.stats['processed_files'] + self.stats['failed_files']) % batch_size == 0:
                            self._save_checkpoint()
                            
                except Exception as e:
                    self.logger.error(f"處理文件失敗: {file_path}, 錯誤: {e}")
                    with self.stats_lock:
                        self.stats['failed_files'] += 1
                
                # 檢查停止信號
                if self.should_stop:
                    self.logger.info("收到停止信號，終止轉換")
                    break
    
    def _convert_files_sequential(self,
                                 input_files: List[Path],
                                 resize: Any,
                                 quality: int,
                                 checkpoint: Optional[Dict]):
        """
        順序轉換文件
        
        Args:
            input_files: 輸入文件列表
            resize: 調整大小參數
            quality: 圖像品質
            checkpoint: 檢查點數據
        """
        for file_path in input_files:
            if self.should_stop:
                self.logger.info("收到停止信號，終止轉換")
                break
            
            if checkpoint and str(file_path) in checkpoint.get('completed', []):
                self.stats['skipped_files'] += 1
                continue
            
            try:
                success = self._convert_single_file(file_path, resize, quality)
                if success:
                    self.stats['processed_files'] += 1
                else:
                    self.stats['failed_files'] += 1
                    
            except Exception as e:
                self.logger.error(f"處理文件失敗: {file_path}, 錯誤: {e}")
                self.stats['failed_files'] += 1
    
    def _convert_single_file(self,
                           file_path: Path,
                           resize: Any,
                           quality: int) -> bool:
        """
        轉換單個文件
        
        Args:
            file_path: 文件路徑
            resize: 調整大小參數
            quality: 圖像品質
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            # 轉換標註
            conversion_success = self.converter_strategy.convert_file(
                file_path, self.output_dir
            )
            
            if not conversion_success:
                return False
            
            # 處理對應的圖像（如果啟用）
            if self.config.get('enable_image_processing', True):
                image_start = time.time()
                image_path = self._find_related_image(file_path)
                
                if image_path and image_path.exists():
                    # 處理圖像
                    if resize or quality != 75:
                        annotation_output = self.output_dir / f"{file_path.stem}.json"
                        if annotation_output.exists():
                            self.image_processor.process_image_with_annotation(
                                image_path, annotation_output, self.output_dir,
                                resize=resize, quality=quality
                            )
                        else:
                            # 只處理圖像
                            output_image_path = self.image_processor._get_output_image_path(
                                image_path, self.output_dir, None
                            )
                            self.image_processor.resize_image(
                                image_path, output_image_path, resize or 1.0, quality
                            )
                    else:
                        # 直接複製圖像
                        self.image_processor.copy_image_simple(image_path, self.output_dir)
                
                self.conversion_stats['image_processing_time'] += time.time() - image_start
            
            return True
            
        except Exception as e:
            self.logger.error(f"轉換文件失敗: {file_path}, 錯誤: {e}")
            return False
    
    def _find_related_image(self, annotation_path: Path) -> Optional[Path]:
        """
        查找相關的圖像文件
        
        Args:
            annotation_path: 標註文件路徑
            
        Returns:
            Optional[Path]: 圖像文件路徑
        """
        return self.image_processor.find_related_image(annotation_path)
    
    def _load_yolo_classes(self) -> List[str]:
        """
        載入YOLO類別
        
        Returns:
            List[str]: 類別列表
        """
        classes_file = self.input_dir / "classes.txt"
        if classes_file.exists():
            try:
                with open(classes_file, 'r', encoding='utf-8') as f:
                    classes = [line.strip() for line in f.readlines() if line.strip()]
                self.logger.info(f"載入了 {len(classes)} 個YOLO類別")
                return classes
            except Exception as e:
                self.logger.warning(f"載入YOLO類別失敗: {e}")
        
        return []
    
    def _post_process(self):
        """後處理"""
        try:
            # 如果輸出格式是YOLO，保存類別映射
            if (self.output_format == 'yolo' and 
                isinstance(self.converter_strategy, LabelMeToYOLOConverter)):
                self.converter_strategy.save_class_mapping(self.output_dir)
            
            # 生成轉換報告
            self._generate_conversion_report()
            
        except Exception as e:
            self.logger.warning(f"後處理失敗: {e}")
    
    def _generate_conversion_report(self):
        """生成轉換報告"""
        try:
            report = {
                'conversion_summary': {
                    'input_format': self.input_format,
                    'output_format': self.output_format,
                    'input_directory': str(self.input_dir),
                    'output_directory': str(self.output_dir),
                    'total_files': self.stats['total_files'],
                    'processed_files': self.stats['processed_files'],
                    'failed_files': self.stats['failed_files'],
                    'skipped_files': self.stats['skipped_files']
                },
                'timing': {
                    'format_detection_time': self.conversion_stats['format_detection_time'],
                    'conversion_time': self.conversion_stats['conversion_time'],
                    'image_processing_time': self.conversion_stats['image_processing_time']
                },
                'config_used': self.config
            }
            
            report_file = self.output_dir / "conversion_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"轉換報告已保存到: {report_file}")
            
        except Exception as e:
            self.logger.warning(f"生成轉換報告失敗: {e}")
    
    def _load_checkpoint(self) -> Optional[Dict]:
        """載入檢查點"""
        try:
            if self.checkpoint_file and self.checkpoint_file.exists():
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint = json.load(f)
                self.logger.info(f"載入檢查點: {len(checkpoint.get('completed', []))} 個已完成文件")
                return checkpoint
        except Exception as e:
            self.logger.warning(f"載入檢查點失敗: {e}")
        
        return None
    
    def _save_checkpoint(self):
        """保存檢查點"""
        try:
            checkpoint = {
                'completed': [],  # 這裡可以添加已完成文件的跟踪
                'stats': self.stats.copy(),
                'timestamp': time.time()
            }
            
            if self.checkpoint_file:
                with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint, f, indent=2)
                    
        except Exception as e:
            self.logger.warning(f"保存檢查點失敗: {e}")
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        獲取詳細的轉換統計信息
        
        Returns:
            Dict[str, Any]: 轉換統計信息
        """
        stats = self.stats.copy()
        stats.update(self.conversion_stats)
        
        # 添加轉換器統計
        if self.converter_strategy:
            stats['converter_stats'] = self.converter_strategy.get_stats()
        
        # 添加圖像處理統計
        stats['image_processor_stats'] = self.image_processor.get_stats()
        
        return stats