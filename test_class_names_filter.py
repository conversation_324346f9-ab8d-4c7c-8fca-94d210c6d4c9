#!/usr/bin/env python3
"""
測試只處理 CLASS_NAMES 中類別的功能
"""

def test_class_names_filter():
    """測試過濾邏輯，只處理 CLASS_NAMES 中定義的類別"""
    
    # 模擬 CLASS_NAMES
    CLASS_NAMES = [
        "expansion_joint_伸縮縫",      # ID: 0
        "joint_路面接縫",               # ID: 1
        "linear_crack_裂縫",        # ID: 2
        "Alligator_crack_龜裂",     # ID: 3
        "potholes_坑洞",            # ID: 4
        "patch_補綻",               # ID: 5
        "manhole_人孔蓋或排水溝",             # ID: 6
        "deformation_變形",         # ID: 7
        "dirt_污垢",                # ID: 8
        "lane_line_linear_白綫裂縫"     # ID: 9
    ]
    
    # 模擬檢測結果（包含在和不在 CLASS_NAMES 中的類別）
    detection_results = [
        {'class_name': 'potholes_坑洞', 'bbox': [10, 10, 50, 50], 'confidence': 0.9, 'class_id': 4},
        {'class_name': 'manhole_人孔蓋或排水溝', 'bbox': [100, 100, 140, 140], 'confidence': 0.8, 'class_id': 6},
        {'class_name': 'unknown_class', 'bbox': [200, 200, 240, 240], 'confidence': 0.7, 'class_id': 99},  # 不在 CLASS_NAMES 中
        {'class_name': 'random_detection', 'bbox': [300, 300, 340, 340], 'confidence': 0.6, 'class_id': 100}  # 不在 CLASS_NAMES 中
    ]
    
    # 模擬GT標註（包含在和不在 CLASS_NAMES 中的類別）
    gt_annotations = [
        {'class_name': 'potholes_坑洞', 'bbox': [12, 12, 52, 52], 'class_id': 4},
        {'class_name': 'linear_crack_裂縫', 'bbox': [150, 150, 190, 190], 'class_id': 2},
        {'class_name': 'unknown_class', 'bbox': [202, 202, 242, 242], 'class_id': 99},  # 不在 CLASS_NAMES 中
        {'class_name': 'another_unknown', 'bbox': [350, 350, 390, 390], 'class_id': 101}  # 不在 CLASS_NAMES 中
    ]
    
    print("=== 測試 CLASS_NAMES 過濾功能 ===")
    print(f"CLASS_NAMES 定義: {len(CLASS_NAMES)} 個類別")
    print(f"原始檢測結果: {len(detection_results)} 個")
    print(f"原始GT標註: {len(gt_annotations)} 個")
    print()
    
    # 1. 測試檢測結果過濾
    print("=== 檢測結果過濾 ===")
    filtered_detections = []
    for det in detection_results:
        if det['class_name'] in CLASS_NAMES:
            filtered_detections.append(det)
            print(f"✅ 保留: {det['class_name']}")
        else:
            print(f"❌ 過濾: {det['class_name']} (不在 CLASS_NAMES 中)")
    
    print(f"過濾後檢測結果: {len(filtered_detections)} 個")
    
    # 2. 測試GT標註過濾
    print(f"\n=== GT標註過濾 ===")
    filtered_gt = []
    for gt in gt_annotations:
        if gt['class_name'] in CLASS_NAMES:
            filtered_gt.append(gt)
            print(f"✅ 保留: {gt['class_name']}")
        else:
            print(f"❌ 過濾: {gt['class_name']} (不在 CLASS_NAMES 中)")
    
    print(f"過濾後GT標註: {len(filtered_gt)} 個")
    
    # 3. 測試 class_details 收集（只收集 CLASS_NAMES 中的類別）
    print(f"\n=== class_details 收集 ===")
    class_details = {}
    for det in detection_results:
        class_name = det['class_name']
        # 只處理 CLASS_NAMES 中定義的類別
        if class_name not in CLASS_NAMES:
            print(f"跳過不在 CLASS_NAMES 中的類別: {class_name}")
            continue
        
        if class_name not in class_details:
            class_details[class_name] = {
                'detection_count': 0,
                'confidences': []
            }
        
        class_details[class_name]['detection_count'] += 1
        class_details[class_name]['confidences'].append(det['confidence'])
        print(f"✅ 收集類別: {class_name}")
    
    print(f"收集到的類別詳情: {len(class_details)} 個類別")
    for class_name, details in class_details.items():
        print(f"  {class_name}: {details['detection_count']} 個檢測")
    
    # 4. 測試 class_metrics 生成（只處理 CLASS_NAMES 中的類別）
    print(f"\n=== class_metrics 生成 ===")
    
    # 模擬已累積的 batch_class_data（可能包含不在 CLASS_NAMES 中的類別）
    batch_class_data = {
        'potholes_坑洞': {'total_tp': 1, 'total_fp': 0, 'total_fn': 0},
        'manhole_人孔蓋或排水溝': {'total_tp': 0, 'total_fp': 1, 'total_fn': 0},
        'linear_crack_裂縫': {'total_tp': 0, 'total_fp': 0, 'total_fn': 1},
        'unknown_class': {'total_tp': 0, 'total_fp': 1, 'total_fn': 1},  # 不在 CLASS_NAMES 中，應被忽略
    }
    
    # 只處理 CLASS_NAMES 中定義的類別
    class_stats = {}
    for class_name in CLASS_NAMES:
        if class_name in batch_class_data:
            data = batch_class_data[class_name]
            class_stats[class_name] = {
                'tp': data['total_tp'],
                'fp': data['total_fp'], 
                'fn': data['total_fn']
            }
            print(f"✅ 包含類別: {class_name} (TP={data['total_tp']}, FP={data['total_fp']}, FN={data['total_fn']})")
        else:
            # CLASS_NAMES中的類別但從未出現，設為零
            class_stats[class_name] = {
                'tp': 0,
                'fp': 0,
                'fn': 0
            }
            print(f"⭕ 零值類別: {class_name}")
    
    # 檢查是否有不在 CLASS_NAMES 中的類別被忽略
    ignored_classes = set(batch_class_data.keys()) - set(CLASS_NAMES)
    if ignored_classes:
        print(f"\n❌ 忽略的類別: {ignored_classes}")
    
    print(f"\n最終 class_metrics 包含: {len(class_stats)} 個類別")
    
    # 5. 驗證結果
    print(f"\n=== 驗證結果 ===")
    
    # 檢查過濾效果
    detection_filter_correct = len(filtered_detections) == 2  # 應該保留 potholes_坑洞 和 manhole_人孔蓋或排水溝
    gt_filter_correct = len(filtered_gt) == 2  # 應該保留 potholes_坑洞 和 linear_crack_裂縫
    class_details_correct = len(class_details) == 2  # 應該收集 potholes_坑洞 和 manhole_人孔蓋或排水溝
    class_metrics_correct = len(class_stats) == len(CLASS_NAMES)  # 應該包含所有 CLASS_NAMES 中的類別
    ignored_correctly = 'unknown_class' not in class_stats  # unknown_class 應該被忽略
    
    results = {
        '檢測結果過濾': detection_filter_correct,
        'GT標註過濾': gt_filter_correct,
        'class_details收集': class_details_correct,
        'class_metrics生成': class_metrics_correct,
        '正確忽略無關類別': ignored_correctly
    }
    
    all_passed = all(results.values())
    
    for test_name, passed in results.items():
        status = "✅" if passed else "❌"
        print(f"{status} {test_name}: {'通過' if passed else '失敗'}")
    
    if all_passed:
        print(f"\n🎉 所有測試通過！")
        print(f"✅ 成功過濾，只處理 CLASS_NAMES 中的 {len(CLASS_NAMES)} 個類別")
        print(f"✅ 正確忽略不在 CLASS_NAMES 中的類別")
        print(f"✅ 圖像顯示和CSV記錄都只包含指定類別")
    else:
        print(f"\n❌ 部分測試失敗，需要檢查過濾邏輯")

if __name__ == "__main__":
    test_class_names_filter()