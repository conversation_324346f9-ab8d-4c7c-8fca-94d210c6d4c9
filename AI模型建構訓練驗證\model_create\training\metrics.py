"""
指標整合模組

整合現有的指標計算功能，提供統一的指標接口
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass
import logging
from abc import ABC, abstractmethod

# 重用現有的指標模組
from ..util.metrics import get_metrics


@dataclass
class MetricsConfig:
    """指標配置"""
    # 基礎指標
    calculate_accuracy: bool = True
    calculate_precision: bool = True
    calculate_recall: bool = True
    calculate_f1: bool = True
    
    # 分割指標
    calculate_iou: bool = True
    calculate_dice: bool = True
    calculate_pixel_accuracy: bool = True
    
    # 檢測指標
    calculate_map: bool = False
    map_iou_thresholds: List[float] = None
    
    # 分類指標
    num_classes: Optional[int] = None
    class_names: Optional[List[str]] = None
    
    # 回歸指標
    calculate_mse: bool = False
    calculate_mae: bool = False
    calculate_r2: bool = False
    
    # 高級選項
    per_class_metrics: bool = False
    confusion_matrix: bool = False
    top_k_accuracy: Optional[int] = None
    
    def __post_init__(self):
        if self.map_iou_thresholds is None:
            self.map_iou_thresholds = [0.5, 0.75]


class BaseMetrics(ABC):
    """指標計算基類"""
    
    def __init__(self, config: MetricsConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.reset()
    
    @abstractmethod
    def update(self, predictions: torch.Tensor, targets: torch.Tensor):
        """更新指標狀態"""
        pass
    
    @abstractmethod
    def compute(self) -> Dict[str, float]:
        """計算最終指標"""
        pass
    
    @abstractmethod
    def reset(self):
        """重置指標狀態"""
        pass


class ClassificationMetrics(BaseMetrics):
    """分類任務指標"""
    
    def __init__(self, config: MetricsConfig):
        super().__init__(config)
        self.predictions = []
        self.targets = []
    
    def reset(self):
        """重置狀態"""
        self.predictions = []
        self.targets = []
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor):
        """更新預測和目標"""
        # 轉換為CPU並保存
        pred_cpu = predictions.detach().cpu()
        target_cpu = targets.detach().cpu()
        
        # 如果是softmax輸出，取最大值索引
        if pred_cpu.dim() > 1 and pred_cpu.size(1) > 1:
            pred_cpu = torch.argmax(pred_cpu, dim=1)
        
        self.predictions.append(pred_cpu)
        self.targets.append(target_cpu)
    
    def compute(self) -> Dict[str, float]:
        """計算分類指標"""
        if not self.predictions:
            return {}
        
        # 合併所有預測和目標
        all_preds = torch.cat(self.predictions)
        all_targets = torch.cat(self.targets)
        
        # 轉換為numpy
        preds_np = all_preds.numpy()
        targets_np = all_targets.numpy()
        
        metrics = {}
        
        # 準確率
        if self.config.calculate_accuracy:
            accuracy = (preds_np == targets_np).mean()
            metrics['accuracy'] = float(accuracy)
        
        # 使用sklearn計算其他指標
        try:
            from sklearn.metrics import precision_recall_fscore_support, classification_report
            
            # 精確率、召回率、F1
            if any([self.config.calculate_precision, self.config.calculate_recall, self.config.calculate_f1]):
                precision, recall, f1, _ = precision_recall_fscore_support(
                    targets_np, preds_np, average='weighted', zero_division=0
                )
                
                if self.config.calculate_precision:
                    metrics['precision'] = float(precision)
                if self.config.calculate_recall:
                    metrics['recall'] = float(recall)
                if self.config.calculate_f1:
                    metrics['f1'] = float(f1)
            
            # 每類別指標
            if self.config.per_class_metrics and self.config.class_names:
                report = classification_report(
                    targets_np, preds_np, 
                    target_names=self.config.class_names,
                    output_dict=True,
                    zero_division=0
                )
                
                for class_name in self.config.class_names:
                    if class_name in report:
                        class_metrics = report[class_name]
                        metrics[f'{class_name}_precision'] = class_metrics['precision']
                        metrics[f'{class_name}_recall'] = class_metrics['recall']
                        metrics[f'{class_name}_f1'] = class_metrics['f1-score']
        
        except ImportError:
            self.logger.warning("sklearn不可用，跳過高級指標計算")
        
        # Top-K準確率
        if self.config.top_k_accuracy and len(self.predictions) > 0:
            # 這需要原始的softmax輸出
            # 如果有需要可以在update中同時保存原始輸出
            pass
        
        return metrics


class SegmentationMetrics(BaseMetrics):
    """分割任務指標"""
    
    def __init__(self, config: MetricsConfig):
        super().__init__(config)
        self.intersection_sum = 0
        self.union_sum = 0
        self.target_sum = 0
        self.pred_sum = 0
        self.pixel_total = 0
        self.pixel_correct = 0
    
    def reset(self):
        """重置狀態"""
        self.intersection_sum = 0
        self.union_sum = 0
        self.target_sum = 0
        self.pred_sum = 0
        self.pixel_total = 0
        self.pixel_correct = 0
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor):
        """更新分割指標"""
        # 確保預測和目標是二值的
        if predictions.dim() > 3:  # (B, C, H, W)
            pred_binary = torch.argmax(predictions, dim=1)
        else:
            pred_binary = (predictions > 0.5).float()
        
        if targets.dim() > 3:
            target_binary = torch.argmax(targets, dim=1)
        else:
            target_binary = targets.float()
        
        # 計算交集和並集
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        self.intersection_sum += intersection.item()
        self.union_sum += union.item()
        self.target_sum += target_binary.sum().item()
        self.pred_sum += pred_binary.sum().item()
        
        # 像素準確率
        if self.config.calculate_pixel_accuracy:
            correct_pixels = (pred_binary == target_binary).sum()
            total_pixels = target_binary.numel()
            
            self.pixel_correct += correct_pixels.item()
            self.pixel_total += total_pixels
    
    def compute(self) -> Dict[str, float]:
        """計算分割指標"""
        metrics = {}
        
        # IoU (Intersection over Union)
        if self.config.calculate_iou and self.union_sum > 0:
            iou = self.intersection_sum / self.union_sum
            metrics['iou'] = float(iou)
        
        # Dice係數
        if self.config.calculate_dice and (self.pred_sum + self.target_sum) > 0:
            dice = 2 * self.intersection_sum / (self.pred_sum + self.target_sum)
            metrics['dice'] = float(dice)
        
        # 像素準確率
        if self.config.calculate_pixel_accuracy and self.pixel_total > 0:
            pixel_acc = self.pixel_correct / self.pixel_total
            metrics['pixel_accuracy'] = float(pixel_acc)
        
        return metrics


class RegressionMetrics(BaseMetrics):
    """回歸任務指標"""
    
    def __init__(self, config: MetricsConfig):
        super().__init__(config)
        self.predictions = []
        self.targets = []
    
    def reset(self):
        """重置狀態"""
        self.predictions = []
        self.targets = []
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor):
        """更新預測和目標"""
        pred_cpu = predictions.detach().cpu()
        target_cpu = targets.detach().cpu()
        
        self.predictions.append(pred_cpu)
        self.targets.append(target_cpu)
    
    def compute(self) -> Dict[str, float]:
        """計算回歸指標"""
        if not self.predictions:
            return {}
        
        # 合併所有預測和目標
        all_preds = torch.cat(self.predictions)
        all_targets = torch.cat(self.targets)
        
        metrics = {}
        
        # MSE (Mean Squared Error)
        if self.config.calculate_mse:
            mse = torch.mean((all_preds - all_targets) ** 2)
            metrics['mse'] = float(mse)
        
        # MAE (Mean Absolute Error)
        if self.config.calculate_mae:
            mae = torch.mean(torch.abs(all_preds - all_targets))
            metrics['mae'] = float(mae)
        
        # R²決定係數
        if self.config.calculate_r2:
            target_mean = torch.mean(all_targets)
            ss_tot = torch.sum((all_targets - target_mean) ** 2)
            ss_res = torch.sum((all_targets - all_preds) ** 2)
            
            if ss_tot > 0:
                r2 = 1 - (ss_res / ss_tot)
                metrics['r2'] = float(r2)
        
        return metrics


class MetricsFactory:
    """指標工廠"""
    
    @staticmethod
    def create_metrics(task_type: str, config: MetricsConfig) -> BaseMetrics:
        """
        創建指標計算器
        
        Args:
            task_type: 任務類型 ('classification', 'segmentation', 'regression')
            config: 指標配置
            
        Returns:
            BaseMetrics: 指標計算器實例
        """
        task_type = task_type.lower()
        
        if task_type == 'classification':
            return ClassificationMetrics(config)
        elif task_type == 'segmentation':
            return SegmentationMetrics(config)
        elif task_type == 'regression':
            return RegressionMetrics(config)
        else:
            raise ValueError(f"不支援的任務類型: {task_type}")
    
    @staticmethod
    def get_default_config(task_type: str, num_classes: Optional[int] = None) -> MetricsConfig:
        """
        獲取默認配置
        
        Args:
            task_type: 任務類型
            num_classes: 類別數量
            
        Returns:
            MetricsConfig: 默認配置
        """
        task_type = task_type.lower()
        
        if task_type == 'classification':
            return MetricsConfig(
                calculate_accuracy=True,
                calculate_precision=True,
                calculate_recall=True,
                calculate_f1=True,
                num_classes=num_classes,
                calculate_iou=False,
                calculate_dice=False
            )
        elif task_type == 'segmentation':
            return MetricsConfig(
                calculate_iou=True,
                calculate_dice=True,
                calculate_pixel_accuracy=True,
                num_classes=num_classes,
                calculate_accuracy=False,
                calculate_precision=False,
                calculate_recall=False,
                calculate_f1=False
            )
        elif task_type == 'regression':
            return MetricsConfig(
                calculate_mse=True,
                calculate_mae=True,
                calculate_r2=True,
                calculate_accuracy=False,
                calculate_iou=False,
                calculate_dice=False
            )
        else:
            return MetricsConfig()


class UnifiedMetricsCalculator:
    """統一指標計算器"""
    
    def __init__(self, 
                 task_type: str,
                 config: Optional[MetricsConfig] = None,
                 legacy_metrics_fn: Optional[Callable] = None):
        """
        初始化統一指標計算器
        
        Args:
            task_type: 任務類型
            config: 指標配置
            legacy_metrics_fn: 現有的指標計算函數（向後兼容）
        """
        self.task_type = task_type
        self.config = config or MetricsFactory.get_default_config(task_type)
        self.legacy_metrics_fn = legacy_metrics_fn
        self.logger = logging.getLogger(__name__)
        
        # 創建主要指標計算器
        self.metrics_calculator = MetricsFactory.create_metrics(task_type, self.config)
        
    def update(self, predictions: torch.Tensor, targets: torch.Tensor):
        """更新指標"""
        self.metrics_calculator.update(predictions, targets)
    
    def compute(self) -> Dict[str, float]:
        """計算所有指標"""
        # 計算新的指標
        metrics = self.metrics_calculator.compute()
        
        # 如果有legacy函數，也計算legacy指標
        if self.legacy_metrics_fn:
            try:
                # 獲取累積的預測和目標
                if hasattr(self.metrics_calculator, 'predictions') and self.metrics_calculator.predictions:
                    all_preds = torch.cat(self.metrics_calculator.predictions)
                    all_targets = torch.cat(self.metrics_calculator.targets)
                    
                    legacy_metrics = self.legacy_metrics_fn(all_preds, all_targets)
                    if isinstance(legacy_metrics, dict):
                        # 合併legacy指標，避免重複鍵
                        for key, value in legacy_metrics.items():
                            legacy_key = f"legacy_{key}" if key in metrics else key
                            metrics[legacy_key] = value
                
            except Exception as e:
                self.logger.warning(f"Legacy指標計算失敗: {e}")
        
        return metrics
    
    def reset(self):
        """重置指標狀態"""
        self.metrics_calculator.reset()
    
    def get_summary(self) -> str:
        """獲取指標摘要"""
        metrics = self.compute()
        
        if not metrics:
            return "無可用指標"
        
        summary_lines = [f"=== {self.task_type.upper()} 指標摘要 ==="]
        
        # 按類型分組顯示
        accuracy_metrics = {k: v for k, v in metrics.items() if 'accuracy' in k.lower()}
        if accuracy_metrics:
            summary_lines.append("準確率指標:")
            for k, v in accuracy_metrics.items():
                summary_lines.append(f"  {k}: {v:.4f}")
        
        loss_metrics = {k: v for k, v in metrics.items() if 'loss' in k.lower() or 'error' in k.lower()}
        if loss_metrics:
            summary_lines.append("損失指標:")
            for k, v in loss_metrics.items():
                summary_lines.append(f"  {k}: {v:.4f}")
        
        other_metrics = {k: v for k, v in metrics.items() 
                        if k not in accuracy_metrics and k not in loss_metrics}
        if other_metrics:
            summary_lines.append("其他指標:")
            for k, v in other_metrics.items():
                summary_lines.append(f"  {k}: {v:.4f}")
        
        return "\n".join(summary_lines)


def create_metrics_calculator(task_type: str, 
                            num_classes: Optional[int] = None,
                            custom_config: Optional[Dict[str, Any]] = None,
                            legacy_metrics_fn: Optional[Callable] = None) -> UnifiedMetricsCalculator:
    """
    便捷函數：創建指標計算器
    
    Args:
        task_type: 任務類型
        num_classes: 類別數量
        custom_config: 自定義配置
        legacy_metrics_fn: 現有指標函數
        
    Returns:
        UnifiedMetricsCalculator: 統一指標計算器
    """
    # 獲取默認配置
    config = MetricsFactory.get_default_config(task_type, num_classes)
    
    # 應用自定義配置
    if custom_config:
        for key, value in custom_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
    
    return UnifiedMetricsCalculator(task_type, config, legacy_metrics_fn)