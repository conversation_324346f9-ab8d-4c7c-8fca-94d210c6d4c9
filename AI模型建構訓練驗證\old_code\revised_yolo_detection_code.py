import os
import logging
import torch
from ultralytics import YOLO
import glob
import cv2
import numpy as np
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import argparse
import time

"""設置日誌"""
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('YOLO_Predict')

# 置信度閾值設定
CONFIDENCE_THRESHOLD = 0.7  # 預設值通常是 0.25，可以調整為 0.1 到 0.9 之間的任何值

# 類別名稱映射
class_names = {0: '背景', 1: '鳥', 2: '方塊', 3: '擋土座', 4: '樹魚', 5: '台北蓋水特色框蓋'}

# 從英文到中文的完整映射
en_to_cn_mapping = {
    'bird': '鳥',
    'block': '方塊',
    'retaining seat': '擋土座',
    'treefish': '樹魚',
    'tpe': '台北蓋水特色框蓋',
    'background': '背景'
}

# 從中文到英文的映射
cn_to_en_mapping = {v: k for k, v in en_to_cn_mapping.items()}

def draw_chinese_labels(image, boxes, font_path=None):
    """繪製中文標籤的檢測框"""
    # 複製圖像以避免修改原始圖像
    img_copy = image.copy()
    
    # 轉換為 PIL 圖像以支援中文
    img_pil = Image.fromarray(cv2.cvtColor(img_copy, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img_pil)
    
    # 嘗試載入中文字體
    try:
        if font_path and os.path.exists(font_path):
            font = ImageFont.truetype(font_path, 20)
        else:
            # 嘗試使用系統字體
            font_paths = [
                'C:/Windows/Fonts/msyh.ttc',  # 微軟雅黑
                'C:/Windows/Fonts/mingliub.ttc',  # 細明體
                'C:/Windows/Fonts/simsun.ttc',  # 宋體
                '/System/Library/Fonts/PingFang.ttc',  # macOS
                '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
            ]
            
            font = None
            for path in font_paths:
                if os.path.exists(path):
                    font = ImageFont.truetype(path, 20)
                    break
            
            if font is None:
                font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()
    
    # 定義顏色
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
    
    # 繪製每個檢測框
    for box in boxes:
        # 獲取座標
        x1, y1, x2, y2 = box.xyxy[0].tolist()
        cls_id = int(box.cls)
        conf = float(box.conf)
        
        # 選擇顏色
        color = colors[cls_id % len(colors)]
        
        # 繪製框
        draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
        
        # 準備標籤文字
        class_name = class_names.get(cls_id+1, f'類別{cls_id+1}')
        label = f"{class_name} {conf:.2f}"
        
        # 計算文字大小
        left, top, right, bottom = draw.textbbox((x1, y1), label, font=font)
        text_width = right - left
        text_height = bottom - top
        
        # 繪製文字背景
        draw.rectangle([x1, y1 - text_height, x1 + text_width, y1], fill=color)
        
        # 繪製文字
        draw.text((x1, y1 - text_height), label, fill=(0, 0, 0), font=font)
    
    # 轉換回 OpenCV 格式
    result_img = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    return result_img

def verify_test_dir(test_dir):
    """驗證測試目錄結構並顯示資訊"""
    logger.info(f"檢查測試目錄: {test_dir}")
    
    if os.path.exists(test_dir):
        logger.info(f"✓ 測試目錄存在")
        
        # 使用新的遞迴函數來檢查所有子目錄
        image_files = get_image_files(test_dir)
        
        if image_files:
            logger.info(f"✓ 總共找到 {len(image_files)} 個圖像文件")
            
            # 顯示前幾個圖像文件作為示例
            for i in range(min(5, len(image_files))):
                logger.info(f"  - {os.path.basename(image_files[i])}")
            if len(image_files) > 5:
                logger.info(f"  - ... 等 {len(image_files) - 5} 個文件")
            return True
        else:
            logger.warning(f"✗ 在目錄及其子目錄中沒有找到任何圖像文件")
    else:
        logger.error(f"✗ 測試目錄不存在")
        
    return False

def get_image_files(test_dir):
    """遞迴獲取目錄及其子目錄下的所有圖像文件"""
    image_files = []
    valid_extensions = ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']
    
    # 遞迴函數
    def collect_images(directory):
        nonlocal image_files
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            
            # 如果是目錄，則遞迴調用
            if os.path.isdir(item_path):
                # 檢查是否為特殊的 images 子目錄
                if item == 'images':
                    # 優先處理 images 子目錄
                    for img_file in os.listdir(item_path):
                        img_file_path = os.path.join(item_path, img_file)
                        if os.path.isfile(img_file_path) and os.path.splitext(img_file_path)[1].lower() in [ext.lower() for ext in valid_extensions]:
                            image_files.append(img_file_path)
                else:
                    # 處理其他子目錄
                    collect_images(item_path)
            
            # 如果是文件，檢查是否為圖像文件
            elif os.path.isfile(item_path):
                ext = os.path.splitext(item_path)[1]
                if ext.lower() in [ext.lower() for ext in valid_extensions]:
                    image_files.append(item_path)
    
    # 開始遞迴收集圖像
    try:
        collect_images(test_dir)
        logger.info(f"在 {test_dir} 及其子目錄中共找到 {len(image_files)} 個圖像文件")
    except Exception as e:
        logger.error(f"搜尋圖像時出錯: {str(e)}")
    
    return image_files

def load_model(model_path):
    """載入訓練好的模型"""
    logger.info(f"載入權重檔案: {model_path}")
    
    try:
        # 載入模型
        model = YOLO(model_path)
        logger.info(f"模型載入成功")
        return model
    except Exception as e:
        logger.error(f"模型載入失敗: {str(e)}")
        return None

def process_images(model, input_dirs, output_dir):
    """處理多個輸入目錄中的圖像並產生指定的輸出格式"""
    logger.info("開始處理圖像...")
    
    # 確保輸出目錄存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 創建圖像輸出目錄
    images_output_dir = os.path.join(output_dir, '圖像')
    os.makedirs(images_output_dir, exist_ok=True)
    
    # 初始化CSV數據
    csv_data = []
    serial_number = 0
    
    # 收集所有輸入目錄中的圖像文件
    all_image_files = []
    for input_dir in input_dirs:
        image_files = get_image_files(input_dir)
        if not image_files:
            logger.warning(f"在 {input_dir} 及其子目錄中找不到任何圖像檔案")
        else:
            all_image_files.extend(image_files)
            
    if not all_image_files:
        logger.error("在所有輸入目錄中均找不到任何圖像檔案，無法繼續")
        return
        
    logger.info(f"共找到 {len(all_image_files)} 個待處理的圖像檔案")
    
    # 逐一處理圖像
    for i, img_path in enumerate(all_image_files):
        # 計算進度
        progress = (i + 1) / len(all_image_files) * 100
        logger.info(f"處理進度: {progress:.1f}% ({i+1}/{len(all_image_files)})")
        
        try:
            # 確認檔案是否為有效圖像
            try:
                img = cv2.imread(img_path)
                if img is None:
                    logger.warning(f"跳過無效圖像檔案: {img_path}")
                    continue
            except Exception as e:
                logger.warning(f"讀取圖像時出錯，跳過: {img_path}，錯誤: {str(e)}")
                continue
                
            # 獲取原始文件名（不含擴展名）
            original_filename = os.path.splitext(os.path.basename(img_path))[0]
            
            # 進行預測
            results = model.predict(
                source=img_path,
                conf=CONFIDENCE_THRESHOLD,
                save=False,
                save_txt=False,
                save_conf=False,
                verbose=False
            )
            
            # 處理檢測結果
            result = results[0]
            detected_classes = []
            
            if hasattr(result, 'boxes') and result.boxes is not None and len(result.boxes) > 0:
                # 有檢測到物體
                boxes = result.boxes
                
                # 使用自定義函數繪製中文標籤
                result_img = draw_chinese_labels(img, boxes)
                
                # 收集所有檢測到的類別
                for box in boxes:
                    cls_id = int(box.cls.item())
                    class_name_cn = class_names.get(cls_id+1, f'類別{cls_id+1}')
                    
                    # 添加到檢測類別列表
                    if class_name_cn not in detected_classes:
                        detected_classes.append(class_name_cn)
                    
                    # 添加到CSV數據
                    class_name_en = cn_to_en_mapping.get(class_name_cn, f'class{cls_id+1}')
                    
                    csv_data.append({
                        '序號': serial_number,
                        '照片原始檔名': original_filename,
                        '原始檔名_DETECT': f"{original_filename}_{'_'.join(detected_classes)}_DETECT",
                        '類別': class_name_cn
                    })
                    
                    serial_number += 1
                
                # 生成文件名
                classes_string = '_'.join(detected_classes)
                output_filename = f"{original_filename}_{classes_string}_DETECT.jpg"
                
            else:
                # 沒有檢測到物體，分類為「孔蓋下地」
                result_img = img.copy()
                
                # 在圖像上添加「孔蓋下地」文字
                # 使用 PIL 繪製中文文字
                img_pil = Image.fromarray(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
                draw = ImageDraw.Draw(img_pil)
                
                # 嘗試載入中文字體
                try:
                    font_paths = [
                        'C:/Windows/Fonts/msyh.ttc',  # 微軟雅黑
                        'C:/Windows/Fonts/mingliub.ttc',  # 細明體
                        'C:/Windows/Fonts/simsun.ttc',  # 宋體
                        '/System/Library/Fonts/PingFang.ttc',  # macOS
                        '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
                    ]
                    
                    font = None
                    for path in font_paths:
                        if os.path.exists(path):
                            font = ImageFont.truetype(path, 40)
                            break
                    
                    if font is None:
                        font = ImageFont.load_default()
                except:
                    font = ImageFont.load_default()
                
                # 計算文字位置（置中）
                img_width, img_height = img_pil.size
                text = "孔蓋下地"
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                position = ((img_width - text_width) // 2, (img_height - text_height) // 2)
                
                # 繪製文字背景
                draw.rectangle([position[0]-10, position[1]-10, position[0]+text_width+10, position[1]+text_height+10], fill=(255, 0, 0))
                
                # 繪製文字
                draw.text(position, text, font=font, fill=(255, 255, 255))
                
                # 轉換回 OpenCV 格式
                result_img = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
                
                output_filename = f"{original_filename}_孔蓋下地_DETECT.jpg"
                
                # 添加到CSV數據
                csv_data.append({
                    '序號': serial_number,
                    '照片原始檔名': original_filename,
                    '原始檔名_DETECT': f"{original_filename}_孔蓋下地_DETECT",
                    '類別': '孔蓋下地'
                })
                
                serial_number += 1
            
            # 保存圖像
            output_path = os.path.join(images_output_dir, output_filename)
            cv2.imwrite(output_path, result_img)
            
            logger.info(f"已處理: {os.path.basename(img_path)} -> {output_filename}")
            
        except Exception as e:
            logger.error(f"處理圖像時發生錯誤: {img_path}, 錯誤: {str(e)}")
            continue
    
    # 保存CSV數據
    if csv_data:
        df = pd.DataFrame(csv_data)
        csv_path = os.path.join(output_dir, '孔蓋檢測結果.csv')
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        logger.info(f"所有圖像處理完成！")
        logger.info(f"共處理了 {len(all_image_files)} 張圖像")
        logger.info(f"圖像已保存至: {images_output_dir}")
        logger.info(f"CSV已保存至: {csv_path}")
    else:
        logger.warning("沒有檢測到任何有效結果，未生成CSV文件")

def main():
    # 創建命令行參數解析器
    parser = argparse.ArgumentParser(description='YOLOv12 孔蓋檢測程式')
    parser.add_argument('--model', type=str, default='run_data/yolov12x_20250505_191229/weights/best.pt', help='模型權重檔案路徑')
    parser.add_argument('--input_dirs', nargs='+', required=True, help='輸入目錄路徑，可以指定多個')
    parser.add_argument('--output_dir', type=str, default='孔蓋檢測結果', help='輸出目錄路徑')
    parser.add_argument('--conf', type=float, default=0.7, help='置信度閾值，預設為0.25')
    
    args = parser.parse_args()
    
    # 更新全局置信度閾值
    global CONFIDENCE_THRESHOLD
    CONFIDENCE_THRESHOLD = args.conf
    
    logger.info("=" * 50)
    logger.info("===== 開始 YOLOv12 孔蓋檢測 =====")
    logger.info("=" * 50)
    logger.info(f"模型路徑: {args.model}")
    logger.info(f"輸入目錄: {args.input_dirs}")
    logger.info(f"輸出目錄: {args.output_dir}")
    logger.info(f"置信度閾值: {CONFIDENCE_THRESHOLD}")
    
    # 驗證輸入目錄
    valid_dirs = []
    for input_dir in args.input_dirs:
        if os.path.exists(input_dir):
            valid_dirs.append(input_dir)
            logger.info(f"驗證目錄: {input_dir} - 有效")
        else:
            logger.error(f"驗證目錄: {input_dir} - 不存在，將被跳過")
    
    if not valid_dirs:
        logger.error("所有輸入目錄均不存在，無法繼續")
        return
    
    # 載入模型
    model = load_model(args.model)
    if model is None:
        logger.error("模型載入失敗，無法繼續")
        return
    
    # 顯示處理開始的詳細時間資訊
    start_time = time.time()
    start_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
    logger.info(f"處理開始時間: {start_time_str}")
    
    # 處理圖像
    process_images(model, valid_dirs, args.output_dir)
    
    # 顯示處理完成的詳細時間資訊
    end_time = time.time()
    end_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))
    logger.info(f"處理結束時間: {end_time_str}")
    
    # 計算總耗時
    total_time = end_time - start_time
    hours = int(total_time // 3600)
    minutes = int((total_time % 3600) // 60)
    seconds = int(total_time % 60)
    
    logger.info(f"總處理時間: {hours}小時 {minutes}分鐘 {seconds}秒")
    logger.info("=" * 50)
    logger.info("===== 程式執行完成！=====")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()