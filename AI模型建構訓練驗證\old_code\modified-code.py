from ultralytics import YOLO
import logging
import os
import time
import gc
import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
import pandas as pd
from pathlib import Path
import glob
import sys
import locale
from tqdm import tqdm
import psutil
import csv

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False
# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Main')


def inference_yolo_with_csv_images(model, csv_file1, csv_file2, primary_img_dir, secondary_img_dir, output_dir,
                                   device="cuda", class_names=None, conf_threshold=0.7, iou_threshold=0.5,
                                   original_scale=0.0166):
    """
    使用YOLO segmentation模型進行推理，並結合兩組圖像和兩份CSV檔案

    參數:
        model: 已加載的YOLO模型 (應該是分割模型，如yolov11-seg)
        csv_file1: 第一份CSV檔案路徑
        csv_file2: 第二份CSV檔案路徑
        primary_img_dir: 主要圖像目錄 (包含第一份CSV中的圖像)
        secondary_img_dir: 次要圖像目錄 (包含比對用的圖像)
        output_dir: 輸出結果保存目錄
        device: 計算設備
        class_names: 類別名稱字典
        conf_threshold: 置信度閾值
        iou_threshold: IoU閾值
        original_scale: 原始圖像的比例因子，每像素對應的實際距離(米)
    """

    # 設定編碼環境
    if sys.platform.startswith('win'):
        # 在Windows設定控制台編碼
        locale.setlocale(locale.LC_ALL, 'zh_TW.UTF-8')  # 台灣繁體中文
    else:
        # 在Linux/Mac設定UTF-8編碼
        locale.setlocale(locale.LC_ALL, 'zh_TW.utf8')

    # 設定matplotlib中文支援
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
    plt.rcParams['axes.unicode_minus'] = False  # 正確顯示負號
    plt.ioff()  # 關閉互動模式

    # 註冊OpenCV中文字型
    def cv2_chinese_text(img, text, position, font_size=0.7, color=(255, 255, 255), thickness=2):
        """使用PIL在OpenCV圖像上添加中文文字"""
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np

        # 將OpenCV圖像轉換為PIL圖像
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)

        # 獲取系統支援的字型
        try:
            if sys.platform.startswith('win'):
                font_path = 'C:\\Windows\\Fonts\\msjh.ttc'  # Windows微軟正黑體
            elif sys.platform.startswith('darwin'):
                font_path = '/System/Library/Fonts/PingFang.ttc'  # macOS
            else:
                font_path = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'  # Linux文泉驛微米黑
            font = ImageFont.truetype(font_path, int(font_size * 20))
        except:
            # 如果找不到字型，使用默認字型
            font = ImageFont.load_default()

        # 繪製文字
        draw.text(position, text, font=font, fill=color)

        # 將PIL圖像轉換回OpenCV圖像
        return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

    # 確保輸出路徑存在
    os.makedirs(output_dir, exist_ok=True)

    # 為CSV數據創建多個小文件而不是一個大列表
    csv_temp_dir = os.path.join(output_dir, 'temp_csv')
    os.makedirs(csv_temp_dir, exist_ok=True)

    # 讀取CSV檔案
    try:
        df1 = pd.read_csv(csv_file1, encoding='utf-8-sig')
        logger.info(f"成功讀取第一份CSV: {csv_file1}")
    except Exception as e:
        try:
            df1 = pd.read_csv(csv_file1, encoding='utf-8')
            logger.info(f"成功讀取第一份CSV (utf-8): {csv_file1}")
        except Exception as e2:
            logger.error(f"無法讀取第一份CSV: {csv_file1}, 錯誤: {str(e2)}")
            return

    try:
        df2 = pd.read_csv(csv_file2, encoding='utf-8-sig')
        logger.info(f"成功讀取第二份CSV: {csv_file2}")
    except Exception as e:
        try:
            df2 = pd.read_csv(csv_file2, encoding='utf-8')
            logger.info(f"成功讀取第二份CSV (utf-8): {csv_file2}")
        except Exception as e2:
            logger.error(f"無法讀取第二份CSV: {csv_file2}, 錯誤: {str(e2)}")
            return

    # 檢查CSV是否包含必要欄位
    if '圖片' not in df1.columns and '檔案名稱' not in df1.columns:
        logger.error(f"錯誤: 第一份CSV沒有 '圖片' 或 '檔案名稱' 欄位")
        return

    if '圖片' not in df2.columns and '檔案名稱' not in df2.columns:
        logger.error(f"錯誤: 第二份CSV沒有 '圖片' 或 '檔案名稱' 欄位")
        return

    # 確定使用哪個欄位作為圖片名稱
    img_col1 = '圖片' if '圖片' in df1.columns else '檔案名稱'
    img_col2 = '圖片' if '圖片' in df2.columns else '檔案名稱'

    # 合併兩個CSV的圖片列表，但保持唯一性 (避免重複處理相同圖片)
    all_images = pd.concat([df1[img_col1], df2[img_col2]]
                           ).drop_duplicates().tolist()

    # 建立次要圖像清單 (用於比對)
    secondary_image_list = {}
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        for img_path in glob.glob(os.path.join(secondary_img_dir, ext)):
            img_name = os.path.basename(img_path)
            secondary_image_list[img_name] = img_path

    logger.info(f"找到 {len(secondary_image_list)} 張次要比對圖像")
    logger.info(f"共需處理 {len(all_images)} 張圖像")

    # 每個批次保存的CSV數據行數，用於最終合併
    csv_counter = 0
    record_count = 0  # 記錄計數
    total_time = 0
    total_frames = 0

    # 記憶體監控初始值
    initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    logger.info(f"初始記憶體使用: {initial_memory:.2f} MB")

    # 創建進度條
    infer_loop = tqdm(enumerate(all_images),
                      total=len(all_images), desc="處理圖像")

    # 存儲處理結果的列表
    batch_results_list = []

    # 處理每個圖像
    for batch_idx, img_name in infer_loop:
        try:
            # 構建原始圖像的完整路徑
            img_path = os.path.join(primary_img_dir, img_name)
            if not os.path.exists(img_path):
                # 嘗試在次目錄中查找
                found = False
                for root, dirs, files in os.walk(primary_img_dir):
                    if img_name in files:
                        img_path = os.path.join(root, img_name)
                        found = True
                        break

                if not found:
                    logger.warning(f"找不到圖像: {img_name}")
                    continue

            # 讀取原始圖像
            original_img = cv2.imread(img_path)
            if original_img is None:
                logger.warning(f"無法讀取圖像: {img_path}")
                continue

            # 獲取圖像尺寸
            img_height, img_width = original_img.shape[:2]

            # 模型預測
            start_time = time.time()
            results = model.predict(
                source=img_path,
                conf=conf_threshold,
                iou=iou_threshold,
                verbose=False
            )
            end_time = time.time()

            inference_time = end_time - start_time
            total_time += inference_time
            total_frames += 1

            # 處理預測結果
            result = results[0]  # 獲取第一個結果（因為只有一張圖像）

            has_predictions = False
            batch_results = []

            # 檢查任務類型和掩碼可用性
            if not hasattr(result, 'masks') or result.masks is None:
                logger.warning(
                    f"警告: 圖像 {img_name} 沒有分割掩碼。請確保您使用的是分割模型(如yolov11-seg)")
                continue

            # 創建彩色掩碼圖像用於視覺化
            mask_img = np.zeros((img_height, img_width, 3), dtype=np.uint8)

            # 固定的顏色映射
            np.random.seed(42)
            colors = {}

            # 獲取數據
            boxes = result.boxes
            masks = result.masks

            if len(boxes) == 0:
                logger.info(f"圖像 {img_name} 沒有檢測到任何物體")
                # 沒有物體時仍然創建組合圖像，但掩碼部分為空
                has_predictions = False
            else:
                has_predictions = True

                # 為每個類創建隨機顏色
                unique_classes = set()
                for j in range(len(boxes)):
                    cls_id = int(boxes.cls[j].item())
                    # 跳過路面接縫(ID 1)和伸縮縫(ID 0)
                    if cls_id == 1 or cls_id == 0:
                        continue
                    unique_classes.add(cls_id)

                for cls in unique_classes:
                    if cls not in colors:
                        colors[cls] = np.random.randint(0, 255, 3).tolist()

                # 處理每個檢測到的物體
                for i, c in enumerate(result):
                    try:
                        # 獲取類別ID和置信度
                        cls_id = int(c.boxes.cls.tolist().pop())
                        conf = float(c.boxes.conf.tolist().pop())

                        # 跳過路面接縫(類別ID 1)和伸縮縫(類別ID 0)
                        if cls_id == 1 or cls_id == 0:  # 注意: YOLO類別ID是從0開始，但我們的class_names是從1開始
                            continue

                        # 獲取類別名稱
                        class_name = class_names.get(
                            cls_id+1, f"類別 {cls_id+1}") if class_names else f"類別 {cls_id+1}"
                        # 獲取框座標
                        x1, y1, x2, y2 = c.boxes.xyxy.cpu().numpy().squeeze().astype(np.int32)

                        # 創建二值掩碼
                        b_mask = np.zeros(
                            (img_height, img_width), dtype=np.uint8)

                        # 獲取輪廓
                        try:
                            # 提取輪廓結果
                            if hasattr(c.masks, 'xy') and len(c.masks.xy) > 0:
                                contour = c.masks.xy[0].astype(
                                    np.int32).reshape(-1, 1, 2)
                                # 在掩碼上繪製輪廓
                                cv2.drawContours(
                                    b_mask, [contour], -1, (255, 255, 255), cv2.FILLED)
                            else:
                                # 如果沒有輪廓數據，使用框作為備用方案
                                cv2.rectangle(b_mask, (x1, y1),
                                              (x2, y2), 255, -1)
                        except Exception as e:
                            logger.warning(f"提取輪廓時出錯: {str(e)}")
                            # 使用框作為備用方案
                            cv2.rectangle(b_mask, (x1, y1), (x2, y2), 255, -1)

                        # 將掩碼添加到彩色掩碼圖像
                        color = colors[cls_id]
                        mask_img[b_mask > 0] = color

                        # 計算掩碼的面積（像素）
                        area_px = np.sum(b_mask > 0)

                        # 計算實際尺寸
                        area_m2 = area_px * (original_scale ** 2)

                        # 計算寬度和高度 (從掩碼中)
                        width_px = x2 - x1
                        height_px = y2 - y1
                        width_m = width_px * original_scale
                        height_m = height_px * original_scale

                        if cls_id == 3:  # 裂縫類別
                            # 判斷是裂縫還是龜裂
                            aspect_ratio_threshold = 0.8  # 可調整的閾值
                            area_ratio_threshold = 0.4    # 可調整的閾值

                            # 計算長寬比
                            aspect_ratio = min(
                                width_px, height_px) / max(width_px, height_px)

                            # 計算框面積與掩碼面積的比率
                            box_area = width_px * height_px
                            mask_to_box_ratio = area_px / box_area if box_area > 0 else 0

                            # 判斷是裂縫還是龜裂
                            if aspect_ratio < aspect_ratio_threshold and mask_to_box_ratio < area_ratio_threshold:
                                # 符合裂縫條件
                                class_name = "裂縫"
                            else:
                                # 符合龜裂條件
                                class_name = "龜裂"

                        # 增加記錄計數
                        record_count += 1

                        # 添加到結果列表
                        batch_results.append({
                            "序號": record_count,
                            "檔案名稱": img_name,
                            "類別": class_name,
                            "長": f"{height_m:.2f}",
                            "寬": f"{width_m:.2f}",
                            "面積": f"{area_m2:.2f}",
                            "置信度": f"{conf:.4f}",  # 加入置信度
                            "x_min": x1,  # 加入box的x最小值
                            "y_min": y1,  # 加入box的y最小值
                            "x_max": x2,  # 加入box的x最大值
                            "y_max": y2,  # 加入box的y最大值
                        })
                    except Exception as e:
                        logger.warning(f"處理物體 {i} 時出錯: {str(e)}")
                        continue

            # 轉換原圖為RGB以便處理
            original_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)

            # 創建掩碼疊加圖像
            alpha = 0.7
            overlay_img = original_rgb.copy()
            mask = np.any(mask_img > 0, axis=2)
            overlay_img[mask] = (1-alpha) * \
                overlay_img[mask] + alpha * mask_img[mask]

            # 查找次要圖像 (比對用)
            secondary_img = None
            secondary_img_path = None
            if img_name in secondary_image_list:
                secondary_img_path = secondary_image_list[img_name]
                secondary_img = cv2.imread(secondary_img_path)
                if secondary_img is None:
                    logger.warning(f"無法讀取次要圖像: {secondary_img_path}")

            # 創建三張圖像的拼接
            if secondary_img is not None:
                # 確保次要圖像與原圖大小一致
                secondary_img = cv2.resize(
                    secondary_img, (original_img.shape[1], original_img.shape[0]))
                # 將次要圖像轉為RGB
                secondary_rgb = cv2.cvtColor(secondary_img, cv2.COLOR_BGR2RGB)
                # 三張圖並排 (原圖, 掩碼疊加, 次要圖像)
                combined_img = np.hstack(
                    (original_rgb, overlay_img, secondary_rgb))
            else:
                # 只有原圖和掩碼疊加
                combined_img = np.hstack((original_rgb, overlay_img))
                # 添加一個空白區域替代次要圖像
                blank_img = np.zeros_like(original_rgb)
                combined_img = np.hstack((combined_img, blank_img))

            # 轉回BGR用於OpenCV處理
            combined_img = cv2.cvtColor(combined_img, cv2.COLOR_RGB2BGR)

            # 添加圖例
            legend_y = 0
            for cls_id, color in colors.items():
                if cls_id in class_names:
                    class_name = class_names[cls_id+1]
                else:
                    class_name = f"類別 {cls_id+1}"

                # 繪製顏色方塊
                cv2.rectangle(combined_img,
                              (original_img.shape[1] * 3 - 400, legend_y + 50),
                              (original_img.shape[1] *
                               3 - 250, legend_y + 200),
                              color, -1)

                # 添加類別名稱
                combined_img = cv2_chinese_text(
                    combined_img,
                    class_name,
                    (original_img.shape[1] * 3 - 240, legend_y + 100),
                    font_size=2.0,
                    color=(0, 0, 0)
                )

                legend_y += 230

            # 添加標題
            combined_img = cv2_chinese_text(
                combined_img,
                "原始圖像",
                (original_img.shape[1] // 4, 30),
                font_size=2.0,
                color=(255, 255, 255)
            )

            combined_img = cv2_chinese_text(
                combined_img,
                "分割掩碼",
                (original_img.shape[1] + original_img.shape[1] // 4, 30),
                font_size=2.0,
                color=(255, 255, 255)
            )

            combined_img = cv2_chinese_text(
                combined_img,
                "次要圖像" if secondary_img is not None else "無對應次要圖像",
                (original_img.shape[1] * 2 + original_img.shape[1] // 4, 30),
                font_size=2.0,
                color=(255, 255, 255)
            )

            # 在圖像上添加掩碼信息
            if has_predictions:
                info_y = img_height - 80
                combined_img = cv2_chinese_text(
                    combined_img,
                    f"掩碼數量: {len(batch_results)}",
                    (original_img.shape[1] + 20, info_y),
                    font_size=0.8,
                    color=(255, 255, 255)
                )

                # 添加總面積
                total_area_m2 = sum(float(result['面積'].strip())
                                    for result in batch_results)

                combined_img = cv2_chinese_text(
                    combined_img,
                    f"總面積: {total_area_m2:.2f} m²",
                    (original_img.shape[1] + 20, info_y + 25),
                    font_size=0.8,
                    color=(255, 255, 255)
                )
            else:
                # 未檢測到物體的信息
                info_y = img_height - 80
                combined_img = cv2_chinese_text(
                    combined_img,
                    "未檢測到物體",
                    (original_img.shape[1] + 20, info_y),
                    font_size=1.0,
                    color=(255, 255, 255)
                )

            # 保存圖像 - 使用安全的檔名
            safe_filename = ''.join(
                c if c.isalnum() or c in '._- ' else '_' for c in img_name)
            save_path = os.path.join(
                output_dir, f'segmentation_{safe_filename}')
            cv2.imwrite(save_path, combined_img)

            # 將批次結果添加到總結果列表
            if batch_results:
                batch_results_list.extend(batch_results)
                # 當超過一定數量時，保存到臨時CSV
                if len(batch_results_list) >= 1000:
                    temp_df = pd.DataFrame(batch_results_list)
                    temp_csv_path = os.path.join(
                        csv_temp_dir, f'temp_{csv_counter:06d}.csv')
                    temp_df.to_csv(temp_csv_path, index=False,
                                   encoding='utf_8_sig')
                    csv_counter += 1
                    batch_results_list = []  # 清空列表

            # 更新進度條
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_diff = current_memory - initial_memory
            infer_loop.set_postfix(
                Progress=f"{batch_idx+1}/{len(all_images)}",
                Records=record_count,
                Time=f"{inference_time:.4f}s",
                Memory=f"{current_memory:.2f}MB ({memory_diff:+.2f})"
            )

            # 每50張圖像進行一次強制清理
            if batch_idx % 50 == 0 and batch_idx > 0:
                # 強制清理
                for _ in range(3):  # 多次嘗試清理
                    gc.collect()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    time.sleep(0.1)  # 給系統一些時間來整理記憶體

                # 記錄清理後的記憶體使用
                after_gc_memory = psutil.Process().memory_info().rss / 1024 / 1024
                logger.info(
                    f"批次 {batch_idx}: 清理後記憶體使用 {after_gc_memory:.2f} MB")

                # 定期保存中間結果
                if batch_idx % 500 == 0:
                    # 儲存現有batch_results_list到臨時文件
                    if batch_results_list:
                        temp_df = pd.DataFrame(batch_results_list)
                        temp_csv_path = os.path.join(
                            csv_temp_dir, f'temp_{csv_counter:06d}.csv')
                        temp_df.to_csv(temp_csv_path, index=False,
                                       encoding='utf_8_sig')
                        csv_counter += 1
                        batch_results_list = []
                    # 合併目前為止的所有臨時CSV
                    merge_temp_csvs(csv_temp_dir, output_dir,
                                    f'segment_checkpoint_{batch_idx}.csv')

        except Exception as e:
            logger.error(f"處理圖像 {img_name} 時發生錯誤: {str(e)}")
            # 保存當前進度
            logger.info("正在保存目前進度...")
            try:
                # 保存剩餘的batch_results_list
                if batch_results_list:
                    temp_df = pd.DataFrame(batch_results_list)
                    temp_csv_path = os.path.join(
                        csv_temp_dir, f'temp_{csv_counter:06d}.csv')
                    temp_df.to_csv(temp_csv_path, index=False,
                                   encoding='utf_8_sig')
                    csv_counter += 1
                    batch_results_list = []

                # 合併所有已處理的CSV
                merge_temp_csvs(csv_temp_dir, output_dir,
                                f'segment_error_at_{batch_idx}.csv')
            except Exception as csv_err:
                logger.error(f"保存進度時發生錯誤: {str(csv_err)}")
            import traceback
            traceback.print_exc()

    # 保存最後的batch_results_list
    if batch_results_list:
        temp_df = pd.DataFrame(batch_results_list)
        temp_csv_path = os.path.join(
            csv_temp_dir, f'temp_{csv_counter:06d}.csv')
        temp_df.to_csv(temp_csv_path, index=False, encoding='utf_8_sig')

    # 計算性能指標
    fps = total_frames / total_time if total_time > 0 else 0
    logger.info(f"\n推理性能指標:")
    logger.info(f"總推理時間: {total_time:.2f} 秒")
    logger.info(f"總幀數: {total_frames}")
    logger.info(f"平均FPS: {fps:.2f}")

    # 保存性能指標
    with open(os.path.join(output_dir, 'segment_performance.csv'), 'w', newline='', encoding='utf_8_sig') as f:
        writer = csv.writer(f)
        writer.writerow(['總推理時間(秒)', '總幀數', '平均FPS'])
        writer.writerow([f"{total_time:.2f}", total_frames, f"{fps:.2f}"])

    # 合併所有臨時CSV文件到最終CSV
    logger.info("正在合併所有臨時CSV文件...")
    merge_temp_csvs(csv_temp_dir, output_dir, 'segment_statistics.csv')

    # 最終記憶體使用狀況
    final_memory = psutil.Process().memory_info().rss / 1024 / 1024
    logger.info(f"最終記憶體使用: {final_memory:.2f} MB")
    logger.info(f"記憶體增加: {final_memory - initial_memory:.2f} MB")

    logger.info(f"推理結果已保存至 {output_dir} 目錄")
    logger.info(
        f"分割統計已保存至 {os.path.join(output_dir, 'segment_statistics.csv')}")

    return fps


def merge_temp_csvs(temp_dir, output_dir, output_filename):
    """合併臨時CSV文件到一個最終文件，支援中文內容"""
    import glob
    import pandas as pd
    import os

    all_files = glob.glob(os.path.join(temp_dir, 'temp_*.csv'))
    if not all_files:
        logger.warning("沒有找到臨時CSV文件")
        return

    # 排序檔案，確保按順序合併
    all_files.sort()

    # 讀取並合併所有CSV
    all_dfs = []
    for file in all_files:
        try:
            # 使用utf_8_sig編碼處理中文
            df = pd.read_csv(file, encoding='utf_8_sig')
            all_dfs.append(df)
        except Exception as e:
            logger.error(f"讀取文件 {file} 時發生錯誤: {str(e)}")

    if all_dfs:
        # 合併所有DataFrame
        combined_df = pd.concat(all_dfs, ignore_index=True)
        # 保存合併後的CSV（使用utf_8_sig編碼處理中文）
        output_path = os.path.join(output_dir, output_filename)
        combined_df.to_csv(output_path, index=False, encoding='utf_8_sig')
        logger.info(f"已合併 {len(all_dfs)} 個臨時CSV文件，共 {len(combined_df)} 行數據")
    else:
        logger.warning("沒有有效的CSV數據可以合併")


def main():
    """主函數"""
    import argparse

    parser = argparse.ArgumentParser(description='道路損傷檢測與分割')
    parser.add_argument('--model_path', type=str,
                        required=True, help='模型權重文件路徑')
    parser.add_argument('--csv_file1', type=str,
                        required=True, help='第一份CSV文件路徑')
    parser.add_argument('--csv_file2', type=str,
                        required=True, help='第二份CSV文件路徑')
    parser.add_argument('--primary_img_dir', type=str,
                        required=True, help='主要圖像目錄')
    parser.add_argument('--secondary_img_dir', type=str,
                        required=True, help='次要比對圖像目錄')
    parser.add_argument('--output_dir', type=str, required=True, help='輸出目錄')
    parser.add_argument('--conf', type=float, default=0.1, help='置信度閾值')
    parser.add_argument('--iou', type=float, default=0.25, help='IoU閾值')
    parser.add_argument('--scale', type=float,
                        default=0.0166, help='每像素對應實際距離(米)')

    args = parser.parse_args()

    # 定義類別名稱
    class_names = {0: 'expansion_joint', 1: 'joint',
                   2: 'linear_crack', 3: 'Alligator_crack',
                   4: 'potholes', 5: 'patch', 6: 'manhole',
                   7: 'deformation', 8: 'dirt', 9: 'lane_line_linear',
                   }

    # 載入模型
    logger.info(f"載入權重檔案: {args.model_path}")
    try:
        model = YOLO(args.model_path)
        logger.info(f"模型載入成功")
    except Exception as e:
        logger.error(f"模型載入失敗: {str(e)}")
        return

    # 執行推理
    inference_yolo_with_csv_images(
        model=model,
        csv_file1=args.csv_file1,
        csv_file2=args.csv_file2,
        primary_img_dir=args.primary_img_dir,
        secondary_img_dir=args.secondary_img_dir,
        output_dir=args.output_dir,
        device="cuda" if torch.cuda.is_available() else "cpu",
        class_names=class_names,
        conf_threshold=args.conf,
        iou_threshold=args.iou,
        original_scale=args.scale
    )

    logger.info("處理完成")


if __name__ == "__main__":
    main()
