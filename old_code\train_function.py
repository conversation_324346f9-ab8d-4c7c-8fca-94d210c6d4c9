import torch
from tqdm import tqdm
import logging
import numpy as np
import torch
import os
import matplotlib.pyplot as plt
import time
import pandas as pd
import csv
import cv2
import random
import gc
import glob


"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Training')


def train(train_loader, model, optimizer, epoch_i, epoch_total, scheduler, device, loss_fn=None):
    """
    訓練函數

    參數:
        train_loader: 訓練資料加載器
        model: 模型
        optimizer: 優化器
        epoch_i: 當前輪次索引
        epoch_total: 總輪次數
        scheduler: 學習率調度器
        device: 計算設備
        loss_fn: 損失函數（若提供）

    返回:
        images[0]: 批次中的第一張圖片
        labels[0]: 批次中的第一張標籤
        preds[0]: 批次中的第一張預測
        loss_list: 損失列表
    """
    train_loop = tqdm(enumerate(train_loader), total=len(train_loader))
    # 用於累積損失的列表
    loss_list = []
    loss_components = {
        'total': [],
        'ce': [],
        'dice': [],
        'focal': [],
        'boundary': []
    }
    for index, (images, labels, filenames) in train_loop:
        # 確保模型處於訓練模式
        model.train()

        # 將數據移至設備
        images = images.to(device, non_blocking=True)
        labels = labels.to(device, non_blocking=True)

        # # 確保圖像是浮點型
        # if images.dtype != torch.float32:
        #     images = images.float()
        #     if images.max() > 1.0:
        #         images = images / 255.0

        # 模型預測
        pred = model(images)
        _, preds = torch.max(pred, dim=1)

        # 損失計算
        if loss_fn is not None:
            # 使用提供的損失函數
            loss = loss_fn(pred, labels).to(device)
        else:
            # 預設使用交叉熵損失
            from model_create.util.losses import cross_entropy2d, DiceLoss
            loss_1 = cross_entropy2d(pred, labels).to(device)
            loss_2 = DiceLoss()(preds, labels).to(device)
            loss = 0.4 * loss_1 + 0.6 * loss_2

        # 反向傳播和優化
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()

        # 調整學習率
        if scheduler is not None:
            scheduler.step()

        # 記錄損失
        loss_list.append(loss.item())

        # 如果損失函數提供了 get_loss_components 方法，記錄各組件
        # if hasattr(loss_fn, 'get_loss_components'):

        comps = loss_fn.get_loss_components(pred, labels)
        loss_components['ce'].append(comps.get('ce_loss', 0))
        loss_components['dice'].append(comps.get('dice_loss', 0))
        loss_components['focal'].append(comps.get('focal_loss', 0))
        loss_components['boundary'].append(comps.get('boundary_loss', 0))

        # 更新進度條
        train_loop.set_description(
            f'Epoch [{epoch_i+1:0>4d}/{epoch_total:0>4d}]')
        train_loop.set_postfix(
            Loss=loss.item(),
            LR=optimizer.param_groups[0]['lr']
        )

    # 返回最後批次的第一個樣本和平均損失
    return images[0], labels[0], preds[0], (loss_list, loss_components)


def validate(val_loader, model, epoch_i, epoch_total, n_classes, device, get_metrics_fn, runningScore):
    """
    驗證函數

    參數:
        val_loader: 驗證資料加載器
        model: 模型
        epoch_i: 當前輪次索引
        epoch_total: 總輪次數
        n_classes: 類別數量
        device: 計算設備
        get_metrics_fn: 獲取指標的函數
        runningScore: 用於計算指標的類
    """
    val_loop = tqdm(enumerate(val_loader), total=len(val_loader))

    # 設定模型為評估模式
    model.eval()

    # 初始化運行指標
    running_metrics_val = runningScore(n_classes=n_classes)

    # 空列表，用於添加準確率和 Jaccard 分數計算
    acc_sh = []
    js_sh = []

    with torch.no_grad():
        for image_num, (val_images, val_labels, filenames) in val_loop:
            # 將數據移至設備
            val_images = val_images.to(device, non_blocking=True)
            val_labels = val_labels.to(device, non_blocking=True)

            # 模型預測
            val_pred = model(val_images)
            _, preds = torch.max(val_pred, dim=1)

            # 轉換預測為 NumPy 陣列
            pred = val_pred.data.max(1)[1].cpu().numpy()
            gt = val_labels.data.cpu().numpy()

            # 損失計算
            from model_create.util.losses import cross_entropy2d
            loss = cross_entropy2d(val_pred, val_labels).to(device)

            # 更新指標
            running_metrics_val.update(gt, pred)
            sh_metrics = get_metrics_fn(gt.flatten(), pred.flatten())
            acc_sh.append(sh_metrics[0])
            js_sh.append(sh_metrics[1])

            # 更新進度條
            val_loop.set_description(
                f'Epoch [{epoch_i+1:0>4d}/{epoch_total:0>4d}]')
            val_loop.set_postfix(
                Accuracy=sh_metrics[0],
                Jaccard_Score=sh_metrics[1],
                Loss=loss.item()
            )

    # 獲取指標分數
    score = running_metrics_val.get_scores()
    running_metrics_val.reset()

    # 計算平均準確率和 Jaccard 分數
    acc_s = sum(acc_sh) / len(acc_sh)
    js_s = sum(js_sh) / len(js_sh)
    score["acc"] = acc_s
    score["js"] = js_s

    # 返回最後批次的第一個樣本和指標分數
    return val_images[0], val_labels[0], preds[0], (score)


def test(test_loader, model, n_classes, path_img, device, get_metrics_fn, runningScore, class_names):
    """
    測試函數，使用整體評估而非逐批次評估

    參數:
        test_loader: 測試資料加載器
        model: 模型
        n_classes: 類別數量
        path_img: 圖像保存路徑
        device: 計算設備
        get_metrics_fn: 獲取指標的函數
        runningScore: 用於計算指標的類
    """

    # 設定模型為評估模式
    model.eval()
    total_time = 0
    total_frames = 0
    # 初始化一個runningScore實例用於整體評估
    overall_metrics = runningScore(n_classes=n_classes)

    # 空列表，用於添加準確率和 Jaccard 分數計算
    all_gt_flattened = []
    all_pred_flattened = []

    # 創建進度條
    test_loop = tqdm(enumerate(test_loader), total=len(test_loader))

    with torch.no_grad():
        for image_num, (test_images, test_labels, filenames) in test_loop:

            # 將數據移至設備
            test_images = test_images.to(device, non_blocking=True)
            test_labels = test_labels.to(device, non_blocking=True)

            # 模型預測
            start_time = time.time()

            test_pred = model(test_images)
            end_time = time.time()
            inference_time = end_time - start_time
            total_time += inference_time
            total_frames += test_images.size(0)  # 加上當前batch中的圖像數量
            _, preds = torch.max(test_pred, dim=1)

            # 轉換預測為 NumPy 陣列
            pred = test_pred.data.max(1)[1].cpu().numpy()
            gt = test_labels.data.cpu().numpy()

            # 更新整體評估的混淆矩陣
            overall_metrics.update(gt, pred)

            # 收集所有展平的真實標籤和預測標籤，用於整體計算 accuracy 和 jaccard
            all_gt_flattened.append(gt.flatten())
            all_pred_flattened.append(pred.flatten())

            # 獲取當前文件名（如果有）
            current_filename = None
            if filenames is not None and len(filenames) > 0:
                current_filename = filenames[0]  # 僅取第一個檔名，因為batch_size=1

            # 更新進度條
            test_loop.set_postfix(
                Progress=f"{image_num+1}/{len(test_loader)}"
            )

            # 顯示測試樣本 (可選擇性顯示)
            from model_create.util.show_img import show_img
            # show_img(
            #     image_num + 1,
            #     test_images[0],
            #     test_labels[0],
            #     preds[0],
            #     path_img,
            #     'test',
            #     filename=current_filename,
            #     class_names=class_names
            # )

    # 將所有展平的標籤連接起來進行整體評估
    all_gt_flattened = np.concatenate(all_gt_flattened)
    all_pred_flattened = np.concatenate(all_pred_flattened)

    # 使用 get_metrics_fn 計算整體的準確率和 Jaccard 分數
    acc_s, js_s = get_metrics_fn(all_gt_flattened, all_pred_flattened)

    # 獲取整體評估的指標分數
    score = overall_metrics.get_scores()

    # 添加準確率和 Jaccard 分數到結果中
    score["acc"] = acc_s
    score["js"] = js_s

    print("\n整體評估指標:")

    # 打印混淆矩陣
    print("混淆矩陣:")
    print(score['confusion_matrix'])

    # 顯示總體指標
    print("\n總體指標:")
    print(f"F1: {score['F1']:.4f}")
    print(f"MIoU: {score['MIoU']:.4f}")
    print(f"Pixel_Accuracy: {score['Pixel_Accuracy']:.4f}")
    print(f"Specificity: {score['Specificity']:.4f}")
    print(f"Sensitivity: {score['Sensitivity']:.4f}")
    print(f"Precision: {score['Precision']:.4f}")
    print(f"Accuracy: {score['acc']:.4f}")
    print(f"Jaccard_Score: {score['js']:.4f}")

    # 列印每個類別的指標
    print("\n每個類別的指標:")
    for class_idx in range(n_classes):
        class_name = class_names.get(
            class_idx, f"類別 {class_idx}") if class_names else f"類別 {class_idx}"
        print(f"{class_name}:")
        print(f"  F1: {score['F1_per_class'][class_idx]:.4f}")
        print(f"  IoU: {score['IoU'][class_idx]:.4f}")
        print(f"  Dice: {score['Dice_per_class'][class_idx]:.4f}")
        print(f"  Precision: {score['Precision_per_class'][class_idx]:.4f}")
        print(
            f"  Sensitivity: {score['Sensitivity_per_class'][class_idx]:.4f}")
        print(
            f"  Specificity: {score['Specificity_per_class'][class_idx]:.4f}")

    # 計算非背景類別的平均指標
    non_bg_indices = list(range(1, n_classes))
    if non_bg_indices:  # 確保有非背景類別
        non_bg_f1 = np.mean(score['F1_per_class'][non_bg_indices])
        non_bg_iou = np.mean(score['IoU'][non_bg_indices])
        non_bg_dice = np.mean(score['Dice_per_class'][non_bg_indices])
        non_bg_precision = np.mean(
            score['Precision_per_class'][non_bg_indices])
        non_bg_sensitivity = np.mean(
            score['Sensitivity_per_class'][non_bg_indices])

        print("\n非背景類別的平均指標:")
        print(f"  F1: {non_bg_f1:.4f}")
        print(f"  IoU: {non_bg_iou:.4f}")
        print(f"  Dice: {non_bg_dice:.4f}")
        print(f"  Precision: {non_bg_precision:.4f}")
        print(f"  Sensitivity: {non_bg_sensitivity:.4f}")

    # 可視化每個類別的F1分數
    plt.figure(figsize=(10, 6))
    class_names_list = [class_names.get(i, f"類別 {i}") for i in range(
        n_classes)] if class_names else [f"類別 {i}" for i in range(n_classes)]
    plt.bar(class_names_list, score['F1_per_class'])
    plt.title('各類別F1分數')
    plt.ylabel('F1分數')
    plt.xlabel('類別')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig(os.path.join(path_img, 'class_f1_scores.png'))
    plt.show()

    # 可視化混淆矩陣
    plt.figure(figsize=(10, 8))
    confusion_matrix = score['confusion_matrix']

    # 計算每行的總和，用於歸一化
    row_sums = confusion_matrix.sum(axis=1)
    normalized_cm = confusion_matrix / row_sums[:, np.newaxis]

    plt.imshow(normalized_cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('歸一化混淆矩陣')
    plt.colorbar()
    tick_marks = np.arange(n_classes)
    plt.xticks(tick_marks, class_names_list, rotation=45, ha='right')
    plt.yticks(tick_marks, class_names_list)

    # 在混淆矩陣中顯示數值
    thresh = normalized_cm.max() / 2.
    for i in range(normalized_cm.shape[0]):
        for j in range(normalized_cm.shape[1]):
            plt.text(j, i, f"{normalized_cm[i, j]:.2f}",
                     horizontalalignment="center",
                     color="white" if normalized_cm[i, j] > thresh else "black")

    plt.ylabel('真實標籤')
    plt.xlabel('預測標籤')
    plt.tight_layout()
    plt.savefig(os.path.join(path_img, 'confusion_matrix.png'))
    plt.show()
    fps = total_frames / total_time if total_time > 0 else 0
    print(f"\n性能指標:")
    print(f"總推理時間: {total_time:.2f} 秒")
    print(f"總幀數: {total_frames}")
    print(f"平均FPS: {fps:.2f}")

    # 將FPS添加到結果字典中
    score["fps"] = fps

    # 1. 創建總體指標的CSV
    overall_metrics_dict = {
        "Metric": ["F1", "MIoU", "Pixel_Accuracy", "Specificity", "Sensitivity", "Precision", "Accuracy", "Jaccard_Score", "FPS"],
        "Value": [
            score['F1'],
            score['MIoU'],
            score['Pixel_Accuracy'],
            score['Specificity'],
            score['Sensitivity'],
            score['Precision'],
            score['acc'],
            score['js'],
            score['fps']
        ]
    }
    overall_df = pd.DataFrame(overall_metrics_dict)
    overall_df.to_csv(os.path.join(path_img, 'overall_metrics.csv'),
                      index=False, encoding='utf_8_sig')

    # 2. 創建各類別指標的CSV
    class_metrics = []
    for class_idx in range(n_classes):
        class_name = class_names.get(
            class_idx, f"類別 {class_idx}") if class_names else f"類別 {class_idx}"
        class_metrics.append({
            "Class": class_name,
            "F1": score['F1_per_class'][class_idx],
            "IoU": score['IoU'][class_idx],
            "Dice": score['Dice_per_class'][class_idx],
            "Precision": score['Precision_per_class'][class_idx],
            "Sensitivity": score['Sensitivity_per_class'][class_idx],
            "Specificity": score['Specificity_per_class'][class_idx]
        })
    class_df = pd.DataFrame(class_metrics)
    class_df.to_csv(os.path.join(path_img, 'class_metrics.csv'),
                    index=False, encoding='utf_8_sig')

    # 3. 創建非背景類別平均指標的CSV
    non_bg_indices = list(range(1, n_classes))
    if non_bg_indices:  # 確保有非背景類別
        non_bg_metrics_dict = {
            "Metric": ["F1", "IoU", "Dice", "Precision", "Sensitivity"],
            "Value": [
                np.mean(score['F1_per_class'][non_bg_indices]),
                np.mean(score['IoU'][non_bg_indices]),
                np.mean(score['Dice_per_class'][non_bg_indices]),
                np.mean(score['Precision_per_class'][non_bg_indices]),
                np.mean(score['Sensitivity_per_class'][non_bg_indices])
            ]
        }
        non_bg_df = pd.DataFrame(non_bg_metrics_dict)
        non_bg_df.to_csv(os.path.join(
            path_img, 'non_background_metrics.csv'), index=False, encoding='utf_8_sig')

    # 4. 將所有指標合併到一個完整的CSV文件
    with open(os.path.join(path_img, 'all_metrics.csv'), 'w', newline='', encoding='utf_8_sig') as f:
        writer = csv.writer(f)

        # 寫入標題
        writer.writerow(['指標類型', '類別/指標名稱', 'F1/Dice', 'MIoU/IoU', 'Precision', 'Sensitivity/Recall',
                        'Specificity', 'Pixel_Accuracy', 'Accuracy', 'Jaccard_Score', 'FPS'])

        # 寫入總體指標
        writer.writerow([
            '總體指標',
            '所有類別',
            f"{score['F1']:.4f}",
            f"{score['MIoU']:.4f}",
            f"{score['Precision']:.4f}",
            f"{score['Sensitivity']:.4f}",
            f"{score['Specificity']:.4f}",
            f"{score['Pixel_Accuracy']:.4f}",
            f"{score['acc']:.4f}",
            f"{score['js']:.4f}",
            f"{score['fps']:.2f}"
        ])

        # 寫入各類別指標
        for class_idx in range(n_classes):
            class_name = class_names.get(
                class_idx, f"類別 {class_idx}") if class_names else f"類別 {class_idx}"
            writer.writerow([
                '各類別指標',
                class_name,
                f"{score['F1_per_class'][class_idx]:.4f}",
                f"{score['IoU'][class_idx]:.4f}",
                f"{score['Precision_per_class'][class_idx]:.4f}",
                f"{score['Sensitivity_per_class'][class_idx]:.4f}",
                f"{score['Specificity_per_class'][class_idx]:.4f}",
                '', '', '', ''  # 這些指標在類別層級沒有定義
            ])

        # 寫入非背景類別平均指標
        if non_bg_indices:
            writer.writerow([
                '非背景類別平均指標',
                '非背景類別',
                f"{np.mean(score['F1_per_class'][non_bg_indices]):.4f}",
                f"{np.mean(score['IoU'][non_bg_indices]):.4f}",
                f"{np.mean(score['Precision_per_class'][non_bg_indices]):.4f}",
                f"{np.mean(score['Sensitivity_per_class'][non_bg_indices]):.4f}",
                '', '', '', '', ''  # 部分指標可能沒有對應的非背景平均值
            ])

    print(f"所有指標已保存至 {path_img} 目錄下的CSV文件中")
    return score


def inference(test_loader, model, path_img, device, class_names=None, original_size=(3000, 4000), original_scale=0.0166):
    """
    推理函數，進行預測並保存結果，同時應用掩碼處理和產生破壞統計CSV
    使用更積極的記憶體管理策略，並處理中文問題
    """
    
    # 導入必要的庫
    import gc
    import psutil
    import matplotlib.pyplot as plt
    import os
    import glob
    import time
    from datetime import datetime
    import sys
    import locale
    
    # 設定編碼環境
    if sys.platform.startswith('win'):
        # 在Windows設定控制台編碼
        locale.setlocale(locale.LC_ALL, 'zh_TW.UTF-8')  # 台灣繁體中文
    else:
        # 在Linux/Mac設定UTF-8編碼
        locale.setlocale(locale.LC_ALL, 'zh_TW.utf8')
    
    # 設定matplotlib中文支援
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
    plt.rcParams['axes.unicode_minus'] = False  # 正確顯示負號
    plt.ioff()  # 關閉互動模式
    
    # 註冊OpenCV中文字型
    def cv2_chinese_text(img, text, position, font_size=0.7, color=(255, 255, 255), thickness=2):
        """使用PIL在OpenCV圖像上添加中文文字"""
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 將OpenCV圖像轉換為PIL圖像
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)
        
        # 獲取系統支援的字型
        try:
            if sys.platform.startswith('win'):
                font_path = 'C:\\Windows\\Fonts\\msjh.ttc'  # Windows微軟正黑體
            elif sys.platform.startswith('darwin'):
                font_path = '/System/Library/Fonts/PingFang.ttc'  # macOS
            else:
                font_path = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'  # Linux文泉驛微米黑
            font = ImageFont.truetype(font_path, int(font_size * 20))
        except:
            # 如果找不到字型，使用默認字型
            font = ImageFont.load_default()
        
        # 繪製文字
        draw.text(position, text, font=font, fill=color)
        
        # 將PIL圖像轉換回OpenCV圖像
        return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    
    # 設定模型為評估模式
    model.eval()
    total_time = 0
    total_frames = 0
    
    # 確保輸出路徑存在
    os.makedirs(path_img, exist_ok=True)
    
    # 為CSV數據創建多個小文件而不是一個大列表
    csv_temp_dir = os.path.join(path_img, 'temp_csv')
    os.makedirs(csv_temp_dir, exist_ok=True)
    
    # 每個批次保存的CSV數據行數，用於最終合併
    csv_counter = 0
    record_count = 0  # 記錄計數
    
    # 記憶體監控初始值
    initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    print(f"初始記憶體使用: {initial_memory:.2f} MB")
    
    # 使用限制記憶體的處理策略
    def process_batch(batch_idx, images, filenames):
        nonlocal total_time, total_frames, record_count, csv_counter
        
        # 將圖像移至設備
        images = images.to(device, non_blocking=True)
        
        # 模型預測
        start_time = time.time()
        with torch.cuda.amp.autocast(enabled=True):  # 使用半精度加速並減少記憶體
            predictions = model(images)
        end_time = time.time()
        
        inference_time = end_time - start_time
        total_time += inference_time
        total_frames += images.size(0)
        
        # 處理結果並釋放未使用的張量
        batch_results = []
        
        for i in range(images.size(0)):
            # 獲取檔名（確保中文檔名正確處理）
            if filenames and len(filenames) > i:
                filename = filenames[i]
                # 確保檔名是字串類型
                if not isinstance(filename, str):
                    try:
                        filename = str(filename)
                    except:
                        filename = f"image_{batch_idx}_{i}"
            else:
                filename = f"image_{batch_idx}_{i}"
            
            # 用於判斷是否有預測
            has_predictions = False
            image_results = []
            
            # 提取當前圖像的預測掩碼
            if isinstance(predictions, tuple) and len(predictions) >= 2:
                logits, masks = predictions[:2]
                if len(masks[i].shape) == 3:  # [num_classes, H, W]
                    pred_mask = torch.argmax(masks[i], dim=0).cpu().numpy()
                else:
                    pred_mask = (masks[i].cpu().numpy() > 0.5).astype(np.uint8)
            else:
                if len(predictions[i].shape) == 3:  # [C, H, W]
                    pred_mask = torch.argmax(predictions[i], dim=0).cpu().numpy()
                else:
                    pred_mask = predictions[i].cpu().numpy()
            
            # 獲取原始圖像
            original_image = images[i].cpu().detach().numpy()
            # 轉換為HWC格式用於視覺化
            if original_image.shape[0] == 3:  # CHW -> HWC
                original_image = np.transpose(original_image, (1, 2, 0))
            
            # 反標準化
            mean = np.array([0.485, 0.456, 0.406])
            std = np.array([0.229, 0.224, 0.225])
            original_image = original_image * std + mean
            original_image = np.clip(original_image, 0, 1)
            if np.max(original_image) <= 1.0:
                original_image = (original_image * 255).astype(np.uint8)
            
            # 處理每個類別
            unique_classes = np.unique(pred_mask)
            for cls in unique_classes:
                if cls == 0:  # 跳過背景類別
                    continue
                
                has_predictions = True
                
                # 獲取該類別的二值掩碼
                class_mask = (pred_mask == cls).astype(np.uint8)
                
                # 獲取類別名稱（確保中文正確處理）
                if class_names and cls in class_names:
                    class_name = class_names[cls]
                else:
                    class_name = f"類別 {cls}"
                
                # 確保class_name是字串
                if not isinstance(class_name, str):
                    try:
                        class_name = str(class_name)
                    except:
                        class_name = f"類別 {cls}"
                
                # 計算破損統計信息
                # 將掩碼擴充回原始大小
                resized_mask = cv2.resize(class_mask, (original_size[1], original_size[0]), 
                                         interpolation=cv2.INTER_NEAREST)
                
                # 找輪廓 (在擴充後的掩碼上)
                binary = (resized_mask > 0.5).astype(np.uint8) * 255
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
                
                # 為每個輪廓計算統計資訊
                for contour in contours:
                    # 創建單獨的輪廓掩碼
                    contour_mask = np.zeros_like(binary)
                    cv2.drawContours(contour_mask, [contour], 0, 255, -1)
                    
                    # 計算像素面積
                    area_pixels = np.sum(contour_mask > 0)
                    
                    # 獲取邊界框
                    rect = cv2.boundingRect(contour)
                    x, y, w, h = rect
                    
                    # 篩選過小的區域
                    min_area = 100  # 像素
                    if area_pixels < min_area:
                        continue
                    
                    # 計算實際尺寸
                    area = area_pixels * (original_scale ** 2)
                    length = h * original_scale
                    width = w * original_scale
                    
                    # 增加記錄計數
                    record_count += 1
                    
                    # 添加到臨時結果
                    image_results.append({
                        "序號": record_count,
                        "檔案名稱": filename,
                        "破壞": class_name,
                        "長": f"{length:.2f}",
                        "寬": f"{width:.2f}",
                        "面積": f"{area:.2f}"
                    })
                    
                # 釋放記憶體
                del resized_mask, binary, contours
            
            # 如果有預測結果，則保存圖像
            if has_predictions and image_results:
                batch_results.extend(image_results)
                
                # 創建彩色分割圖
                pred_colored = np.zeros((pred_mask.shape[0], pred_mask.shape[1], 3), dtype=np.uint8)
                
                # 使用固定的顏色映射
                np.random.seed(42)
                colors = {}
                for cls in unique_classes:
                    if cls == 0:
                        continue
                    if cls not in colors:
                        colors[cls] = np.random.randint(0, 255, 3)
                    pred_colored[pred_mask == cls] = colors[cls]
                
                # 疊加原圖和預測
                alpha = 0.7
                pred_overlay = original_image.copy()
                mask = pred_mask > 0
                pred_overlay[mask] = pred_overlay[mask] * (1 - alpha) + pred_colored[mask] * alpha
                
                # 使用OpenCV創建並保存圖像
                combined_img = np.hstack((original_image, pred_overlay))
                
                # 處理中文標題和圖例
                combined_img = cv2.cvtColor(combined_img, cv2.COLOR_RGB2BGR)
                
                # 添加中文文字
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     f'原始圖片: {filename}', 
                #     (50, 0),
                #     color=(0, 0, 0)
                # )
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     '預測結果疊加', 
                #     (original_image.shape[1] + 150, 0),
                #     color=(0, 0, 0)
                # )
                
                # 添加圖例（使用中文）
                legend_y = 0
                for idx, cls in enumerate(sorted([c for c in unique_classes if c > 0])):
                    # 顏色方塊
                    color = tuple(map(int, colors[cls]))
                    cv2.rectangle(combined_img, 
                                 (original_image.shape[1] + 140, legend_y + idx * 20),
                                 (original_image.shape[1] + 160, legend_y + idx * 20 + 20), 
                                 color, -1)
                    
                    # 類別名稱
                    class_name = class_names[cls] if class_names and cls in class_names else f"類別 {cls}"
                    combined_img = cv2_chinese_text(
                        combined_img, 
                        class_name, 
                        (original_image.shape[1] + 165, legend_y + idx * 20), 
                        font_size=0.8,
                        color=(0, 0, 0)
                    )
                
                # 保存圖像 - 使用安全的檔名
                safe_filename = ''.join(c if c.isalnum() or c in '._- ' else '_' for c in filename)
                save_path = os.path.join(path_img, f'test_{safe_filename}.jpg')
                cv2.imwrite(save_path, combined_img)
                
                # 釋放記憶體
                del pred_colored, pred_overlay, combined_img
            
            # 釋放記憶體
            del original_image, pred_mask
        
        # 保存批次結果到臨時CSV文件
        if batch_results:
            # 轉換為DataFrame並保存
            temp_df = pd.DataFrame(batch_results)
            # 使用批次索引作為檔名以避免中文檔名問題
            temp_csv_path = os.path.join(csv_temp_dir, f'temp_{batch_idx:06d}.csv')
            temp_df.to_csv(temp_csv_path, index=False, encoding='utf_8_sig')
            csv_counter += len(batch_results)
            
            # 釋放記憶體
            del batch_results, temp_df
        
        # 釋放記憶體
        del images, predictions
        torch.cuda.empty_cache()
    
    # 創建進度條
    infer_loop = tqdm(enumerate(test_loader), total=len(test_loader))
    
    # 處理每個批次
    for batch_idx, (images, filenames) in infer_loop:
        try:
            # 處理批次
            process_batch(batch_idx, images, filenames)
            
            # 更新進度條
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_diff = current_memory - initial_memory
            infer_loop.set_postfix(
                Progress=f"{batch_idx+1}/{len(test_loader)}",
                Records=record_count,
                Memory=f"{current_memory:.2f}MB ({memory_diff:+.2f})"
            )
            
            # 每50個批次進行一次強制清理
            if batch_idx % 50 == 0 and batch_idx > 0:
                # 強制清理
                for _ in range(3):  # 多次嘗試清理
                    gc.collect()
                    torch.cuda.empty_cache()
                    time.sleep(0.1)  # 給系統一些時間來整理記憶體
                
                # 記錄清理後的記憶體使用
                after_gc_memory = psutil.Process().memory_info().rss / 1024 / 1024
                print(f"批次 {batch_idx}: 清理後記憶體使用 {after_gc_memory:.2f} MB")
                
                # 定期保存中間結果
                if batch_idx % 500 == 0:
                    merge_temp_csvs(csv_temp_dir, path_img, f'damage_statistics_checkpoint_{batch_idx}.csv')
        
        except Exception as e:
            print(f"處理批次 {batch_idx} 時發生錯誤: {str(e)}")
            # 保存當前進度
            print("正在保存目前進度...")
            try:
                # 合併所有已處理的CSV
                merge_temp_csvs(csv_temp_dir, path_img, f'damage_statistics_error_at_{batch_idx}.csv')
            except Exception as csv_err:
                print(f"保存進度時發生錯誤: {str(csv_err)}")
            raise e
    
    # 計算性能指標
    fps = total_frames / total_time if total_time > 0 else 0
    print(f"\n推理性能指標:")
    print(f"總推理時間: {total_time:.2f} 秒")
    print(f"總幀數: {total_frames}")
    print(f"平均FPS: {fps:.2f}")
    
    # 保存性能指標
    with open(os.path.join(path_img, 'inference_performance.csv'), 'w', newline='', encoding='utf_8_sig') as f:
        writer = csv.writer(f)
        writer.writerow(['總推理時間(秒)', '總幀數', '平均FPS'])
        writer.writerow([f"{total_time:.2f}", total_frames, f"{fps:.2f}"])
    
    # 合併所有臨時CSV文件到最終CSV
    print("正在合併所有臨時CSV文件...")
    merge_temp_csvs(csv_temp_dir, path_img, 'damage_statistics.csv')
    
    # 最終記憶體使用狀況
    final_memory = psutil.Process().memory_info().rss / 1024 / 1024
    print(f"最終記憶體使用: {final_memory:.2f} MB")
    print(f"記憶體增加: {final_memory - initial_memory:.2f} MB")
    
    print(f"推理結果已保存至 {path_img} 目錄")
    print(f"破損統計已保存至 {os.path.join(path_img, 'damage_statistics.csv')}")
    
    return fps

def merge_temp_csvs(temp_dir, output_dir, output_filename):
    """合併臨時CSV文件到一個最終文件，支援中文內容"""
    all_files = glob.glob(os.path.join(temp_dir, 'temp_*.csv'))
    if not all_files:
        print("沒有找到臨時CSV文件")
        return
    
    # 排序檔案，確保按順序合併
    all_files.sort()
    
    # 讀取並合併所有CSV
    all_dfs = []
    for file in all_files:
        try:
            # 使用utf_8_sig編碼處理中文
            df = pd.read_csv(file, encoding='utf_8_sig')
            all_dfs.append(df)
        except Exception as e:
            print(f"讀取文件 {file} 時發生錯誤: {str(e)}")
    
    if all_dfs:
        # 合併所有DataFrame
        combined_df = pd.concat(all_dfs, ignore_index=True)
        # 保存合併後的CSV（使用utf_8_sig編碼處理中文）
        output_path = os.path.join(output_dir, output_filename)
        combined_df.to_csv(output_path, index=False, encoding='utf_8_sig')
        print(f"已合併 {len(all_dfs)} 個臨時CSV文件，共 {len(combined_df)} 行數據")
    else:
        print("沒有有效的CSV數據可以合併")