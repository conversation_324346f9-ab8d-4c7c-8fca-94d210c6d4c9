# Gemini's Guide to the Road AI Framework

This document provides a comprehensive guide to the Modernized Road Infrastructure AI Detection Framework. It is intended for developers and researchers working on this project.

## 🚀 Project Overview

This is an enterprise-grade AI framework for detecting road infrastructure damage. It has been significantly refactored to be a modern, unified, and configuration-driven system.

### 🏆 Core Features

*   **State-of-the-Art Models:**
    *   **Vision Mamba:** Implementation of the ICML 2024 Best Paper for high-performance, linear-complexity computer vision.
    *   **CSP_IFormer:** A powerful and original custom-designed model architecture.
    *   **Unified YOLO Inference System:** The latest and recommended engine for detection and segmentation, integrating YOLOv11 and YOLOv12.
*   **Modern Engineering Practices:**
    *   **Configuration-Driven:**  Uses `@dataclass` and YAML/JSON for flexible and easy-to-manage configurations.
    *   **Factory Pattern:** Simplifies the creation of models, datasets, and other components.
    *   **Unified Import System:** A centralized `import_helper.py` to manage all project paths and dependencies.
    *   **Unified Training System:** A `UnifiedTrainer` class provides a consistent interface for training all models.
*   **Advanced Inference Capabilities:**
    *   **SAHI (Slicing Aided Hyper Inference):**  For accurate object detection in high-resolution images.
    *   **Intelligent Filtering:** Advanced logic to reduce false positives and improve detection accuracy.
    *   **Three-View Output:** A powerful visualization tool that shows the original image, ground truth, and prediction side-by-side.

## 🗂️ Project Structure

The project is organized into a clean and modern structure:

```
/
├── road_ai_framework/      # 🌟 Main, refactored project directory
│   ├── core/               # 🔧 Core components (factories, registries)
│   ├── models/             # 🤖 AI model architectures and logic
│   ├── data/               # 🔄 Data loading and processing
│   ├── configs/            # ⚙️ Configuration files
│   ├── examples/           # 📚 Usage examples
│   └── tests/              # 🧪 Test suite
├── AI模型建構訓練驗證/       # 🔬 Legacy model development and experiments
├── 資料前處理/               # 🖼️ Data preprocessing tools (including a PyQt GUI)
├── run_unified_yolo.py     # 🚀 Main script for the Unified YOLO Inference System
└── gemini.md               # 📄 This guide
```

## ⚡ Quick Start

Follow these steps to get the Unified YOLO Inference System running.

### 1. Environment Setup

```bash
# Install core dependencies
pip install torch torchvision ultralytics
pip install opencv-python numpy matplotlib
pip install sahi albumentations

# Optional: for the GUI and advanced features
pip install PyQt6 qdarkstyle
```

### 2. Configure and Run

The main script for the Unified YOLO Inference System is `run_unified_yolo.py`. All important parameters are conveniently located at the top of this file.

1.  **Edit `run_unified_yolo.py`:**

    ```python
    # --- Key Parameters to Modify ---

    # Model and I/O paths
    segmentation_model_path = "/mnt/d/4_road_crack/best.pt"
    input_path = "/mnt/d/image/test/"
    output_path = "/mnt/d/image/output/"

    # Feature flags
    enable_sahi = False
    enable_intelligent_filtering = True
    enable_three_view_output = True

    # Class-specific configurations
    class_configs = {
        2: ["linear_crack", "縱向裂縫", [0, 0, 255], 0.2, 0.08, True],
        3: ["Alligator_crack", "龜裂", [255, 255, 0], 0.3, 0.15, True],
        # ... configure other classes as needed
    }
    ```

2.  **Run the script:**

    ```bash
    python run_unified_yolo.py
    ```

### 3. Expected Output

The script will generate the following outputs in the specified `output_path`:

*   `images/`: Contains the visual results, including the three-view comparison.
*   `reports/`: Contains detailed CSV and JSON reports with detection statistics.

## 🧠 Core Technologies

### Unified YOLO Inference System

This is the centerpiece of the framework's inference capabilities. It combines the best features of previous versions into a single, powerful, and easy-to-use system.

*   **Unified Configuration:** All parameters are managed in a single, clear location.
*   **Fine-grained Class Control:** Set confidence thresholds, colors, and names for each class independently.
*   **Advanced Filtering:** A two-step filtering process to resolve ambiguities between similar classes (e.g., `linear_crack` vs. `joint`).
*   **Enhanced Visualization:** The three-view output and customizable font settings provide clear and professional-looking results.

### Vision Mamba

The framework includes a full implementation of the Vision Mamba architecture.

*   **Linear Complexity:**  O(n) complexity makes it highly efficient for large images.
*   **State-Space Model:**  Excellent at capturing long-range dependencies in visual data.
*   **Multiple Scales:**  Available in Tiny, Small, and Base versions to suit different performance needs.

To use Vision Mamba, you can use the provided factory functions:

```python
from road_ai_framework.models.vision_mamba.vision_mamba_core import create_vision_mamba_small

model = create_vision_mamba_small(
    img_size=224,
    num_classes=5
)
```

### CSP_IFormer

This is a custom-designed architecture that combines the strengths of CSP networks and Transformers.

*   **Cross Stage Partial (CSP) Connections:** Reduce computational cost and improve feature propagation.
*   **Inception-style Transformer (IFormer) Blocks:** Capture features at multiple scales.
*   **Channel Shuffle and DropKey:**  Innovative regularization techniques to improve robustness.

Create a CSP_IFormer model using the model factory:

```python
from road_ai_framework.core.model_factory import ModelFactory

factory = ModelFactory()
model = factory.create_model({
    "model_type": "csp_iformer",
    "variant": "final_segmentation",
    "num_classes": 5,
})
```

## 🧪 Testing

The project includes a comprehensive test suite. To run the tests, execute the following command from the root directory:

```bash
python run_tests.py
```

This will run all the tests in the `tests/` and `road_ai_framework/tests` directories and provide a detailed report.