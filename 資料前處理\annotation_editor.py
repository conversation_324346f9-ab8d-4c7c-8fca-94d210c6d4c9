import os
import json
import logging
from pathlib import Path
from collections import Counter, defaultdict
import shutil
import cv2
import numpy as np


class AnnotationEditor:
    """
    LabelMe標籤處理器
    支持標籤合併、重命名和刪除功能
    """

    def __init__(self, input_dir, output_dir=None, logger=None, recursive=True):
        """
        初始化處理器

        參數:
            input_dir: 輸入目錄，包含LabelMe JSON文件
            output_dir: 輸出根目錄，None表示直接修改原文件
            logger: 日誌記錄器，如果為None則創建新的
            recursive: 是否遞迴處理子目錄
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir) if output_dir else self.input_dir
        self.logger = logger or logging.getLogger(__name__)
        self.recursive = recursive

        # 操作歷史和緩存
        self.operations = []  # 已確認的操作
        self.pending_operations = []  # 待確認的操作
        self.history_states = []  # 狀態歷史，用於回滾

        # 維護的當前標籤狀態
        self.current_labels = {}  # {文件路徑: 標籤數據}

        # 紀錄所有發現的JSON檔案路徑
        self.json_files = []

        # 初始化統計
        self.stats = {
            "total": 0,
            "processed": 0,
            "skipped": 0,
            "original_labels": {},
            "current_labels": {},  # 新增當前標籤統計
            "final_labels": {},
            "deleted_annotations": 0,
            "empty_files": 0
        }

        # 確保輸出目錄存在
        if self.output_dir != self.input_dir:
            self.output_dir.mkdir(parents=True, exist_ok=True)

    def find_json_files(self):
        """尋找所有JSON檔案，支援遞迴搜尋子目錄"""
        self.json_files = []

        if self.recursive:
            # 遞迴搜尋所有子目錄中的JSON檔案
            for root, _, files in os.walk(str(self.input_dir)):  # 轉換為字符串確保處理中文路徑
                for file in files:
                    if file.endswith('.json'):
                        self.json_files.append(Path(root) / file)
        else:
            # 只搜尋主目錄
            self.json_files = list(self.input_dir.glob("*.json"))

        self.logger.info(f"找到 {len(self.json_files)} 個JSON檔案")
        return self.json_files

    def load_data(self):
        """載入所有標籤數據到內存"""
        self.current_labels = {}

        # 先找到所有JSON檔案
        self.find_json_files()

        for annotation_file in self.json_files:
            try:
                with open(str(annotation_file), 'r', encoding='utf-8') as f:  # 轉換為字符串確保處理中文路徑
                    data = json.load(f)
                self.current_labels[str(annotation_file)] = data
            except Exception as e:
                self.logger.error(f"載入 {annotation_file.name} 失敗: {e}")

        self.logger.info(f"已載入 {len(self.current_labels)} 個標籤文件到內存")

        # 初始狀態作為恢復點
        self.save_current_state()

        # 初始化原始標籤統計
        self.preview_labels(update_stats=True)

        return len(self.current_labels)

    def save_current_state(self):
        """保存當前狀態用於可能的回滾"""
        # 深拷貝當前標籤狀態
        import copy
        state = copy.deepcopy(self.current_labels)
        self.history_states.append(state)
        self.logger.info(f"已保存當前狀態 (狀態 #{len(self.history_states)})")

    def rollback_to_previous_state(self):
        """回滾到上一個狀態"""
        if len(self.history_states) <= 1:
            self.logger.warning("沒有可回滾的歷史狀態")
            return False

        # 移除當前狀態
        self.history_states.pop()
        # 恢復到前一個狀態
        previous_state = self.history_states[-1]

        # 深拷貝避免引用問題
        import copy
        self.current_labels = copy.deepcopy(previous_state)

        self.logger.info(f"已回滾到狀態 #{len(self.history_states)}")
        return True

    def preview_labels(self, update_stats=True):
        """
        預覽目前標籤分佈

        參數:
            update_stats: 是否更新統計信息

        返回:
            標籤計數字典 {標籤: 計數}
        """
        label_counts = Counter()
        label_to_files = defaultdict(list)

        for file_path, data in self.current_labels.items():
            file_name = Path(file_path).name

            # 同時支援 annotations 和 shapes 兩種格式
            annotations = data.get("annotations", [])
            shapes = data.get("shapes", [])

            # 處理 annotations 格式
            for shape in annotations:
                label = shape.get("label", "unknown")
                label_counts[label] += 1
                label_to_files[label].append(file_name)

            # 處理 shapes 格式
            for shape in shapes:
                label = shape.get("label", "unknown")
                label_counts[label] += 1
                label_to_files[label].append(file_name)

        self.logger.info(f"當前標籤統計 ({len(label_counts)} 個不同標籤):")

        for label, count in sorted(label_counts.items(), key=lambda x: -x[1]):
            file_count = len(set(label_to_files[label]))
            self.logger.info(f"  {label}: {count} 次 (在 {file_count} 個檔案中)")

        if update_stats:
            if not self.stats["original_labels"]:
                self.stats["original_labels"] = {
                    k: v for k, v in label_counts.items()}
            self.stats["current_labels"] = {
                k: v for k, v in label_counts.items()}

        return {"label_counts": label_counts, "label_to_files": label_to_files, "total_files": len(self.current_labels)}

    def add_merge_operation(self, merge_rules):
        """
        添加標籤合併操作到待處理隊列

        參數:
            merge_rules: 合併規則字典，{目標標籤: [源標籤列表]}

        返回:
            操作ID
        """
        operation = {
            "type": "merge",
            "rules": merge_rules,
            "description": f"合併標籤: {merge_rules}"
        }

        self.pending_operations.append(operation)
        op_id = len(self.pending_operations)

        self.logger.info(f"已添加合併操作 (ID: {op_id}): {merge_rules}")

        # 立即預覽結果但不實際執行
        self._preview_operation_result(operation)

        return op_id

    def add_rename_operation(self, rename_rules):
        """
        添加標籤重命名操作到待處理隊列

        參數:
            rename_rules: 重命名規則字典，{舊標籤: 新標籤}

        返回:
            操作ID
        """
        operation = {
            "type": "rename",
            "rules": rename_rules,
            "description": f"重命名標籤: {rename_rules}"
        }

        self.pending_operations.append(operation)
        op_id = len(self.pending_operations)

        self.logger.info(f"已添加重命名操作 (ID: {op_id}): {rename_rules}")

        # 立即預覽結果但不實際執行
        self._preview_operation_result(operation)

        return op_id

    def add_delete_operation(self, labels_to_delete):
        """
        添加標籤刪除操作到待處理隊列

        參數:
            labels_to_delete: 要刪除的標籤列表

        返回:
            操作ID
        """
        operation = {
            "type": "delete",
            "labels": labels_to_delete,
            "description": f"刪除標籤: {labels_to_delete}"
        }

        self.pending_operations.append(operation)
        op_id = len(self.pending_operations)

        self.logger.info(f"已添加刪除操作 (ID: {op_id}): {labels_to_delete}")

        # 立即預覽結果但不實際執行
        self._preview_operation_result(operation)

        return op_id

    def _preview_operation_result(self, operation):
        """預覽操作結果但不實際執行"""
        import copy
        temp_labels = copy.deepcopy(self.current_labels)

        # 臨時應用操作
        if operation["type"] == "merge":
            self._apply_merge_rules(temp_labels, operation["rules"])
        elif operation["type"] == "rename":
            self._apply_rename_rules(temp_labels, operation["rules"])
        elif operation["type"] == "delete":
            self._apply_delete_labels(temp_labels, operation["labels"])

        # 計算標籤統計
        label_counts = Counter()
        for file_path, data in temp_labels.items():
            # 處理 annotations 格式
            for shape in data.get("annotations", []):
                label = shape.get("label", "unknown")
                label_counts[label] += 1

            # 處理 shapes 格式
            for shape in data.get("shapes", []):
                label = shape.get("label", "unknown")
                label_counts[label] += 1

        # 顯示預覽結果
        self.logger.info("預覽操作後的標籤統計:")
        for label, count in sorted(label_counts.items(), key=lambda x: -x[1]):
            orig_count = self.stats["current_labels"].get(label, 0)
            diff = count - orig_count
            if diff != 0:
                sign = "+" if diff > 0 else ""
                self.logger.info(
                    f"  {label}: {orig_count} → {count} ({sign}{diff})")

    def _apply_merge_rules(self, labels_data, merge_rules):
        """應用合併規則到標籤數據"""
        # 建立反向映射
        reverse_map = {}
        for target, sources in merge_rules.items():
            for source in sources:
                reverse_map[source] = target

        # 應用到所有文件
        for file_path, data in labels_data.items():
            # 處理 annotations 格式
            if "annotations" in data:
                annotations = data.get("annotations", [])
                for shape in annotations:
                    label = shape.get("label", "unknown")
                    if label in reverse_map:
                        shape["label"] = reverse_map[label]

            # 處理 shapes 格式
            if "shapes" in data:
                shapes = data.get("shapes", [])
                for shape in shapes:
                    label = shape.get("label", "unknown")
                    if label in reverse_map:
                        shape["label"] = reverse_map[label]

    def _apply_rename_rules(self, labels_data, rename_rules):
        """應用重命名規則到標籤數據"""
        # 應用到所有文件
        for file_path, data in labels_data.items():
            # 處理 annotations 格式
            if "annotations" in data:
                annotations = data.get("annotations", [])
                for shape in annotations:
                    label = shape.get("label", "unknown")
                    if label in rename_rules:
                        shape["label"] = rename_rules[label]

            # 處理 shapes 格式
            if "shapes" in data:
                shapes = data.get("shapes", [])
                for shape in shapes:
                    label = shape.get("label", "unknown")
                    if label in rename_rules:
                        shape["label"] = rename_rules[label]

    def _apply_delete_labels(self, labels_data, labels_to_delete):
        """應用刪除標籤到標籤數據"""
        labels_to_delete = set(labels_to_delete)

        # 應用到所有文件
        for file_path, data in labels_data.items():
            # 處理 annotations 格式
            if "annotations" in data:
                annotations = data.get("annotations", [])
                data["annotations"] = [s for s in annotations if s.get(
                    "label", "unknown") not in labels_to_delete]

            # 處理 shapes 格式
            if "shapes" in data:
                shapes = data.get("shapes", [])
                data["shapes"] = [s for s in shapes if s.get(
                    "label", "unknown") not in labels_to_delete]

    def remove_operation(self, operation_id):
        """
        移除待處理隊列中的操作

        參數:
            operation_id: 操作ID (1-based)

        返回:
            是否成功移除
        """
        if 1 <= operation_id <= len(self.pending_operations):
            op = self.pending_operations.pop(operation_id - 1)
            self.logger.info(f"已移除操作 {operation_id}: {op['description']}")
            return True
        else:
            self.logger.warning(f"無效的操作ID: {operation_id}")
            return False

    def list_pending_operations(self):
        """列出所有待處理的操作"""
        if not self.pending_operations:
            self.logger.info("沒有待處理的操作")
            return []

        self.logger.info("待處理操作列表:")
        for i, op in enumerate(self.pending_operations, 1):
            self.logger.info(f"  {i}: {op['type']} - {op['description']}")

        return self.pending_operations

    def clear_pending_operations(self):
        """清空所有待處理操作"""
        count = len(self.pending_operations)
        self.pending_operations = []
        self.logger.info(f"已清空 {count} 個待處理操作")
        return count

    def execute_pending_operations(self):
        """執行所有待處理的操作"""
        if not self.pending_operations:
            self.logger.info("沒有待處理的操作")
            return False

        # 保存當前狀態用於可能的回滾
        self.save_current_state()

        # 執行所有操作
        for op in self.pending_operations:
            self.logger.info(f"執行操作: {op['description']}")

            if op["type"] == "merge":
                self._apply_merge_rules(self.current_labels, op["rules"])
            elif op["type"] == "rename":
                self._apply_rename_rules(self.current_labels, op["rules"])
            elif op["type"] == "delete":
                deleted_count = 0
                empty_files = 0

                # 計算要刪除的形狀數量
                for file_path, data in self.current_labels.items():
                    # 計算 annotations 格式中要刪除的形狀
                    annotations = data.get("annotations", [])
                    delete_count_annotations = sum(
                        1 for s in annotations if s.get("label", "unknown") in op["labels"])

                    # 計算 shapes 格式中要刪除的形狀
                    shapes = data.get("shapes", [])
                    delete_count_shapes = sum(1 for s in shapes if s.get(
                        "label", "unknown") in op["labels"])

                    deleted_count += delete_count_annotations + delete_count_shapes

                # 應用刪除
                self._apply_delete_labels(self.current_labels, op["labels"])

                # 檢查空文件
                for file_path, data in self.current_labels.items():
                    # 檢查 annotations 和 shapes 是否都為空
                    annotations_empty = len(data.get("annotations", [])) == 0
                    shapes_empty = len(data.get("shapes", [])) == 0

                    # 文件中只有 annotations
                    if "annotations" in data and "shapes" not in data and annotations_empty:
                        empty_files += 1
                    # 文件中只有 shapes
                    elif "shapes" in data and "annotations" not in data and shapes_empty:
                        empty_files += 1
                    # 文件中同時有 annotations 和 shapes
                    elif "annotations" in data and "shapes" in data and annotations_empty and shapes_empty:
                        empty_files += 1

                self.stats["deleted_annotations"] += deleted_count
                self.stats["empty_files"] += empty_files

        # 將已執行的操作加入歷史
        self.operations.extend(self.pending_operations)

        # 清空待處理隊列
        count = len(self.pending_operations)
        self.pending_operations = []

        # 更新統計信息
        self.preview_labels(update_stats=True)

        self.logger.info(f"已執行 {count} 個操作")
        return True

    def save_results(self):
        """保存處理結果到文件，依照標籤類別進行分類，優先處理多類別，簡化目錄結構"""
        if self.output_dir == self.input_dir:
            self.logger.warning("輸出目錄與輸入目錄相同，將直接修改原文件")

            # 直接寫回原文件
            for file_path, data in self.current_labels.items():
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"已保存 {len(self.current_labels)} 個文件")

        else:
            # 創建多類別文件目錄
            multi_class_dir = self.output_dir / "multi_class"
            multi_class_dir.mkdir(exist_ok=True)
            # 直接將圖片和標籤檔放到多類別目錄中，而不是子目錄
            multi_class_overlap_dir = multi_class_dir / "overlap"
            multi_class_overlap_dir.mkdir(exist_ok=True)

            # 創建映射來追踪相對路徑結構
            relative_paths = {}
            for file_path in self.current_labels.keys():
                path_obj = Path(file_path)
                # 計算相對於輸入目錄的路徑
                try:
                    rel_path = path_obj.relative_to(self.input_dir)
                    relative_paths[file_path] = rel_path
                except ValueError:
                    # 如果不是子路徑，則使用檔名
                    relative_paths[file_path] = path_obj.name

            # 首先統計每個檔案的標籤類別
            file_labels = {}
            all_labels = set()
            multi_class_files = set()  # 追踪多類別檔案
            single_class_files = {}    # 按類別追踪單類別檔案

            for file_path, data in self.current_labels.items():
                labels = set()

                # 處理 annotations 格式
                for shape in data.get("annotations", []):
                    label = shape.get("label", "unknown")
                    labels.add(label)
                    all_labels.add(label)

                # 處理 shapes 格式
                for shape in data.get("shapes", []):
                    label = shape.get("label", "unknown")
                    labels.add(label)
                    all_labels.add(label)

                file_labels[file_path] = labels

                # 區分多類別和單類別檔案
                if len(labels) > 1:
                    multi_class_files.add(file_path)
                elif len(labels) == 1:
                    label = next(iter(labels))  # 獲取唯一的標籤
                    if label not in single_class_files:
                        single_class_files[label] = set()
                    single_class_files[label].add(file_path)

            # 為每個類別創建目錄
            label_dirs = {}
            for label in all_labels:
                label_dir = self.output_dir / label
                label_dir.mkdir(exist_ok=True)

                # 只創建 overlap 子目錄
                label_overlap_dir = label_dir / "overlap"
                label_overlap_dir.mkdir(exist_ok=True)

                label_dirs[label] = {
                    "main": label_dir,
                    "overlap": label_overlap_dir
                }

            # 1. 優先處理多類別檔案，只存放到多類別目錄
            multi_class_count = len(multi_class_files)
            self.logger.info(f"正在處理 {multi_class_count} 個多類別檔案...")

            for file_path in multi_class_files:
                data = self.current_labels[file_path]

                # 獲取圖像路徑
                img_path = data.get("imagePath")
                if not img_path:
                    img_path = f"{Path(file_path).stem}.jpg"

                # 嘗試使用相對路徑找到原始圖像
                src_img_path = Path(file_path).parent / img_path
                if not src_img_path.exists():
                    # 嘗試直接從輸入目錄開始尋找
                    src_img_path = self.input_dir / img_path

                # 處理JSON檔案名稱
                json_filename = Path(file_path).name

                # 處理圖片檔案名稱
                img_filename = src_img_path.name if src_img_path.exists(
                ) else f"{Path(file_path).stem}.jpg"

                # 複製圖像到多類別目錄
                if src_img_path.exists():
                    dst_img_path = multi_class_dir / img_filename
                    shutil.copy2(str(src_img_path), str(
                        dst_img_path))  # 轉換為字符串確保處理中文路徑

                # 保存JSON到多類別目錄
                output_json = multi_class_dir / json_filename
                with open(str(output_json), 'w', encoding='utf-8') as f:  # 轉換為字符串確保處理中文路徑
                    json.dump(data, f, ensure_ascii=False, indent=2)

                # 生成疊加圖
                if src_img_path.exists():
                    overlap_img = self._generate_overlap_image(
                        src_img_path, data)
                    if overlap_img is not None:
                        overlap_path = multi_class_overlap_dir / \
                            f"overlap_{img_filename}"
                        # 使用 imencode 和 tofile 處理中文路徑
                        _, img_ext = os.path.splitext(img_filename)
                        if not img_ext:
                            img_ext = ".jpg"  # 設置默認擴展名
                        is_success, im_buf_arr = cv2.imencode(
                            img_ext, overlap_img)
                        if is_success:
                            im_buf_arr.tofile(str(overlap_path))
                        else:
                            self.logger.error(f"無法保存疊加圖: {overlap_path}")

            # 2. 處理單類別檔案，只處理那些不是多類別的文件
            # 確保單類別文件集合中不包含多類別文件
            for label in single_class_files:
                single_class_files[label] = single_class_files[label] - \
                    multi_class_files

            single_class_total = sum(len(files)
                                     for files in single_class_files.values())
            self.logger.info(f"正在處理 {single_class_total} 個單類別檔案...")

            for label, files in single_class_files.items():
                self.logger.info(f"  處理 {len(files)} 個 '{label}' 類別檔案")

                for file_path in files:
                    # 再次確認這不是多類別文件
                    if file_path in multi_class_files:
                        continue

                    data = self.current_labels[file_path]

                    # 獲取圖像路徑
                    img_path = data.get("imagePath")
                    if not img_path:
                        img_path = f"{Path(file_path).stem}.jpg"

                    # 嘗試使用相對路徑找到原始圖像
                    src_img_path = Path(file_path).parent / img_path
                    if not src_img_path.exists():
                        # 嘗試直接從輸入目錄開始尋找
                        src_img_path = self.input_dir / img_path

                    # 處理JSON檔案名稱
                    json_filename = Path(file_path).name

                    # 處理圖片檔案名稱
                    img_filename = src_img_path.name if src_img_path.exists(
                    ) else f"{Path(file_path).stem}.jpg"

                    # 複製圖像到標籤主目錄（不是子目錄）
                    if src_img_path.exists():
                        dst_img_path = label_dirs[label]["main"] / img_filename
                        shutil.copy2(src_img_path, dst_img_path)

                    # 保存JSON到標籤主目錄（不是子目錄）
                    output_json = label_dirs[label]["main"] / json_filename
                    with open(output_json, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)

                    # 生成疊加圖到 overlap 子目錄
                    if src_img_path.exists():
                        overlap_img = self._generate_overlap_image(
                            src_img_path, data)
                        if overlap_img is not None:
                            overlap_path = label_dirs[label]["overlap"] / \
                                f"overlap_{img_filename}"
                            cv2.imwrite(str(overlap_path), overlap_img)

            # 輸出保存結果統計
            self.logger.info(f"總共處理 {len(self.current_labels)} 個檔案")

            # 顯示多類別檔案數量
            self.logger.info(
                f"多類別檔案: {multi_class_count} 個（僅存放於multi_class目錄）")

            # 顯示每個類別的檔案數量（不包含多類別檔案）
            cleaned_label_file_counts = Counter()
            for label, files in single_class_files.items():
                cleaned_label_file_counts[label] = len(files)

            self.logger.info("各單類別檔案數量（不含多類別檔案）:")
            for label, count in sorted(cleaned_label_file_counts.items(), key=lambda x: -x[1]):
                self.logger.info(f"  {label}: {count} 個")

        # 更新最終標籤統計
        final_label_counts = Counter()
        for file_path, data in self.current_labels.items():
            # 處理 annotations 格式
            for shape in data.get("annotations", []):
                label = shape.get("label", "unknown")
                final_label_counts[label] += 1

            # 處理 shapes 格式
            for shape in data.get("shapes", []):
                label = shape.get("label", "unknown")
                final_label_counts[label] += 1

        self.stats["final_labels"] = {
            k: v for k, v in final_label_counts.items()}

        self.logger.info("處理完成，標籤統計變化:")
        original = self.stats["original_labels"]
        final = self.stats["final_labels"]

        all_labels = sorted(set(list(original.keys()) + list(final.keys())))

        for label in all_labels:
            orig_count = original.get(label, 0)
            final_count = final.get(label, 0)
            diff = final_count - orig_count

            if diff == 0 and orig_count > 0:
                self.logger.info(f"  {label}: {orig_count} (unchanged)")
            elif orig_count == 0:
                self.logger.info(f"  + {label}: {final_count} (new)")
            elif final_count == 0:
                self.logger.info(f"  - {label}: {orig_count} → 0 (removed)")
            else:
                sign = "+" if diff > 0 else ""
                self.logger.info(
                    f"  {label}: {orig_count} → {final_count} ({sign}{diff})")

        return self.stats

    def interactive_session(self):
        """開始互動式操作會話"""
        # 載入數據
        if len(self.current_labels) == 0:
            self.load_data()

        # 顯示當前標籤統計
        self.preview_labels()

        # 互動式操作循環
        while True:
            print("\n=== LabelMe標籤處理器互動式會話 ===")
            print("1. 查看當前標籤統計")
            print("2. 添加標籤合併操作")
            print("3. 添加標籤重命名操作")
            print("4. 添加標籤刪除操作")
            print("5. 列出待處理操作")
            print("6. 移除待處理操作")
            print("7. 清空待處理操作")
            print("8. 執行所有待處理操作")
            print("9. 回滾到上一個狀態")
            print("10. 保存結果")
            print("0. 退出")

            choice = input("\n請輸入選項 (0-10): ")

            try:
                if choice == "1":
                    # 查看當前標籤統計
                    if self.pending_operations:
                        # 有待處理操作時，顯示預覽結果
                        print("注意：以下是包含待處理操作預覽效果的標籤統計")
                        # 臨時應用所有待處理操作
                        import copy
                        temp_labels = copy.deepcopy(self.current_labels)

                        for op in self.pending_operations:
                            if op["type"] == "merge":
                                self._apply_merge_rules(
                                    temp_labels, op["rules"])
                            elif op["type"] == "rename":
                                self._apply_rename_rules(
                                    temp_labels, op["rules"])
                            elif op["type"] == "delete":
                                self._apply_delete_labels(
                                    temp_labels, op["labels"])

                        # 計算並顯示預覽標籤統計
                        label_counts = Counter()
                        label_to_files = defaultdict(list)

                        for file_path, data in temp_labels.items():
                            file_name = Path(file_path).name

                            # 處理 annotations 格式
                            for shape in data.get("annotations", []):
                                label = shape.get("label", "unknown")
                                label_counts[label] += 1
                                label_to_files[label].append(file_name)

                            # 處理 shapes 格式
                            for shape in data.get("shapes", []):
                                label = shape.get("label", "unknown")
                                label_counts[label] += 1
                                label_to_files[label].append(file_name)

                        self.logger.info(
                            f"預覽操作後的標籤統計 ({len(label_counts)} 個不同標籤):")

                        for label, count in sorted(label_counts.items(), key=lambda x: -x[1]):
                            file_count = len(set(label_to_files[label]))
                            self.logger.info(
                                f"  {label}: {count} 次 (在 {file_count} 個檔案中)")
                    else:
                        # 沒有待處理操作，直接顯示當前標籤
                        self.preview_labels()

                elif choice == "2":
                    # 添加標籤合併操作
                    rule_str = input(
                        "請輸入合併規則 (格式: 目標標籤:源標籤1,源標籤2;目標標籤2:源標籤3): ")
                    merge_rules = {}

                    for pair in rule_str.split(";"):
                        if ":" in pair:
                            target, sources = pair.split(":")
                            source_labels = [s.strip()
                                             for s in sources.split(",")]
                            merge_rules[target.strip()] = source_labels

                    if merge_rules:
                        self.add_merge_operation(merge_rules)
                    else:
                        print("無效的規則格式")

                elif choice == "3":
                    # 添加標籤重命名操作
                    rule_str = input("請輸入重命名規則 (格式: 舊標籤1:新標籤1;舊標籤2:新標籤2): ")
                    rename_rules = self.parse_rule_string(rule_str)

                    if rename_rules:
                        self.add_rename_operation(rename_rules)
                    else:
                        print("無效的規則格式")

                elif choice == "4":
                    # 添加標籤刪除操作
                    labels_str = input("請輸入要刪除的標籤 (用逗號分隔): ")
                    labels_to_delete = [
                        label.strip() for label in labels_str.split(",") if label.strip()]

                    if labels_to_delete:
                        self.add_delete_operation(labels_to_delete)
                    else:
                        print("無效的標籤列表")

                elif choice == "5":
                    # 列出待處理操作
                    ops = self.list_pending_operations()
                    if not ops:
                        print("目前沒有待處理的操作")

                elif choice == "6":
                    # 移除待處理操作
                    self.list_pending_operations()
                    op_id = input("請輸入要移除的操作ID: ")
                    try:
                        op_id = int(op_id)
                        if self.remove_operation(op_id):
                            print(f"已移除操作 {op_id}")
                        else:
                            print(f"移除操作失敗，無效的ID: {op_id}")
                    except ValueError:
                        print("請輸入有效的數字ID")

                elif choice == "7":
                    # 清空待處理操作
                    confirm = input("確定要清空所有待處理操作嗎? (y/n): ")
                    if confirm.lower() == 'y':
                        count = self.clear_pending_operations()
                        print(f"已清空 {count} 個待處理操作")

                elif choice == "8":
                    # 執行所有待處理操作
                    if not self.pending_operations:
                        print("沒有待處理的操作")
                        continue

                    print("以下操作將被執行:")
                    self.list_pending_operations()
                    confirm = input("確定要執行這些操作嗎? (y/n): ")

                    if confirm.lower() == 'y':
                        if self.execute_pending_operations():
                            print("所有操作已執行完成")
                            # 顯示當前標籤統計
                            self.preview_labels()

                elif choice == "9":
                    # 回滾到上一個狀態
                    confirm = input("確定要回滾到上一個狀態嗎? (y/n): ")
                    if confirm.lower() == 'y':
                        if self.rollback_to_previous_state():
                            print("已回滾到上一個狀態")
                            # 顯示當前標籤統計
                            self.preview_labels()
                        else:
                            print("無法回滾，沒有之前的狀態")

                elif choice == "10":
                    # 保存結果
                    confirm = input("確定要保存當前處理結果嗎? (y/n): ")
                    if confirm.lower() == 'y':
                        self.save_results()
                        print("處理結果已保存")

                elif choice == "0":
                    # 退出
                    if self.pending_operations:
                        print(
                            f"注意: 還有 {len(self.pending_operations)} 個待處理操作未執行")
                        confirm = input("確定要退出嗎? (y/n): ")
                        if confirm.lower() != 'y':
                            continue

                    print("退出互動式會話")
                    break

                else:
                    print("無效的選項，請重新輸入")

            except Exception as e:
                print(f"操作出錯: {e}")
                self.logger.error(f"互動式操作出錯: {e}")

    def parse_rule_string(self, rule_string):
        """
        解析規則字符串

        參數:
            rule_string: 格式如 "oldLabel1,oldLabel2:newLabel;oldLabel3:anotherLabel"

        返回:
            解析後的規則字典
        """
        if not rule_string or not rule_string.strip():
            return {}

        result = {}

        try:
            groups = rule_string.split(";")
            for group in groups:
                group = group.strip()
                if not group:
                    continue

                parts = group.split(":")
                if len(parts) != 2:
                    self.logger.warning(f"無效的規則格式: {group}")
                    continue

                old_part, new_label = parts
                old_labels = [label.strip() for label in old_part.split(",")]
                new_label = new_label.strip()

                for old_label in old_labels:
                    if not old_label:
                        continue
                    result[old_label] = new_label
        except Exception as e:
            self.logger.error(f"解析規則字符串時出錯: {e}")

        return result

    def _generate_overlap_image(self, img_path, json_data, label_filter=None):
        """
        根據標籤數據生成疊加圖

        參數:
            img_path: 圖片路徑
            json_data: 標籤JSON數據
            label_filter: 只顯示特定標籤，None表示顯示所有標籤

        返回:
            疊加後的圖像
        """
        # 檢查圖片是否存在
        if not Path(img_path).exists():
            self.logger.warning(f"圖片不存在: {img_path}")
            return None

        try:
            # 使用 np.fromfile 和 cv2.imdecode 處理中文路徑圖片
            img_array = np.fromfile(str(img_path), dtype=np.uint8)
            image = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            if image is None:
                self.logger.warning(f"無法讀取圖片: {img_path}")
                return None

            # 創建用於繪製的複製圖像
            overlay = image.copy()

            # 設置透明度
            alpha = 0.4  # 填充透明度

            # 為不同標籤生成不同顏色 (BGR格式)
            label_colors = {}
            all_labels = set()

            # 從 annotations 和 shapes 中收集所有標籤
            for shape in json_data.get("annotations", []):
                all_labels.add(shape.get("label", "unknown"))

            for shape in json_data.get("shapes", []):
                all_labels.add(shape.get("label", "unknown"))

            # 為每個標籤分配固定顏色
            color_list = [
                (0, 0, 255),    # 紅色
                (0, 255, 0),    # 綠色
                (255, 0, 0),    # 藍色
                (0, 255, 255),  # 黃色
                (255, 0, 255),  # 紫色
                (255, 255, 0),  # 青色
                (128, 0, 0),    # 深藍色
                (0, 128, 0),    # 深綠色
                (0, 0, 128),    # 深紅色
                (128, 128, 0),  # 深青色
                (128, 0, 128),  # 深紫色
                (0, 128, 128)   # 深黃色
            ]

            for i, label in enumerate(sorted(all_labels)):
                label_colors[label] = color_list[i % len(color_list)]

            # 繪製透明度不同的圖例
            if len(all_labels) > 0:
                legend_y = 30
                legend_x = 10
                for label in sorted(all_labels):
                    if label_filter is not None and label != label_filter:
                        continue
                    color = label_colors[label]
                    # 使用粗白字背景加彩色細字前景，讓文字在任何背景下都清晰
                    cv2.putText(overlay, label, (legend_x, legend_y),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                    cv2.putText(overlay, label, (legend_x, legend_y),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 1)
                    legend_y += 25

            # 繪製 annotations 中的形狀
            for shape in json_data.get("annotations", []):
                label = shape.get("label", "unknown")

                # 如果指定了標籤過濾，則只顯示指定標籤
                if label_filter is not None and label != label_filter:
                    continue

                color = label_colors[label]

                # 獲取座標
                points = shape.get("coordinates", [])
                if not points:
                    continue

                # 繪製多邊形
                if len(points) > 2:
                    # 確保座標是整數
                    try:
                        pts = np.array([[int(float(p[0])), int(float(p[1]))]
                                       for p in points], np.int32)
                        pts = pts.reshape((-1, 1, 2))

                        # 創建蒙版用於半透明填充
                        mask = np.zeros_like(image)
                        cv2.fillPoly(mask, [pts], color)
                        cv2.addWeighted(mask, alpha, overlay, 1, 0, overlay)

                        # 繪製邊界
                        cv2.polylines(overlay, [pts], True, color, 2)

                        # 在多邊形中心添加標籤文字
                        M = cv2.moments(pts)
                        if M['m00'] != 0:
                            cx = int(M['m10'] / M['m00'])
                            cy = int(M['m01'] / M['m00'])
                            # 繪製清晰可見的標籤
                            cv2.putText(
                                overlay, label, (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                            cv2.putText(overlay, label, (cx, cy),
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製多邊形時出錯: {e}, 座標點: {points}")

                # 繪製線條
                elif len(points) == 2:
                    try:
                        pt1 = (int(float(points[0][0])),
                               int(float(points[0][1])))
                        pt2 = (int(float(points[1][0])),
                               int(float(points[1][1])))

                        # 加粗線條以突顯
                        cv2.line(overlay, pt1, pt2, color, 3)

                        # 在線條中心添加標籤文字
                        cx = (pt1[0] + pt2[0]) // 2
                        cy = (pt1[1] + pt2[1]) // 2
                        # 繪製清晰可見的標籤
                        cv2.putText(
                            overlay, label, (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                        cv2.putText(overlay, label, (cx, cy),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製線條時出錯: {e}, 座標點: {points}")

                # 繪製點
                elif len(points) == 1:
                    try:
                        pt = (int(float(points[0][0])),
                              int(float(points[0][1])))
                        # 繪製更大的點以突顯
                        cv2.circle(overlay, pt, 7, color, -1)
                        # 繪製清晰可見的標籤
                        cv2.putText(
                            overlay, label, (pt[0] + 10, pt[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                        cv2.putText(
                            overlay, label, (pt[0] + 10, pt[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製點時出錯: {e}, 座標點: {points}")

            # 繪製 shapes 中的形狀
            for shape in json_data.get("shapes", []):
                label = shape.get("label", "unknown")

                # 如果指定了標籤過濾，則只顯示指定標籤
                if label_filter is not None and label != label_filter:
                    continue

                color = label_colors[label]
                shape_type = shape.get("shape_type", "polygon")

                # 獲取座標
                points = shape.get("points", [])
                if not points:
                    continue

                # 繪製多邊形
                if shape_type == "polygon" and len(points) > 2:
                    try:
                        # 確保座標是整數
                        pts = np.array([[int(float(p[0])), int(float(p[1]))]
                                       for p in points], np.int32)
                        pts = pts.reshape((-1, 1, 2))

                        # 創建蒙版用於半透明填充
                        mask = np.zeros_like(image)
                        cv2.fillPoly(mask, [pts], color)
                        cv2.addWeighted(mask, alpha, overlay, 1, 0, overlay)

                        # 繪製邊界
                        cv2.polylines(overlay, [pts], True, color, 2)

                        # 在多邊形中心添加標籤文字
                        M = cv2.moments(pts)
                        if M['m00'] != 0:
                            cx = int(M['m10'] / M['m00'])
                            cy = int(M['m01'] / M['m00'])
                            # 繪製清晰可見的標籤
                            cv2.putText(
                                overlay, label, (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                            cv2.putText(overlay, label, (cx, cy),
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製多邊形時出錯: {e}, 座標點: {points}")

                # 繪製矩形
                elif shape_type == "rectangle" and len(points) == 2:
                    try:
                        pt1 = (int(float(points[0][0])),
                               int(float(points[0][1])))
                        pt2 = (int(float(points[1][0])),
                               int(float(points[1][1])))

                        # 創建蒙版用於半透明填充
                        mask = np.zeros_like(image)
                        cv2.rectangle(mask, pt1, pt2, color, -1)
                        cv2.addWeighted(mask, alpha, overlay, 1, 0, overlay)

                        # 繪製邊界
                        cv2.rectangle(overlay, pt1, pt2, color, 2)

                        # 在矩形左上角添加標籤文字
                        text_x = min(pt1[0], pt2[0])
                        text_y = min(pt1[1], pt2[1]) - 5
                        if text_y < 15:  # 如果太靠近頂部，則放在底部
                            text_y = max(pt1[1], pt2[1]) + 20

                        # 繪製清晰可見的標籤
                        cv2.putText(overlay, label, (text_x, text_y),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                        cv2.putText(overlay, label, (text_x, text_y),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製矩形時出錯: {e}, 座標點: {points}")

                # 繪製線條
                elif shape_type == "line" and len(points) == 2:
                    try:
                        pt1 = (int(float(points[0][0])),
                               int(float(points[0][1])))
                        pt2 = (int(float(points[1][0])),
                               int(float(points[1][1])))

                        # 加粗線條以突顯
                        cv2.line(overlay, pt1, pt2, color, 3)

                        # 在線條中心添加標籤文字
                        cx = (pt1[0] + pt2[0]) // 2
                        cy = (pt1[1] + pt2[1]) // 2
                        # 繪製清晰可見的標籤
                        cv2.putText(
                            overlay, label, (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                        cv2.putText(overlay, label, (cx, cy),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製線條時出錯: {e}, 座標點: {points}")

                # 繪製點
                elif shape_type == "point" and len(points) == 1:
                    try:
                        pt = (int(float(points[0][0])),
                              int(float(points[0][1])))
                        # 繪製更大的點以突顯
                        cv2.circle(overlay, pt, 7, color, -1)
                        # 繪製清晰可見的標籤
                        cv2.putText(
                            overlay, label, (pt[0] + 10, pt[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                        cv2.putText(
                            overlay, label, (pt[0] + 10, pt[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製點時出錯: {e}, 座標點: {points}")

                # 繪製圓形
                elif shape_type == "circle" and len(points) == 2:
                    try:
                        center = (int(float(points[0][0])), int(
                            float(points[0][1])))
                        edge = (int(float(points[1][0])),
                                int(float(points[1][1])))
                        radius = int(
                            np.sqrt(((center[0]-edge[0])**2) + ((center[1]-edge[1])**2)))

                        # 創建蒙版用於半透明填充
                        mask = np.zeros_like(image)
                        cv2.circle(mask, center, radius, color, -1)
                        cv2.addWeighted(mask, alpha, overlay, 1, 0, overlay)

                        # 繪製邊界
                        cv2.circle(overlay, center, radius, color, 2)

                        # 在圓形中心添加標籤文字
                        # 繪製清晰可見的標籤
                        cv2.putText(
                            overlay, label, center, cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                        cv2.putText(overlay, label, center,
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
                    except Exception as e:
                        self.logger.error(f"繪製圓形時出錯: {e}, 座標點: {points}")

            # 特殊處理 expansion_joint 和 joint 標籤，確保它們被明顯標示出來
            expansion_joint_found = False
            joint_found = False

            # 檢查是否有 metal 或 expansion_joint 標籤並記錄它們的座標
            for shape_list in [json_data.get("annotations", []), json_data.get("shapes", [])]:
                for shape in shape_list:
                    label = shape.get("label", "unknown")

                    if label == "expansion_joint" or label == "joint":
                        color = label_colors[label]
                        points = None

                        if "coordinates" in shape:
                            points = shape.get("coordinates", [])
                        elif "points" in shape:
                            points = shape.get("points", [])

                        if points and len(points) > 0:
                            # 確保座標是有效的
                            try:
                                if len(points) == 1:  # 點
                                    pt = (int(float(points[0][0])), int(
                                        float(points[0][1])))
                                    # 繪製大而醒目的圓圈
                                    cv2.circle(overlay, pt, 15, color, -1)
                                    # 標籤文字放在點的上方，加大字體
                                    cv2.putText(
                                        overlay, label, (pt[0] - 40, pt[1] - 20), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 3)
                                    cv2.putText(
                                        overlay, label, (pt[0] - 40, pt[1] - 20), cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 1)

                                    if label == "expansion_joint":
                                        expansion_joint_found = True
                                    elif label == "joint":
                                        joint_found = True

                                elif len(points) == 2:  # 線或矩形
                                    pt1 = (int(float(points[0][0])), int(
                                        float(points[0][1])))
                                    pt2 = (int(float(points[1][0])), int(
                                        float(points[1][1])))
                                    # 畫一條加粗的線
                                    cv2.line(overlay, pt1, pt2,
                                             color, 5)  # 加粗至5像素
                                    # 在線條中心加標籤
                                    cx = (pt1[0] + pt2[0]) // 2
                                    cy = (pt1[1] + pt2[1]) // 2
                                    cv2.putText(
                                        overlay, label, (cx, cy - 10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 3)
                                    cv2.putText(
                                        overlay, label, (cx, cy - 10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 1)

                                    if label == "expansion_joint":
                                        expansion_joint_found = True
                                    elif label == "joint":
                                        joint_found = True

                                elif len(points) > 2:  # 多邊形
                                    pts = np.array(
                                        [[int(float(p[0])), int(float(p[1]))] for p in points], np.int32)
                                    pts = pts.reshape((-1, 1, 2))
                                    # 填充多邊形
                                    mask = np.zeros_like(image)
                                    cv2.fillPoly(mask, [pts], color)
                                    cv2.addWeighted(
                                        mask, 0.6, overlay, 1, 0, overlay)  # 增加透明度
                                    # 繪製加粗邊界
                                    cv2.polylines(
                                        overlay, [pts], True, color, 3)
                                    # 在多邊形中心添加標籤
                                    M = cv2.moments(pts)
                                    if M['m00'] != 0:
                                        cx = int(M['m10'] / M['m00'])
                                        cy = int(M['m01'] / M['m00'])
                                        cv2.putText(
                                            overlay, label, (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 3)
                                        cv2.putText(
                                            overlay, label, (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 1)

                                    if label == "expansion_joint":
                                        expansion_joint_found = True
                                    elif label == "joint":
                                        joint_found = True
                            except Exception as e:
                                self.logger.error(
                                    f"特殊處理標籤 {label} 時出錯: {e}, 座標點: {points}")

            # 如果沒有找到這些特殊標籤但它們在標籤列表中，則在左上角提示
            if "expansion_joint" in all_labels and not expansion_joint_found:
                cv2.putText(overlay, "注意: expansion_joint 標籤存在但無法顯示", (10, image.shape[0] - 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            if "joint" in all_labels and not joint_found:
                cv2.putText(overlay, "注意: joint 標籤存在但無法顯示", (10, image.shape[0] - 60),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            return overlay

        except Exception as e:
            self.logger.error(f"生成疊加圖時出錯: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None


# 如果作為獨立腳本運行
if __name__ == "__main__":
    import argparse

    # 設置命令行參數
    parser = argparse.ArgumentParser(description="LabelMe標籤處理工具")
    parser.add_argument("--input", required=True, help="輸入目錄")
    parser.add_argument("--output", help="輸出目錄，默認與輸入相同")
    parser.add_argument("--preview", action="store_true", help="僅預覽標籤統計")
    parser.add_argument(
        "--merge", help="合併規則，格式為 'target:old1,old2;target2:old3'")
    parser.add_argument("--rename", help="重命名規則，格式為 'old1:new1;old2:new2'")
    parser.add_argument("--delete", help="要刪除的標籤，格式為 'label1,label2,label3'")
    parser.add_argument("--interactive", action="store_true", help="啟動互動式會話")
    parser.add_argument("--no-recursive", action="store_true", help="不遞迴處理子目錄")

    args = parser.parse_args()

    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("labelme_processor.log",
                                mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("AnnotationEditor")

    # 創建處理器
    processor = AnnotationEditor(
        input_dir=args.input,
        output_dir=args.output,
        logger=logger,
        recursive=not args.no_recursive  # 默認啟用遞迴處理，除非指定 --no-recursive
    )

    # 載入數據
    processor.load_data()

    # 互動式模式
    if args.interactive:
        processor.interactive_session()
    # 自動處理模式
    else:
        # 預覽原始標籤
        processor.preview_labels()

        # 添加操作到隊列
        operations_added = False

        # 添加合併標籤操作
        if args.merge:
            merge_rules = {}
            for pair in args.merge.split(";"):
                if ":" in pair:
                    target, sources = pair.split(":")
                    source_labels = [s.strip() for s in sources.split(",")]
                    merge_rules[target.strip()] = source_labels

            if merge_rules:
                processor.add_merge_operation(merge_rules)
                operations_added = True

        # 添加重命名標籤操作
        if args.rename:
            rename_rules = processor.parse_rule_string(args.rename)
            if rename_rules:
                processor.add_rename_operation(rename_rules)
                operations_added = True

        # 添加刪除標籤操作
        if args.delete:
            labels_to_delete = [label.strip()
                                for label in args.delete.split(",") if label.strip()]
            if labels_to_delete:
                processor.add_delete_operation(labels_to_delete)
                operations_added = True

        # 如果只是預覽
        if args.preview:
            logger.info("操作預覽模式，不會執行任何實際操作")
            if operations_added:
                processor.list_pending_operations()
        # 否則執行操作
        elif operations_added:
            logger.info("執行操作中...")
            processor.execute_pending_operations()
            processor.save_results()
            logger.info("處理完成")
        else:
            # 如果沒有添加任何操作也沒有預覽標記，則顯示幫助
            if not operations_added and not args.preview:
                parser.print_help()
