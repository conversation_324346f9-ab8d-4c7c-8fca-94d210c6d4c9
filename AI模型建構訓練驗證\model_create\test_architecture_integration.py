"""
架構整合測試腳本 (簡化版，不依賴PyTorch)

驗證所有新架構的整合邏輯：
- 檢查文件結構
- 驗證導入邏輯
- 測試配置系統
- 檢查工廠模式
"""

import os
import sys
import importlib.util
from pathlib import Path
from typing import Dict, List, Any
import traceback


class ArchitectureIntegrationTester:
    """架構整合測試器 (不依賴PyTorch)"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results = {}
        self.failed_tests = []
        
        print(f"Project root: {self.project_root}")
    
    def run_integration_test(self):
        """運行整合測試"""
        
        print("Starting Architecture Integration Test")
        print("=" * 60)
        
        # 測試文件結構
        self.test_file_structure()
        
        # 測試CSP_IFormer變體文件
        self.test_csp_iformer_files()
        
        # 測試SegMAN變體文件
        self.test_segman_files()
        
        # 測試核心系統文件
        self.test_core_system_files()
        
        # 測試導入邏輯
        self.test_import_logic()
        
        # 生成測試報告
        self.generate_test_report()
    
    def test_file_structure(self):
        """測試文件結構"""
        
        print("\n1. Testing File Structure")
        print("-" * 40)
        
        # 檢查關鍵目錄
        required_dirs = [
            'core',
            'encoder/VIT',
            'full_model/segman'
        ]
        
        structure_results = {}
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            exists = full_path.exists() and full_path.is_dir()
            
            if exists:
                print(f"  ✓ {dir_path}")
                structure_results[dir_path] = {'status': 'exists', 'path': str(full_path)}
            else:
                print(f"  ✗ {dir_path} (missing)")
                structure_results[dir_path] = {'status': 'missing', 'path': str(full_path)}
                self.failed_tests.append(f'structure_{dir_path}')
        
        self.test_results['file_structure'] = structure_results
    
    def test_csp_iformer_files(self):
        """測試CSP_IFormer變體文件"""
        
        print("\n2. Testing CSP_IFormer Variant Files")
        print("-" * 40)
        
        # 檢查CSP_IFormer 2024變體文件
        csp_files = [
            'encoder/VIT/CSP_IFormer_v2024_efficient.py',
            'encoder/VIT/CSP_IFormer_v2024_mamba.py',
            'encoder/VIT/CSP_IFormer_v2024_enhanced.py',
            'encoder/VIT/CSP_IFormer_final_SegMode.py'  # 原始版本
        ]
        
        csp_results = {}
        
        for file_path in csp_files:
            full_path = self.project_root / file_path
            
            if full_path.exists():
                # 檢查文件大小和基本內容
                file_size = full_path.stat().st_size
                
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = len(content.split('\n'))
                        
                        # 檢查關鍵類別和函數
                        has_main_class = any(keyword in content for keyword in [
                            'class CSP_IFormer', 'class SegMAN', 'class Efficient', 'class Enhanced'
                        ])
                        has_model_functions = any(keyword in content for keyword in [
                            'def forward', 'def __init__', 'nn.Module'
                        ])
                        
                    print(f"  ✓ {file_path} ({file_size} bytes, {lines} lines)")
                    csp_results[file_path] = {
                        'status': 'exists',
                        'size_bytes': file_size,
                        'lines': lines,
                        'has_main_class': has_main_class,
                        'has_model_functions': has_model_functions
                    }
                    
                except Exception as e:
                    print(f"  ! {file_path} (read error: {e})")
                    csp_results[file_path] = {'status': 'read_error', 'error': str(e)}
                    
            else:
                print(f"  ✗ {file_path} (missing)")
                csp_results[file_path] = {'status': 'missing'}
                self.failed_tests.append(f'csp_file_{file_path.replace("/", "_")}')
        
        self.test_results['csp_iformer_files'] = csp_results
    
    def test_segman_files(self):
        """測試SegMAN變體文件"""
        
        print("\n3. Testing SegMAN Variant Files")
        print("-" * 40)
        
        segman_files = [
            'full_model/segman/segman_encoder_mambavision.py',
            'full_model/segman/segman_encoder_efficient.py',
            'full_model/segman/segman_encoder_enhanced.py',
            'full_model/segman/segman_factory.py'
        ]
        
        segman_results = {}
        
        for file_path in segman_files:
            full_path = self.project_root / file_path
            
            if full_path.exists():
                file_size = full_path.stat().st_size
                
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = len(content.split('\n'))
                        
                        # 檢查SegMAN特定內容
                        has_segman_class = 'SegMAN' in content
                        has_factory_pattern = 'Factory' in content or 'create_' in content
                        has_mamba_features = 'Mamba' in content or 'SSM' in content
                        
                    print(f"  ✓ {file_path} ({file_size} bytes, {lines} lines)")
                    segman_results[file_path] = {
                        'status': 'exists',
                        'size_bytes': file_size,
                        'lines': lines,
                        'has_segman_class': has_segman_class,
                        'has_factory_pattern': has_factory_pattern,
                        'has_mamba_features': has_mamba_features
                    }
                    
                except Exception as e:
                    print(f"  ! {file_path} (read error: {e})")
                    segman_results[file_path] = {'status': 'read_error', 'error': str(e)}
                    
            else:
                print(f"  ✗ {file_path} (missing)")
                segman_results[file_path] = {'status': 'missing'}
                self.failed_tests.append(f'segman_file_{file_path.replace("/", "_")}')
        
        self.test_results['segman_files'] = segman_results
    
    def test_core_system_files(self):
        """測試核心系統文件"""
        
        print("\n4. Testing Core System Files")
        print("-" * 40)
        
        core_files = [
            'core/enhanced_factory.py',
            'core/unified_registry.py',
            'core/model_factory.py',
            'core/registry.py'
        ]
        
        core_results = {}
        
        for file_path in core_files:
            full_path = self.project_root / file_path
            
            if full_path.exists():
                file_size = full_path.stat().st_size
                
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = len(content.split('\n'))
                        
                        # 檢查核心功能
                        has_factory_class = 'Factory' in content
                        has_registry_pattern = 'Registry' in content or 'register_' in content
                        has_enhanced_features = 'Enhanced' in content or 'Unified' in content
                        
                    print(f"  ✓ {file_path} ({file_size} bytes, {lines} lines)")
                    core_results[file_path] = {
                        'status': 'exists',
                        'size_bytes': file_size,
                        'lines': lines,
                        'has_factory_class': has_factory_class,
                        'has_registry_pattern': has_registry_pattern,
                        'has_enhanced_features': has_enhanced_features
                    }
                    
                except Exception as e:
                    print(f"  ! {file_path} (read error: {e})")
                    core_results[file_path] = {'status': 'read_error', 'error': str(e)}
                    
            else:
                print(f"  ✗ {file_path} (missing)")
                core_results[file_path] = {'status': 'missing'}
                self.failed_tests.append(f'core_file_{file_path.replace("/", "_")}')
        
        self.test_results['core_system_files'] = core_results
    
    def test_import_logic(self):
        """測試導入邏輯 (不實際導入PyTorch)"""
        
        print("\n5. Testing Import Logic (Static Analysis)")
        print("-" * 40)
        
        import_results = {}
        
        # 檢查核心模組的導入邏輯
        test_files = [
            'core/enhanced_factory.py',
            'core/unified_registry.py'
        ]
        
        for file_path in test_files:
            full_path = self.project_root / file_path
            
            if not full_path.exists():
                print(f"  ✗ {file_path} (file missing)")
                import_results[file_path] = {'status': 'missing'}
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 分析導入語句
                imports = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line.startswith('import ') or line.startswith('from '):
                        imports.append(line)
                
                # 檢查關鍵導入
                has_torch_import = any('torch' in imp for imp in imports)
                has_relative_imports = any(imp.startswith('from .') for imp in imports)
                has_nn_module = 'nn.Module' in content
                has_dataclass = '@dataclass' in content
                has_typing_imports = any('typing' in imp for imp in imports)
                
                print(f"  ✓ {file_path} ({len(imports)} imports)")
                import_results[file_path] = {
                    'status': 'analyzed',
                    'total_imports': len(imports),
                    'has_torch_import': has_torch_import,
                    'has_relative_imports': has_relative_imports,
                    'has_nn_module': has_nn_module,
                    'has_dataclass': has_dataclass,
                    'has_typing_imports': has_typing_imports,
                    'sample_imports': imports[:5]  # 前5個導入作為樣本
                }
                
            except Exception as e:
                print(f"  ✗ {file_path} (analysis error: {e})")
                import_results[file_path] = {'status': 'error', 'error': str(e)}
                self.failed_tests.append(f'import_{file_path.replace("/", "_")}')
        
        self.test_results['import_logic'] = import_results
    
    def generate_test_report(self):
        """生成測試報告"""
        
        print("\n" + "=" * 60)
        print("ARCHITECTURE INTEGRATION TEST REPORT")
        print("=" * 60)
        
        # 統計總體結果
        total_tests = 0
        passed_tests = 0
        
        for category, results in self.test_results.items():
            if isinstance(results, dict):
                for item, result in results.items():
                    if isinstance(result, dict) and 'status' in result:
                        total_tests += 1
                        if result['status'] in ['exists', 'analyzed']:
                            passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\nOverall Results:")
        print(f"  Total Checks: {total_tests}")
        print(f"  Passed: {passed_tests}")
        print(f"  Failed: {total_tests - passed_tests}")
        print(f"  Success Rate: {success_rate:.1f}%")
        
        # 詳細結果
        print(f"\nDetailed Results by Category:")
        print("-" * 40)
        
        for category, results in self.test_results.items():
            print(f"\n{category.upper().replace('_', ' ')}:")
            
            category_passed = 0
            category_total = 0
            
            for item, result in results.items():
                if isinstance(result, dict) and 'status' in result:
                    category_total += 1
                    
                    if result['status'] in ['exists', 'analyzed']:
                        status_icon = "✓"
                        category_passed += 1
                    else:
                        status_icon = "✗"
                    
                    print(f"  {status_icon} {item}")
                    
                    # 顯示額外信息
                    if result['status'] == 'exists' and 'lines' in result:
                        print(f"    Lines: {result['lines']}, Size: {result['size_bytes']} bytes")
                    elif result['status'] == 'analyzed' and 'total_imports' in result:
                        print(f"    Imports: {result['total_imports']}")
                    elif 'error' in result:
                        print(f"    Error: {result['error']}")
            
            if category_total > 0:
                category_rate = (category_passed / category_total) * 100
                print(f"  Category Success Rate: {category_rate:.1f}% ({category_passed}/{category_total})")
        
        # 失敗的測試
        if self.failed_tests:
            print(f"\nFailed Tests:")
            for failed_test in self.failed_tests:
                print(f"  ✗ {failed_test}")
        
        # 架構統計
        print(f"\nArchitecture Statistics:")
        print("-" * 40)
        
        if 'csp_iformer_files' in self.test_results:
            csp_files = [k for k, v in self.test_results['csp_iformer_files'].items() 
                        if v.get('status') == 'exists']
            print(f"  CSP_IFormer variants: {len(csp_files)}")
        
        if 'segman_files' in self.test_results:
            segman_files = [k for k, v in self.test_results['segman_files'].items() 
                           if v.get('status') == 'exists']
            print(f"  SegMAN variants: {len(segman_files)}")
        
        if 'core_system_files' in self.test_results:
            core_files = [k for k, v in self.test_results['core_system_files'].items() 
                         if v.get('status') == 'exists']
            print(f"  Core system files: {len(core_files)}")
        
        # 代碼量統計
        total_lines = 0
        total_size = 0
        
        for category in ['csp_iformer_files', 'segman_files', 'core_system_files']:
            if category in self.test_results:
                for item, result in self.test_results[category].items():
                    if result.get('status') == 'exists':
                        total_lines += result.get('lines', 0)
                        total_size += result.get('size_bytes', 0)
        
        print(f"\nCode Statistics:")
        print(f"  Total lines: {total_lines:,}")
        print(f"  Total size: {total_size:,} bytes ({total_size/1024:.1f} KB)")
        
        print(f"\n" + "=" * 60)
        print("INTEGRATION TEST COMPLETED")
        print("=" * 60)
        
        # 給出建議
        if success_rate >= 90:
            print("\n🎉 Excellent! Architecture integration is highly successful.")
        elif success_rate >= 75:
            print("\n👍 Good! Architecture integration is mostly successful.")
        elif success_rate >= 50:
            print("\n⚠️  Warning! Some integration issues need attention.")
        else:
            print("\n❌ Critical! Major integration issues detected.")
        
        return self.test_results


def main():
    """主測試函數"""
    
    print("Architecture Integration Test Suite")
    print("(PyTorch-independent version)")
    print("=" * 60)
    
    # 創建測試器
    tester = ArchitectureIntegrationTester()
    
    # 運行測試
    test_results = tester.run_integration_test()
    
    return test_results


if __name__ == "__main__":
    try:
        test_results = main()
        
        # 保存測試結果
        import json
        
        results_file = Path(__file__).parent / 'integration_test_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\nTest results saved to: {results_file}")
        
    except Exception as e:
        print(f"\nTest execution failed: {e}")
        traceback.print_exc()