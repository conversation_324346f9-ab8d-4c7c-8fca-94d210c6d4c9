#!/usr/bin/env python3
"""
測試 class_metrics 修正的正確性
"""

def test_class_metrics_logic():
    """模擬測試 class_metrics 計算邏輯"""
    
    # 模擬匹配結果
    matches = [
        {'matched': True, 'class_id': 0, 'gt_idx': 0},   # TP for class 0
        {'matched': False, 'class_id': 0, 'gt_idx': -1}, # FP for class 0
        {'matched': True, 'class_id': 1, 'gt_idx': 1},   # TP for class 1
    ]
    
    # 模擬GT類別
    gt_classes = [0, 1, 0]  # 3個GT: 2個class 0, 1個class 1
    
    # 模擬預測類別
    pred_classes = [0, 0, 1]  # 3個預測: 2個class 0, 1個class 1
    
    print("=== 測試數據 ===")
    print(f"GT classes: {gt_classes}")
    print(f"Pred classes: {pred_classes}")
    print(f"Matches: {matches}")
    print()
    
    # 計算 class_metrics
    class_metrics = {}
    all_classes = set(pred_classes + gt_classes)
    
    print("=== 計算過程 ===")
    for class_id in all_classes:
        print(f"\n--- Class {class_id} ---")
        
        # 基於匹配結果計算TP, FP, FN
        class_tp = sum(1 for match in matches 
                      if match['matched'] and match['class_id'] == class_id)
        print(f"TP: {class_tp}")
        
        # FP: 該類別的預測但沒有匹配成功
        class_fp = sum(1 for match in matches 
                      if not match['matched'] and match['class_id'] == class_id)
        print(f"FP: {class_fp}")
        
        # FN: 該類別的GT但沒有被匹配到
        matched_gt_indices = {match['gt_idx'] for match in matches if match['matched']}
        class_fn = sum(1 for i, gt_class in enumerate(gt_classes) 
                      if gt_class == class_id and i not in matched_gt_indices)
        print(f"FN: {class_fn}")
        
        # 統計數量
        gt_count = sum(1 for cls in gt_classes if cls == class_id)
        pred_count = sum(1 for cls in pred_classes if cls == class_id)
        print(f"GT count: {gt_count}, Pred count: {pred_count}")
        
        # 計算指標
        class_precision = class_tp / (class_tp + class_fp) if (class_tp + class_fp) > 0 else 0.0
        class_recall = class_tp / (class_tp + class_fn) if (class_tp + class_fn) > 0 else 0.0
        class_f1 = 2 * class_precision * class_recall / (class_precision + class_recall) if (class_precision + class_recall) > 0 else 0.0
        
        print(f"Precision: {class_precision:.3f}")
        print(f"Recall: {class_recall:.3f}")
        print(f"F1: {class_f1:.3f}")
        
        class_metrics[f'class_{class_id}'] = {
            'precision': class_precision,
            'recall': class_recall,
            'f1_score': class_f1,
            'tp': class_tp,
            'fp': class_fp,
            'fn': class_fn,
            'gt_count': gt_count,
            'pred_count': pred_count
        }
    
    print("\n=== 最終結果 ===")
    for class_name, metrics in class_metrics.items():
        print(f"{class_name}: TP={metrics['tp']}, FP={metrics['fp']}, FN={metrics['fn']}")
        print(f"  P={metrics['precision']:.3f}, R={metrics['recall']:.3f}, F1={metrics['f1_score']:.3f}")
    
    # 驗證整體指標
    total_tp = sum(metrics['tp'] for metrics in class_metrics.values())
    total_fp = sum(metrics['fp'] for metrics in class_metrics.values())
    total_fn = sum(metrics['fn'] for metrics in class_metrics.values())
    
    print(f"\n整體: TP={total_tp}, FP={total_fp}, FN={total_fn}")
    
    # 預期結果驗證
    expected_class_0 = {'tp': 1, 'fp': 1, 'fn': 1}  # 1個TP, 1個FP, 1個未匹配的GT
    expected_class_1 = {'tp': 1, 'fp': 0, 'fn': 0}  # 1個TP, 0個FP, 0個FN
    
    print(f"\n=== 驗證 ===")
    print(f"Class 0 預期: {expected_class_0}")
    print(f"Class 0 實際: {{'tp': {class_metrics['class_0']['tp']}, 'fp': {class_metrics['class_0']['fp']}, 'fn': {class_metrics['class_0']['fn']}}}")
    
    print(f"Class 1 預期: {expected_class_1}")
    print(f"Class 1 實際: {{'tp': {class_metrics['class_1']['tp']}, 'fp': {class_metrics['class_1']['fp']}, 'fn': {class_metrics['class_1']['fn']}}}")
    
    # 檢查是否修正成功
    class_0_correct = (class_metrics['class_0']['tp'] == 1 and 
                      class_metrics['class_0']['fp'] == 1 and 
                      class_metrics['class_0']['fn'] == 1)
    
    class_1_correct = (class_metrics['class_1']['tp'] == 1 and 
                      class_metrics['class_1']['fp'] == 0 and 
                      class_metrics['class_1']['fn'] == 0)
    
    if class_0_correct and class_1_correct:
        print("\n✅ 修正成功！TP/FP 不再被錯誤記錄為 FN")
    else:
        print("\n❌ 修正失敗，仍有問題")

if __name__ == "__main__":
    test_class_metrics_logic()