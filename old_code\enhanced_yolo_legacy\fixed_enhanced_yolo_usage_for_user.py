#!/usr/bin/env python3
"""
為用戶測試數據修正的Enhanced YOLO使用示例
專門針對 D:\image\5_test_image_test\test1 測試數據
"""

from pathlib import Path
import sys
import logging

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

def main():
    """Enhanced YOLO使用示例主函數 - 針對用戶測試數據優化"""
    
    print("🚀 Enhanced YOLO使用示例 - 用戶測試數據版本")
    print("=" * 60)
    
    # 🔧 啟用詳細日誌 - 這很重要！
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    # ===================================================================
    # 📋 針對用戶測試數據的專用配置
    # ===================================================================
    
    # 🎯 基礎配置 - 使用WSL路徑格式
    detection_model_path = ""                           # 檢測模型路徑 (YOLO12) - 空字符串則跳過檢測
    segmentation_model_path = r"/path/to/your/segmentation_model.pt"    # 請替換為實際的分割模型路徑
    input_path = r"/mnt/d/image/5_test_image_test/test1/image"          # 使用WSL路徑格式
    output_path = r"/mnt/d/image/5_test_image_test/test1_output"        # 輸出目錄路徑
    task_type = "segmentation"                          # 任務類型: "detection", "segmentation", "both"
    
    # 🎛️ 推理參數
    img_size = 640                                      # 圖像大小
    global_conf = 0.05                                  # 全局置信度閾值
    iou_threshold = 0.35                                # IoU閾值
    max_det = 1000                                      # 最大檢測數量
    device = "cuda"                                     # 設備: "auto", "cuda", "cpu"
    
    # 🧩 SAHI配置 (大圖像切片推理) - 完整參數設定
    enable_sahi = False                                 # 是否啟用SAHI
    
    # 核心切片參數
    slice_height = 512                                  # 切片高度
    slice_width = 512                                   # 切片寬度
    overlap_height_ratio = 0.2                          # 高度重疊比例 (0.0-1.0)
    overlap_width_ratio = 0.2                           # 寬度重疊比例 (0.0-1.0)
    
    # 進階SAHI參數
    auto_slice_resolution = True                        # 自動切片解析度
    perform_standard_pred = True                        # 執行標準預測
    roi_ratio = (0.0, 0.0, 1.0, 1.0)                  # ROI區域比例 (x1, y1, x2, y2)
    
    # 後處理參數
    postprocess_type = "GREEDYNMM"                      # 後處理類型: "GREEDYNMM", "NMM", "NMS"
    postprocess_match_threshold = 0.1                   # 後處理匹配閾值
    postprocess_class_agnostic = False                  # 類別無關後處理
    
    # 類別過濾參數
    exclude_classes_by_name = []                        # 按名稱排除類別 [\"unwanted_class\"]
    exclude_classes_by_id = []                          # 按ID排除類別 [0, 1]
    
    # 輸出控制
    no_standard_prediction = False                      # 禁用標準預測
    no_sliced_prediction = False                        # 禁用切片預測
    export_pickle = False                               # 導出pickle格式
    export_crop = False                                 # 導出裁剪圖像
    
    # 🤝 Simple Tool功能整合 - 雙模型協同
    secondary_detection_model_path = ""                 # 副檢測模型路徑
    secondary_segmentation_model_path = ""              # 副分割模型路徑
    enable_dual_model_consensus = False                 # 啟用雙模型共識機制
    consensus_threshold = 0.05                          # 共識閾值
    consensus_iou_threshold = 0.05                      # 共識IoU閾值
    
    # 🧠 Simple Tool功能整合 - 智能檢測過濾
    enable_intelligent_filtering = True                 # 啟用智能過濾 (linear vs alligator, linear vs joint)
    enable_detection_merge = True                       # 啟用檢測合併
    iou_merge_threshold = 0.2                           # 合併IoU閾值
    
    # 🎯 Simple Tool功能整合 - 選擇性類別保存
    target_classes = None                               # 指定保存類別 [1, 2, 3] 或 None=保存全部
    save_all_when_target_found = True                   # 找到目標類別時是否保存全部結果
    skip_empty_results = True                           # 跳過空檢測結果
    
    # 📊 輸出配置
    save_visualizations = True                          # 保存可視化結果
    save_predictions = True                             # 保存預測結果
    save_statistics = True                              # 保存統計信息
    
    # 🏷️ LabelMe自動配置 - 針對用戶數據的WSL路徑
    labelme_dir = r"/mnt/d/image/5_test_image_test/test1/label"  # 使用WSL路徑格式
    auto_generate_classes = True                        # 是否自動從LabelMe生成類別配置
    
    # 🎨 進階配置
    batch_processing = True                             # 批次處理 (True=目錄, False=單張圖像)
    enable_tracking = False                             # 物件追蹤
    
    # ===================================================================
    # 🔍 執行檢查和驗證
    # ===================================================================
    
    print("🔍 檢查測試數據...")
    
    if not ENHANCED_YOLO_AVAILABLE:
        print("❌ Enhanced YOLO模組不可用")
        print("請檢查import_helper.py和enhanced_yolo_inference.py")
        return
    
    # 檢查輸入路徑 - 使用Path對象進行檢查
    input_path_obj = Path(input_path)
    if not input_path_obj.exists():
        print(f"❌ 錯誤: 輸入路徑不存在: {input_path}")
        print("💡 建議:")
        print("   1. 檢查路徑格式是否正確（WSL格式: /mnt/d/...)")
        print("   2. 確認目錄確實存在")
        return
    else:
        print(f"✅ 輸入路徑存在: {input_path}")
    
    # 檢查標註目錄
    labelme_path_obj = Path(labelme_dir)
    if not labelme_path_obj.exists():
        print(f"❌ 錯誤: 標註目錄不存在: {labelme_dir}")
        return
    else:
        print(f"✅ 標註目錄存在: {labelme_dir}")
        # 統計標註文件
        json_files = list(labelme_path_obj.glob("*.json"))
        print(f"📊 找到 {len(json_files)} 個JSON標註文件")
    
    # 檢查模型路徑（僅警告，不阻止運行）
    if segmentation_model_path and not Path(segmentation_model_path).exists():
        print(f"⚠️  警告: 分割模型路徑不存在: {segmentation_model_path}")
        print("🔧 請替換為實際的模型路徑，或使用空字符串跳過")
    
    # ===================================================================
    # 🚀 執行Enhanced YOLO推理
    # ===================================================================
    
    try:
        # 導入Enhanced YOLO相關模組
        from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
            EnhancedYOLOConfig, 
            create_enhanced_yolo_inference,
            scan_labelme_annotations,
            generate_class_configs_from_labelme
        )
        
        print("✅ Enhanced YOLO模組導入成功")
        
        # 🎯 關鍵修正：自動生成包含"joint"類別的配置
        print(f"🏷️  正在掃描LabelMe標註: {labelme_dir}")
        class_configs = {}
        
        try:
            # 自動從LabelMe標註中生成類別配置
            class_configs = generate_class_configs_from_labelme(labelme_dir)
            print(f"✅ 自動生成了 {len(class_configs)} 個類別配置")
            
            # 顯示檢測到的類別
            for class_id, config in class_configs.items():
                print(f"   類別 {class_id}: {config.name}")
                
        except Exception as e:
            print(f"⚠️  LabelMe掃描失敗: {e}")
            print("🔧 手動創建類別配置...")
            
            # 手動創建包含"joint"類別的配置
            from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import ClassConfig
            
            class_configs = {
                0: ClassConfig(
                    name="joint",
                    conf_threshold=0.3,
                    color=(0, 255, 0),  # 綠色
                    enabled=True
                )
            }
            print("✅ 手動創建了joint類別配置")
        
        # 創建配置對象
        config = EnhancedYOLOConfig(
            # 模型配置
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            device=device,
            
            # 推理配置
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            
            # 🎯 關鍵：類別配置
            class_configs=class_configs,
            
            # SAHI配置 - 完整23個參數
            enable_sahi=enable_sahi,
            slice_height=slice_height,
            slice_width=slice_width,
            overlap_height_ratio=overlap_height_ratio,
            overlap_width_ratio=overlap_width_ratio,
            auto_slice_resolution=auto_slice_resolution,
            perform_standard_pred=perform_standard_pred,
            roi_ratio=roi_ratio,
            postprocess_type=postprocess_type,
            postprocess_match_threshold=postprocess_match_threshold,
            postprocess_class_agnostic=postprocess_class_agnostic,
            exclude_classes_by_name=exclude_classes_by_name,
            exclude_classes_by_id=exclude_classes_by_id,
            no_standard_prediction=no_standard_prediction,
            no_sliced_prediction=no_sliced_prediction,
            export_pickle=export_pickle,
            export_crop=export_crop,
            
            # Simple Tool功能整合 - 雙模型協同
            secondary_detection_model_path=secondary_detection_model_path,
            secondary_segmentation_model_path=secondary_segmentation_model_path,
            enable_dual_model_consensus=enable_dual_model_consensus,
            consensus_threshold=consensus_threshold,
            consensus_iou_threshold=consensus_iou_threshold,
            
            # Simple Tool功能整合 - 智能檢測過濾
            enable_intelligent_filtering=enable_intelligent_filtering,
            enable_detection_merge=enable_detection_merge,
            iou_merge_threshold=iou_merge_threshold,
            
            # Simple Tool功能整合 - 選擇性類別保存
            target_classes=target_classes,
            save_all_when_target_found=save_all_when_target_found,
            skip_empty_results=skip_empty_results,
            
            # 輸出配置
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics,
            
            # 進階功能
            enable_tracking=enable_tracking,
            auto_convert_annotations=True
        )
        
        print("🔧 創建推理器...")
        inference = create_enhanced_yolo_inference(config)
        print("✅ 推理器創建成功")
        
        # 創建輸出目錄
        Path(output_path).mkdir(parents=True, exist_ok=True)
        
        print("📊 配置信息:")
        print(f"   🎯 任務類型: {task_type}")
        print(f"   📁 輸入: {input_path}")
        print(f"   📁 輸出: {output_path}")
        print(f"   🏷️  標註: {labelme_dir}")
        print(f"   🎛️  圖像大小: {img_size}")
        print(f"   🎛️  置信度: {global_conf}")
        print(f"   🧩 SAHI: {'啟用' if enable_sahi else '禁用'}")
        if class_configs:
            print(f"   🏷️  類別數: {len(class_configs)}")
            for class_id, config in class_configs.items():
                print(f"      - {class_id}: {config.name}")
        
        print("\n🚀 開始推理...")
        print("🔍 注意觀察日誌中的GT載入信息！")
        
        if batch_processing and Path(input_path).is_dir():
            # 批次處理
            print(f"📂 批次處理目錄: {input_path}")
            
            # 檢查是否有標註文件
            input_dir = Path(input_path)
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
            annotation_extensions = ['.json', '.txt', '.xml']
            
            # 統計圖像和標註文件
            image_files = []
            for ext in image_extensions:
                image_files.extend(input_dir.glob(f"*{ext}"))
                image_files.extend(input_dir.glob(f"*{ext.upper()}"))
            
            annotation_files = []
            for ext in annotation_extensions:
                annotation_files.extend(input_dir.glob(f"*{ext}"))
                annotation_files.extend(input_dir.glob(f"*{ext.upper()}"))
            
            has_labels = len(annotation_files) > 0
            print(f"📊 統計: {len(image_files)} 個圖像, {len(annotation_files)} 個標註文件")
            
            results = inference.batch_predict(
                input_dir=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            
            # 根據是否有標註文件調整顯示數量
            processed_count = len(results) if results else 0
            if has_labels and processed_count > 0:
                display_count = processed_count // 2  # 如果有標註文件，數量除以2
                print(f"✅ 批次處理完成! 處理了 {display_count} 組數據 (圖像+標註)")
            else:
                print(f"✅ 批次處理完成! 處理了 {processed_count} 張圖像")
            
        else:
            # 單張圖像處理
            print(f"🖼️  處理單張圖像: {input_path}")
            results = inference.predict_single_image(
                image_path=input_path,
                output_dir=output_path,
                task_type=task_type
            )
            print("✅ 單張圖像處理完成!")
        
        # 顯示統計信息
        if hasattr(inference, 'inference_stats'):
            stats = inference.inference_stats
            print("\n📊 統計信息:")
            print(f"   🎯 總檢測數: {stats.get('total_detections', 0)}")
            print(f"   📊 類別統計: {stats.get('class_counts', {})}")
            print(f"   ⏱️  處理時間: {stats.get('total_time', 0):.2f}秒")
        
        print(f"\n🎉 推理完成! 結果保存至: {output_path}")
        print(f"\n🔍 請檢查輸出目錄中的可視化結果，應該可以看到GT標註了！")
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保Enhanced YOLO相關依賴已安裝:")
        print("   pip install torch torchvision ultralytics")
        print("   pip install opencv-python numpy matplotlib")
        print("   pip install sahi")
        
    except Exception as e:
        print(f"❌ 推理失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()