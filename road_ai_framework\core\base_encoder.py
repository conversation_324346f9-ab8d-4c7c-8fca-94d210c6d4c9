"""
基礎編碼器類別：為所有編碼器提供統一接口
支持不同類型的編碼器（CNN、Transformer、Mamba等）
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import torch
import torch.nn as nn


class BaseEncoder(nn.Module, ABC):
    """編碼器基礎類別"""
    
    def __init__(self, 
                 input_channels: int = 3,
                 output_channels: List[int] = None,
                 **kwargs):
        """
        初始化編碼器
        
        Args:
            input_channels: 輸入通道數
            output_channels: 各層輸出通道數列表
            **kwargs: 其他配置參數
        """
        super().__init__()
        self.input_channels = input_channels
        self.output_channels = output_channels or [64, 128, 256, 512]
        self.config = kwargs
        
        # 特徵層信息，用於FPN等解碼器
        self.feature_info = []
        
        # 構建編碼器
        self._build_encoder()
        
        # 初始化權重
        self._init_weights()
    
    @abstractmethod
    def _build_encoder(self):
        """構建編碼器架構，子類必須實現"""
        pass
    
    @abstractmethod
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """
        前向傳播
        
        Args:
            x: 輸入張量 [B, C, H, W]
            
        Returns:
            特徵層列表，通常是多個分辨率的特徵圖
        """
        pass
    
    def _init_weights(self):
        """權重初始化，默認實現"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def get_feature_info(self) -> List[Dict[str, Any]]:
        """
        獲取特徵層信息
        
        Returns:
            特徵層信息列表，包含通道數、步長等
        """
        return self.feature_info
    
    def get_output_channels(self) -> List[int]:
        """獲取輸出通道數列表"""
        return self.output_channels
    
    @property
    def encoder_type(self) -> str:
        """編碼器類型標識"""
        return self.__class__.__name__


class CNNEncoder(BaseEncoder):
    """CNN編碼器基類"""
    
    def __init__(self, **kwargs):
        self.encoder_family = "CNN"
        super().__init__(**kwargs)


class TransformerEncoder(BaseEncoder):
    """Transformer編碼器基類"""
    
    def __init__(self, **kwargs):
        self.encoder_family = "Transformer"
        super().__init__(**kwargs)


class MambaEncoder(BaseEncoder):
    """Mamba編碼器基類（預留）"""
    
    def __init__(self, **kwargs):
        self.encoder_family = "Mamba"
        super().__init__(**kwargs)


class EncoderConfig:
    """編碼器配置類"""
    
    def __init__(self, 
                 encoder_type: str,
                 encoder_variant: str = "default",
                 input_channels: int = 3,
                 output_channels: List[int] = None,
                 **kwargs):
        self.encoder_type = encoder_type
        self.encoder_variant = encoder_variant
        self.input_channels = input_channels
        self.output_channels = output_channels or [64, 128, 256, 512]
        self.config = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'encoder_type': self.encoder_type,
            'encoder_variant': self.encoder_variant,
            'input_channels': self.input_channels,
            'output_channels': self.output_channels,
            **self.config
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'EncoderConfig':
        """從字典創建配置"""
        return cls(**config_dict)