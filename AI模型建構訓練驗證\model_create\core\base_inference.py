#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基礎推理類

提供所有推理模組的基類和統一接口
"""

import torch
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
import numpy as np


class BaseInference(ABC):
    """
    基礎推理抽象類
    
    所有推理模組的基類，定義統一的接口規範
    """
    
    def __init__(self, device: str = "auto"):
        """
        初始化基礎推理器
        
        參數:
            device: 計算設備 ("cuda", "cpu", "auto")
        """
        self.device = self._setup_device(device)
        self.model = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _setup_device(self, device: str) -> torch.device:
        """
        設置計算設備
        
        參數:
            device: 設備字符串
            
        返回:
            torch.device對象
        """
        if device == "auto":
            if torch.cuda.is_available():
                device = "cuda"
            else:
                device = "cpu"
        
        return torch.device(device)
    
    @abstractmethod
    def predict(self, input_data: Any, **kwargs) -> Dict:
        """
        單個預測的抽象方法
        
        參數:
            input_data: 輸入數據
            **kwargs: 其他參數
            
        返回:
            預測結果字典
        """
        pass
    
    @abstractmethod
    def batch_predict(self, input_list: List[Any], **kwargs) -> List[Dict]:
        """
        批次預測的抽象方法
        
        參數:
            input_list: 輸入數據列表
            **kwargs: 其他參數
            
        返回:
            預測結果列表
        """
        pass
    
    def preprocess(self, input_data: Any) -> Any:
        """
        預處理輸入數據（可選實現）
        
        參數:
            input_data: 原始輸入數據
            
        返回:
            預處理後的數據
        """
        return input_data
    
    def postprocess(self, prediction: Any) -> Any:
        """
        後處理預測結果（可選實現）
        
        參數:
            prediction: 原始預測結果
            
        返回:
            後處理後的結果
        """
        return prediction
    
    def evaluate(self, test_data: Any, ground_truth: Any = None) -> Dict:
        """
        評估模型性能（可選實現）
        
        參數:
            test_data: 測試數據
            ground_truth: 真實標籤
            
        返回:
            評估指標字典
        """
        self.logger.warning("評估方法未實現")
        return {}
    
    def get_model_info(self) -> Dict:
        """
        獲取模型信息
        
        返回:
            模型信息字典
        """
        info = {
            "model_class": self.__class__.__name__,
            "device": str(self.device),
            "model_loaded": self.model is not None
        }
        
        if self.model is not None:
            try:
                # 計算模型參數數量
                total_params = sum(p.numel() for p in self.model.parameters())
                trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
                
                info.update({
                    "total_parameters": total_params,
                    "trainable_parameters": trainable_params,
                    "model_size_mb": total_params * 4 / (1024 * 1024)  # 假設float32
                })
            except Exception as e:
                self.logger.warning(f"無法獲取模型參數信息: {e}")
        
        return info
    
    def warmup(self, dummy_input: Any = None, iterations: int = 3):
        """
        模型預熱
        
        參數:
            dummy_input: 虛擬輸入數據
            iterations: 預熱迭代次數
        """
        if self.model is None:
            self.logger.warning("模型未載入，無法進行預熱")
            return
        
        self.logger.info(f"開始模型預熱，迭代 {iterations} 次")
        
        self.model.eval()
        with torch.no_grad():
            for i in range(iterations):
                if dummy_input is not None:
                    try:
                        _ = self.predict(dummy_input)
                    except Exception as e:
                        self.logger.warning(f"預熱迭代 {i+1} 失敗: {e}")
                        break
        
        self.logger.info("模型預熱完成")
    
    def set_precision(self, precision: str = "float32"):
        """
        設置模型精度
        
        參數:
            precision: 精度類型 ("float32", "float16", "bfloat16")
        """
        if self.model is None:
            self.logger.warning("模型未載入，無法設置精度")
            return
        
        try:
            if precision == "float16":
                self.model = self.model.half()
            elif precision == "bfloat16":
                self.model = self.model.bfloat16()
            elif precision == "float32":
                self.model = self.model.float()
            else:
                self.logger.warning(f"不支持的精度類型: {precision}")
                return
            
            self.logger.info(f"模型精度已設置為: {precision}")
            
        except Exception as e:
            self.logger.error(f"設置模型精度失敗: {e}")
    
    def memory_usage(self) -> Dict:
        """
        獲取記憶體使用情況
        
        返回:
            記憶體使用信息字典
        """
        memory_info = {}
        
        if torch.cuda.is_available() and self.device.type == "cuda":
            memory_info.update({
                "gpu_allocated_mb": torch.cuda.memory_allocated(self.device) / (1024 * 1024),
                "gpu_cached_mb": torch.cuda.memory_reserved(self.device) / (1024 * 1024),
                "gpu_max_allocated_mb": torch.cuda.max_memory_allocated(self.device) / (1024 * 1024)
            })
        
        # 可以添加CPU記憶體使用情況
        try:
            import psutil
            process = psutil.Process()
            memory_info["cpu_memory_mb"] = process.memory_info().rss / (1024 * 1024)
        except ImportError:
            pass
        
        return memory_info
    
    def clear_cache(self):
        """
        清理記憶體緩存
        """
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            self.logger.info("GPU緩存已清理")
    
    def save_model(self, save_path: str, **kwargs):
        """
        保存模型（可選實現）
        
        參數:
            save_path: 保存路徑
            **kwargs: 其他參數
        """
        if self.model is None:
            self.logger.warning("模型未載入，無法保存")
            return
        
        try:
            torch.save(self.model.state_dict(), save_path)
            self.logger.info(f"模型已保存至: {save_path}")
        except Exception as e:
            self.logger.error(f"保存模型失敗: {e}")
    
    def load_model(self, model_path: str, **kwargs):
        """
        載入模型（可選實現）
        
        參數:
            model_path: 模型路徑
            **kwargs: 其他參數
        """
        self.logger.warning("載入模型方法需要在子類中實現")


class InferenceFactory:
    """
    推理器工廠類
    
    用於創建不同類型的推理器
    """
    
    _inference_registry = {}
    
    @classmethod
    def register(cls, name: str, inference_class):
        """
        註冊推理器類
        
        參數:
            name: 推理器名稱
            inference_class: 推理器類
        """
        cls._inference_registry[name] = inference_class
    
    @classmethod
    def create_inference(cls, name: str, **kwargs) -> BaseInference:
        """
        創建推理器實例
        
        參數:
            name: 推理器名稱
            **kwargs: 初始化參數
            
        返回:
            推理器實例
        """
        if name not in cls._inference_registry:
            raise ValueError(f"未知的推理器類型: {name}")
        
        inference_class = cls._inference_registry[name]
        return inference_class(**kwargs)
    
    @classmethod
    def list_available(cls) -> List[str]:
        """
        列出可用的推理器類型
        
        返回:
            推理器名稱列表
        """
        return list(cls._inference_registry.keys())


# 裝飾器：用於自動註冊推理器
def register_inference(name: str):
    """
    推理器註冊裝飾器
    
    參數:
        name: 推理器名稱
    """
    def decorator(cls):
        InferenceFactory.register(name, cls)
        return cls
    return decorator