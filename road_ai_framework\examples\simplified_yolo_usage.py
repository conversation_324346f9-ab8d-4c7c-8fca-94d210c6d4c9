#!/usr/bin/env python3
"""
簡化YOLO推理系統完整參數使用範例
可直接運行的腳本，支援所有簡化API的15個核心參數調整
"""

import sys
import os
from pathlib import Path

# 添加模組路徑
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# ==================== 完整參數設定區域 ====================
# 🔧 在這裡修改所有可用的配置參數

# ===== 模型配置 (3個參數) =====
detection_model_path = ""      # YOLO檢測模型路徑
segmentation_model_path = r"D:\4_road_crack\best.pt"  # YOLO分割模型路徑（優先使用）
# 運算設備: "auto", "cpu", "cuda", "mps"
device = "cuda"

# ===== 推理配置 (4個參數) =====
img_size = 640                    # 輸入圖像尺寸
global_conf = 0.05               # 全局置信度閾值 (0.0-1.0)
iou_threshold = 0.45             # IoU閾值 (0.0-1.0)
max_det = 1000                   # 最大檢測數量

# ===== SAHI配置 (5個參數) =====
enable_sahi = False              # 是否啟用SAHI大圖像切片推理
slice_size = 640                 # SAHI切片大小
overlap_ratio = 0.2              # SAHI重疊比例 (0.0-1.0)
sahi_conf = 0.1                  # SAHI專用置信度閾值
sahi_iou = 0.5                   # SAHI專用IoU閾值

# ===== 輸出配置 (3個參數) =====
save_visualizations = True       # 是否保存可視化結果
save_predictions = True          # 是否保存預測結果
save_statistics = True           # 是否保存統計信息

# ===== 輸入輸出路徑配置 =====
input_path = r"D:\image\5_test_image\test_2_org"       # 輸入圖片路徑或資料夾
output_path = r"D:\image\5_test_image_test\test_100_output"          # 輸出結果資料夾

# ===== 全域類別設定 (可選) =====
custom_class_names = [           # 自定義類別名稱，空列表表示自動檢測
    "expansion_joint_伸縮縫",      # ID: 0
    "joint_路面接縫",               # ID: 1
    "linear_crack_裂縫",        # ID: 2
    "Alligator_crack_龜裂",     # ID: 3
    "potholes_坑洞",            # ID: 4
    "patch_補綻",               # ID: 5
    "manhole_人孔蓋或排水溝",             # ID: 6
    "deformation_變形",         # ID: 7
    "dirt_污垢",                # ID: 8
    "lane_line_linear_白綫裂縫"     # ID: 9
]

custom_label_aliases = {         # 標籤別名映射，空字典表示無映射
    'manhole': 'manhole_人孔蓋或排水溝',
    'potholes': 'potholes_坑洞',
    'linear_crack': 'linear_crack_裂縫',
    'linear_crack_': 'linear_crack_裂縫',
    'dirt': 'dirt_污垢',
    'expansion_joint': 'expansion_joint_伸縮縫',
    'expansion_joint_': 'expansion_joint_伸縮縫',
    'joint': 'joint_路面接縫',
    'joint_': 'joint_路面接縫',
    'deformation': 'deformation_變形',
    'patch': 'patch_補綻',
    'Alligator_crack': 'Alligator_crack_龜裂',
    'lane_line_linear': 'lane_line_linear_白綫裂縫'
}

# ==================== 執行模式設定 ====================
run_mode = "inference"                # 執行模式: "demo", "inference", "batch", "all"


def run_actual_inference():
    """實際運行推理 - 使用頂部設定的所有參數"""
    print("🚀 實際推理執行")
    print("=" * 60)

    try:
        from models.inference.simplified import SimplifiedYOLO, SimplifiedYOLOConfig, quick_predict

        # 顯示當前配置
        print("📋 當前配置參數:")
        print(f"  檢測模型: {detection_model_path}")
        print(f"  分割模型: {segmentation_model_path}")
        print(f"  設備: {device}")
        print(f"  圖像尺寸: {img_size}")
        print(f"  置信度: {global_conf}")
        print(f"  IoU閾值: {iou_threshold}")
        print(f"  最大檢測: {max_det}")
        print(f"  SAHI: {'啟用' if enable_sahi else '停用'}")
        if enable_sahi:
            print(f"    切片大小: {slice_size}")
            print(f"    重疊比例: {overlap_ratio}")
            print(f"    SAHI置信度: {sahi_conf}")
            print(f"    SAHI IoU: {sahi_iou}")
        print(f"  輸入路徑: {input_path}")
        print(f"  輸出路徑: {output_path}")

        # 創建完整配置
        config = SimplifiedYOLOConfig(
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            device=device,
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            enable_sahi=enable_sahi,
            slice_size=slice_size,
            overlap_ratio=overlap_ratio,
            sahi_conf=sahi_conf,
            sahi_iou=sahi_iou,
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics
        )

        print("\n📊 配置摘要:")
        print(config.summary())

        # 檢查輸入路徑
        if not os.path.exists(input_path):
            print(f"\n❌ 輸入路徑不存在: {input_path}")
            print("請修改 input_path 參數指向正確的圖片路徑或資料夾")
            return False

        # 檢查模型路徑
        model_path = config.get_model_path()
        if not os.path.exists(model_path):
            print(f"\n❌ 模型路徑不存在: {model_path}")
            print("請修改模型路徑參數指向正確的 .pt 檔案")
            return False

        # 創建輸出目錄
        os.makedirs(output_path, exist_ok=True)

        print(f"\n🎯 開始推理...")
        print("-" * 40)

        # 創建YOLO實例
        yolo = SimplifiedYOLO(config)

        # 單張圖片處理
        if os.path.isfile(input_path):
            print(f"📸 處理單張圖片: {input_path}")
            result = yolo.predict(input_path, output_dir=output_path)
            print(f"✅ 推理完成，結果: {len(result.get('boxes', []))} 個物件")

        # 批次處理
        else:
            print(f"📁 批次處理資料夾: {input_path}")
            results = yolo.predict_batch(input_path, output_path)
            print(f"✅ 批次推理完成，處理了 {len(results)} 張圖片")

        print(f"💾 結果保存至: {output_path}")
        return True

    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保在 road_ai_framework 目錄中運行此腳本")
        return False
    except Exception as e:
        print(f"❌ 推理執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_api_usage():
    """演示API使用方式"""
    print("\n🎯 API使用方式演示")
    print("=" * 60)

    try:
        from models.inference.simplified import SimplifiedYOLO, SimplifiedYOLOConfig

        print("📚 可用的API創建方式:")

        # 方法1: 完整配置
        print("\n1️⃣ 完整配置方式:")
        config_code = f"""
config = SimplifiedYOLOConfig(
    segmentation_model_path="{segmentation_model_path}",
    device="{device}",
    img_size={img_size},
    global_conf={global_conf},
    iou_threshold={iou_threshold},
    max_det={max_det},
    enable_sahi={enable_sahi},
    slice_size={slice_size},
    overlap_ratio={overlap_ratio},
    save_visualizations={save_visualizations}
)
yolo = SimplifiedYOLO(config)
        """
        print(config_code)

        # 方法2: 快速創建
        print("\n2️⃣ 快速創建方式:")
        model_path = segmentation_model_path or detection_model_path
        print(
            f"yolo = SimplifiedYOLO.from_model('{model_path}', confidence={global_conf})")

        # 方法3: SAHI專用
        if enable_sahi:
            print("\n3️⃣ SAHI專用方式:")
            print(
                f"yolo = SimplifiedYOLO.with_sahi('{model_path}', slice_size={slice_size}, overlap_ratio={overlap_ratio})")

        # 方法4: 檢測專用
        if detection_model_path:
            print("\n4️⃣ 檢測專用方式:")
            print(
                f"yolo = SimplifiedYOLO.detection_only('{detection_model_path}')")

        print("\n🔧 推理使用方式:")
        print("  result = yolo.predict('image.jpg')")
        print("  results = yolo.predict_batch('input_dir', 'output_dir')")
        print("  yolo.set_confidence(0.5)")
        print("  yolo.enable_sahi()")

        print("✅ API演示完成")

    except ImportError as e:
        print(f"❌ 導入失敗: {e}")


def demonstrate_batch_processing():
    """演示批次處理功能"""
    print("\n📁 批次處理功能演示")
    print("=" * 60)

    try:
        from models.inference.simplified import quick_batch, SimplifiedYOLO

        print("📊 批次處理方式對比:")

        model_path = segmentation_model_path or detection_model_path

        # 方法1: 快速批次處理
        print("\n1️⃣ 快速批次處理:")
        print(
            f"results = quick_batch('{model_path}', '{input_path}', '{output_path}')")

        # 方法2: 配置驅動批次處理
        print("\n2️⃣ 配置驅動批次處理:")
        batch_code = f"""
config = SimplifiedYOLOConfig(
    segmentation_model_path="{model_path}",
    global_conf={global_conf},
    enable_sahi={enable_sahi}
)
yolo = SimplifiedYOLO(config)
results = yolo.predict_batch('{input_path}', '{output_path}')
        """
        print(batch_code)

        # 方法3: 逐步批次處理
        print("\n3️⃣ 逐步批次處理:")
        step_code = f"""
yolo = SimplifiedYOLO.from_model('{model_path}', confidence={global_conf})
if {enable_sahi}:
    yolo.enable_sahi(slice_size={slice_size}, overlap_ratio={overlap_ratio})

for image_file in os.listdir('{input_path}'):
    if image_file.lower().endswith(('.jpg', '.png', '.jpeg')):
        result = yolo.predict(os.path.join('{input_path}', image_file), output_dir='{output_path}')
        print(f"處理完成: {{image_file}}, 檢測數量: {{len(result.get('boxes', []))}}")
        """
        print(step_code)

        print("\n📋 批次處理參數說明:")
        print(f"  輸入路徑: {input_path}")
        print(f"  輸出路徑: {output_path}")
        print(f"  SAHI模式: {'啟用' if enable_sahi else '停用'}")
        print(f"  保存可視化: {save_visualizations}")
        print(f"  保存預測結果: {save_predictions}")
        print(f"  保存統計信息: {save_statistics}")

        print("\n✅ 批次處理演示完成")

    except ImportError as e:
        print(f"❌ 導入失敗: {e}")


def test_configuration():
    """測試配置管理功能"""
    print("\n⚙️ 配置管理測試")
    print("=" * 60)

    try:
        from models.inference.simplified import SimplifiedYOLOConfig, SimplifiedYOLO

        print("📋 當前配置測試:")

        # 創建配置實例
        config = SimplifiedYOLOConfig(
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            device=device,
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            enable_sahi=enable_sahi,
            slice_size=slice_size,
            overlap_ratio=overlap_ratio,
            sahi_conf=sahi_conf,
            sahi_iou=sahi_iou,
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics
        )

        print("✅ 配置實例創建成功")

        # 顯示配置摘要
        print("\n📊 完整配置摘要:")
        print(config.summary())

        # 測試配置方法
        print("\n🔍 配置方法測試:")
        print(f"  優先模型路徑: {config.get_model_path()}")
        print(f"  僅檢測模式: {config.is_detection_only()}")
        print(f"  分割可用: {config.is_segmentation_available()}")

        # 參數統計
        config_dict = {
            'detection_model_path': config.detection_model_path,
            'segmentation_model_path': config.segmentation_model_path,
            'device': config.device,
            'img_size': config.img_size,
            'global_conf': config.global_conf,
            'iou_threshold': config.iou_threshold,
            'max_det': config.max_det,
            'enable_sahi': config.enable_sahi,
            'slice_size': config.slice_size,
            'overlap_ratio': config.overlap_ratio,
            'sahi_conf': config.sahi_conf,
            'sahi_iou': config.sahi_iou,
            'save_visualizations': config.save_visualizations,
            'save_predictions': config.save_predictions,
            'save_statistics': config.save_statistics
        }

        print(f"\n📈 配置統計:")
        print(f"  總參數數: {len(config_dict)}")
        print(f"  非預設參數: {sum(1 for k, v in config_dict.items() if v != '')}")
        print(f"  SAHI相關參數: 5 個")
        print(f"  相比原版減少: 79% (從70+個到15個)")

        print("\n✅ 配置管理測試完成")

    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
    except Exception as e:
        print(f"❌ 配置測試失敗: {e}")


def configure_global_settings():
    """配置全域設定"""
    print("\n🌍 全域設定配置")
    print("=" * 60)

    try:
        from models.inference.simplified.config.yolo_config import set_global_class_names, set_global_label_aliases

        print("📋 當前全域設定:")

        # 設定自定義類別名稱
        if custom_class_names:
            print(f"\n1️⃣ 設定自定義類別 ({len(custom_class_names)}個):")
            set_global_class_names(custom_class_names)
            for i, class_name in enumerate(custom_class_names):
                print(f"   {i}: {class_name}")
            print("✅ 類別設定完成")
        else:
            print("\n1️⃣ 使用自動類別檢測:")
            print("   將從模型或LabelMe標註自動檢測類別")

        # 設定標籤別名
        if custom_label_aliases:
            print(f"\n2️⃣ 設定標籤別名 ({len(custom_label_aliases)}個):")
            set_global_label_aliases(custom_label_aliases)
            for original, alias in custom_label_aliases.items():
                print(f"   '{original}' → '{alias}'")
            print("✅ 別名設定完成")
        else:
            print("\n2️⃣ 無標籤別名映射:")
            print("   直接使用原始標籤名稱")

        print("\n📝 設定說明:")
        print("  • CLASS_NAMES: 模型輸出設定，空值表示自動檢測")
        print("  • LABEL_ALIASES: 標籤映射，用於處理標註與模型不匹配")
        print("  • 優先級: 自定義類別 > 模型檢測 > LabelMe掃描")

        print("\n✅ 全域設定配置完成")

    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
    except Exception as e:
        print(f"❌ 全域設定失敗: {e}")


def example_system_check():
    """範例6: 系統檢查"""
    print("\n🔍 範例6: 系統檢查")
    print("=" * 50)

    try:
        from models.inference.simplified import print_dependency_report, check_dependencies

        print("系統檢查功能:")
        print("1. 依賴檢查:")
        print("   print_dependency_report()")

        print("2. 程式化檢查:")
        print("   deps = check_dependencies()")
        print("   if deps['required']['ultralytics']['installed']:")
        print("       print('YOLO可用')")

        # 執行實際檢查
        print("\n執行實際檢查:")
        deps = check_dependencies()

        # 顯示結果摘要
        required_ok = all(info['installed']
                          for info in deps['required'].values())
        optional_count = sum(
            1 for info in deps['optional'].values() if info['installed'])

        print(f"必需依賴: {'✅' if required_ok else '❌'}")
        print(f"可選依賴: {optional_count}/{len(deps['optional'])} 已安裝")

    except ImportError as e:
        print(f"❌ 導入失敗: {e}")


def example_comparison():
    """範例7: 新舊API對比"""
    print("\n📊 範例7: 新舊API對比")
    print("=" * 50)

    print("重構前 (複雜):")
    old_api = """
# 70+參數配置
config = EnhancedYOLOConfig(
    detection_model_path="...",
    segmentation_model_path="...",
    device="auto",
    img_size=640,
    global_conf=0.25,
    iou_threshold=0.45,
    max_det=1000,
    # SAHI的23個參數
    enable_sahi=True,
    slice_height=640,
    slice_width=640,
    overlap_height_ratio=0.3,
    overlap_width_ratio=0.3,
    auto_slice_resolution=False,
    perform_standard_pred=True,
    # ... 還有17個SAHI參數
    # Simple Tool的11個參數
    enable_intelligent_filtering=True,
    enable_detection_merge=True,
    # ... 還有9個參數
    # 類別配置的複雜方法
    class_configs=create_manual_class_configs([...])
)
inference = create_enhanced_yolo_inference(config)
    """

    print("重構後 (簡化):")
    new_api = """
# 15個核心參數，自動配置
yolo = SimplifiedYOLO.from_model("model.pt", confidence=0.3)
# 或者啟用SAHI
yolo = SimplifiedYOLO.with_sahi("model.pt", slice_size=640)
    """

    print("對比結果:")
    print("  配置參數: 70+ → 15 (-79%)")
    print("  代碼行數: ~100行 → ~3行 (-97%)")
    print("  學習難度: 高 → 低")
    print("  維護成本: 高 → 低")


def main():
    """主函數 - 根據run_mode執行對應功能"""
    print("🚀 簡化YOLO推理系統 - 完整參數配置版")
    print("="*70)
    print("支援所有15個核心參數的完整配置和調整")
    print("相比原版: 參數減少79% (70→15)，代碼簡化97%")
    print("="*70)

    # 顯示當前參數設定
    print("\n📋 當前參數設定:")
    print(f"  執行模式: {run_mode}")
    print(f"  檢測模型: {detection_model_path}")
    print(f"  分割模型: {segmentation_model_path}")
    print(f"  輸入路徑: {input_path}")
    print(f"  輸出路徑: {output_path}")
    print(f"  SAHI模式: {'啟用' if enable_sahi else '停用'}")
    print(f"  自定義類別: {len(custom_class_names)} 個")
    print(f"  標籤別名: {len(custom_label_aliases)} 個")

    try:
        # 配置全域設定
        configure_global_settings()

        # 根據執行模式運行
        if run_mode == "demo":
            print("\n🎯 執行演示模式...")
            demonstrate_api_usage()
            test_configuration()
            demonstrate_batch_processing()
            example_system_check()
            example_comparison()

        elif run_mode == "inference":
            print("\n🚀 執行推理模式...")
            success = run_actual_inference()
            if not success:
                print("\n💡 提示: 請檢查模型路徑和輸入路徑設定")

        elif run_mode == "batch":
            print("\n📁 執行批次處理模式...")
            if os.path.isdir(input_path):
                success = run_actual_inference()
            else:
                print(f"❌ 批次模式需要輸入資料夾，但 {input_path} 不是資料夾")

        elif run_mode == "all":
            print("\n🎯 執行完整模式...")
            demonstrate_api_usage()
            test_configuration()
            demonstrate_batch_processing()
            example_system_check()

            # 如果路徑正確，執行實際推理
            model_path = segmentation_model_path or detection_model_path
            if os.path.exists(model_path) and os.path.exists(input_path):
                print("\n🚀 執行實際推理...")
                run_actual_inference()
            else:
                print("\n⚠️ 跳過實際推理（路徑不存在）")

        else:
            print(f"❌ 未知執行模式: {run_mode}")
            print("支援的模式: demo, inference, batch, all")
            return

    except KeyboardInterrupt:
        print("\n⏹️ 用戶中斷執行")
    except Exception as e:
        print(f"\n💥 執行失敗: {e}")
        import traceback
        traceback.print_exc()

    # 最終總結
    print("\n" + "="*70)
    print("🎉 簡化YOLO推理系統執行完成!")
    print("\n📊 重構成果:")
    print("  ✅ API簡化 79% (70參數 → 15參數)")
    print("  ✅ 代碼減少 97% (~100行 → ~3行)")
    print("  ✅ 全參數可調 (15個核心參數)")
    print("  ✅ 類別配置自動化 (auto-detect)")
    print("  ✅ 向後兼容保持")
    print("  ✅ 核心功能完整保留")
    print("\n🔗 使用指南:")
    print("  1. 修改頂部參數設定區域")
    print("  2. 設定 run_mode 選擇執行模式")
    print("  3. 直接運行: python simplified_yolo_usage.py")
    print("  4. 支援的模式: demo, inference, batch, all")


if __name__ == "__main__":
    main()
