#!/usr/bin/env python3
"""
🔍 快速診斷LabelMe JSON輸出問題
檢查為什麼有檢測結果但沒有LabelMe JSON輸出
"""

import sys
from pathlib import Path

# 設置路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_labelme_config():
    """檢查LabelMe配置"""
    print("🔍 檢查LabelMe配置...")
    
    try:
        # 讀取run_unified_yolo.py
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查enable_labelme_output參數
        if "enable_labelme_output = True" in content:
            print("✅ enable_labelme_output = True")
        elif "enable_labelme_output = False" in content:
            print("❌ enable_labelme_output = False (這是問題所在!)")
            return False
        else:
            print("⚠️ 找不到enable_labelme_output參數")
            return False
        
        # 檢查其他相關配置
        params_to_check = [
            "labelme_output_dir",
            "labelme_simplify_tolerance",
            "labelme_min_polygon_points",
            "labelme_include_confidence"
        ]
        
        for param in params_to_check:
            if param in content:
                print(f"✅ {param} 參數存在")
            else:
                print(f"⚠️ {param} 參數缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查配置失敗: {e}")
        return False

def check_integration_points():
    """檢查整合點"""
    print("\n🔍 檢查LabelMe整合點...")
    
    try:
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵整合點
        integration_points = [
            "from models.inference.labelme_integration import create_labelme_integration",
            "labelme_integration = create_labelme_integration(config_manager)",
            "labelme_integration.enabled",
            "labelme_integration.process_single_image_result"
        ]
        
        missing_points = []
        for point in integration_points:
            if point in content:
                print(f"✅ {point}")
            else:
                print(f"❌ {point} (缺失)")
                missing_points.append(point)
        
        return len(missing_points) == 0
        
    except Exception as e:
        print(f"❌ 檢查整合點失敗: {e}")
        return False

def check_debug_logs():
    """檢查調試日誌是否存在"""
    print("\n🔍 檢查調試日誌...")
    
    try:
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵調試日誌
        debug_logs = [
            "🏷️ 生成LabelMe JSON檔案...",
            "🔍 檢測結果數量:",
            "有mask數據",
            "🎯 有效mask數量:",
            "labelme_integration.process_single_image_result"
        ]
        
        found_logs = []
        for log in debug_logs:
            if log in content:
                print(f"✅ {log}")
                found_logs.append(log)
            else:
                print(f"❌ {log} (缺失)")
        
        return len(found_logs) >= 3  # 至少要有3個關鍵日誌
        
    except Exception as e:
        print(f"❌ 檢查調試日誌失敗: {e}")
        return False

def provide_specific_fix():
    """提供具體修復建議"""
    print("\n🛠️ 具體修復建議:")
    
    print("1. 檢查參數設定:")
    print("   在run_unified_yolo.py第82行左右，確認:")
    print("   enable_labelme_output = True")
    
    print("\n2. 檢查模型類型:")
    print("   確認使用的是分割模型，不是檢測模型")
    print("   模型檔名應該包含'seg'字樣")
    
    print("\n3. 檢查運行模式:")
    print("   如果是預覽模式，要設定:")
    print("   preview_only = False")
    
    print("\n4. 檢查檢測結果:")
    print("   運行時應該看到這些日誌:")
    print("   - '🔍 檢測結果數量: X'")
    print("   - '✅ Detection[0]: 有mask數據'")
    print("   - '🎯 有效mask數量: X/Y'")
    
    print("\n5. 如果仍然沒有輸出:")
    print("   可能是檢測結果中確實沒有mask數據")
    print("   請確認使用的是YOLO分割模型(.pt檔案)")

def main():
    """主診斷函數"""
    print("🔍 LabelMe JSON輸出問題快速診斷")
    print("=" * 50)
    print("🎯 診斷用戶報告的問題：有檢測結果但沒有LabelMe JSON輸出")
    print("=" * 50)
    
    checks = [
        ("LabelMe配置", check_labelme_config),
        ("整合點檢查", check_integration_points),
        ("調試日誌檢查", check_debug_logs)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}檢查異常: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 所有檢查通過！")
        print("💡 如果仍然沒有LabelMe JSON輸出，最可能的原因是:")
        print("   1. 檢測結果中確實沒有mask數據")
        print("   2. 使用的是檢測模型而非分割模型")
        print("   3. preview_only = True (只有預覽沒有推理)")
    else:
        print("❌ 發現問題，請根據上述檢查結果進行修復")
    
    provide_specific_fix()
    
    print("\n🎯 下一步:")
    print("1. 修復上述發現的問題")
    print("2. 重新運行推理系統")
    print("3. 查看是否出現LabelMe相關的日誌輸出")

if __name__ == "__main__":
    main()