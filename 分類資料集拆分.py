import os
import shutil
import random
from datetime import datetime

def split_dataset(input_dir, output_dir, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1, seed=None):
    """
    將圖像資料夾按比例拆分為train/val/test集
    
    參數:
        input_dir (str): 包含各類別子資料夾的輸入目錄
        output_dir (str): 輸出目錄，將在此創建train/val/test子目錄
        train_ratio (float): 訓練集比例
        val_ratio (float): 驗證集比例
        test_ratio (float): 測試集比例
        seed (int): 隨機種子，確保可重現性
    """
    # 驗證比例總和為1
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 0.0001, "比例總和必須為1"
    
    # 設置隨機種子
    if seed is not None:
        random.seed(seed)
    
    # 創建輸出目錄結構
    train_dir = os.path.join(output_dir, 'train')
    val_dir = os.path.join(output_dir, 'val')
    test_dir = os.path.join(output_dir, 'test')
    
    for dir_path in [train_dir, val_dir, test_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    # 初始化報告數據
    report = {
        'total_images': 0,
        'categories': {},
        'split_ratios': {
            'train': train_ratio,
            'val': val_ratio,
            'test': test_ratio
        },
        'timestamp': datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    }
    
    # 遍歷每個類別資料夾
    for category in os.listdir(input_dir):
        category_path = os.path.join(input_dir, category)
        if not os.path.isdir(category_path):
            continue
        
        # 獲取該類別所有圖像文件
        images = [f for f in os.listdir(category_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif'))]
        if not images:
            continue
        
        # 打亂圖像順序
        random.shuffle(images)
        total = len(images)
        report['total_images'] += total
        
        # 計算各集數量
        train_end = int(total * train_ratio)
        val_end = train_end + int(total * val_ratio)
        
        # 分類圖像
        splits = {
            'train': images[:train_end],
            'val': images[train_end:val_end],
            'test': images[val_end:]
        }
        
        # 記錄數量到報告
        report['categories'][category] = {
            'total': total,
            'train': len(splits['train']),
            'val': len(splits['val']),
            'test': len(splits['test'])
        }
        
        # 創建類別子目錄並複製文件
        for split_name, split_images in splits.items():
            split_category_dir = os.path.join(output_dir, split_name, category)
            os.makedirs(split_category_dir, exist_ok=True)
            
            for img in split_images:
                src = os.path.join(category_path, img)
                dst = os.path.join(split_category_dir, img)
                shutil.copy2(src, dst)
    
    return report

def generate_report(report, output_dir):
    """生成並保存報告文件"""
    report_path = os.path.join(output_dir, 'dataset_split_report.txt')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("=== 資料集拆分報告 ===\n")
        f.write(f"生成時間: {report['timestamp']}\n")
        f.write(f"總圖像數: {report['total_images']}\n")
        f.write("\n拆分比例:\n")
        f.write(f"  - 訓練集: {report['split_ratios']['train']*100:.1f}%\n")
        f.write(f"  - 驗證集: {report['split_ratios']['val']*100:.1f}%\n")
        f.write(f"  - 測試集: {report['split_ratios']['test']*100:.1f}%\n")
        
        f.write("\n各類別統計:\n")
        for category, stats in report['categories'].items():
            f.write(f"\n{category}:\n")
            f.write(f"  - 總數: {stats['total']}\n")
            f.write(f"  - 訓練集: {stats['train']} ({stats['train']/stats['total']*100:.1f}%)\n")
            f.write(f"  - 驗證集: {stats['val']} ({stats['val']/stats['total']*100:.1f}%)\n")
            f.write(f"  - 測試集: {stats['test']} ({stats['test']/stats['total']*100:.1f}%)\n")
    
    print(f"報告已生成: {report_path}")

# 使用示例
if __name__ == "__main__":
    # 使用者設定
    input_directory = r"D:\5_Hole_cover\classification\total_1"  # 替換為您的輸入目錄
    output_directory = r"D:\5_Hole_cover\classification\ai_train"     # 替換為您的輸出目錄
    train_ratio = 0.9    # 訓練集比例
    val_ratio = 0.08      # 驗證集比例
    test_ratio = 0.02     # 測試集比例
    random_seed = 42     # 隨機種子(可選)
    
    # 執行拆分
    report = split_dataset(
        input_dir=input_directory,
        output_dir=output_directory,
        train_ratio=train_ratio,
        val_ratio=val_ratio,
        test_ratio=test_ratio,
        seed=random_seed
    )
    
    # 生成報告
    generate_report(report, output_directory)