#!/usr/bin/env python3
"""
診斷用戶測試數據: D:\image\5_test_image_test\test1
"""

import sys
import json
import os
from pathlib import Path

def diagnose_test_directory():
    """診斷用戶的測試目錄"""
    print("🔍 診斷用戶測試數據")
    print("=" * 50)
    
    # 用戶的測試路徑
    test_path = Path(r"D:\image\5_test_image_test\test1")
    
    print(f"📁 測試目錄: {test_path}")
    
    # 檢查目錄是否存在
    if not test_path.exists():
        print(f"❌ 測試目錄不存在: {test_path}")
        print("💡 請確認路徑是否正確，或者路徑在WSL環境中可能需要不同的格式")
        
        # 嘗試WSL路徑格式
        wsl_path = Path("/mnt/d/image/5_test_image_test/test1")
        print(f"🔄 嘗試WSL路徑: {wsl_path}")
        
        if wsl_path.exists():
            print("✅ WSL路徑存在，使用WSL格式路徑")
            test_path = wsl_path
        else:
            print("❌ WSL路徑也不存在")
            return False
    else:
        print("✅ 目錄存在")
    
    # 列出目錄結構
    print(f"\n📂 目錄結構:")
    try:
        for item in test_path.iterdir():
            if item.is_dir():
                print(f"   📁 {item.name}/")
                # 顯示子目錄內容（前5個文件）
                try:
                    files = list(item.iterdir())[:5]
                    for file in files:
                        print(f"      📄 {file.name}")
                    if len(list(item.iterdir())) > 5:
                        print(f"      ... 還有 {len(list(item.iterdir())) - 5} 個文件")
                except PermissionError:
                    print(f"      ❌ 無法訪問")
            else:
                print(f"   📄 {item.name}")
    except PermissionError:
        print("❌ 無法訪問目錄內容")
        return False
    
    # 檢查圖像目錄
    image_dir = test_path / "image"
    label_dir = test_path / "label"
    
    print(f"\n🖼️  檢查圖像目錄: {image_dir}")
    if image_dir.exists():
        print("✅ 圖像目錄存在")
        
        # 查找圖像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        for ext in image_extensions:
            image_files.extend(image_dir.glob(f"*{ext}"))
            image_files.extend(image_dir.glob(f"*{ext.upper()}"))
        
        print(f"📊 找到 {len(image_files)} 個圖像文件")
        
        # 顯示前5個圖像文件
        for i, img_file in enumerate(image_files[:5]):
            print(f"   📷 {img_file.name}")
        
        if len(image_files) > 5:
            print(f"   ... 還有 {len(image_files) - 5} 個圖像文件")
            
    else:
        print("❌ 圖像目錄不存在")
    
    print(f"\n🏷️  檢查標註目錄: {label_dir}")
    if label_dir.exists():
        print("✅ 標註目錄存在")
        
        # 查找標註文件
        annotation_files = []
        json_files = list(label_dir.glob("*.json"))
        txt_files = list(label_dir.glob("*.txt"))
        xml_files = list(label_dir.glob("*.xml"))
        
        annotation_files.extend(json_files)
        annotation_files.extend(txt_files)
        annotation_files.extend(xml_files)
        
        print(f"📊 找到標註文件:")
        print(f"   JSON: {len(json_files)} 個")
        print(f"   TXT: {len(txt_files)} 個")
        print(f"   XML: {len(xml_files)} 個")
        print(f"   總計: {len(annotation_files)} 個")
        
        # 分析標註文件
        if annotation_files:
            print(f"\n🔬 分析標註文件:")
            
            # 分析前3個文件
            for i, ann_file in enumerate(annotation_files[:3]):
                print(f"\n   📄 文件 {i+1}: {ann_file.name}")
                analyze_annotation_file(ann_file)
            
            if len(annotation_files) > 3:
                print(f"\n   ... 還有 {len(annotation_files) - 3} 個標註文件")
        else:
            print("❌ 未找到任何標註文件")
    else:
        print("❌ 標註目錄不存在")
    
    # 檢查圖像和標註的對應關係
    print(f"\n🔗 檢查圖像和標註對應關係:")
    if image_dir.exists() and label_dir.exists():
        check_image_annotation_matching(image_dir, label_dir)
    
    return True

def analyze_annotation_file(file_path):
    """分析單個標註文件"""
    try:
        if file_path.suffix.lower() == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"      ✅ JSON格式正確")
            print(f"      📐 圖像信息: {data.get('imagePath', 'N/A')}")
            
            if 'imageWidth' in data and 'imageHeight' in data:
                print(f"      📏 尺寸: {data['imageWidth']}x{data['imageHeight']}")
            
            shapes = data.get('shapes', [])
            print(f"      🏷️  標註數量: {len(shapes)} 個")
            
            # 分析每個shape
            class_names = set()
            for j, shape in enumerate(shapes):
                label = shape.get('label', 'unknown')
                shape_type = shape.get('shape_type', 'unknown')
                points = shape.get('points', [])
                
                class_names.add(label)
                print(f"         Shape {j+1}: {label} ({shape_type}) - {len(points)} 點")
                
                # 嘗試計算邊界框
                if shape_type == "rectangle" and len(points) == 2:
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    bbox = [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
                    area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                    print(f"             邊界框: {[int(x) for x in bbox]}, 面積: {int(area)}")
                elif shape_type == "polygon" and points:
                    if isinstance(points[0], list):
                        xs = [p[0] for p in points]
                        ys = [p[1] for p in points]
                    else:
                        xs = points[::2]
                        ys = points[1::2]
                    
                    if xs and ys:
                        bbox = [min(xs), min(ys), max(xs), max(ys)]
                        area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                        print(f"             邊界框: {[int(x) for x in bbox]}, 面積: {int(area)}")
            
            print(f"      🎯 類別統計: {list(class_names)}")
            
        elif file_path.suffix.lower() == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"      ✅ TXT格式，行數: {len(lines)}")
            
            class_ids = set()
            valid_lines = 0
            for line in lines:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            class_id = int(parts[0])
                            class_ids.add(class_id)
                            valid_lines += 1
                        except ValueError:
                            pass
            
            print(f"      📊 有效行數: {valid_lines}")
            print(f"      🎯 類別ID: {sorted(list(class_ids))}")
        
        elif file_path.suffix.lower() == '.xml':
            print(f"      📄 XML格式（需要進一步解析）")
    
    except json.JSONDecodeError as e:
        print(f"      ❌ JSON解析錯誤: {e}")
    except Exception as e:
        print(f"      ❌ 文件分析錯誤: {e}")

def check_image_annotation_matching(image_dir, label_dir):
    """檢查圖像和標註文件的對應關係"""
    
    # 獲取所有圖像文件名（不含擴展名）
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_stems = set()
    
    for ext in image_extensions:
        for img_file in image_dir.glob(f"*{ext}"):
            image_stems.add(img_file.stem)
        for img_file in image_dir.glob(f"*{ext.upper()}"):
            image_stems.add(img_file.stem)
    
    # 獲取所有標註文件名（不含擴展名）
    annotation_extensions = ['.json', '.txt', '.xml']
    annotation_stems = set()
    
    for ext in annotation_extensions:
        for ann_file in label_dir.glob(f"*{ext}"):
            annotation_stems.add(ann_file.stem)
    
    # 檢查匹配
    matched = image_stems & annotation_stems
    image_only = image_stems - annotation_stems
    annotation_only = annotation_stems - image_stems
    
    print(f"   📊 匹配統計:")
    print(f"      ✅ 有標註的圖像: {len(matched)} 個")
    print(f"      📷 僅有圖像: {len(image_only)} 個")
    print(f"      🏷️  僅有標註: {len(annotation_only)} 個")
    
    if matched:
        print(f"   🎯 匹配的文件示例:")
        for stem in sorted(list(matched))[:3]:
            print(f"      - {stem}")
        if len(matched) > 3:
            print(f"      ... 還有 {len(matched) - 3} 個匹配")
    
    if image_only:
        print(f"   ⚠️  無標註的圖像示例:")
        for stem in sorted(list(image_only))[:3]:
            print(f"      - {stem}")
    
    if annotation_only:
        print(f"   ⚠️  無對應圖像的標註:")
        for stem in sorted(list(annotation_only))[:3]:
            print(f"      - {stem}")

def provide_solutions():
    """提供解決方案"""
    print(f"\n💡 GT標註顯示問題解決方案:")
    print(f"=" * 40)
    
    print(f"🔧 1. 檢查Enhanced YOLO配置:")
    print(f"   - 確保 labelme_dir 參數指向正確的標註目錄")
    print(f"   - 檢查 auto_generate_classes 是否啟用")
    print(f"   - 驗證類別配置是否包含標註中的所有類別")
    
    print(f"\n🔧 2. 檢查文件路徑:")
    print(f"   - Windows路徑: D:\\image\\5_test_image_test\\test1")
    print(f"   - WSL路徑: /mnt/d/image/5_test_image_test/test1")
    print(f"   - 確保enhanced_yolo_usage.py中使用正確的路徑格式")
    
    print(f"\n🔧 3. 啟用詳細日誌:")
    print(f"   在enhanced_yolo_usage.py中添加:")
    print(f"   import logging")
    print(f"   logging.basicConfig(level=logging.DEBUG)")
    
    print(f"\n🔧 4. 檢查具體錯誤:")
    print(f"   運行Enhanced YOLO時查看日誌中的:")
    print(f"   - '🔍 開始載入GT標註' 相關信息")
    print(f"   - 類別匹配錯誤")
    print(f"   - 文件讀取錯誤")
    
    print(f"\n🔧 5. 驗證標註格式:")
    print(f"   - 確保JSON文件格式正確（LabelMe格式）")
    print(f"   - 確保TXT文件格式正確（YOLO格式）")
    print(f"   - 檢查類別名稱是否與配置一致")

def main():
    """主函數"""
    success = diagnose_test_directory()
    
    if success:
        provide_solutions()
    
    print(f"\n📋 下一步建議:")
    print(f"1. 根據上述分析結果，修正發現的問題")
    print(f"2. 啟用DEBUG日誌運行Enhanced YOLO")
    print(f"3. 查看詳細的GT載入日誌信息")
    print(f"4. 如果問題持續，請提供具體的錯誤日誌")

if __name__ == "__main__":
    main()