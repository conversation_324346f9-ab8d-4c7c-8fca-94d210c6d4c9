"""
註冊系統：統一管理所有模型組件
支持動態註冊和查詢編碼器、解碼器和完整模型
"""

from typing import Dict, Any, Callable, Type
import torch.nn as nn


class Registry:
    """通用註冊系統"""
    
    def __init__(self, name: str):
        self._name = name
        self._registry: Dict[str, Type[nn.Module]] = {}
        
    def register(self, name: str = None):
        """註冊裝飾器"""
        def decorator(cls: Type[nn.Module]):
            register_name = name or cls.__name__
            if register_name in self._registry:
                raise ValueError(f"模型 {register_name} 已存在於 {self._name} 註冊表中")
            self._registry[register_name] = cls
            return cls
        return decorator
    
    def get(self, name: str) -> Type[nn.Module]:
        """獲取註冊的模型類別"""
        if name not in self._registry:
            raise KeyError(f"模型 {name} 未在 {self._name} 註冊表中找到。"
                          f"可用模型: {list(self._registry.keys())}")
        return self._registry[name]
    
    def list_available(self) -> list:
        """列出所有可用的模型"""
        return list(self._registry.keys())
    
    def contains(self, name: str) -> bool:
        """檢查模型是否已註冊"""
        return name in self._registry


# 創建各種註冊表
ENCODER_REGISTRY = Registry("Encoder")
DECODER_REGISTRY = Registry("Decoder")
MODEL_REGISTRY = Registry("Model")


def register_encoder(name: str = None):
    """編碼器註冊裝飾器"""
    return ENCODER_REGISTRY.register(name)


def register_decoder(name: str = None):
    """解碼器註冊裝飾器"""
    return DECODER_REGISTRY.register(name)


def register_model(name: str = None):
    """完整模型註冊裝飾器"""
    return MODEL_REGISTRY.register(name)


def get_encoder(name: str) -> Type[nn.Module]:
    """獲取編碼器類別"""
    return ENCODER_REGISTRY.get(name)


def get_decoder(name: str) -> Type[nn.Module]:
    """獲取解碼器類別"""
    return DECODER_REGISTRY.get(name)


def get_model(name: str) -> Type[nn.Module]:
    """獲取模型類別"""
    return MODEL_REGISTRY.get(name)


def list_encoders() -> list:
    """列出所有可用編碼器"""
    return ENCODER_REGISTRY.list_available()


def list_decoders() -> list:
    """列出所有可用解碼器"""
    return DECODER_REGISTRY.list_available()


def list_models() -> list:
    """列出所有可用模型"""
    return MODEL_REGISTRY.list_available()