{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rg:*)", "<PERSON><PERSON>(true)", "WebFetch(domain:github.com)", "Bash(rm:*)", "mcp__repomix__pack_codebase", "mcp__repomix__grep_repomix_output", "mcp__repomix__grep_repomix_output", "mcp__repomix__grep_repomix_output", "mcp__repomix__grep_repomix_output", "mcp__tavily-mcp__tavily-search", "<PERSON><PERSON>(diff:*)", "Bash(pip3 install:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(cp:*)", "mcp__repomix__read_repomix_output", "Bash(awk:*)", "mcp__repomix__file_system_read_directory", "mcp__repomix__file_system_read_file", "Bash(sed -n '441,2000p' \"/mnt/d/99_AI_model/AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, '.')\nfrom AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import modify_class_confidence\nprint('✅ modify_class_confidence 函數成功導入')\nprint('函數簽名:', modify_class_confidence.__doc__.split('\\n')[0:3])\n\")", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:docs.ultralytics.com)"], "deny": []}, "enableAllProjectMcpServers": false}