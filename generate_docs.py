#!/usr/bin/env python3
"""
API文檔生成腳本
自動掃描整個項目並生成完整的API文檔
"""

import os
import sys
import logging
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

from AI模型建構訓練驗證.model_create.util.doc_generator import (
    DocConfig, DocGenerator, generate_api_docs
)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函數 - 生成完整的API文檔"""
    
    logger.info("開始生成道路基礎設施AI檢測框架API文檔")
    
    # 項目根目錄
    project_root = Path(__file__).parent
    output_dir = project_root / "docs" / "api"
    
    # 創建輸出目錄
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置文檔生成器
    config = DocConfig(
        output_dir=str(output_dir),
        output_format="markdown",
        
        # 掃描配置
        scan_patterns=[
            "AI模型建構訓練驗證/**/*.py",
            "資料前處理/**/*.py",
            "!**/old_code/**",
            "!**/tests/**",
            "!**/__pycache__/**",
            "!**/.*/**"
        ],
        
        # 生成配置
        include_private=False,
        include_examples=True,
        include_source_code=False,
        generate_index=True,
        
        # 分類配置
        factory_pattern=r"create_.*|.*Factory",
        config_pattern=r".*Config$",
        model_pattern=r".*Model$|.*Network$|.*Backbone$|.*Mamba.*|.*Transformer.*|.*CNN.*|.*VIT.*",
        
        # 語言配置
        language="zh-CN",
        encoding="utf-8"
    )
    
    try:
        # 生成文檔
        generator = DocGenerator(config)
        result = generator.generate_docs(str(project_root))
        
        # 輸出結果
        logger.info("=== 文檔生成完成 ===")
        logger.info(f"輸出目錄: {result['output_dir']}")
        logger.info(f"掃描模組數: {result['total_modules']}")
        logger.info(f"生成文件: {', '.join(result['generated_files'])}")
        
        logger.info("分類統計:")
        for category, count in result['categorized'].items():
            if count > 0:
                logger.info(f"  {category}: {count} 個模組")
        
        # 生成使用指南
        generate_usage_guide(output_dir)
        
        # 生成範例文檔
        generate_examples_doc(output_dir)
        
        logger.info("=== 所有文檔生成完成 ===")
        logger.info(f"請查看: {output_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"文檔生成失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def generate_usage_guide(output_dir: Path):
    """生成使用指南"""
    
    usage_guide = """# 使用指南

## 快速開始

### 1. 模型創建

使用工廠函數創建模型：

```python
from AI模型建構訓練驗證.model_create.core import ModelFactory

# 創建CSP_IFormer模型
factory = ModelFactory()
model = factory.create_model({
    "model_type": "csp_iformer",
    "variant": "final_segmentation",
    "num_classes": 5
})

# 或使用直接工廠函數
from AI模型建構訓練驗證.model_create.encoder.VIT.CSP_IFormer_final_SegMode import create_csp_iformer_seg

model = create_csp_iformer_seg(num_classes=5)
```

### 2. Vision Mamba架構

```python
from AI模型建構訓練驗證.model_create.encoder.mamba.vision_mamba_core import (
    create_vision_mamba_tiny, create_vision_mamba_small, create_vision_mamba_base
)

# 創建不同規模的Vision Mamba模型
model_tiny = create_vision_mamba_tiny(num_classes=5)
model_small = create_vision_mamba_small(num_classes=5)
model_base = create_vision_mamba_base(num_classes=5)
```

### 3. 統一訓練系統

```python
from AI模型建構訓練驗證.model_create.training import UnifiedTrainer, TrainingConfig

# 配置訓練
config = TrainingConfig(
    epochs=100,
    enable_mixed_precision=True,
    gradient_accumulation_steps=4,
    enable_early_stopping=True
)

# 創建訓練器
trainer = UnifiedTrainer(model, optimizer, config=config)
history = trainer.fit(train_loader, val_loader)
```

### 4. 增強YOLO推理

```python
from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
    create_enhanced_yolo_inference, EnhancedYOLOConfig
)

# 創建推理配置
config = EnhancedYOLOConfig(
    detection_model_path="./models/yolo12.pt",
    segmentation_model_path="./models/yolo11_seg.pt",
    enable_sahi=True,
    auto_convert_annotations=True
)

# 創建推理器
inference = create_enhanced_yolo_inference(config)

# 推理
results = inference.predict_single_image(
    image_path="./test_image/road.jpg",
    task_type="both"
)
```

### 5. 資料前處理

```python
from 資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2

# 標註格式轉換
converter = AnnotationConverterV2(
    input_dir='./labelme_data',
    output_dir='./yolo_data',
    input_format='auto',
    output_format='yolo'
)

result = converter.run()
```

## 配置驅動開發

### 使用YAML配置

```yaml
# model_config.yaml
model:
  type: "csp_iformer"
  variant: "final_segmentation"
  num_classes: 5
  backbone_config:
    enable_channel_shuffle: true
    enable_dropkey: true
    dropout_rate: 0.3

training:
  epochs: 100
  batch_size: 32
  learning_rate: 0.001
  enable_mixed_precision: true
```

```python
# 加載配置
import yaml
with open('model_config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 使用配置創建模型
model = factory.create_model(config['model'])
```

## 分散式訓練

```python
from AI模型建構訓練驗證.model_create.distributed import (
    create_ray_training_system, RayTrainingConfig
)

# 創建Ray訓練系統
manager, training_system = create_ray_training_system()

# 分散式訓練配置
ray_config = RayTrainingConfig(
    num_workers=4,
    use_gpu=True,
    resources_per_worker={'CPU': 2, 'GPU': 0.25}
)

# 執行分散式訓練
result = training_system.run_distributed_training(
    model_config, dataset_config, ray_config, train_config
)
```

## 最佳實踐

1. **優先使用工廠函數**: 確保一致性和可維護性
2. **配置驅動**: 使用YAML/JSON配置文件而非硬編碼
3. **類型提示**: 利用完整的類型註釋進行開發
4. **錯誤處理**: 使用內建的異常處理機制
5. **測試驗證**: 使用test_image進行功能驗證

## 故障排除

### 常見問題

1. **CUDA記憶體不足**
   ```python
   config = TrainingConfig(enable_mixed_precision=True, gradient_accumulation_steps=4)
   ```

2. **模組導入錯誤**
   ```bash
   export PYTHONPATH=$PYTHONPATH:/path/to/99_AI_model
   ```

3. **中文字體問題**
   ```python
   matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei']
   ```
"""
    
    with open(output_dir / "usage_guide.md", 'w', encoding='utf-8') as f:
        f.write(usage_guide)


def generate_examples_doc(output_dir: Path):
    """生成範例文檔"""
    
    examples_doc = """# 範例集合

## 1. 模型創建範例

### CSP_IFormer模型

```python
from AI模型建構訓練驗證.model_create.encoder.VIT.CSP_IFormer_final_SegMode import create_csp_iformer_seg
from AI模型建構訓練驗證.model_create.encoder.VIT.CSP_IFormer_final_ClsMode import create_csp_iformer_cls

# 分割模式
seg_model = create_csp_iformer_seg(
    num_classes=5,
    enable_channel_shuffle=True,
    enable_dropkey=True,
    dropout_rate=0.3
)

# 分類模式
cls_model = create_csp_iformer_cls(
    num_classes=5,
    enable_channel_shuffle=True,
    enable_dropkey=True,
    dropout_rate=0.3
)
```

### Vision Mamba模型

```python
from AI模型建構訓練驗證.model_create.encoder.mamba.vision_mamba_core import VisionMambaConfig, VisionMamba

# 自定義配置
config = VisionMambaConfig(
    d_model=768,
    d_state=16,
    img_size=224,
    patch_size=16,
    num_classes=5,
    depths=[2, 2, 9, 2],
    dims=[96, 192, 384, 768],
    bidirectional=True
)

model = VisionMamba(config)
```

## 2. 訓練範例

### 統一訓練系統

```python
from AI模型建構訓練驗證.model_create.training import UnifiedTrainer, TrainingConfig
from AI模型建構訓練驗證.model_create.training.optimizers import OptimizerFactory

# 創建模型
model = create_csp_iformer_seg(num_classes=5)

# 創建優化器
optimizer_factory = OptimizerFactory()
optimizer = optimizer_factory.create_optimizer('adamw', model.parameters(), lr=0.001)

# 訓練配置
config = TrainingConfig(
    epochs=100,
    enable_mixed_precision=True,
    gradient_accumulation_steps=4,
    enable_early_stopping=True,
    early_stopping_patience=10,
    save_best_only=True
)

# 創建訓練器
trainer = UnifiedTrainer(model, optimizer, config=config)

# 開始訓練
history = trainer.fit(train_loader, val_loader)
```

### 分散式訓練

```python
from AI模型建構訓練驗證.model_create.distributed import create_ray_training_system, RayTrainingConfig

# 初始化Ray
manager, training_system = create_ray_training_system()

# 分散式配置
ray_config = RayTrainingConfig(
    num_workers=4,
    use_gpu=True,
    resources_per_worker={'CPU': 2, 'GPU': 0.25}
)

# 模型配置
model_config = {
    'model_type': 'csp_iformer',
    'num_classes': 5
}

# 數據配置
dataset_config = {
    'train_dir': './train',
    'val_dir': './val',
    'batch_size': 32
}

# 執行分散式訓練
result = training_system.run_distributed_training(
    model_config, dataset_config, ray_config, config
)
```

## 3. 推理範例

### 增強YOLO推理

```python
from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import *

# 單張圖像推理
config = EnhancedYOLOConfig(
    detection_model_path="./models/yolo12.pt",
    segmentation_model_path="./models/yolo11_seg.pt",
    enable_sahi=True,
    save_visualizations=True
)

inference = create_enhanced_yolo_inference(config)
results = inference.predict_single_image(
    image_path="./test_image/road.jpg",
    task_type="both"
)

# 批次推理
batch_results = inference.batch_predict(
    input_dir="./test_images",
    output_dir="./output",
    task_type="detection"
)
```

### 使用GUI界面

```python
from AI模型建構訓練驗證.enhanced_yolo_gui import EnhancedYOLOGUI
import sys
from PyQt6.QtWidgets import QApplication

app = QApplication(sys.argv)
gui = EnhancedYOLOGUI()
gui.show()
sys.exit(app.exec())
```

## 4. 資料處理範例

### 標註轉換

```python
from 資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2

# LabelMe到YOLO轉換
converter = AnnotationConverterV2(
    input_dir='./labelme_annotations',
    output_dir='./yolo_annotations',
    input_format='labelme',
    output_format='yolo',
    config={
        'batch_size': 100,
        'enable_validation': True,
        'preserve_hierarchy': True
    }
)

result = converter.run()
print(f"轉換完成: {result['processed_files']}/{result['total_files']}")
```

### 圖像增強

```python
from 資料前處理.img_augmenter import ImageAugmenter

augmenter = ImageAugmenter(
    source_dirs=['./objects/'],
    target_dir='./augmented/',
    task_type='both'
)

# 配置網格放置策略
augmenter.configure_placement(
    enable_grid_placement=True,
    avoid_similar_positions=True,
    min_distance_ratio=0.1
)

augmented_data = augmenter.run()
```

## 5. 測試範例

### 使用test_image測試

```python
from tests.base_test import BaseTestCase

class TestMyModel(BaseTestCase):
    def test_csp_iformer_inference(self):
        # 使用真實測試數據
        image, annotation, key = self.get_image_annotation_pair()
        
        # 創建模型
        model = create_csp_iformer_seg(num_classes=5)
        
        # 推理測試
        with torch.no_grad():
            output = model(image.unsqueeze(0))
        
        # 驗證輸出
        self.assert_output_valid(output)
        self.assert_shape_correct(output, expected_shape=(1, 5, 224, 224))
```

## 6. 配置檔案範例

### 模型配置 (model_config.yaml)

```yaml
model:
  type: "csp_iformer"
  variant: "final_segmentation"
  num_classes: 5
  backbone_config:
    enable_channel_shuffle: true
    enable_dropkey: true
    dropout_rate: 0.3

training:
  epochs: 100
  batch_size: 32
  learning_rate: 0.001
  optimizer: "adamw"
  scheduler: "cosine"
  enable_mixed_precision: true
  gradient_accumulation_steps: 4

data:
  train_dir: "./train"
  val_dir: "./val"
  image_size: 224
  augmentation:
    horizontal_flip: true
    vertical_flip: false
    rotation: 15
```

### 推理配置 (inference_config.yaml)

```yaml
detection_model_path: "./models/yolo12.pt"
segmentation_model_path: "./models/yolo11_seg.pt"
device: "auto"

class_configs:
  0:
    name: "裂縫"
    conf_threshold: 0.5
    color: [255, 0, 0]
    enabled: true
  1:
    name: "坑洞"
    conf_threshold: 0.4
    color: [0, 255, 0]
    enabled: true

sahi:
  enable_sahi: true
  slice_height: 512
  slice_width: 512
  overlap_height_ratio: 0.2
  overlap_width_ratio: 0.2

output:
  save_visualizations: true
  save_predictions: true
  output_format: "both"
```

這些範例涵蓋了框架的主要功能，可以作為開發的起點。
"""
    
    with open(output_dir / "examples.md", 'w', encoding='utf-8') as f:
        f.write(examples_doc)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)