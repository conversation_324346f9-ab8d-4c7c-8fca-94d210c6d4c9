import numpy as np
import sklearn.metrics as skm

def get_metrics(gt_label, pred_label):
    """
    計算評估指標
    
    參數:
        gt_label: 真實標籤（展平的陣列）
        pred_label: 預測標籤（展平的陣列）
        
    返回:
        包含準確率和 Jaccard 分數的元組
    """
    # 確保輸入是 NumPy 陣列
    if not isinstance(gt_label, np.ndarray):
        gt_label = np.array(gt_label)
    if not isinstance(pred_label, np.ndarray):
        pred_label = np.array(pred_label)
    
    # 移除無效標籤（如果有的話）
    mask = (gt_label >= 0)
    gt_valid = gt_label[mask]
    pred_valid = pred_label[mask]
    
    # 如果沒有有效標籤，返回零值
    if len(gt_valid) == 0:
        return (0.0, 0.0)
    
    # 計算準確率
    acc = skm.accuracy_score(gt_valid, pred_valid, normalize=True)
    
    # 嘗試計算 Jaccard 分數，處理可能的警告或錯誤
    try:
        # 對於二分類問題，使用 'binary'，否則使用 'macro'
        if np.unique(gt_valid).size <= 2 and np.unique(pred_valid).size <= 2:
            js = skm.jaccard_score(gt_valid, pred_valid, average='binary', zero_division=0)
        else:
            js = skm.jaccard_score(gt_valid, pred_valid, average='macro', zero_division=0)
    except Exception as e:
        print(f"計算 Jaccard 分數時出錯: {e}")
        js = 0.0
    
    return (acc, js)


class runningScore(object):
    def __init__(self, n_classes):
        """
        初始化運行評分類
        
        參數:
            n_classes: 類別數量
        """
        self.n_classes = n_classes
        self.confusion_matrix = np.zeros((n_classes, n_classes))
    
    def _fast_hist(self, label_true, label_pred, n_class):
        """
        快速計算混淆矩陣
        
        參數:
            label_true: 真實標籤（展平的陣列）
            label_pred: 預測標籤（展平的陣列）
            n_class: 類別數量
            
        返回:
            混淆矩陣
        """
        # 只考慮有效的標籤（0 到 n_class-1 之間）
        mask = (label_true >= 0) & (label_true < n_class)
        hist = np.bincount(
            n_class * label_true[mask].astype(int) + label_pred[mask], 
            minlength=n_class ** 2
        ).reshape(n_class, n_class)
        return hist
    
    def update(self, label_trues, label_preds):
        """
        更新混淆矩陣
        
        參數:
            label_trues: 真實標籤列表
            label_preds: 預測標籤列表
        """
        # 確保輸入是 NumPy 陣列
        if isinstance(label_trues, np.ndarray) and label_trues.ndim == 3:
            label_trues = [label_trues[i] for i in range(label_trues.shape[0])]
        if isinstance(label_preds, np.ndarray) and label_preds.ndim == 3:
            label_preds = [label_preds[i] for i in range(label_preds.shape[0])]
        
        for lt, lp in zip(label_trues, label_preds):
            # 確保標籤是 NumPy 陣列
            if not isinstance(lt, np.ndarray):
                lt = np.array(lt)
            if not isinstance(lp, np.ndarray):
                lp = np.array(lp)
                
            # 確保預測標籤在有效範圍內
            lp = np.clip(lp, 0, self.n_classes - 1)
            
            # 更新混淆矩陣
            self.confusion_matrix += self._fast_hist(lt.flatten(), lp.flatten(), self.n_classes)
    
    def get_scores(self):
        """
        計算評估指標，增加每個類別的單獨計算
        
        返回:
            包含各種評估指標的字典
        """
        # 獲取混淆矩陣
        hist = self.confusion_matrix
        
        # 處理空的混淆矩陣
        if np.sum(hist) == 0:
            empty_class_metrics = {
                "Specificity_per_class": np.zeros(self.n_classes),
                "Sensitivity_per_class": np.zeros(self.n_classes),
                "F1_per_class": np.zeros(self.n_classes),
                "Precision_per_class": np.zeros(self.n_classes),
                "Pixel_Accuracy_per_class": np.zeros(self.n_classes),
                "Dice_per_class": np.zeros(self.n_classes),
            }
            
            return {
                "confusion_matrix": hist,
                "TP": np.zeros(self.n_classes),
                "TN": np.zeros(self.n_classes),
                "FP": np.zeros(self.n_classes),
                "FN": np.zeros(self.n_classes),
                "Specificity": 0.0,
                "Sensitivity": 0.0,
                "F1": 0.0,
                "Precision": 0.0,
                "Pixel_Accuracy": 0.0,
                "IoU": np.zeros(self.n_classes),
                "MIoU": 0.0,
                **empty_class_metrics
            }
        
        # 計算 TP, TN, FP, FN
        TP = np.diag(hist)
        FP = hist.sum(axis=0) - TP
        FN = hist.sum(axis=1) - TP
        TN = hist.sum() - TP - FP - FN
        
        # 避免除以零
        epsilon = 1e-6
        
        # 計算每個類別的特異性: TN / (TN + FP)
        specif_cls = TN / (TN + FP + epsilon)
        specif = np.nanmean(specif_cls)
        
        # 計算每個類別的敏感性/召回率: TP / (TP + FN)
        sensti_cls = TP / (TP + FN + epsilon)
        sensti = np.nanmean(sensti_cls)
        
        # 計算每個類別的精確度: TP / (TP + FP)
        prec_cls = TP / (TP + FP + epsilon)
        prec = np.nanmean(prec_cls)
        
        # 計算每個類別的像素準確率: (TP + TN) / (TP + TN + FP + FN)
        pixel_acc_cls = (TP + TN) / (TP + TN + FP + FN + epsilon)
        pixel_acc = np.nanmean(pixel_acc_cls)
        
        # 計算每個類別的 IoU: TP / (TP + FP + FN)
        iou = TP / (TP + FP + FN + epsilon)
        
        # 計算平均 IoU
        Miou = np.nanmean(iou)
        
        # 計算每個類別的 F1 分數: 2 * (Precision * Recall) / (Precision + Recall)
        f1_cls = (2 * prec_cls * sensti_cls) / (prec_cls + sensti_cls + epsilon)
        f1 = np.nanmean(f1_cls)
        
        # 計算每個類別的 Dice 系數: 2 * TP / (2 * TP + FP + FN)
        dice_cls = (2 * TP) / (2 * TP + FP + FN + epsilon)
        
        # 返回所有指標
        return {
            "confusion_matrix": self.confusion_matrix,
            "TP": TP,
            "TN": TN,
            "FP": FP,
            "FN": FN,
            "Specificity": float(specif),
            "Sensitivity": float(sensti),
            "F1": float(f1),
            "Precision": float(prec),
            "Pixel_Accuracy": float(pixel_acc),
            "IoU": iou,
            "MIoU": float(Miou),
            # 每個類別的單獨指標
            "Specificity_per_class": specif_cls,
            "Sensitivity_per_class": sensti_cls,
            "F1_per_class": f1_cls,
            "Precision_per_class": prec_cls,
            "Pixel_Accuracy_per_class": pixel_acc_cls,
            "Dice_per_class": dice_cls
        }
    
    def reset(self):
        """重置混淆矩陣"""
        self.confusion_matrix = np.zeros((self.n_classes, self.n_classes))