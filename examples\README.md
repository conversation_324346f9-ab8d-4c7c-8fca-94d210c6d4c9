# Examples - 使用示例

本目錄包含了現代化道路基礎設施AI檢測框架的完整使用示例，所有示例都可以直接修改參數並執行。

## 📚 示例概覽

### 🚀 [enhanced_yolo_usage.py](enhanced_yolo_usage.py)
**Enhanced YOLO推理系統使用示例**
- YOLO11分割 + YOLO12檢測雙模型支援
- SAHI大圖像切片推理
- LabelMe自動類別配置
- 道路基礎設施專業檢測

### 🧠 [vision_mamba_usage.py](vision_mamba_usage.py)  
**Vision Mamba使用示例**
- 2024年ICML最佳論文架構
- 線性複雜度O(n)突破
- Tiny/Small/Base三種規模
- 高解析度圖像處理優勢

### 📁 [data_preprocessing.py](data_preprocessing.py)
**資料前處理系統使用示例**
- 智能格式檢測與轉換
- 策略模式標註轉換
- 數據增強和數據集分割
- 全景圖像處理(OPK旋轉)

### 🎓 [unified_training.py](unified_training.py)
**統一訓練系統使用示例**
- 整合90%+重複功能
- 混合精度訓練(節省30-50%記憶體)
- 智能早停和檢查點管理
- Ray分散式訓練支援

### 🏭 [csp_iformer_usage.py](csp_iformer_usage.py)
**CSP_IFormer原創架構使用示例**
- Channel Shuffle + DropKey創新技術
- CSP連接 + IFormer塊
- 分割/分類雙模式支援
- 論文發表級別的原創架構

## 🔧 使用方法

### 📋 標準使用流程

1. **選擇示例文件**：根據需求選擇對應的示例
2. **修改參數設定**：編輯文件中的參數設定區域
3. **直接運行**：`python example_file.py`

### 📝 參數設定示例

所有示例都採用統一的參數設定模式：

```python
# ===================================================================
# 📋 參數設定區域 - 請根據您的需求修改以下參數
# ===================================================================

# 🎯 基礎配置
model_path = "path/to/your/model.pt"          # 修改為您的模型路徑
input_path = "./test_image"                   # 修改為您的輸入路徑
output_path = "./output"                      # 修改為您的輸出路徑

# 🎛️ 處理參數
batch_size = 16                               # 根據GPU記憶體調整
learning_rate = 1e-4                          # 根據任務需求調整
enable_sahi = True                            # 啟用/禁用特定功能
```

## 🚀 快速開始指南

### 1分鐘體驗Enhanced YOLO
```bash
# 1. 修改模型路徑
vim examples/enhanced_yolo_usage.py
# 編輯 segmentation_model_path = "your_model_path"

# 2. 運行
python examples/enhanced_yolo_usage.py
```

### 5分鐘體驗Vision Mamba
```bash
# 1. 查看配置
cat examples/vision_mamba_usage.py

# 2. 修改參數並運行
python examples/vision_mamba_usage.py
```

### 完整流程體驗 (資料處理→訓練→推理)
```bash
# 1. 資料前處理
python examples/data_preprocessing.py

# 2. 統一訓練
python examples/unified_training.py

# 3. Enhanced YOLO推理
python examples/enhanced_yolo_usage.py
```

## ⚙️ 環境需求

### 基礎依賴
```bash
pip install torch torchvision
pip install opencv-python numpy matplotlib
pip install ultralytics sahi
```

### 進階功能 (可選)
```bash
pip install PyQt6 qdarkstyle          # GUI功能
pip install ray[tune,train]           # 分散式訓練
pip install tensorboard               # 訓練監控
```

## 📊 示例功能對照表

| 示例文件 | 主要功能 | 適用場景 | 依賴需求 |
|---------|---------|---------|---------|
| enhanced_yolo_usage.py | 道路檢測推理 | 生產部署 | torch, ultralytics, sahi |
| vision_mamba_usage.py | 線性複雜度模型 | 高解析度處理 | torch |
| data_preprocessing.py | 資料前處理 | 數據準備 | opencv, numpy |
| unified_training.py | 模型訓練 | 研發訓練 | torch, tensorboard |
| csp_iformer_usage.py | 原創架構 | 學術研究 | torch, yaml |

## 🔧 故障排除

### 常見問題

#### 1. 模組導入失敗
```bash
❌ 錯誤: ModuleNotFoundError: No module named 'AI模型建構訓練驗證'
✅ 解決: 確保在專案根目錄運行，使用統一導入管理系統
```

#### 2. CUDA記憶體不足
```python
# 在示例文件中調整參數
batch_size = 4              # 減少批次大小
enable_mixed_precision = True  # 啟用混合精度
```

#### 3. 模型路徑不存在
```python
# 檢查並修改模型路徑
model_path = "path/to/your/actual/model.pt"
```

#### 4. 參數衝突錯誤
```bash
❌ 錯誤: got multiple values for keyword argument 'depths'
✅ 解決: 工廠函數已內建預設參數，無需手動傳遞depths、dims等架構參數
```

#### 5. 錯誤的模組導入
```bash
❌ 錯誤: No module named 'model_create.Models'
✅ 解決: 已修復util模組中的錯誤導入路徑，Vision Mamba使用內建DropPath實現
```

#### 6. Vision Mamba奇數尺寸問題
```bash
❌ 錯誤: x size (7*7) are not even.
✅ 解決: 自動檢測奇數尺寸並添加padding，支援任意輸入尺寸
```

### 調試建議

1. **檢查依賴**：運行前確保所有依賴已安裝
2. **檢查路徑**：確認輸入/輸出路徑存在
3. **檢查GPU**：確認CUDA環境正常
4. **查看日誌**：所有示例都有詳細的執行日誌

## 💡 使用技巧

### 參數調優建議

- **記憶體不足**：減少batch_size，啟用混合精度
- **速度較慢**：使用smaller模型變體，啟用CUDA
- **精度不佳**：增加訓練輪數，調整學習率
- **數據不足**：啟用數據增強，使用預訓練模型

### 最佳實踐

1. **逐步驗證**：先運行基礎示例，再嘗試進階功能
2. **參數記錄**：記錄有效的參數組合
3. **結果比較**：對比不同模型和參數的效果
4. **定期備份**：保存訓練好的模型和配置

## 🔗 相關文檔

- [CLAUDE.md](../CLAUDE.md) - 專案總覽和技術文檔
- [import_helper.py](../import_helper.py) - 統一導入管理系統
- [AI模型建構訓練驗證/](../AI模型建構訓練驗證/) - 核心AI架構
- [資料前處理/](../資料前處理/) - 資料處理工具

## 🎯 下一步

1. **選擇示例**：根據需求選擇合適的示例文件
2. **修改參數**：根據實際情況調整參數設定
3. **運行測試**：執行示例並檢查結果
4. **深入研究**：查閱源代碼了解實現細節
5. **自定義開發**：基於示例開發自己的應用

---

**提示**: 所有示例都設計為可直接修改和執行，無需複雜的命令行參數。如果遇到問題，請檢查參數設定區域的配置是否正確。