#!/usr/bin/env python3
"""
測試GT載入功能
"""

import sys
from pathlib import Path

# 添加專案路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from import_helper import setup_project_paths, ENHANCED_YOLO_AVAILABLE
setup_project_paths()

def test_gt_loading():
    """測試GT載入功能"""
    
    if not ENHANCED_YOLO_AVAILABLE:
        print("❌ Enhanced YOLO模組不可用")
        return
    
    try:
        from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
            EnhancedYOLOConfig,
            generate_class_configs_from_labelme,
            create_enhanced_yolo_inference
        )
        
        print("✅ 模組導入成功")
        
        # 測試目錄
        test_dir = Path("./test_image2")
        image_dir = test_dir / "image"  
        label_dir = test_dir / "label"
        
        if not image_dir.exists() or not label_dir.exists():
            print(f"❌ 測試目錄不存在: {test_dir}")
            return
        
        print(f"📁 測試目錄: {test_dir}")
        
        # 生成類別配置
        try:
            class_configs = generate_class_configs_from_labelme(str(label_dir))
            print(f"🏷️ 生成了 {len(class_configs)} 個類別配置:")
            for cid, config in class_configs.items():
                print(f"   ID {cid}: {config.name}")
        except Exception as e:
            print(f"⚠️ LabelMe掃描失敗，使用預設配置: {e}")
            # 手動創建類別配置
            from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import ClassConfig
            class_configs = {
                0: ClassConfig(name="expansion_joint", color=(255, 0, 0)),
                1: ClassConfig(name="joint", color=(0, 255, 0))
            }
            print(f"🏷️ 使用手動類別配置:")
            for cid, config in class_configs.items():
                print(f"   ID {cid}: {config.name}")
        
        # 創建配置
        config = EnhancedYOLOConfig(
            detection_model_path="",  # 只測試GT載入
            segmentation_model_path="",
            class_configs=class_configs,
            device="cpu"
        )
        
        # 創建推理器
        inference = create_enhanced_yolo_inference(config)
        print("✅ 推理器創建成功")
        
        # 測試幾個特定的圖像
        test_files = ["101.jpg", "39.jpg", "139.jpg"]
        
        for image_file in test_files:
            image_path = image_dir / image_file
            if not image_path.exists():
                print(f"⚠️ 圖像不存在: {image_path}")
                continue
                
            print(f"\n🖼️ 測試圖像: {image_file}")
            
            # 測試GT載入
            try:
                import cv2
                image = cv2.imread(str(image_path))
                if image is None:
                    print(f"❌ 無法載入圖像: {image_path}")
                    continue
                
                image_shape = image.shape
                print(f"📐 圖像尺寸: {image_shape}")
                
                # 查找對應的標註文件
                json_path = label_dir / (image_file.replace('.jpg', '.json'))
                txt_path = label_dir / (image_file.replace('.jpg', '.txt'))
                
                if json_path.exists():
                    print(f"🏷️ 測試JSON標註: {json_path.name}")
                    gt_annotations = inference._load_gt_annotations(str(json_path), image_shape)
                    print(f"   載入了 {len(gt_annotations)} 個GT標註")
                    for i, gt in enumerate(gt_annotations):
                        print(f"   GT {i+1}: {gt['class_name']} (ID: {gt['class_id']}) - {gt['bbox']}")
                
                if txt_path.exists():
                    print(f"🎯 測試TXT標註: {txt_path.name}")
                    gt_annotations = inference._load_gt_annotations(str(txt_path), image_shape)
                    print(f"   載入了 {len(gt_annotations)} 個GT標註")
                    for i, gt in enumerate(gt_annotations):
                        print(f"   GT {i+1}: {gt['class_name']} (ID: {gt['class_id']}) - {gt['bbox']}")
                
            except Exception as e:
                print(f"❌ GT載入失敗: {e}")
                import traceback
                traceback.print_exc()
    
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 開始測試GT載入功能...")
    test_gt_loading()
    print("🎉 測試完成!")