# SAHI 功能改進總結

## 🎯 問題與解決方案

### 1. **字體大小問題** ✅ 已修復
**問題**: 使用SAHI後字體變小了
**原因**: SAHI結果使用預測標籤邏輯，字體為0.5，比GT的0.6小
**解決方案**:
```python
# 統一使用較大字體 (與GT保持一致)
font_scale = 0.6  # 提升字體大小
thickness = 2
label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
```

### 2. **Mask區域顯示** ✅ 已修復
**問題**: 使用SAHI，也要能夠顯示mask區域
**原因**: SAHI原本只支持檢測框，沒有mask處理
**解決方案**:
```python
# 優先使用分割模型以獲得mask支持
model_path = self.config.segmentation_model_path or self.config.detection_model_path

# 嘗試獲取mask (如果SAHI支持分割)
if hasattr(detection, 'mask') and detection.mask is not None:
    mask = detection.mask.bool_mask
    if mask is not None:
        sahi_detection['mask'] = mask.astype(np.uint8)
```

### 3. **每個類別confidence設定** ✅ 已修復
**問題**: 改成每個類別都能設定conf
**原因**: SAHI AutoDetectionModel只支持全局confidence_threshold
**解決方案**:
```python
# 計算最低confidence閾值作為SAHI初始閾值
min_conf = min([config.conf_threshold for config in self.config.class_configs.values()])

# 在後處理中按類別特定閾值過濾
if class_id in self.config.class_configs:
    class_config = self.config.class_configs[class_id]
    if confidence < class_config.conf_threshold or not class_config.enabled:
        continue  # 跳過不符合類別特定閾值的檢測
```

## 📝 修改的檔案和函數

### 1. `_draw_predictions()` 函數
- **位置**: 第2506-2527行
- **修改**: 統一字體大小為0.6，添加SAHI標籤前綴識別

### 2. `_run_sahi()` 函數  
- **位置**: 第1754-1789行
- **修改**: 添加類別特定閾值檢查和mask支持

### 3. `_setup_sahi()` 函數
- **位置**: 第1503-1527行
- **修改**: 優先使用分割模型，計算最低confidence閾值

## 🎨 視覺效果改進

| 項目 | 修復前 | 修復後 |
|------|--------|--------|
| **字體大小** | 0.5 (小) | 0.6 (與GT一致) |
| **標籤識別** | 無前綴 | SAHI-前綴 |
| **Mask顯示** | 不支援 | 半透明mask |
| **Confidence** | 全局閾值 | 類別特定閾值 |

## ⚙️ 技術實現細節

### 字體統一化
```python
# 繪製標籤 (增大字體以保持與GT一致)
source = pred.get('source', 'unknown')
if source == 'sahi':
    label = f"SAHI-{class_name}: {conf:.2f}"
else:
    label = f"{class_name}: {conf:.2f}"

# 統一使用較大字體 (與GT保持一致)
font_scale = 0.6  # 提升字體大小
thickness = 2
```

### Mask支持
```python
# 嘗試獲取mask (如果SAHI支持分割)
if hasattr(detection, 'mask') and detection.mask is not None:
    try:
        # 將SAHI的mask轉換為我們的格式
        mask = detection.mask.bool_mask
        if mask is not None:
            sahi_detection['mask'] = mask.astype(np.uint8)
            self.logger.debug(f"✅ SAHI檢測到mask: {class_name}")
    except Exception as e:
        self.logger.debug(f"⚠️ SAHI mask處理失敗: {e}")
```

### 類別特定Confidence
```python
# 檢查類別配置和閾值
if class_id in self.config.class_configs:
    class_config = self.config.class_configs[class_id]
    if confidence < class_config.conf_threshold or not class_config.enabled:
        continue  # 跳過不符合類別特定閾值的檢測
    class_name = class_config.name  # 使用配置中的名稱
```

## 🔧 配置示例

```python
# 類別特定confidence配置示例
class_configs = {
    0: ClassConfig("expansion_joint_伸縮縫", conf_threshold=0.4),
    1: ClassConfig("joint_路面接縫", conf_threshold=0.6),
    2: ClassConfig("linear_crack_裂縫", conf_threshold=0.3),
    3: ClassConfig("potholes_坑洞", conf_threshold=0.2),
    4: ClassConfig("manhole_人孔蓋或排水溝", conf_threshold=0.8),
}

# SAHI將使用最低閾值(0.2)初始化，然後在後處理中按類別過濾
```

## 🚀 使用效果

### 改進前
- ❌ SAHI字體較小，視覺不一致
- ❌ 無mask顯示，信息不完整
- ❌ 無法區分SAHI和普通預測
- ❌ 所有類別使用相同confidence

### 改進後
- ✅ 字體大小統一，視覺一致
- ✅ 支持mask半透明顯示
- ✅ SAHI標籤有前綴標識
- ✅ 每個類別可設定不同confidence

## 🧪 測試建議

1. **字體測試**: 啟用SAHI並檢查標籤字體大小與GT是否一致
2. **Mask測試**: 使用分割模型測試SAHI的mask顯示效果
3. **Confidence測試**: 設定不同類別的confidence閾值並驗證過濾效果
4. **標籤測試**: 確認SAHI結果帶有"SAHI-"前綴
5. **整合測試**: 檢查三面板圖像中SAHI結果的完整顯示

## ✨ 總結

通過這些改進，SAHI功能現在提供了：
- 🎯 **一致的視覺效果**: 字體大小與GT統一
- 🎭 **完整的信息顯示**: 支持mask區域
- ⚙️ **靈活的配置**: 每個類別獨立confidence設定
- 🔍 **清楚的標識**: SAHI結果易於識別

所有修改都保持了向後兼容性，並與現有的統一預測收集邏輯完美整合。