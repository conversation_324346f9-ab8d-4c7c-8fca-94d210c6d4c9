#!/usr/bin/env python3
"""
Vision Mamba架構測試
"""

import unittest
import sys
from pathlib import Path

# 添加項目路徑
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from AI模型建構訓練驗證.model_create.util.test_framework import BaseTestCase
    from AI模型建構訓練驗證.model_create.encoder.mamba.vision_mamba_core import (
        VisionMambaConfig, VisionMamba, PatchEmbed, SSMLayer, VisionMambaBlock,
        create_vision_mamba_tiny, create_vision_mamba_small, create_vision_mamba_base
    )
    VISION_MAMBA_AVAILABLE = True
except ImportError as e:
    print(f"Vision Mamba not available: {e}")
    VISION_MAMBA_AVAILABLE = False
    # 提供簡化的BaseTestCase
    class BaseTestCase:
        def __init__(self):
            self.logger = type('Logger', (), {'info': print, 'warning': print, 'error': print})()
        def get_test_image_paths(self):
            return []
        def get_image_annotation_pair(self):
            return None, None, "test"
        def assert_image_valid(self, path):
            pass
        def assert_annotation_valid(self, path):
            pass

try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False


class TestVisionMambaConfig(unittest.TestCase):
    """Vision Mamba配置測試"""
    
    @unittest.skipUnless(VISION_MAMBA_AVAILABLE, "Vision Mamba not available")
    def test_default_config(self):
        """測試默認配置"""
        config = VisionMambaConfig()
        
        self.assertEqual(config.d_model, 768)
        self.assertEqual(config.d_state, 16)
        self.assertEqual(config.img_size, 224)
        self.assertEqual(config.patch_size, 16)
        self.assertEqual(config.num_classes, 1000)
        self.assertTrue(config.bidirectional)
    
    @unittest.skipUnless(VISION_MAMBA_AVAILABLE, "Vision Mamba not available")
    def test_custom_config(self):
        """測試自定義配置"""
        config = VisionMambaConfig(
            d_model=512,
            d_state=8,
            img_size=224,
            num_classes=5,
            depths=[2, 2, 6, 2],
            dims=[64, 128, 256, 512]
        )
        
        self.assertEqual(config.d_model, 512)
        self.assertEqual(config.d_state, 8)
        self.assertEqual(config.num_classes, 5)
        self.assertEqual(config.depths, [2, 2, 6, 2])
        self.assertEqual(config.dims, [64, 128, 256, 512])
    
    @unittest.skipUnless(VISION_MAMBA_AVAILABLE, "Vision Mamba not available")
    def test_dt_rank_auto(self):
        """測試dt_rank自動計算"""
        config = VisionMambaConfig(d_model=768)
        self.assertEqual(config.dt_rank, 48)  # 768 / 16 = 48


@unittest.skipUnless(TORCH_AVAILABLE and VISION_MAMBA_AVAILABLE, "PyTorch or Vision Mamba not available")
class TestVisionMambaComponents(BaseTestCase):
    """Vision Mamba組件測試"""
    
    def test_patch_embed(self):
        """測試補丁嵌入"""
        patch_embed = PatchEmbed(img_size=224, patch_size=16, in_chans=3, embed_dim=768)
        
        # 測試輸入
        x = torch.randn(2, 3, 224, 224)
        output = patch_embed(x)
        
        # 驗證輸出形狀
        expected_patches = (224 // 16) ** 2  # 196
        self.assertEqual(output.shape, (2, expected_patches, 768))
    
    def test_ssm_layer(self):
        """測試SSM層"""
        ssm = SSMLayer(d_model=768, d_state=16, expand=2, bidirectional=True)
        
        # 測試輸入
        x = torch.randn(2, 196, 768)  # [batch, seq_len, d_model]
        output = ssm(x)
        
        # 驗證輸出形狀
        self.assertEqual(output.shape, x.shape)
    
    def test_vision_mamba_block(self):
        """測試Vision Mamba塊"""
        config = VisionMambaConfig(d_model=768)
        block = VisionMambaBlock(dim=768, config=config)
        
        # 測試輸入
        x = torch.randn(2, 196, 768)
        output = block(x)
        
        # 驗證輸出形狀
        self.assertEqual(output.shape, x.shape)
    
    def test_vision_mamba_model(self):
        """測試完整Vision Mamba模型"""
        config = VisionMambaConfig(
            img_size=224,
            patch_size=16,
            num_classes=5,
            depths=[2, 2, 6, 2],
            dims=[96, 192, 384, 768]
        )
        
        model = VisionMamba(config)
        
        # 測試前向傳播
        x = torch.randn(2, 3, 224, 224)
        output = model(x)
        
        # 驗證輸出形狀
        self.assertEqual(output.shape, (2, 5))
    
    def test_feature_extraction(self):
        """測試特徵提取"""
        config = VisionMambaConfig(num_classes=5)
        model = VisionMamba(config)
        
        # 測試特徵提取
        x = torch.randn(1, 3, 224, 224)
        features, feature_list = model.forward_features(x)
        
        # 驗證特徵形狀
        self.assertEqual(len(feature_list), len(config.depths))
        self.assertIsInstance(features, torch.Tensor)
    
    def test_feature_maps(self):
        """測試特徵圖獲取"""
        config = VisionMambaConfig(num_classes=5)
        model = VisionMamba(config)
        
        # 測試特徵圖
        x = torch.randn(1, 3, 224, 224)
        feature_maps = model.get_feature_maps(x)
        
        # 驗證特徵圖數量
        self.assertEqual(len(feature_maps), len(config.depths))
        
        # 驗證每個特徵圖的形狀
        for i, fm in enumerate(feature_maps):
            self.assertEqual(fm.dim(), 4)  # [B, C, H, W]
            self.assertEqual(fm.shape[0], 1)  # batch size


@unittest.skipUnless(TORCH_AVAILABLE and VISION_MAMBA_AVAILABLE, "PyTorch or Vision Mamba not available")
class TestVisionMambaFactory(BaseTestCase):
    """Vision Mamba工廠函數測試"""
    
    def test_create_tiny(self):
        """測試創建tiny模型"""
        model = create_vision_mamba_tiny(num_classes=5)
        
        self.assertIsInstance(model, VisionMamba)
        self.assertEqual(model.num_classes, 5)
        
        # 測試前向傳播
        x = torch.randn(1, 3, 224, 224)
        output = model(x)
        self.assertEqual(output.shape, (1, 5))
    
    def test_create_small(self):
        """測試創建small模型"""
        model = create_vision_mamba_small(num_classes=10)
        
        self.assertIsInstance(model, VisionMamba)
        self.assertEqual(model.num_classes, 10)
        
        # 測試前向傳播
        x = torch.randn(1, 3, 224, 224)
        output = model(x)
        self.assertEqual(output.shape, (1, 10))
    
    def test_create_base(self):
        """測試創建base模型"""
        model = create_vision_mamba_base(num_classes=1000)
        
        self.assertIsInstance(model, VisionMamba)
        self.assertEqual(model.num_classes, 1000)
        
        # 驗證模型參數數量更多
        tiny_model = create_vision_mamba_tiny()
        base_params = sum(p.numel() for p in model.parameters())
        tiny_params = sum(p.numel() for p in tiny_model.parameters())
        
        self.assertGreater(base_params, tiny_params)
    
    def test_custom_parameters(self):
        """測試自定義參數"""
        model = create_vision_mamba_tiny(
            num_classes=5,
            img_size=256,
            patch_size=32,
            bidirectional=False
        )
        
        self.assertEqual(model.config.img_size, 256)
        self.assertEqual(model.config.patch_size, 32)
        self.assertFalse(model.config.bidirectional)
        
        # 測試不同輸入尺寸
        x = torch.randn(1, 3, 256, 256)
        output = model(x)
        self.assertEqual(output.shape, (1, 5))


@unittest.skipUnless(VISION_MAMBA_AVAILABLE, "Vision Mamba not available")
class TestVisionMambaIntegration(BaseTestCase):
    """Vision Mamba整合測試"""
    
    def test_model_registry(self):
        """測試模型註冊"""
        from AI模型建構訓練驗證.model_create.encoder.mamba.vision_mamba_core import VISION_MAMBA_MODELS
        
        self.assertIn('vision_mamba_tiny', VISION_MAMBA_MODELS)
        self.assertIn('vision_mamba_small', VISION_MAMBA_MODELS)
        self.assertIn('vision_mamba_base', VISION_MAMBA_MODELS)
        
        # 測試通過註冊表創建模型
        create_func = VISION_MAMBA_MODELS['vision_mamba_tiny']
        model = create_func(num_classes=5)
        self.assertEqual(model.num_classes, 5)
    
    @unittest.skipUnless(TORCH_AVAILABLE, "PyTorch not available")
    def test_real_data_compatibility(self):
        """測試真實數據兼容性"""
        # 檢查是否有測試圖像
        test_images = self.get_test_image_paths()
        if not test_images:
            self.skipTest("No real test data available")
        
        # 創建模型
        model = create_vision_mamba_tiny(num_classes=5)
        model.eval()
        
        # 模擬圖像預處理
        # 實際應用中需要適當的圖像預處理
        image_path = test_images[0]
        self.assert_image_valid(image_path)
        
        # 創建模擬輸入
        x = torch.randn(1, 3, 224, 224)
        
        with torch.no_grad():
            output = model(x)
        
        self.assertEqual(output.shape, (1, 5))
        self.logger.info(f"Vision Mamba compatible with test data format")


if __name__ == '__main__':
    unittest.main()