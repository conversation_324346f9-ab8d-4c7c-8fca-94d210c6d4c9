import cv2
import numpy as np
import time

class UniversalDetector:
    def __init__(self,
                 model,
                 slice_height: int = 640,
                 slice_width: int = 640,
                 overlap_ratio: float = 0.2,
                 overall_inference: bool = False,
                 merge_strategy: str = "largest_object",
                 merge_adjacent_same_class: bool = False,
                 excluded_classes: list = None,
                 log_level: str = "lightweight"):
        self.model = model
        self.slice_height = slice_height
        self.slice_width = slice_width
        self.overlap_ratio = overlap_ratio
        self.overall_inference = overall_inference
        self.merge_strategy = merge_strategy
        self.merge_adjacent_same_class = merge_adjacent_same_class
        self.excluded_classes = excluded_classes if excluded_classes is not None else []
        self.log_level = log_level

    def _slice_image(self, image, rois=None):
        """
        Slices the image into smaller patches, optionally within specified ROIs.
        Returns a list of (slice_image, original_x, original_y) tuples.
        """
        slices = []
        if rois is None or len(rois) == 0:
            target_regions = [(0, 0, image.shape[1], image.shape[0])]
        else:
            target_regions = rois

        for x_start, y_start, x_end, y_end in target_regions:
            region_width = x_end - x_start
            region_height = y_end - y_start

            y_steps = int(np.ceil(region_height * (1 - self.overlap_ratio) / self.slice_height))
            x_steps = int(np.ceil(region_width * (1 - self.overlap_ratio) / self.slice_width))

            for i in range(y_steps + 1):
                for j in range(x_steps + 1):
                    y1 = int(y_start + i * self.slice_height * (1 - self.overlap_ratio))
                    x1 = int(x_start + j * self.slice_width * (1 - self.overlap_ratio))
                    y2 = y1 + self.slice_height
                    x2 = x1 + self.slice_width

                    # Adjust coordinates to stay within image bounds
                    y1 = max(0, y1)
                    x1 = max(0, x1)
                    y2 = min(image.shape[0], y2)
                    x2 = min(image.shape[1], x2)

                    # Ensure slice has minimum dimensions (e.g., if it's the last slice and smaller)
                    if y2 - y1 < self.slice_height and y1 != 0:
                        y1 = max(0, y2 - self.slice_height)
                    if x2 - x1 < self.slice_width and x1 != 0:
                        x1 = max(0, x2 - self.slice_width)

                    slice_img = image[y1:y2, x1:x2]
                    if slice_img.shape[0] > 0 and slice_img.shape[1] > 0:
                        slices.append((slice_img, x1, y1))
        return slices

    def _calculate_iou(self, box1, box2):
        # box format: (x1, y1, x2, y2)
        x1_inter = max(box1[0], box2[0])
        y1_inter = max(box1[1], box2[1])
        x2_inter = min(box1[2], box2[2])
        y2_inter = min(box1[3], box2[3])

        inter_width = max(0, x2_inter - x1_inter)
        inter_height = max(0, y2_inter - y1_inter)
        intersection_area = inter_width * inter_height

        box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
        box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])

        union_area = box1_area + box2_area - intersection_area
        iou = intersection_area / union_area if union_area > 0 else 0
        return iou

    def _calculate_diou(self, box1, box2):
        # box format: (x1, y1, x2, y2)
        iou = self._calculate_iou(box1, box2)

        # Center points
        b1_x_center = (box1[0] + box1[2]) / 2
        b1_y_center = (box1[1] + box1[3]) / 2
        b2_x_center = (box2[0] + box2[2]) / 2
        b2_y_center = (box2[1] + box2[3]) / 2

        # Distance between center points
        center_dist_sq = (b1_x_center - b2_x_center)**2 + (b1_y_center - b2_y_center)**2

        # Bounding box of both boxes
        c_x1 = min(box1[0], box2[0])
        c_y1 = min(box1[1], box2[1])
        c_x2 = max(box1[2], box2[2])
        c_y2 = max(box1[3], box2[3])

        # Diagonal length of the smallest enclosing box
        c_diag_sq = (c_x2 - c_x1)**2 + (c_y2 - c_y1)**2

        if c_diag_sq == 0:
            return iou # Avoid division by zero

        diou = iou - center_dist_sq / c_diag_sq
        return diou

    def _merge_overlapping_boxes(self, detections, iou_threshold=0.5, score_threshold=0.001, sigma=0.5):
        """
        Merges overlapping bounding boxes based on the specified strategy.
        Detections format: [(x1, y1, x2, y2, score, class_id, mask), ...]
        """
        if not detections:
            return []

        if self.merge_strategy == "largest_object":
            # Sort detections by score in descending order
            detections.sort(key=lambda x: x[4], reverse=True)
            
            merged_detections = []
            for i, det1 in enumerate(detections):
                x1_1, y1_1, x2_1, y2_1, score1, class_id1, mask1 = det1
                is_merged = False
                for j, det2 in enumerate(merged_detections):
                    x1_2, y1_2, x2_2, y2_2, score2, class_id2, mask2 = det2

                    # Check for overlap and same class
                    if class_id1 == class_id2 and self._calculate_iou((x1_1, y1_1, x2_1, y2_1), (x1_2, y1_2, x2_2, y2_2)) > 0:
                        
                        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
                        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)

                        if area1 > area2: # Keep the larger object
                            merged_detections[j] = det1
                        is_merged = True
                        break
                if not is_merged:
                    merged_detections.append(det1)
            return merged_detections
        
        elif self.merge_strategy == "NMS":
            # Sort detections by score in descending order
            detections.sort(key=lambda x: x[4], reverse=True)
            
            keep_detections = []
            while detections:
                best_detection = detections.pop(0) # Get the highest score detection
                keep_detections.append(best_detection)
                
                # Filter out detections that significantly overlap with the best_detection
                remaining_detections = []
                for det in detections:
                    if best_detection[5] != det[5] or self._calculate_iou(best_detection[0:4], det[0:4]) < iou_threshold:
                        remaining_detections.append(det)
                detections = remaining_detections
            return keep_detections

        elif self.merge_strategy == "soft_NMS":
            # Sort detections by score in descending order
            detections.sort(key=lambda x: x[4], reverse=True)
            
            processed_detections = []
            while detections:
                best_detection = list(detections.pop(0)) # Get the highest score detection
                processed_detections.append(best_detection)

                for i, det in enumerate(detections):
                    if best_detection[5] == det[5]: # Only apply Soft-NMS to same class
                        iou = self._calculate_iou(best_detection[0:4], det[0:4])
                        if iou > 0: # If there is overlap
                            # Reduce score based on overlap
                            detections[i] = list(detections[i]) # Convert to list to modify
                            detections[i][4] = detections[i][4] * np.exp(-(iou * iou) / sigma)
                            if detections[i][4] < score_threshold: # Remove if score falls below threshold
                                detections.pop(i)
                                break # Restart inner loop as list size changed
                detections.sort(key=lambda x: x[4], reverse=True) # Re-sort after score reduction
            return processed_detections

        elif self.merge_strategy == "DIoU-NMS":
            # Sort detections by score in descending order
            detections.sort(key=lambda x: x[4], reverse=True)
            
            keep_detections = []
            while detections:
                best_detection = detections.pop(0) # Get the highest score detection
                keep_detections.append(best_detection)
                
                # Filter out detections that significantly overlap with the best_detection using DIoU
                remaining_detections = []
                for det in detections:
                    if best_detection[5] != det[5] or self._calculate_diou(best_detection[0:4], det[0:4]) < iou_threshold:
                        remaining_detections.append(det)
                detections = remaining_detections
            return keep_detections
        else:
            return detections # No merging if strategy is unknown

    def _merge_adjacent_segments(self, detections, distance_threshold=50):
        """
        Merges adjacent bounding boxes/segmentation masks of the same class.
        For bounding boxes, it checks for proximity. For masks, it checks for overlap/proximity.
        Detections format: [(x1, y1, x2, y2, score, class_id, mask), ...]

        """
        if not detections:
            return []

        # Group detections by class
        detections_by_class = {}
        for det in detections:
            class_id = det[5]
            if class_id not in detections_by_class:
                detections_by_class[class_id] = []
            detections_by_class[class_id].append(det)

        final_merged_detections = []

        for class_id, class_detections in detections_by_class.items():
            if not self.merge_adjacent_same_class:
                final_merged_detections.extend(class_detections)
                continue

            # For bounding boxes: simple proximity check and merge
            merged_class_detections = []
            class_detections.sort(key=lambda x: x[0]) # Sort by x1 for easier processing

            while class_detections:
                current_det = list(class_detections.pop(0)) # Convert to list to modify
                
                i = 0
                while i < len(class_detections):
                    other_det = class_detections[i]
                    
                    box1 = current_det[0:4]
                    box2 = other_det[0:4]

                    # Calculate distance between closest edges
                    dist_x = max(0, box1[0] - box2[2], box2[0] - box1[2])
                    dist_y = max(0, box1[1] - box2[3], box2[1] - box1[3])

                    if dist_x <= distance_threshold and dist_y <= distance_threshold:
                        # Merge bounding boxes: take the union
                        current_det[0] = min(box1[0], box2[0])
                        current_det[1] = min(box1[1], box2[1])
                        current_det[2] = max(box1[2], box2[2])
                        current_det[3] = max(box1[3], box2[3])
                        
                        # Merge masks (if available): logical OR operation
                        if current_det[6] is not None and other_det[6] is not None:
                            # Create a new mask that encompasses the merged bounding box
                            # This assumes masks are binary (0 or 255) and can be directly combined
                            min_x = int(min(current_det[0], other_det[0]))
                            min_y = int(min(current_det[1], other_det[1]))
                            max_x = int(max(current_det[2], other_det[2]))
                            max_y = int(max(current_det[3], other_det[3]))

                            merged_mask_shape = (max_y - min_y, max_x - min_x)
                            merged_mask = np.zeros(merged_mask_shape, dtype=np.uint8)

                            # Paste current_det mask onto the new merged_mask
                            current_mask_y1 = int(current_det[1] - min_y)
                            current_mask_x1 = int(current_det[0] - min_x)
                            current_mask_y2 = int(current_det[3] - min_y)
                            current_mask_x2 = int(current_det[2] - min_x)
                            
                            # Resize current_det[6] to fit the new merged_mask_shape if necessary
                            # This is a simplified approach. A more robust solution would involve
                            # re-projecting the mask onto the new bounding box.
                            current_mask_resized = cv2.resize(current_det[6], (current_mask_x2 - current_mask_x1, current_mask_y2 - current_mask_y1), interpolation=cv2.INTER_NEAREST)
                            merged_mask[current_mask_y1:current_mask_y2, current_mask_x1:current_mask_x2] = np.maximum(merged_mask[current_mask_y1:current_mask_y2, current_mask_x1:current_mask_x2], current_mask_resized)

                            # Paste other_det mask onto the new merged_mask
                            other_mask_y1 = int(other_det[1] - min_y)
                            other_mask_x1 = int(other_det[0] - min_x)
                            other_mask_y2 = int(other_det[3] - min_y)
                            other_mask_x2 = int(other_det[2] - min_x)

                            other_mask_resized = cv2.resize(other_det[6], (other_mask_x2 - other_mask_x1, other_mask_y2 - other_mask_y1), interpolation=cv2.INTER_NEAREST)
                            merged_mask[other_mask_y1:other_mask_y2, other_mask_x1:other_mask_x2] = np.maximum(merged_mask[other_mask_y1:other_mask_y2, other_mask_x1:other_mask_x2], other_mask_resized)

                            current_det[6] = merged_mask

                        class_detections.pop(i) # Remove the merged detection
                        i = 0 # Restart check from beginning as list changed
                    else:
                        i += 1
                merged_class_detections.append(tuple(current_det))
            final_merged_detections.extend(merged_class_detections)

        return final_merged_detections

    def _log_progress(self, log_level, slice_count=0, prediction_count=0, class_predictions=None, processing_time=0.0):
        if log_level == "none":
            return

        if log_level == "lightweight":
            print(f"Slice Count: {slice_count}, Total Predictions: {prediction_count}")
        elif log_level == "detailed":
            print(f"Slice Count: {slice_count}")
            print(f"Prediction Count: {prediction_count}")
            if class_predictions:
                print("Predictions by Class:")
                for class_id, count in class_predictions.items():
                    print(f"  Class {class_id}: {count}")
            print(f"Processing Time: {processing_time:.2f} seconds")

    def detect(self, image, rois=None):
        start_time = time.time()
        all_detections = []
        slice_count = 0

        # 1. Slicing
        slices = self._slice_image(image, rois)
        slice_count = len(slices)

        # 2. Slice Inference
        for slice_img, x_offset, y_offset in slices:
            # Perform inference on the slice using the provided model
            # The model is expected to return detections in a format like:
            # [(x1, y1, x2, y2, score, class_id, mask), ...]
            # where coordinates are relative to the slice_img
            slice_detections = self.model.predict(slice_img) # Assuming model has a predict method

            # Convert slice-local coordinates to original image coordinates
            for det in slice_detections:
                x1, y1, x2, y2, score, class_id, mask = det
                original_x1 = x1 + x_offset
                original_y1 = y1 + y_offset
                original_x2 = x2 + x_offset
                original_y2 = y2 + y_offset
                all_detections.append((original_x1, original_y1, original_x2, original_y2, score, class_id, mask))

        # 3. Overall Image Inference (Optional)
        if self.overall_inference:
            overall_detections = self.model.predict(image)
            all_detections.extend(overall_detections)

        # 4. Class Exclusion
        filtered_detections = [det for det in all_detections if det[5] not in self.excluded_classes]

        # 5. Merging Overlapping Objects
        merged_detections = self._merge_overlapping_boxes(filtered_detections)

        # 6. Merging Adjacent Same-Class Objects/Segments
        final_detections = self._merge_adjacent_segments(merged_detections)

        end_time = time.time()
        processing_time = end_time - start_time

        # Logging
        prediction_count = len(final_detections)
        class_predictions = {}
        if self.log_level == "detailed":
            for det in final_detections:
                class_id = det[5]
                class_predictions[class_id] = class_predictions.get(class_id, 0) + 1

        self._log_progress(self.log_level,
                           slice_count=slice_count,
                           prediction_count=prediction_count,
                           class_predictions=class_predictions,
                           processing_time=processing_time)

        return final_detections

# Example Usage (for testing purposes, not part of the core library)
if __name__ == "__main__":
    # Dummy Model for demonstration
    class DummyModel:
        def predict(self, image):
            # Simulate some detections
            h, w, _ = image.shape
            detections = []
            if w > 100 and h > 100:
                # Example: a single detection in the center
                x1, y1, x2, y2 = w // 4, h // 4, w * 3 // 4, h * 3 // 4
                score = 0.95
                class_id = 0 # Assuming class 0 is "person"
                mask = np.zeros((h, w), dtype=np.uint8) # Dummy mask
                cv2.rectangle(mask, (x1, y1), (x2, y2), 255, -1)
                detections.append((x1, y1, x2, y2, score, class_id, mask))

                # Example: another detection, possibly overlapping
                if w > 200 and h > 200:
                    x1_2, y1_2, x2_2, y2_2 = w // 3, h // 3, w * 2 // 3, h * 2 // 3
                    score_2 = 0.85
                    class_id_2 = 0
                    mask_2 = np.zeros((h, w), dtype=np.uint8) # Dummy mask
                    cv2.rectangle(mask_2, (x1_2, y1_2), (x2_2, y2_2), 255, -1)
                    detections.append((x1_2, y1_2, x2_2, y2_2, score_2, class_id_2, mask_2))
                
                # Example: a detection of a different class
                if w > 300 and h > 300:
                    x1_3, y1_3, x2_3, y2_3 = w // 2, h // 2, w * 4 // 5, h * 4 // 5
                    score_3 = 0.90
                    class_id_3 = 1 # Assuming class 1 is "car"
                    mask_3 = np.zeros((h, w), dtype=np.uint8) # Dummy mask
                    cv2.rectangle(mask_3, (x1_3, y1_3), (x2_3, y2_3), 255, -1)
                    detections.append((x1_3, y1_3, x2_3, y2_3, score_3, class_id_3, mask_3))

            return detections

    # Create a dummy image
    dummy_image = np.zeros((1000, 1500, 3), dtype=np.uint8)
    dummy_image = cv2.rectangle(dummy_image, (100, 100), (400, 400), (0, 255, 0), -1) # ROI example

    dummy_model = DummyModel()

    # Test with default parameters (largest_object merge strategy)
    detector = UniversalDetector(dummy_model, log_level="detailed")
    print("--- Test 1: Default Parameters (largest_object merge) ---")
    results = detector.detect(dummy_image)
    print(f"Detected objects: {len(results)}")
    # print(results)

    # Test with NMS merge strategy
    detector_nms = UniversalDetector(dummy_model, merge_strategy="NMS", log_level="detailed")
    print("\n--- Test 2: NMS Merge Strategy ---")
    results_nms = detector_nms.detect(dummy_image)
    print(f"Detected objects: {len(results_nms)}")

    # Test with Soft-NMS merge strategy
    detector_soft_nms = UniversalDetector(dummy_model, merge_strategy="soft_NMS", log_level="detailed")
    print("\n--- Test 3: Soft-NMS Merge Strategy ---")
    results_soft_nms = detector_soft_nms.detect(dummy_image)
    print(f"Detected objects: {len(results_soft_nms)}")

    # Test with DIoU-NMS merge strategy
    detector_diou_nms = UniversalDetector(dummy_model, merge_strategy="DIoU-NMS", log_level="detailed")
    print("\n--- Test 4: DIoU-NMS Merge Strategy ---")
    results_diou_nms = detector_diou_nms.detect(dummy_image)
    print(f"Detected objects: {len(results_diou_nms)}")

    # Test with overall inference and excluded classes
    detector_full = UniversalDetector(dummy_model, overall_inference=True, excluded_classes=[1], log_level="detailed")
    print("\n--- Test 5: Overall Inference & Excluded Classes ---")
    results_full = detector_full.detect(dummy_image)
    print(f"Detected objects: {len(results_full)}")

    # Test with ROIs
    print("\n--- Test 6: With ROIs ---")
    # Define some ROIs (x_start, y_start, x_end, y_end)
    rois_to_process = [
        (50, 50, 300, 300),
        (200, 200, 500, 500)
    ]
    detector_roi = UniversalDetector(dummy_model, log_level="detailed")
    results_roi = detector_roi.detect(dummy_image, rois=rois_to_process)
    print(f"Detected objects in ROIs: {len(results_roi)}")

    # Test with different slice size and overlap
    print("\n--- Test 7: Different Slice Size and Overlap ---")
    detector_slice = UniversalDetector(dummy_model, slice_height=300, slice_width=300, overlap_ratio=0.5, log_level="detailed")
    results_slice = detector_slice.detect(dummy_image)
    print(f"Detected objects with custom slicing: {len(results_slice)}")

    # Test with "none" log level
    print("\n--- Test 8: No Logging ---")
    detector_no_log = UniversalDetector(dummy_model, log_level="none")
    results_no_log = detector_no_log.detect(dummy_image)
    print(f"Detected objects (no log): {len(results_no_log)}")

    # Test with merge_adjacent_same_class
    print("\n--- Test 9: Merge Adjacent Same Class ---")
    # Create a dummy image with adjacent objects of the same class
    adjacent_image = np.zeros((500, 500, 3), dtype=np.uint8)
    # Two adjacent boxes of class 0
    cv2.rectangle(adjacent_image, (50, 50), (150, 150), (255, 0, 0), -1)
    cv2.rectangle(adjacent_image, (140, 50), (240, 150), (0, 0, 255), -1) # Slightly overlapping
    # Two adjacent boxes of class 1
    cv2.rectangle(adjacent_image, (50, 200), (150, 300), (0, 255, 0), -1)
    cv2.rectangle(adjacent_image, (160, 200), (260, 300), (255, 255, 0), -1) # Small gap

    class DummyModelAdjacent(DummyModel):
        def predict(self, image):
            h, w, _ = image.shape
            detections = []
            # Simulate two adjacent detections of class 0
            detections.append((50, 50, 150, 150, 0.9, 0, np.zeros((h,w), dtype=np.uint8)))
            detections.append((140, 50, 240, 150, 0.8, 0, np.zeros((h,w), dtype=np.uint8)))
            # Simulate two adjacent detections of class 1 with a gap
            detections.append((50, 200, 150, 300, 0.85, 1, np.zeros((h,w), dtype=np.uint8)))
            detections.append((160, 200, 260, 300, 0.75, 1, np.zeros((h,w), dtype=np.uint8)))
            return detections

    dummy_model_adjacent = DummyModelAdjacent()
    detector_adjacent = UniversalDetector(dummy_model_adjacent, merge_adjacent_same_class=True, log_level="detailed")
    results_adjacent = detector_adjacent.detect(adjacent_image)
    print(f"Detected objects with adjacent merge: {len(results_adjacent)}")
    for det in results_adjacent:
        print(f"  Box: ({det[0]}, {det[1]}, {det[2]}, {det[3]}), Class: {det[5]}, Score: {det[4]:.2f}")