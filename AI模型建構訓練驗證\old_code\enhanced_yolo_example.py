#!/usr/bin/env python3
"""
增強YOLO推理系統使用範例
展示如何使用新的增強YOLO系統進行道路基礎設施檢測
"""

import os
import sys
import logging
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

from model_create.inference.enhanced_yolo_inference import (
    EnhancedYOLOConfig, EnhancedYOLOInference, ClassConfig,
    create_enhanced_yolo_inference, create_default_config
)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_single_image_inference():
    """單張圖像推理範例"""
    
    logger.info("=== 單張圖像推理範例 ===")
    
    # 創建配置
    config = EnhancedYOLOConfig(
        detection_model_path="./models/yolo12_detection.pt",
        segmentation_model_path="./models/yolo11_segmentation.pt",
        device="auto",
        
        # 類別配置
        class_configs={
            0: ClassConfig("裂縫", 0.5, (255, 0, 0), True, "道路表面裂縫"),
            1: ClassConfig("坑洞", 0.4, (0, 255, 0), True, "路面坑洞"),
            2: ClassConfig("人孔蓋", 0.6, (0, 0, 255), True, "下水道人孔蓋")
        },
        
        # SAHI配置
        enable_sahi=True,
        slice_height=512,
        slice_width=512,
        
        # 輸出配置
        save_visualizations=True,
        save_predictions=True,
        auto_convert_annotations=True
    )
    
    # 創建推理器
    inference = create_enhanced_yolo_inference(config)
    
    # 單張圖像推理
    image_path = "./test_image/road_sample.jpg"
    annotation_path = "./test_image/road_sample.json"  # LabelMe格式
    output_dir = "./output/single_image"
    
    try:
        results = inference.predict_single_image(
            image_path=image_path,
            annotation_path=annotation_path,
            output_dir=output_dir,
            task_type="both"  # 同時進行檢測和分割
        )
        
        logger.info("推理完成！")
        
        # 顯示結果摘要
        if 'detection' in results:
            det_count = len(results['detection']['detections'])
            logger.info(f"檢測到 {det_count} 個物件")
        
        if 'segmentation' in results:
            seg_count = len(results['segmentation']['segments'])
            logger.info(f"分割出 {seg_count} 個區域")
        
        if 'sahi' in results:
            sahi_count = len(results['sahi']['sahi_detections'])
            logger.info(f"SAHI檢測到 {sahi_count} 個物件")
        
        if 'converted_annotations' in results:
            original_format = results['converted_annotations']['original_format']
            logger.info(f"標註格式從 {original_format} 轉換為 YOLO")
        
    except Exception as e:
        logger.error(f"推理失敗: {e}")


def example_batch_processing():
    """批次處理範例"""
    
    logger.info("=== 批次處理範例 ===")
    
    # 使用配置文件
    config_path = "./model_create/configs/enhanced_yolo_config.yaml"
    
    if not os.path.exists(config_path):
        logger.warning("配置文件不存在，使用預設配置")
        config = create_default_config(
            detection_model="./models/yolo12_detection.pt",
            segmentation_model="./models/yolo11_segmentation.pt",
            enable_sahi=True
        )
    else:
        config = config_path
    
    # 創建推理器
    inference = create_enhanced_yolo_inference(config)
    
    # 批次處理
    input_dir = "./test_image"
    output_dir = "./output/batch_processing"
    
    try:
        batch_results = inference.batch_predict(
            input_dir=input_dir,
            output_dir=output_dir,
            task_type="both"
        )
        
        logger.info(f"批次處理完成！處理了 {len(batch_results)} 張圖像")
        
        # 顯示統計資訊
        stats = inference.inference_stats
        logger.info(f"總檢測數量: {stats['total_detections']}")
        logger.info(f"類別統計: {stats['class_counts']}")
        
    except Exception as e:
        logger.error(f"批次處理失敗: {e}")


def example_custom_class_configuration():
    """自定義類別配置範例"""
    
    logger.info("=== 自定義類別配置範例 ===")
    
    # 創建自定義類別配置
    custom_classes = {
        0: ClassConfig(
            name="嚴重裂縫",
            conf_threshold=0.7,    # 高置信度閾值
            color=(255, 0, 0),     # 紅色
            enabled=True,
            description="需要緊急修復的嚴重裂縫"
        ),
        1: ClassConfig(
            name="輕微裂縫", 
            conf_threshold=0.4,    # 低置信度閾值
            color=(255, 165, 0),   # 橙色
            enabled=True,
            description="可延後修復的輕微裂縫"
        ),
        2: ClassConfig(
            name="大型坑洞",
            conf_threshold=0.6,
            color=(0, 255, 0),     # 綠色
            enabled=True,
            description="影響行車安全的大型坑洞"
        ),
        3: ClassConfig(
            name="小型坑洞",
            conf_threshold=0.3,
            color=(0, 128, 0),     # 深綠色
            enabled=False,         # 暫時禁用
            description="小型坑洞檢測"
        ),
        4: ClassConfig(
            name="人孔蓋",
            conf_threshold=0.8,
            color=(0, 0, 255),     # 藍色
            enabled=True,
            description="標準人孔蓋"
        )
    }
    
    config = EnhancedYOLOConfig(
        detection_model_path="./models/custom_road_detection.pt",
        class_configs=custom_classes,
        
        # 針對不同嚴重程度的檢測策略
        global_conf=0.2,       # 較低的全局閾值
        iou_threshold=0.3,     # 較低的IoU閾值以捕捉更多重疊
        
        # 啟用SAHI以檢測小物件
        enable_sahi=True,
        slice_height=256,      # 更小的切片以檢測細節
        slice_width=256,
        
        save_visualizations=True,
        save_statistics=True
    )
    
    inference = create_enhanced_yolo_inference(config)
    
    # 顯示模型資訊
    model_info = inference.get_model_info()
    logger.info("模型配置:")
    for class_id, class_info in model_info['class_configs'].items():
        status = "啟用" if class_info['enabled'] else "禁用"
        logger.info(f"  類別 {class_id}: {class_info['name']} "
                   f"(閾值: {class_info['conf_threshold']}, 狀態: {status})")


def example_with_labelme_conversion():
    """LabelMe標註轉換範例"""
    
    logger.info("=== LabelMe標註轉換範例 ===")
    
    config = EnhancedYOLOConfig(
        detection_model_path="./models/yolo_detection.pt",
        
        # 啟用自動標註轉換
        auto_convert_annotations=True,
        input_annotation_format="auto",  # 自動檢測格式
        
        save_visualizations=True,
        save_predictions=True
    )
    
    inference = create_enhanced_yolo_inference(config)
    
    # 處理帶有LabelMe標註的圖像
    results = inference.predict_single_image(
        image_path="./test_image/sample.jpg",
        annotation_path="./test_image/sample.json",  # LabelMe格式
        output_dir="./output/labelme_conversion"
    )
    
    if 'converted_annotations' in results:
        conversion_info = results['converted_annotations']
        logger.info(f"標註轉換完成:")
        logger.info(f"  原始格式: {conversion_info['original_format']}")
        logger.info(f"  目標格式: {conversion_info['converted_format']}")
        logger.info(f"  轉換結果: {conversion_info['conversion_result']}")


def example_sahi_large_image():
    """SAHI大圖像處理範例"""
    
    logger.info("=== SAHI大圖像處理範例 ===")
    
    config = EnhancedYOLOConfig(
        detection_model_path="./models/yolo_detection.pt",
        
        # SAHI配置 - 專門用於大圖像
        enable_sahi=True,
        slice_height=640,      # 較大的切片
        slice_width=640,
        overlap_height_ratio=0.3,  # 較大的重疊
        overlap_width_ratio=0.3,
        
        # 推理配置
        img_size=1280,         # 較大的輸入尺寸
        global_conf=0.3,
        iou_threshold=0.5,
        
        save_visualizations=True
    )
    
    inference = create_enhanced_yolo_inference(config)
    
    # 處理大圖像（如高解析度道路全景圖）
    large_image_path = "./test_image/large_road_panorama.jpg"
    
    try:
        results = inference.predict_single_image(
            image_path=large_image_path,
            output_dir="./output/sahi_large_image",
            task_type="detection"
        )
        
        if 'sahi' in results:
            sahi_info = results['sahi']
            logger.info(f"SAHI檢測完成:")
            logger.info(f"  檢測數量: {len(sahi_info['sahi_detections'])}")
            logger.info(f"  切片尺寸: {sahi_info['slice_info']['slice_height']}x{sahi_info['slice_info']['slice_width']}")
            logger.info(f"  重疊比例: {sahi_info['slice_info']['overlap_height_ratio']}")
        
    except Exception as e:
        logger.error(f"SAHI處理失敗: {e}")


def example_performance_comparison():
    """性能比較範例"""
    
    logger.info("=== 性能比較範例 ===")
    
    import time
    
    # 標準推理配置
    standard_config = EnhancedYOLOConfig(
        detection_model_path="./models/yolo_detection.pt",
        enable_sahi=False,
        img_size=640,
        save_visualizations=False
    )
    
    # SAHI推理配置
    sahi_config = EnhancedYOLOConfig(
        detection_model_path="./models/yolo_detection.pt",
        enable_sahi=True,
        slice_height=320,
        slice_width=320,
        img_size=640,
        save_visualizations=False
    )
    
    test_image = "./test_image/test.jpg"
    
    # 標準推理
    inference_standard = create_enhanced_yolo_inference(standard_config)
    start_time = time.time()
    results_standard = inference_standard.predict_single_image(test_image, task_type="detection")
    standard_time = time.time() - start_time
    
    # SAHI推理
    inference_sahi = create_enhanced_yolo_inference(sahi_config)
    start_time = time.time()
    results_sahi = inference_sahi.predict_single_image(test_image, task_type="detection")
    sahi_time = time.time() - start_time
    
    # 比較結果
    standard_count = len(results_standard.get('detection', {}).get('detections', []))
    sahi_count = len(results_sahi.get('sahi', {}).get('sahi_detections', []))
    
    logger.info("性能比較結果:")
    logger.info(f"  標準推理: {standard_count} 個檢測, 用時 {standard_time:.2f}s")
    logger.info(f"  SAHI推理: {sahi_count} 個檢測, 用時 {sahi_time:.2f}s")
    logger.info(f"  檢測數量提升: {((sahi_count - standard_count) / max(standard_count, 1) * 100):.1f}%")


def main():
    """主函數 - 運行所有範例"""
    
    logger.info("增強YOLO推理系統範例開始")
    
    try:
        # 檢查必要的目錄
        os.makedirs("./output", exist_ok=True)
        
        # 運行範例（根據可用資源選擇）
        logger.info("如果您有相應的模型文件，可以運行以下範例:")
        
        print("\n可用範例:")
        print("1. 單張圖像推理")
        print("2. 批次處理")
        print("3. 自定義類別配置")
        print("4. LabelMe標註轉換")
        print("5. SAHI大圖像處理")
        print("6. 性能比較")
        
        # 示範配置創建
        logger.info("\n=== 配置創建示範 ===")
        
        # 方法1: 直接創建
        config1 = create_default_config(
            detection_model="./models/yolo12.pt",
            segmentation_model="./models/yolo11_seg.pt",
            enable_sahi=True
        )
        logger.info("預設配置創建完成")
        
        # 方法2: 自定義配置
        config2 = EnhancedYOLOConfig(
            detection_model_path="./models/custom_detection.pt",
            global_conf=0.3,
            enable_sahi=True,
            slice_height=512,
            auto_convert_annotations=True
        )
        logger.info("自定義配置創建完成")
        
        # 顯示配置資訊
        logger.info("配置資訊:")
        logger.info(f"  檢測模型: {config1.detection_model_path}")
        logger.info(f"  分割模型: {config1.segmentation_model_path}")
        logger.info(f"  SAHI啟用: {config1.enable_sahi}")
        logger.info(f"  自動轉換: {config1.auto_convert_annotations}")
        
    except Exception as e:
        logger.error(f"範例運行失敗: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="增強YOLO推理系統範例")
    parser.add_argument("--example", 
                       choices=["single", "batch", "custom", "labelme", "sahi", "performance", "all"],
                       default="all",
                       help="運行特定範例")
    
    args = parser.parse_args()
    
    if args.example == "single":
        example_single_image_inference()
    elif args.example == "batch":
        example_batch_processing()
    elif args.example == "custom":
        example_custom_class_configuration()
    elif args.example == "labelme":
        example_with_labelme_conversion()
    elif args.example == "sahi":
        example_sahi_large_image()
    elif args.example == "performance":
        example_performance_comparison()
    else:
        main()