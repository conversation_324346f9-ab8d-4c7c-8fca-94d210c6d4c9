#!/usr/bin/env python3
"""
API文檔自動生成系統
基於現有的工廠函數生態和完善的架構自動生成API文檔
"""

import os
import sys
import ast
import inspect
import importlib
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
import logging
from datetime import datetime
import re

# 文檔模板
MARKDOWN_TEMPLATE = """
# {title}

{description}

## 目錄
{toc}

{content}

---
*文檔自動生成於: {timestamp}*
*生成器版本: v1.0*
"""

CLASS_TEMPLATE = """
## {name}

**類型**: {type}
**模組**: {module}

{description}

### 參數
{parameters}

### 方法
{methods}

### 使用範例
```python
{example}
```

---
"""

FUNCTION_TEMPLATE = """
## {name}

**類型**: {type}
**模組**: {module}

{description}

### 參數
{parameters}

### 返回值
{returns}

### 使用範例
```python
{example}
```

---
"""


@dataclass
class DocConfig:
    """文檔生成配置"""
    
    # 輸出配置
    output_dir: str = "./docs/api"
    output_format: str = "markdown"  # markdown, html, json
    
    # 掃描配置
    scan_patterns: List[str] = field(default_factory=lambda: [
        "**/*.py",
        "!**/old_code/**",
        "!**/tests/**",
        "!**/__pycache__/**"
    ])
    
    # 生成配置
    include_private: bool = False
    include_examples: bool = True
    include_source_code: bool = False
    generate_index: bool = True
    
    # 分類配置
    factory_pattern: str = r"create_.*|.*Factory"
    config_pattern: str = r".*Config$"
    model_pattern: str = r".*Model$|.*Network$|.*Backbone$"
    
    # 語言配置
    language: str = "zh-CN"
    encoding: str = "utf-8"


class DocParser:
    """文檔解析器"""
    
    def __init__(self, config: DocConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def parse_module(self, module_path: str) -> Dict[str, Any]:
        """解析模組"""
        try:
            with open(module_path, 'r', encoding=self.config.encoding) as f:
                source = f.read()
            
            tree = ast.parse(source)
            
            module_info = {
                'path': module_path,
                'docstring': ast.get_docstring(tree),
                'classes': [],
                'functions': [],
                'imports': [],
                'constants': []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = self._parse_class(node, source)
                    if class_info:
                        module_info['classes'].append(class_info)
                        
                elif isinstance(node, ast.FunctionDef):
                    if not node.name.startswith('_') or self.config.include_private:
                        func_info = self._parse_function(node, source)
                        if func_info:
                            module_info['functions'].append(func_info)
                            
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        module_info['imports'].append(alias.name)
                        
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            module_info['imports'].append(f"{node.module}.{alias.name}")
                            
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id.isupper():
                            # 常數
                            module_info['constants'].append({
                                'name': target.id,
                                'value': ast.unparse(node.value) if hasattr(ast, 'unparse') else str(node.value)
                            })
            
            return module_info
            
        except Exception as e:
            self.logger.error(f"解析模組失敗 {module_path}: {e}")
            return None
    
    def _parse_class(self, node: ast.ClassDef, source: str) -> Dict[str, Any]:
        """解析類別"""
        try:
            class_info = {
                'name': node.name,
                'docstring': ast.get_docstring(node),
                'bases': [ast.unparse(base) if hasattr(ast, 'unparse') else str(base) for base in node.bases],
                'methods': [],
                'attributes': [],
                'decorators': [ast.unparse(dec) if hasattr(ast, 'unparse') else str(dec) for dec in node.decorator_list],
                'lineno': node.lineno
            }
            
            # 解析方法
            for item in node.body:
                if isinstance(item, ast.FunctionDef):
                    if not item.name.startswith('_') or self.config.include_private:
                        method_info = self._parse_function(item, source, is_method=True)
                        if method_info:
                            class_info['methods'].append(method_info)
                            
                elif isinstance(item, ast.Assign):
                    # 類別屬性
                    for target in item.targets:
                        if isinstance(target, ast.Name):
                            class_info['attributes'].append({
                                'name': target.id,
                                'value': ast.unparse(item.value) if hasattr(ast, 'unparse') else str(item.value)
                            })
            
            return class_info
            
        except Exception as e:
            self.logger.error(f"解析類別失敗 {node.name}: {e}")
            return None
    
    def _parse_function(self, node: ast.FunctionDef, source: str, is_method: bool = False) -> Dict[str, Any]:
        """解析函數"""
        try:
            func_info = {
                'name': node.name,
                'docstring': ast.get_docstring(node),
                'args': [],
                'returns': None,
                'decorators': [ast.unparse(dec) if hasattr(ast, 'unparse') else str(dec) for dec in node.decorator_list],
                'lineno': node.lineno,
                'is_method': is_method
            }
            
            # 解析參數
            for arg in node.args.args:
                arg_info = {
                    'name': arg.arg,
                    'annotation': ast.unparse(arg.annotation) if arg.annotation and hasattr(ast, 'unparse') else None
                }
                func_info['args'].append(arg_info)
            
            # 解析返回值註釋
            if node.returns:
                func_info['returns'] = ast.unparse(node.returns) if hasattr(ast, 'unparse') else str(node.returns)
            
            return func_info
            
        except Exception as e:
            self.logger.error(f"解析函數失敗 {node.name}: {e}")
            return None
    
    def extract_examples(self, docstring: str) -> List[str]:
        """從docstring中提取範例"""
        if not docstring:
            return []
        
        examples = []
        in_example = False
        current_example = []
        
        for line in docstring.split('\n'):
            line = line.strip()
            
            if 'example' in line.lower() or '範例' in line:
                in_example = True
                if current_example:
                    examples.append('\n'.join(current_example))
                    current_example = []
            elif in_example:
                if line.startswith('>>>') or line.startswith('...'):
                    current_example.append(line[3:].strip())
                elif line and not line.startswith('#'):
                    current_example.append(line)
                elif not line and current_example:
                    in_example = False
                    examples.append('\n'.join(current_example))
                    current_example = []
        
        if current_example:
            examples.append('\n'.join(current_example))
        
        return examples


class DocGenerator:
    """API文檔生成器"""
    
    def __init__(self, config: DocConfig):
        self.config = config
        self.parser = DocParser(config)
        self.logger = logging.getLogger(__name__)
        
        # 確保輸出目錄存在
        os.makedirs(self.config.output_dir, exist_ok=True)
    
    def generate_docs(self, project_root: str) -> Dict[str, Any]:
        """生成完整的API文檔"""
        self.logger.info("開始生成API文檔...")
        
        # 掃描項目
        modules = self._scan_project(project_root)
        
        # 分類模組
        categorized = self._categorize_modules(modules)
        
        # 生成各類別文檔
        docs = {}
        
        # 生成工廠函數文檔
        if categorized['factories']:
            docs['factories'] = self._generate_factory_docs(categorized['factories'])
        
        # 生成配置類文檔
        if categorized['configs']:
            docs['configs'] = self._generate_config_docs(categorized['configs'])
        
        # 生成模型文檔
        if categorized['models']:
            docs['models'] = self._generate_model_docs(categorized['models'])
        
        # 生成工具函數文檔
        if categorized['utils']:
            docs['utils'] = self._generate_utils_docs(categorized['utils'])
        
        # 生成索引文檔
        if self.config.generate_index:
            docs['index'] = self._generate_index_doc(docs, categorized)
        
        # 寫入文件
        self._write_docs(docs)
        
        self.logger.info(f"API文檔生成完成，輸出至: {self.config.output_dir}")
        
        return {
            'status': 'success',
            'output_dir': self.config.output_dir,
            'generated_files': list(docs.keys()),
            'total_modules': len(modules),
            'categorized': {k: len(v) for k, v in categorized.items()}
        }
    
    def _scan_project(self, project_root: str) -> List[Dict[str, Any]]:
        """掃描項目中的所有Python模組"""
        modules = []
        project_path = Path(project_root)
        
        for pattern in self.config.scan_patterns:
            if pattern.startswith('!'):
                continue  # 跳過排除模式
                
            for py_file in project_path.rglob(pattern):
                if py_file.suffix == '.py':
                    # 檢查是否被排除
                    excluded = False
                    for exclude_pattern in self.config.scan_patterns:
                        if exclude_pattern.startswith('!'):
                            if py_file.match(exclude_pattern[1:]):
                                excluded = True
                                break
                    
                    if not excluded:
                        module_info = self.parser.parse_module(str(py_file))
                        if module_info:
                            modules.append(module_info)
        
        return modules
    
    def _categorize_modules(self, modules: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """將模組按類型分類"""
        categorized = {
            'factories': [],
            'configs': [],
            'models': [],
            'utils': [],
            'core': [],
            'training': [],
            'inference': [],
            'distributed': []
        }
        
        for module in modules:
            module_name = Path(module['path']).stem
            module_path = module['path']
            
            # 根據路徑分類
            if 'factory' in module_path.lower() or 'create' in module_name:
                categorized['factories'].append(module)
            elif 'config' in module_path.lower() or module_name.endswith('_config'):
                categorized['configs'].append(module)
            elif any(keyword in module_path.lower() for keyword in ['model', 'network', 'backbone', 'encoder', 'decoder']):
                categorized['models'].append(module)
            elif 'core' in module_path.lower():
                categorized['core'].append(module)
            elif 'training' in module_path.lower() or 'train' in module_path.lower():
                categorized['training'].append(module)
            elif 'inference' in module_path.lower() or 'predict' in module_path.lower():
                categorized['inference'].append(module)
            elif 'distributed' in module_path.lower() or 'ray' in module_path.lower():
                categorized['distributed'].append(module)
            else:
                categorized['utils'].append(module)
        
        return categorized
    
    def _generate_factory_docs(self, factory_modules: List[Dict[str, Any]]) -> str:
        """生成工廠函數文檔"""
        content = []
        
        content.append("# 工廠函數API文檔\n")
        content.append("本文檔包含所有create_*工廠函數和Factory類的詳細說明。\n")
        
        # 按模組組織
        for module in factory_modules:
            if not module['functions'] and not module['classes']:
                continue
                
            module_name = Path(module['path']).stem
            content.append(f"## {module_name}\n")
            
            if module['docstring']:
                content.append(f"{module['docstring']}\n")
            
            # 工廠函數
            factory_functions = [f for f in module['functions'] 
                               if re.match(self.config.factory_pattern, f['name'])]
            
            if factory_functions:
                content.append("### 工廠函數\n")
                for func in factory_functions:
                    content.append(self._format_function_doc(func))
            
            # Factory類
            factory_classes = [c for c in module['classes'] 
                             if 'Factory' in c['name']]
            
            if factory_classes:
                content.append("### Factory類\n")
                for cls in factory_classes:
                    content.append(self._format_class_doc(cls))
        
        return '\n'.join(content)
    
    def _generate_config_docs(self, config_modules: List[Dict[str, Any]]) -> str:
        """生成配置類文檔"""
        content = []
        
        content.append("# 配置系統API文檔\n")
        content.append("本文檔包含所有配置類和相關函數的詳細說明。\n")
        
        for module in config_modules:
            if not module['classes']:
                continue
                
            module_name = Path(module['path']).stem
            content.append(f"## {module_name}\n")
            
            if module['docstring']:
                content.append(f"{module['docstring']}\n")
            
            # 配置類
            config_classes = [c for c in module['classes'] 
                            if re.match(self.config.config_pattern, c['name'])]
            
            for cls in config_classes:
                content.append(self._format_config_class_doc(cls))
        
        return '\n'.join(content)
    
    def _generate_model_docs(self, model_modules: List[Dict[str, Any]]) -> str:
        """生成模型文檔"""
        content = []
        
        content.append("# 模型架構API文檔\n")
        content.append("本文檔包含所有模型類、網路架構和相關組件的詳細說明。\n")
        
        # 按類型分組
        encoder_modules = [m for m in model_modules if 'encoder' in m['path'].lower()]
        decoder_modules = [m for m in model_modules if 'decoder' in m['path'].lower()]
        backbone_modules = [m for m in model_modules if 'backbone' in m['path'].lower()]
        other_modules = [m for m in model_modules if m not in encoder_modules + decoder_modules + backbone_modules]
        
        if encoder_modules:
            content.append("## 編碼器\n")
            for module in encoder_modules:
                content.append(self._format_module_doc(module, "編碼器"))
        
        if decoder_modules:
            content.append("## 解碼器\n")
            for module in decoder_modules:
                content.append(self._format_module_doc(module, "解碼器"))
        
        if backbone_modules:
            content.append("## 骨幹網路\n")
            for module in backbone_modules:
                content.append(self._format_module_doc(module, "骨幹網路"))
        
        if other_modules:
            content.append("## 其他模型組件\n")
            for module in other_modules:
                content.append(self._format_module_doc(module, "模型組件"))
        
        return '\n'.join(content)
    
    def _generate_utils_docs(self, util_modules: List[Dict[str, Any]]) -> str:
        """生成工具函數文檔"""
        content = []
        
        content.append("# 工具函數API文檔\n")
        content.append("本文檔包含所有工具函數和輔助類的詳細說明。\n")
        
        for module in util_modules:
            module_name = Path(module['path']).stem
            content.append(f"## {module_name}\n")
            
            if module['docstring']:
                content.append(f"{module['docstring']}\n")
            
            # 公開函數
            public_functions = [f for f in module['functions'] 
                              if not f['name'].startswith('_')]
            
            if public_functions:
                content.append("### 函數\n")
                for func in public_functions:
                    content.append(self._format_function_doc(func))
            
            # 工具類
            if module['classes']:
                content.append("### 類\n")
                for cls in module['classes']:
                    content.append(self._format_class_doc(cls))
        
        return '\n'.join(content)
    
    def _generate_index_doc(self, docs: Dict[str, str], categorized: Dict[str, List]) -> str:
        """生成索引文檔"""
        content = []
        
        content.append("# API文檔索引\n")
        content.append("歡迎使用道路基礎設施AI檢測框架API文檔。\n")
        
        content.append("## 文檔結構\n")
        content.append("- [工廠函數](factories.md) - 所有create_*函數和Factory類")
        content.append("- [配置系統](configs.md) - 配置類和參數設定")
        content.append("- [模型架構](models.md) - 模型類和網路組件")
        content.append("- [工具函數](utils.md) - 工具函數和輔助類")
        
        content.append("\n## 快速開始\n")
        content.append("### 工廠函數快速參考\n")
        
        # 統計工廠函數
        factory_count = 0
        for module in categorized.get('factories', []):
            factory_functions = [f for f in module['functions'] 
                               if re.match(self.config.factory_pattern, f['name'])]
            factory_count += len(factory_functions)
        
        content.append(f"框架提供了 **{factory_count}** 個工廠函數，涵蓋：")
        content.append("- 模型創建 (create_model, create_csp_iformer, etc.)")
        content.append("- 訓練組件 (create_optimizer, create_scheduler, etc.)")
        content.append("- 推理引擎 (create_yolo_inference, create_enhanced_yolo_inference, etc.)")
        content.append("- 分散式訓練 (create_ray_training_system, etc.)")
        
        content.append("\n### 配置系統快速參考\n")
        
        # 統計配置類
        config_count = 0
        for module in categorized.get('configs', []):
            config_classes = [c for c in module['classes'] 
                            if re.match(self.config.config_pattern, c['name'])]
            config_count += len(config_classes)
        
        content.append(f"框架提供了 **{config_count}** 個配置類，支援：")
        content.append("- 模型配置 (ModelConfig, TrainingConfig, etc.)")
        content.append("- 推理配置 (InferenceConfig, YOLOConfig, etc.)")
        content.append("- 分散式配置 (RayConfig, DistributedConfig, etc.)")
        
        content.append("\n## 最佳實踐\n")
        content.append("1. **配置驅動**: 優先使用配置文件而非硬編碼參數")
        content.append("2. **工廠模式**: 使用create_*函數創建組件，確保一致性")
        content.append("3. **類型提示**: 所有API都提供完整的類型註釋")
        content.append("4. **錯誤處理**: 使用內建的異常處理和驗證機制")
        
        return '\n'.join(content)
    
    def _format_function_doc(self, func: Dict[str, Any]) -> str:
        """格式化函數文檔"""
        lines = []
        
        lines.append(f"#### {func['name']}\n")
        
        if func['docstring']:
            lines.append(f"{func['docstring']}\n")
        
        # 參數
        if func['args']:
            lines.append("**參數:**")
            for arg in func['args']:
                arg_str = f"- `{arg['name']}`"
                if arg['annotation']:
                    arg_str += f": {arg['annotation']}"
                lines.append(arg_str)
            lines.append("")
        
        # 返回值
        if func['returns']:
            lines.append(f"**返回:** {func['returns']}\n")
        
        # 範例
        if func['docstring']:
            examples = self.parser.extract_examples(func['docstring'])
            if examples:
                lines.append("**範例:**")
                lines.append("```python")
                lines.append(examples[0])
                lines.append("```\n")
        
        return '\n'.join(lines)
    
    def _format_class_doc(self, cls: Dict[str, Any]) -> str:
        """格式化類別文檔"""
        lines = []
        
        lines.append(f"#### {cls['name']}\n")
        
        if cls['docstring']:
            lines.append(f"{cls['docstring']}\n")
        
        # 繼承關係
        if cls['bases']:
            lines.append(f"**繼承:** {', '.join(cls['bases'])}\n")
        
        # 主要方法
        public_methods = [m for m in cls['methods'] 
                         if not m['name'].startswith('_') or m['name'] in ['__init__']]
        
        if public_methods:
            lines.append("**主要方法:**")
            for method in public_methods[:5]:  # 只顯示前5個方法
                method_str = f"- `{method['name']}`"
                if method['args']:
                    args_str = ', '.join([arg['name'] for arg in method['args']])
                    method_str += f"({args_str})"
                if method['docstring']:
                    method_str += f": {method['docstring'].split('.')[0]}"
                lines.append(method_str)
            lines.append("")
        
        return '\n'.join(lines)
    
    def _format_config_class_doc(self, cls: Dict[str, Any]) -> str:
        """格式化配置類文檔"""
        lines = []
        
        lines.append(f"#### {cls['name']}\n")
        
        if cls['docstring']:
            lines.append(f"{cls['docstring']}\n")
        
        # 配置屬性
        if cls['attributes']:
            lines.append("**配置項:**")
            for attr in cls['attributes']:
                lines.append(f"- `{attr['name']}`: {attr['value']}")
            lines.append("")
        
        return '\n'.join(lines)
    
    def _format_module_doc(self, module: Dict[str, Any], category: str) -> str:
        """格式化模組文檔"""
        lines = []
        
        module_name = Path(module['path']).stem
        lines.append(f"### {module_name}\n")
        
        if module['docstring']:
            lines.append(f"{module['docstring']}\n")
        
        # 主要類別
        if module['classes']:
            lines.append(f"**{category}類:**")
            for cls in module['classes'][:3]:  # 只顯示前3個類
                lines.append(f"- `{cls['name']}`")
                if cls['docstring']:
                    lines.append(f"  {cls['docstring'].split('.')[0]}")
            lines.append("")
        
        return '\n'.join(lines)
    
    def _write_docs(self, docs: Dict[str, str]):
        """寫入文檔文件"""
        for doc_name, content in docs.items():
            if self.config.output_format == "markdown":
                output_file = Path(self.config.output_dir) / f"{doc_name}.md"
                with open(output_file, 'w', encoding=self.config.encoding) as f:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    formatted_content = MARKDOWN_TEMPLATE.format(
                        title=doc_name.title() + " API文檔",
                        description=f"自動生成的{doc_name}API文檔",
                        toc="<!-- 目錄自動生成 -->",
                        content=content,
                        timestamp=timestamp
                    )
                    f.write(formatted_content)
            
            elif self.config.output_format == "json":
                output_file = Path(self.config.output_dir) / f"{doc_name}.json"
                with open(output_file, 'w', encoding=self.config.encoding) as f:
                    json.dump({'content': content}, f, ensure_ascii=False, indent=2)


def create_doc_generator(output_dir: str = "./docs/api", **kwargs) -> DocGenerator:
    """創建文檔生成器"""
    config = DocConfig(output_dir=output_dir, **kwargs)
    return DocGenerator(config)


def generate_api_docs(project_root: str, output_dir: str = "./docs/api", **kwargs) -> Dict[str, Any]:
    """快速生成API文檔"""
    generator = create_doc_generator(output_dir=output_dir, **kwargs)
    return generator.generate_docs(project_root)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="API文檔自動生成器")
    parser.add_argument("--project", "-p", 
                       default="./", 
                       help="項目根目錄路徑")
    parser.add_argument("--output", "-o",
                       default="./docs/api",
                       help="輸出目錄")
    parser.add_argument("--format", "-f",
                       choices=["markdown", "html", "json"],
                       default="markdown",
                       help="輸出格式")
    parser.add_argument("--verbose", "-v",
                       action="store_true",
                       help="詳細輸出")
    
    args = parser.parse_args()
    
    # 設置日誌
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 生成文檔
    result = generate_api_docs(
        project_root=args.project,
        output_dir=args.output,
        output_format=args.format,
        include_examples=True,
        generate_index=True
    )
    
    print(f"文檔生成完成: {result}")