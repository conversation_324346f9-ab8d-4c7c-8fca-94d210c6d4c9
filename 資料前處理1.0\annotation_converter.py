import os
import json
import xml.etree.ElementTree as ET
import logging
from pathlib import Path
from PIL import Image
import numpy as np
import argparse
import shutil


class AnnotationConverter:
    def __init__(self, logger=None):
        """初始化標籤轉換器"""
        self.logger = logger or logging.getLogger(__name__)

        # 轉換統計
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }

    def convert_format(self, input_path, output_path, source_format='auto', target_shape='polygon', resize=0.3, quality=75):
        """
        將各種格式轉換為LabelMe格式

        參數:
            input_path: 輸入文件或目錄路徑
            output_path: 輸出目錄路徑
            source_format: 輸入格式 ('auto', 'voc', 'yolo', 'coco', 'labelme')
            target_shape: 轉換目標形狀 ('box', 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數

        返回:
            轉換統計信息字典
        """
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }

        # 確保輸出目錄存在
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)

        # 實現格式自動檢測
        if source_format == 'auto':
            source_format = self._detect_format(input_path)
            self.logger.info(f"檢測到輸入格式: {source_format}")

        # 實現各種格式到LabelMe的轉換
        converters = {
            'voc': self._voc_to_labelme,
            'yolo': self._yolo_to_labelme,
            'coco': self._coco_to_labelme
        }

        # 如果輸入是 LabelMe 格式，直接進行檢查和處理
        if source_format == 'labelme':
            self.logger.info(f"檢測到LabelMe格式，直接進行檢查和圖像處理")
            check_stats = self.validate_annotations(input_path, output_path, resize=resize, quality=quality)
            # 更新統計信息
            self.stats.update(check_stats)
        # 如果輸入是其他支持的格式，先轉換再檢查
        elif source_format in converters:
            self.logger.info(f"開始將 {source_format} 格式轉換為 LabelMe 格式")
            converters[source_format](input_path, output_path, target_shape, resize=resize, quality=quality)
            # 執行檢查
            check_stats = self.validate_annotations(output_path, resize=resize, quality=quality)
            # 更新統計信息
            self.stats.update(check_stats)
        else:
            self.logger.error(f"不支持的源格式: {source_format}")
            return self.stats

        self.logger.info(f"處理完成: 總計 {self.stats['total']} 個文件")
        self.logger.info(
            f"成功: {self.stats.get('success', 0)}, 失敗: {self.stats.get('failed', 0)}, 跳過: {self.stats.get('skipped', 0)}")

        return self.stats

    def _detect_format(self, path):
        """
        檢測輸入路徑的標註格式
        """
        path = Path(path)
        # 如果是目錄
        if path.is_dir():
            # 首先檢查是否有 .json 文件（LabelMe格式）
            json_files = list(path.glob("*.json"))
            if json_files:
                for json_file in json_files:
                    try:
                        with open(json_file, 'r', encoding='utf-8-sig') as f:
                            data = json.load(f)
                            # LabelMe格式通常有以下特徵
                            if "version" in data and "imagePath" in data and "shapes" in data:
                                return "labelme"
                    except Exception:
                        continue

            # 檢查是否有 .xml 文件（VOC格式）
            xml_files = list(path.glob("*.xml"))
            if xml_files:
                sample_xml = xml_files[0]
                try:
                    tree = ET.parse(sample_xml)
                    root = tree.getroot()
                    if root.tag == "annotation":
                        return "voc"
                except Exception:
                    pass

            # 檢查是否有 .txt 文件（YOLO格式）
            txt_files = list(path.glob("*.txt"))
            if txt_files:
                sample_txt = txt_files[0]
                try:
                    with open(sample_txt, 'r') as f:
                        line = f.readline().strip()
                        parts = line.split()
                        if len(parts) >= 5 and parts[0].isdigit():
                            return "yolo"
                except Exception:
                    pass

            # 檢查是否有 .json 文件（COCO格式）
            annotation_files = list(path.glob("*.json"))
            if annotation_files:
                for annotation_file in annotation_files:
                    try:
                        with open(annotation_file, 'r', encoding='utf-8-sig') as f:
                            data = json.load(f)
                            if "images" in data and "annotations" in data and "categories" in data:
                                return "coco"
                    except Exception:
                        continue

        # 如果是單個文件
        elif path.is_file():
            # 檢查是否是 JSON 文件（LabelMe格式）
            if path.suffix.lower() == ".json":
                try:
                    with open(path, 'r', encoding='utf-8-sig') as f:
                        data = json.load(f)
                        if "version" in data and "imagePath" in data and "annotations" in data:
                            return "labelme"
                except Exception:
                    pass

            # 檢查是否是 XML 文件（VOC格式）
            if path.suffix.lower() == ".xml":
                try:
                    tree = ET.parse(path)
                    root = tree.getroot()
                    if root.tag == "annotation":
                        return "voc"
                except Exception:
                    pass

            # 檢查是否是 JSON 文件（COCO格式）
            if path.suffix.lower() == ".json":
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if "images" in data and "annotations" in data and "categories" in data:
                            return "coco"
                except Exception:
                    pass

        self.logger.warning(f"無法檢測格式，使用默認格式: voc")
        return "voc"

    def _voc_to_labelme(self, input_path, output_path, target_shape, resize=0.3, quality=75):
        """
        VOC XML 轉 LabelMe

        參數:
            input_path: 輸入目錄路徑，包含 VOC XML 文件
            output_path: 輸出目錄路徑
            target_shape: 目標形狀 ('box' 或 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數
        """
        input_path = Path(input_path)
        output_path = Path(output_path)

        # 如果輸入是目錄，則獲取所有 XML 文件（包括子目錄）
        if input_path.is_dir():
            xml_files = list(input_path.glob("**/*.xml"))  # 使用 ** 搜索所有子目錄
        # 如果輸入是單個文件，則直接處理
        elif input_path.is_file() and input_path.suffix.lower() == ".xml":
            xml_files = [input_path]
        else:
            self.logger.error(f"無效的輸入路徑: {input_path}")
            return

        self.stats["total"] = len(xml_files)
        self.stats["formats"]["voc"] = len(xml_files)

        for xml_file in xml_files:
            try:
                # 解析 XML
                tree = ET.parse(xml_file)
                root = tree.getroot()

                # 提取基本信息
                filename = root.find('filename').text
                size_elem = root.find('size')
                width = int(size_elem.find('width').text)
                height = int(size_elem.find('height').text)

                # 提取所有物體
                annotations = []
                for obj in root.findall('object'):
                    label = obj.find('name').text
                    bndbox = obj.find('bndbox')
                    xmin = float(bndbox.find('xmin').text)
                    ymin = float(bndbox.find('ymin').text)
                    xmax = float(bndbox.find('xmax').text)
                    ymax = float(bndbox.find('ymax').text)

                    if target_shape == 'polygon':
                        # 轉換為多邊形
                        points = self._get_polygon_from_box(
                            xmin, ymin, xmax, ymax)
                        shape_type = "polygon"
                    else:
                        # 保持為矩形
                        points = [[xmin, ymin], [xmax, ymax]]
                        shape_type = "rectangle"

                    annotations.append({
                        "label": label,
                        "points": points,
                        "group_id": None,
                        "shape_type": shape_type,
                        "flags": {}
                    })

                # 創建 LabelMe JSON
                labelme_data = {
                    "version": "4.5.6",
                    "flags": {},
                    "annotations": annotations,
                    "imagePath": filename,
                    "imageData": None,
                    "imageHeight": height,
                    "imageWidth": width
                }

                # 寫入 JSON 文件
                output_json = output_path / f"{xml_file.stem}.json"
                with open(output_json, 'w', encoding='utf-8') as f:
                    json.dump(labelme_data, f, ensure_ascii=False, indent=2)
                
                # 複製對應的圖像文件到輸出目錄
                # 從包含XML文件的目錄開始查找圖像文件
                source_img_dir = xml_file.parent
                self._copy_image_file(source_img_dir, output_path, filename, resize=resize, quality=quality)

                self.logger.info(
                    f"成功轉換: {xml_file.name} -> {output_json.name}")
                self.stats["success"] += 1

            except Exception as e:
                self.logger.error(f"轉換失敗: {xml_file.name}, 錯誤: {e}")
                self.stats["failed"] += 1

    def _yolo_to_labelme(self, input_path, output_path, target_shape, resize=0.3, quality=75):
        """
        YOLO 轉 LabelMe

        參數:
            input_path: 輸入目錄路徑，包含 YOLO txt 文件和對應的圖像
            output_path: 輸出目錄路徑
            target_shape: 目標形狀 ('box' 或 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數
        """
        input_path = Path(input_path)
        output_path = Path(output_path)

        # YOLO格式需要對應的圖像來獲取尺寸信息
        if not input_path.is_dir():
            self.logger.error(f"YOLO格式轉換需要輸入目錄包含 .txt 文件和對應的圖像: {input_path}")
            return

        # 獲取所有 txt 文件（包括子目錄）
        txt_files = list(input_path.glob("**/*.txt"))

        # 排除 classes.txt 和資料夾路徑包含"classes"的文件
        txt_files = [f for f in txt_files if f.name.lower() != "classes.txt" and "classes" not in str(f).lower()]

        self.stats["total"] = len(txt_files)
        self.stats["formats"]["yolo"] = len(txt_files)

        # 讀取類別映射（如果有classes.txt）
        # 在輸入目錄及其子目錄中查找classes.txt
        classes_file = None
        for classes_path in input_path.glob("**/classes.txt"):
            classes_file = classes_path
            break
        
        # 如果找不到classes.txt，找data.yaml或dataset.yaml
        if classes_file is None:
            for yaml_path in input_path.glob("**/*.yaml"):
                if yaml_path.name.lower() in ["data.yaml", "dataset.yaml"]:
                    # 這裡需要解析YAML文件來獲取類別，但此簡化版不實現
                    self.logger.info(f"找到YAML配置文件: {yaml_path}，但不解析。請提供classes.txt")
                    break
        
        class_names = []
        if classes_file and classes_file.exists():
            try:
                with open(classes_file, 'r', encoding='utf-8') as f:
                    class_names = [line.strip() for line in f if line.strip()]
                self.logger.info(f"讀取到 {len(class_names)} 個類別名稱，來自 {classes_file}")
            except Exception as e:
                self.logger.warning(f"無法讀取 classes.txt: {e}")

        for txt_file in txt_files:
            # 查找對應的圖像文件，可能在txt文件所在目錄或其他相關目錄
            img_file = self._find_related_files(
                txt_file.parent,  # 從txt文件所在目錄開始查找
                txt_file.stem,   # 使用txt文件的基本名稱
                ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                recursive=True   # 允許在子目錄中查找
            )

            if not img_file:
                self.logger.warning(f"找不到對應的圖像文件: {txt_file.stem}.*，跳過")
                self.stats["skipped"] += 1
                continue

            try:
                # 獲取圖像尺寸
                with Image.open(img_file) as img:
                    img_width, img_height = img.size

                # 讀取 YOLO 格式文件
                with open(txt_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                annotations = []
                for line in lines:
                    parts = line.strip().split()
                    if len(parts) < 5:
                        continue

                    class_id = int(parts[0])

                    # 獲取類別名稱
                    if class_id < len(class_names):
                        label = class_names[class_id]
                    else:
                        label = f"class_{class_id}"

                    # 判斷是否為多邊形標註
                    if len(parts) > 5 and len(parts) % 2 == 1:  # 多邊形標註
                        points = []
                        for i in range(1, len(parts), 2):
                            x = float(parts[i]) * img_width
                            y = float(parts[i+1]) * img_height
                            points.append([x, y])

                        shape_type = "polygon"
                    else:  # 標準 YOLO 矩形框
                        # YOLO 格式: [類別id, 中心x, 中心y, 寬, 高]
                        x_center = float(parts[1]) * img_width
                        y_center = float(parts[2]) * img_height
                        box_width = float(parts[3]) * img_width
                        box_height = float(parts[4]) * img_height

                        # 計算四個角點
                        xmin = x_center - (box_width / 2)
                        ymin = y_center - (box_height / 2)
                        xmax = x_center + (box_width / 2)
                        ymax = y_center + (box_height / 2)

                        if target_shape == 'polygon':
                            points = self._get_polygon_from_box(
                                xmin, ymin, xmax, ymax)
                            shape_type = "polygon"
                        else:
                            points = [[xmin, ymin], [xmax, ymax]]
                            shape_type = "rectangle"

                    annotations.append({
                        "label": label,
                        "points": points,
                        "group_id": None,
                        "shape_type": shape_type,
                        "flags": {}
                    })

                # 創建 LabelMe JSON
                labelme_data = {
                    "version": "4.5.6",
                    "flags": {},
                    "annotations": annotations,
                    "imagePath": img_file.name,
                    "imageData": None,
                    "imageHeight": img_height,
                    "imageWidth": img_width
                }

                # 寫入 JSON 文件
                output_json = output_path / f"{txt_file.stem}.json"
                with open(output_json, 'w', encoding='utf-8') as f:
                    json.dump(labelme_data, f, ensure_ascii=False, indent=2)
                
                # 複製對應的圖像文件到輸出目錄
                self._copy_image_file(img_file.parent, output_path, img_file.name, resize=resize, quality=quality)

                self.logger.info(
                    f"成功轉換: {txt_file.name} -> {output_json.name}")
                self.stats["success"] += 1

            except Exception as e:
                self.logger.error(f"轉換失敗: {txt_file.name}, 錯誤: {e}")
                self.stats["failed"] += 1

    def _coco_to_labelme(self, input_path, output_path, target_shape, resize=0.3, quality=75):
        """
        COCO JSON 轉 LabelMe

        參數:
            input_path: 輸入文件路徑，COCO JSON 文件
            output_path: 輸出目錄路徑
            target_shape: 目標形狀 ('box' 或 'polygon')
            resize: 圖像縮放參數
            quality: 圖像品質參數
        """
        input_path = Path(input_path)
        output_path = Path(output_path)

        # 需要找到對應的圖像目錄
        img_dir = input_path.parent if input_path.is_file() else input_path

        try:
            # 讀取 COCO JSON
            if input_path.is_dir():
                # 查找目錄中的 COCO 格式 JSON 文件
                annotation_files = list(input_path.glob("*.json"))
                coco_file = None
                for annotation_file in annotation_files:
                    try:
                        with open(annotation_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if "images" in data and "annotations" in data and "categories" in data:
                                coco_file = annotation_file
                                break
                    except Exception:
                        continue

                if coco_file is None:
                    self.logger.error(
                        f"在目錄中找不到 COCO 格式的 JSON 文件: {input_path}")
                    return
            else:
                coco_file = input_path

            with open(coco_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)

            # 獲取類別映射
            categories = {cat["id"]: cat["name"]
                          for cat in coco_data.get("categories", [])}

            # 建立圖像ID到標註的映射
            annotations_by_image = {}
            for ann in coco_data.get("annotations", []):
                img_id = ann["image_id"]
                if img_id not in annotations_by_image:
                    annotations_by_image[img_id] = []
                annotations_by_image[img_id].append(ann)

            # 處理每一個圖像
            total_images = len(coco_data.get("images", []))
            self.stats["total"] = total_images
            self.stats["formats"]["coco"] = total_images

            for img_info in coco_data.get("images", []):
                img_id = img_info["id"]
                img_file_name = img_info["file_name"]
                img_width = img_info["width"]
                img_height = img_info["height"]

                # 獲取圖像的標註
                anns = annotations_by_image.get(img_id, [])

                annotations = []
                for ann in anns:
                    cat_id = ann.get("category_id")
                    label = categories.get(cat_id, f"category_{cat_id}")

                    # 處理分割標註（多邊形）
                    if "segmentation" in ann and ann["segmentation"] and target_shape == 'polygon':
                        for seg in ann["segmentation"]:
                            if len(seg) >= 6:  # 至少需要三個點
                                points = []
                                for i in range(0, len(seg), 2):
                                    points.append([seg[i], seg[i+1]])

                                annotations.append({
                                    "label": label,
                                    "points": points,
                                    "group_id": None,
                                    "shape_type": "polygon",
                                    "flags": {}
                                })
                    else:  # 使用邊界框
                        bbox = ann.get("bbox", [0, 0, 0, 0])
                        x, y, w, h = bbox
                        xmin, ymin = x, y
                        xmax, ymax = x + w, y + h

                        if target_shape == 'polygon':
                            points = self._get_polygon_from_box(
                                xmin, ymin, xmax, ymax)
                            shape_type = "polygon"
                        else:
                            points = [[xmin, ymin], [xmax, ymax]]
                            shape_type = "rectangle"

                        annotations.append({
                            "label": label,
                            "points": points,
                            "group_id": None,
                            "shape_type": shape_type,
                            "flags": {}
                        })

                # 創建 LabelMe JSON
                labelme_data = {
                    "version": "4.5.6",
                    "flags": {},
                    "annotations": annotations,
                    "imagePath": img_file_name,
                    "imageData": None,
                    "imageHeight": img_height,
                    "imageWidth": img_width
                }

                # 寫入 JSON 文件
                output_stem = Path(img_file_name).stem
                output_json = output_path / f"{output_stem}.json"
                with open(output_json, 'w', encoding='utf-8') as f:
                    json.dump(labelme_data, f, ensure_ascii=False, indent=2)
                
                # 尋找並複製對應的圖像文件到輸出目錄
                image_file_path = img_dir / img_file_name
                if image_file_path.exists():
                    self._copy_image_file(img_dir, output_path, img_file_name, resize=resize, quality=quality)

                self.logger.info(
                    f"成功轉換: {img_file_name} -> {output_json.name}")
                self.stats["success"] += 1

        except Exception as e:
            self.logger.error(f"轉換 COCO 格式失敗, 錯誤: {e}")
            self.stats["failed"] = total_images if 'total_images' in locals() else 1

    def _get_polygon_from_box(self, xmin, ymin, xmax, ymax):
        """將矩形框轉為四角多邊形"""
        return [[xmin, ymin], [xmax, ymin], [xmax, ymax], [xmin, ymax]]

    def _copy_image_file(self, source_dir, target_dir, image_name, resize=0.3, quality=75):
        """複製圖像文件到目標目錄，並可調整大小和品質
        
        參數:
            source_dir: 源目錄
            target_dir: 目標目錄
            image_name: 圖像文件名
            resize: 可以是縮放比例(如0.3)或(寬,高)元組
            quality: JPEG壓縮品質(1-100)
        """
        source_dir = Path(source_dir)
        target_dir = Path(target_dir)
        
        # 首先嘗試直接在源目錄找到圖像
        source_path = source_dir / image_name
        if not source_path.exists():
            # 如果直接路徑不存在，使用擴展搜索功能
            source_path = self._find_related_files(
                source_dir,
                image_name,
                ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                recursive=True
            )
            
            if not source_path:
                self.logger.warning(f"找不到圖像文件: {image_name}")
                return False, None, None, None, None
        
        target_path = target_dir / Path(image_name).name
        
        # 如果源目錄和目標目錄相同且不需要調整大小/品質，則不需要處理
        if source_dir == target_dir and resize == 1.0 and quality == 100 and source_path.suffix.lower() in ['.jpg', '.jpeg']:
            return True, None, None, 1.0, 1.0
            
        try:
            # 讀取並處理圖像
            with Image.open(source_path) as img:
                # 保存原始尺寸
                orig_width, orig_height = img.size
                orig_size = (orig_width, orig_height)
                scale_x, scale_y = 1.0, 1.0
                new_size = orig_size
                # 調整大小
                # 計算新尺寸和縮放比例
                if resize != 1.0:
                    if isinstance(resize, tuple) and len(resize) == 2:
                        # 如果resize是(寬,高)元組
                        new_size = resize
                        scale_x = new_size[0] / orig_width  # 水平縮放比例
                        scale_y = new_size[1] / orig_height  # 垂直縮放比例
                        img = img.resize(new_size, Image.LANCZOS)
                    elif isinstance(resize, (int, float)):
                        # 如果resize是縮放比例
                        scale_x = scale_y = float(resize)  # 等比例縮放
                        new_size = (int(orig_width * scale_x), int(orig_height * scale_y))
                        img = img.resize(new_size, Image.LANCZOS)
                else:
                    new_size = orig_size
                    scale_x = scale_y = 1.0  # 不縮放
                
                # 確保目標路徑的副檔名是.jpg
                if target_path.suffix.lower() not in ['.jpg', '.jpeg']:
                    target_path = target_path.with_suffix('.jpg')
                
                # 儲存為JPEG，設定品質
                img.save(target_path, 'JPEG', quality=quality, optimize=True)
                
            self.logger.info(f"已處理並儲存圖像: {source_path.name} -> {target_path.name} (縮放: {scale_x:.2f}x{scale_y:.2f})")
            return True, orig_size, new_size, scale_x, scale_y
            
        except Exception as e:
            self.logger.error(f"處理圖像文件失敗: {source_path.name}, 錯誤: {e}")
            return False, None, None, None, None

    def validate_annotations(self, input_path, output_path=None, clean_image_data=True, resize=0.3, quality=75):
        """
        檢查 LabelMe 格式文件

        參數:
            input_path: 輸入目錄，包含 LabelMe JSON 文件
            output_path: 輸出目錄，默認與輸入相同
            clean_image_data: 是否清除 imageData 中的 base64 編碼
            resize: 圖像縮放參數
            quality: 圖像品質參數

        返回:
            檢查統計信息
        """
        input_path = Path(input_path)
        if output_path:
            output_path = Path(output_path)
        else:
            output_path = input_path

        # 確保輸出目錄存在
        output_path.mkdir(parents=True, exist_ok=True)

        # 獲取所有 JSON 文件（包括子目錄）
        annotation_files = list(input_path.glob("**/*.json"))

        stats = {
            "total": len(annotation_files),
            "valid": 0,
            "missing_image": 0,
            "cleaned_image_data": 0,
            "other_errors": 0
        }

        self.logger.info(f"開始檢查 {len(annotation_files)} 個 LabelMe 文件")

        for annotation_file in annotation_files:
            try:
                # 讀取 JSON
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 檢查是否是LabelMe格式
                if not ("version" in data and ("annotations" in data or "shapes" in data) and "imagePath" in data):
                    self.logger.warning(f"{annotation_file.name}: 不是有效的LabelMe格式，跳過")
                    stats["other_errors"] += 1
                    continue

                # 檢查 imagePath
                image_path = data.get("imagePath")
                if not image_path:
                    self.logger.warning(
                        f"{annotation_file.name}: 缺少 imagePath")
                    stats["other_errors"] += 1
                    continue

                # 查找對應的圖像文件
                # 首先嘗試直接在JSON文件所在目錄找圖像
                found_image_path = self._find_related_files(
                    annotation_file.parent, 
                    image_path, 
                    ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                    recursive=True
                )
                
                # 如果找不到圖像，嘗試使用JSON文件名稱查找
                if not found_image_path:
                    found_image_path = self._find_related_files(
                        annotation_file.parent, 
                        annotation_file.stem, 
                        ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'],
                        recursive=True
                    )

                # 如果仍然找不到圖像，標記為缺少圖像
                if not found_image_path:
                    self.logger.warning(f"{annotation_file.name}: 找不到對應的圖像文件")
                    stats["missing_image"] += 1
                    
                    # 如果是同一路徑，直接刪除缺少圖像的 JSON 文件
                    if input_path == output_path:
                        os.remove(annotation_file)
                        self.logger.info(f"已刪除缺少圖像的標籤文件: {annotation_file.name}")
                    continue

                # 更新圖像路徑為找到的實際文件名
                data["imagePath"] = found_image_path.name

                # 清除 imageData
                if clean_image_data and "imageData" in data and data["imageData"]:
                    data["imageData"] = None
                    stats["cleaned_image_data"] += 1
                    
                # 複製對應的圖像文件到輸出目錄，並進行調整
                if found_image_path:
                    try:
                        # 使用改進的圖像處理函數，獲取縮放資訊
                        success, orig_size, new_size, scale_x, scale_y = self._copy_image_file(
                            found_image_path.parent, output_path, found_image_path.name, resize=resize, quality=quality
                        )
                        
                        # 如果圖像成功處理並且尺寸發生變化
                        if success and orig_size != new_size and scale_x is not None and scale_y is not None:
                            # 調整標籤座標
                            data = self._adjust_annotation_coordinates(
                                data, scale_x, scale_y, new_size[0], new_size[1]
                            )
                            self.logger.info(f"已調整標籤座標: {annotation_file.name} (縮放: {scale_x:.2f}x{scale_y:.2f})")
                            
                    except Exception as e:
                        self.logger.error(f"處理圖像或調整座標時出錯: {found_image_path.name}, 錯誤: {e}")
                
                # 保存修改後的 JSON
                output_json = output_path / annotation_file.name
                with open(output_json, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                stats["valid"] += 1

            except Exception as e:
                self.logger.error(f"處理 {annotation_file.name} 時出錯: {e}")
                stats["other_errors"] += 1

        self.logger.info(
            f"檢查完成: 總計 {stats['total']} 個文件，有效 {stats['valid']}，缺少圖像 {stats['missing_image']}，清理 imageData {stats['cleaned_image_data']}，其他錯誤 {stats['other_errors']}")

        return stats

    def process_multiple_inputs(self, input_paths, output_path, resize=0.3, quality=75, force_overwrite=False):
        """
        處理多個輸入路徑

        參數:
            input_paths: 輸入路徑列表
            output_path: 輸出目錄路徑
            resize: 圖像縮放參數
            quality: 圖像品質參數
            force_overwrite: 強制覆寫已存在檔案
        
        返回:
            整體處理統計信息
        """
        overall_stats = {
            "total_processed": 0,
            "total_success": 0,
            "total_failed": 0,
            "total_skipped": 0,
            "total_reused": 0,  # 新增：重用已存在檔案的計數
            "paths": {}
        }

        # 確保輸出目錄存在
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)

        for input_path in input_paths:
            input_path = Path(input_path)
            
            # 為每個輸入路徑創建對應的輸出子目錄
            path_output = output_path / input_path.name
            path_output.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"開始處理輸入路徑: {input_path}")
            
            # 使用 polygon 作為默認目標形狀
            stats = self.convert_format(
                input_path, 
                path_output, 
                source_format='auto', 
                target_shape='polygon',
                resize=resize,
                quality=quality,
                force_overwrite=force_overwrite
            )
            
            # 記錄每個路徑的處理結果
            overall_stats["paths"][str(input_path)] = stats
            
            # 更新總體統計
            overall_stats["total_processed"] += stats.get("total", 0)
            overall_stats["total_success"] += stats.get("success", 0) 
            overall_stats["total_failed"] += stats.get("failed", 0)
            overall_stats["total_skipped"] += stats.get("skipped", 0)
            overall_stats["total_reused"] += stats.get("reused", 0)  # 更新重用檔案統計
            
            self.logger.info(f"完成處理: {input_path}")
        
        self.logger.info(f"所有路徑處理完成！")
        self.logger.info(f"總計處理: {overall_stats['total_processed']} 個文件")
        self.logger.info(f"總成功: {overall_stats['total_success']}, 總失敗: {overall_stats['total_failed']}, 總跳過: {overall_stats['total_skipped']}, 總重用: {overall_stats['total_reused']}")
        
        return overall_stats

    def _adjust_annotation_coordinates(self, data, scale_x, scale_y, new_width, new_height):
        """調整標籤座標以匹配縮放後的圖像尺寸
        
        參數:
            data: LabelMe格式的標籤數據
            scale_x: X方向縮放比例
            scale_y: Y方向縮放比例
            new_width: 新圖像寬度
            new_height: 新圖像高度
        
        返回:
            調整後的標籤數據
        """
        # 更新圖像尺寸
        data["imageWidth"] = new_width
        data["imageHeight"] = new_height
        
        # 更新所有標籤的座標
        if "annotations" in data:
            for anno in data["annotations"]:
                # 處理點座標
                if "points" in anno:
                    new_points = []
                    for point in anno["points"]:
                        # 根據縮放比例調整座標
                        new_x = point[0] * scale_x
                        new_y = point[1] * scale_y
                        new_points.append([new_x, new_y])
                    anno["points"] = new_points
        
        # 如果使用舊版LabelMe格式(shapes而非annotations)
        elif "shapes" in data:
            for shape in data["shapes"]:
                # 處理點座標
                if "points" in shape:
                    new_points = []
                    for point in shape["points"]:
                        # 根據縮放比例調整座標
                        new_x = point[0] * scale_x
                        new_y = point[1] * scale_y
                        new_points.append([new_x, new_y])
                    shape["points"] = new_points
        
        return data

    def _find_related_files(self, base_path, filename, extensions, recursive=True):
        """
        在目錄中尋找相關文件（可能在子目錄中）
        
        參數:
            base_path: 基礎路徑
            filename: 文件名（可以是帶或不帶擴展名的基本名稱）
            extensions: 可能的擴展名列表，如 ['.jpg', '.png']
            recursive: 是否遞迴搜索子目錄
            
        返回:
            找到的文件路徑，如果沒找到則返回 None
        """
        base_path = Path(base_path)
        basename = Path(filename).stem
        
        # 首先在當前目錄中尋找
        for ext in extensions:
            file_path = base_path / f"{basename}{ext}"
            if file_path.exists():
                return file_path
        
        # 如果沒找到並且允許遞迴搜索
        if recursive:
            # 在子目錄中尋找
            for subdir in base_path.glob('*'):
                if subdir.is_dir():
                    # 檢查常見的子目錄命名模式
                    common_dirs = [subdir]
                    if subdir.name.lower() in ['images', 'image', 'img', 'imgs', 'pictures', 'pics']:
                        common_dirs.append(subdir)
                    elif 'label' in subdir.name.lower() or 'annotation' in subdir.name.lower() or 'json' in subdir.name.lower():
                        # 如果是標籤目錄，可能需要檢查平行的圖像目錄
                        for img_dir_name in ['images', 'image', 'img', 'imgs', 'pictures', 'pics']:
                            img_dir = base_path / img_dir_name
                            if img_dir.exists():
                                common_dirs.append(img_dir)
                    
                    # 在這些目錄中尋找文件
                    for search_dir in common_dirs:
                        for ext in extensions:
                            file_path = search_dir / f"{basename}{ext}"
                            if file_path.exists():
                                return file_path
                    
                    # 遞迴搜索更深層的子目錄
                    result = self._find_related_files(subdir, basename, extensions, recursive=True)
                    if result:
                        return result


# 如果作為獨立腳本運行
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="標籤格式自動轉換與檢查工具")
    parser.add_argument("--inputs", nargs='+', required=True, help="輸入路徑，可指定多個路徑")
    parser.add_argument("--output", required=True, help="輸出路徑")
    parser.add_argument("--resize", type=str, default="0.3", help="調整圖像大小，可以是縮放比例(如0.3)或寬x高格式(如'800x600')")
    parser.add_argument("--quality", type=int, default=75, help="JPEG品質設定(1-100)，預設為75")
    parser.add_argument("--force", action="store_true", help="強制覆寫已存在檔案")
    
    args = parser.parse_args()

    # 處理resize參數
    resize_value = args.resize
    if 'x' in resize_value:
        # 如果是寬x高格式
        try:
            width, height = map(int, resize_value.split('x'))
            resize_param = (width, height)
        except ValueError:
            print(f"無效的尺寸格式: {resize_value}，使用預設值0.3")
            resize_param = 0.3
    else:
        # 如果是縮放比例
        try:
            resize_param = float(resize_value)
        except ValueError:
            print(f"無效的縮放比例: {resize_value}，使用預設值0.3")
            resize_param = 0.3

    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("annotation_converter.log", mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("AnnotationConverter")

    converter = AnnotationConverter(logger)
    
    # 處理多個輸入路徑
    stats = converter.process_multiple_inputs(
        args.inputs, 
        args.output, 
        resize=resize_param, 
        quality=args.quality,
        force_overwrite=args.force
    )
    
    logger.info(f"處理完成！統計: {stats}")
    print(f"\n處理完成！共處理 {stats['total_processed']} 個檔案")
    print(f"成功: {stats['total_success']}，失敗: {stats['total_failed']}，跳過: {stats['total_skipped']}，重用: {stats['total_reused']}")
    print(f"圖像已使用縮放參數: {resize_param}, 品質: {args.quality}")
    print(f"詳細日誌請查看 annotation_converter.log")