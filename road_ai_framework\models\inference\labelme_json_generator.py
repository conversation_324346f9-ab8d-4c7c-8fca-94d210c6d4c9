#!/usr/bin/env python3
"""
🏷️ LabelMe JSON Generator
將YOLO模型預測結果轉換為LabelMe格式的JSON檔案

功能：
- 支援一般推理和切片推理模式
- 將mask轉換為polygon格式
- 生成標準LabelMe JSON格式
- 不使用base64編碼，包含圖像檔名
- 支援多類別和多實例檢測結果
"""

import json
import cv2
import numpy as np
import base64  # 🆕 添加base64模組
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging


class LabelMeJSONGenerator:
    """LabelMe JSON格式生成器"""

    def __init__(self, simplify_tolerance: float = 2.0):
        """
        初始化LabelMe JSON生成器

        Args:
            simplify_tolerance: Polygon簡化容差，數值越大polygon越簡化
        """
        self.simplify_tolerance = simplify_tolerance
        self.version = "4.5.6"  # LabelMe版本

    def mask_to_polygon(self, mask: np.ndarray, simplify: bool = True) -> List[List[float]]:
        """
        將二進制mask轉換為polygon點列表

        Args:
            mask: 二進制mask (0/1或0/255)
            simplify: 是否簡化polygon

        Returns:
            Polygon點列表 [[x1,y1], [x2,y2], ...]
        """
        try:
            # 確保mask是正確的格式
            if mask.dtype != np.uint8:
                mask = mask.astype(np.uint8)

            # 如果mask值是0/1，轉換為0/255
            if mask.max() <= 1:
                mask = mask * 255

            # 找到輪廓
            contours, _ = cv2.findContours(
                mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return []

            # 選擇最大的輪廓
            largest_contour = max(contours, key=cv2.contourArea)

            # 簡化polygon（可選）
            if simplify and self.simplify_tolerance > 0:
                epsilon = self.simplify_tolerance
                largest_contour = cv2.approxPolyDP(
                    largest_contour, epsilon, True)

            # 轉換為LabelMe格式的點列表
            polygon = []
            for point in largest_contour:
                x, y = point[0]
                polygon.append([float(x), float(y)])

            return polygon

        except Exception as e:
            logging.warning(f"Failed to convert mask to polygon: {e}")
            return []

    def create_shape_object(self, label: str, polygon: List[List[float]],
                            shape_type: str = "polygon", group_id: Optional[int] = None) -> Dict[str, Any]:
        """
        創建LabelMe shape物件

        Args:
            label: 類別標籤
            polygon: Polygon點列表
            shape_type: 形狀類型，默認為"polygon"
            group_id: 群組ID（可選）

        Returns:
            LabelMe shape物件
        """
        return {
            "label": label,
            "points": polygon,
            "group_id": group_id,
            "shape_type": shape_type,
            "flags": {}
        }

    def generate_labelme_json(self, detections: List[Dict], image_path: str,
                              image_shape: Tuple[int, int], class_names: Dict[int, str],
                              save_path: Optional[str] = None, include_base64_image: bool = False) -> Dict[str, Any]:
        """
        生成完整的LabelMe JSON格式

        Args:
            detections: 檢測結果列表，每個檢測包含mask、class_id、confidence等
            image_path: 圖像檔案路徑
            image_shape: 圖像尺寸 (height, width)
            class_names: 類別ID到名稱的映射
            save_path: JSON保存路徑（可選）

        Returns:
            LabelMe JSON物件
        """
        try:
            image_height, image_width = image_shape[:2]
            image_filename = Path(image_path).name

            # 初始化LabelMe JSON結構
            labelme_json = {
                "version": self.version,
                "flags": {},
                "shapes": [],
                "imagePath": image_filename,
                "imageData": None,  # 預設不使用base64
                "imageHeight": int(image_height),
                "imageWidth": int(image_width)
            }

            # 如果啟用base64圖像，則讀取圖像並編碼
            if include_base64_image:
                try:
                    with open(image_path, 'rb') as f:
                        image_bytes = f.read()
                    labelme_json['imageData'] = base64.b64encode(
                        image_bytes).decode('utf-8')
                except Exception as e:
                    logging.warning(f"無法將圖像 {image_path} 編碼為base64: {e}")
                    labelme_json['imageData'] = None

            # 處理每個檢測結果
            for idx, detection in enumerate(detections):
                try:
                    # 獲取檢測信息
                    class_id = detection.get('class_id', 0)
                    confidence = detection.get('confidence', 0.0)
                    mask = detection.get('mask', None)

                    if mask is None:
                        continue

                    # 獲取類別名稱
                    class_name = class_names.get(class_id, f"class_{class_id}")

                    # 將mask轉換為polygon
                    polygon = self.mask_to_polygon(mask)

                    if len(polygon) < 3:  # polygon至少需要3個點
                        continue

                    # 創建shape物件
                    shape = self.create_shape_object(
                        label=class_name,
                        polygon=polygon,
                        group_id=None
                    )

                    labelme_json["shapes"].append(shape)

                except Exception as e:
                    logging.warning(f"Failed to process detection {idx}: {e}")
                    continue

            # 保存JSON檔案（如果指定了路徑）
            if save_path:
                self.save_json(labelme_json, save_path)

            return labelme_json

        except Exception as e:
            logging.error(f"Failed to generate LabelMe JSON: {e}")
            return {}

    def save_json(self, labelme_json: Dict[str, Any], save_path: str) -> bool:
        """
        保存LabelMe JSON到檔案

        Args:
            labelme_json: LabelMe JSON物件
            save_path: 保存路徑

        Returns:
            是否保存成功
        """
        try:
            # 確保輸出目錄存在
            output_dir = Path(save_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存JSON檔案
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(labelme_json, f, indent=2, ensure_ascii=False)

            # 獲取檔案資訊用於詳細信息
            file_size = Path(save_path).stat().st_size
            shapes_count = len(labelme_json.get('shapes', []))

            logging.info(f"✅ LabelMe JSON儲存成功: {Path(save_path).name}")
            logging.info(f"   📁 檔案路徑: {save_path}")
            logging.info(f"   📊 檢測數量: {shapes_count} 個物件")
            logging.info(f"   📋 檔案大小: {file_size:,} bytes")

            return True

        except Exception as e:
            logging.error(f"❌ Failed to save LabelMe JSON: {e}")
            return False

    def process_image_results(self, image_path: str, results: List[Dict],
                              output_dir: str, class_names: Dict[int, str], include_base64_image: bool = False) -> Optional[str]:
        """
        處理單張圖像的結果並生成LabelMe JSON

        Args:
            image_path: 圖像路徑
            results: 檢測結果列表
            output_dir: 輸出目錄
            class_names: 類別名稱映射

        Returns:
            生成的JSON檔案路徑
        """
        try:
            # 讀取圖像獲取尺寸
            image = cv2.imread(image_path)
            if image is None:
                logging.error(f"Failed to read image: {image_path}")
                return None

            image_shape = image.shape

            # 生成JSON檔案名
            image_name = Path(image_path).stem
            json_filename = f"{image_name}.json"
            json_path = Path(output_dir) / json_filename

            # 生成LabelMe JSON
            labelme_json = self.generate_labelme_json(
                detections=results,
                image_path=image_path,
                image_shape=image_shape,
                class_names=class_names,
                save_path=str(json_path),
                include_base64_image=include_base64_image
            )

            if labelme_json and labelme_json.get("shapes"):
                print(f"🎉 LabelMe JSON儲存完成: {json_filename}")
                print(f"   📁 輸出位置: {json_path}")
                print(f"   📊 包含物件: {len(labelme_json['shapes'])} 個")
                return str(json_path)
            else:
                logging.warning(f"No valid shapes generated for {image_path}")
                return None

        except Exception as e:
            logging.error(f"Failed to process image results: {e}")
            return None

    def batch_process_results(self, batch_results: List[Dict], output_dir: str,
                              class_names: Dict[int, str], include_base64_image: bool = False) -> List[str]:
        """
        批次處理多張圖像的結果

        Args:
            batch_results: 批次結果列表，每個包含image_path和detections
            output_dir: 輸出目錄
            class_names: 類別名稱映射

        Returns:
            生成的JSON檔案路徑列表
        """
        generated_files = []

        for result in batch_results:
            try:
                image_path = result.get('image_path')
                detections = result.get('detections', [])

                if not image_path or not detections:
                    continue

                json_path = self.process_image_results(
                    image_path=image_path,
                    results=detections,
                    output_dir=output_dir,
                    class_names=class_names,
                    include_base64_image=include_base64_image
                )

                if json_path:
                    generated_files.append(json_path)

            except Exception as e:
                logging.error(f"Failed to process batch result: {e}")
                continue

        # 顯示批次處理總結
        if generated_files:
            print(f"\n🎊 批次LabelMe JSON儲存完成!")
            print(f"   📁 輸出目錄: {output_dir}")
            print(f"   📊 成功生成: {len(generated_files)} 個JSON檔案")
            print(f"   📋 檔案列表:")
            for file_path in generated_files:
                print(f"      📄 {Path(file_path).name}")

        return generated_files


# 工廠函數
def create_labelme_json_generator(simplify_tolerance: float = 2.0) -> LabelMeJSONGenerator:
    """
    創建LabelMe JSON生成器的工廠函數

    Args:
        simplify_tolerance: Polygon簡化容差

    Returns:
        LabelMe JSON生成器實例
    """
    return LabelMeJSONGenerator(simplify_tolerance=simplify_tolerance)


if __name__ == "__main__":
    print("🏷️ LabelMe JSON Generator")
    print("將YOLO模型預測結果轉換為LabelMe格式的JSON檔案")
