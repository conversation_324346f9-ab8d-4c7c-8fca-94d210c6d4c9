#!/usr/bin/env python3
"""
統一YOLO推理工具 - 快速開始示例
簡化版接口，快速配置和使用
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from unified_yolo_inference import (
    UnifiedYOLOConfig, ModelConfig, SAHIConfig, 
    ProcessingConfig, AnnotationConfig,
    TaskType, OutputFormat, VisualizationMode,
    create_unified_inference
)


def quick_detection_only():
    """快速檢測模式 - 基於Simple Tool優勢"""
    print("🎯 快速檢測模式")
    
    config = UnifiedYOLOConfig()
    
    # 簡化配置 - 專注檢測
    config.model_config.detection_model_path = "path/to/yolo_detection.pt"
    config.model_config.global_conf = 0.25
    config.model_config.enable_class_filtering = True  # Simple Tool優勢
    config.model_config.enable_detection_merge = True
    
    # 關閉SAHI (快速處理)
    config.sahi_config.enable_sahi = False
    
    # 簡化可視化
    config.processing_config.visualization_mode = VisualizationMode.STANDARD
    config.processing_config.target_classes = [1, 2, 3]  # 只保存特定類別
    
    return config


def advanced_detection_segmentation():
    """進階檢測+分割模式 - 基於Enhanced YOLO優勢"""
    print("🚀 進階檢測+分割模式")
    
    config = UnifiedYOLOConfig()
    
    # Enhanced YOLO雙模型配置
    config.model_config.detection_model_path = "path/to/yolo12_detection.pt"
    config.model_config.segmentation_model_path = "path/to/yolo11_segmentation.pt"
    config.model_config.device = "cuda"
    config.model_config.img_size = 640
    
    # 啟用SAHI大圖處理
    config.sahi_config.enable_sahi = True
    config.sahi_config.slice_height = 512
    config.sahi_config.slice_width = 512
    config.sahi_config.overlap_height_ratio = 0.2
    config.sahi_config.postprocess_type = "GREEDYNMM"
    
    # 完整可視化
    config.processing_config.visualization_mode = VisualizationMode.COMBINED
    config.processing_config.output_format = OutputFormat.BOTH
    
    # LabelMe自動配置
    config.annotation_config.auto_generate_classes = True
    config.annotation_config.enable_gt_comparison = True
    
    return config


def dual_model_consensus():
    """雙模型協同模式 - Simple Tool核心優勢"""
    print("🤝 雙模型協同模式")
    
    config = UnifiedYOLOConfig()
    
    # 主模型和副模型
    config.model_config.detection_model_path = "path/to/primary_model.pt"
    config.model_config.secondary_model_path = "path/to/secondary_model.pt"
    
    # 協同配置 (Simple Tool優勢)
    config.model_config.enable_consensus = True
    config.model_config.consensus_threshold = 0.3
    config.model_config.enable_class_filtering = True
    
    # 類別特定閾值 (Simple Tool優勢)
    config.model_config.class_thresholds = {
        0: 0.2,  # 背景
        1: 0.4,  # 裂縫
        2: 0.5,  # 坑洞
        3: 0.3   # 龜裂
    }
    
    # 檢測合併
    config.model_config.enable_detection_merge = True
    config.model_config.iou_merge_threshold = 0.3
    
    return config


def sahi_large_image():
    """SAHI大圖處理模式 - 完整參數配置"""
    print("🧩 SAHI大圖處理模式")
    
    config = UnifiedYOLOConfig()
    
    # 基礎模型
    config.model_config.detection_model_path = "path/to/model.pt"
    config.model_config.global_conf = 0.1  # 降低閾值用於大圖
    
    # 完整SAHI配置 (23個參數)
    config.sahi_config.enable_sahi = True
    config.sahi_config.slice_height = 256  # 小切片
    config.sahi_config.slice_width = 256
    config.sahi_config.overlap_height_ratio = 0.3  # 高重疊
    config.sahi_config.overlap_width_ratio = 0.3
    config.sahi_config.auto_slice_resolution = True
    config.sahi_config.perform_standard_pred = True
    config.sahi_config.roi_ratio = (0.1, 0.1, 0.9, 0.9)  # 排除邊緣
    config.sahi_config.postprocess_type = "GREEDYNMM"
    config.sahi_config.postprocess_match_threshold = 0.05
    config.sahi_config.exclude_classes_by_id = [0]  # 排除背景
    
    return config


def gt_comparison_mode():
    """GT對比模式 - 新增功能"""
    print("📊 GT對比可視化模式")
    
    config = UnifiedYOLOConfig()
    
    # 基礎模型配置
    config.model_config.detection_model_path = "path/to/model.pt"
    
    # GT對比配置
    config.annotation_config.enable_gt_comparison = True
    config.annotation_config.gt_annotation_format = "auto"
    config.annotation_config.class_names = {
        0: "背景",
        1: "裂縫", 
        2: "坑洞",
        3: "龜裂"
    }
    
    # 對比可視化
    config.processing_config.visualization_mode = VisualizationMode.COMPARISON
    config.processing_config.show_masks = True
    config.processing_config.mask_alpha = 0.4
    
    return config


def run_unified_inference(config: UnifiedYOLOConfig, 
                         input_path: str, 
                         output_path: str,
                         task_type: TaskType = TaskType.BOTH):
    """執行統一推理"""
    
    try:
        print(f"📁 輸入: {input_path}")
        print(f"📁 輸出: {output_path}")
        
        # 創建推理器
        inference = create_unified_inference(config)
        
        # 保存配置
        config_path = Path(output_path) / "config.json"
        Path(output_path).mkdir(parents=True, exist_ok=True)
        config.save_config(str(config_path))
        
        # 執行推理
        if Path(input_path).is_dir():
            results = inference.batch_predict(input_path, output_path, task_type)
        else:
            results = inference.predict_single_image(input_path, output_dir=output_path, task_type=task_type)
        
        # 顯示統計
        stats = inference.get_unified_stats()
        print("\\n📊 處理統計:")
        print(f"  圖像數: {stats['total_images']}")
        print(f"  檢測數: {stats['total_detections']}")
        
        print(f"\\n✅ 處理完成! 結果保存至: {output_path}")
        return results
        
    except Exception as e:
        print(f"❌ 處理失敗: {e}")
        return None


def main():
    """主函數 - 快速開始示例"""
    
    print("🚀 統一YOLO推理工具 - 快速開始")
    print("=" * 50)
    
    # 選擇模式
    print("請選擇運行模式:")
    print("1. 快速檢測模式 (Simple Tool優勢)")
    print("2. 進階檢測+分割模式 (Enhanced YOLO優勢)")
    print("3. 雙模型協同模式 (Simple Tool核心優勢)")  
    print("4. SAHI大圖處理模式 (完整參數)")
    print("5. GT對比模式 (新增功能)")
    
    # 示例：使用模式2
    mode = 2
    print(f"\\n使用模式 {mode}")
    
    # 配置映射
    mode_configs = {
        1: quick_detection_only(),
        2: advanced_detection_segmentation(),
        3: dual_model_consensus(),
        4: sahi_large_image(),
        5: gt_comparison_mode()
    }
    
    config = mode_configs.get(mode)
    if not config:
        print("❌ 無效模式")
        return
    
    # 設置路徑 (請修改為實際路徑)
    input_path = "D:/image/test_input"          # 修改為您的輸入路徑
    output_path = "D:/image/unified_output"     # 修改為您的輸出路徑
    
    # 執行推理
    results = run_unified_inference(
        config=config,
        input_path=input_path, 
        output_path=output_path,
        task_type=TaskType.BOTH
    )
    
    if results:
        print("🎉 快速開始示例完成!")
    else:
        print("❌ 處理失敗，請檢查配置和路徑")


if __name__ == "__main__":
    main()