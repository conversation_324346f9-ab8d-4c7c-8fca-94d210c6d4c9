"""
統一的檢查點管理模組
整合原始checkpoint.py和checkpoint_optimized.py的功能
提供安全、靈活的模型檢查點保存和載入功能
"""

import os
import torch
import glob
import re
import logging
import json
import shutil
from typing import Optional, Dict, Any, Tuple, Union, List
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CheckpointInfo:
    """檢查點信息數據類"""
    filename: str
    epoch: int
    metrics: Dict[str, float]
    timestamp: str
    is_best: bool = False
    file_size: int = 0
    model_name: Optional[str] = None


class CheckpointManager:
    """
    檢查點管理器 - 提供完整的檢查點管理功能
    """
    
    def __init__(self, 
                 checkpoint_dir: Union[str, Path],
                 max_checkpoints: int = 5,
                 auto_resume: bool = True,
                 save_optimizer: bool = True,
                 save_scheduler: bool = True):
        """
        初始化檢查點管理器
        
        Args:
            checkpoint_dir: 檢查點目錄
            max_checkpoints: 保留的最大檢查點數量
            auto_resume: 是否自動恢復最新檢查點
            save_optimizer: 是否保存優化器狀態
            save_scheduler: 是否保存調度器狀態
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.max_checkpoints = max_checkpoints
        self.auto_resume = auto_resume
        self.save_optimizer = save_optimizer
        self.save_scheduler = save_scheduler
        
        # 檢查點元資料
        self.metadata_file = self.checkpoint_dir / "checkpoint_metadata.json"
        self.metadata = self._load_metadata()
        
        logger.info(f"檢查點管理器初始化完成: {self.checkpoint_dir}")
    
    def _load_metadata(self) -> Dict[str, Any]:
        """載入檢查點元資料"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                logger.info(f"載入檢查點元資料: {len(metadata.get('checkpoints', []))} 個檢查點")
                return metadata
            except Exception as e:
                logger.warning(f"無法載入檢查點元資料: {e}")
        
        return {
            "created_at": datetime.now().isoformat(),
            "checkpoints": [],
            "best_checkpoint": None,
            "last_checkpoint": None,
            "version": "1.0"
        }
    
    def _save_metadata(self):
        """保存檢查點元資料"""
        try:
            self.metadata["updated_at"] = datetime.now().isoformat()
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"無法保存檢查點元資料: {e}")
    
    def save_checkpoint(self,
                       model: torch.nn.Module,
                       epoch: int,
                       metrics: Dict[str, float],
                       optimizer: Optional[torch.optim.Optimizer] = None,
                       scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
                       is_best: bool = False,
                       model_name: Optional[str] = None,
                       extra_state: Optional[Dict[str, Any]] = None) -> str:
        """
        保存檢查點
        
        Args:
            model: 模型
            epoch: 當前epoch
            metrics: 評估指標
            optimizer: 優化器
            scheduler: 學習率調度器
            is_best: 是否為最佳模型
            model_name: 模型名稱
            extra_state: 額外狀態信息
            
        Returns:
            檢查點文件路徑
        """
        try:
            # 創建檢查點數據
            checkpoint_data = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'metrics': metrics,
                'timestamp': datetime.now().isoformat(),
                'model_name': model_name,
                'extra_state': extra_state or {}
            }
            
            # 條件性保存優化器和調度器狀態
            if self.save_optimizer and optimizer is not None:
                checkpoint_data['optimizer_state_dict'] = optimizer.state_dict()
            
            if self.save_scheduler and scheduler is not None:
                checkpoint_data['scheduler_state_dict'] = scheduler.state_dict()
            
            # 生成檢查點文件名
            primary_metric = self._get_primary_metric(metrics)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if model_name:
                prefix = f"{model_name}_"
            else:
                prefix = ""
            
            if is_best:
                filename = f"{prefix}best_epoch{epoch:04d}_metric{primary_metric:.4f}_{timestamp}.pth"
            else:
                filename = f"{prefix}checkpoint_epoch{epoch:04d}_metric{primary_metric:.4f}_{timestamp}.pth"
            
            checkpoint_path = self.checkpoint_dir / filename
            
            # 保存檢查點
            torch.save(checkpoint_data, checkpoint_path)
            
            # 更新元資料
            checkpoint_info = CheckpointInfo(
                filename=filename,
                epoch=epoch,
                metrics=metrics,
                timestamp=checkpoint_data['timestamp'],
                is_best=is_best,
                file_size=checkpoint_path.stat().st_size,
                model_name=model_name
            )
            
            self._update_metadata(checkpoint_info)
            
            # 清理舊檢查點
            self._cleanup_old_checkpoints()
            
            # 保存元資料
            self._save_metadata()
            
            logger.info(f"檢查點已保存: {checkpoint_path} (大小: {checkpoint_info.file_size / 1024 / 1024:.2f} MB)")
            return str(checkpoint_path)
            
        except Exception as e:
            logger.error(f"保存檢查點時發生錯誤: {e}")
            raise
    
    def _get_primary_metric(self, metrics: Dict[str, float]) -> float:
        """獲取主要指標值"""
        # 優先級：F1 > accuracy > val_loss (取負值) > loss (取負值) > 第一個指標
        for metric_name in ['F1', 'f1', 'accuracy', 'acc']:
            if metric_name in metrics:
                return metrics[metric_name]
        
        for metric_name in ['val_loss', 'validation_loss']:
            if metric_name in metrics:
                return -metrics[metric_name]  # 損失值取負值，因為越小越好
        
        if 'loss' in metrics:
            return -metrics['loss']
        
        # 如果沒有找到標準指標，返回第一個指標值
        if metrics:
            return list(metrics.values())[0]
        
        return 0.0
    
    def _update_metadata(self, checkpoint_info: CheckpointInfo):
        """更新檢查點元資料"""
        # 添加到檢查點列表
        checkpoint_dict = {
            "filename": checkpoint_info.filename,
            "epoch": checkpoint_info.epoch,
            "metrics": checkpoint_info.metrics,
            "timestamp": checkpoint_info.timestamp,
            "is_best": checkpoint_info.is_best,
            "file_size": checkpoint_info.file_size,
            "model_name": checkpoint_info.model_name
        }
        
        self.metadata["checkpoints"].append(checkpoint_dict)
        self.metadata["last_checkpoint"] = checkpoint_info.filename
        
        if checkpoint_info.is_best:
            self.metadata["best_checkpoint"] = checkpoint_info.filename
    
    def load_checkpoint(self,
                       model: torch.nn.Module,
                       optimizer: Optional[torch.optim.Optimizer] = None,
                       scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
                       checkpoint_path: Optional[str] = None,
                       load_best: bool = True,
                       device: Optional[torch.device] = None,
                       strict: bool = True) -> Tuple[int, Dict[str, float], Dict[str, Any]]:
        """
        載入檢查點
        
        Args:
            model: 模型
            optimizer: 優化器（可選）
            scheduler: 學習率調度器（可選）
            checkpoint_path: 指定檢查點路徑（可選）
            load_best: 是否載入最佳檢查點
            device: 目標設備
            strict: 是否嚴格匹配模型狀態字典
            
        Returns:
            (epoch, metrics, extra_state)
        """
        try:
            # 確定要載入的檢查點
            if checkpoint_path is None:
                checkpoint_path = self._find_checkpoint_to_load(load_best)
            
            if checkpoint_path is None or not Path(checkpoint_path).exists():
                logger.warning("未找到可用的檢查點，從頭開始訓練")
                return 0, {}, {}
            
            # 自動檢測設備
            if device is None:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
            # 載入檢查點
            logger.info(f"載入檢查點: {checkpoint_path}")
            checkpoint_data = torch.load(checkpoint_path, map_location=device)
            
            # 驗證檢查點數據
            self._validate_checkpoint(checkpoint_data)
            
            # 載入模型狀態
            try:
                model.load_state_dict(checkpoint_data['model_state_dict'], strict=strict)
            except Exception as e:
                if strict:
                    logger.error(f"嚴格模式下無法載入模型狀態: {e}")
                    raise
                else:
                    logger.warning(f"非嚴格模式載入模型狀態，某些層可能不匹配: {e}")
                    model.load_state_dict(checkpoint_data['model_state_dict'], strict=False)
            
            # 載入優化器狀態
            if optimizer and 'optimizer_state_dict' in checkpoint_data:
                try:
                    optimizer.load_state_dict(checkpoint_data['optimizer_state_dict'])
                    logger.info("成功載入優化器狀態")
                except Exception as e:
                    logger.warning(f"載入優化器狀態失敗: {e}")
            
            # 載入調度器狀態
            if scheduler and checkpoint_data.get('scheduler_state_dict'):
                try:
                    scheduler.load_state_dict(checkpoint_data['scheduler_state_dict'])
                    logger.info("成功載入調度器狀態")
                except Exception as e:
                    logger.warning(f"載入調度器狀態失敗: {e}")
            
            epoch = checkpoint_data.get('epoch', 0)
            metrics = checkpoint_data.get('metrics', {})
            extra_state = checkpoint_data.get('extra_state', {})
            
            logger.info(f"成功載入檢查點 - Epoch: {epoch}, Metrics: {metrics}")
            return epoch, metrics, extra_state
            
        except Exception as e:
            logger.error(f"載入檢查點時發生錯誤: {e}")
            raise
    
    def _find_checkpoint_to_load(self, load_best: bool = True) -> Optional[str]:
        """尋找要載入的檢查點"""
        if load_best and self.metadata.get("best_checkpoint"):
            best_path = self.checkpoint_dir / self.metadata["best_checkpoint"]
            if best_path.exists():
                return str(best_path)
        
        if self.metadata.get("last_checkpoint"):
            last_path = self.checkpoint_dir / self.metadata["last_checkpoint"]
            if last_path.exists():
                return str(last_path)
        
        # 如果元資料中沒有，嘗試在文件系統中尋找
        return self.find_latest_checkpoint()
    
    def find_latest_checkpoint(self, model_name: Optional[str] = None) -> Optional[str]:
        """
        尋找最新的檢查點文件
        
        Args:
            model_name: 模型名稱過濾器
            
        Returns:
            最新檢查點的路徑
        """
        try:
            pattern = "*.pth"
            if model_name:
                pattern = f"{model_name}_*.pth"
            
            checkpoint_files = list(self.checkpoint_dir.glob(pattern))
            
            if not checkpoint_files:
                return None
            
            # 按修改時間排序，最新的在最後
            latest_checkpoint = max(checkpoint_files, key=lambda p: p.stat().st_mtime)
            return str(latest_checkpoint)
            
        except Exception as e:
            logger.error(f"尋找檢查點時發生錯誤: {e}")
            return None
    
    def _validate_checkpoint(self, checkpoint_data: Dict[str, Any]):
        """驗證檢查點數據完整性"""
        required_keys = ['model_state_dict']
        for key in required_keys:
            if key not in checkpoint_data:
                raise ValueError(f"檢查點缺少必要字段: {key}")
    
    def _cleanup_old_checkpoints(self):
        """清理舊的檢查點文件"""
        try:
            if len(self.metadata["checkpoints"]) <= self.max_checkpoints:
                return
            
            # 按時間排序，保留最新的
            sorted_checkpoints = sorted(
                self.metadata["checkpoints"],
                key=lambda x: x["timestamp"]
            )
            
            # 計算需要刪除的檢查點
            to_delete = sorted_checkpoints[:-self.max_checkpoints]
            
            for checkpoint_info in to_delete:
                # 保護最佳檢查點
                if checkpoint_info.get("is_best", False):
                    continue
                
                file_path = self.checkpoint_dir / checkpoint_info["filename"]
                if file_path.exists():
                    file_path.unlink()
                    logger.info(f"已刪除舊檢查點: {file_path}")
                
                self.metadata["checkpoints"].remove(checkpoint_info)
                
        except Exception as e:
            logger.error(f"清理檢查點時發生錯誤: {e}")
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """列出所有可用的檢查點"""
        return self.metadata.get("checkpoints", [])
    
    def get_best_checkpoint_path(self) -> Optional[str]:
        """獲取最佳檢查點路徑"""
        best_filename = self.metadata.get("best_checkpoint")
        if best_filename:
            return str(self.checkpoint_dir / best_filename)
        return None
    
    def get_last_checkpoint_path(self) -> Optional[str]:
        """獲取最後檢查點路徑"""
        last_filename = self.metadata.get("last_checkpoint")
        if last_filename:
            return str(self.checkpoint_dir / last_filename)
        return None
    
    def remove_checkpoint(self, filename: str) -> bool:
        """
        移除指定的檢查點
        
        Args:
            filename: 檢查點文件名
            
        Returns:
            是否成功移除
        """
        try:
            file_path = self.checkpoint_dir / filename
            if file_path.exists():
                file_path.unlink()
                
                # 從元資料中移除
                self.metadata["checkpoints"] = [
                    cp for cp in self.metadata["checkpoints"] 
                    if cp["filename"] != filename
                ]
                
                # 更新最佳和最後檢查點引用
                if self.metadata.get("best_checkpoint") == filename:
                    self.metadata["best_checkpoint"] = None
                if self.metadata.get("last_checkpoint") == filename:
                    self.metadata["last_checkpoint"] = None
                
                self._save_metadata()
                logger.info(f"已移除檢查點: {filename}")
                return True
            else:
                logger.warning(f"檢查點文件不存在: {filename}")
                return False
                
        except Exception as e:
            logger.error(f"移除檢查點時發生錯誤: {e}")
            return False
    
    def export_checkpoint(self, filename: str, export_path: str) -> bool:
        """
        導出檢查點到指定路徑
        
        Args:
            filename: 檢查點文件名
            export_path: 導出路徑
            
        Returns:
            是否成功導出
        """
        try:
            source_path = self.checkpoint_dir / filename
            if not source_path.exists():
                logger.error(f"檢查點文件不存在: {filename}")
                return False
            
            # 確保導出目錄存在
            export_dir = Path(export_path).parent
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # 複製文件
            shutil.copy2(source_path, export_path)
            logger.info(f"檢查點已導出: {source_path} -> {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"導出檢查點時發生錯誤: {e}")
            return False


# 向後兼容性函數
def find_latest_checkpoint(path_total: str, modelname: str) -> Optional[str]:
    """
    向後兼容的函數 - 尋找最新檢查點目錄
    
    Args:
        path_total: 檢查點目錄
        modelname: 模型名稱
        
    Returns:
        最新檢查點目錄路徑
    """
    try:
        pattern = os.path.join(path_total, f"{modelname}_*")
        matching_dirs = glob.glob(pattern)
        
        if not matching_dirs:
            logger.warning(f"未找到匹配的檢查點目錄: {pattern}")
            return None
        
        latest_dir = max(matching_dirs, key=os.path.getctime)
        logger.info(f"找到最新檢查點目錄: {latest_dir}")
        return latest_dir
        
    except Exception as e:
        logger.error(f"尋找檢查點目錄時發生錯誤: {e}")
        return None


def load_checkpoint(model: torch.nn.Module,
                   path_weight: str,
                   best: bool = True,
                   device: Optional[torch.device] = None,
                   strict: bool = True) -> Tuple[torch.nn.Module, int, float]:
    """
    向後兼容的函數 - 載入檢查點
    
    Args:
        model: 模型
        path_weight: 權重目錄路徑
        best: 是否載入最佳模型
        device: 目標設備
        strict: 是否嚴格匹配狀態字典
        
    Returns:
        (model, start_epoch, best_metric)
    """
    try:
        # 自動檢測設備
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 根據參數決定加載哪種檢查點
        if best:
            pattern = os.path.join(path_weight, '*best*.pth')
        else:
            pattern = os.path.join(path_weight, '*last*.pth')
        
        checkpoints = glob.glob(pattern)
        
        # 如果沒有找到特定類型，嘗試找任何.pth文件
        if not checkpoints:
            pattern = os.path.join(path_weight, '*.pth')
            checkpoints = glob.glob(pattern)
        
        if not checkpoints:
            logger.warning(f"未找到檢查點文件在: {path_weight}")
            return model, 0, 0.0
        
        # 選擇最新的檢查點
        latest_checkpoint = max(checkpoints, key=os.path.getctime)
        
        # 載入檢查點
        logger.info(f"載入檢查點: {latest_checkpoint}")
        checkpoint_data = torch.load(latest_checkpoint, map_location=device)
        
        # 處理不同的檢查點格式
        if isinstance(checkpoint_data, dict):
            if 'model_state_dict' in checkpoint_data:
                # 新格式
                model.load_state_dict(checkpoint_data['model_state_dict'], strict=strict)
                start_epoch = checkpoint_data.get('epoch', 0)
                
                # 從metrics中獲取最佳指標
                metrics = checkpoint_data.get('metrics', {})
                best_metric = metrics.get('F1', metrics.get('f1', metrics.get('accuracy', 0.0)))
                
                # 如果沒有找到標準指標，嘗試從loss中計算（取負值）
                if best_metric == 0.0:
                    best_metric = -metrics.get('val_loss', -metrics.get('loss', 0.0))
                    
            else:
                # 舊格式，直接是state_dict
                model.load_state_dict(checkpoint_data, strict=strict)
                start_epoch = 0
                best_metric = 0.0
        else:
            # 更舊的格式
            model.load_state_dict(checkpoint_data, strict=strict)
            start_epoch = 0
            best_metric = 0.0
        
        # 從檔名解析資訊（作為備用）
        filename = os.path.basename(latest_checkpoint)
        
        # 嘗試從文件名解析epoch
        if start_epoch == 0:
            epoch_match = re.search(r'epoch(\d+)', filename)
            if epoch_match:
                start_epoch = int(epoch_match.group(1))
        
        # 嘗試從文件名解析metric
        if best_metric == 0.0:
            metric_matches = re.findall(r'metric(\d+\.\d+)', filename)
            if metric_matches:
                best_metric = float(metric_matches[-1])  # 取最後一個匹配的metric
            else:
                # 嘗試其他常見模式
                f1_match = re.search(r'f1_(\d+\.\d+)', filename)
                if f1_match:
                    best_metric = float(f1_match.group(1))
        
        logger.info(f"檢查點載入成功 - Epoch: {start_epoch}, Metric: {best_metric:.4f}")
        return model, start_epoch, best_metric
        
    except Exception as e:
        logger.error(f"載入檢查點時發生錯誤: {e}")
        return model, 0, 0.0


# 便捷函數
def create_checkpoint_manager(checkpoint_dir: str, **kwargs) -> CheckpointManager:
    """創建檢查點管理器的便捷函數"""
    return CheckpointManager(checkpoint_dir, **kwargs)


def quick_save(model: torch.nn.Module,
              checkpoint_dir: str,
              epoch: int,
              metrics: Dict[str, float],
              **kwargs) -> str:
    """快速保存檢查點的便捷函數"""
    manager = CheckpointManager(checkpoint_dir)
    return manager.save_checkpoint(model, epoch, metrics, **kwargs)


def quick_load(model: torch.nn.Module,
              checkpoint_dir: str,
              **kwargs) -> Tuple[int, Dict[str, float], Dict[str, Any]]:
    """快速載入檢查點的便捷函數"""
    manager = CheckpointManager(checkpoint_dir)
    return manager.load_checkpoint(model, **kwargs)


# 使用示例
if __name__ == "__main__":
    import torch.nn as nn
    
    # 創建一個簡單的模型用於測試
    model = nn.Linear(10, 1)
    optimizer = torch.optim.Adam(model.parameters())
    scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=10)
    
    # 創建檢查點管理器
    checkpoint_manager = CheckpointManager(
        checkpoint_dir="./test_checkpoints",
        max_checkpoints=3
    )
    
    # 保存檢查點
    checkpoint_path = checkpoint_manager.save_checkpoint(
        model=model,
        epoch=10,
        metrics={'val_loss': 0.5, 'F1': 0.8},
        optimizer=optimizer,
        scheduler=scheduler,
        is_best=True,
        model_name="test_model"
    )
    
    print(f"檢查點已保存: {checkpoint_path}")
    
    # 載入檢查點
    new_model = nn.Linear(10, 1)
    epoch, metrics, extra_state = checkpoint_manager.load_checkpoint(
        model=new_model,
        optimizer=optimizer,
        scheduler=scheduler,
        load_best=True
    )
    
    print(f"檢查點已載入 - Epoch: {epoch}, Metrics: {metrics}")
    
    # 列出所有檢查點
    checkpoints = checkpoint_manager.list_checkpoints()
    print(f"所有檢查點: {checkpoints}")