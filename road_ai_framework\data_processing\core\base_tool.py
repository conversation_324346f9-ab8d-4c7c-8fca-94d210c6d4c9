"""
統一工具基類

提供所有資料前處理工具的統一基礎架構
"""

import logging
import os
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional, Union
from datetime import datetime

from .exceptions import ValidationError, ProcessingError


class BaseTool(ABC):
    """所有工具的統一基類"""
    
    def __init__(self, 
                 input_dir: Optional[Union[str, Path]] = None,
                 output_dir: Optional[Union[str, Path]] = None,
                 logger: Optional[logging.Logger] = None,
                 config: Optional[Dict[str, Any]] = None,
                 max_workers: Optional[int] = None):
        """
        基礎工具初始化
        
        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            logger: 日誌記錄器
            config: 配置字典
            max_workers: 最大工作線程數
        """
        self.input_dir = Path(input_dir) if input_dir else None
        self.output_dir = Path(output_dir) if output_dir else None
        self.logger = logger or self._create_default_logger()
        self.config = config or {}
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) * 2)
        
        # 統計信息
        self.stats = {
            'start_time': None,
            'end_time': None,
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'errors': []
        }
        
        # 執行狀態
        self.is_running = False
        self.should_stop = False
    
    def _create_default_logger(self) -> logging.Logger:
        """創建默認日誌記錄器"""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def validate_inputs(self) -> bool:
        """
        驗證輸入參數
        
        Returns:
            bool: 驗證是否通過
        """
        pass
    
    def run(self, **kwargs) -> Dict[str, Any]:
        """
        執行主要邏輯
        
        Returns:
            Dict[str, Any]: 執行結果統計
        """
        if not self.validate_inputs():
            raise ValidationError("輸入驗證失敗")
        
        self.stats['start_time'] = datetime.now()
        self.is_running = True
        
        try:
            self.logger.info(f"開始執行 {self.__class__.__name__}")
            result = self._execute_main_logic(**kwargs)
            self._calculate_final_stats()
            self.logger.info(f"執行完成 {self.__class__.__name__}")
            return result
        except Exception as e:
            self.logger.error(f"執行過程中發生錯誤: {e}")
            self.stats['errors'].append(str(e))
            raise ProcessingError(f"執行失敗: {e}") from e
        finally:
            self.stats['end_time'] = datetime.now()
            self.is_running = False
    
    def _execute_main_logic(self, **kwargs) -> Dict[str, Any]:
        """
        執行主要邏輯的默認實現
        子類可以重寫此方法實現自定義邏輯
        """
        return self.stats
    
    def _calculate_final_stats(self):
        """計算最終統計信息"""
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            self.stats['duration_seconds'] = duration.total_seconds()
            
            # 計算處理速度
            if self.stats['duration_seconds'] > 0:
                self.stats['files_per_second'] = (
                    self.stats['processed_files'] / self.stats['duration_seconds']
                )
            else:
                self.stats['files_per_second'] = 0
                
            # 計算成功率
            if self.stats['total_files'] > 0:
                self.stats['success_rate'] = (
                    self.stats['processed_files'] / self.stats['total_files']
                )
            else:
                self.stats['success_rate'] = 1.0
    
    def stop(self):
        """停止執行"""
        self.should_stop = True
        self.logger.info(f"收到停止信號: {self.__class__.__name__}")
    
    def get_progress(self) -> float:
        """
        獲取處理進度
        
        Returns:
            float: 進度百分比 (0.0-1.0)
        """
        if self.stats['total_files'] == 0:
            return 0.0
        completed = self.stats['processed_files'] + self.stats['failed_files']
        return min(completed / self.stats['total_files'], 1.0)
    
    def reset_stats(self):
        """重置統計信息"""
        self.stats = {
            'start_time': None,
            'end_time': None,
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'errors': []
        }
        self.should_stop = False
    
    def get_summary(self) -> Dict[str, Any]:
        """
        獲取執行摘要
        
        Returns:
            Dict[str, Any]: 執行摘要信息
        """
        summary = {
            'tool_name': self.__class__.__name__,
            'status': 'running' if self.is_running else 'completed',
            'total_files': self.stats['total_files'],
            'processed_files': self.stats['processed_files'],
            'failed_files': self.stats['failed_files'],
            'skipped_files': self.stats['skipped_files']
        }
        
        if self.stats.get('duration_seconds'):
            summary['duration_seconds'] = self.stats['duration_seconds']
            summary['files_per_second'] = self.stats.get('files_per_second', 0)
            summary['success_rate'] = self.stats.get('success_rate', 0)
        
        if self.stats['errors']:
            summary['error_count'] = len(self.stats['errors'])
            summary['recent_errors'] = self.stats['errors'][-5:]  # 最近5個錯誤
            
        return summary
    
    def log_processing_start(self, item_path: Union[str, Path], operation: str = "處理"):
        """記錄處理開始"""
        self.logger.debug(f"開始{operation}: {item_path}")
    
    def log_processing_success(self, item_path: Union[str, Path], operation: str = "處理"):
        """記錄處理成功"""
        self.stats['processed_files'] += 1
        self.logger.debug(f"{operation}成功: {item_path}")
    
    def log_processing_error(self, item_path: Union[str, Path], error: Exception, operation: str = "處理"):
        """記錄處理錯誤"""
        self.stats['failed_files'] += 1
        error_msg = f"{operation}失敗: {item_path}, 錯誤: {error}"
        self.stats['errors'].append(error_msg)
        self.logger.error(error_msg)
    
    def log_processing_skip(self, item_path: Union[str, Path], reason: str = "已存在"):
        """記錄跳過處理"""
        self.stats['skipped_files'] += 1
        self.logger.debug(f"跳過處理: {item_path}, 原因: {reason}")


class ProgressCallback:
    """進度回調接口"""
    
    def __init__(self, callback_func=None):
        self.callback_func = callback_func
    
    def update(self, current: int, total: int, message: str = ""):
        """更新進度"""
        if self.callback_func:
            progress = current / total if total > 0 else 0
            self.callback_func(progress, message)
    
    def set_total(self, total: int):
        """設置總數"""
        pass
    
    def increment(self, message: str = ""):
        """增加計數"""
        pass