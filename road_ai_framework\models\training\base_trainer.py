#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基礎訓練器類

提供所有訓練器的基類和統一接口
"""

import torch
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
import time
import json
import os
from pathlib import Path


class BaseTrainer(ABC):
    """
    基礎訓練器抽象類
    
    所有訓練器的基類，定義統一的接口規範
    """
    
    def __init__(self, device: str = "auto"):
        """
        初始化基礎訓練器
        
        參數:
            device: 計算設備 ("cuda", "cpu", "auto")
        """
        self.device = self._setup_device(device)
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 訓練狀態
        self.current_epoch = 0
        self.best_metric = 0.0
        self.training_history = []
        self.start_time = None
        self.end_time = None
    
    def _setup_device(self, device: str) -> torch.device:
        """
        設置計算設備
        
        參數:
            device: 設備字符串
            
        返回:
            torch.device對象
        """
        if device == "auto":
            if torch.cuda.is_available():
                device = "cuda"
            else:
                device = "cpu"
        
        return torch.device(device)
    
    @abstractmethod
    def train(self, *args, **kwargs) -> Dict[str, Any]:
        """
        訓練的抽象方法
        
        返回:
            訓練結果字典
        """
        pass
    
    def validate(self, *args, **kwargs) -> Dict[str, Any]:
        """
        驗證方法（可選實現）
        
        返回:
            驗證結果字典
        """
        self.logger.warning("驗證方法未實現")
        return {}
    
    def save_checkpoint(self, 
                       filepath: str, 
                       epoch: int = None,
                       metrics: Dict = None,
                       **kwargs):
        """
        保存檢查點
        
        參數:
            filepath: 檢查點檔案路徑
            epoch: 當前epoch
            metrics: 指標字典
            **kwargs: 其他要保存的數據
        """
        if self.model is None:
            self.logger.warning("模型未初始化，無法保存檢查點")
            return
        
        checkpoint = {
            'epoch': epoch or self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'metrics': metrics or {},
            'training_history': self.training_history,
            'device': str(self.device)
        }
        
        # 保存優化器狀態
        if self.optimizer is not None:
            checkpoint['optimizer_state_dict'] = self.optimizer.state_dict()
        
        # 保存調度器狀態
        if self.scheduler is not None:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # 添加額外數據
        checkpoint.update(kwargs)
        
        try:
            torch.save(checkpoint, filepath)
            self.logger.info(f"檢查點已保存: {filepath}")
        except Exception as e:
            self.logger.error(f"保存檢查點失敗: {e}")
    
    def load_checkpoint(self, 
                       filepath: str,
                       load_optimizer: bool = True,
                       load_scheduler: bool = True) -> Dict:
        """
        載入檢查點
        
        參數:
            filepath: 檢查點檔案路徑
            load_optimizer: 是否載入優化器狀態
            load_scheduler: 是否載入調度器狀態
            
        返回:
            載入的檢查點數據
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"檢查點檔案不存在: {filepath}")
        
        try:
            checkpoint = torch.load(filepath, map_location=self.device)
            
            # 載入模型狀態
            if self.model is not None and 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.logger.info("已載入模型狀態")
            
            # 載入優化器狀態
            if (load_optimizer and self.optimizer is not None and 
                'optimizer_state_dict' in checkpoint):
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                self.logger.info("已載入優化器狀態")
            
            # 載入調度器狀態
            if (load_scheduler and self.scheduler is not None and 
                'scheduler_state_dict' in checkpoint):
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                self.logger.info("已載入調度器狀態")
            
            # 載入訓練狀態
            self.current_epoch = checkpoint.get('epoch', 0)
            self.training_history = checkpoint.get('training_history', [])
            
            self.logger.info(f"檢查點載入完成: epoch {self.current_epoch}")
            
            return checkpoint
            
        except Exception as e:
            self.logger.error(f"載入檢查點失敗: {e}")
            raise
    
    def log_metrics(self, 
                   metrics: Dict[str, float],
                   step: int = None,
                   prefix: str = ""):
        """
        記錄指標
        
        參數:
            metrics: 指標字典
            step: 步數（通常是epoch）
            prefix: 指標名稱前綴
        """
        step = step or self.current_epoch
        
        # 添加前綴
        if prefix:
            prefixed_metrics = {f"{prefix}_{k}": v for k, v in metrics.items()}
        else:
            prefixed_metrics = metrics
        
        # 記錄到歷史
        history_entry = {
            'step': step,
            'timestamp': time.time(),
            **prefixed_metrics
        }
        self.training_history.append(history_entry)
        
        # 記錄到日誌
        metric_str = ", ".join([f"{k}: {v:.4f}" if isinstance(v, float) else f"{k}: {v}" 
                               for k, v in prefixed_metrics.items()])
        self.logger.info(f"Step {step} - {metric_str}")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """
        獲取訓練統計信息
        
        返回:
            訓練統計字典
        """
        stats = {
            'current_epoch': self.current_epoch,
            'best_metric': self.best_metric,
            'total_training_time': None,
            'device': str(self.device),
            'history_length': len(self.training_history)
        }
        
        # 計算總訓練時間
        if self.start_time and self.end_time:
            stats['total_training_time'] = self.end_time - self.start_time
        elif self.start_time:
            stats['current_training_time'] = time.time() - self.start_time
        
        # 獲取最新指標
        if self.training_history:
            latest_metrics = self.training_history[-1]
            stats['latest_metrics'] = {k: v for k, v in latest_metrics.items() 
                                     if k not in ['step', 'timestamp']}
        
        return stats
    
    def save_training_history(self, filepath: str):
        """
        保存訓練歷史
        
        參數:
            filepath: 保存路徑
        """
        try:
            with open(filepath, 'w') as f:
                json.dump({
                    'training_history': self.training_history,
                    'stats': self.get_training_stats()
                }, f, indent=2)
            
            self.logger.info(f"訓練歷史已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存訓練歷史失敗: {e}")
    
    def load_training_history(self, filepath: str):
        """
        載入訓練歷史
        
        參數:
            filepath: 載入路徑
        """
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.training_history = data.get('training_history', [])
            
            # 恢復訓練狀態
            if self.training_history:
                latest = self.training_history[-1]
                self.current_epoch = latest.get('step', 0)
            
            self.logger.info(f"訓練歷史已載入: {filepath}")
            
        except Exception as e:
            self.logger.error(f"載入訓練歷史失敗: {e}")
    
    def early_stopping_check(self, 
                            current_metric: float,
                            patience: int,
                            min_delta: float = 0.0,
                            mode: str = 'max') -> bool:
        """
        早停檢查
        
        參數:
            current_metric: 當前指標值
            patience: 耐心值（多少個epoch沒有改善）
            min_delta: 最小改善幅度
            mode: 模式 ('max' 或 'min')
            
        返回:
            是否應該早停
        """
        if mode == 'max':
            improved = current_metric > self.best_metric + min_delta
        else:
            improved = current_metric < self.best_metric - min_delta
        
        if improved:
            self.best_metric = current_metric
            self._no_improve_count = 0
            return False
        else:
            self._no_improve_count = getattr(self, '_no_improve_count', 0) + 1
            return self._no_improve_count >= patience
    
    def setup_directories(self, base_dir: str, subdirs: List[str] = None):
        """
        設置目錄結構
        
        參數:
            base_dir: 基礎目錄
            subdirs: 子目錄列表
        """
        base_path = Path(base_dir)
        base_path.mkdir(parents=True, exist_ok=True)
        
        default_subdirs = ['weights', 'logs', 'images', 'configs']
        subdirs = subdirs or default_subdirs
        
        for subdir in subdirs:
            (base_path / subdir).mkdir(exist_ok=True)
        
        self.logger.info(f"目錄結構已建立: {base_path}")
    
    def start_training(self):
        """開始訓練計時"""
        self.start_time = time.time()
        self.logger.info("開始訓練")
    
    def end_training(self):
        """結束訓練計時"""
        self.end_time = time.time()
        if self.start_time:
            total_time = self.end_time - self.start_time
            self.logger.info(f"訓練結束，總時間: {total_time:.2f}秒")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        獲取模型信息
        
        返回:
            模型信息字典
        """
        info = {
            "trainer_class": self.__class__.__name__,
            "device": str(self.device),
            "model_loaded": self.model is not None
        }
        
        if self.model is not None:
            try:
                # 計算模型參數數量
                total_params = sum(p.numel() for p in self.model.parameters())
                trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
                
                info.update({
                    "total_parameters": total_params,
                    "trainable_parameters": trainable_params,
                    "model_size_mb": total_params * 4 / (1024 * 1024)  # 假設float32
                })
            except Exception as e:
                self.logger.warning(f"無法獲取模型參數信息: {e}")
        
        return info
    
    def memory_usage(self) -> Dict[str, float]:
        """
        獲取記憶體使用情況
        
        返回:
            記憶體使用信息字典
        """
        memory_info = {}
        
        if torch.cuda.is_available() and self.device.type == "cuda":
            memory_info.update({
                "gpu_allocated_mb": torch.cuda.memory_allocated(self.device) / (1024 * 1024),
                "gpu_cached_mb": torch.cuda.memory_reserved(self.device) / (1024 * 1024),
                "gpu_max_allocated_mb": torch.cuda.max_memory_allocated(self.device) / (1024 * 1024)
            })
        
        # 可以添加CPU記憶體使用情況
        try:
            import psutil
            process = psutil.Process()
            memory_info["cpu_memory_mb"] = process.memory_info().rss / (1024 * 1024)
        except ImportError:
            pass
        
        return memory_info


class TrainerFactory:
    """
    訓練器工廠類
    
    用於創建不同類型的訓練器
    """
    
    _trainer_registry = {}
    
    @classmethod
    def register(cls, name: str, trainer_class):
        """
        註冊訓練器類
        
        參數:
            name: 訓練器名稱
            trainer_class: 訓練器類
        """
        cls._trainer_registry[name] = trainer_class
    
    @classmethod
    def create_trainer(cls, name: str, **kwargs) -> BaseTrainer:
        """
        創建訓練器實例
        
        參數:
            name: 訓練器名稱
            **kwargs: 初始化參數
            
        返回:
            訓練器實例
        """
        if name not in cls._trainer_registry:
            raise ValueError(f"未知的訓練器類型: {name}")
        
        trainer_class = cls._trainer_registry[name]
        return trainer_class(**kwargs)
    
    @classmethod
    def list_available(cls) -> List[str]:
        """
        列出可用的訓練器類型
        
        返回:
            訓練器名稱列表
        """
        return list(cls._trainer_registry.keys())


# 裝飾器：用於自動註冊訓練器
def register_trainer(name: str):
    """
    訓練器註冊裝飾器
    
    參數:
        name: 訓練器名稱
    """
    def decorator(cls):
        TrainerFactory.register(name, cls)
        return cls
    return decorator