# 模型架構與資料集讀取
# from model_create.encoder.mobilev3seg.mobilenetv3_seg import MobileNetV3Seg
# from model_create.UNet import UNet
# from model_create.Models.CSP_IFormer_final_SegMode import iformer_small
# from model_create.decoder.BiFPN_2_CSPIformer import B<PERSON><PERSON><PERSON><PERSON>oder
# from model_create.head.heads import SegmentationHead
# from model_create.util.encoder_decoder_cat import Encoder_decoder

from model_create.util.Dataset_read import YOLODataset, convert_labelme_to_yolo
from model_create.util.metrics import get_metrics, runningScore
from model_create.util.train_function import train, validate, test
from model_create.util.show_img import show_img
from model_create.util.losses import StagedRoadDamageLoss
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint

# import
import logging
import os
import yaml
import time
import wandb
from IPython.display import clear_output
import datetime
import torch
import numpy as np
import random
import matplotlib.pyplot as plt
from ultralytics import YOLO
import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2

# pytorch

from torch.optim import AdamW
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts


"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False
# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Main')

def setup_seed(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.enabled = False


# set random seed
seed = 3407
setup_seed(seed)

# 設定參數
use_wandb = False
use_checkpoint = False  # 新增: 是否使用檢查點繼續訓練
modelname = 'yolov12x'
image_size = 640
learning_rate = 1e-3
train_epochs = 500
batch_size = 4
n_classes = 6
num_workers = 0
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
# class_names = {0: '背景', 1: '伸縮縫', 2: '路面接縫', 3: '裂縫', 4: '坑洞', 5: '補綻'}
class_mapping = {'bird': 0, 'block': 1,
                 'retaining seat': 2, 'treefish': 3, 'tpe': 4}
class_names = {0: '背景', 1: '鳥', 2: '方塊', 3: '擋土座', 4: '樹魚', 5: '台北蓋水特色框蓋'}


# dataset from
data_dir = r'D:\5_Hole_cover\train_20250522_151822\3_divided_dataset\object_detect'
train_dir = os.path.join(data_dir, 'train')  # 訓練資料目錄
val_dir = os.path.join(data_dir, 'val')  # 驗證資料目錄
test_dir = os.path.join(data_dir, 'test')  # 測試資料目錄

# 取得當前時間
current_time = datetime.datetime.now()
# 格式化時間戳
timestamp = current_time.strftime("%Y%m%d_%H%M%S")
# running data savepath
path_total = r'.\run_data'
if not os.path.isdir(path_total):
    os.mkdir(path_total)

# 檢查是否使用檢查點
latest_checkpoint_dir = None
if use_checkpoint:
    latest_checkpoint_dir = find_latest_checkpoint(path_total, modelname)

    if latest_checkpoint_dir:
        logger.info(f"找到最新檢查點目錄: {latest_checkpoint_dir}")
        runpath = os.path.basename(latest_checkpoint_dir)
        # 使用現有目錄
        path_img = os.path.join(latest_checkpoint_dir, 'img')
        path_weight = os.path.join(latest_checkpoint_dir, 'weight')
    else:
        logger.info(f"未找到檢查點，將創建新目錄")
        # 使用新時間戳創建新目錄
        runpath = f'/{modelname}_{timestamp}'
        if not os.path.isdir(path_total+runpath):
            os.mkdir(path_total+runpath)
        path_img = path_total+runpath+r'\img'
        if not os.path.isdir(path_img):
            os.mkdir(path_img)
        path_weight = path_total+runpath+r'\weight'
        if not os.path.isdir(path_weight):
            os.mkdir(path_weight)
else:
    runpath = f'/{modelname}_{timestamp}'
    run_dir = path_total + runpath
    os.makedirs(run_dir, exist_ok=True)
    path_img = os.path.join(run_dir, 'img')
    os.makedirs(path_img, exist_ok=True)
    path_weight = os.path.join(run_dir, 'weight')
    os.makedirs(path_weight, exist_ok=True)


logger.info(f"使用設備: {device}")
logger.info(f"類別數量: {n_classes}")
logger.info(f"批次大小: {batch_size}")
logger.info(f"圖像大小: {image_size}x{image_size}")
logger.info(f"圖像存放位置:{path_img}")
logger.info(f"權重存放位置:{path_weight}")

# wandb
if use_wandb:
    wandb.init(project="road_crack", name="mobilenetv3seg")
    wandb.config.update({
        "learning_rate": learning_rate,
        "epochs": train_epochs,
        "batch_size": batch_size,
        "image_size": image_size,
        "n_classes": n_classes,
        "optimizer": "AdamW",
        "loss_function": "StagedRoadDamageLoss"
    })


# Step 1: 確保資料已經是YOLO格式 (如果不是，進行轉換)
def prepare_data():
    # 檢查 train_dir 是否已經有 labels 和 images 子目錄
    train_labels_dir = os.path.join(train_dir, 'labels')
    train_images_dir = os.path.join(train_dir, 'images')

    if not os.path.exists(train_labels_dir) or not os.path.exists(train_images_dir):
        logger.info("需要進行 labelme 到 YOLO 格式的轉換")

        # 轉換訓練集
        logger.info(f"轉換訓練集: {train_dir}")
        success_count, mapping = convert_labelme_to_yolo(
            input_dir=train_dir,
            output_root=train_dir,
            class_mapping=class_mapping,
            copy_images=True,
            log_enabled=True
        )
        logger.info(f"已轉換 {success_count} 個訓練集標註檔案")

        # 轉換驗證集
        logger.info(f"轉換驗證集: {val_dir}")
        success_count, mapping = convert_labelme_to_yolo(
            input_dir=val_dir,
            output_root=val_dir,
            class_mapping=class_mapping,
            copy_images=True,
            log_enabled=True
        )
        logger.info(f"已轉換 {success_count} 個驗證集標註檔案")

        # 轉換測試集
        logger.info(f"轉換測試集: {test_dir}")
        success_count, mapping = convert_labelme_to_yolo(
            input_dir=test_dir,
            output_root=test_dir,
            class_mapping=class_mapping,
            copy_images=True,
            log_enabled=True
        )
        logger.info(f"已轉換 {success_count} 個測試集標註檔案")
    else:
        logger.info("資料已經是 YOLO 格式，無需轉換")

    return {
        'train': train_dir,
        'val': val_dir,
        'test': test_dir
    }


# Step 2: 建立 YOLO 資料集設定檔
def create_dataset_yaml(data_paths):
    yaml_path = os.path.join(run_dir, 'dataset.yaml')

    # 建立資料集設定檔內容
    dataset_config = {
        'path': data_dir,  # 資料集根目錄
        'train': os.path.join('train', 'images'),  # 從根目錄的相對路徑
        'val': os.path.join('val', 'images'),
        'test': os.path.join('test', 'images'),

        # 類別資訊 (從 0 開始的索引)
        'names': {v: k for k, v in class_mapping.items()}
    }

    # 寫入 YAML 檔案
    with open(yaml_path, 'w') as f:
        yaml.dump(dataset_config, f, default_flow_style=False)

    logger.info(f"已建立資料集設定檔: {yaml_path}")
    return yaml_path

# Step 3: 訓練 YOLO 模型
def train_yolo(dataset_yaml, epochs=500):
    logger.info("開始訓練 yolov12x 模型...")

    # 選擇 YOLOv12 模型
    model = YOLO(
        r'D:\new_model_pipline\run_data\yolov12x_20250602_162435\weights\last.pt')

    # 設定訓練參數
    results = model.train(
        data=dataset_yaml,
        # resume=True,
        epochs=epochs,
        imgsz=image_size,
        batch=batch_size,  # 可根據 GPU 記憶體調整
        device=0 if torch.cuda.is_available() else 'cpu',
        project=path_total,
        name=os.path.basename(run_dir),
        exist_ok=True,
        pretrained=True,
        save=True,  # 保存最佳模型
        plots=True,  # 生成圖表
        auto_augment=None,
        hsv_h=0.015,
        hsv_s=1.0,
        hsv_v=0.7,
        degrees=180.0,
        translate=0.4,
        scale=0.1,
        shear=30,
        perspective=0.0005,
        flipud=0.5,
        fliplr=0.5,
        erasing=0.4,
        mosaic=0.5,
        cutmix=0,
        patience=train_epochs,
        cos_lr=True,
        seed=seed,

    )

    logger.info("YOLOv12 訓練完成")
    return model, results

# 主流程
# 準備資料
data_paths = prepare_data()

# 建立資料集設定檔
dataset_yaml = create_dataset_yaml(data_paths)

# 訓練模型
model, train_results = train_yolo(dataset_yaml, epochs=train_epochs)

logger.info("全部流程完成")

import os
import logging
import torch
from ultralytics import YOLO
import glob
import matplotlib.pyplot as plt
import cv2
import numpy as np
import time
import pandas as pd
from collections import defaultdict
from PIL import Image, ImageDraw, ImageFont

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

"""設置日誌"""
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('YOLO_Predict')

# 設定參數 - 請根據您的實際路徑進行調整
best_weight_path = r'run_data\yolov12x_20250602_162435\weights\best.pt'  # 已訓練好的權重檔案

# 置信度閾值設定（可以在這裡修改）
CONFIDENCE_THRESHOLD = 0.05  # 預設值通常是 0.25，您可以設定為 0.1 到 0.9 之間的任何值

# 使用您確認存在的路徑
test_dir = r'D:\image\6_test_image_test\ai_photo\臺北蓋水特色框蓋\遠照'
# test_images_dir = os.path.join(test_dir, 'images')
test_images_dir = test_dir

output_dir = r'run_data\yolov12x_20250602_162435\evaluation_results2'  # 保存評估結果的目錄

# 確保輸出目錄存在
os.makedirs(output_dir, exist_ok=True)

# 類別名稱映射
class_mapping = {'bird': 0, 'block': 1,
                 'retaining seat': 2, 'treefish': 3, 'tpe': 4}
class_names = {0: '鳥',  1: '方塊', 2: '擋土', 3: '樹魚', 4: '台北蓋水特色框蓋', }

# 從英文到中文的完整映射
en_to_cn_mapping = {
    'bird': '鳥',
    'block': '方塊',
    'retaining seat': '擋土座',
    'treefish': '樹魚',
    'tpe': '台北蓋水特色框蓋'
}


def draw_chinese_labels(image, boxes, font_path=None):
    """繪製中文標籤的檢測框"""
    # 複製圖像以避免修改原始圖像
    img_copy = image.copy()

    # 轉換為 PIL 圖像以支援中文
    img_pil = Image.fromarray(cv2.cvtColor(img_copy, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img_pil)

    # 嘗試載入中文字體
    try:
        if font_path and os.path.exists(font_path):
            font = ImageFont.truetype(font_path, 20)
        else:
            # 嘗試使用系統字體
            font_paths = [
                'C:/Windows/Fonts/msyh.ttc',  # 微軟雅黑
                'C:/Windows/Fonts/mingliub.ttc',  # 細明體
                'C:/Windows/Fonts/simsun.ttc',  # 宋體
                '/System/Library/Fonts/PingFang.ttc',  # macOS
                '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
            ]

            font = None
            for path in font_paths:
                if os.path.exists(path):
                    font = ImageFont.truetype(path, 20)
                    break

            if font is None:
                font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()

    # 定義顏色
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255),
              (255, 255, 0), (255, 0, 255), (0, 255, 255)]

    # 繪製每個檢測框
    for box in boxes:
        # 獲取座標
        x1, y1, x2, y2 = box.xyxy[0].tolist()
        cls_id = int(box.cls)
        conf = float(box.conf)

        # 選擇顏色
        color = colors[cls_id % len(colors)]

        # 繪製框
        draw.rectangle([x1, y1, x2, y2], outline=color, width=2)

        # 準備標籤文字
        class_name = class_names.get(cls_id, f'類別{cls_id}')
        label = f"{class_name} {conf:.2f}"

        # 計算文字大小
        left, top, right, bottom = draw.textbbox((x1, y1), label, font=font)
        text_width = right - left
        text_height = bottom - top

        # 繪製文字背景
        draw.rectangle([x1, y1 - text_height, x1 + text_width, y1], fill=color)

        # 繪製文字
        draw.text((x1 + 2, y1 - text_height - 2),
                  label, fill=(0, 0, 0), font=font)

    # 轉換回 OpenCV 格式
    result_img = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    return result_img


def verify_test_dir():
    """驗證測試目錄結構並顯示資訊"""
    logger.info(f"檢查測試目錄: {test_dir}")

    if os.path.exists(test_dir):
        logger.info(f"✓ 測試目錄存在")

        # 檢查 images 子目錄
        if os.path.exists(test_images_dir):
            logger.info(f"✓ images 子目錄存在")

            # 檢查圖像文件
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                image_files.extend(
                    glob.glob(os.path.join(test_images_dir, ext)))

            if image_files:
                logger.info(f"✓ 找到 {len(image_files)} 個圖像文件")
                for i in range(min(5, len(image_files))):
                    logger.info(f"  - {os.path.basename(image_files[i])}")
                if len(image_files) > 5:
                    logger.info(f"  - ... 等 {len(image_files)} 個文件")
            else:
                logger.warning(f"✗ 沒有找到任何圖像文件")
        else:
            logger.warning(f"✗ images 子目錄不存在")

        # 檢查 labels 子目錄
        labels_dir = os.path.join(test_dir, 'labels')
        if os.path.exists(labels_dir):
            logger.info(f"✓ labels 子目錄存在")

            # 檢查標籤文件
            label_files = glob.glob(os.path.join(labels_dir, '*.txt'))
            if label_files:
                logger.info(f"✓ 找到 {len(label_files)} 個標籤文件")
            else:
                logger.warning(f"✗ 沒有找到任何標籤文件")
        else:
            logger.warning(f"✗ labels 子目錄不存在")
    else:
        logger.error(f"✗ 測試目錄不存在")
        return False

    return True


def load_model():
    """載入訓練好的模型"""
    logger.info(f"載入權重檔案: {best_weight_path}")

    try:
        # 載入模型
        model = YOLO(best_weight_path)
        logger.info(f"模型載入成功")
        return model
    except Exception as e:
        logger.error(f"模型載入失敗: {str(e)}")
        return None


def predict_all_images(model, num_images=None):
    """為所有測試圖像進行預測並計算性能指標"""
    logger.info("開始進行預測與性能評估...")

    # 獲取測試圖像
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(os.path.join(test_images_dir, ext)))

    if not image_files:
        logger.error(f"在 {test_images_dir} 找不到任何圖像檔案")
        return

    # 限制樣本數量
    if num_images is not None and num_images < len(image_files):
        sample_images = image_files[:num_images]
        logger.info(f"限制為前 {num_images} 張圖像")
    else:
        sample_images = image_files
        logger.info(f"使用全部 {len(sample_images)} 張圖像")

    # 創建預測結果目錄
    predict_dir = os.path.join(output_dir, 'predictions')
    os.makedirs(predict_dir, exist_ok=True)

    # 初始化性能計算變數
    total_time = 0
    total_boxes = 0
    results_data = []
    class_metrics = defaultdict(
        lambda: {'count': 0, 'total_confidence': 0, 'areas': []})

    # 逐一處理圖像
    for i, img_path in enumerate(sample_images):
        # 讀取原始圖像
        img = cv2.imread(img_path)
        img_height, img_width = img.shape[:2]

        # 進行預測並計時
        start_time = time.time()
        results = model.predict(
            source=img_path,
            conf=CONFIDENCE_THRESHOLD,  # 使用設定的置信度閾值
            save=False,  # 我們自己控制儲存
            save_txt=False,
            save_conf=False,
            verbose=False
        )
        end_time = time.time()

        inference_time = end_time - start_time
        total_time += inference_time

        # 獲取預測結果
        result = results[0]

        # 處理檢測框結果
        num_boxes = 0
        if hasattr(result, 'boxes') and result.boxes is not None:
            boxes = result.boxes
            num_boxes = len(boxes)
            total_boxes += num_boxes

            logger.info(
                f"圖像 {os.path.basename(img_path)} - 檢測到 {num_boxes} 個目標 (耗時 {inference_time:.4f}秒)")

            # 收集結果數據
            for box in boxes:
                cls_id = int(box.cls.item())
                conf = float(box.conf.item())
                x1, y1, x2, y2 = box.xyxy[0].tolist()

                # 計算相對於圖像大小的百分比
                x1_pct = x1 / img_width * 100
                y1_pct = y1 / img_height * 100
                x2_pct = x2 / img_width * 100
                y2_pct = y2 / img_height * 100
                area_pct = (x2_pct - x1_pct) * (y2_pct - y1_pct)

                # 獲取中文類別名稱
                class_name_cn = class_names.get(cls_id, f'類別{cls_id}')

                results_data.append({
                    'image': os.path.basename(img_path),
                    'class': class_name_cn,
                    'confidence': conf,
                    'x1': x1_pct,
                    'y1': y1_pct,
                    'x2': x2_pct,
                    'y2': y2_pct,
                    'width': (x2_pct - x1_pct),
                    'height': (y2_pct - y1_pct),
                    'area': area_pct,
                    'x1_pixel': x1,
                    'y1_pixel': y1,
                    'x2_pixel': x2,
                    'y2_pixel': y2
                })

                # 更新類別統計
                class_metrics[class_name_cn]['count'] += 1
                class_metrics[class_name_cn]['total_confidence'] += conf
                class_metrics[class_name_cn]['areas'].append(area_pct)

        # 獲取預測結果
        result = results[0]

        # 手動繪製中文標籤
        if hasattr(result, 'boxes') and result.boxes is not None:
            # 讀取原始圖像
            img = cv2.imread(img_path)
            # 使用自定義函數繪製中文標籤
            result_img = draw_chinese_labels(img, result.boxes)
        else:
            # 如果沒有檢測框，使用默認繪製
            result_img = result.plot()

        # 使用原始檔名儲存視覺化結果
        output_path = os.path.join(predict_dir, os.path.basename(img_path))
        cv2.imwrite(output_path, result_img)

    # 計算整體性能指標
    num_images = len(sample_images)
    avg_time_per_image = total_time / num_images
    fps = 1.0 / avg_time_per_image
    avg_boxes_per_image = total_boxes / num_images

    logger.info("\n===== 整體性能統計 =====")
    logger.info(f"處理圖像數量: {num_images}")
    logger.info(f"總處理時間: {total_time:.4f} 秒")
    logger.info(f"平均每張圖片處理時間: {avg_time_per_image:.4f} 秒")
    logger.info(f"FPS: {fps:.2f}")
    logger.info(f"總檢測框數量: {total_boxes}")
    logger.info(f"平均每張圖像檢測框數量: {avg_boxes_per_image:.2f}")

    # 計算各類別指標
    class_stats = {}
    for class_name, metrics in class_metrics.items():
        if metrics['count'] > 0:
            avg_confidence = metrics['total_confidence'] / metrics['count']
            avg_area = sum(metrics['areas']) / len(metrics['areas'])
            class_stats[class_name] = {
                'count': metrics['count'],
                'average_confidence': avg_confidence,
                'average_area': avg_area,
                'min_area': min(metrics['areas']),
                'max_area': max(metrics['areas'])
            }

    # 保存整體性能結果
    performance_path = os.path.join(
        output_dir, 'overall_performance_metrics.txt')
    with open(performance_path, 'w', encoding='utf-8') as f:
        f.write("===== YOLOv12 模型整體性能評估 =====\n")
        f.write(f"模型: {os.path.basename(best_weight_path)}\n")
        f.write(f"置信度閾值: {CONFIDENCE_THRESHOLD}\n")
        f.write(f"測試圖像數量: {num_images}\n")
        f.write(f"總處理時間: {total_time:.4f} 秒\n")
        f.write(f"平均每張圖片處理時間: {avg_time_per_image:.4f} 秒\n")
        f.write(f"FPS: {fps:.2f}\n")
        f.write(f"總檢測框數量: {total_boxes}\n")
        f.write(f"平均每張圖像檢測框數量: {avg_boxes_per_image:.2f}\n")

    # 保存各類別指標
    class_performance_path = os.path.join(
        output_dir, 'class_performance_metrics.txt')
    with open(class_performance_path, 'w', encoding='utf-8') as f:
        f.write("===== 各類別檢測指標 =====\n\n")
        for class_name, stats in class_stats.items():
            f.write(f"類別: {class_name}\n")
            f.write(f"  檢測數量: {stats['count']}\n")
            f.write(f"  平均置信度: {stats['average_confidence']:.4f}\n")
            f.write(f"  平均面積佔比: {stats['average_area']:.2f}%\n")
            f.write(f"  最小面積佔比: {stats['min_area']:.2f}%\n")
            f.write(f"  最大面積佔比: {stats['max_area']:.2f}%\n")
            f.write("\n")

    # 保存所有檢測結果為 CSV
    df = pd.DataFrame(results_data)
    csv_path = os.path.join(output_dir, 'all_detection_results.csv')
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    logger.info(f"所有檢測結果已保存至: {csv_path}")

    # 保存各類別檢測結果為單獨的 CSV
    for class_name in df['class'].unique():
        class_df = df[df['class'] == class_name]
        class_csv_path = os.path.join(
            output_dir, f'detection_results_{class_name}.csv')
        class_df.to_csv(class_csv_path, index=False, encoding='utf-8-sig')
        logger.info(f"{class_name} 檢測結果已保存至: {class_csv_path}")

    # 繪製類別分布圖 (使用中文)
    plt.figure(figsize=(10, 6))
    class_counts = df['class'].value_counts()
    class_counts.plot(kind='bar')
    plt.title('各類別檢測數量分布', fontsize=16)
    plt.xlabel('類別', fontsize=14)
    plt.ylabel('數量', fontsize=14)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'class_distribution.png'), dpi=300)
    plt.close()

    # 繪製各類別置信度分布圖
    plt.figure(figsize=(12, 8))
    for class_name in df['class'].unique():
        class_df = df[df['class'] == class_name]
        plt.hist(class_df['confidence'], bins=20,
                 alpha=0.5, label=class_name, range=(0, 1))
    plt.title('各類別置信度分布', fontsize=16)
    plt.xlabel('置信度', fontsize=14)
    plt.ylabel('數量', fontsize=14)
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(
        output_dir, 'confidence_distribution_by_class.png'), dpi=300)
    plt.close()

    # 保存各類別統計為 CSV
    class_stats_df = pd.DataFrame.from_dict(class_stats, orient='index')
    class_stats_df.index.name = '類別'
    class_stats_csv_path = os.path.join(output_dir, 'class_statistics.csv')
    class_stats_df.to_csv(class_stats_csv_path, encoding='utf-8-sig')
    logger.info(f"各類別統計已保存至: {class_stats_csv_path}")

    return fps, avg_time_per_image, results_data, class_stats


def create_visualization_grid(model, num_images=9):
    """創建一個檢測結果的圖像網格"""
    logger.info("創建檢測結果圖像網格...")

    # 獲取測試圖像
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(os.path.join(test_images_dir, ext)))

    if not image_files:
        logger.error(f"在 {test_images_dir} 找不到任何圖像檔案")
        return

    # 選擇樣本
    import random
    random.seed(42)  # 確保結果可重複
    if len(image_files) > num_images:
        sample_images = random.sample(image_files, num_images)
    else:
        sample_images = image_files

    # 設定網格大小
    grid_size = int(np.ceil(np.sqrt(len(sample_images))))

    # 創建大圖
    plt.figure(figsize=(15, 15))

    for i, img_path in enumerate(sample_images):
        if i >= num_images:
            break

        # 進行預測
        results = model.predict(
            source=img_path,
            conf=CONFIDENCE_THRESHOLD,  # 使用設定的置信度閾值
            verbose=False
        )
        result = results[0]

        # 手動繪製中文標籤
        if hasattr(result, 'boxes') and result.boxes is not None:
            # 讀取原始圖像
            img = cv2.imread(img_path)
            # 使用自定義函數繪製中文標籤
            result_img = draw_chinese_labels(img, result.boxes)
        else:
            # 如果沒有檢測框，使用默認繪製
            result_img = result.plot()

        # 添加到網格
        plt.subplot(grid_size, grid_size, i + 1)
        plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        plt.title(f'#{i+1}: {os.path.basename(img_path)}', fontsize=10)
        plt.axis('off')

    plt.tight_layout()
    grid_path = os.path.join(output_dir, 'detection_grid.png')
    plt.savefig(grid_path, dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"檢測結果圖像網格已保存至: {grid_path}")


def main():
    logger.info("===== 開始 YOLOv12 模型預測評估 =====")
    logger.info(f"當前置信度閾值: {CONFIDENCE_THRESHOLD}")
    logger.info(f"輸出目錄: {output_dir}")

    # 驗證測試目錄
    if not verify_test_dir():
        logger.error("目錄驗證失敗，無法繼續評估")
        return

    # 載入模型
    model = load_model()
    if model is None:
        logger.error("模型載入失敗，無法繼續評估")
        return

    # 進行預測並評估性能
    fps, inference_time, results, class_stats = predict_all_images(
        model, num_images=None)  # 設為 None 處理所有圖像

    # 創建可視化網格
    create_visualization_grid(model)

    logger.info("所有預測評估任務完成！")
    logger.info(f"結果已保存至: {output_dir}")


if __name__ == "__main__":
    main()


import os
import logging
import torch
from ultralytics import YOLO
import glob
import matplotlib.pyplot as plt
import cv2
import numpy as np
import time
import pandas as pd
from collections import defaultdict
from PIL import Image, ImageDraw, ImageFont
import json
from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

"""設置日誌"""
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('YOLO_Metrics')

# 設定參數 - 請根據您的實際路徑進行調整
best_weight_path = r'run_data\yolov12x_20250602_162435\weights\best.pt'  # 已訓練好的權重檔案

# 置信度閾值設定（可以在這裡修改）
CONFIDENCE_THRESHOLD = 0.1  # 預設值通常是 0.25，您可以設定為 0.1 到 0.9 之間的任何值
IOU_THRESHOLD = 0.3  # IoU 閾值，用於決定檢測框與真實框是否匹配

# 使用您確認存在的路徑
test_dir = r'D:\5_Hole_cover\train_20250522_151822\3_divided_dataset\object_detect\test'

test_images_dir = os.path.join(test_dir, 'images')
test_labels_dir = os.path.join(test_dir, 'labels')

output_dir = r'run_data\yolov12x_20250602_162435\metrics_evaluation'  # 保存評估結果的目錄

# 確保輸出目錄存在
os.makedirs(output_dir, exist_ok=True)

# 類別名稱映射
class_mapping = {'bird': 0, 'block': 1,
                 'retaining seat': 2, 'treefish': 3, 'tpe': 4}
class_names = {0: 'bird', 1: 'block',
               2: 'retaining seat', 3: 'treefish', 4: 'tpe'}
# class_names = {0: 'face', 1: 'license_plate', 2: 'mask'}


# 從英文到中文的完整映射
en_to_cn_mapping = {
    'bird': '鳥',
    'block': '方塊',
    'retaining seat': '擋土座',
    'treefish': '樹魚',
    'tpe': '台北蓋水特色框蓋'
}


def verify_test_dir():
    """驗證測試目錄結構並顯示資訊"""
    logger.info(f"檢查測試目錄: {test_dir}")

    if os.path.exists(test_dir):
        logger.info(f"✓ 測試目錄存在")

        # 檢查 images 子目錄
        if os.path.exists(test_images_dir):
            logger.info(f"✓ images 子目錄存在")

            # 檢查圖像文件
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                image_files.extend(
                    glob.glob(os.path.join(test_images_dir, ext)))

            if image_files:
                logger.info(f"✓ 找到 {len(image_files)} 個圖像文件")
                for i in range(min(5, len(image_files))):
                    logger.info(f"  - {os.path.basename(image_files[i])}")
                if len(image_files) > 5:
                    logger.info(f"  - ... 等 {len(image_files)} 個文件")
            else:
                logger.warning(f"✗ 沒有找到任何圖像文件")
        else:
            logger.warning(f"✗ images 子目錄不存在")

        # 檢查 labels 子目錄
        if os.path.exists(test_labels_dir):
            logger.info(f"✓ labels 子目錄存在")

            # 檢查標籤文件
            label_files = glob.glob(os.path.join(test_labels_dir, '*.txt'))
            if label_files:
                logger.info(f"✓ 找到 {len(label_files)} 個標籤文件")

                # 檢查標籤文件內容
                for i in range(min(3, len(label_files))):
                    with open(label_files[i], 'r') as f:
                        content = f.read().strip()
                        logger.info(f"  - {os.path.basename(label_files[i])} 內容: {content[:50]}..." if len(
                            content) > 50 else f"  - {os.path.basename(label_files[i])} 內容: {content}")
            else:
                logger.warning(f"✗ 沒有找到任何標籤文件")
        else:
            logger.warning(f"✗ labels 子目錄不存在")
    else:
        logger.error(f"✗ 測試目錄不存在")
        return False

    return True


def load_model():
    """載入訓練好的模型"""
    logger.info(f"載入權重檔案: {best_weight_path}")

    try:
        # 載入模型
        model = YOLO(best_weight_path)
        logger.info(f"模型載入成功")
        return model
    except Exception as e:
        logger.error(f"模型載入失敗: {str(e)}")
        return None


def calculate_iou(box1, box2):
    """計算兩個框的IoU (Intersection over Union)"""
    # 轉換格式為 [x1, y1, x2, y2]
    if len(box1) == 4 and len(box2) == 4:
        # 計算相交區域
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])

        # 計算相交面積
        if x2 < x1 or y2 < y1:
            return 0.0  # 沒有相交
        intersection = (x2 - x1) * (y2 - y1)

        # 計算各自面積
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])

        # 計算IoU
        iou = intersection / (area1 + area2 - intersection)
        return iou
    else:
        return 0.0


def read_yolo_labels(label_file, img_width, img_height):
    """讀取YOLO格式的標籤文件"""
    boxes = []

    if not os.path.exists(label_file):
        return boxes

    try:
        with open(label_file, 'r') as f:
            lines = f.readlines()

        for line in lines:
            parts = line.strip().split()
            if len(parts) >= 5:  # class_id, x_center, y_center, width, height
                class_id = int(parts[0])
                x_center = float(parts[1]) * img_width
                y_center = float(parts[2]) * img_height
                width = float(parts[3]) * img_width
                height = float(parts[4]) * img_height

                # 轉換為 [x1, y1, x2, y2] 格式
                x1 = x_center - width / 2
                y1 = y_center - height / 2
                x2 = x_center + width / 2
                y2 = y_center + height / 2

                boxes.append({
                    'class_id': class_id,
                    'bbox': [x1, y1, x2, y2]
                })
    except Exception as e:
        logger.error(f"讀取標籤文件時出錯: {str(e)}")

    return boxes


def evaluate_metrics(all_predictions, all_groundtruths, num_classes):
    """計算各種評估指標"""
    # 初始化指標字典
    metrics = {
        'overall': {
            'TP': 0, 'FP': 0, 'FN': 0, 'TN': 0,
            'IoU': 0, 'precision': 0, 'recall': 0, 'specificity': 0, 'f1': 0
        },
        'per_class': {},
        'no_background': {
            'TP': 0, 'FP': 0, 'FN': 0, 'TN': 0,
            'IoU': 0, 'precision': 0, 'recall': 0, 'specificity': 0, 'f1': 0
        }
    }

    # 初始化每個類別的指標
    for i in range(num_classes):
        metrics['per_class'][i] = {
            'TP': 0, 'FP': 0, 'FN': 0, 'TN': 0,
            'IoU': 0, 'precision': 0, 'recall': 0, 'specificity': 0, 'f1': 0,
            'IoU_values': []
        }

    # 遍歷所有圖像
    total_images = len(all_groundtruths)
    if total_images == 0:
        logger.error("沒有找到任何標籤文件")
        return metrics

    # 統計各類別的數量
    gt_class_counts = defaultdict(int)
    pred_class_counts = defaultdict(int)

    # 逐個圖像評估
    for img_id in all_groundtruths.keys():
        if img_id not in all_predictions:
            continue

        gt_boxes = all_groundtruths[img_id]
        pred_boxes = all_predictions[img_id]

        # 標記已匹配的真實框和預測框
        matched_gt = [False] * len(gt_boxes)
        matched_pred = [False] * len(pred_boxes)

        # 記錄每個類別的真實框
        for i, gt in enumerate(gt_boxes):
            class_id = gt['class_id']
            gt_class_counts[class_id] += 1

        # 記錄每個類別的預測框
        for i, pred in enumerate(pred_boxes):
            class_id = pred['class_id']
            pred_class_counts[class_id] += 1

        # 計算每個預測框和每個真實框之間的IoU
        for i, pred in enumerate(pred_boxes):
            pred_class = pred['class_id']
            pred_bbox = pred['bbox']

            best_iou = -1
            best_gt_idx = -1

            # 尋找最匹配的真實框
            for j, gt in enumerate(gt_boxes):
                gt_class = gt['class_id']
                gt_bbox = gt['bbox']

                # 如果類別相同，計算IoU
                if gt_class == pred_class:
                    iou = calculate_iou(pred_bbox, gt_bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = j

            # 如果IoU超過閾值，則匹配成功
            if best_iou >= IOU_THRESHOLD and not matched_gt[best_gt_idx]:
                matched_gt[best_gt_idx] = True
                matched_pred[i] = True

                # 真陽性 (TP) - 整體
                metrics['overall']['TP'] += 1
                # 真陽性 (TP) - 對應類別
                metrics['per_class'][pred_class]['TP'] += 1
                # 記錄IoU值
                metrics['per_class'][pred_class]['IoU_values'].append(best_iou)

                # 非背景類別
                if pred_class > 0:
                    metrics['no_background']['TP'] += 1

        # 計算假陽性 (FP)
        for i, matched in enumerate(matched_pred):
            if not matched:
                pred_class = pred_boxes[i]['class_id']
                metrics['overall']['FP'] += 1
                metrics['per_class'][pred_class]['FP'] += 1

                if pred_class > 0:
                    metrics['no_background']['FP'] += 1

        # 計算假陰性 (FN)
        for i, matched in enumerate(matched_gt):
            if not matched:
                gt_class = gt_boxes[i]['class_id']
                metrics['overall']['FN'] += 1
                metrics['per_class'][gt_class]['FN'] += 1

                if gt_class > 0:
                    metrics['no_background']['FN'] += 1

    # 計算真陰性 (TN) - 對於目標檢測任務，這是較為複雜的
    # 簡化：真陰性可以估計為其他類別的正確拒絕
    for class_id in range(num_classes):
        # 每個類別的TN是其他所有類別的TP + 其他所有類別未檢測到的真實框 (其他類別的FN)
        metrics['per_class'][class_id]['TN'] = 0
        for other_class in range(num_classes):
            if other_class != class_id:
                metrics['per_class'][class_id]['TN'] += (metrics['per_class'][other_class]['TP'] +
                                                         metrics['per_class'][other_class]['FN'])

    # 計算整體TN
    metrics['overall']['TN'] = sum(
        metrics['per_class'][i]['TN'] for i in range(num_classes)) // (num_classes - 1)

    # 計算非背景類別的TN
    bg_tn = metrics['per_class'][0]['TN'] if 0 in metrics['per_class'] else 0
    metrics['no_background']['TN'] = bg_tn

    # 計算各種指標 - 整體
    tp = metrics['overall']['TP']
    fp = metrics['overall']['FP']
    fn = metrics['overall']['FN']
    tn = metrics['overall']['TN']

    if tp + fp > 0:
        metrics['overall']['precision'] = tp / (tp + fp)
    if tp + fn > 0:
        metrics['overall']['recall'] = tp / (tp + fn)
    if tn + fp > 0:
        metrics['overall']['specificity'] = tn / (tn + fp)
    if tp + fp + fn > 0:
        metrics['overall']['IoU'] = tp / (tp + fp + fn)
    if tp + fp + fn + tn > 0:
        metrics['overall']['accuracy'] = (tp + tn) / (tp + fp + fn + tn)
    if metrics['overall']['precision'] + metrics['overall']['recall'] > 0:
        metrics['overall']['f1'] = 2 * metrics['overall']['precision'] * \
            metrics['overall']['recall'] / \
            (metrics['overall']['precision'] + metrics['overall']['recall'])

    # 計算各種指標 - 每個類別
    for class_id in range(num_classes):
        tp = metrics['per_class'][class_id]['TP']
        fp = metrics['per_class'][class_id]['FP']
        fn = metrics['per_class'][class_id]['FN']
        tn = metrics['per_class'][class_id]['TN']

        if tp + fp > 0:
            metrics['per_class'][class_id]['precision'] = tp / (tp + fp)
        if tp + fn > 0:
            metrics['per_class'][class_id]['recall'] = tp / (tp + fn)
        if tn + fp > 0:
            metrics['per_class'][class_id]['specificity'] = tn / (tn + fp)
        if tp + fp + fn > 0:
            metrics['per_class'][class_id]['IoU'] = tp / (tp + fp + fn)
        if tp + fp + fn + tn > 0:
            metrics['per_class'][class_id]['accuracy'] = (
                tp + tn) / (tp + fp + fn + tn)
        if metrics['per_class'][class_id]['precision'] + metrics['per_class'][class_id]['recall'] > 0:
            metrics['per_class'][class_id]['f1'] = 2 * metrics['per_class'][class_id]['precision'] * \
                metrics['per_class'][class_id]['recall'] / (
                    metrics['per_class'][class_id]['precision'] + metrics['per_class'][class_id]['recall'])

        # 平均IoU
        if metrics['per_class'][class_id]['IoU_values']:
            metrics['per_class'][class_id]['mean_IoU'] = sum(
                metrics['per_class'][class_id]['IoU_values']) / len(metrics['per_class'][class_id]['IoU_values'])

    # 計算mIoU
    class_ious = []
    for class_id in range(num_classes):
        if metrics['per_class'][class_id]['IoU'] > 0:
            class_ious.append(metrics['per_class'][class_id]['IoU'])

    metrics['overall']['mIoU'] = sum(
        class_ious) / len(class_ious) if class_ious else 0

    # 計算各種指標 - 非背景類別
    tp = metrics['no_background']['TP']
    fp = metrics['no_background']['FP']
    fn = metrics['no_background']['FN']
    tn = metrics['no_background']['TN']

    if tp + fp > 0:
        metrics['no_background']['precision'] = tp / (tp + fp)
    if tp + fn > 0:
        metrics['no_background']['recall'] = tp / (tp + fn)
    if tn + fp > 0:
        metrics['no_background']['specificity'] = tn / (tn + fp)
    if tp + fp + fn > 0:
        metrics['no_background']['IoU'] = tp / (tp + fp + fn)
    if tp + fp + fn + tn > 0:
        metrics['no_background']['accuracy'] = (tp + tn) / (tp + fp + fn + tn)
    if metrics['no_background']['precision'] + metrics['no_background']['recall'] > 0:
        metrics['no_background']['f1'] = 2 * metrics['no_background']['precision'] * \
            metrics['no_background']['recall'] / \
            (metrics['no_background']['precision'] +
             metrics['no_background']['recall'])

    # 添加類別統計
    metrics['class_counts'] = {
        'groundtruth': dict(gt_class_counts),
        'prediction': dict(pred_class_counts)
    }

    return metrics


def run_metrics_evaluation(model, num_images=None):
    """執行指標評估"""
    logger.info("開始進行指標評估...")

    # 獲取測試圖像
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(os.path.join(test_images_dir, ext)))

    if not image_files:
        logger.error(f"在 {test_images_dir} 找不到任何圖像檔案")
        return

    # 限制樣本數量
    if num_images is not None and num_images < len(image_files):
        sample_images = image_files[:num_images]
        logger.info(f"限制為前 {num_images} 張圖像")
    else:
        sample_images = image_files
        logger.info(f"使用全部 {len(sample_images)} 張圖像")

    # 創建評估結果目錄
    eval_dir = os.path.join(output_dir, 'evaluation')
    os.makedirs(eval_dir, exist_ok=True)

    # 初始化收集預測和真實標籤的字典
    all_predictions = {}  # 存儲所有預測結果
    all_groundtruths = {}  # 存儲所有真實標籤

    # 逐一處理圖像
    for i, img_path in enumerate(sample_images):
        # 獲取圖像ID（不含副檔名）
        img_id = os.path.splitext(os.path.basename(img_path))[0]

        # 讀取原始圖像，獲取寬高
        img = cv2.imread(img_path)
        if img is None:
            logger.error(f"無法讀取圖像: {img_path}")
            continue

        img_height, img_width = img.shape[:2]

        # 獲取對應的標籤文件路徑
        label_path = os.path.join(test_labels_dir, f"{img_id}.txt")

        # 讀取真實標籤
        gt_boxes = read_yolo_labels(label_path, img_width, img_height)
        all_groundtruths[img_id] = gt_boxes

        # 進行預測
        results = model.predict(
            source=img_path,
            conf=CONFIDENCE_THRESHOLD,
            iou=IOU_THRESHOLD,
            verbose=False
        )

        # 收集預測框
        result = results[0]
        pred_boxes = []

        if hasattr(result, 'boxes') and result.boxes is not None:
            boxes = result.boxes

            for j in range(len(boxes)):
                cls_id = int(boxes.cls[j].item())
                conf = float(boxes.conf[j].item())
                x1, y1, x2, y2 = boxes.xyxy[j].tolist()

                pred_boxes.append({
                    'class_id': cls_id,
                    'conf': conf,
                    'bbox': [x1, y1, x2, y2]
                })

        all_predictions[img_id] = pred_boxes

        # 繪製檢測結果與真實標籤的對比
        vis_img = img.copy()

        # 先繪製真實標籤 (綠色)
        for box in gt_boxes:
            x1, y1, x2, y2 = box['bbox']
            class_id = box['class_id']
            class_name = class_names.get(class_id, f'類別{class_id}')

            cv2.rectangle(vis_img, (int(x1), int(y1)),
                          (int(x2), int(y2)), (0, 255, 0), 2)
            cv2.putText(vis_img, f"GT:{class_name}", (int(x1), int(y1) - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 再繪製預測框 (紅色)
        for box in pred_boxes:
            x1, y1, x2, y2 = box['bbox']
            class_id = box['class_id']
            conf = box['conf']
            class_name = class_names.get(class_id, f'類別{class_id}')

            cv2.rectangle(vis_img, (int(x1), int(y1)),
                          (int(x2), int(y2)), (0, 0, 255), 2)
            cv2.putText(vis_img, f"PR:{class_name} {conf:.2f}", (int(x1), int(y1) - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

        # 保存可視化結果
        vis_path = os.path.join(eval_dir, f"compare_{img_id}.jpg")
        cv2.imwrite(vis_path, vis_img)

        # 報告進度
        if (i + 1) % 10 == 0:
            logger.info(f"已處理 {i+1}/{len(sample_images)} 張圖像")

    # 計算所有指標
    metrics = evaluate_metrics(
        all_predictions, all_groundtruths, len(class_names))

    # 保存指標結果
    metrics_path = os.path.join(output_dir, 'all_metrics.json')
    with open(metrics_path, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, indent=2, ensure_ascii=False)

    # 生成指標報告（文字格式）
    report_path = os.path.join(output_dir, 'metrics_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("===== YOLOv12 模型評估指標報告 =====\n\n")
        f.write(f"模型: {os.path.basename(best_weight_path)}\n")
        f.write(f"置信度閾值: {CONFIDENCE_THRESHOLD}\n")
        f.write(f"IoU閾值: {IOU_THRESHOLD}\n")
        f.write(f"測試圖像數量: {len(sample_images)}\n\n")

        # 輸出整體指標
        f.write("===== 整體指標 =====\n")
        f.write(f"TP (真陽性): {metrics['overall']['TP']}\n")
        f.write(f"FP (假陽性): {metrics['overall']['FP']}\n")
        f.write(f"FN (假陰性): {metrics['overall']['FN']}\n")
        f.write(f"TN (真陰性): {metrics['overall']['TN']}\n")
        f.write(f"IoU: {metrics['overall']['IoU']:.4f}\n")
        f.write(f"mIoU: {metrics['overall']['mIoU']:.4f}\n")
        f.write(f"Precision (精確率): {metrics['overall']['precision']:.4f}\n")
        f.write(
            f"Recall/Sensitivity (召回率/敏感度): {metrics['overall']['recall']:.4f}\n")
        f.write(
            f"Specificity (特異度): {metrics['overall']['specificity']:.4f}\n")
        f.write(f"F1 Score (F1分數): {metrics['overall']['f1']:.4f}\n")
        if 'accuracy' in metrics['overall']:
            f.write(f"Accuracy (準確率): {metrics['overall']['accuracy']:.4f}\n")
        f.write("\n")

        # 輸出非背景類別指標
        f.write("===== 非背景類別指標 =====\n")
        f.write(f"TP (真陽性): {metrics['no_background']['TP']}\n")
        f.write(f"FP (假陽性): {metrics['no_background']['FP']}\n")
        f.write(f"FN (假陰性): {metrics['no_background']['FN']}\n")
        f.write(f"TN (真陰性): {metrics['no_background']['TN']}\n")
        f.write(f"IoU: {metrics['no_background']['IoU']:.4f}\n")
        f.write(
            f"Precision (精確率): {metrics['no_background']['precision']:.4f}\n")
        f.write(
            f"Recall/Sensitivity (召回率/敏感度): {metrics['no_background']['recall']:.4f}\n")
        f.write(
            f"Specificity (特異度): {metrics['no_background']['specificity']:.4f}\n")
        f.write(f"F1 Score (F1分數): {metrics['no_background']['f1']:.4f}\n")
        if 'accuracy' in metrics['no_background']:
            f.write(
                f"Accuracy (準確率): {metrics['no_background']['accuracy']:.4f}\n")
        f.write("\n")

        # 輸出每個類別的指標
        f.write("===== 各類別指標 =====\n\n")
        for class_id in range(len(class_names)):
            class_name = class_names.get(class_id, f'類別{class_id}')
            f.write(f"--- {class_name} ---\n")
            f.write(f"TP (真陽性): {metrics['per_class'][class_id]['TP']}\n")
            f.write(f"FP (假陽性): {metrics['per_class'][class_id]['FP']}\n")
            f.write(f"FN (假陰性): {metrics['per_class'][class_id]['FN']}\n")
            f.write(f"TN (真陰性): {metrics['per_class'][class_id]['TN']}\n")
            f.write(f"IoU: {metrics['per_class'][class_id]['IoU']:.4f}\n")
            if 'mean_IoU' in metrics['per_class'][class_id]:
                f.write(
                    f"Mean IoU: {metrics['per_class'][class_id]['mean_IoU']:.4f}\n")
            f.write(
                f"Precision (精確率): {metrics['per_class'][class_id]['precision']:.4f}\n")
            f.write(
                f"Recall/Sensitivity (召回率/敏感度): {metrics['per_class'][class_id]['recall']:.4f}\n")
            f.write(
                f"Specificity (特異度): {metrics['per_class'][class_id]['specificity']:.4f}\n")
            f.write(
                f"F1 Score (F1分數): {metrics['per_class'][class_id]['f1']:.4f}\n")
            if 'accuracy' in metrics['per_class'][class_id]:
                f.write(
                    f"Accuracy (準確率): {metrics['per_class'][class_id]['accuracy']:.4f}\n")
            f.write("\n")

        # 輸出類別統計
        f.write("===== 類別數量統計 =====\n")
        f.write("真實標籤 (Ground Truth):\n")
        for class_id, count in metrics['class_counts']['groundtruth'].items():
            class_name = class_names.get(int(class_id), f'類別{class_id}')
            f.write(f"  {class_name}: {count}\n")

        f.write("\n預測結果 (Prediction):\n")
        for class_id, count in metrics['class_counts']['prediction'].items():
            class_name = class_names.get(int(class_id), f'類別{class_id}')
            f.write(f"  {class_name}: {count}\n")

    # 生成CSV格式的報告
    csv_path = os.path.join(output_dir, 'metrics_summary.csv')
    with open(csv_path, 'w', encoding='utf-8-sig', newline='') as f:
        import csv
        writer = csv.writer(f)

        # 寫入標題
        writer.writerow(['類別', 'TP', 'FP', 'FN', 'TN', 'IoU', 'Precision',
                        'Recall/Sensitivity', 'Specificity', 'F1 Score', 'Accuracy'])

        # 寫入整體指標
        writer.writerow([
            '整體',
            metrics['overall']['TP'],
            metrics['overall']['FP'],
            metrics['overall']['FN'],
            metrics['overall']['TN'],
            f"{metrics['overall']['IoU']:.4f}",
            f"{metrics['overall']['precision']:.4f}",
            f"{metrics['overall']['recall']:.4f}",
            f"{metrics['overall']['specificity']:.4f}",
            f"{metrics['overall']['f1']:.4f}",
            f"{metrics['overall'].get('accuracy', 0):.4f}"
        ])

        # 寫入非背景類別指標
        writer.writerow([
            '非背景類別',
            metrics['no_background']['TP'],
            metrics['no_background']['FP'],
            metrics['no_background']['FN'],
            metrics['no_background']['TN'],
            f"{metrics['no_background']['IoU']:.4f}",
            f"{metrics['no_background']['precision']:.4f}",
            f"{metrics['no_background']['recall']:.4f}",
            f"{metrics['no_background']['specificity']:.4f}",
            f"{metrics['no_background']['f1']:.4f}",
            f"{metrics['no_background'].get('accuracy', 0):.4f}"
        ])

        # 寫入每個類別的指標
        for class_id in range(len(class_names)):
            class_name = class_names.get(class_id, f'類別{class_id}')
            writer.writerow([
                class_name,
                metrics['per_class'][class_id]['TP'],
                metrics['per_class'][class_id]['FP'],
                metrics['per_class'][class_id]['FN'],
                metrics['per_class'][class_id]['TN'],
                f"{metrics['per_class'][class_id]['IoU']:.4f}",
                f"{metrics['per_class'][class_id]['precision']:.4f}",
                f"{metrics['per_class'][class_id]['recall']:.4f}",
                f"{metrics['per_class'][class_id]['specificity']:.4f}",
                f"{metrics['per_class'][class_id]['f1']:.4f}",
                f"{metrics['per_class'][class_id].get('accuracy', 0):.4f}"
            ])

    # 繪製指標視覺化圖表
    # 1. 各類別精確率、召回率和F1分數對比
    plt.figure(figsize=(12, 8))
    class_ids = list(range(len(class_names)))
    class_names_list = [class_names.get(i, f'類別{i}') for i in class_ids]

    precision_values = [metrics['per_class'][i]['precision']
                        for i in class_ids]
    recall_values = [metrics['per_class'][i]['recall'] for i in class_ids]
    f1_values = [metrics['per_class'][i]['f1'] for i in class_ids]

    x = np.arange(len(class_ids))
    width = 0.25

    plt.bar(x - width, precision_values, width, label='Precision')
    plt.bar(x, recall_values, width, label='Recall')
    plt.bar(x + width, f1_values, width, label='F1')

    plt.xlabel('類別')
    plt.ylabel('分數')
    plt.title('各類別精確率、召回率和F1分數')
    plt.xticks(x, class_names_list, rotation=45)
    plt.ylim(0, 1.1)
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'metrics_comparison.png'), dpi=300)
    plt.close()

    # 2. 混淆矩陣
    # 構建混淆矩陣
    conf_matrix = np.zeros((len(class_names), len(class_names)), dtype=int)

    # 逐個圖像處理
    for img_id in all_groundtruths.keys():
        if img_id not in all_predictions:
            continue

        gt_boxes = all_groundtruths[img_id]
        pred_boxes = all_predictions[img_id]

        # 標記已匹配的真實框和預測框
        matched_gt = [False] * len(gt_boxes)
        matched_pred = [False] * len(pred_boxes)

        # 計算每個預測框和每個真實框之間的IoU
        for i, pred in enumerate(pred_boxes):
            pred_class = pred['class_id']
            pred_bbox = pred['bbox']

            best_iou = -1
            best_gt_idx = -1
            best_gt_class = -1

            # 尋找最匹配的真實框
            for j, gt in enumerate(gt_boxes):
                gt_class = gt['class_id']
                gt_bbox = gt['bbox']

                # 計算IoU
                iou = calculate_iou(pred_bbox, gt_bbox)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
                    best_gt_class = gt_class

            # 如果IoU超過閾值，則匹配成功，否則為假陽性
            if best_iou >= IOU_THRESHOLD:
                matched_gt[best_gt_idx] = True
                matched_pred[i] = True
                conf_matrix[best_gt_class][pred_class] += 1
            else:
                # 假陽性 - 實際是背景（或其他）但預測為某類別
                conf_matrix[0][pred_class] += 1  # 假設 0 為背景類別

        # 處理未匹配的真實框（假陰性）
        for j, matched in enumerate(matched_gt):
            if not matched:
                gt_class = gt_boxes[j]['class_id']
                # 假陰性 - 實際是某類別但預測為背景
                conf_matrix[gt_class][0] += 1  # 假設 0 為背景類別

    # 繪製混淆矩陣
    plt.figure(figsize=(10, 8))
    plt.imshow(conf_matrix, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('混淆矩陣')
    plt.colorbar()

    tick_marks = np.arange(len(class_names))
    plt.xticks(tick_marks, class_names_list, rotation=45)
    plt.yticks(tick_marks, class_names_list)

    # 在混淆矩陣中顯示數值
    thresh = conf_matrix.max() / 2.0
    for i in range(conf_matrix.shape[0]):
        for j in range(conf_matrix.shape[1]):
            plt.text(j, i, format(conf_matrix[i, j], 'd'),
                     horizontalalignment="center",
                     color="white" if conf_matrix[i, j] > thresh else "black")

    plt.ylabel('真實類別')
    plt.xlabel('預測類別')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'), dpi=300)
    plt.close()

    # 3. IoU 分布
    plt.figure(figsize=(10, 6))
    for class_id in range(len(class_names)):
        if metrics['per_class'][class_id]['IoU_values']:
            plt.hist(metrics['per_class'][class_id]['IoU_values'], alpha=0.5, bins=10,
                     label=class_names.get(class_id, f'類別{class_id}'))

    plt.xlabel('IoU')
    plt.ylabel('數量')
    plt.title('各類別IoU分布')
    plt.legend()
    plt.savefig(os.path.join(output_dir, 'iou_distribution.png'), dpi=300)
    plt.close()

    # 4. 預測框大小與置信度關係圖
    plt.figure(figsize=(10, 6))
    for img_id, boxes in all_predictions.items():
        for box in boxes:
            x1, y1, x2, y2 = box['bbox']
            width = x2 - x1
            height = y2 - y1
            area = width * height
            conf = box['conf']
            class_id = box['class_id']

            plt.scatter(area, conf, alpha=0.6, c=class_id, cmap='viridis')

    plt.xlabel('面積 (像素^2)')
    plt.ylabel('置信度')
    plt.title('預測框面積與置信度關係')
    plt.colorbar(label='類別')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'area_vs_confidence.png'), dpi=300)
    plt.close()

    logger.info(f"指標評估完成！結果已保存至: {output_dir}")

    return metrics

logger.info("===== 開始 YOLOv12 模型指標評估 =====")
logger.info(f"當前置信度閾值: {CONFIDENCE_THRESHOLD}")
logger.info(f"當前IoU閾值: {IOU_THRESHOLD}")
logger.info(f"輸出目錄: {output_dir}")

# 驗證測試目錄
if not verify_test_dir():
    logger.error("目錄驗證失敗，無法繼續評估")

# 載入模型
model = load_model()
if model is None:
    logger.error("模型載入失敗，無法繼續評估")

# 執行指標評估
metrics = run_metrics_evaluation(model, num_images=None)  # 設為 None 處理所有圖像

# 輸出主要指標摘要
logger.info("\n==== 主要指標摘要 ====")
logger.info(f"mIoU: {metrics['overall']['mIoU']:.4f}")
logger.info(f"Precision: {metrics['overall']['precision']:.4f}")
logger.info(f"Recall: {metrics['overall']['recall']:.4f}")
logger.info(f"F1 Score: {metrics['overall']['f1']:.4f}")

logger.info("\n所有評估任務完成！")
logger.info(f"結果已保存至: {output_dir}")

# 將所有指標匯總打印成表格
print("\n===== 指標摘要表 =====")
print(f"{'類別':<15} {'TP':<6} {'FP':<6} {'FN':<6} {'TN':<6} {'IoU':<8} {'Precision':<10} {'Recall':<10} {'F1':<8}")
print("-" * 80)

# 整體指標
print(f"{'整體':<15} {metrics['overall']['TP']:<6} {metrics['overall']['FP']:<6} "
      f"{metrics['overall']['FN']:<6} {metrics['overall']['TN']:<6} "
      f"{metrics['overall']['IoU']:.4f}   {metrics['overall']['precision']:.4f}     "
      f"{metrics['overall']['recall']:.4f}     {metrics['overall']['f1']:.4f}")

# 非背景類別指標
print(f"{'非背景類別':<15} {metrics['no_background']['TP']:<6} {metrics['no_background']['FP']:<6} "
      f"{metrics['no_background']['FN']:<6} {metrics['no_background']['TN']:<6} "
      f"{metrics['no_background']['IoU']:.4f}   {metrics['no_background']['precision']:.4f}     "
      f"{metrics['no_background']['recall']:.4f}     {metrics['no_background']['f1']:.4f}")

# 各類別指標
for class_id in range(len(class_names)):
    class_name = class_names.get(class_id, f'類別{class_id}')
    print(f"{class_name:<15} {metrics['per_class'][class_id]['TP']:<6} {metrics['per_class'][class_id]['FP']:<6} "
          f"{metrics['per_class'][class_id]['FN']:<6} {metrics['per_class'][class_id]['TN']:<6} "
          f"{metrics['per_class'][class_id]['IoU']:.4f}   {metrics['per_class'][class_id]['precision']:.4f}     "
          f"{metrics['per_class'][class_id]['recall']:.4f}     {metrics['per_class'][class_id]['f1']:.4f}")
    

from ultralytics import YOLO
import logging
import os
import time
import wandb
from IPython.display import clear_output
import datetime
import torch
import numpy as np
import random
import matplotlib.pyplot as plt
import albumentations as A
from albumentations.pytorch import ToTensorV2

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False
# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Main')


def inference_yolo_with_isolated_mask(model, test_dir, output_dir, device="cuda", class_names=None, conf_threshold=0.7, iou_threshold=0.5, original_scale=0.0166):
    """
    使用YOLO segmentation模型進行推理，並以ultralytics文檔中推薦的方式提取掩碼
    
    參數:
        model: 已加載的YOLO模型 (應該是分割模型，如yolov11-seg)
        test_dir: 測試圖像目錄
        output_dir: 輸出結果保存目錄
        device: 計算設備
        class_names: 類別名稱字典
        conf_threshold: 置信度閾值
        iou_threshold: IoU閾值
        original_scale: 原始圖像的比例因子，每像素對應的實際距離(米)
    """
    
    # 導入必要的庫
    import gc
    import psutil
    import matplotlib.pyplot as plt
    import os
    import glob
    import time
    from datetime import datetime
    import sys
    import locale
    import csv
    import numpy as np
    import cv2
    import torch
    from PIL import Image, ImageDraw, ImageFont
    from tqdm import tqdm
    import pandas as pd
    from pathlib import Path
    
    # 設定編碼環境
    if sys.platform.startswith('win'):
        # 在Windows設定控制台編碼
        locale.setlocale(locale.LC_ALL, 'zh_TW.UTF-8')  # 台灣繁體中文
    else:
        # 在Linux/Mac設定UTF-8編碼
        locale.setlocale(locale.LC_ALL, 'zh_TW.utf8')
    
    # 設定matplotlib中文支援
    plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'Arial Unicode MS', 'SimHei', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False  # 正確顯示負號
    plt.ioff()  # 關閉互動模式
    
    # 註冊OpenCV中文字型
    def cv2_chinese_text(img, text, position, font_size=0.7, color=(255, 255, 255), thickness=2):
        """使用PIL在OpenCV圖像上添加中文文字"""
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 將OpenCV圖像轉換為PIL圖像
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)
        
        # 獲取系統支援的字型
        try:
            if sys.platform.startswith('win'):
                font_path = 'C:\\Windows\\Fonts\\msjh.ttc'  # Windows微軟正黑體
            elif sys.platform.startswith('darwin'):
                font_path = '/System/Library/Fonts/PingFang.ttc'  # macOS
            else:
                font_path = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'  # Linux文泉驛微米黑
            font = ImageFont.truetype(font_path, int(font_size * 20))
        except:
            # 如果找不到字型，使用默認字型
            font = ImageFont.load_default()
        
        # 繪製文字
        draw.text(position, text, font=font, fill=color)
        
        # 將PIL圖像轉換回OpenCV圖像
        return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    
    # 確保輸出路徑存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 創建掩碼專用目錄
    # masks_dir = os.path.join(output_dir, 'masks')
    # os.makedirs(masks_dir, exist_ok=True)
    
    # 為CSV數據創建多個小文件而不是一個大列表
    csv_temp_dir = os.path.join(output_dir, 'temp_csv')
    os.makedirs(csv_temp_dir, exist_ok=True)
    
    # 獲取測試圖像列表
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:  # 添加BMP支持
        image_files.extend(glob.glob(os.path.join(test_dir, ext)))
        # 檢查子目錄
        subdirs = [d for d in glob.glob(os.path.join(test_dir, '*')) if os.path.isdir(d)]
        for subdir in subdirs:
            image_files.extend(glob.glob(os.path.join(subdir, ext)))
    
    if not image_files:
        print(f"在 {test_dir} 中沒有找到任何圖像")
        return
    
    print(f"找到 {len(image_files)} 張圖像進行推理")
    
    # 每個批次保存的CSV數據行數，用於最終合併
    csv_counter = 0
    record_count = 0  # 記錄計數
    total_time = 0
    total_frames = 0
    
    # 記憶體監控初始值
    initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    print(f"初始記憶體使用: {initial_memory:.2f} MB")
    
    # 創建進度條
    infer_loop = tqdm(enumerate(image_files), total=len(image_files))
    
    # 處理每個圖像
    for batch_idx, img_path in infer_loop:
        try:
            # 獲取檔名
            filename = os.path.basename(img_path)
            img_name = Path(img_path).stem  # 檔案名稱（不含副檔名）
            
            # 讀取原始圖像
            original_img = cv2.imread(img_path)
            if original_img is None:
                print(f"無法讀取圖像: {img_path}")
                continue
            
            # 獲取圖像尺寸
            img_height, img_width = original_img.shape[:2]
            original_size = (img_height, img_width)
            
            # 模型預測
            start_time = time.time()
            results = model.predict(
                source=img_path,
                conf=conf_threshold,
                iou=iou_threshold,
                verbose=False
            )
            end_time = time.time()
            
            inference_time = end_time - start_time
            total_time += inference_time
            total_frames += 1
            
            # 處理預測結果
            result = results[0]  # 獲取第一個結果（因為只有一張圖像）
            
            has_predictions = False
            batch_results = []
            
            # 檢查任務類型和掩碼可用性
            if not hasattr(result, 'masks') or result.masks is None:
                print(f"警告: 圖像 {filename} 沒有分割掩碼。請確保您使用的是分割模型(如yolov11-seg)")
                continue
            
            # 創建彩色掩碼圖像用於視覺化
            mask_img = np.zeros((img_height, img_width, 3), dtype=np.uint8)
            
            # 固定的顏色映射
            np.random.seed(42)
            colors = {}
            
            # 獲取數據
            boxes = result.boxes
            masks = result.masks
            
            if len(boxes) == 0:
                print(f"圖像 {filename} 沒有檢測到任何物體")
                continue
                
            has_predictions = True
            
            # 為每個類創建隨機顏色
            unique_classes = set()
            for j in range(len(boxes)):
                cls_id = int(boxes.cls[j].item())
                unique_classes.add(cls_id)
            
            for cls in unique_classes:
                if cls not in colors:
                    colors[cls] = np.random.randint(0, 255, 3).tolist()
            
            # 處理每個檢測到的物體
            for i, c in enumerate(result):
                try:
                    # 獲取類別ID和置信度
                    cls_id = int(c.boxes.cls.tolist().pop())
                    conf = float(c.boxes.conf.tolist().pop())
                    if cls_id == 0 or cls_id == 1 :
                        continue
                    
                    # 獲取類別名稱
                    class_name = class_names.get(cls_id+1, f"類別 {cls_id+1}") if class_names else f"類別 {cls_id+1}"
                    # 獲取框座標
                    x1, y1, x2, y2 = c.boxes.xyxy.cpu().numpy().squeeze().astype(np.int32)
                    
                    # 使用ultralytics文檔中的方法提取掩碼
                    # 創建二值掩碼
                    b_mask = np.zeros((img_height, img_width), dtype=np.uint8)
                    
                    # 獲取輪廓
                    try:
                        # 提取輪廓結果 - 使用適當的錯誤處理
                        if hasattr(c.masks, 'xy') and len(c.masks.xy) > 0:
                            contour = c.masks.xy[0].astype(np.int32).reshape(-1, 1, 2)
                            # 在掩碼上繪製輪廓
                            cv2.drawContours(b_mask, [contour], -1, (255, 255, 255), cv2.FILLED)
                        else:
                            # 如果沒有輪廓數據，使用框作為備用方案
                            cv2.rectangle(b_mask, (x1, y1), (x2, y2), 255, -1)
                    except Exception as e:
                        print(f"提取輪廓時出錯: {str(e)}")
                        # 使用框作為備用方案
                        cv2.rectangle(b_mask, (x1, y1), (x2, y2), 255, -1)
                    
                    # 將掩碼添加到彩色掩碼圖像
                    color = colors[cls_id]
                    mask_img[b_mask > 0] = color
                    
                    # 計算掩碼的面積（像素）
                    area_px = np.sum(b_mask > 0)
                    
                    # 計算實際尺寸
                    area_m2 = area_px * (original_scale ** 2)
                    
                    # 計算寬度和高度 (從掩碼中)
                    width_px = x2 - x1
                    height_px = y2 - y1
                    width_m = width_px * original_scale
                    height_m = height_px * original_scale

                    if cls_id == 3:  # 裂縫類別
                        # 判斷是裂縫還是龜裂
                        aspect_ratio_threshold = 0.8  # 可調整的閾值
                        area_ratio_threshold = 0.4    # 可調整的閾值
                        
                        # 計算長寬比
                        aspect_ratio = min(width_px, height_px) / max(width_px, height_px)
                        
                        # 計算框面積與掩碼面積的比率
                        box_area = width_px * height_px
                        mask_to_box_ratio = area_px / box_area if box_area > 0 else 0
                        
                        # 判斷是裂縫還是龜裂
                        if aspect_ratio < aspect_ratio_threshold and mask_to_box_ratio < area_ratio_threshold:
                            # 符合裂縫條件
                            class_name = "裂縫"
                        else:
                            # 符合龜裂條件
                            class_name = "龜裂"

                    
                    # # 選項1: 隔離帶黑色背景的對象
                    # mask3ch = cv2.cvtColor(b_mask, cv2.COLOR_GRAY2BGR)
                    # isolated = cv2.bitwise_and(mask3ch, original_img)
                    
                    # # 選項2: 隔離帶透明背景的對象 (保存為PNG時有效)
                    # isolated_transparent = np.dstack([original_img, b_mask])
                    
                    # # 保存隔離的對象掩碼
                    # mask_filename = f"{img_name}_class{cls_id}_{i}.png"
                    # cv2.imwrite(os.path.join(masks_dir, mask_filename), isolated)
                    
                    # # 可選: 裁剪隔離物體
                    # iso_crop = isolated[y1:y2, x1:x2]
                    # crop_filename = f"{img_name}_class{cls_id}_{i}_crop.png"
                    # cv2.imwrite(os.path.join(masks_dir, crop_filename), iso_crop)
                    
                    # 增加記錄計數
                    record_count += 1
                    
                    # 添加到結果列表
                    batch_results.append({
                        "序號": record_count,
                        "檔案名稱": filename,
                        "類別": class_name,
                        "長": f"{height_m:.2f}",
                        "寬": f"{width_m:.2f}",
                        "面積": f"{area_m2:.2f}",
                        "置信度": f"{conf:.4f}",  # 加入置信度
                        "x_min": x1,  # 加入box的x最小值
                        "y_min": y1,  # 加入box的y最小值
                        "x_max": x2,  # 加入box的x最大值
                        "y_max": y2,  # 加入box的y最大值                       
                    })
                except Exception as e:
                    print(f"處理物體 {i} 時出錯: {str(e)}")
                    continue
            
            # 如果有預測結果，則保存圖像
            if has_predictions and batch_results:
                # 轉換原圖為RGB以便處理
                original_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
                
                # 創建掩碼疊加圖像
                alpha = 0.7
                overlay_img = original_rgb.copy()
                mask = np.any(mask_img > 0, axis=2)
                overlay_img[mask] = (1-alpha) * overlay_img[mask] + alpha * mask_img[mask]
                
                # 水平拼接原圖和疊加圖
                combined_img = np.hstack((original_rgb, overlay_img))
                
                # 轉回BGR用於OpenCV處理
                combined_img = cv2.cvtColor(combined_img, cv2.COLOR_RGB2BGR)
                
                # 添加圖例
                legend_y = 0
                for cls_id, color in colors.items():
                    if cls_id+1 in class_names:
                        class_name = class_names[cls_id+1]
                    else:
                        class_name = f"類別 {cls_id+1}"
                    
                    # 繪製顏色方塊
                    cv2.rectangle(combined_img, 
                                 (original_img.shape[1] + 1550, legend_y + 50),
                                 (original_img.shape[1] + 1700, legend_y + 200), 
                                 color, -1)
                    
                    # 添加類別名稱
                    combined_img = cv2_chinese_text(
                        combined_img, 
                        class_name, 
                        (original_img.shape[1] + 1750, legend_y), 
                        font_size=9.0,
                        color=(0, 0, 0)  
                    )
                    
                    legend_y += 230
                
                # 添加標題
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     "原始圖像", 
                #     (original_img.shape[1] // 4, 20), 
                #     font_size=1.0,
                #     color=(255, 255, 255)
                # )
                
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     "分割掩碼", 
                #     (original_img.shape[1] + original_img.shape[1] // 4, 20), 
                #     font_size=1.0,
                #     color=(255, 255, 255)
                # )
                
                # 在圖像上添加掩碼面積信息
                # info_y = img_height - 80
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     f"掩碼數量: {len(batch_results)}", 
                #     (original_img.shape[1] + 20, info_y), 
                #     font_size=0.8,
                #     color=(255, 255, 255)
                # )
                
                # 添加總面積
                # total_area_px = sum(float(result['像素']) for result in batch_results)
                # total_area_m2 = sum(float(result['面積'].strip()) for result in batch_results)
                
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     f"總面積: {total_area_m2:.2f} m²", 
                #     (original_img.shape[1] + 20, info_y + 25), 
                #     font_size=0.8,
                #     color=(255, 255, 255)
                # )
                
                # combined_img = cv2_chinese_text(
                #     combined_img, 
                #     f"總像素: {total_area_px}", 
                #     (original_img.shape[1] + 20, info_y + 50), 
                #     font_size=0.8,
                #     color=(255, 255, 255)
                # )
                
                # 保存圖像 - 使用安全的檔名
                safe_filename = ''.join(c if c.isalnum() or c in '._- ' else '_' for c in filename)
                save_path = os.path.join(output_dir, f'segmentation_{safe_filename}')
                cv2.imwrite(save_path, combined_img)
                
                # 保存批次結果到臨時CSV文件
                if batch_results:
                    # 轉換為DataFrame並保存
                    temp_df = pd.DataFrame(batch_results)
                    # 使用批次索引作為檔名以避免中文檔名問題
                    temp_csv_path = os.path.join(csv_temp_dir, f'temp_{batch_idx:06d}.csv')
                    temp_df.to_csv(temp_csv_path, index=False, encoding='utf_8_sig')
                    csv_counter += len(batch_results)
            
            # 更新進度條
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_diff = current_memory - initial_memory
            infer_loop.set_postfix(
                Progress=f"{batch_idx+1}/{len(image_files)}",
                Records=record_count,
                Time=f"{inference_time:.4f}s",
                Memory=f"{current_memory:.2f}MB ({memory_diff:+.2f})"
            )
            
            # 每50張圖像進行一次強制清理
            if batch_idx % 50 == 0 and batch_idx > 0:
                # 強制清理
                for _ in range(3):  # 多次嘗試清理
                    gc.collect()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    time.sleep(0.1)  # 給系統一些時間來整理記憶體
                
                # 記錄清理後的記憶體使用
                after_gc_memory = psutil.Process().memory_info().rss / 1024 / 1024
                print(f"批次 {batch_idx}: 清理後記憶體使用 {after_gc_memory:.2f} MB")
                
                # 定期保存中間結果
                if batch_idx % 500 == 0:
                    merge_temp_csvs(csv_temp_dir, output_dir, f'segment_checkpoint_{batch_idx}.csv')
            
        except Exception as e:
            print(f"處理圖像 {img_path} 時發生錯誤: {str(e)}")
            # 保存當前進度
            print("正在保存目前進度...")
            try:
                # 合併所有已處理的CSV
                merge_temp_csvs(csv_temp_dir, output_dir, f'segment_error_at_{batch_idx}.csv')
            except Exception as csv_err:
                print(f"保存進度時發生錯誤: {str(csv_err)}")
            import traceback
            traceback.print_exc()
    
    # 計算性能指標
    fps = total_frames / total_time if total_time > 0 else 0
    print(f"\n推理性能指標:")
    print(f"總推理時間: {total_time:.2f} 秒")
    print(f"總幀數: {total_frames}")
    print(f"平均FPS: {fps:.2f}")
    
    # 保存性能指標
    with open(os.path.join(output_dir, 'segment_performance.csv'), 'w', newline='', encoding='utf_8_sig') as f:
        writer = csv.writer(f)
        writer.writerow(['總推理時間(秒)', '總幀數', '平均FPS'])
        writer.writerow([f"{total_time:.2f}", total_frames, f"{fps:.2f}"])
    
    # 合併所有臨時CSV文件到最終CSV
    print("正在合併所有臨時CSV文件...")
    merge_temp_csvs(csv_temp_dir, output_dir, 'segment_statistics.csv')
    
    # 最終記憶體使用狀況
    final_memory = psutil.Process().memory_info().rss / 1024 / 1024
    print(f"最終記憶體使用: {final_memory:.2f} MB")
    print(f"記憶體增加: {final_memory - initial_memory:.2f} MB")
    
    print(f"推理結果已保存至 {output_dir} 目錄")
    # print(f"掩碼已保存至 {masks_dir} 目錄")
    print(f"分割統計已保存至 {os.path.join(output_dir, 'segment_statistics.csv')}")
    
    return fps

def merge_temp_csvs(temp_dir, output_dir, output_filename):
    """合併臨時CSV文件到一個最終文件，支援中文內容"""
    import glob
    import pandas as pd
    import os
    
    all_files = glob.glob(os.path.join(temp_dir, 'temp_*.csv'))
    if not all_files:
        print("沒有找到臨時CSV文件")
        return
    
    # 排序檔案，確保按順序合併
    all_files.sort()
    
    # 讀取並合併所有CSV
    all_dfs = []
    for file in all_files:
        try:
            # 使用utf_8_sig編碼處理中文
            df = pd.read_csv(file, encoding='utf_8_sig')
            all_dfs.append(df)
        except Exception as e:
            print(f"讀取文件 {file} 時發生錯誤: {str(e)}")
    
    if all_dfs:
        # 合併所有DataFrame
        combined_df = pd.concat(all_dfs, ignore_index=True)
        # 保存合併後的CSV（使用utf_8_sig編碼處理中文）
        output_path = os.path.join(output_dir, output_filename)
        combined_df.to_csv(output_path, index=False, encoding='utf_8_sig')
        print(f"已合併 {len(all_dfs)} 個臨時CSV文件，共 {len(combined_df)} 行數據")
    else:
        print("沒有有效的CSV數據可以合併")

best_weight_path = r"D:\new_model_pipline\model_with_test\best.pt"
test_data_path = r"D:\image\5_test_image_test\0516"  # 替換為你的測試圖像目錄
# test_data_path = r"D:\image\5_test_image_test\0220"  # 替換為你的測試圖像目錄
# test_data_path = r"D:\image\5_test_image_test\0415"  # 替換為你的測試圖像目錄
output_path = r".\yolov11seg_onroad_0516"          # 替換為你想要保存結果的目錄
class_names = {0: '背景', 1: '伸縮縫', 2: '路面接縫', 3: '裂縫', 4: '坑洞', 5: '補綻'}

def load_model():
    """載入訓練好的模型"""
    logger.info(f"載入權重檔案: {best_weight_path}")
    
    try:
        # 載入模型
        model = YOLO(best_weight_path)
        logger.info(f"模型載入成功")
        return model
    except Exception as e:
        logger.error(f"模型載入失敗: {str(e)}")
        return None

# 使用範例
model = load_model()  # 加載YOLO模型
inference_yolo_with_isolated_mask(
    model=model,
    test_dir=test_data_path,
    output_dir=output_path,
    device="cuda",
    class_names=class_names,
    conf_threshold=0.6,
    iou_threshold=0.5,
    original_scale=0.0166
)

test_data_path = r"D:\image\5_test_image_test\0220"  # 替換為你的測試圖像目錄
output_path = r".\yolov11seg_onroad_0220"          # 替換為你想要保存結果的目錄

model = load_model()  # 加載YOLO模型
inference_yolo_with_isolated_mask(
    model=model,
    test_dir=test_data_path,
    output_dir=output_path,
    device="cuda",
    class_names=class_names,
    conf_threshold=0.6,
    iou_threshold=0.5,
    original_scale=0.0166
)

test_data_path = r"D:\image\5_test_image_test\0415"  # 替換為你的測試圖像目錄
output_path = r".\yolov11seg_onroad_0415"          # 替換為你想要保存結果的目錄

model = load_model()  # 加載YOLO模型
inference_yolo_with_isolated_mask(
    model=model,
    test_dir=test_data_path,
    output_dir=output_path,
    device="cuda",
    class_names=class_names,
    conf_threshold=0.6,
    iou_threshold=0.5,
    original_scale=0.0166
)

# 整合 YOLO 分割模型測試與現有測試函數
from ultralytics import YOLO
import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import time
from pathlib import Path
import logging
import torch
from tqdm import tqdm
from IPython.display import clear_output


# 設置繪圖樣式為英文，避免中文顯示問題
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('YOLO_Test_Integration')

# 指標評估類別


class runningScore(object):
    def __init__(self, n_classes):
        self.n_classes = n_classes
        self.confusion_matrix = np.zeros((n_classes, n_classes))

    def _fast_hist(self, label_true, label_pred, n_class):
        mask = (label_true >= 0) & (label_true < n_class)
        hist = np.bincount(
            n_class * label_true[mask].astype(int) + label_pred[mask],
            minlength=n_class ** 2,
        ).reshape(n_class, n_class)
        return hist

    def update(self, label_trues, label_preds):
        """更新混淆矩陣"""
        for lt, lp in zip(label_trues, label_preds):
            self.confusion_matrix += self._fast_hist(
                lt.flatten(), lp.flatten(), self.n_classes
            )

    def get_scores(self):
        """獲取評估指標"""
        # 計算各類指標
        hist = self.confusion_matrix
        TP = np.diag(hist)
        FP = hist.sum(axis=0) - TP
        FN = hist.sum(axis=1) - TP
        TN = hist.sum() - (TP + FP + FN)

        # 計算像素準確率
        acc = np.diag(hist).sum() / hist.sum()

        # 計算每個類別的 IoU
        IoU = TP / (TP + FP + FN + 1e-10)
        mIoU = np.nanmean(IoU)

        # 計算每個類別的 Dice
        dice_per_class = (2 * TP) / (2 * TP + FP + FN + 1e-10)

        # 計算每個類別的 F1 分數
        f1_per_class = (2 * TP) / (2 * TP + FP + FN + 1e-10)
        f1 = np.nanmean(f1_per_class)

        # 計算每個類別的精確率
        precision_per_class = TP / (TP + FP + 1e-10)
        precision = np.nanmean(precision_per_class)

        # 計算每個類別的敏感性
        sensitivity_per_class = TP / (TP + FN + 1e-10)
        sensitivity = np.nanmean(sensitivity_per_class)

        # 計算每個類別的特異性
        specificity_per_class = TN / (TN + FP + 1e-10)
        specificity = np.nanmean(specificity_per_class)

        # 返回所有指標
        return {
            "Pixel_Accuracy": acc,
            "MIoU": mIoU,
            "IoU": IoU,
            "F1": f1,
            "F1_per_class": f1_per_class,
            "Dice_per_class": dice_per_class,
            "Precision": precision,
            "Precision_per_class": precision_per_class,
            "Sensitivity": sensitivity,
            "Sensitivity_per_class": sensitivity_per_class,
            "Specificity": specificity,
            "Specificity_per_class": specificity_per_class,
            "confusion_matrix": hist
        }

# 獲取指標函數


def get_metrics(y_true, y_pred):
    """計算準確率和 Jaccard 分數"""
    y_true = y_true.flatten()
    y_pred = y_pred.flatten()

    # 計算準確率
    accuracy = np.sum(y_true == y_pred) / len(y_true)

    # 計算 Jaccard 分數 (IoU)
    intersection = np.sum((y_true > 0) & (y_pred > 0))
    union = np.sum((y_true > 0) | (y_pred > 0))
    jaccard = intersection / (union + 1e-10)

    return accuracy, jaccard

# YOLO 分割模型測試集成類


class YOLOSegmentTest:
    def __init__(self, model_path=None, device=None):
        """
        初始化 YOLO 分割模型測試器

        參數:
            model_path: YOLO 模型路徑，例如 'yolo11s-seg.pt'
            device: 指定運行設備，預設為自動選擇
        """
        self.device = device if device is not None else torch.device(
            "cuda:0" if torch.cuda.is_available() else "cpu")

        # 預設模型路徑
        if model_path is None:
            model_path = r"D:\路面缺陷資料集\YOLO\ultralytics\download_model\semantic_segmentation\yolo11s-seg.pt"

        logger.info(f"加載 YOLO 模型: {model_path}")
        logger.info(f"使用設備: {self.device}")

        # 加載模型
        try:
            self.model = YOLO(model_path)
            logger.info("模型加載成功!")
        except Exception as e:
            logger.error(f"模型加載失敗: {str(e)}")
            raise

    def yolo_test(self, test_dir, output_dir, class_names, conf=0.25, iou=0.45, n_classes=6,
                  class_thresholds=None, classes=None, skip_empty=True):
        """
        使用類似於原始測試函數的方式對 YOLO 分割模型進行測試

        參數:
            test_dir: 測試圖像目錄
            output_dir: 輸出結果目錄
            class_names: 類別名稱字典
            conf: 置信度閾值
            iou: NMS IoU 閾值
            n_classes: 類別數量
            class_thresholds: 每類別自訂信心門檻的字典 {class_id: threshold}
            classes: 要顯示的類別列表
            skip_empty: 是否跳過無檢測結果的圖像
        """
        # 設定保存路徑
        test_dir = Path(test_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 設定模型為評估模式
        self.model.model.eval()

        # 初始化一個runningScore實例用於整體評估
        overall_metrics = runningScore(n_classes=n_classes)

        # 空列表，用於添加準確率和 Jaccard 分數計算
        all_gt_flattened = []
        all_pred_flattened = []

        # 獲取所有圖像和對應的標籤路徑
        image_paths = list(test_dir.glob(
            "*.jpg")) + list(test_dir.glob("*.png")) + list(test_dir.glob("*.jpeg"))
        print(image_paths)
        print(len(image_paths))
        if not image_paths:
            logger.warning(f"找不到圖像檔案: {test_dir}")
            return

        logger.info(f"找到 {len(image_paths)} 張圖像，開始處理...")

        # 創建輸出目錄
        predict_dir = output_dir / "predict"
        predict_dir.mkdir(exist_ok=True)

        # 測試計時
        start_time = time.time()

        # 創建進度條
        test_loop = tqdm(enumerate(image_paths), total=len(image_paths))

        # 找出標籤目錄
        test_label_dir = test_dir  # 標籤和圖像在同一目錄
        if not test_label_dir.exists():
            logger.warning(f"找不到標籤目錄: {test_label_dir}")

        with torch.no_grad():
            for image_num, img_path in test_loop:
                filename = img_path.name

                # 尋找對應的標籤檔案
                label_path = test_label_dir / f"{img_path.stem}.txt"

                # 讀取原始圖像
                original_img = cv2.imread(str(img_path))
                original_img = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
                h, w = original_img.shape[:2]

                # 進行推理
                results = self.model(
                    source=str(img_path),
                    conf=conf,
                    iou=iou,
                    device=self.device
                )

                result = results[0]

                # ---------- 步驟 1：class_thresholds 過濾 ----------
                if class_thresholds:
                    keep_idx = []
                    default_thr = conf
                    for i, (cls_id, conf_val) in enumerate(
                        zip(result.boxes.cls.cpu().numpy(),
                            result.boxes.conf.cpu().numpy())
                    ):
                        thr = class_thresholds.get(int(cls_id), default_thr)
                        if conf_val >= thr:
                            keep_idx.append(i)
                    if keep_idx:
                        result = result[keep_idx]
                    elif skip_empty:
                        logger.info(f"⚠️ {img_path.name} 無符合門檻目標，已跳過")
                        continue
                # -------------------------------------------------

                # ---------- 步驟 2：display classes 過濾 ----------
                if classes is not None:
                    keep_idx = [
                        i for i, cls_id in enumerate(result.boxes.cls.cpu().numpy())
                        if int(cls_id) in classes
                    ]
                    if keep_idx:
                        result = result[keep_idx]
                    elif skip_empty:
                        logger.info(f"⚠️ {img_path.name} 不含指定顯示類別，已跳過")
                        continue
                # -------------------------------------------------

                # 若經過兩輪過濾仍沒有結果
                if skip_empty and len(result.boxes) == 0:
                    logger.info(f"⚠️ {img_path.name} 無任何偵測，已跳過保存")
                    continue

                # 獲取分割掩碼 (如果有)
                pred_mask = None
                if hasattr(result, 'masks') and result.masks is not None and len(result.boxes) > 0:
                    # 將所有掩碼合併為單一分割
                    merged_mask = np.zeros((h, w), dtype=np.int32)

                    # 處理每個檢測結果
                    for i, (mask, box) in enumerate(zip(result.masks.data, result.boxes.data)):
                        class_id = int(box[5]) + 1  # 背景為0，類別從1開始
                        mask_array = mask.cpu().numpy()
                        mask_resized = cv2.resize(mask_array, (w, h)) > 0.5
                        # 用類別ID填充掩碼區域
                        merged_mask[mask_resized] = class_id

                    pred_mask = merged_mask

                # 讀取真實標籤掩碼
                gt_mask = np.zeros((h, w), dtype=np.int32)

                if label_path.exists():
                    with open(label_path, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if len(parts) < 5:
                                continue

                            cls_id = int(parts[0]) + 1  # 背景為0，類別從1開始
                            coords = list(map(float, parts[1:]))

                            # 檢查是否為多邊形標註 (至少有 3 個點，6 個座標)
                            if len(coords) >= 6:
                                # 轉換所有點的座標
                                pts = np.array([
                                    (int(coords[i] * w), int(coords[i+1] * h))
                                    for i in range(0, len(coords), 2)
                                ], dtype=np.int32).reshape(-1, 1, 2)

                                # 填充多邊形區域
                                cv2.fillPoly(
                                    gt_mask, [pts.reshape(-1, 2)], cls_id)
                            else:
                                # 邊界框模式 (前 4 個數為中心點 x,y 和寬高)
                                x_c, y_c, bw, bh = coords[:4]
                                x1 = int((x_c - bw/2) * w)
                                y1 = int((y_c - bh/2) * h)
                                x2 = int((x_c + bw/2) * w)
                                y2 = int((y_c + bh/2) * h)

                                # 填充矩形區域
                                cv2.rectangle(gt_mask, (x1, y1),
                                              (x2, y2), cls_id, -1)

                # 如果存在預測掩碼，則更新指標
                if pred_mask is not None:
                    # 更新整體評估的混淆矩陣
                    overall_metrics.update(
                        gt_mask[np.newaxis, ...], pred_mask[np.newaxis, ...])

                    # 收集所有展平的真實標籤和預測標籤
                    all_gt_flattened.append(gt_mask.flatten())
                    all_pred_flattened.append(pred_mask.flatten())

                # 保存預測結果
                save_path = predict_dir / filename
                result.save(filename=str(save_path))

                # 更新進度條
                test_loop.set_postfix(
                    Progress=f"{image_num+1}/{len(image_paths)}"
                )
                clear_output(wait=True)

        # 計算統計資訊
        duration = time.time() - start_time
        fps = len(image_paths) / duration if duration > 0 else 0

        # 如果有收集到指標數據
        if all_gt_flattened and all_pred_flattened:
            # 將所有展平的標籤連接起來進行整體評估
            all_gt_flattened = np.concatenate(all_gt_flattened)
            all_pred_flattened = np.concatenate(all_pred_flattened)

            # 計算整體的準確率和 Jaccard 分數
            acc_s, js_s = get_metrics(all_gt_flattened, all_pred_flattened)

            # 獲取整體評估的指標分數
            score = overall_metrics.get_scores()

            # 添加準確率和 Jaccard 分數到結果中
            score["acc"] = acc_s
            score["js"] = js_s

            logger.info("\n整體評估指標:")

            # 打印混淆矩陣
            logger.info("混淆矩陣:")
            logger.info(f"{score['confusion_matrix']}")

            # 顯示總體指標
            logger.info("\n總體指標:")
            logger.info(f"F1: {score['F1']:.4f}")
            logger.info(f"MIoU: {score['MIoU']:.4f}")
            logger.info(f"Pixel_Accuracy: {score['Pixel_Accuracy']:.4f}")
            logger.info(f"Specificity: {score['Specificity']:.4f}")
            logger.info(f"Sensitivity: {score['Sensitivity']:.4f}")
            logger.info(f"Precision: {score['Precision']:.4f}")
            logger.info(f"Accuracy: {score['acc']:.4f}")
            logger.info(f"Jaccard_Score: {score['js']:.4f}")

            # 列印每個類別的指標
            logger.info("\n每個類別的指標:")
            for class_idx in range(n_classes):
                class_name = class_names.get(class_idx, f"類別 {class_idx}")
                logger.info(f"{class_name}:")
                logger.info(f"  F1: {score['F1_per_class'][class_idx]:.4f}")
                logger.info(f"  IoU: {score['IoU'][class_idx]:.4f}")
                logger.info(
                    f"  Dice: {score['Dice_per_class'][class_idx]:.4f}")
                logger.info(
                    f"  Precision: {score['Precision_per_class'][class_idx]:.4f}")
                logger.info(
                    f"  Sensitivity: {score['Sensitivity_per_class'][class_idx]:.4f}")
                logger.info(
                    f"  Specificity: {score['Specificity_per_class'][class_idx]:.4f}")

            # 計算非背景類別的平均指標
            non_bg_indices = list(range(1, n_classes))
            if non_bg_indices:  # 確保有非背景類別
                non_bg_f1 = np.mean(score['F1_per_class'][non_bg_indices])
                non_bg_iou = np.mean(score['IoU'][non_bg_indices])
                non_bg_dice = np.mean(score['Dice_per_class'][non_bg_indices])
                non_bg_precision = np.mean(
                    score['Precision_per_class'][non_bg_indices])
                non_bg_sensitivity = np.mean(
                    score['Sensitivity_per_class'][non_bg_indices])

                logger.info("\n非背景類別的平均指標:")
                logger.info(f"  F1: {non_bg_f1:.4f}")
                logger.info(f"  IoU: {non_bg_iou:.4f}")
                logger.info(f"  Dice: {non_bg_dice:.4f}")
                logger.info(f"  Precision: {non_bg_precision:.4f}")
                logger.info(f"  Sensitivity: {non_bg_sensitivity:.4f}")

            # 可視化每個類別的F1分數
            plt.figure(figsize=(10, 6))
            class_names_list = [class_names.get(
                i, f"類別 {i}") for i in range(n_classes)]
            plt.bar(class_names_list, score['F1_per_class'])
            plt.title('各類別F1分數')
            plt.ylabel('F1分數')
            plt.xlabel('類別')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'class_f1_scores.png'))
            plt.show()

            # 可視化混淆矩陣
            plt.figure(figsize=(10, 8))
            confusion_matrix = score['confusion_matrix']

            # 計算每行的總和，用於歸一化
            row_sums = confusion_matrix.sum(axis=1)
            normalized_cm = confusion_matrix / row_sums[:, np.newaxis]

            plt.imshow(normalized_cm, interpolation='nearest',
                       cmap=plt.cm.Blues)
            plt.title('歸一化混淆矩陣')
            plt.colorbar()
            tick_marks = np.arange(n_classes)
            plt.xticks(tick_marks, class_names_list, rotation=45, ha='right')
            plt.yticks(tick_marks, class_names_list)

            # 在混淆矩陣中顯示數值
            thresh = normalized_cm.max() / 2.
            for i in range(normalized_cm.shape[0]):
                for j in range(normalized_cm.shape[1]):
                    plt.text(j, i, f"{normalized_cm[i, j]:.2f}",
                             horizontalalignment="center",
                             color="white" if normalized_cm[i, j] > thresh else "black")

            plt.ylabel('真實標籤')
            plt.xlabel('預測標籤')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
            plt.show()

            logger.info(f"全部處理完成! 共 {len(image_paths)} 張圖像")
            logger.info(f"總耗時: {duration:.2f} 秒，平均 {fps:.2f} FPS")

            return score

# 設定參數
yolo_model_path = r"D:\4_road_crack\best.pt"
test_dir = r'D:\image\5_test_image\test'
output_dir = r"./new_2569"

# 類別名稱定義
class_names = {0: 'expansion_joint', 1: 'joint',
               2: 'linear_crack', 3: 'Alligator_crack',
               4: 'potholes', 5: 'patch', 6: 'manhole',
               7: 'deformation', 8: 'dirt', 9: 'lane_line_linear',
               }
# 初始化測試器
yolo_tester = YOLOSegmentTest(model_path=yolo_model_path)

# 定義每類別自訂信心門檻
class_thresholds = {}

# 定義要顯示的類別
classes_to_display = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9,]  # 只顯示裂縫、坑洞和補綻

# 運行測試
results = yolo_tester.yolo_test(
    test_dir=test_dir,
    output_dir=output_dir,
    class_names=class_names,
    conf=0.1,            # 初始置信度閾值 (較低)
    iou=0.1,             # NMS IoU 閾值
    n_classes=len(class_names) + 1,  # +1 是為了背景類別
    class_thresholds=class_thresholds,  # 每類別自訂信心門檻
    classes=classes_to_display,        # 只顯示這些類別
    skip_empty=True                    # 跳過沒有檢測結果的圖像
)

print("測試完成!")
print(f"總共處理 {results['total_images']} 張圖像")
print(f"總耗時: {results['duration']:.2f} 秒")
print(f"平均處理速度: {results['fps']:.2f} FPS")
print(f"結果保存於: {results['output_dir']}")