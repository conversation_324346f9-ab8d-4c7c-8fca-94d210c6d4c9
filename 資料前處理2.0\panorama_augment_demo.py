#!/usr/bin/env python3
"""
全景圖像擴增工具示例腳本
展示各種擴增方法的使用方式
"""

import os
import json
import numpy as np
import cv2
import pandas as pd
from panorama_augmenter import PanoramaAugmenter, setup_logging


def create_sample_labelme(image_path: str, width: int, height: int) -> dict:
    """創建示例LabelMe標籤文件"""
    return {
        "version": "5.0.1",
        "flags": {},
        "shapes": [
            {
                "label": "building",
                "points": [
                    [width * 0.2, height * 0.3],
                    [width * 0.4, height * 0.3],
                    [width * 0.4, height * 0.7],
                    [width * 0.2, height * 0.7]
                ],
                "group_id": None,
                "shape_type": "polygon",
                "flags": {}
            },
            {
                "label": "car",
                "points": [
                    [width * 0.6, height * 0.5],
                    [width * 0.8, height * 0.5],
                    [width * 0.8, height * 0.6],
                    [width * 0.6, height * 0.6]
                ],
                "group_id": None,
                "shape_type": "polygon",
                "flags": {}
            }
        ],
        "imagePath": os.path.basename(image_path),
        "imageData": None,
        "imageHeight": height,
        "imageWidth": width
    }


def demo_single_image_processing():
    """示例：單張圖像處理"""
    print("\n=== 單張圖像處理示例 ===")
    
    # 設置日誌
    logger = setup_logging("demo_single.log")
    
    # 創建擴增器
    augmenter = PanoramaAugmenter(logger)
    
    # 假設有一張全景圖像
    sample_image_path = "sample_panorama.jpg"
    sample_label_path = ''
    # sample_label_path = "sample_panorama.json"
    
    # 如果沒有實際圖像，創建一個示例
    if not os.path.exists(sample_image_path):
        print("創建示例圖像...")
        # 創建一個2:1比例的示例圖像
        width, height = 3600, 1800
        sample_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # 添加一些幾何圖案作為示例
        cv2.rectangle(sample_image, (100, 100), (500, 400), (255, 0, 0), -1)
        cv2.circle(sample_image, (1800, 900), 200, (0, 255, 0), -1)
        cv2.rectangle(sample_image, (2800, 1200), (3200, 1600), (0, 0, 255), -1)
        
        cv2.imwrite(sample_image_path, sample_image)
        
        # 創建對應的標籤文件
        sample_label = create_sample_labelme(sample_image_path, width, height)
        with open(sample_label_path, 'w', encoding='utf-8') as f:
            json.dump(sample_label, f, ensure_ascii=False, indent=2)
        
        print(f"已創建示例圖像: {sample_image_path}")
        print(f"已創建示例標籤: {sample_label_path}")
    
    # 外方位參數（示例值）
    omega, phi, kappa = 310322.4199240926536731, 2772117.4666974092833698, 11.1590312892970545
    
    # 執行不同的擴增方法
    print("\n執行各種擴增方法...")
    
    try:
        # 1. 旋轉擴增
        results = augmenter.process_single_image(
            image_path=sample_image_path,
            label_path=sample_label_path,
            orientation=(omega, phi, kappa),
            methods=['rotation'],
            output_dir="demo_output/single"
        )
        print(f"旋轉擴增完成，生成 {len(results.get('rotation', []))} 張圖像")
        
        # 2. 視角變換擴增
        results = augmenter.process_single_image(
            image_path=sample_image_path,
            label_path=sample_label_path,
            methods=['perspective'],
            output_dir="demo_output/single"
        )
        print(f"視角變換擴增完成，生成 {len(results.get('perspective', []))} 張圖像")
        
        # 3. 傾斜擴增
        results = augmenter.process_single_image(
            image_path=sample_image_path,
            label_path=sample_label_path,
            methods=['tilt'],
            output_dir="demo_output/single"
        )
        print(f"傾斜擴增完成，生成 {len(results.get('tilt', []))} 張圖像")
        
        # 4. 多角度擴增
        results = augmenter.process_single_image(
            image_path=sample_image_path,
            label_path=sample_label_path,
            methods=['multi_angle'],
            output_dir="demo_output/single"
        )
        print(f"多角度擴增完成，生成 {len(results.get('multi_angle', []))} 張圖像")
        
    except Exception as e:
        print(f"處理時發生錯誤: {e}")


def demo_batch_processing():
    """示例：批量處理"""
    print("\n=== 批量處理示例 ===")
    
    # 設置日誌
    logger = setup_logging("demo_batch.log")
    
    # 創建擴增器
    augmenter = PanoramaAugmenter(logger)
    
    # 創建示例數據集
    input_dir = "demo_input"
    os.makedirs(input_dir, exist_ok=True)
    
    # 創建多張示例圖像
    width, height = 3600, 1800
    image_files = []
    
    for i in range(3):
        # 創建不同的示例圖像
        image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # 添加不同的幾何圖案
        if i == 0:
            cv2.rectangle(image, (200, 200), (800, 600), (255, 100, 100), -1)
        elif i == 1:
            cv2.circle(image, (1800, 900), 300, (100, 255, 100), -1)
        else:
            cv2.ellipse(image, (2700, 1200), (400, 200), 45, 0, 360, (100, 100, 255), -1)
        
        # 保存圖像
        image_filename = f"sample_{i:03d}.jpg"
        image_path = os.path.join(input_dir, image_filename)
        cv2.imwrite(image_path, image)
        image_files.append(image_filename)
        
        # 創建對應的標籤
        label = create_sample_labelme(image_filename, width, height)
        label_filename = f"sample_{i:03d}.json"
        label_path = os.path.join(input_dir, label_filename)
        with open(label_path, 'w', encoding='utf-8') as f:
            json.dump(label, f, ensure_ascii=False, indent=2)
    
    print(f"已創建 {len(image_files)} 張示例圖像在 {input_dir}")
    
    # 創建Excel外方位文件
    excel_path = "demo_orientations.xlsx"
    orientations = [
        {"image_name": "sample_000.jpg", "omega": -74.77, "phi": -86.70, "kappa": -165.42},
        {"image_name": "sample_001.jpg", "omega": -12.34, "phi": 45.67, "kappa": 89.12},
        {"image_name": "sample_002.jpg", "omega": 23.45, "phi": -12.34, "kappa": 156.78}
    ]
    
    df = pd.DataFrame(orientations)
    df.to_excel(excel_path, index=False)
    print(f"已創建外方位Excel文件: {excel_path}")
    
    # 執行批量處理
    try:
        print("\n開始批量處理...")
        stats = augmenter.process_batch(
            input_dir=input_dir,
            output_dir="demo_output/batch",
            excel_path=excel_path,
            methods=['rotation', 'perspective']
        )
        
        print(f"批量處理完成:")
        print(f"  處理圖像數: {stats['processed_images']}")
        print(f"  生成擴增圖像數: {stats['total_augmented']}")
        
    except Exception as e:
        print(f"批量處理時發生錯誤: {e}")


def demo_command_line_usage():
    """示例：命令行使用方式"""
    print("\n=== 命令行使用示例 ===")
    
    print("1. 單張圖像處理:")
    print("python panorama_augmenter.py \\")
    print("    --input sample_panorama.jpg \\")
    print("    --label sample_panorama.json \\")
    print("    --output output_dir \\")
    print("    --omega -74.77 --phi -86.70 --kappa -165.42 \\")
    print("    --methods rotation perspective tilt")
    
    print("\n2. 批量處理:")
    print("python panorama_augmenter.py \\")
    print("    --input input_directory \\")
    print("    --output output_directory \\")
    print("    --excel orientations.xlsx \\")
    print("    --methods rotation perspective \\")
    print("    --batch")
    
    print("\n3. 交互式模式:")
    print("python panorama_augmenter.py \\")
    print("    --output output_directory \\")
    print("    --interactive")


def demo_programmatic_usage():
    """示例：程序化使用方式"""
    print("\n=== 程序化使用示例 ===")
    
    example_code = '''
from panorama_augmenter import PanoramaAugmenter
import cv2
import json

# 創建擴增器
augmenter = PanoramaAugmenter()

# 讀取圖像和標籤
image = cv2.imread("panorama.jpg")
with open("labels.json", "r") as f:
    labelme_data = json.load(f)

# 方法1: 旋轉擴增
rotation_results = augmenter.augment_rotation(
    image, labelme_data, 
    omega=-74.77, phi=-86.70, kappa=-165.42,
    num_variations=5
)

# 方法2: 視角變換擴增
perspective_results = augmenter.augment_perspective(
    image, labelme_data, num_variations=5
)

# 方法3: 傾斜擴增
tilt_results = augmenter.augment_tilt(
    image, labelme_data, max_tilt=20.0, num_variations=5
)

# 方法4: 多角度擴增
multi_angle_results = augmenter.augment_multi_angle(
    image, labelme_data, angle_step=45.0
)

# 保存結果
for i, (aug_image, aug_label) in enumerate(rotation_results):
    cv2.imwrite(f"aug_rotation_{i}.jpg", aug_image)
    with open(f"aug_rotation_{i}.json", "w") as f:
        json.dump(aug_label, f, ensure_ascii=False, indent=2)
'''
    
    print(example_code)


def main():
    """主函數"""
    print("全景圖像擴增工具示例")
    print("=" * 50)
    
    # 創建輸出目錄
    os.makedirs("demo_output", exist_ok=True)
    
    # 執行各種示例
    demo_single_image_processing()
    demo_batch_processing()
    demo_command_line_usage()
    demo_programmatic_usage()
    
    print("\n" + "=" * 50)
    print("所有示例執行完成！")
    print("查看生成的文件：")
    print("- demo_output/single/ : 單張處理結果")
    print("- demo_output/batch/  : 批量處理結果")
    print("- demo_input/         : 示例輸入數據")
    print("- demo_orientations.xlsx : 外方位Excel文件")
    print("- *.log               : 日誌文件")


if __name__ == "__main__":
    main()