"""
優化的訓練函數模組 - 包含現代深度學習最佳實踐
支援混合精度訓練、梯度累積、早停機制等功能
"""

import torch
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
from tqdm import tqdm
import logging
import numpy as np
import os
import matplotlib.pyplot as plt
import time
import pandas as pd
import csv
import cv2
import random
import gc
import glob
import warnings
from typing import Optional, Dict, List, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

# 設置日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Training')

# 設置matplotlib
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class TrainingConfig:
    """訓練配置類"""
    mixed_precision: bool = True
    gradient_accumulation_steps: int = 1
    max_grad_norm: float = 1.0
    early_stopping_patience: int = 10
    early_stopping_min_delta: float = 1e-4
    checkpoint_frequency: int = 5
    log_frequency: int = 10
    memory_cleanup_frequency: int = 50
    
class EarlyStopping:
    """早停機制"""
    def __init__(self, patience: int = 10, min_delta: float = 1e-4, mode: str = 'min'):
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        
        if mode == 'min':
            self.compare = lambda a, b: a < b - min_delta
        else:
            self.compare = lambda a, b: a > b + min_delta
    
    def __call__(self, score: float) -> bool:
        if self.best_score is None:
            self.best_score = score
        elif self.compare(score, self.best_score):
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        return self.early_stop

class TrainingMetrics:
    """訓練指標收集器"""
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.losses = []
        self.loss_components = {
            'total': [],
            'ce': [],
            'dice': [],
            'focal': [],
            'boundary': []
        }
        self.learning_rates = []
        self.batch_times = []
        
    def update(self, loss: float, loss_components: Dict = None, lr: float = None, batch_time: float = None):
        self.losses.append(loss)
        if loss_components:
            for key, value in loss_components.items():
                if key in self.loss_components:
                    self.loss_components[key].append(value)
        if lr is not None:
            self.learning_rates.append(lr)
        if batch_time is not None:
            self.batch_times.append(batch_time)
    
    def get_averages(self) -> Dict:
        return {
            'avg_loss': np.mean(self.losses) if self.losses else 0.0,
            'avg_lr': np.mean(self.learning_rates) if self.learning_rates else 0.0,
            'avg_batch_time': np.mean(self.batch_times) if self.batch_times else 0.0,
            'loss_components': {k: np.mean(v) if v else 0.0 for k, v in self.loss_components.items()}
        }

def train_epoch(
    train_loader,
    model,
    optimizer,
    scheduler,
    device,
    epoch: int,
    total_epochs: int,
    config: TrainingConfig = None,
    loss_fn = None,
    scaler: Optional[GradScaler] = None
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, TrainingMetrics]:
    """
    優化的訓練函數
    
    Args:
        train_loader: 訓練資料加載器
        model: 模型
        optimizer: 優化器
        scheduler: 學習率調度器
        device: 計算設備
        epoch: 當前epoch
        total_epochs: 總epoch數
        config: 訓練配置
        loss_fn: 損失函數
        scaler: 混合精度縮放器
        
    Returns:
        最後一個batch的圖像、標籤、預測和訓練指標
    """
    if config is None:
        config = TrainingConfig()
    
    if scaler is None and config.mixed_precision:
        scaler = GradScaler()
    
    model.train()
    metrics = TrainingMetrics()
    
    train_loop = tqdm(enumerate(train_loader), total=len(train_loader))
    
    # 初始化梯度累積
    optimizer.zero_grad()
    
    for batch_idx, (images, labels, filenames) in train_loop:
        batch_start_time = time.time()
        
        # 數據移至設備
        images = images.to(device, non_blocking=True)
        labels = labels.to(device, non_blocking=True)
        
        # 混合精度訓練
        if config.mixed_precision and scaler is not None:
            with autocast():
                pred = model(images)
                loss = compute_loss(pred, labels, loss_fn, device)
                # 梯度累積
                loss = loss / config.gradient_accumulation_steps
            
            # 反向傳播
            scaler.scale(loss).backward()
            
            # 梯度累積步驟
            if (batch_idx + 1) % config.gradient_accumulation_steps == 0:
                # 梯度裁剪
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), config.max_grad_norm)
                
                # 優化器步驟
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()
                
                # 學習率調度
                if scheduler is not None:
                    scheduler.step()
        else:
            # 標準精度訓練
            pred = model(images)
            loss = compute_loss(pred, labels, loss_fn, device)
            loss = loss / config.gradient_accumulation_steps
            
            loss.backward()
            
            if (batch_idx + 1) % config.gradient_accumulation_steps == 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), config.max_grad_norm)
                optimizer.step()
                optimizer.zero_grad()
                
                if scheduler is not None:
                    scheduler.step()
        
        # 計算預測類別
        _, preds = torch.max(pred, dim=1)
        
        # 記錄指標
        batch_time = time.time() - batch_start_time
        current_lr = optimizer.param_groups[0]['lr']
        
        # 獲取損失組件（如果可用）
        loss_components = {}
        if loss_fn and hasattr(loss_fn, 'get_loss_components'):
            try:
                loss_components = loss_fn.get_loss_components(pred, labels)
            except Exception as e:
                logger.warning(f"無法獲取損失組件: {e}")
        
        metrics.update(
            loss=loss.item() * config.gradient_accumulation_steps,
            loss_components=loss_components,
            lr=current_lr,
            batch_time=batch_time
        )
        
        # 更新進度條
        if batch_idx % config.log_frequency == 0:
            train_loop.set_description(f'Epoch [{epoch+1:0>4d}/{total_epochs:0>4d}]')
            train_loop.set_postfix(
                Loss=f"{metrics.losses[-1]:.4f}",
                LR=f"{current_lr:.2e}",
                Time=f"{batch_time:.2f}s"
            )
        
        # 記憶體清理
        if batch_idx % config.memory_cleanup_frequency == 0:
            torch.cuda.empty_cache()
            gc.collect()
    
    return images[0], labels[0], preds[0], metrics

def compute_loss(pred, labels, loss_fn, device):
    """計算損失的輔助函數"""
    if loss_fn is not None:
        return loss_fn(pred, labels).to(device)
    else:
        # 預設損失組合
        from model_create.util.losses import cross_entropy2d, DiceLoss
        loss_1 = cross_entropy2d(pred, labels).to(device)
        loss_2 = DiceLoss()(torch.argmax(pred, dim=1), labels).to(device)
        return 0.4 * loss_1 + 0.6 * loss_2

def validate_epoch(
    val_loader,
    model,
    device,
    epoch: int,
    total_epochs: int,
    n_classes: int,
    get_metrics_fn,
    runningScore,
    config: TrainingConfig = None
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, Dict]:
    """
    優化的驗證函數
    """
    if config is None:
        config = TrainingConfig()
    
    model.eval()
    running_metrics_val = runningScore(n_classes=n_classes)
    
    val_loop = tqdm(enumerate(val_loader), total=len(val_loader))
    acc_list = []
    js_list = []
    val_losses = []
    
    with torch.no_grad():
        for batch_idx, (val_images, val_labels, filenames) in val_loop:
            # 數據移至設備
            val_images = val_images.to(device, non_blocking=True)
            val_labels = val_labels.to(device, non_blocking=True)
            
            # 混合精度推理
            if config.mixed_precision:
                with autocast():
                    val_pred = model(val_images)
            else:
                val_pred = model(val_images)
            
            _, preds = torch.max(val_pred, dim=1)
            
            # 轉換為numpy進行指標計算
            pred_np = val_pred.data.max(1)[1].cpu().numpy()
            gt_np = val_labels.data.cpu().numpy()
            
            # 計算損失
            from model_create.util.losses import cross_entropy2d
            loss = cross_entropy2d(val_pred, val_labels).to(device)
            val_losses.append(loss.item())
            
            # 更新指標
            running_metrics_val.update(gt_np, pred_np)
            sh_metrics = get_metrics_fn(gt_np.flatten(), pred_np.flatten())
            acc_list.append(sh_metrics[0])
            js_list.append(sh_metrics[1])
            
            # 更新進度條
            if batch_idx % config.log_frequency == 0:
                val_loop.set_description(f'Validation [{epoch+1:0>4d}/{total_epochs:0>4d}]')
                val_loop.set_postfix(
                    Accuracy=f"{sh_metrics[0]:.4f}",
                    Jaccard=f"{sh_metrics[1]:.4f}",
                    Loss=f"{loss.item():.4f}"
                )
    
    # 獲取最終指標
    score = running_metrics_val.get_scores()
    running_metrics_val.reset()
    
    # 添加平均指標
    score["acc"] = np.mean(acc_list)
    score["js"] = np.mean(js_list)
    score["val_loss"] = np.mean(val_losses)
    
    return val_images[0], val_labels[0], preds[0], score

def save_checkpoint(
    model,
    optimizer,
    scheduler,
    epoch: int,
    best_score: float,
    checkpoint_dir: str,
    filename: str = None
):
    """保存檢查點"""
    if filename is None:
        filename = f'checkpoint_epoch_{epoch:04d}.pth'
    
    checkpoint_path = os.path.join(checkpoint_dir, filename)
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'best_score': best_score,
    }
    
    torch.save(checkpoint, checkpoint_path)
    logger.info(f"檢查點已保存: {checkpoint_path}")
    
    return checkpoint_path

def load_checkpoint(
    model,
    optimizer,
    scheduler,
    checkpoint_path: str
) -> Tuple[int, float]:
    """載入檢查點"""
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    if scheduler and checkpoint['scheduler_state_dict']:
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    epoch = checkpoint['epoch']
    best_score = checkpoint['best_score']
    
    logger.info(f"檢查點已載入: {checkpoint_path}, Epoch: {epoch}, Best Score: {best_score}")
    
    return epoch, best_score

# 保持原有的test和inference函數，但添加配置支持
def test_optimized(
    test_loader,
    model,
    n_classes: int,
    path_img: str,
    device,
    get_metrics_fn,
    runningScore,
    class_names: Dict = None,
    config: TrainingConfig = None
):
    """優化的測試函數，添加混合精度支持"""
    if config is None:
        config = TrainingConfig()
    
    model.eval()
    total_time = 0
    total_frames = 0
    overall_metrics = runningScore(n_classes=n_classes)
    
    all_gt_flattened = []
    all_pred_flattened = []
    
    test_loop = tqdm(enumerate(test_loader), total=len(test_loader))
    
    with torch.no_grad():
        for image_num, (test_images, test_labels, filenames) in test_loop:
            test_images = test_images.to(device, non_blocking=True)
            test_labels = test_labels.to(device, non_blocking=True)
            
            start_time = time.time()
            
            # 混合精度推理
            if config.mixed_precision:
                with autocast():
                    test_pred = model(test_images)
            else:
                test_pred = model(test_images)
                
            end_time = time.time()
            inference_time = end_time - start_time
            total_time += inference_time
            total_frames += test_images.size(0)
            
            _, preds = torch.max(test_pred, dim=1)
            
            pred = test_pred.data.max(1)[1].cpu().numpy()
            gt = test_labels.data.cpu().numpy()
            
            overall_metrics.update(gt, pred)
            all_gt_flattened.append(gt.flatten())
            all_pred_flattened.append(pred.flatten())
            
            test_loop.set_postfix(Progress=f"{image_num+1}/{len(test_loader)}")
    
    # 計算最終指標
    all_gt_flattened = np.concatenate(all_gt_flattened)
    all_pred_flattened = np.concatenate(all_pred_flattened)
    
    acc_s, js_s = get_metrics_fn(all_gt_flattened, all_pred_flattened)
    score = overall_metrics.get_scores()
    score["acc"] = acc_s
    score["js"] = js_s
    
    fps = total_frames / total_time if total_time > 0 else 0
    score["fps"] = fps
    
    # 打印詳細結果（與原函數相同）
    print_detailed_results(score, n_classes, class_names, path_img, fps, total_time, total_frames)
    
    return score

def print_detailed_results(score, n_classes, class_names, path_img, fps, total_time, total_frames):
    """打印詳細的測試結果"""
    print("\n整體評估指標:")
    print("混淆矩陣:")
    print(score['confusion_matrix'])
    
    print("\n總體指標:")
    for metric in ['F1', 'MIoU', 'Pixel_Accuracy', 'Specificity', 'Sensitivity', 'Precision']:
        print(f"{metric}: {score[metric]:.4f}")
    print(f"Accuracy: {score['acc']:.4f}")
    print(f"Jaccard_Score: {score['js']:.4f}")
    print(f"FPS: {fps:.2f}")
    
    # 每個類別的指標
    print("\n每個類別的指標:")
    for class_idx in range(n_classes):
        class_name = class_names.get(class_idx, f"類別 {class_idx}") if class_names else f"類別 {class_idx}"
        print(f"{class_name}:")
        for metric in ['F1_per_class', 'IoU', 'Dice_per_class', 'Precision_per_class', 'Sensitivity_per_class', 'Specificity_per_class']:
            print(f"  {metric.split('_')[0]}: {score[metric][class_idx]:.4f}")
    
    # 保存結果為CSV（與原函數相同的格式）
    save_results_to_csv(score, n_classes, class_names, path_img, fps, total_time, total_frames)

def save_results_to_csv(score, n_classes, class_names, path_img, fps, total_time, total_frames):
    """保存結果到CSV文件"""
    # 1. 總體指標CSV
    overall_metrics_dict = {
        "Metric": ["F1", "MIoU", "Pixel_Accuracy", "Specificity", "Sensitivity", "Precision", "Accuracy", "Jaccard_Score", "FPS"],
        "Value": [
            score['F1'], score['MIoU'], score['Pixel_Accuracy'],
            score['Specificity'], score['Sensitivity'], score['Precision'],
            score['acc'], score['js'], score['fps']
        ]
    }
    overall_df = pd.DataFrame(overall_metrics_dict)
    overall_df.to_csv(os.path.join(path_img, 'overall_metrics.csv'), index=False, encoding='utf_8_sig')
    
    # 2. 各類別指標CSV
    class_metrics = []
    for class_idx in range(n_classes):
        class_name = class_names.get(class_idx, f"類別 {class_idx}") if class_names else f"類別 {class_idx}"
        class_metrics.append({
            "Class": class_name,
            "F1": score['F1_per_class'][class_idx],
            "IoU": score['IoU'][class_idx],
            "Dice": score['Dice_per_class'][class_idx],
            "Precision": score['Precision_per_class'][class_idx],
            "Sensitivity": score['Sensitivity_per_class'][class_idx],
            "Specificity": score['Specificity_per_class'][class_idx]
        })
    class_df = pd.DataFrame(class_metrics)
    class_df.to_csv(os.path.join(path_img, 'class_metrics.csv'), index=False, encoding='utf_8_sig')
    
    print(f"指標已保存至 {path_img} 目錄")