import re
import os
import io
import cv2
import glob
import time
import json
import shutil
import argparse
import numpy as np
import pandas as pd
from PIL import Image
import skimage
import skimage.io
import random
from mrcnn.config import Config
from mrcnn import model as modellib
# from prometheus_client import Counter
import tool as tl
import base64
import threading
from tqdm import tqdm
import sys
sys.modules.pop("lanedet.ops.nms_impl", None)  # 確保不會循環導入
#import lanedet.ops.nms_impl as nms_impl
from lane_detect import LaneDetect
from lanedet.utils.config import Config as cf
from tkinter import Y
import imgviz
from matplotlib import pyplot as plt
import uuid
import glob
import labelme
import argparse
import datetime
import collections
import numpy as np
from Tooltest import *
from mrcnn import utils as utilslib
import sklearn.metrics
from skimage import io, color
try:
    import pycocotools.mask
except ImportError:
    print("Please install pycocotools:\n\n    pip install pycocotools\n")
    sys.exit(1)

# import matplotlib.pyplot as plt

# import copy
# from numpy.lib.function_base import copy
"""
406 是否要改成patch 碰到2條lane再刪除
## 178  243~247  256
存檔改為md 規定格式
"""
'''
if_cut_image=True
cut_image=[2825,500]
RecognitionArea=[1600,2850,100,3660]
change_class=[]
class_ch=["","橫向裂縫","微裂縫","裂縫","直線污漬","點狀污漬","補綻","小補綻","白線裂縫","伸縮縫","路面接縫","變形","龜裂","路邊水泥龜裂","方形人孔","圓形人孔","坑洞"]
class_show=["linear_crack","patch","small_patch","deformation","Alligator_crack","Alligator_crack_sides","potholes"]
class_names=["BG","Horizontal_line_crack","linear_crack_light","linear_crack","dirt","spot_dirt","patch","small_patch","lane_line_linear","expansion_joint","joint","deformation","Alligator_crack","Alligator_crack_sides","manhole_square","manhole_circle","potholes"]
Config.NUM_CLASSES = len(class_names)
Config.DETECTION_MIN_CONFIDENCE = 0.9
Config.IMAGE_META_SIZE = 1 + 3 + 3 + 4 + 1 + Config.NUM_CLASSES
Config.NAME = 'road'
Config.IMAGES_PER_GPU = 1


cfgfile = "./configs/condlane/resnet101_culane.py"
cfg = cf.fromfile(cfgfile)
cfg.load_from = './parameters/condlane_r101_culane.pth'
Lanedetect = LaneDetect(cfg)

MODEL_DIR = os.getcwd()
MODEL_PATH = os.path.join(r"parameters\mask_rcnn_coco_1568_2021_11_9_dataset_08.h5")
model = modellib.MaskRCNN(mode="inference", config=Config(), model_dir=MODEL_DIR)
model.load_weights(MODEL_PATH, by_name=True)
#model.keras_model._make_predict_function()
'''
if_cut_image=True
cut_image=[1275,2525,800]
#RecognitionArea=[1600,2850,100,3660]
#RecognitionArea=[1700,2600,25,3800]
RecognitionArea=[0,3000,0,4000]
change_class={}
#class_ch=["","裂縫","白線裂縫","補綻","小補綻","龜裂","方形人孔","圓形人孔","坑洞","車轍","微裂縫"]
#class_show=["linear_crack","lane_line_linear","patch","small_patch","Alligator_crack","manhole_square","manhole_circle","potholes","rutting","crack_light"]
#class_names=["BG","linear_crack","lane_line_linear","patch","small_patch","Alligator_crack","manhole_square","manhole_circle","potholes","rutting","crack_light"]
#class_ch=["","橫向裂縫","縱向裂縫","微裂縫","白線裂縫","輕度龜裂","重度龜裂","方形補綻","圓形補綻","方形補綻含標線","圓形補綻含標線","坑洞","方形人孔","圓形人孔","制水閥","路邊孔蓋","水溝蓋","車轍","路面接縫","伸縮縫","點狀污漬","直線污漬","變形"]
#class_show=["crack_horizontal","crack_vertical","crack_light","crack_lane_line","alligator_crack_light","alligator_crack_heavy","patch_square","patch_circle","patch_square_lane","patch_circle_lane","potholes","manhole_square","manhole_circle","manhole_water","manhole_sides","manhole_ditch","rutting","joint","expansion_joint","dirt_spot","dirt_linear","deformation"]
#class_names=["BG","crack_horizontal","crack_vertical","crack_light","crack_lane_line","alligator_crack_light","alligator_crack_heavy","patch_square","patch_circle","patch_square_lane","patch_circle_lane","potholes","manhole_square","manhole_circle","manhole_water","manhole_sides","manhole_ditch","rutting","joint","expansion_joint","dirt_spot","dirt_linear","deformation"]
#class_ch=["","橫向裂縫","縱向裂縫","微裂縫","白線裂縫","輕度龜裂","重度龜裂","方形補綻","圓形補綻","方形補綻含標線","圓形補綻含標線","坑洞","方形人孔","圓形人孔","制水閥","小方形孔蓋","路邊孔蓋","水溝蓋","車轍","路面接縫","伸縮縫","點狀污漬","直線污漬","變形"]
#class_show=["crack_horizontal","crack_vertical","crack_light","crack_lane_line","alligator_crack_light","alligator_crack_heavy","patch_square","patch_circle","patch_square_lane","patch_circle_lane","potholes","manhole_square","manhole_circle","manhole_water","manhole_square_small","manhole_sides","manhole_ditch","rutting","joint","expansion_joint","dirt_spot","dirt_linear","deformation"]
#class_names=["BG","crack_horizontal","crack_vertical","crack_light","crack_lane_line","alligator_crack_light","alligator_crack_heavy","patch_square","patch_circle","patch_square_lane","patch_circle_lane","potholes","manhole_square","manhole_circle","manhole_water","manhole_square_small","manhole_sides","manhole_ditch","rutting","joint","expansion_joint","dirt_spot","dirt_linear","deformation"]

#

class_ch=["","縱橫向裂縫","縱向裂縫","微裂縫","白線裂縫","龜裂","重度龜裂","補綻","圓形補綻","方形補綻含標線","圓形補綻含標線","路面坑洞","孔蓋","圓形人孔","制水閥","小方形孔蓋","路邊孔蓋","水溝蓋","車轍","路面接縫","伸縮縫","點狀污漬","直線污漬","變形"]
class_show=["crack_horizontal","alligator_crack_light","patch_square","potholes","manhole_square","rutting"]
class_names=["BG","crack_horizontal","crack_vertical","crack_light","crack_lane_line","alligator_crack_light","alligator_crack_heavy","patch_square","patch_circle","patch_square_lane","patch_circle_lane","potholes","manhole_square","manhole_circle","manhole_water","manhole_square_small","manhole_sides","manhole_ditch","rutting","joint","expansion_joint","dirt_spot","dirt_linear","deformation"]
chg_class=["crack_horizontal:crack_vertical/crack_light","alligator_crack_light:alligator_crack_heavy","patch_square:patch_circle/patch_square_lane/patch_circle_lane","manhole_square:manhole_circle/manhole_water/manhole_square_small"]
if chg_class:

    for j in chg_class:
        value_ = j.strip().split(':')
        change_class.update({class_names.index(value_[0]):[class_names.index(cl) for cl in value_[1].strip().split('/')]})



mapx, mapy = np.load(os.path.join(
            os.getcwd(), './parameters/mtx/xy_mtx_12_22_pick.npy'))  # 裁切固定參數
mtxs_path = './parameters/mtx'
pixcel_translate = './parameters/pixcel_translate.cfg'
branches_parameters = tl.branches_parameters(mtxs_path, pixcel_translate)
pixcel = branches_parameters["pixcel_m"]
mtx = branches_parameters["mtx"]

Config.NUM_CLASSES = len(class_names)
Config.DETECTION_MIN_CONFIDENCE = 0.55
Config.IMAGE_META_SIZE = 1 + 3 + 3 + 4 + 1 + Config.NUM_CLASSES
Config.NAME = 'road'
Config.IMAGES_PER_GPU = 1
cfgfile = "./configs/condlane/resnet101_culane.py"
cfg = cf.fromfile(cfgfile)
cfg.load_from = './parameters/condlane_r101_culane.pth'
Lanedetect = LaneDetect(cfg)
crack_count_all = 0
colors_fixed=[(255,0,0),(0,255,0),(0,0,255),(255,255,0),(255,0,255),(0,255,255)]
class_dict = {name: color for name,color in zip(class_show, colors_fixed)}





photo_pathList = []
ch_labelList = []
areaList = []
LENGTHList = []
WIDTHList = []

def getcolor():
    return (random.randint(0,255),random.randint(0,255),random.randint(0,255))

def get_results(img_path):
    ori_image = skimage.io.imread(img_path)
    image = ori_image[RecognitionArea[0]:RecognitionArea[1],RecognitionArea[2]:RecognitionArea[3]]  # (y,x) [1300:2460,:]

    #adjust image brightness
    #image = lab_adjust(image,1.7)# k>1:brighter, k<1:darker #大概6秒一張

    #output recognitionArea image
    #temp_str = img_path.split(".")[0] + "_cut.jpg"
    #cv2.imwrite(temp_str, image) 

    # plt.subplot(121),plt.imshow(ori_image),plt.title('ori_image')
    # plt.subplot(122),plt.imshow(image),plt.title('image')
    # plt.show()
    results = model.detect([image], verbose=1)  # 檢測結果
    #print("0:::", results[0])
    return ori_image, results[0]



def image_correction( img):
    # 魚眼(裁切) + 正射 校正
    # orimage = copy.deepcopy(img)
    
    img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)[
            1540:2880, 2200:3700]  # 是否改為輸入參數  #[1310:2850,350:1770] # (y,x)
    dst = cv2.warpPerspective(img, mtx, (img.shape[1], img.shape[0]))
    # plt.subplot(131),plt.imshow(orimage),plt.title('Input')
    # plt.subplot(132),plt.imshow(img),plt.title('Input')
    # plt.subplot(133),plt.imshow(dst),plt.title('Output')
    # plt.show()
    return dst



def image_correction_FMO(img):
    # 魚眼(裁切) + 正射 校正
    # orimage = copy.deepcopy(img)
    
    img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)[
            1140:2480, 2200:3700]  # 是否改為輸入參數  #[1310:2850,350:1770] # (y,x)
    dst = cv2.warpPerspective(img, mtx, (img.shape[1], img.shape[0]))
    # plt.subplot(131),plt.imshow(orimage),plt.title('Input')
    # plt.subplot(132),plt.imshow(img),plt.title('Input')
    # plt.subplot(133),plt.imshow(dst),plt.title('Output')
    # plt.show()
    return dst


def lab_adjust(img, k):
    array_adjust = np.repeat(k, 3000*4000).reshape(3000,4000) # k>1:bright, k<1:dark

    image_float = img / 255.0
    image_lab = color.rgb2lab(image_float)
    image_lab_adjusted = np.multiply(image_lab[:,:,0], array_adjust)
    image_lab[:,:,0] = image_lab_adjusted

    image_rgb_adjusted = color.lab2rgb(image_lab)
    array_adjust_rgb = np.repeat(255, 3000*4000).reshape(3000,4000)
    image_rgb_adjusted[:,:,0] = np.multiply(image_rgb_adjusted[:,:,0], array_adjust_rgb)
    image_rgb_adjusted[:,:,1] = np.multiply(image_rgb_adjusted[:,:,1], array_adjust_rgb)
    image_rgb_adjusted[:,:,2] = np.multiply(image_rgb_adjusted[:,:,2], array_adjust_rgb)
    return image_rgb_adjusted

def del_outsidearea(result): #後期的自檢有些要畫紅線, 整個辨識在紅線以上的刪掉, 有壓線的會留著
    del_detect = []
    for i in range(0,len(result["rois"])):
        #print(result["rois"][i])
        if result["rois"][i][2] < 1900: #紅線最後畫在1900
            del_detect.append(i)
    if del_detect:
        del_reverse = sorted(del_detect, reverse = True)
        for i in range(0, len(del_reverse)):
            result["class_ids"] = np.delete(result["class_ids"], del_reverse[i])
            result["scores"] = np.delete(result["scores"], del_reverse[i])
            result["rois"] = np.delete(result["rois"], del_reverse[i], axis = 0)
            result["masks"] = np.delete(result["masks"], del_reverse[i], axis = 2)
    return result

def result_bind(result): #10229
    del_detect = []
    result_tran = np.transpose(result["masks"],(2,1,0))
    if (len(result_tran) > 1):
        for i in range(0,len(result_tran)):
            for j in range(i+1,len(result_tran)):
                if i in del_detect or j in del_detect:
                    continue
                if result["class_ids"][i] in [11,12,13,14,15] and result["class_ids"][j] in [11,12,13,14,15]:
                    sum_mask = 0
                    sum_i = 0
                    sum_j = 0
                    for k in range(0,np.shape(result_tran[i,:,:])[0]):
                        list1 = result_tran[i,k,:]
                        list2 = result_tran[j,k,:]
                        sum_mask += sum(np.array(list1) & np.array(list2))
                        sum_i += sum(np.array(list1))
                        sum_j += sum(np.array(list2))
                    print("i,j:",i,",",j)
                    print(sum_mask)
                    print(sum_i)
                    print(sum_j)
                    if (sum_mask/sum_i > 0.5 or sum_mask/sum_j > 0.5):
                        if (result["scores"][i] > result["scores"][j]):
                            del_detect.append(j)
                        else:
                            del_detect.append(i)

    del_detect = np.unique(del_detect)          
    del_reverse = sorted(del_detect, reverse = True)
    print("del::",del_reverse)        
    for i in range(0, len(del_reverse)):
        result["class_ids"] = np.delete(result["class_ids"], del_reverse[i])
        result["scores"] = np.delete(result["scores"], del_reverse[i])
        result["rois"] = np.delete(result["rois"], del_reverse[i], axis = 0)
        result["masks"] = np.delete(result["masks"], del_reverse[i], axis = 2)
        
    print("boxes:::", result["rois"])
    print("classes:::", result["class_ids"])
    print("scores:::", result["scores"])
    print("masks:::", result["masks"])
    return result

def get_bbx(results):
    boxes, classes = results['rois'],results["class_ids"]
    return boxes, classes

def mask_shape(mask):
    return mask.shape[-1]

def skimage2CV2(image):
    image = image[:, :, ::-1]
    image = np.array(image)
    return image

def apply_mask(image, mask, color, detect_name, en_class,trans_angle,correction_type):
    """apply mask to image"""
    # 一次一種類別畫破損輪廓
    binary = np.array(mask)*255
    binary = binary.astype(np.uint8)
    # 記錄所有輪廓的點                             # 取最外圈         # 所有點
    contours, hierarchy = cv2.findContours(binary,cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)  # cv2.RETR_TREE cv2.RETR_EXTERNAL cv2.CHAIN_APPROX_SIMPLE
    # 破損位置、面積
    crack_detail = []
    save_big_mask = []
    if (len(contours) > 0):  
        for contour in contours:
            # pixel 加回原始位置
            contour = np.add(contour, [[RecognitionArea[2], RecognitionArea[0]]])                    
            # 加入破損面積
            silhouette = np.zeros((image.shape[0], image.shape[1]))
            new_points = contour.astype(int)
            silhouette = cv2.fillPoly(silhouette, [new_points], 255)


            ####校正
            mimg = cv2.remap(silhouette, mapx, mapy, cv2.INTER_LINEAR)[
            1140:2480, 2200:3700]

            mask = cv2.warpPerspective(mimg, mtx['Jilong'], (img.shape[1], img.shape[0]))   #做透視變換
            
 
            
            area = np.sum(mask > 0)*(0.0166**2)  # *比例(m)**2
            print(area)
            # 加入資料，並在最後畫在圖上
            save_big_mask.append(contour)
            print('破壞類型 '+detect_name)
            #面積經計算後小於0.1剃除
            if area < 0.01:#0.1:
                continue
            # 取校正後的長寬
            mask = mask.astype(np.uint8)
            ### 20221226修改 ###
            crack_contours, hierarchy = cv2.findContours(
                mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
            #rect = cv2.boundingRect(crack_contours[0])  # 須為整數
            rect = cv2.boundingRect(mask)  # 須為整數
            crack_x, crack_y, crack_w, crack_h = rect
            ######
            #plt.subplot(121),plt.imshow(silhouette),plt.title('Input')
            #plt.subplot(122),plt.imshow(mask),plt.title('Output')
            #plt.show()
            
 
            
            # cv2.circle(image, (int(crack_x+(crack_w/2)), int(crack_y+(crack_h/2))), 50, (0,0,255), -1)
            # plt.imshow(image)
            # plt.show()
            
            # 校正前破損位置
            ori_rect = cv2.boundingRect(contour.reshape(
                contour.shape[0], contour.shape[2]))  # 須為整數
            ori_crack_x, ori_crack_y, ori_crack_w, ori_crack_h = ori_rect
            
            info = dict()
            info['photo_path'] = append_dict['photo_path']
            info["ch_label"] = detect_name
            info["label"] = en_class
            info["area"] = format(float(area), '.2f')
            info["LENGTH"] = format(crack_h * (0.0166), '.2f')
            info["WIDTH"] = format(crack_w * (0.0166), '.2f')
            info["bbx"] = str(ori_rect)
            crack_detail.append(info)
            print(info)
            

            photo_pathList.append(info['photo_path'])            
            ch_labelList.append(info['ch_label'])
            areaList.append(info['area']) 
            LENGTHList.append(info['LENGTH'])
            WIDTHList.append(info['WIDTH'] )
                      

            image = tl.change_cv2_draw(
                image, detect_name, ori_crack_x, ori_crack_y, 65, color)  # + "  " + str(area)
            # 因破損太小被剃除，結果此類別沒有畫上破損
        if not save_big_mask:
            return image, crack_detail
        image = cv2.drawContours(
                image, save_big_mask, -1, color, 4)  # 最後一個參數為輪廓粗細                 
    return image,crack_detail

def del_line_on_line_patch(image,merge):      
    sulhouette = dict()
    image = skimage2CV2(image)
    sulhouette = Lanedetect.run(image)
    # 有找到標線
    if sulhouette:
        # 迴圈取範圍
        sulhouette["all_lane"] = sulhouette["all_lane"][RecognitionArea[0]:RecognitionArea[1],RecognitionArea[2]:RecognitionArea[3]]
        for i in sulhouette["sig_lane"]:
            sulhouette["sig_lane"][i] = sulhouette["sig_lane"][i][RecognitionArea[0]:RecognitionArea[1],RecognitionArea[2]:RecognitionArea[3]]
            # plt.subplot(2,2,i+1),plt.imshow(sulhouette["sig_lane"][i],"gray"),plt.title(str(i))
        # plt.show()
        # 改成判斷所有

    # 若有補綻標線黑底增加補綻輪廓
    patch_contours = None
    if "patch_square" in merge:
        # patch 輪廓點
        patch_mask = merge["patch_square"]["mask"]
        patch_mask = np.array(patch_mask)*255
        patch_mask = patch_mask.astype(np.uint8) # 轉為8yte
        patch_contours, hierarchy = cv2.findContours(patch_mask,cv2.RETR_TREE,cv2.CHAIN_APPROX_NONE) # cv2.CHAIN_APPROX_SIMPLE

    if "linear_crack" in merge and ("patch_square" in merge or sulhouette):
        # sulhouette
        if not sulhouette:
            sulhouette = np.zeros((merge["linear_crack"]["mask"].shape[0], merge["linear_crack"]["mask"].shape[1]))
            patch_sulhouette = cv2.drawContours(sulhouette,patch_contours,-1,255,4) # 最後一個參數為輪廓粗細
        else:
            ## 去除與補綻、標線重疊的裂縫  
            patch_sulhouette = cv2.drawContours(sulhouette["all_lane"],patch_contours,-1,255,4) # 最後一個參數為輪廓粗細
            # plt.imshow(patch_sulhouette,"gray")
            # plt.show()
            # cv2.waitKey()

        # linear_crack 輪廓
        linear_crack_mask = merge["linear_crack"]["mask"]
        linear_crack_mask = np.array(linear_crack_mask)*255
        linear_crack_mask = linear_crack_mask.astype(np.uint8) # 轉為8yte
        linear_crack_contours, hierarchy = cv2.findContours(linear_crack_mask,cv2.RETR_TREE,cv2.CHAIN_APPROX_NONE) # cv2.CHAIN_APPROX_SIMPLE
        # plt.imshow(linear_crack_mask,"binary")
        # plt.show()
        if (len(linear_crack_contours) >0): # 解決點太小無法出現邊框問題
            # 對一個bbox 中每一個 contour 做比較，通常只會有一個contour
            for i in range(0,len(linear_crack_contours)):
                if cv2.contourArea(linear_crack_contours[i])>6000:
                    # 取最小矩形
                    np_point = linear_crack_contours[i].reshape(linear_crack_contours[i].shape[0],linear_crack_contours[i].shape[2])
                    rect = cv2.boundingRect(np_point) # 須為整數
                    x,y,w,h = rect
                    ## (1) Crop the bounding rect  #### https://www.coder.work/article/1249069
                    croped = patch_sulhouette[y:y+h, x:x+w].copy()
                    ## (2) make mask
                    np_point = np_point - np_point.min(axis=0)
                    mask = np.zeros(croped.shape[:2], np.uint8)
                    cv2.drawContours(mask, [np_point], -1, (255, 255, 255), -1, cv2.LINE_AA)
                    ## (3) do bit-op
                    dst = cv2.bitwise_and(croped, croped, mask=mask)
                    # 如果有重疊，用mask 將那個破損去除
                    if cv2.countNonZero(dst) != 0:
                        merge["linear_crack"]["mask"][y:y+h, x:x+w] = np.where(mask >0, False, merge["linear_crack"]["mask"][y:y+h, x:x+w])
                        # plt.imshow(merge["linear_crack"]["mask"])
                        # plt.show()
                        # cv2.waitKey(0)

    if "patch_square" in merge and type(sulhouette)==dict:
        ## 比較patch 是否接觸左右標線
        if len(sulhouette["sig_lane"])>=2 and (len(patch_contours) >0): # 有>=2條標線 # 解決點太小無法出現邊框問題
            # 對一個bbox 中每一個 contour 做比較，通常只會有一個contour
            for i in range(0,len(patch_contours)):
                if cv2.contourArea(patch_contours[i])>6000:
                    # 取最小矩形
                    np_point = patch_contours[i].reshape(patch_contours[i].shape[0],patch_contours[i].shape[2])
                    rect = cv2.boundingRect(np_point) # 須為整數
                    x,y,w,h = rect
                    # 用patch 的範圍看是否有lane
                    overlap_num = 0
                    for i in sulhouette["sig_lane"]:
                        ## (1) Crop the bounding rect  #### https://www.coder.work/article/1249069
                        croped = sulhouette["sig_lane"][i][y:y+h, x:x+w].copy()
                        ## (2) make mask
                        np_point = np_point - np_point.min(axis=0)
                        mask = np.zeros(croped.shape[:2], np.uint8)
                        cv2.drawContours(mask, [np_point], -1, (255, 255, 255), -1, cv2.LINE_AA)
                        ## (3) do bit-op
                        dst = cv2.bitwise_and(croped, croped, mask=mask)
                        if cv2.countNonZero(dst) != 0:
                            overlap_num = overlap_num + 1
                            if overlap_num ==2:
                                merge["patch_square"]["mask"][y:y+h, x:x+w] = np.where(mask >0, False, merge["patch_square"]["mask"][y:y+h, x:x+w])
                                break
    return merge

def get_merge_mask(masks,boxes,classes,n_instances,scores):
        merge = dict()
        for i in range(n_instances):
            if not np.any(boxes[i]):
                continue
            if not class_names[classes[i]] in class_show:
                continue
            if if_cut_image==True:
                #masks[ cut_image[2]:, cut_image[0]:cut_image[1], i] = np.where(masks[cut_image[2]:, cut_image[0]:cut_image[1], i] == True, False, masks[ cut_image[2]:,  cut_image[0]:cut_image[1], i])
                masks[:,:,i] = np.where(cut_mask == 1, False, masks[:,:,i])
                if np.sum(masks[:,:,i]) == 0:
                    print("無mask")
                    continue
            if class_names[classes[i]] not in merge:
                merge[class_names[classes[i]]] = {}
                merge[class_names[classes[i]]]["mask"] = masks[:, :, i]
                merge[class_names[classes[i]]]["scores"] = masks[:, :, i]
                merge[class_names[classes[i]]]["scores"] = np.where(masks[:, :, i] == 1, scores[i], 0)
                # merge[self.class_names[classes[i]]]["boxes"] = []
                # merge[self.class_names[classes[i]]]["boxes"].append(boxes[i])
                # merge[self.class_names[classes[i]]]["scores"] = str(self.scores[i])
            else:
                merge[class_names[classes[i]]]["mask"] = np.where(
                    masks[:, :, i] == 1, 1, merge[class_names[classes[i]]]["mask"])
                merge[class_names[classes[i]]]["scores"] = np.where(masks[:, :, i] == 1, scores[i], merge[class_names[classes[i]]]["scores"])
                # merge[self.class_names[classes[i]]]["boxes"].append(boxes[i])
                # merge[self.class_names[classes[i]]]["scores"] = str(self.scores[i])+"  "+str(merge[self.class_names[classes[i]]]["scores"])
        #print("merge")
        #print(merge)
        return merge


def del_merge_overlap(merge): #10229  
    #裂縫跟龜裂有重疊的, 裂縫的範圍也改成龜裂
    if "crack_horizontal" in merge and "alligator_crack_light" in merge:
        merge["all"] = {}
        #取得所有跟龜裂重疊的裂縫的分數
        crack_to_alli_list = np.unique(merge["crack_horizontal"]["scores"][np.where((merge["crack_horizontal"]["scores"] + merge["alligator_crack_light"]["scores"]) > 1)]) #有跟龜裂重疊的裂縫
        if crack_to_alli_list.any():
            for i in crack_to_alli_list:
                merge["alligator_crack_light"]["mask"] = np.where(merge["crack_horizontal"]["scores"] == i, 1, merge["alligator_crack_light"]["mask"])
                merge["crack_horizontal"]["mask"] = np.where(merge["crack_horizontal"]["scores"] == i, 0, merge["crack_horizontal"]["mask"])
        merge["all"]["scores"] = merge["crack_horizontal"]["scores"] + merge["alligator_crack_light"]["scores"]
        overlap_list = np.unique(merge["all"]["scores"][np.where(merge["all"]["scores"] > 1)])
        for i in overlap_list:
            merge["crack_horizontal"]["temp"] = np.where(merge["all"]["scores"] == i, merge["crack_horizontal"]["scores"], 0)
            merge["alligator_crack_light"]["temp"] = np.where(merge["all"]["scores"] == i, merge["alligator_crack_light"]["scores"], 0)    
            if (merge["alligator_crack_light"]["temp"] > merge["crack_horizontal"]["temp"]).any():
                merge["all"]["scores"] = np.where(merge["all"]["scores"] == i, merge["alligator_crack_light"]["temp"], merge["all"]["scores"])
            else:
                merge["all"]["scores"] = np.where(merge["all"]["scores"] == i, merge["crack_horizontal"]["temp"], merge["all"]["scores"])
    
    other_classes = ["crack_horizontal","alligator_crack_light","patch_square","manhole_square","rutting","potholes"]
    done_classes = []
    for class_name in other_classes:
        if "all" in merge and class_name == "crack_horizontal":
            done_classes.append(class_name)
            continue
        elif "all" in merge and class_name == "alligator_crack_light":
            done_classes.append(class_name)
            continue
        else:
            if "all" not in merge:
                if class_name in merge:
                    merge["all"] = {}
                    merge["all"]["scores"] = merge[class_name]["scores"]
                done_classes.append(class_name)
            else:
                if class_name in merge:
                    merge["all"]["scores"] = merge["all"]["scores"] + merge[class_name]["scores"]
                    overlap_list = np.unique(merge["all"]["scores"][np.where(merge["all"]["scores"] > 1)])
                    for i in overlap_list:
                        merge["all"]["temp"] = np.where(merge["all"]["scores"] == i, merge["all"]["scores"], 0)
                        merge[class_name]["temp"] = np.where(merge["all"]["scores"] == i, merge[class_name]["scores"], 0)    
                        if (merge["all"]["temp"] > merge[class_name]["temp"]).any():
                            merge["all"]["scores"] = np.where(merge["all"]["scores"] == i, merge["all"]["temp"], merge["all"]["scores"])
                            merge[class_name]["mask"] = np.where(merge["all"]["scores"] == i, 0, merge[class_name]["mask"])
                        else:
                            merge["all"]["scores"] = np.where(merge["all"]["scores"] == i, merge[class_name]["temp"], merge["all"]["scores"])    
                            for done_class in done_classes:
                                merge[done_class]["mask"] = np.where(merge["all"]["scores"] == i, 0, merge[done_class]["mask"])
                done_classes.append(class_name)
    del merge["all"]
    return merge


def display_instances( image,results,trans_angle,correction_type):
    masks,scores = results["masks"],results["scores"]
    boxes, classes = get_bbx(results)
    n_instances = boxes.shape[0]
    masked_img = False  # 判斷是否有 masked
    if not n_instances:
        print("\n*** No instances to display *** \n")
    else:
        assert boxes.shape[0] == mask_shape(masks) == classes.shape[0]
    # 合併相近類別 橫向裂縫 = 裂縫  小補綻 = 補綻
    for i in change_class:
        classes = [i if value in change_class[i] else value for value in classes]

    #print("mask:::", masks)
    #print("boxes:::", boxes)
    #print("classes:::", classes)
    #print("instances:::", n_instances)
    #print("scores:::", scores)
    merge = get_merge_mask(masks,boxes,classes,n_instances,scores)
    crack_data = []
    if not merge:
        with open(os.path.join(save_dir , r"record_result.txt"), "a", encoding = "utf-8") as f:
            f.write("0\n")
            f.close()
        return masked_img,image,crack_data
    # 去除補綻裂縫、標線裂縫，去除與左右標線相交的大補綻
    
    '''
    if "linear_crack" in merge or "patch" in merge:
    '''
    if "patch_square" in merge:
        merge = del_line_on_line_patch(image,merge)
        # 檢查是否有要輸出的破損
        merge_del = []
        for i in merge:
            if np.sum(merge[i]["mask"]) == 0:
                print("已無mask")
                merge_del.append(i)
        if merge_del:
            for i in merge_del:
                del merge[i]
        if not merge:
            with open(os.path.join(save_dir , r"record_result.txt"), "a", encoding = "utf-8") as f:
                f.write("0\n")
                f.close()
            return masked_img,image,crack_data
    
    merge = del_merge_overlap(merge) #10229

    photo_count = 0
    for i in merge:
            # 輸出顯示類別
        image, crack_detail = apply_mask(image,
                                                  mask=merge[i]["mask"],
                                                  color=class_dict[i],
                                                  detect_name=class_ch[class_names.index(
                                                      i)],
                                                  en_class=i,
                                                  trans_angle=trans_angle,
                                                  correction_type=correction_type
                                                  )
        crack_data = crack_data + crack_detail
        # 有一個破損，就有masked img
        #print("len(crack_detail)")
        print(len(crack_detail))
        global crack_count_all
        crack_count_all += len(crack_detail)
        photo_count += len(crack_detail)
        if crack_detail:
            masked_img = True

    with open(os.path.join(save_dir , r"record_result.txt"), "a", encoding = "utf-8") as f:
        f.write(str(photo_count)+"\n")
        f.close()
    
    '''
    for i in range(len(classes)):
        if not class_names[classes[i]] in class_show: 
            continue
        score = str(round(scores[i],3))
        font_x = boxes[i][1] - 75
        font_y = boxes[i][0] + RecognitionArea[0]
        image = tl.change_cv2_draw(image, score, font_x, font_y, 65, [255,0,0])
    '''
    
    if masked_img == True:
        image = skimage2CV2(image)
    return masked_img,image,crack_data
########模型路徑
MODEL_DIR = os.getcwd()
path_str = r"parameters\mask_rcnn_coco_20230721_crop25141_1440.h5"
#path_str = r"parameters\mask_rcnn_coco_20221223_8800_1374.h5"
#path_str = r"parameters\mask_rcnn_coco_20230721_crop25141_1440.h5"
#path_str = r"parameters\mask_rcnn_coco_20230721_crop25141_1440.h5"
#mask_rcnn_coco_20221006_5000_1100.h5
#mask_rcnn_coco_20221017_5750_1296.h5
#mask_rcnn_coco_20221029_6300_1229.h5
#mask_rcnn_coco_20221017_5750_1200.h5
#mask_rcnn_coco_20221110_8000_1077.h5 
#mask_rcnn_coco_20221110_8000_1282.h5 #車轍比之前的模型好?
#mask_rcnn_coco_20221121_8000_1054.h5
#mask_rcnn_coco_20221121_8000_1144.h5
#mask_rcnn_coco_20221121_8000_1304.h5
#mask_rcnn_coco_20221202_8000_1294.h5
#mask_rcnn_coco_20221202_8000_1287.h5
#mask_rcnn_coco_20221202_8000_1367.h5
#mask_rcnn_coco_20221223_8800_1313.h5
#mask_rcnn_coco_20221223_8800_1374.h5 #管線挖掘的補綻可以辨識到一部份
#mask_rcnn_coco_20221223_8800_1426.h5
#mask_rcnn_coco_20221223_8800_1470.h5
#mask_rcnn_coco_20221223_8800_1486.h5
#mask_rcnn_coco_20230119_9377_1313.h5
#mask_rcnn_coco_20230202_10000_1335.h5
#mask_rcnn_coco_20230202_10000_1359.h5 #孔蓋很爛, 但抓得到很淺的裂縫龜裂(會多辨識, 多辨識的有些很奇怪),  長條的補綻抓得到(會多抓一些莫名奇妙的)
#mask_rcnn_coco_20230202_10000_1371.h5
#mask_rcnn_coco_20230202_10000_1481.h5
#mask_rcnn_coco_20230217_10000_1417.h5
#mask_rcnn_coco_20230217_10000_1509.h5
#mask_rcnn_coco_20230217_10000_1624.h5
#mask_rcnn_coco_20230217_10000_1692.h5
#mask_rcnn_coco_20230306_10000_0266.h5
#mask_rcnn_coco_20230306_10000_0326.h5
#mask_rcnn_coco_20230306_10000_0373.h5
#mask_rcnn_coco_20230306_10000_0424.h5
#mask_rcnn_coco_20230306_10000_0524.h5
#mask_rcnn_coco_20230306_10000_0567.h5
#mask_rcnn_coco_20230306_10000_0792.h5
#mask_rcnn_coco_20230306_10000_0952.h5
#mask_rcnn_coco_20230306_10000_0988.h5
#mask_rcnn_coco_20230306_10000_1020.h5
#mask_rcnn_coco_20230306_10000_1081.h5
#mask_rcnn_coco_20230306_10000_1195.h5
#mask_rcnn_coco_20230328_10000_0486.h5
#mask_rcnn_coco_20230407_10000_0565.h5 #MEAN_PIXEL = np.array([87.0, 83.7, 80.2])
#mask_rcnn_coco_20230407_10000_0624.h5 #MEAN_PIXEL = np.array([87.0, 83.7, 80.2])
#mask_rcnn_coco_20230407_10000_0790.h5 #MEAN_PIXEL = np.array([87.0, 83.7, 80.2])
MODEL_PATH = os.path.join(path_str)
model = modellib.MaskRCNN(mode="inference", config=Config(), model_dir=MODEL_DIR)
model.load_weights(MODEL_PATH, by_name=True)

test_cfg = './test_path.cfg'
fdict = tl.ReadConfig(test_cfg)
test_dir = fdict['testdata_path'][0]
save_dir = fdict['saveresult_path'][0] + time.strftime("%Y%m%d%H%M", time.localtime())
record_result = fdict['record_result'][0]
cut_mask=cv2.imread(fdict['cut_mask'][0],cv2.IMREAD_GRAYSCALE)[RecognitionArea[0]:RecognitionArea[1],RecognitionArea[2]:RecognitionArea[3]]

os.makedirs(save_dir, exist_ok=True)

label_list = []
pred_list = []
label_list_per = []
pred_list_per = []
correct_count_all = 0
data = dict()
append_dict = dict()
if record_result:
    with open(r"parameters\detect_print.json", 'r') as f:
        data = json.loads(f.read())
    with open(os.path.join(save_dir , r"record_result.json"), 'w', encoding = "utf-8") as f:
        json.dump(data, f)

test_list = os.listdir(test_dir)
for test_name in tqdm(test_list):
    if test_name.endswith("jpg"):
        label_list_per = []
        pred_list_per = []
        append_dict.clear()
        print("\ntest_name ",test_name)
        append_dict['photo_path'] = test_name
        #append_dict.setdefault('photo_path', test_name)

        with open(os.path.join(save_dir , r"record_result.txt"), "a", encoding = "utf-8") as f:
            f.write(test_name+",")
            f.close()

        img, result = get_results(os.path.join(test_dir, test_name))
        #result = result_bind(result) #10229
        result = del_outsidearea(result)


        append_dict['pred_ids'] = result["class_ids"].tolist()
        append_dict['pred_scores'] = result["scores"].tolist()
        #print(result)

        masked_img, image, crack_detail = display_instances(img, result,0,0)
        if masked_img == True:
            #cv2.line(image, (RecognitionArea[2],1600), (RecognitionArea[3],1600), (0,255,0), 1)
            #cv2.line(image, (RecognitionArea[2],1750), (RecognitionArea[3],1750), (0,255,0), 1)
            #cv2.line(image, (RecognitionArea[2],1850), (RecognitionArea[3],1850), (0,255,0), 1)
            #cv2.line(image, (RecognitionArea[2],RecognitionArea[0]), (3710,RecognitionArea[0]), (0,0,255), 1)
            cv2.imwrite(os.path.join(save_dir,test_name), image) 
 
        
        if os.path.exists(os.path.join(test_dir,test_name.replace("jpg","json"))):
            class_name_to_id = {}
            for i, class_name in enumerate(class_names):
                class_id = i # - 1  # starts with -1
                # # class_name = line.strip()
                # if class_id == -1:
                #     assert class_name == "__ignore__"
                #     continue
                class_name_to_id[class_name] = class_id
            test_label = os.path.join(test_dir,test_name.replace("jpg","json"))
            label_file = labelme.LabelFile(filename=test_label)
            if label_file.shapes == []:
                continue
            img = labelme.utils.img_data_to_arr(label_file.imageData)
            masks_label ={}
            segmentations = collections.defaultdict(list)  # for segmentation
            for shape in label_file.shapes:
                if shape["label"] in class_names:   #　確定有此類別
                    points = shape["points"]
                    label = shape["label"]
                    group_id = shape.get("group_id")
                    shape_type = shape.get("shape_type", "polygon")
                    mask = labelme.utils.shape_to_mask(
                        img.shape[:2], points, shape_type
                    )

                    if group_id is None:
                        group_id = uuid.uuid1()

                    instance = (label, group_id)

                    if instance in masks_label:
                        masks_label[instance] = masks_label[instance] | mask
                    else:
                        masks_label[instance] = mask

                    if shape_type == "rectangle":
                        (x1, y1), (x2, y2) = points
                        x1, x2 = sorted([x1, x2])
                        y1, y2 = sorted([y1, y2])
                        points = [x1, y1, x2, y1, x2, y2, x1, y2]
                    if shape_type == "circle":
                        (x1, y1), (x2, y2) = points
                        r = np.linalg.norm([x2 - x1, y2 - y1])
                        # r(1-cos(a/2))<x, a=2*pi/N => N>pi/arccos(1-x/r)
                        # x: tolerance of the gap between the arc and the line segment
                        n_points_circle = max(int(np.pi / np.arccos(1 - 1 / r)), 12)
                        i = np.arange(n_points_circle)
                        x = x1 + r * np.sin(2 * np.pi / n_points_circle * i)
                        y = y1 + r * np.cos(2 * np.pi / n_points_circle * i)
                        points = np.stack((x, y), axis=1).flatten().tolist()
                    else:
                        points = np.asarray(points).flatten().tolist()

                    segmentations[instance].append(points)
            segmentations = dict(segmentations)

            gt_label = {}
            for instance, mask in masks_label.items():
                cls_name, group_id = instance
                if cls_name not in class_name_to_id:
                    continue
                cls_id = class_name_to_id[cls_name]              
                
                mask2 = np.asfortranarray(mask.astype(np.uint8))
                mask2 = pycocotools.mask.encode(mask2)
                area = float(pycocotools.mask.area(mask2))
                bbox = pycocotools.mask.toBbox(mask2).flatten().tolist()

                if not gt_label:
                    gt_label = {
                        "gt_boxes":[bbox],
                        "gt_class_ids":[cls_id],
                        "gt_masks":[mask.tolist()],
                    }
                else:
                    gt_label["gt_boxes"].append(bbox)
                    gt_label["gt_class_ids"].append(cls_id)
                    gt_label["gt_masks"].extend([mask.tolist()])
            print("gt_final_shape:", np.array(gt_label["gt_masks"]).shape)
            print("result_shape::", np.array(result["masks"]).shape)
            print("gt_ids:", gt_label["gt_class_ids"])
            print("pred_ids:", np.array(result["class_ids"]))

            reshape_gt_masks = np.transpose(gt_label["gt_masks"], (1,2,0))
            reshape_gt_masks = reshape_gt_masks[RecognitionArea[0]:RecognitionArea[1], RecognitionArea[2]:RecognitionArea[3],:]
            print("after_reshape_gt", np.array(reshape_gt_masks).shape)
            
            gt_match, pred_match, overlaps = utilslib.compute_matches(
                    np.array(gt_label["gt_boxes"]), np.array(gt_label["gt_class_ids"]), np.array(reshape_gt_masks),
                    np.array(result["rois"]), np.array(result["class_ids"]), result["scores"], np.array(result["masks"]),
                    iou_threshold=0.5, score_threshold=0.0)
            print("gt_match:::", gt_match)
            print("pred_match:::", pred_match)
            print("overlaps:::\n", overlaps)
            correct_count_all += len(np.where(pred_match > -1)[0])

            #for confusion matrix
            gt_read = -1 * np.ones(len(gt_match))
            pred_read = -1 * np.ones(len(pred_match))

            #find all gt_match != -1 first, then gt_match == -1
            for i in range(len(gt_match)):
                if gt_match[i] != -1:
                    label_list.append(gt_label["gt_class_ids"][i])
                    label_list_per.append(gt_label["gt_class_ids"][i])
                    pred_list.append(result["class_ids"][int(gt_match[i])])
                    pred_list_per.append(result["class_ids"][int(gt_match[i])])
                    gt_read[i] = 1
                    pred_read[int(gt_match[i])] = 1
            for i in range(len(gt_match)):    
                if gt_match[i] == -1:
                    #IOU > 0.5, wrong predict id
                    if overlaps[:,i].size != 0:
                        if max(overlaps[:,i]) > 0.5:
                            max_col_list = overlaps[:,i].argsort()
                            while(1):
                                max_col = max_col_list[-1]
                                if pred_read[max_col] < 0:
                                    label_list.append(gt_label["gt_class_ids"][i])
                                    label_list_per.append(gt_label["gt_class_ids"][i])
                                    pred_list.append(result["class_ids"][max_col])
                                    pred_list_per.append(result["class_ids"][max_col])
                                    gt_read[i] = 1
                                    pred_read[max_col] = 1
                                    break
                                else:
                                    max_col_list.pop()
                        else:
                            label_list.append(gt_label["gt_class_ids"][i])
                            label_list_per.append(gt_label["gt_class_ids"][i])
                            pred_list.append(-1)
                            pred_list_per.append(-1)
                            gt_read[i] = 1
                    #did not detect
                    else:
                        label_list.append(gt_label["gt_class_ids"][i])
                        label_list_per.append(gt_label["gt_class_ids"][i])
                        pred_list.append(-1)
                        pred_list_per.append(-1)
                        gt_read[i] = 1

            #pred_match == -1 and pred_read == -1
            for i in range(len(result["class_ids"])):
                if pred_match[i] == -1 and pred_read[i] == -1:
                    label_list.append(-1)
                    label_list_per.append(-1)
                    pred_list.append(result["class_ids"][i])
                    pred_list_per.append(result["class_ids"][i])
                    pred_read[i] = 1           
            
            append_dict['gt_ids'] = gt_label["gt_class_ids"]
            append_dict['gt_match'] = gt_match.tolist()
            append_dict['pred_match'] = pred_match.tolist()
            append_dict['gt_list'] = np.array(label_list_per).tolist()
            append_dict['pred_list'] = np.array(pred_list_per).tolist()
            append_dict['overlaps'] = overlaps.tolist()
      
    if record_result:   
        with open(os.path.join(save_dir, r"record_result.json"), 'r') as f:
            data = json.loads(f.read())  
            data["photo_result"].append(append_dict)
        with open(os.path.join(save_dir, r"record_result.json"), 'w', encoding="utf-8") as f:
            json.dump(data, f) 

####2024/1/5
"""
    for crack in crack_detail:
        print(crack["ch_label"])
"""

            
labels = list(range(1, len(class_names)))
labels.append(-1)

print("\n標籤破壞個數:", len(np.where(np.array(label_list) > -1)[0]))
print("辨識破壞個數: ", crack_count_all)
print("正確分類個數: ", correct_count_all)

if label_list:
    print("label_list", label_list)
    print("pred_list", pred_list)
    print("confusion_matrix::")
    print(sklearn.metrics.confusion_matrix(label_list, pred_list, labels=labels))

if record_result:
    with open(os.path.join(save_dir, r"record_result.json"), 'r') as f:
        data = json.loads(f.read())
        data['class_names'] = class_names
        data['detection_min_confidence'] = Config.DETECTION_MIN_CONFIDENCE
        data['model'] = path_str
        data['confusion_matrix'] = sklearn.metrics.confusion_matrix(label_list, pred_list, labels=labels).tolist()
        data['label_count'] = len(np.where(np.array(label_list) > -1)[0])
        data['pred_count'] = crack_count_all
        data['match_count'] = correct_count_all
    
    with open(os.path.join(save_dir, r"record_result.json"), 'w', encoding="utf-8") as f:
        json.dump(data, f)

#2024/1/10
df = pd.DataFrame({"圖片": photo_pathList, "破壞": ch_labelList, "面積": areaList, "長": LENGTHList, "寬": WIDTHList})
df.to_csv(f"{save_dir}record_result.csv", encoding="big5")



"""
photo_pathList 
ch_labelList 
areaList 
LENGTHList 
WIDTHList 
"""

#img,result=get_results(r"D:\高雄AI訓練\dataset\test_0728\Camera_0_20191226_150506_693.jpg")
#print(result)

#masked_img, image, crack_detail = display_instances(img, result)
# 存圖
#if masked_img == True:
#    cv2.imwrite("666ew.jpg", image)
#img=skimage2CV2(img)
#masked_img, image, crack_detail = self.display_instances(image, results)
#cv2.imwrite("test23.jpg",img)



