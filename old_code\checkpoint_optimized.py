"""
優化的檢查點管理模組
提供安全、靈活的模型檢查點保存和載入功能
"""

import os
import torch
import glob
import re
import logging
import json
from typing import Optional, Dict, Any, Tuple, Union
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class CheckpointManager:
    """
    檢查點管理器 - 提供完整的檢查點管理功能
    """
    
    def __init__(self, checkpoint_dir: str, max_checkpoints: int = 5, auto_resume: bool = True):
        """
        初始化檢查點管理器
        
        Args:
            checkpoint_dir: 檢查點目錄
            max_checkpoints: 保留的最大檢查點數量
            auto_resume: 是否自動恢復最新檢查點
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.max_checkpoints = max_checkpoints
        self.auto_resume = auto_resume
        
        # 檢查點元資料
        self.metadata_file = self.checkpoint_dir / "checkpoint_metadata.json"
        self.metadata = self._load_metadata()
        
    def _load_metadata(self) -> Dict[str, Any]:
        """載入檢查點元資料"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"無法載入檢查點元資料: {e}")
        
        return {
            "created_at": datetime.now().isoformat(),
            "checkpoints": [],
            "best_checkpoint": None,
            "last_checkpoint": None
        }
    
    def _save_metadata(self):
        """保存檢查點元資料"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"無法保存檢查點元資料: {e}")
    
    def save_checkpoint(
        self,
        model: torch.nn.Module,
        optimizer: torch.optim.Optimizer,
        scheduler: Optional[torch.optim.lr_scheduler._LRScheduler],
        epoch: int,
        metrics: Dict[str, float],
        is_best: bool = False,
        extra_state: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        保存檢查點
        
        Args:
            model: 模型
            optimizer: 優化器
            scheduler: 學習率調度器
            epoch: 當前epoch
            metrics: 評估指標
            is_best: 是否為最佳模型
            extra_state: 額外狀態信息
            
        Returns:
            檢查點文件路徑
        """
        try:
            # 創建檢查點數據
            checkpoint_data = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
                'metrics': metrics,
                'timestamp': datetime.now().isoformat(),
                'extra_state': extra_state or {}
            }
            
            # 生成檢查點文件名
            primary_metric = metrics.get('val_loss', metrics.get('F1', 0))
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if is_best:
                filename = f"best_epoch{epoch:04d}_metric{primary_metric:.4f}_{timestamp}.pth"
            else:
                filename = f"checkpoint_epoch{epoch:04d}_metric{primary_metric:.4f}_{timestamp}.pth"
            
            checkpoint_path = self.checkpoint_dir / filename
            
            # 保存檢查點
            torch.save(checkpoint_data, checkpoint_path)
            
            # 更新元資料
            checkpoint_info = {
                "filename": filename,
                "epoch": epoch,
                "metrics": metrics,
                "timestamp": checkpoint_data['timestamp'],
                "is_best": is_best,
                "file_size": checkpoint_path.stat().st_size
            }
            
            self.metadata["checkpoints"].append(checkpoint_info)
            self.metadata["last_checkpoint"] = filename
            
            if is_best:
                self.metadata["best_checkpoint"] = filename
            
            # 清理舊檢查點
            self._cleanup_old_checkpoints()
            
            # 保存元資料
            self._save_metadata()
            
            logger.info(f"檢查點已保存: {checkpoint_path}")
            return str(checkpoint_path)
            
        except Exception as e:
            logger.error(f"保存檢查點時發生錯誤: {e}")
            raise
    
    def load_checkpoint(
        self,
        model: torch.nn.Module,
        optimizer: Optional[torch.optim.Optimizer] = None,
        scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
        checkpoint_path: Optional[str] = None,
        load_best: bool = True,
        device: Optional[torch.device] = None
    ) -> Tuple[int, Dict[str, float], Dict[str, Any]]:
        """
        載入檢查點
        
        Args:
            model: 模型
            optimizer: 優化器（可選）
            scheduler: 學習率調度器（可選）
            checkpoint_path: 指定檢查點路徑（可選）
            load_best: 是否載入最佳檢查點
            device: 目標設備
            
        Returns:
            (epoch, metrics, extra_state)
        """
        try:
            # 確定要載入的檢查點
            if checkpoint_path is None:
                if load_best and self.metadata.get("best_checkpoint"):
                    checkpoint_path = self.checkpoint_dir / self.metadata["best_checkpoint"]
                elif self.metadata.get("last_checkpoint"):
                    checkpoint_path = self.checkpoint_dir / self.metadata["last_checkpoint"]
                else:
                    # 尋找最新的檢查點文件
                    checkpoint_path = self.find_latest_checkpoint()
            
            if checkpoint_path is None or not Path(checkpoint_path).exists():
                logger.warning("未找到可用的檢查點，從頭開始訓練")
                return 0, {}, {}
            
            # 自動檢測設備
            if device is None:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
            # 載入檢查點
            logger.info(f"載入檢查點: {checkpoint_path}")
            checkpoint_data = torch.load(checkpoint_path, map_location=device)
            
            # 驗證檢查點數據
            self._validate_checkpoint(checkpoint_data)
            
            # 載入模型狀態
            model.load_state_dict(checkpoint_data['model_state_dict'])
            
            # 載入優化器狀態
            if optimizer and 'optimizer_state_dict' in checkpoint_data:
                optimizer.load_state_dict(checkpoint_data['optimizer_state_dict'])
            
            # 載入調度器狀態
            if scheduler and checkpoint_data.get('scheduler_state_dict'):
                scheduler.load_state_dict(checkpoint_data['scheduler_state_dict'])
            
            epoch = checkpoint_data.get('epoch', 0)
            metrics = checkpoint_data.get('metrics', {})
            extra_state = checkpoint_data.get('extra_state', {})
            
            logger.info(f"成功載入檢查點 - Epoch: {epoch}, Metrics: {metrics}")
            return epoch, metrics, extra_state
            
        except Exception as e:
            logger.error(f"載入檢查點時發生錯誤: {e}")
            raise
    
    def find_latest_checkpoint(self, model_name: Optional[str] = None) -> Optional[str]:
        """
        尋找最新的檢查點文件
        
        Args:
            model_name: 模型名稱過濾器
            
        Returns:
            最新檢查點的路徑
        """
        try:
            pattern = "*.pth"
            if model_name:
                pattern = f"{model_name}_*.pth"
            
            checkpoint_files = list(self.checkpoint_dir.glob(pattern))
            
            if not checkpoint_files:
                return None
            
            # 按修改時間排序，最新的在最後
            latest_checkpoint = max(checkpoint_files, key=lambda p: p.stat().st_mtime)
            return str(latest_checkpoint)
            
        except Exception as e:
            logger.error(f"尋找檢查點時發生錯誤: {e}")
            return None
    
    def _validate_checkpoint(self, checkpoint_data: Dict[str, Any]):
        """驗證檢查點數據完整性"""
        required_keys = ['model_state_dict']
        for key in required_keys:
            if key not in checkpoint_data:
                raise ValueError(f"檢查點缺少必要字段: {key}")
    
    def _cleanup_old_checkpoints(self):
        """清理舊的檢查點文件"""
        try:
            if len(self.metadata["checkpoints"]) <= self.max_checkpoints:
                return
            
            # 按時間排序，保留最新的
            sorted_checkpoints = sorted(
                self.metadata["checkpoints"],
                key=lambda x: x["timestamp"]
            )
            
            # 計算需要刪除的檢查點
            to_delete = sorted_checkpoints[:-self.max_checkpoints]
            
            for checkpoint_info in to_delete:
                # 保護最佳檢查點
                if checkpoint_info.get("is_best", False):
                    continue
                
                file_path = self.checkpoint_dir / checkpoint_info["filename"]
                if file_path.exists():
                    file_path.unlink()
                    logger.info(f"已刪除舊檢查點: {file_path}")
                
                self.metadata["checkpoints"].remove(checkpoint_info)
                
        except Exception as e:
            logger.error(f"清理檢查點時發生錯誤: {e}")
    
    def list_checkpoints(self) -> list:
        """列出所有可用的檢查點"""
        return self.metadata.get("checkpoints", [])
    
    def get_best_checkpoint_path(self) -> Optional[str]:
        """獲取最佳檢查點路徑"""
        best_filename = self.metadata.get("best_checkpoint")
        if best_filename:
            return str(self.checkpoint_dir / best_filename)
        return None


# 兼容性函數 - 保持與原始API的兼容性
def find_latest_checkpoint(path_total: str, modelname: str) -> Optional[str]:
    """
    向後兼容的函數 - 尋找最新檢查點
    
    Args:
        path_total: 檢查點目錄
        modelname: 模型名稱
        
    Returns:
        最新檢查點目錄路徑
    """
    try:
        pattern = os.path.join(path_total, f"{modelname}_*")
        matching_dirs = glob.glob(pattern)
        
        if not matching_dirs:
            return None
        
        latest_dir = max(matching_dirs, key=os.path.getctime)
        return latest_dir
        
    except Exception as e:
        logger.error(f"尋找檢查點時發生錯誤: {e}")
        return None


def load_checkpoint(
    model: torch.nn.Module,
    path_weight: str,
    best: bool = True,
    device: Optional[torch.device] = None
) -> Tuple[torch.nn.Module, int, float]:
    """
    向後兼容的函數 - 載入檢查點
    
    Args:
        model: 模型
        path_weight: 權重目錄路徑
        best: 是否載入最佳模型
        device: 目標設備
        
    Returns:
        (model, start_epoch, best_metric)
    """
    try:
        # 自動檢測設備
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 根據參數決定加載哪種檢查點
        pattern = os.path.join(path_weight, '*_best.pth' if best else '*_last.pth')
        checkpoints = glob.glob(pattern)
        
        if not checkpoints:
            logger.warning("未找到檢查點文件")
            return model, 0, 0
        
        # 選擇最新的檢查點
        latest_checkpoint = max(checkpoints, key=os.path.getctime)
        
        # 載入檢查點
        logger.info(f"載入檢查點: {latest_checkpoint}")
        checkpoint_data = torch.load(latest_checkpoint, map_location=device)
        
        # 處理不同的檢查點格式
        if isinstance(checkpoint_data, dict):
            if 'model_state_dict' in checkpoint_data:
                model.load_state_dict(checkpoint_data['model_state_dict'])
                start_epoch = checkpoint_data.get('epoch', 0)
                best_metric = checkpoint_data.get('metrics', {}).get('F1', 0)
            else:
                # 舊格式，直接是state_dict
                model.load_state_dict(checkpoint_data)
                start_epoch = 0
                best_metric = 0
        else:
            model.load_state_dict(checkpoint_data)
            start_epoch = 0
            best_metric = 0
        
        # 從檔名解析資訊（作為備用）
        if start_epoch == 0 or best_metric == 0:
            filename = os.path.basename(latest_checkpoint)
            epoch_match = re.search(r'epoch(\d+)', filename)
            metric_match = re.search(r'metric(\d+\.\d+)', filename)
            
            if epoch_match:
                start_epoch = int(epoch_match.group(1))
            if metric_match:
                best_metric = float(metric_match.group(1))
        
        logger.info(f"檢查點載入成功 - Epoch: {start_epoch}, Metric: {best_metric}")
        return model, start_epoch, best_metric
        
    except Exception as e:
        logger.error(f"載入檢查點時發生錯誤: {e}")
        return model, 0, 0


# 使用示例
if __name__ == "__main__":
    # 創建檢查點管理器
    checkpoint_manager = CheckpointManager(
        checkpoint_dir="./checkpoints",
        max_checkpoints=5
    )
    
    # 假設有模型和優化器
    # model = YourModel()
    # optimizer = torch.optim.Adam(model.parameters())
    # scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=10)
    
    # 保存檢查點
    # checkpoint_manager.save_checkpoint(
    #     model=model,
    #     optimizer=optimizer,
    #     scheduler=scheduler,
    #     epoch=10,
    #     metrics={'val_loss': 0.5, 'F1': 0.8},
    #     is_best=True
    # )
    
    # 載入檢查點
    # epoch, metrics, extra_state = checkpoint_manager.load_checkpoint(
    #     model=model,
    #     optimizer=optimizer,
    #     scheduler=scheduler,
    #     load_best=True
    # )