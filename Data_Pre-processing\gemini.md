# 專案重構分析與建議

## 總體評估

本專案是一個功能完整的圖像資料集預處理工具鏈，具備良好的模組化基礎。主要功能包括標籤格式轉換、標籤編輯、資料集分割、圖像增強及全景圖像擴增，並提供了一個 PyQt GUI 應用程式。

隨著功能擴展，程式碼在結構、設定管理和資源使用方面出現了可優化的空間。以下提出三個重構方向，旨在提升專案的健壯性、可維護性和可擴展性。

---

## 方向一：建立核心函式庫 (Core Library) 與重構工具類別 (建議選項)

此方向旨在將專案的核心邏輯與執行層（GUI、CLI）徹底分離，是長遠來看效益最高的方案。

-   **核心概念：**
    將所有可重用的業務邏輯（如格式轉換演算法、圖像處理函式、資料增強演算法）抽取出來，形成一個獨立的 `core` 函式庫。目前的各個工具類別 (`AnnotationConverter`, `ImageAugmenter` 等) 將被重構，只保留與該工具相關的特定流程控制邏輯，並呼叫 `core` 函式庫來完成通用任務。

-   **關鍵步驟：**
    1.  **建立 `core` 套件**：建立一個 `core` 資料夾，使其成為一個 Python 套件。
    2.  **定義通用資料結構**：在 `core/structures.py` 中使用 `dataclasses` 或 `pydantic` 定義標準化的資料結構，例如 `Annotation`, `BoundingBox`, `Polygon`, `ImageSample` 等，統一專案內的資料流動格式。
    3.  **抽象化檔案處理**：建立一個 `core/dataset.py` 模組，負責處理資料集的讀取、寫入和遍歷。它可以實現一個迭代器模式，一次只讀取和處理一個樣本，從根本上解決目前部分模組（如 `AnnotationEditor`）一次性載入所有資料可能導致的記憶體瓶頸問題。
    4.  **重構工具類別**：
        *   `AnnotationConverter`：將各種格式的解析邏輯拆分為獨立的純函式，只返回標準化的 `Annotation` 物件列表。檔案的讀寫和遍歷交給 `core/dataset.py` 處理。
        *   `AnnotationEditor` 和 `DatasetDivider`：修改為使用 `core/dataset.py` 的迭代器模式，實現流式處理。
        *   `ImageAugmenter` 和 `PanoramaAugmenter`：將核心的圖像和座標變換演算法移至 `core/augmentations.py`。
    5.  **統一化配置管理**：設計一個統一的設定檔格式（如 YAML），讓 `core` 函式庫、CLI 和 GUI 都能讀取和使用，作為唯一的設定來源 (Single Source of Truth)。

-   **優點：**
    -   **高內聚，低耦合**：核心邏輯與應用層完全分離，職責清晰。
    -   **易於測試**：可以針對 `core` 函式庫中的純函式編寫精確的單元測試。
    -   **可擴展性強**：未來若要支援新的標籤格式或增強演算法，只需在 `core` 中添加新模組，對上層應用影響極小。
    -   **解決記憶體問題**：透過流式處理資料，能應對更大規模的資料集。
    -   **多前端支援**：重構後的 `core` 函式庫可以輕易地被其他前端（如 Web API、新的 CLI 工具）復用。

-   **缺點：**
    -   **重構工作量最大**：需要對現有程式碼進行較大規模的結構性調整。

---

## 方向二：統一為單一命令列工具 (CLI)

此方向專注於改善目前分散的命令列腳本，提供一個統一、現代化的 CLI 入口，大幅簡化使用流程和體驗。

-   **核心概念：**
    棄用 `img_dataset_pipeline.py` 和各個模組中的 `if __name__ == "__main__":` 部分，使用 `Typer` 或 `Click` 等現代 CLI 框架，將所有功能整合到一個主命令下，例如 `datakit convert`, `datakit edit`, `datakit augment` 等。

-   **關鍵步驟：**
    1.  **引入 `Typer` 或 `Click`**：在專案中加入新的依賴。
    2.  **建立 `cli.py`**：作為新的命令列程式統一入口。
    3.  **定義命令結構**：
        *   建立主應用程式 `app = typer.Typer()`。
        *   為每個工具 (convert, edit, divide, augment) 建立清晰的子命令。
    4.  **包裝現有工具類別**：在 `cli.py` 中為每個子命令建立一個對應的函式，該函式負責解析命令列參數，實例化對應的工具類別，並呼叫其執行方法。
    5.  **移除舊的 `argparse`**：刪除所有模組中的 `argparse` 相關程式碼，簡化模組職責。

-   **優點：**
    -   **使用者體驗極佳**：提供清晰、一致且具備自動產生說明文件的 CLI。
    -   **入口統一**：所有功能都從單一命令觸發，易於管理和發現。
    -   **重構範圍適中**：主要修改程式的進入點和參數傳遞方式，對核心類別的內部邏輯改動較小。

-   **缺點：**
    -   未解決 `core` 邏輯分散和記憶體佔用的根本問題。
    -   GUI 與 CLI 的邏輯仍然是分開維護的。

---

## 方向三：GUI 與後端邏輯徹底分離

此方向的重點是強化 `pyqt_gui_application.py` 的穩定性和可維護性，將其與後端工具的執行徹底解耦，為未來的多介面發展打下基礎。

-   **核心概念：**
    將所有工具類別視為一個「後端 API」。GUI 層不直接實例化或呼叫這些類別的方法，而是透過一個明確定義的介面層 (API Layer) 來與之互動，實現前後端分離。

-   **關鍵步驟：**
    1.  **建立 `api.py` 模組**：這個模組將作為 GUI 和後端工具之間的中介層。
    2.  **定義 API 函式**：在 `api.py` 中為每個工具建立啟動函式，例如 `start_conversion_task(params: dict) -> QThread`。此函式負責：
        *   接收從 GUI 傳來的參數字典。
        *   創建對應的 `WorkerThread`（例如 `ConverterWorkerThread`）。
        *   將必要的訊號 (progress, log, finished, error) 連接到 GUI 的插槽。
        *   啟動並返回執行緒，供 GUI 管理。
    3.  **重構 GUI 程式碼**：
        *   GUI 中的各個工具 Widget，在執行時不再自行創建 `WorkerThread`。
        *   它們將收集好的參數傳遞給 `api.py` 中的對應函式，並處理回傳的訊號。
    4.  **確保後端無狀態**：確保工具類別是無狀態的，或每次執行時都創建新的實例，避免多次執行之間產生狀態干擾。

-   **優點：**
    -   **GUI 穩定性提升**：GUI 只負責介面互動和資料展示，與耗時的後端任務完全分離，避免介面凍結。
    -   **職責清晰**：介面、API 層、核心邏輯層次分明。
    -   **便於未來擴展**：如果未來要開發 Web 介面，`api.py` 可以被直接復用，作為 Web API 的基礎。

-   **缺點：**
    -   增加了額外的 API 層，帶來些微的複雜度。
    -   與方向二一樣，未解決 `core` 邏輯分散和記憶體佔用的問題。
