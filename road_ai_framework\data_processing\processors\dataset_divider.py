import os
import json
import shutil
import random
import logging
import copy
import time
import gc
import threading
from pathlib import Path
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Union, Optional, Any, Set
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

# 自定義異常類型


class DividerError(Exception):
    """數據集分割器基本異常類型"""
    pass


class SamplingError(DividerError):
    """樣本收集和選擇錯誤"""
    pass


class SplitError(DividerError):
    """數據集分割錯誤"""
    pass


class DatasetDivider:
    """
    數據集分割器
    將數據集分割為訓練、驗證和測試集，支持類別平衡和批處理
    """

    def __init__(self,
                 input_dir: Union[str, Path],
                 output_dir: Union[str, Path],
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 test_ratio: float = 0.15,
                 class_limits: Optional[Dict[str, int]] = None,
                 logger: Optional[logging.Logger] = None,
                 max_workers: Optional[int] = None):
        """
        初始化分割器

        參數:
            input_dir: 輸入目錄路徑
            output_dir: 輸出目錄
            train_ratio: 訓練集比例
            val_ratio: 驗證集比例
            test_ratio: 測試集比例
            class_limits: 類別數量限制 {類別: 最大數量}
            logger: 日誌記錄器，如果為None則創建新的
            max_workers: 最大並行工作線程數量，None表示根據CPU數量自動決定
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.class_limits = class_limits or {}
        self.logger = logger or logging.getLogger(__name__)
        self.max_workers = max_workers or min(32, os.cpu_count() * 2)

        # 分割名稱
        self.splits = ["train", "val", "test"]

        # 任務類型
        self.tasks = ["object_detect", "segment"]

        # 初始化統計和結果存儲
        self.stats = {
            "samples": {
                "total": 0,
                "train": 0,
                "val": 0,
                "test": 0
            },
            "class_distribution": defaultdict(lambda: {"total": 0, "train": 0, "val": 0, "test": 0})
        }

        # 鎖用於多線程更新統計
        self.stats_lock = threading.Lock()

        # 檢查點文件名
        self.checkpoint_file = ".divider_checkpoint.json"

        # 創建輸出目錄結構
        self._create_directories()

        # 收集到的樣本緩存
        self.collected_samples = []
        self.class_counts = {}

    def run(self, batch_size: int = 50, resume: bool = True) -> Dict[str, Any]:
        """
        執行數據集分割

        參數:
            batch_size: 批處理大小
            resume: 是否從中斷處恢復

        返回:
            分割統計信息
        """
        start_time = time.time()

        # 1. 檢查是否有檢查點
        checkpoint = None
        if resume:
            checkpoint = self._load_checkpoint()
            if checkpoint and "splits" in checkpoint:
                self.logger.info("從檢查點恢復分割結果")
                splits = checkpoint["splits"]
                # 更新統計
                if "stats" in checkpoint:
                    self.stats = checkpoint["stats"]

                # 4. 處理和複製文件，修改為遍歷每個分割
                self.logger.info("處理和複製檔案...")
                for split in self.splits:
                    # 對每個分割處理segment和object_detect兩個任務
                    for task in self.tasks:
                        self._process_split_for_task(
                            splits[split], split, task, batch_size=batch_size)

                # 5. 更新總樣本數
                self.stats["samples"]["total"] = sum(
                    self.stats["samples"][split] for split in self.splits)

                # 6. 生成統計報告
                self._generate_statistics()

                # 7. 驗證分割後的數據集
                self._verify_dataset()

                # 8. 輸出最終統計
                self._print_final_statistics()

                end_time = time.time()
                elapsed_time = end_time - start_time
                self.logger.info(f"分割處理完成，耗時 {elapsed_time:.2f} 秒")

                return self.stats

        # 1. 收集所有樣本
        self.logger.info("收集樣本...")
        samples = self._collect_samples(batch_size=batch_size)
        self.logger.info(f"收集到 {len(samples)} 個樣本")

        # 2. 根據類別限制篩選樣本
        self.logger.info("篩選樣本...")
        filtered_samples = self._filter_samples(samples)
        self.logger.info(f"篩選後剩餘 {len(filtered_samples)} 個樣本")

        # 3. 分割數據集 - 確保樣本一致性
        self.logger.info("分割數據集...")
        splits = self._split_dataset_with_consistency(filtered_samples)

        # 保存檢查點
        self._save_checkpoint(splits)

        # 4. 處理和複製文件，修改為遍歷每個分割
        self.logger.info("處理和複製檔案...")
        for split in self.splits:
            # 對每個分割處理segment和object_detect兩個任務
            for task in self.tasks:
                self._process_split_for_task(
                    splits[split], split, task, batch_size=batch_size)

        # 5. 更新總樣本數
        self.stats["samples"]["total"] = sum(
            self.stats["samples"][split] for split in self.splits)

        # 6. 生成統計報告
        self._generate_statistics()

        # 7. 驗證分割後的數據集
        self._verify_dataset()

        # 8. 輸出最終統計
        self._print_final_statistics()

        # 刪除檢查點
        self._delete_checkpoint()

        end_time = time.time()
        elapsed_time = end_time - start_time
        self.logger.info(f"分割處理完成，耗時 {elapsed_time:.2f} 秒")

        return self.stats

    def _save_checkpoint(self, splits: Dict[str, List[Dict[str, Any]]]) -> None:
        """
        保存處理進度檢查點

        參數:
            splits: 分割數據
        """
        checkpoint_data = {
            "splits": splits,
            "stats": self.stats
        }

        checkpoint_path = self.output_dir / self.checkpoint_file

        try:
            with open(checkpoint_path, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False)

            self.logger.debug(f"已保存檢查點")
        except Exception as e:
            self.logger.warning(f"保存檢查點失敗: {e}")

    def _load_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        加載處理進度檢查點

        返回:
            檢查點數據，如果不存在則返回None
        """
        checkpoint_path = self.output_dir / self.checkpoint_file

        if not checkpoint_path.exists():
            return None

        try:
            with open(checkpoint_path, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)

            self.logger.info(f"已加載檢查點")

            return checkpoint_data
        except Exception as e:
            self.logger.warning(f"加載檢查點失敗: {e}")
            return None

    def _delete_checkpoint(self) -> None:
        """刪除檢查點文件"""
        checkpoint_path = self.output_dir / self.checkpoint_file
        if checkpoint_path.exists():
            try:
                os.remove(checkpoint_path)
                self.logger.info("已刪除檢查點文件")
            except Exception as e:
                self.logger.warning(f"刪除檢查點文件失敗: {e}")

    def _collect_samples(self, batch_size: int = 50) -> List[Dict[str, Any]]:
        """
        從輸入目錄收集樣本

        參數:
            batch_size: 批處理大小

        返回:
            樣本列表
        """
        # 如果已有收集到的樣本，直接返回
        if self.collected_samples:
            return self.collected_samples

        all_samples = []
        class_counts = {}

        # 掃描所有子資料夾作為類別
        class_dirs = list(self.input_dir.iterdir())
        class_dirs = [d for d in class_dirs if d.is_dir()]

        # 遍歷類別目錄
        for i, class_dir in enumerate(class_dirs):
            if not class_dir.is_dir():
                continue

            class_name = class_dir.name
            # 跳過 overlap 資料夾
            if class_name == "overlap":
                self.logger.info(f"跳過 overlap 資料夾")
                continue

            self.logger.info(
                f"從類別 {class_name} 收集樣本 ({i+1}/{len(class_dirs)})")

            # 收集該類別的樣本，現在只處理segment
            class_samples = []

            # 只收集segment task的樣本，後續會同時生成兩種任務的檔案
            if class_name == "multi_class":
                # 對於multi_class資料夾，需要特殊處理
                task_samples = self._collect_multi_class_samples(
                    class_dir, "segment", batch_size)
            else:
                task_samples = self._collect_class_samples(
                    class_dir, class_name, "segment", batch_size)

            class_samples.extend(task_samples)

            class_samples.extend(task_samples)

            self.logger.info(f"從類別 {class_name} 收集到 {len(class_samples)} 個樣本")
            class_counts[class_name] = len(class_samples)
            all_samples.extend(class_samples)

        # 保存類別計數，以便後續使用
        self.class_counts = class_counts
        self.collected_samples = all_samples

        return all_samples

    def _collect_class_samples(self,
                               class_dir: Path,
                               class_name: str,
                               task: str,
                               batch_size: int = 50) -> List[Dict[str, Any]]:
        """
        從單個類別目錄收集樣本，確保只計算真實圖片

        參數:
            class_dir: 類別目錄路徑
            class_name: 類別名稱
            task: 任務類型
            batch_size: 批處理大小

        返回:
            樣本列表
        """
        samples = []

        # 跳過 overlap 資料夾
        if class_dir.name == "overlap":
            return samples

        # 檢查是否是直接存放圖像和標籤的結構
        img_files = []
        img_file_map = {}

        # 收集所有圖片文件並建立映射
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            found_imgs = list(class_dir.glob(f"*{ext}"))
            img_files.extend(found_imgs)
            # 建立圖片stem到圖片路徑的映射
            for img in found_imgs:
                img_file_map[img.stem] = img

        json_files = list(class_dir.glob("*.json"))

        if img_files and json_files:
            # 直接處理當前目錄中的文件
            # 如果文件較少，不使用批處理
            if len(json_files) <= batch_size:
                samples.extend(self._process_json_files(
                    json_files, img_file_map, class_name, task))
            else:
                # 批量處理文件
                for i in range(0, len(json_files), batch_size):
                    batch = json_files[i:i + batch_size]
                    self.logger.info(
                        f"處理類別 {class_name} 批次 {i//batch_size + 1}/{(len(json_files) + batch_size - 1)//batch_size}: {len(batch)} 個檔案")

                    batch_samples = self._process_json_files(
                        batch, img_file_map, class_name, task)
                    samples.extend(batch_samples)

                    # 釋放內存
                    gc.collect()
        else:
            # 檢查傳統目錄結構
            label_dir = class_dir / f"{task}_label" / "labelme_format"

            if not label_dir.exists():
                return samples

            # 圖像目錄
            img_dir = class_dir / "original"

            # 預先建立圖片映射，提高查找效率
            img_file_map = {}
            if img_dir.exists():
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    found_imgs = list(img_dir.glob(f"*{ext}"))
                    for img in found_imgs:
                        img_file_map[img.stem] = img

            json_files = list(label_dir.glob("*.json"))

            # 如果文件較少，不使用批處理
            if len(json_files) <= batch_size:
                samples.extend(self._process_json_files_with_img_dir(
                    json_files, img_dir, img_file_map, class_name, task))
            else:
                # 批量處理文件
                for i in range(0, len(json_files), batch_size):
                    batch = json_files[i:i + batch_size]
                    self.logger.info(
                        f"處理類別 {class_name} 批次 {i//batch_size + 1}/{(len(json_files) + batch_size - 1)//batch_size}: {len(batch)} 個檔案")

                    batch_samples = self._process_json_files_with_img_dir(
                        batch, img_dir, img_file_map, class_name, task)
                    samples.extend(batch_samples)

                    # 釋放內存
                    gc.collect()

        return samples

    def _process_json_files(self,
                            json_files: List[Path],
                            img_file_map: Dict[str, Path],
                            class_name: str,
                            task: str) -> List[Dict[str, Any]]:
        """
        並行處理JSON文件

        參數:
            json_files: JSON文件列表
            img_file_map: 圖片映射
            class_name: 類別名稱
            task: 任務類型

        返回:
            樣本列表
        """
        samples = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for json_file in json_files:
                stem = json_file.stem
                img_file = img_file_map.get(stem)

                if not img_file:
                    continue

                future = executor.submit(
                    self._process_json_file, json_file, img_file, class_name, task
                )
                futures.append(future)

            # 收集結果
            for future in as_completed(futures):
                try:
                    sample = future.result()
                    if sample:
                        samples.append(sample)
                except Exception as e:
                    self.logger.error(f"處理JSON文件時出錯: {e}")

        return samples

    def _process_json_files_with_img_dir(self,
                                         json_files: List[Path],
                                         img_dir: Path,
                                         img_file_map: Dict[str, Path],
                                         class_name: str,
                                         task: str) -> List[Dict[str, Any]]:
        """
        並行處理JSON文件（帶圖片目錄）

        參數:
            json_files: JSON文件列表
            img_dir: 圖片目錄
            img_file_map: 圖片映射
            class_name: 類別名稱
            task: 任務類型

        返回:
            樣本列表
        """
        samples = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for annotation_file in json_files:
                stem = annotation_file.stem

                # 使用映射直接查找對應的圖片
                img_file = img_file_map.get(stem)

                # 如果映射中沒有找到，嘗試傳統方法查找
                if not img_file:
                    img_file = self._find_image_file(img_dir, stem)

                # 如果仍未找到圖片，跳過此標註
                if not img_file:
                    continue

                future = executor.submit(
                    self._process_json_file, annotation_file, img_file, class_name, task
                )
                futures.append(future)

            # 收集結果
            for future in as_completed(futures):
                try:
                    sample = future.result()
                    if sample:
                        samples.append(sample)
                except Exception as e:
                    self.logger.error(f"處理JSON文件時出錯: {e}")

        return samples

    def _process_json_file(self,
                           json_file: Path,
                           img_file: Path,
                           class_name: str,
                           task: str) -> Optional[Dict[str, Any]]:
        """
        處理單個JSON文件

        參數:
            json_file: JSON文件路徑
            img_file: 圖片文件路徑
            class_name: 類別名稱
            task: 任務類型

        返回:
            樣本字典，如果處理失敗則返回None
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)

            # 檢查標註
            shapes_key = "shapes" if "shapes" in annotation_data else "annotations"
            if shapes_key not in annotation_data or not annotation_data[shapes_key]:
                return None

            # 計算標籤分佈
            distribution = Counter()
            for shape in annotation_data[shapes_key]:
                label = shape.get("label", "unknown")
                distribution[label] += 1

            if not distribution:
                return None

            sample = {
                "stem": json_file.stem,
                "task": task,
                "class": class_name,
                "distribution": distribution,
                "annotation_file": json_file,
                "img_file": img_file,
                "shapes_key": shapes_key
            }

            return sample

        except Exception as e:
            self.logger.warning(f"讀取 {json_file} 失敗: {e}")
            return None

    def _find_image_file(self, img_dir: Path, stem: str) -> Optional[Path]:
        """
        查找對應的圖像文件，支援中文路徑

        參數:
            img_dir: 圖片目錄
            stem: 文件基本名

        返回:
            圖片文件路徑，如果找不到則返回None
        """
        try:
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                img_file = img_dir / f"{stem}{ext}"
                if img_file.exists():
                    return img_file
        except UnicodeEncodeError:
            # 處理中文路徑問題
            img_dir_str = str(img_dir)
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                img_path = os.path.join(img_dir_str, f"{stem}{ext}")
                if os.path.exists(img_path):
                    return Path(img_path)
        return None

    def _filter_samples(self, samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根據類別限制篩選樣本

        參數:
            samples: 所有樣本列表

        返回:
            過濾後的樣本列表
        """
        # 如果沒有類別限制，直接返回
        if not self.class_limits:
            return samples

        # 統計每個類別的樣本數
        class_counts = defaultdict(int)
        for sample in samples:
            class_counts[sample["class"]] += 1

        # 顯示原始類別統計
        self.logger.info("原始類別統計:")
        for label, count in sorted(class_counts.items(), key=lambda x: -x[1]):
            self.logger.info(f"  {label}: {count}")

        # 將樣本分為多類別和單類別
        multi_class_samples = [
            s for s in samples if s["class"] == "multi_class"]
        single_class_samples = [
            s for s in samples if s["class"] != "multi_class"]

        # 初始化結果和類別計數
        filtered_samples = []
        current_counts = defaultdict(int)

        # 先處理多類別樣本
        self.logger.info(
            f"優先處理 multi_class 資料夾中的 {len(multi_class_samples)} 個樣本")
        for sample in multi_class_samples:
            # 判斷這個樣本是否對任何未達到限制的類別有貢獻
            is_useful = False
            for label in sample["distribution"]:
                class_name = sample["class"]  # 這裡使用樣本的類別名稱
                max_count = self.class_limits.get(class_name, float('inf'))
                if current_counts[class_name] < max_count:
                    is_useful = True
                    break

            if is_useful:
                filtered_samples.append(sample)
                # 更新計數
                class_name = sample["class"]
                current_counts[class_name] += 1

        # 顯示多類別處理後的統計
        self.logger.info(f"處理完 multi_class 後選取了 {len(filtered_samples)} 個樣本")
        for label, count in sorted(current_counts.items(), key=lambda x: -x[1]):
            max_count = self.class_limits.get(label, float('inf'))
            self.logger.info(f"  {label}: {count}/{max_count}")

        # 按類別分組單類別樣本
        samples_by_class = defaultdict(list)
        for sample in single_class_samples:
            samples_by_class[sample["class"]].append(sample)

        # 從單類別樣本中選取
        for class_name, class_samples in samples_by_class.items():
            # 跳過 multi_class
            if class_name == "multi_class":
                continue

            max_count = self.class_limits.get(class_name, float('inf'))
            remaining = max_count - current_counts[class_name]

            if remaining <= 0:
                self.logger.info(f"類別 {class_name} 已達到限制 {max_count}，不再選取")
                continue

            # 確保 remaining 是整數，若是 float('inf')，則使用所有樣本
            if remaining == float('inf'):
                remaining = len(class_samples)
            else:
                remaining = int(remaining)

            # 隨機打亂樣本
            random.shuffle(class_samples)

            # 選取所需數量的樣本
            selected = class_samples[:remaining]
            filtered_samples.extend(selected)

            self.logger.info(
                f"類別 {class_name}: 追加選取 {len(selected)} 個樣本 (總計: {current_counts[class_name] + len(selected)})")

        return filtered_samples

    def _split_dataset_with_consistency(self, samples: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        將樣本分割為訓練、驗證和測試集，確保樣本一致性

        參數:
            samples: 樣本列表

        返回:
            分割後的樣本字典
        """
        # 按照圖像名稱(stem)分組
        samples_by_stem = defaultdict(list)
        for sample in samples:
            samples_by_stem[sample["stem"]].append(sample)

        # 獲取所有樣本的 stem
        all_stems = list(samples_by_stem.keys())

        self.logger.info(f"保留 {len(all_stems)} 個樣本")

        # 隨機排序 stem
        random.shuffle(all_stems)

        # 根據比例計算每個分割的樣本數量
        n_stems = len(all_stems)
        n_train = int(n_stems * self.train_ratio)
        n_val = int(n_stems * self.val_ratio)
        n_test = n_stems - n_train - n_val

        # 分割樣本
        train_stems = all_stems[:n_train]
        val_stems = all_stems[n_train:n_train+n_val]
        test_stems = all_stems[n_train+n_val:]

        # 創建分割
        splits = {
            "train": [],
            "val": [],
            "test": []
        }

        # 將樣本添加到相應的分割
        for stem in train_stems:
            splits["train"].extend(samples_by_stem[stem])

        for stem in val_stems:
            splits["val"].extend(samples_by_stem[stem])

        for stem in test_stems:
            splits["test"].extend(samples_by_stem[stem])

        # 更新統計
        with self.stats_lock:
            self.stats["samples"]["train"] = len(train_stems)
            self.stats["samples"]["val"] = len(val_stems)
            self.stats["samples"]["test"] = len(test_stems)

        self.logger.info(f"分割結果: 訓練集 {len(train_stems)} 圖像，"
                         f"驗證集 {len(val_stems)} 圖像，"
                         f"測試集 {len(test_stems)} 圖像")

        return splits

    def _process_split_for_task(self,
                                samples: List[Dict[str, Any]],
                                split_name: str,
                                task: str,
                                batch_size: int = 50) -> None:
        """
        處理特定分割的特定任務的樣本

        參數:
            samples: 樣本列表
            split_name: 分割名稱 (train/val/test)
            task: 任務類型 (segment/object_detect)
            batch_size: 批處理大小
        """
        self.logger.info(f"處理 {split_name} 分割的 {task} 任務")

        output_dir = self.output_dir / task / split_name
        output_dir.mkdir(parents=True, exist_ok=True)

        # 如果樣本較少，不使用批處理
        if len(samples) <= batch_size:
            self._process_samples_batch(samples, output_dir, split_name, task)
        else:
            # 批量處理樣本
            for i in range(0, len(samples), batch_size):
                batch = samples[i:i + batch_size]
                self.logger.info(
                    f"處理 {task} {split_name} 批次 {i//batch_size + 1}/{(len(samples) + batch_size - 1)//batch_size}: {len(batch)} 個樣本")

                self._process_samples_batch(
                    batch, output_dir, split_name, task)

                # 釋放內存
                gc.collect()

    def _process_samples_batch(self,
                               samples: List[Dict[str, Any]],
                               output_dir: Path,
                               split_name: str,
                               task: str) -> None:
        """
        並行處理樣本批次

        參數:
            samples: 樣本批次
            output_dir: 輸出目錄
            split_name: 分割名稱
            task: 任務類型
        """
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for sample in samples:
                future = executor.submit(
                    self._process_sample, sample, output_dir, split_name, task
                )
                futures.append((future, sample))

            # 收集結果並更新統計
            for future, sample in tqdm(futures, desc=f"處理 {task} {split_name}"):
                try:
                    success = future.result()
                    if success:
                        # 更新類別分佈統計 - 只在segment任務時更新，避免重複計算
                        if task == "segment":
                            class_name = sample["class"]
                            # 計算該樣本中的實際標籤數量
                            label_count = sum(
                                sample["distribution"].values()) if sample["distribution"] else 1

                            with self.stats_lock:
                                # 為每個任務類型單獨記錄
                                for task_type in self.tasks:
                                    task_class_key = f"{task_type}_{class_name}"
                                    self.stats["class_distribution"][task_class_key][split_name] += label_count
                                    self.stats["class_distribution"][task_class_key]["total"] += label_count
                except Exception as e:
                    self.logger.error(f"處理樣本時出錯: {e}")

    def _process_sample(self,
                        sample: Dict[str, Any],
                        output_dir: Path,
                        split_name: str,
                        task: str) -> bool:
        """
        處理單個樣本

        參數:
            sample: 樣本
            output_dir: 輸出目錄
            split_name: 分割名稱
            task: 任務類型

        返回:
            是否成功
        """
        try:
            stem = sample["stem"]
            img_file = sample["img_file"]
            annotation_file = sample["annotation_file"]

            # 複製圖像，處理中文路徑
            try:
                dst_img = output_dir / img_file.name
                shutil.copy2(img_file, dst_img)
            except UnicodeEncodeError:
                # 處理中文路徑問題
                src_path = str(img_file)
                dst_path = os.path.join(str(output_dir), img_file.name)
                shutil.copy2(src_path, dst_path)

            # 讀取原始標註
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)

            # 根據任務類型處理標註
            if task == "object_detect":
                # 將多邊形轉換為矩形框
                shapes_key = sample["shapes_key"]
                converted_data = self._convert_to_boxes(
                    annotation_data, shapes_key)

                # 保存修改後的標註文件，處理中文路徑
                try:
                    dst_annotation = output_dir / f"{stem}.json"
                    with open(dst_annotation, 'w', encoding='utf-8') as f:
                        json.dump(converted_data, f, indent=2,
                                  ensure_ascii=False)
                except UnicodeEncodeError:
                    # 處理中文路徑問題
                    dst_path = os.path.join(str(output_dir), f"{stem}.json")
                    with open(dst_path, 'w', encoding='utf-8') as f:
                        json.dump(converted_data, f, indent=2,
                                  ensure_ascii=False)
            else:
                # segment任務保持原始標註
                dst_annotation = output_dir / f"{stem}.json"
                shutil.copy2(annotation_file, dst_annotation)

            return True

        except Exception as e:
            self.logger.error(f"處理 {annotation_file} 失敗: {e}")
            # 刪除已複製的圖像
            if 'dst_img' in locals() and dst_img.exists():
                dst_img.unlink()
            return False

    def _convert_to_boxes(self, annotation_data: Dict[str, Any], shapes_key: str) -> Dict[str, Any]:
        """
        將多邊形標註轉換為矩形框

        參數:
            annotation_data: 原始標註數據
            shapes_key: 標註中形狀的鍵名（shapes 或 annotations）

        返回:
            轉換後的標註數據
        """
        # 創建一個深拷貝以避免修改原始數據
        converted_data = copy.deepcopy(annotation_data)

        # 處理每個形狀
        for shape in converted_data.get(shapes_key, []):
            shape_type = shape.get("shape_type", "polygon")
            points = shape.get("points", [])

            # 只處理多邊形
            if shape_type == "polygon" and len(points) >= 3:
                # 計算邊界框
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]
                xmin, xmax = min(xs), max(xs)
                ymin, ymax = min(ys), max(ys)

                # 設置為矩形
                shape["shape_type"] = "rectangle"
                shape["points"] = [[xmin, ymin], [xmax, ymax]]

        return converted_data

    def _collect_multi_class_samples(self,
                                     multi_class_dir: Path,
                                     task: str,
                                     batch_size: int = 50) -> List[Dict[str, Any]]:
        """
        收集多類別資料夾中的樣本，識別每個圖像中包含的類別

        參數:
            multi_class_dir: 多類別目錄
            task: 任務類型
            batch_size: 批處理大小

        返回:
            樣本列表
        """
        samples = []

        # 檢查是否是直接存放圖像和標籤的結構
        img_files = []
        img_file_map = {}

        # 收集所有圖片文件並建立映射
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            found_imgs = list(multi_class_dir.glob(f"*{ext}"))
            img_files.extend(found_imgs)
            # 建立圖片stem到圖片路徑的映射
            for img in found_imgs:
                img_file_map[img.stem] = img

        json_files = list(multi_class_dir.glob("*.json"))

        # 如果文件較少，不使用批處理
        if len(json_files) <= batch_size:
            samples.extend(self._process_multi_class_json_files(
                json_files, img_file_map, multi_class_dir, task))
        else:
            # 批量處理文件
            for i in range(0, len(json_files), batch_size):
                batch = json_files[i:i + batch_size]
                self.logger.info(
                    f"處理多類別批次 {i//batch_size + 1}/{(len(json_files) + batch_size - 1)//batch_size}: {len(batch)} 個檔案")

                batch_samples = self._process_multi_class_json_files(
                    batch, img_file_map, multi_class_dir, task)
                samples.extend(batch_samples)

                # 釋放內存
                gc.collect()

        return samples

    def _process_multi_class_json_files(self,
                                        json_files: List[Path],
                                        img_file_map: Dict[str, Path],
                                        multi_class_dir: Path,
                                        task: str) -> List[Dict[str, Any]]:
        """
        並行處理多類別JSON文件

        參數:
            json_files: JSON文件列表
            img_file_map: 圖片映射
            multi_class_dir: 多類別目錄
            task: 任務類型

        返回:
            樣本列表
        """
        samples = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for json_file in json_files:
                stem = json_file.stem
                img_file = None

                # 查找匹配的圖像文件
                img_file = img_file_map.get(stem)

                if not img_file:
                    # 如果映射中找不到，嘗試查找匹配的圖像文件
                    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                        potential_img = multi_class_dir / f"{stem}{ext}"
                        if potential_img.exists():
                            img_file = potential_img
                            break

                if not img_file:
                    continue

                future = executor.submit(
                    self._process_multi_class_json_file, json_file, img_file, task
                )
                futures.append(future)

            # 收集結果
            for future in as_completed(futures):
                try:
                    sample = future.result()
                    if sample:
                        samples.append(sample)
                except Exception as e:
                    self.logger.error(f"處理多類別JSON文件時出錯: {e}")

        return samples

    def _process_multi_class_json_file(self,
                                       json_file: Path,
                                       img_file: Path,
                                       task: str) -> Optional[Dict[str, Any]]:
        """
        處理單個多類別JSON文件

        參數:
            json_file: JSON文件路徑
            img_file: 圖片文件路徑
            task: 任務類型

        返回:
            樣本字典，如果處理失敗則返回None
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)

            # 檢查標註
            shapes_key = "shapes" if "shapes" in annotation_data else "annotations"
            if shapes_key not in annotation_data or not annotation_data[shapes_key]:
                return None

            # 計算每個類別的分佈
            distribution = Counter()
            for shape in annotation_data[shapes_key]:
                label = shape.get("label", "unknown")

                # 從標籤中提取實際類別名稱
                class_name = self._extract_class_from_label(label)
                distribution[class_name] += 1

            if not distribution:
                return None

            # 創建樣本，添加多類別標記
            sample = {
                "stem": json_file.stem,
                "task": task,
                "class": "multi_class",  # 標記為多類別
                "distribution": distribution,  # 包含所有類別的分佈
                "annotation_file": json_file,
                "img_file": img_file,
                "shapes_key": shapes_key
            }

            return sample

        except Exception as e:
            self.logger.warning(f"讀取 {json_file} 失敗: {e}")
            return None

    def _extract_class_from_label(self, label: str) -> str:
        """
        從標籤中提取實際的類別名稱

        參數:
            label: 標籤名稱

        返回:
            類別名稱
        """
        # 嘗試從標籤中提取類別名稱
        # 標籤可能像 "expansion_joint_伸縮縫" 或 "expansion_joint" 或其他格式

        # 先檢查是否有底線分隔
        if "_" in label:
            parts = label.split("_")
            # 如果有至少兩部分，取第一部分作為英文類別名
            if len(parts) >= 2:
                return parts[0]

        # 如果無法解析，返回原始標籤
        return label

    def _generate_statistics(self) -> None:
        """生成分割統計報告"""
        # 保存JSON格式統計
        stats_file = self.output_dir / "split_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            # 轉換defaultdict為普通字典
            stats_dict = {
                "samples": dict(self.stats["samples"]),
                "class_distribution": {k: dict(v) for k, v in self.stats["class_distribution"].items()},
                "settings": {
                    "train_ratio": self.train_ratio,
                    "val_ratio": self.val_ratio,
                    "test_ratio": self.test_ratio,
                    "class_limits": self.class_limits
                }
            }
            json.dump(stats_dict, f, indent=2, ensure_ascii=False)

        # 生成人類可讀的報告
        report_file = self.output_dir / "split_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("===== 數據集分割報告 =====\n\n")

            f.write("樣本統計:\n")
            for k, v in self.stats["samples"].items():
                f.write(f"  {k}: {v}\n")

            f.write("\n類別分佈:\n")
            for task in self.tasks:
                f.write(f"\n  {task} 任務:\n")

                task_stats = {k: v for k, v in self.stats["class_distribution"].items(
                ) if k.startswith(f"{task}_")}

                for key, counts in sorted(task_stats.items(), key=lambda x: x[0]):
                    # 提取類別名稱
                    class_name = key.split("_", 1)[1]

                    f.write(f"    {class_name}: 總計={counts['total']} "
                            f"(訓練={counts['train']}, 驗證={counts['val']}, 測試={counts['test']})\n")

        self.logger.info(f"統計報告已保存至 {report_file}")

    def _verify_dataset(self) -> None:
        """驗證分割後的數據集完整性"""
        self.logger.info("驗證數據集完整性...")

        for task in self.tasks:
            for split in self.splits:
                split_dir = self.output_dir / task / split

                # 圖像和標籤文件在同一目錄
                image_files = set()
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    image_files.update(
                        {f.stem for f in split_dir.glob(f"*{ext}") if f.is_file()})

                label_files = {f.stem for f in split_dir.glob(
                    "*.json") if f.is_file()}

                missing_labels = image_files - label_files
                extra_labels = label_files - image_files

                if missing_labels:
                    self.logger.warning(
                        f"{task} {split}: {len(missing_labels)} 個圖像缺少標籤")

                if extra_labels:
                    self.logger.warning(
                        f"{task} {split}: {len(extra_labels)} 個標籤缺少圖像")

                self.logger.info(
                    f"{task} {split}: {len(image_files)} 圖像, {len(label_files)} 標籤")

    def _create_directories(self) -> None:
        """創建必要的輸出目錄結構"""
        # 創建主輸出目錄
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)

            # 為每個任務創建目錄
            for task in self.tasks:
                for split in self.splits:
                    # 每個任務的分割目錄
                    task_split_dir = self.output_dir / task / split
                    task_split_dir.mkdir(parents=True, exist_ok=True)
        except UnicodeEncodeError:
            # 處理中文路徑問題
            output_dir_str = str(self.output_dir)
            os.makedirs(output_dir_str, exist_ok=True)

            for task in self.tasks:
                for split in self.splits:
                    task_split_path = os.path.join(output_dir_str, task, split)
                    os.makedirs(task_split_path, exist_ok=True)

    def _print_final_statistics(self) -> None:
        """輸出最終統計信息"""
        self.logger.info("\n========= 最終統計 =========")

        # 輸出各分割集的圖像數量
        self.logger.info(f"訓練集: {self.stats['samples']['train']} 圖像")
        self.logger.info(f"驗證集: {self.stats['samples']['val']} 圖像")
        self.logger.info(f"測試集: {self.stats['samples']['test']} 圖像")
        self.logger.info(f"總計: {self.stats['samples']['total']} 圖像")

        # 輸出各類別統計
        self.logger.info("\n各類別分佈:")
        # 按任務分組
        for task in self.tasks:
            self.logger.info(f"\n{task} 任務:")

            # 找出屬於這個任務的類別
            task_classes = {}
            for key, counts in self.stats["class_distribution"].items():
                if key.startswith(f"{task}_"):
                    class_name = key[len(task)+1:]  # 移除 'task_' 前綴
                    task_classes[class_name] = counts

            # 輸出每個類別的統計
            for class_name, counts in sorted(task_classes.items()):
                self.logger.info(f"  {class_name}: 總計={counts['total']} "
                                 f"(訓練={counts['train']}, 驗證={counts['val']}, 測試={counts['test']})")

        print("\n========= 最終統計 =========")
        print(f"訓練集: {self.stats['samples']['train']} 圖像")
        print(f"驗證集: {self.stats['samples']['val']} 圖像")
        print(f"測試集: {self.stats['samples']['test']} 圖像")
        print(f"總計: {self.stats['samples']['total']} 圖像")


# 如果作為獨立腳本運行
if __name__ == "__main__":
    import argparse
    import sys

    # 設置命令行參數
    parser = argparse.ArgumentParser(description="數據集分割工具")
    parser.add_argument("--input", required=True, help="輸入目錄")
    parser.add_argument("--output", required=True, help="輸出目錄")
    parser.add_argument("--interactive", action="store_true",
                        help="互動式設定類別限制和分割比例")
    parser.add_argument("--train-ratio", type=float, default=0.7, help="訓練集比例")
    parser.add_argument("--val-ratio", type=float, default=0.15, help="驗證集比例")
    parser.add_argument("--test-ratio", type=float, default=0.15, help="測試集比例")
    parser.add_argument("--class-limits", help="類別限制JSON文件")
    parser.add_argument("--threads", type=int, default=None, help="並行處理的線程數")
    parser.add_argument("--batch-size", type=int, default=50, help="批處理大小")
    parser.add_argument("--no-resume", action="store_true", help="不從中斷點恢復")

    args = parser.parse_args()

    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("dataset_splitter.log",
                                mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logger = logging.getLogger("DatasetDivider")

    # 記錄開始時間
    start_time = time.time()
    logger.info(f"開始執行數據集分割任務，輸入: {args.input}, 輸出: {args.output}")

    # 加載類別限制
    class_limits = {}
    if args.class_limits:
        try:
            with open(args.class_limits, 'r', encoding='utf-8') as f:
                class_limits = json.load(f)
            logger.info(f"已載入類別限制文件: {args.class_limits}")
            for cls, limit in class_limits.items():
                logger.info(f"  類別 {cls}: 限制 {limit}")
        except Exception as e:
            logger.error(f"載入類別限制文件失敗: {e}")

    # 創建分割器實例以獲取類別計數
    divider = DatasetDivider(
        input_dir=args.input,
        output_dir=args.output,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        class_limits=class_limits,
        logger=logger,
        max_workers=args.threads
    )

    # 互動式設定
    if args.interactive:
        # 收集樣本以獲取類別數量
        divider._collect_samples(batch_size=args.batch_size)
        class_counts = divider.class_counts

        # 獲取類別列表，排除multi_class
        class_names = [name for name in class_counts.keys()
                       if name != "multi_class"]

        logger.info(f"發現以下類別: {', '.join(class_names)}")
        if "multi_class" in class_counts:
            logger.info(f"發現多類別資料夾，包含 {class_counts['multi_class']} 個樣本")

        print("\n===== 互動式設定 =====")

        # 輸入分割比例
        while True:
            try:
                print("\n請輸入資料集分割比例 (三個數字總和須為 1.0)")
                train_ratio = float(
                    input(f"訓練集比例 [{args.train_ratio}]: ") or args.train_ratio)
                val_ratio = float(
                    input(f"驗證集比例 [{args.val_ratio}]: ") or args.val_ratio)
                test_ratio = float(
                    input(f"測試集比例 [{args.test_ratio}]: ") or args.test_ratio)

                total = train_ratio + val_ratio + test_ratio
                if abs(total - 1.0) <= 0.001:
                    # 更新分割比例
                    divider.train_ratio = train_ratio
                    divider.val_ratio = val_ratio
                    divider.test_ratio = test_ratio
                    break
                else:
                    print(f"錯誤: 比例總和為 {total}，須為 1.0")
            except ValueError:
                print("請輸入有效的數字")

        # 輸入類別限制，並顯示類別總數 (不包括multi_class)
        print("\n請為每個類別設定數量限制 (enter鍵表示不限制)")
        print("注意: 優先從多類別樣本中提取，然後才使用單類別樣本")

        if "multi_class" in class_counts:
            # 計算實際圖像數量（因為統計中每張圖會被計算兩次）
            print(
                f"多類別樣本數: {int(class_counts['multi_class']/2)} (不需要設定限制，會自動優先使用)")

        for class_name in class_names:
            try:
                # 計算實際圖像數量（因為統計中每張圖會被計算兩次）
                total_count = int(class_counts[class_name]/2)

                # 計算這個類別中的標籤物件數量
                class_objects_count = 0
                try:
                    # 收集該類別在此任務中的所有樣本
                    task_samples = divider._collect_class_samples(
                        Path(args.input) / class_name, class_name, "segment", args.batch_size)

                    # 計算所有樣本中的標籤總數
                    for sample in task_samples:
                        if sample["distribution"]:
                            # 除以2，因為每個標籤實際上只算一次
                            class_objects_count += int(
                                sum(sample["distribution"].values())/2)

                except Exception as e:
                    logger.error(f"計算類別 '{class_name}' 的物件數量時出錯: {e}")
                    class_objects_count = "未知"

                # 獲取當前限制
                current_limit = divider.class_limits.get(class_name, 0)
                limit_str = f"[目前: {current_limit}]" if current_limit else ""

                # 顯示類別的圖片數量和物件數量
                user_input = input(
                    f"類別 '{class_name}' 的最大樣本數 {limit_str} (總共有: {total_count}張圖片, {class_objects_count}個類別物件): ")

                if user_input.strip():
                    divider.class_limits[class_name] = int(user_input)
                    logger.info(f"設定類別 '{class_name}' 的限制為 {int(user_input)}")
            except ValueError:
                print(
                    f"為類別 '{class_name}' 使用目前值: {divider.class_limits.get(class_name, '不限制')}")

    # 執行分割
    stats = divider.run(batch_size=args.batch_size, resume=not args.no_resume)

    print(f"分割完成！詳見報告: {os.path.join(args.output, 'split_report.txt')}")

    # 計算總耗時
    end_time = time.time()
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)

    logger.info(f"處理完成！總耗時: {int(hours)}小時 {int(minutes)}分 {seconds:.2f}秒")
