# 圖像數據集處理工具集 - PyQt GUI 版本

這是圖像數據集處理工具集的現代化 PyQt6 GUI 版本，提供了美觀的界面和明亮/黑暗模式支持。

## 功能特點

### 🎨 現代化界面
- 基於 PyQt6 構建的現代化界面
- 支持明亮和黑暗模式切換
- 標籤頁式導航，易於使用
- 實時日誌顯示和進度條

### 🛠️ 完整的工具集成
1. **標籤格式轉換器** - 轉換各種標籤格式為LabelMe格式
2. **標籤編輯器** - 合併、重命名、刪除標籤
3. **數據集分割器** - 分割數據集為訓練/驗證/測試集
4. **圖像增強器** - 通過區域融合生成增強數據
5. **全景圖像擴增器** - 專門針對全景圖像的擴增工具

### 💾 智能配置管理
- 自動保存界面設置
- 配置文件導入/導出
- 記住上次使用的目錄

## 安裝要求

### 基本依賴
```bash
pip install PyQt6>=6.4.0
pip install qdarkstyle>=3.1.0
```

### 完整依賴
```bash
pip install -r requirements.txt
```

## 快速開始

### 1. 啟動GUI應用程式
```bash
python pyqt_gui_application.py
```

### 2. 運行測試
```bash
# 基本功能測試
python test_pyqt_gui.py

# 視覺界面測試
python test_pyqt_gui.py --visual
```

## 使用指南

### 主界面介紹
- **標籤頁導航**: 點擊上方標籤頁切換不同工具
- **工具界面**: 每個工具都有專門的參數設置界面
- **日誌區域**: 底部顯示處理進度和結果
- **菜單欄**: 提供配置管理和主題切換功能

### 主題切換
1. 點擊菜單欄的「視圖」
2. 選擇「切換到黑暗模式」或「切換到明亮模式」
3. 主題設置會自動保存

### 配置管理
1. **新建配置**: 文件 → 新建配置
2. **載入配置**: 文件 → 載入配置
3. **保存配置**: 文件 → 保存配置
4. **保存當前設置**: 在任何工具界面點擊「保存配置」

## 工具使用說明

### 標籤格式轉換器
1. 添加一個或多個輸入目錄
2. 選擇輸出目錄
3. 設置源格式（auto表示自動檢測）
4. 配置縮放比例和圖像品質
5. 點擊「開始處理」

### 標籤編輯器
1. 選擇輸入和輸出目錄
2. 在「標籤操作」區域配置操作：
   - **合併標籤**: 將多個標籤合併為一個
   - **重命名標籤**: 重命名標籤
   - **刪除標籤**: 刪除指定標籤
3. 添加操作到隊列
4. 點擊「開始處理」執行所有操作

### 數據集分割器
1. 選擇輸入和輸出目錄
2. 設置分割比例（總和必須為1.0）
3. 可選：設置類別數量限制
4. 配置處理參數
5. 點擊「開始處理」

### 圖像增強器
1. 添加來源目錄（包含要提取的區域）
2. 選擇目標目錄（要放置區域的圖像）
3. 選擇輸出目錄
4. 設置增強參數和任務類型
5. 可選：配置類別目標數量
6. 點擊「開始處理」

### 全景圖像擴增器
1. 選擇圖像文件（單張模式）或Excel文件（批量模式）
2. 可選：選擇對應的標籤文件
3. 設置外方位參數（Omega、Phi、Kappa）
4. 選擇擴增方法
5. 配置擴增參數
6. 選擇輸出目錄
7. 點擊「開始處理」

## 進階功能

### 多線程處理
- 所有工具都支持多線程處理
- 可在各工具的設置中調整並行工作數
- 處理過程中界面保持響應

### 斷點恢復
- 數據集分割器支持斷點恢復功能
- 處理中斷後可從上次位置繼續

### 批量處理
- 標籤格式轉換器支持多目錄批量處理
- 全景擴增器支持Excel外方位批量處理

### 可視化支持
- 全景擴增器支持結果可視化
- 圖像增強器支持放置區域可視化

## 故障排除

### 常見問題

**1. 無法啟動GUI**
```bash
# 檢查PyQt6是否正確安裝
python -c "from PyQt6.QtWidgets import QApplication; print('PyQt6 OK')"

# 運行測試腳本
python test_pyqt_gui.py
```

**2. 黑暗模式不工作**
```bash
# 安裝qdarkstyle
pip install qdarkstyle
```

**3. 工具模組無法導入**
```bash
# 確保所有依賴已安裝
pip install -r requirements.txt
```

**4. 處理過程中界面凍結**
- 這是正常現象，處理在後台線程中進行
- 查看日誌區域了解處理進度
- 耐心等待處理完成

### 日誌分析
- 所有操作都會在底部日誌區域顯示詳細信息
- 錯誤信息會以紅色顯示
- 可通過菜單「工具 → 清除日誌」清空日誌

## 配置文件

GUI使用 `pyqt_gui_config.json` 文件保存設置：
```json
{
  "window_geometry": {"width": 1400, "height": 900, "x": 100, "y": 100},
  "theme": "light",
  "last_input_dir": "",
  "last_output_dir": "",
  "converter": {"resize": 1.0, "quality": 75},
  "divider": {"train_ratio": 0.7, "val_ratio": 0.15, "test_ratio": 0.15},
  "augmenter": {"num_generations": 100, "task_type": "both"},
  "panorama": {"methods": ["orientation"], "num_variations": 8}
}
```

## 與原版對比

| 功能 | tkinter版本 | PyQt版本 |
|------|-------------|----------|
| 界面風格 | 基礎 | 現代化 |
| 主題支持 | 無 | 明亮/黑暗 |
| 工具整合 | 部分 | 完整 |
| 配置管理 | 基礎 | 高級 |
| 多線程 | 有限 | 完整 |
| 進度顯示 | 簡單 | 詳細 |

## 開發信息

- **GUI框架**: PyQt6
- **主題引擎**: qdarkstyle
- **配置格式**: JSON
- **日誌系統**: Python logging
- **多線程**: QThread

## 貢獻

歡迎提交問題和改進建議！

## 許可證

與主項目保持一致。