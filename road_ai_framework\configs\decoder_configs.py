"""
解碼器配置：定義所有解碼器變體的配置
包含FPN、UNet、分割頭等解碼器的配置
"""

from typing import Dict, Any, List


def get_fpn_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取FPN解碼器配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "decoder_type": "FPNDecoder",
        "task_type": "segmentation",
        "decoder_channels": [256, 128, 64, 32],
        "num_classes": 1,
        "use_batchnorm": True,
        "dropout": 0.1,
    }
    
    variants = {
        "default": {
            # 標準FPN
        },
        
        "lightweight": {
            # 輕量級FPN
            "decoder_channels": [128, 64, 32, 16],
            "dropout": 0.05,
        },
        
        "heavy": {
            # 重型FPN
            "decoder_channels": [512, 256, 128, 64],
            "dropout": 0.2,
        },
        
        "road_damage": {
            # 專門用於道路損壞檢測
            "decoder_channels": [256, 128, 64, 32],
            "num_classes": 5,  # 多類別道路損壞
            "dropout": 0.15,
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["decoder_variant"] = variant
    else:
        raise ValueError(f"未知的FPN變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def get_unet_decoder_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取UNet解碼器配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "decoder_type": "UNetDecoder",
        "task_type": "segmentation",
        "decoder_channels": [256, 128, 64, 32],
        "num_classes": 1,
        "use_batchnorm": True,
        "use_attention": False,
        "dropout": 0.1,
    }
    
    variants = {
        "default": {
            # 標準UNet解碼器
        },
        
        "attention": {
            # 帶注意力機制的UNet解碼器
            "use_attention": True,
        },
        
        "lightweight": {
            # 輕量級UNet解碼器
            "decoder_channels": [128, 64, 32, 16],
            "dropout": 0.05,
        },
        
        "deep": {
            # 深層UNet解碼器
            "decoder_channels": [512, 256, 128, 64, 32],
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["decoder_variant"] = variant
    else:
        raise ValueError(f"未知的UNet解碼器變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def get_segmentation_head_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取分割頭配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "decoder_type": "SegmentationHead",
        "task_type": "segmentation",
        "in_channels": 256,
        "num_classes": 1,
        "kernel_size": 3,
        "use_batchnorm": True,
        "dropout": 0.1,
        "activation": "relu",
    }
    
    variants = {
        "default": {
            # 標準分割頭
        },
        
        "aspp": {
            # ASPP分割頭（Atrous Spatial Pyramid Pooling）
            "use_aspp": True,
            "aspp_dilations": [1, 6, 12, 18],
            "aspp_dropout": 0.5,
        },
        
        "psp": {
            # PSP分割頭（Pyramid Scene Parsing）
            "use_psp": True,
            "psp_pool_sizes": [1, 2, 3, 6],
        },
        
        "road_damage": {
            # 道路損壞專用分割頭
            "num_classes": 5,
            "dropout": 0.15,
            "use_deep_supervision": True,
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["decoder_variant"] = variant
    else:
        raise ValueError(f"未知的分割頭變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def get_detection_head_config(variant: str = "default") -> Dict[str, Any]:
    """
    獲取檢測頭配置
    
    Args:
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    base_config = {
        "decoder_type": "DetectionHead",
        "task_type": "detection",
        "in_channels": 256,
        "num_classes": 80,  # COCO默認類別數
        "num_anchors": 9,
        "use_focal_loss": True,
        "prior_prob": 0.01,
    }
    
    variants = {
        "default": {
            # 標準檢測頭
        },
        
        "yolo": {
            # YOLO風格檢測頭
            "num_anchors": 3,
            "use_objectness": True,
            "use_focal_loss": False,
        },
        
        "road_elements": {
            # 道路元素檢測專用
            "num_classes": 10,  # 道路標誌、車輛等
            "num_anchors": 6,
        },
    }
    
    config = base_config.copy()
    if variant in variants:
        config.update(variants[variant])
        config["decoder_variant"] = variant
    else:
        raise ValueError(f"未知的檢測頭變體: {variant}. 可用變體: {list(variants.keys())}")
    
    return config


def list_decoder_configs() -> Dict[str, List[str]]:
    """列出所有可用的解碼器配置"""
    return {
        "fpn": [
            "default", "lightweight", "heavy", "road_damage"
        ],
        "unet_decoder": [
            "default", "attention", "lightweight", "deep"
        ],
        "segmentation_head": [
            "default", "aspp", "psp", "road_damage"
        ],
        "detection_head": [
            "default", "yolo", "road_elements"
        ]
    }


def get_decoder_config(decoder_type: str, variant: str = "default") -> Dict[str, Any]:
    """
    統一的解碼器配置獲取函數
    
    Args:
        decoder_type: 解碼器類型
        variant: 變體名稱
        
    Returns:
        配置字典
    """
    
    config_getters = {
        "fpn": get_fpn_config,
        "unet_decoder": get_unet_decoder_config,
        "segmentation_head": get_segmentation_head_config,
        "detection_head": get_detection_head_config,
    }
    
    if decoder_type not in config_getters:
        raise ValueError(f"未知的解碼器類型: {decoder_type}. 可用類型: {list(config_getters.keys())}")
    
    return config_getters[decoder_type](variant)