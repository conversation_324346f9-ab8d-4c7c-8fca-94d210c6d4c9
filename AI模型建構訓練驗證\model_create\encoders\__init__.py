"""
統一編碼器模組：整合所有編碼器實現
支持CNN、Transformer、Mamba等不同架構族
"""

# 導入統一編碼器實現
from .cnn_family import (
    MobileNetEncoder,
    UNetEncoder,
    ResNetEncoder,
    CSPMobileNetEncoder
)

from .transformer_family import (
    IFormerEncoder,
    CSPIFormerEncoder,
    EffSegformerEncoder,
    TransceptionEncoder
)

# 導入配置
from .configs import (
    get_mobilenet_config,
    get_iformer_config,
    get_csp_iformer_config,
    list_available_configs
)

# 註冊所有編碼器
def register_all_encoders():
    """註冊所有編碼器到註冊表"""
    from ..core.registry import register_encoder
    
    # 註冊CNN編碼器
    register_encoder("mobilenet")(MobileNetEncoder)
    register_encoder("csp_mobilenet")(CSPMobileNetEncoder)
    register_encoder("unet")(UNetEncoder)
    register_encoder("resnet")(ResNetEncoder)
    
    # 註冊Transformer編碼器
    register_encoder("iformer")(IFormerEncoder)
    register_encoder("csp_iformer")(CSPIFormerEncoder)
    register_encoder("effsegformer")(EffSegformerEncoder)
    register_encoder("transception")(TransceptionEncoder)

# 自動註冊
register_all_encoders()

__all__ = [
    # CNN編碼器
    'MobileNetEncoder',
    'UNetEncoder', 
    'ResNetEncoder',
    'CSPMobileNetEncoder',
    
    # Transformer編碼器
    'IFormerEncoder',
    'CSPIFormerEncoder',
    'EffSegformerEncoder',
    'TransceptionEncoder',
    
    # 配置函數
    'get_mobilenet_config',
    'get_iformer_config', 
    'get_csp_iformer_config',
    'list_available_configs'
]