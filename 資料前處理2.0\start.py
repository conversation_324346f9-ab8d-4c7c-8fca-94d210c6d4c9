#!/usr/bin/env python3
"""
圖像數據集處理工具集 - 快速啟動腳本
提供簡單的選單界面來啟動不同的工具
"""

import os
import sys
import subprocess
import platform


def clear_screen():
    """清空螢幕"""
    os.system('cls' if platform.system() == 'Windows' else 'clear')


def check_requirements():
    """檢查是否安裝了必要的依賴"""
    try:
        import numpy
        import cv2
        import matplotlib
        import pandas
        return True
    except ImportError as e:
        print(f"❌ 缺少必要的依賴: {e}")
        print("請執行：pip install -r requirements.txt")
        return False


def show_main_menu():
    """顯示主選單"""
    clear_screen()
    print("=" * 60)
    print("🖼️  圖像數據集處理工具集 v2.0")
    print("=" * 60)
    print()
    print("請選擇要使用的工具：")
    print()
    print("1️⃣  GUI應用程式（推薦）- 圖形化界面")
    print("2️⃣  全景圖像擴增器 - 命令行版本")
    print("3️⃣  標籤格式轉換器")
    print("4️⃣  標籤編輯器")
    print("5️⃣  數據集分割器")
    print("6️⃣  圖像增強器")
    print("7️⃣  整合流程管道")
    print()
    print("9️⃣  查看使用手冊")
    print("0️⃣  退出")
    print()
    print("=" * 60)


def launch_gui():
    """啟動GUI應用程式"""
    print("🚀 啟動GUI應用程式...")
    try:
        subprocess.run([sys.executable, "gui_application.py"])
    except FileNotFoundError:
        print("❌ 找不到gui_application.py文件")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def launch_panorama_augmenter():
    """啟動全景圖像擴增器"""
    print("🚀 啟動全景圖像擴增器...")
    print("提示：使用 --help 查看所有參數")
    print("使用 --interactive 進入交互模式")
    print()

    while True:
        mode = input("選擇模式 [1] 交互模式 [2] 幫助 [3] 返回: ").strip()
        if mode == "1":
            try:
                subprocess.run(
                    [sys.executable, "panorama_augmenter.py", "--interactive"])
            except Exception as e:
                print(f"❌ 啟動失敗: {e}")
            break
        elif mode == "2":
            try:
                subprocess.run(
                    [sys.executable, "panorama_augmenter.py", "--help"])
            except Exception as e:
                print(f"❌ 顯示幫助失敗: {e}")
        elif mode == "3":
            break
        else:
            print("無效選項，請重新輸入")


def launch_annotation_converter():
    """啟動標籤格式轉換器"""
    print("🚀 啟動標籤格式轉換器...")
    try:
        subprocess.run([sys.executable, "annotation_converter.py", "--help"])
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def launch_annotation_editor():
    """啟動標籤編輯器"""
    print("🚀 啟動標籤編輯器...")
    try:
        subprocess.run([sys.executable, "annotation_editor.py", "--help"])
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def launch_dataset_divider():
    """啟動數據集分割器"""
    print("🚀 啟動數據集分割器...")
    try:
        subprocess.run([sys.executable, "dataset_divider.py", "--help"])
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def launch_img_augmenter():
    """啟動圖像增強器"""
    print("🚀 啟動圖像增強器...")
    try:
        subprocess.run([sys.executable, "img_augmenter.py", "--help"])
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def launch_pipeline():
    """啟動整合流程"""
    print("🚀 啟動整合流程管道...")
    try:
        subprocess.run([sys.executable, "img_dataset_pipeline.py", "--help"])
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def show_manual():
    """顯示使用手冊"""
    manual_file = "使用手冊.md"
    if os.path.exists(manual_file):
        print("📖 在瀏覽器中開啟使用手冊...")
        try:
            if platform.system() == "Windows":
                os.startfile(manual_file)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", manual_file])
            else:  # Linux
                subprocess.run(["xdg-open", manual_file])
        except Exception as e:
            print(f"無法自動開啟文件，請手動開啟: {manual_file}")
            print(f"錯誤: {e}")
    else:
        print("❌ 找不到使用手冊文件")


def main():
    """主函數"""
    # 檢查依賴
    if not check_requirements():
        input("按Enter鍵退出...")
        return

    while True:
        try:
            show_main_menu()
            choice = input("請輸入選項 (0-9): ").strip()

            if choice == "0":
                print("👋 感謝使用！")
                break
            elif choice == "1":
                launch_gui()
            elif choice == "2":
                launch_panorama_augmenter()
            elif choice == "3":
                launch_annotation_converter()
            elif choice == "4":
                launch_annotation_editor()
            elif choice == "5":
                launch_dataset_divider()
            elif choice == "6":
                launch_img_augmenter()
            elif choice == "7":
                launch_pipeline()
            elif choice == "9":
                show_manual()
            else:
                print("❌ 無效選項，請重新輸入")
                input("按Enter鍵繼續...")

        except KeyboardInterrupt:
            print("\n\n👋 用戶中斷，退出程式")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")
            input("按Enter鍵繼續...")


if __name__ == "__main__":
    main()
