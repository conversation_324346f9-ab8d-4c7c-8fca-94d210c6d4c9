#!/usr/bin/env python3
"""
增強YOLO推理系統測試
"""

import unittest
import sys
import os
from pathlib import Path
import tempfile
import shutil

# 添加項目路徑
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from AI模型建構訓練驗證.model_create.util.test_framework import BaseTestCase
    from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
        EnhancedYOLOConfig, ClassConfig, create_enhanced_yolo_inference,
        scan_labelme_annotations, generate_class_configs_from_labelme
    )
    ENHANCED_YOLO_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced YOLO not available: {e}")
    ENHANCED_YOLO_AVAILABLE = False
    # 提供簡化的BaseTestCase
    class BaseTestCase:
        def __init__(self):
            pass
        def get_test_image_paths(self):
            return []
        def get_image_annotation_pair(self):
            return None, None, "test"
        def assert_image_valid(self, path):
            pass
        def assert_annotation_valid(self, path):
            pass


class TestEnhancedYOLO(BaseTestCase):
    """增強YOLO測試類"""
    
    def setUp(self):
        super().__init__()
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @unittest.skipUnless(ENHANCED_YOLO_AVAILABLE, "Enhanced YOLO not available")
    def test_config_creation(self):
        """測試配置創建"""
        config = EnhancedYOLOConfig(
            detection_model_path="./test_model.pt",
            segmentation_model_path="./test_model_seg.pt",
            device="cpu"
        )
        
        self.assertEqual(config.device, "cpu")
        self.assertTrue(config.save_visualizations)
        self.assertEqual(config.img_size, 640)
    
    @unittest.skipUnless(ENHANCED_YOLO_AVAILABLE, "Enhanced YOLO not available")
    def test_class_config(self):
        """測試類別配置"""
        class_config = ClassConfig(
            name="test_class",
            conf_threshold=0.5,
            color=(255, 0, 0),
            enabled=True,
            description="Test class"
        )
        
        self.assertEqual(class_config.name, "test_class")
        self.assertEqual(class_config.conf_threshold, 0.5)
        self.assertEqual(class_config.color, (255, 0, 0))
        self.assertTrue(class_config.enabled)
    
    def test_labelme_scanning_empty_dir(self):
        """測試空目錄的LabelMe掃描"""
        if not ENHANCED_YOLO_AVAILABLE:
            self.skipTest("Enhanced YOLO not available")
        
        with self.assertRaises(ValueError):
            scan_labelme_annotations(self.temp_dir)
    
    def test_config_with_custom_classes(self):
        """測試自定義類別配置"""
        if not ENHANCED_YOLO_AVAILABLE:
            self.skipTest("Enhanced YOLO not available")
        
        custom_classes = {
            0: ClassConfig("裂縫", 0.5, (255, 0, 0), True, "道路裂縫"),
            1: ClassConfig("坑洞", 0.4, (0, 255, 0), True, "路面坑洞")
        }
        
        config = EnhancedYOLOConfig(
            detection_model_path="./test_model.pt",
            class_configs=custom_classes
        )
        
        self.assertEqual(len(config.class_configs), 2)
        self.assertEqual(config.class_configs[0].name, "裂縫")
        self.assertEqual(config.class_configs[1].name, "坑洞")
    
    def test_real_data_if_available(self):
        """如果有真實數據則進行測試"""
        if not ENHANCED_YOLO_AVAILABLE:
            self.skipTest("Enhanced YOLO not available")
        
        # 檢查是否有測試圖像
        test_images = self.get_test_image_paths()
        if test_images:
            image_path, annotation_path, key = self.get_image_annotation_pair()
            
            # 驗證測試數據
            self.assert_image_valid(image_path)
            if annotation_path:
                self.assert_annotation_valid(annotation_path)
            
            # 這裡可以添加實際的推理測試
            # 但需要實際的模型文件，所以僅做數據驗證
            self.logger.info(f"Real test data available: {key}")
        else:
            self.logger.warning("No real test data found")


class TestYOLOConfig(unittest.TestCase):
    """YOLO配置測試"""
    
    def test_config_yaml_format(self):
        """測試YAML配置格式"""
        if not ENHANCED_YOLO_AVAILABLE:
            self.skipTest("Enhanced YOLO not available")
        
        # 測試配置字典
        config_dict = {
            'detection_model_path': './test.pt',
            'device': 'cpu',
            'img_size': 640,
            'global_conf': 0.05,
            'class_configs': {
                0: {
                    'name': 'test_class',
                    'conf_threshold': 0.5,
                    'color': [255, 0, 0],
                    'enabled': True
                }
            }
        }
        
        # 這裡可以測試從字典創建配置
        # 實際實現需要在EnhancedYOLOConfig中添加from_dict方法
        self.assertTrue(isinstance(config_dict, dict))


if __name__ == '__main__':
    unittest.main()