#!/usr/bin/env python3
"""
🧪 分類器設置測試腳本
快速驗證分類器環境和配置
"""

import sys
import json
from pathlib import Path

def test_imports():
    """測試必要的導入"""
    print("🔍 測試導入模組...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"🎮 CUDA 可用: {torch.cuda.get_device_name()}")
            print(f"🔥 CUDA 版本: {torch.version.cuda}")
        else:
            print("💻 使用 CPU 模式")
            
    except ImportError:
        print("❌ PyTorch 未安裝")
        return False
    
    try:
        import torchvision
        print(f"✅ torchvision: {torchvision.__version__}")
    except ImportError:
        print("❌ torchvision 未安裝")
        return False
    
    try:
        import albumentations
        print(f"✅ albumentations: {albumentations.__version__}")
    except ImportError:
        print("❌ albumentations 未安裝")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV 未安裝")
        return False
    
    try:
        import matplotlib
        print(f"✅ matplotlib: {matplotlib.__version__}")
    except ImportError:
        print("❌ matplotlib 未安裝")
        return False
    
    try:
        import seaborn
        print(f"✅ seaborn: {seaborn.__version__}")
    except ImportError:
        print("❌ seaborn 未安裝")
        return False
    
    try:
        import pandas
        print(f"✅ pandas: {pandas.__version__}")
    except ImportError:
        print("❌ pandas 未安裝")
        return False
    
    try:
        import sklearn
        print(f"✅ scikit-learn: {sklearn.__version__}")
    except ImportError:
        print("❌ scikit-learn 未安裝")
        return False
    
    return True

def test_classifier_imports():
    """測試分類器模組導入"""
    print("\n🔍 測試分類器模組...")
    
    try:
        from high_accuracy_classifier import (
            ClassifierConfig, 
            HighAccuracyClassifier, 
            ClassificationTrainer,
            FolderClassificationDataset
        )
        print("✅ 分類器模組導入成功")
        return True
    except ImportError as e:
        print(f"❌ 分類器模組導入失敗: {e}")
        return False

def test_config_loading():
    """測試配置文件載入"""
    print("\n🔍 測試配置文件...")
    
    config_file = "classifier_config_example.json"
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, "r", encoding="utf-8") as f:
            config_dict = json.load(f)
        
        from high_accuracy_classifier import ClassifierConfig
        config = ClassifierConfig.from_dict(config_dict)
        
        print("✅ 配置文件載入成功")
        print(f"   模型: {config.model_name}")
        print(f"   圖像尺寸: {config.image_size}")
        print(f"   批次大小: {config.batch_size}")
        print(f"   訓練輪數: {config.num_epochs}")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件載入失敗: {e}")
        return False

def test_model_creation():
    """測試模型創建"""
    print("\n🔍 測試模型創建...")
    
    try:
        import torch
        from high_accuracy_classifier import HighAccuracyClassifier
        
        # 測試不同模型
        models_to_test = [
            ("efficientnet_v2_l", "EfficientNet V2 Large"),
            ("convnext_large", "ConvNeXt Large"),
            ("swin_v2_t", "Swin Transformer V2 Tiny")
        ]
        
        for model_name, display_name in models_to_test:
            try:
                print(f"🤖 測試 {display_name}...")
                model = HighAccuracyClassifier(
                    num_classes=10,
                    model_name=model_name,
                    pretrained=False  # 不下載預訓練權重，只測試架構
                )
                
                # 測試前向傳播
                dummy_input = torch.randn(1, 3, 224, 224)
                with torch.no_grad():
                    output = model(dummy_input)
                
                total_params = sum(p.numel() for p in model.parameters())
                print(f"   ✅ {display_name} 創建成功")
                print(f"      參數數量: {total_params:,}")
                print(f"      輸出形狀: {output.shape}")
                
            except Exception as e:
                print(f"   ❌ {display_name} 創建失敗: {e}")
                # 如果特定模型失敗，嘗試備用方案
                print(f"   💡 將使用備用模型")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型創建測試失敗: {e}")
        return False

def test_data_transforms():
    """測試數據變換"""
    print("\n🔍 測試數據變換...")
    
    try:
        import albumentations as A
        from albumentations.pytorch import ToTensorV2
        import numpy as np
        
        # 創建測試變換
        transform = A.Compose([
            A.Resize(224, 224),
            A.RandomRotate90(p=0.5),
            A.Flip(p=0.5),
            A.RandomBrightnessContrast(p=0.3),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
        
        # 創建測試圖像
        test_image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        
        # 應用變換
        transformed = transform(image=test_image)
        result_tensor = transformed['image']
        
        print("✅ 數據變換測試成功")
        print(f"   原始圖像形狀: {test_image.shape}")
        print(f"   變換後形狀: {result_tensor.shape}")
        print(f"   張量數據類型: {result_tensor.dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ 數據變換測試失敗: {e}")
        return False

def show_installation_guide():
    """顯示安裝指南"""
    print("\n📦 依賴安裝指南:")
    print("""
    # 安裝 PyTorch (請根據你的CUDA版本選擇)
    pip install torch torchvision torchaudio
    
    # 安裝數據處理庫
    pip install albumentations opencv-python
    
    # 安裝可視化和分析庫
    pip install matplotlib seaborn pandas
    
    # 安裝機器學習工具
    pip install scikit-learn
    
    # 安裝進度條
    pip install tqdm
    """)

def main():
    """主測試函數"""
    print("🧪 高精度分類器環境測試")
    print("=" * 50)
    
    # 測試步驟
    tests = [
        ("依賴模組", test_imports),
        ("分類器模組", test_classifier_imports),
        ("配置文件", test_config_loading),
        ("模型創建", test_model_creation),
        ("數據變換", test_data_transforms)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結結果
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 通過率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！分類器已準備就緒")
        print("\n🚀 現在你可以:")
        print("  1. 準備你的數據（按資料夾結構組織）")
        print("  2. 運行 python high_accuracy_classifier.py --data_dir /path/to/data")
        print("  3. 或查看 python classifier_usage_example.py 獲取詳細示例")
    else:
        print(f"\n⚠️ 有 {len(results)-passed} 個測試失敗")
        print("請根據上述錯誤信息安裝缺失的依賴")
        show_installation_guide()

if __name__ == "__main__":
    main()