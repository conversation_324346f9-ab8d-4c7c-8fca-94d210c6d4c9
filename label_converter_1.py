import os
import json
import xml.etree.ElementTree as ET
import logging
import time
from PIL import Image
from xml.dom import minidom

# -------------------------------
# Logger 設定 (同時印出至 console 與存檔)
# -------------------------------
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Console Handler
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
ch.setFormatter(formatter)
logger.addHandler(ch)

# File Handler (存檔到 conversion.log)
log_file = "conversion.log"
fh = logging.FileHandler(log_file, encoding='utf-8')
fh.setLevel(logging.INFO)
fh.setFormatter(formatter)
logger.addHandler(fh)

# -------------------------------
# 輔助函式：box 與 polygon 轉換
# -------------------------------
def get_polygon_from_box(xmin, ymin, xmax, ymax):
    """
    將 box 轉為四角多邊形（絕對座標）
    """
    return [[xmin, ymin], [xmax, ymin], [xmax, ymax], [xmin, ymax]]

def get_box_from_polygon(points):
    """
    根據多邊形點座標計算外接 bounding box（絕對座標）
    """
    xs = [pt[0] for pt in points]
    ys = [pt[1] for pt in points]
    return min(xs), min(ys), max(xs), max(ys)

# -------------------------------
# AnnotationConverter 類別：各格式互轉功能
# -------------------------------
class AnnotationConverter:
    def __init__(self, target_shape="box"):
        """
        target_shape 可設定為 "box" 或 "polygon"：
          - 若來源標註為多邊形，當 target_shape 為 "polygon" 則保留原始多邊形資訊，
            否則僅輸出 bounding box（以兩點表示或轉為四角多邊形）。
          - VOC 與 YOLO 原始僅含 box 資訊，若 target_shape 為 "polygon" 則以 box 轉為四角多邊形。
        """
        self.target_shape = target_shape.lower()
        # 映射字典（依需求調整）
        self.voc_label_mapping = {
            "expansion_joint": "expansion_joint",
            "joint": "joint",
            "linear_crack": "linear_crack",
            "Alligator_crack": "Alligator_crack",
            "potholes": "potholes",
            "patch": "patch",
            "manhole_ditch": "manhole_ditch",
            "deformation": "deformation",
            "dirt_spot": "dirt_spot",
            "other": "other",
        }
        self.voc_to_yolo_mapping = {
            "expansion_joint": "0",
            "joint": "1",
            "linear_crack": "2",
            "Alligator_crack": "3",
            "potholes": "4",
            "patch": "5",
            "manhole_ditch": "6",
            "deformation": "7",
            "dirt_spot": "8",
            "other": "9",
        }
        self.yolo_label_map = {
            "0": "expansion_joint",
            "1": "joint",
            "2": "linear_crack",
            "3": "Alligator_crack",
            "2": "crack",
            "4": "potholes",
            "5": "patch",
            "6": "manhole_ditch",
            "7": "deformation",
            "8": "dirt_spot",
            "9": "other",
        }
        self.yolo_to_voc_mapping = {
            "0": "potholes",
            "1": "bicycle",
            "2": "car",
        }
        self.labelme_to_voc_mapping = {
            "expansion_joint": "expansion_joint",
            "joint": "joint",
            "linear_crack": "linear_crack",
            "Alligator_crack": "Alligator_crack",
            "potholes": "potholes",
            "patch": "patch",
            "manhole_ditch": "manhole_ditch",
            "deformation": "deformation",
            "dirt_spot": "dirt_spot",
            "other": "other",
        }
        self.labelme_to_yolo_mapping = {
            "expansion_joint": "0",
            "expansion_joint_": "0",
            "joint": "1",
            "joint_": "1",
            # "linear_crack": "2",
            "crack": "2",
            # "Alligator_crack": "3",
            "potholes": "3",
            "patch": "4",
            # "manhole_ditch": "6",
            # "deformation": "7",
            # "dirt_spot": "8",
            # "other": "9",
        }

    # ---------------------------------------------------------
    # 1. VOC → LabelMe
    # ---------------------------------------------------------
    def voc_xml_to_labelme_dict(self, xml_file):
        try:
            tree = ET.parse(xml_file)
        except Exception as e:
            logger.error(f"解析 XML {xml_file} 失敗：{e}")
            return None
        root = tree.getroot()
        filename = root.find('filename').text
        size_elem = root.find('size')
        width = int(size_elem.find('width').text)
        height = int(size_elem.find('height').text)
        shapes = []
        for obj in root.findall('object'):
            voc_label = obj.find('name').text
            label = self.voc_label_mapping.get(voc_label, voc_label)
            bndbox = obj.find('bndbox')
            xmin = float(bndbox.find('xmin').text)
            ymin = float(bndbox.find('ymin').text)
            xmax = float(bndbox.find('xmax').text)
            ymax = float(bndbox.find('ymax').text)
            if self.target_shape == "polygon":
                points = get_polygon_from_box(xmin, ymin, xmax, ymax)
                shape_type = "polygon"
            else:
                points = [[xmin, ymin], [xmax, ymax]]
                shape_type = "rectangle"
            shapes.append({
                "label": label,
                "points": points,
                "group_id": None,
                "shape_type": shape_type,
                "flags": {}
            })
        return {
            "version": "4.5.6",
            "flags": {},
            "shapes": shapes,
            "imagePath": filename,
            "imageData": None,
            "imageHeight": height,
            "imageWidth": width
        }

    def convert_voc_to_labelme(self, input_folder, output_folder):
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        total = success = 0
        failed_files = []
        for file in os.listdir(input_folder):
            if file.endswith('.xml'):
                total += 1
                xml_path = os.path.join(input_folder, file)
                labelme_dict = self.voc_xml_to_labelme_dict(xml_path)
                if labelme_dict is None:
                    logger.error(f"VOC → LabelMe：{file} 轉換失敗")
                    failed_files.append(file)
                    continue
                json_filename = os.path.splitext(file)[0] + '.json'
                output_json_path = os.path.join(output_folder, json_filename)
                try:
                    with open(output_json_path, 'w', encoding='utf-8') as f:
                        json.dump(labelme_dict, f, indent=4, ensure_ascii=False)
                    logger.info(f"VOC → LabelMe 轉換完成：{file}")
                    success += 1
                except Exception as e:
                    logger.error(f"VOC → LabelMe 寫入 {file} 時錯誤：{e}")
                    failed_files.append(file)
        logger.info(f"[VOC → LabelMe] 總共 {total} 檔案，成功 {success}，失敗 {len(failed_files)}：{failed_files}")

    # ---------------------------------------------------------
    # 2. VOC → YOLO
    # ---------------------------------------------------------
    def voc_xml_to_yolo_lines(self, xml_file):
        try:
            tree = ET.parse(xml_file)
        except Exception as e:
            logger.error(f"解析 XML {xml_file} 失敗：{e}")
            return None, None, None
        root = tree.getroot()
        size_elem = root.find('size')
        width = float(size_elem.find('width').text)
        height = float(size_elem.find('height').text)
        lines = []
        for obj in root.findall('object'):
            voc_label = obj.find('name').text
            if voc_label not in self.voc_to_yolo_mapping:
                logger.error(f"VOC 標籤 {voc_label} 未定義於映射字典中，跳過")
                continue
            class_id = self.voc_to_yolo_mapping[voc_label]
            bndbox = obj.find('bndbox')
            xmin = float(bndbox.find('xmin').text)
            ymin = float(bndbox.find('ymin').text)
            xmax = float(bndbox.find('xmax').text)
            ymax = float(bndbox.find('ymax').text)
            x_center = ((xmin + xmax) / 2.0) / width
            y_center = ((ymin + ymax) / 2.0) / height
            w = (xmax - xmin) / width
            h = (ymax - ymin) / height
            line = f"{class_id} {x_center:.6f} {y_center:.6f} {w:.6f} {h:.6f}"
            lines.append(line)
        return lines, width, height

    def convert_voc_to_yolo(self, input_folder, output_folder):
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        total = success = 0
        failed_files = []
        for file in os.listdir(input_folder):
            if file.endswith('.xml'):
                total += 1
                xml_path = os.path.join(input_folder, file)
                lines, width, height = self.voc_xml_to_yolo_lines(xml_path)
                if lines is None:
                    logger.error(f"VOC → YOLO：{file} 轉換失敗")
                    failed_files.append(file)
                    continue
                txt_filename = os.path.splitext(file)[0] + '.txt'
                output_txt_path = os.path.join(output_folder, txt_filename)
                try:
                    with open(output_txt_path, 'w', encoding='utf-8') as f:
                        for line in lines:
                            f.write(line + "\n")
                    logger.info(f"VOC → YOLO 轉換完成：{file}")
                    success += 1
                except Exception as e:
                    logger.error(f"VOC → YOLO 寫入 {file} 時錯誤：{e}")
                    failed_files.append(file)
        logger.info(f"[VOC → YOLO] 總共 {total} 檔案，成功 {success}，失敗 {len(failed_files)}：{failed_files}")

    # ---------------------------------------------------------
    # 3. YOLO → LabelMe
    # ---------------------------------------------------------
    def convert_yolo_to_labelme(self, input_folder, output_folder):
        img_extensions = ['.jpg', '.jpeg', '.png']
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        total = success = 0
        failed_files = []
        for file in os.listdir(input_folder):
            ext = os.path.splitext(file)[1].lower()
            if ext in img_extensions:
                total += 1
                img_path = os.path.join(input_folder, file)
                try:
                    with Image.open(img_path) as img:
                        img_width, img_height = img.size
                except Exception as e:
                    logger.error(f"YOLO → LabelMe：無法開啟圖片 {file}：{e}")
                    failed_files.append(file)
                    continue
                label_file = os.path.splitext(file)[0] + '.txt'
                label_path = os.path.join(input_folder, label_file)
                if not os.path.exists(label_path):
                    logger.error(f"YOLO → LabelMe：找不到 YOLO 標註檔：{label_file}")
                    failed_files.append(file)
                    continue
                shapes = []
                with open(label_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        tokens = line.strip().split()
                        if len(tokens) < 5:
                            logger.error(f"YOLO 標註檔 {label_file} 格式錯誤")
                            continue
                        class_id = tokens[0]
                        x_center = float(tokens[1]) * img_width
                        y_center = float(tokens[2]) * img_height
                        box_w = float(tokens[3]) * img_width
                        box_h = float(tokens[4]) * img_height
                        box = [x_center - box_w/2, y_center - box_h/2, x_center + box_w/2, y_center + box_h/2]
                        if len(tokens) > 5:
                            poly_tokens = tokens[5:]
                            if len(poly_tokens) % 2 != 0:
                                logger.error(f"YOLO 標註檔 {label_file} 多邊形數值錯誤")
                                polygon_abs = None
                            else:
                                polygon_abs = []
                                for i in range(0, len(poly_tokens), 2):
                                    px = float(poly_tokens[i]) * img_width
                                    py = float(poly_tokens[i+1]) * img_height
                                    polygon_abs.append([px, py])
                        else:
                            polygon_abs = None
                        if self.target_shape == "polygon":
                            if polygon_abs is not None:
                                shape_points = polygon_abs
                                shape_type = "polygon"
                            else:
                                shape_points = get_polygon_from_box(box[0], box[1], box[2], box[3])
                                shape_type = "polygon"
                        else:
                            shape_points = [[box[0], box[1]], [box[2], box[3]]]
                            shape_type = "rectangle"
                        label_name = self.yolo_label_map.get(class_id, class_id)
                        shapes.append({
                            "label": label_name,
                            "points": shape_points,
                            "group_id": None,
                            "shape_type": shape_type,
                            "flags": {}
                        })
                labelme_dict = {
                    "version": "4.5.6",
                    "flags": {},
                    "shapes": shapes,
                    "imagePath": file,
                    "imageData": None,
                    "imageHeight": img_height,
                    "imageWidth": img_width
                }
                json_filename = os.path.splitext(file)[0] + '.json'
                output_json_path = os.path.join(output_folder, json_filename)
                try:
                    with open(output_json_path, 'w', encoding='utf-8') as f:
                        json.dump(labelme_dict, f, indent=4, ensure_ascii=False)
                    logger.info(f"YOLO → LabelMe 轉換完成：{file}")
                    success += 1
                except Exception as e:
                    logger.error(f"YOLO → LabelMe 寫入 {file} 時錯誤：{e}")
                    failed_files.append(file)
        logger.info(f"[YOLO → LabelMe] 總共 {total} 張圖片，成功 {success}，失敗 {len(failed_files)}：{failed_files}")

    # ---------------------------------------------------------
    # 4. YOLO → VOC
    # ---------------------------------------------------------
    def convert_yolo_to_voc(self, input_folder, output_folder):
        img_extensions = ['.jpg', '.jpeg', '.png']
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        total = success = 0
        failed_files = []
        for file in os.listdir(input_folder):
            ext = os.path.splitext(file)[1].lower()
            if ext in img_extensions:
                total += 1
                img_path = os.path.join(input_folder, file)
                try:
                    with Image.open(img_path) as img:
                        img_width, img_height = img.size
                except Exception as e:
                    logger.error(f"YOLO → VOC：無法開啟圖片 {file}：{e}")
                    failed_files.append(file)
                    continue
                yolo_txt = os.path.splitext(file)[0] + '.txt'
                yolo_path = os.path.join(input_folder, yolo_txt)
                if not os.path.exists(yolo_path):
                    logger.error(f"YOLO → VOC：找不到 YOLO 標註檔：{yolo_txt}")
                    failed_files.append(file)
                    continue
                objects = []
                with open(yolo_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        tokens = line.strip().split()
                        if len(tokens) < 5:
                            logger.error(f"YOLO 標註檔 {yolo_txt} 格式錯誤")
                            continue
                        class_id = tokens[0]
                        if class_id not in self.yolo_to_voc_mapping:
                            logger.error(f"YOLO 標籤 {class_id} 未定義 (檔案 {file})，跳過")
                            continue
                        voc_label = self.yolo_to_voc_mapping[class_id]
                        x_center = float(tokens[1]) * img_width
                        y_center = float(tokens[2]) * img_height
                        box_w = float(tokens[3]) * img_width
                        box_h = float(tokens[4]) * img_height
                        box = [x_center - box_w/2, y_center - box_h/2, x_center + box_w/2, y_center + box_h/2]
                        if len(tokens) > 5:
                            poly_tokens = tokens[5:]
                            if len(poly_tokens) % 2 != 0:
                                logger.error(f"YOLO 標註檔 {yolo_txt} 多邊形資料錯誤")
                                polygon_abs = None
                            else:
                                polygon_abs = []
                                for i in range(0, len(poly_tokens), 2):
                                    px = float(poly_tokens[i]) * img_width
                                    py = float(poly_tokens[i+1]) * img_height
                                    polygon_abs.append([px, py])
                        else:
                            polygon_abs = None
                        obj = {
                            "name": voc_label,
                            "xmin": box[0],
                            "ymin": box[1],
                            "xmax": box[2],
                            "ymax": box[3]
                        }
                        if self.target_shape == "polygon" and polygon_abs is not None:
                            obj["polygon_points"] = polygon_abs
                        objects.append(obj)
                xml_str = self.create_voc_xml(img_path, img_width, img_height, objects)
                output_xml = os.path.join(output_folder, os.path.splitext(file)[0] + '.xml')
                try:
                    with open(output_xml, 'w', encoding='utf-8') as f:
                        f.write(xml_str)
                    logger.info(f"YOLO → VOC 轉換完成：{file}")
                    success += 1
                except Exception as e:
                    logger.error(f"YOLO → VOC 寫入 {file} 時錯誤：{e}")
                    failed_files.append(file)
        logger.info(f"[YOLO → VOC] 總共 {total} 張圖片，成功 {success}，失敗 {len(failed_files)}：{failed_files}")

    # ---------------------------------------------------------
    # 5. LabelMe → VOC
    # ---------------------------------------------------------
    def convert_labelme_to_voc(self, input_folder, output_folder):
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        total = success = 0
        failed_files = []
        for file in os.listdir(input_folder):
            if file.endswith('.json'):
                total += 1
                json_path = os.path.join(input_folder, file)
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                except Exception as e:
                    logger.error(f"讀取 LabelMe JSON {file} 失敗：{e}")
                    failed_files.append(file)
                    continue
                img_file = data.get("imagePath", file)
                img_width = data.get("imageWidth")
                img_height = data.get("imageHeight")
                if img_width is None or img_height is None:
                    logger.error(f"LabelMe JSON {file} 無圖片尺寸資訊")
                    failed_files.append(file)
                    continue
                objects = []
                for shape in data.get("shapes", []):
                    label = shape.get("label")
                    voc_label = self.labelme_to_voc_mapping.get(label, label)
                    points = shape.get("points")
                    if not points or len(points) == 0:
                        logger.error(f"LabelMe JSON {file} 物件點資料錯誤")
                        continue
                    if len(points) == 2:
                        xmin, ymin = points[0]
                        xmax, ymax = points[1]
                        polygon_points = None
                    else:
                        xmin, ymin, xmax, ymax = get_box_from_polygon(points)
                        polygon_points = points if self.target_shape == "polygon" else None
                    obj = {
                        "name": voc_label,
                        "xmin": xmin,
                        "ymin": ymin,
                        "xmax": xmax,
                        "ymax": ymax
                    }
                    if polygon_points:
                        obj["polygon_points"] = polygon_points
                    objects.append(obj)
                xml_str = self.create_voc_xml(img_file, img_width, img_height, objects)
                output_xml = os.path.join(output_folder, os.path.splitext(file)[0] + '.xml')
                try:
                    with open(output_xml, 'w', encoding='utf-8') as f:
                        f.write(xml_str)
                    logger.info(f"LabelMe → VOC 轉換完成：{file}")
                    success += 1
                except Exception as e:
                    logger.error(f"LabelMe → VOC 寫入 {file} 時錯誤：{e}")
                    failed_files.append(file)
        logger.info(f"[LabelMe → VOC] 總共 {total} 個 JSON，成功 {success}，失敗 {len(failed_files)}：{failed_files}")

    # ---------------------------------------------------------
    # 6. LabelMe → YOLO
    # ---------------------------------------------------------
    def convert_labelme_to_yolo(self, input_folder, output_folder):
        # 確保輸出資料夾存在
        os.makedirs(output_folder, exist_ok=True)
    
        total = success = 0
        failed_files = []
    
        for fname in os.listdir(input_folder):
            if not fname.endswith('.json'):
                continue
            total += 1
            js_path = os.path.join(input_folder, fname)
    
            # 讀 JSON
            try:
                with open(js_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            except Exception as e:
                logger.error(f"讀取 LabelMe JSON {fname} 失敗：{e}")
                failed_files.append(fname)
                continue
    
            W = data.get("imageWidth")
            H = data.get("imageHeight")
            if W is None or H is None:
                logger.error(f"LabelMe JSON {fname} 無圖片尺寸資訊")
                failed_files.append(fname)
                continue
    
            out_lines = []
            for shape in data.get("shapes", []):
                label = shape.get("label")
                if label not in self.labelme_to_yolo_mapping:
                    logger.warning(f"LabelMe 標籤 {label} 未定義 (檔案 {fname})，跳過")
                    continue
    
                class_id = self.labelme_to_yolo_mapping[label]
                pts = shape.get("points", [])
    
                if self.target_shape == "polygon" and len(pts) > 2 and pts[-1] == pts[0]:
                    pts = pts[:-1]
    
                xmin, ymin, xmax, ymax = get_box_from_polygon(pts)
                xC = (xmin + xmax) / 2.0 / W
                yC = (ymin + ymax) / 2.0 / H
                w = (xmax - xmin) / W
                h = (ymax - ymin) / H
    
                if self.target_shape == "polygon":
                    parts = [class_id]
                    for x, y in pts:
                        parts.append(f"{x/W:.6f}")
                        parts.append(f"{y/H:.6f}")
                else:
                    parts = [class_id, f"{xC:.6f}", f"{yC:.6f}", f"{w:.6f}", f"{h:.6f}"]
    
                out_lines.append(" ".join(parts))
    
            txt_name = os.path.splitext(fname)[0] + ".txt"
            txt_path = os.path.join(output_folder, txt_name)
            try:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write("\n".join(out_lines))
                logger.info(f"LabelMe → YOLO 轉換完成：{fname}")
                success += 1
            except Exception as e:
                logger.error(f"LabelMe → YOLO 寫入 {fname} 時錯誤：{e}")
                failed_files.append(fname)
    
        logger.info(f"[LabelMe → YOLO] 總共 {total} 個 JSON，成功 {success}，失敗 {len(failed_files)}：{failed_files}")


    # ---------------------------------------------------------
    # 7. VOC → COCO
    # ---------------------------------------------------------
    def convert_voc_to_coco(self, input_folder, output_file):
        images = []
        annotations = []
        categories = {}
        ann_id = 1
        img_id = 1
        xml_files = [f for f in os.listdir(input_folder) if f.endswith('.xml')]
        for xml_file in xml_files:
            xml_path = os.path.join(input_folder, xml_file)
            try:
                tree = ET.parse(xml_path)
            except Exception as e:
                logger.error(f"解析 XML {xml_file} 失敗：{e}")
                continue
            root = tree.getroot()
            filename = root.find('filename').text
            size_elem = root.find('size')
            width = int(size_elem.find('width').text)
            height = int(size_elem.find('height').text)
            images.append({
                "id": img_id,
                "file_name": filename,
                "width": width,
                "height": height
            })
            for obj in root.findall('object'):
                voc_label = obj.find('name').text
                label = self.voc_label_mapping.get(voc_label, voc_label)
                if label not in categories:
                    categories[label] = len(categories) + 1
                cat_id = categories[label]
                bndbox = obj.find('bndbox')
                xmin = float(bndbox.find('xmin').text)
                ymin = float(bndbox.find('ymin').text)
                xmax = float(bndbox.find('xmax').text)
                ymax = float(bndbox.find('ymax').text)
                box_w = xmax - xmin
                box_h = ymax - ymin
                area = box_w * box_h
                if self.target_shape == "polygon":
                    segmentation = [sum(get_polygon_from_box(xmin, ymin, xmax, ymax), [])]
                else:
                    segmentation = []
                annotations.append({
                    "id": ann_id,
                    "image_id": img_id,
                    "category_id": cat_id,
                    "bbox": [xmin, ymin, box_w, box_h],
                    "area": area,
                    "segmentation": segmentation,
                    "iscrowd": 0
                })
                ann_id += 1
            logger.info(f"處理 {xml_file} 成功")
            img_id += 1
        categories_list = []
        for label, cid in categories.items():
            categories_list.append({
                "id": cid,
                "name": label,
                "supercategory": ""
            })
        coco_dict = {
            "info": {
                "description": "VOC to COCO Conversion",
                "version": "1.0",
                "year": time.strftime("%Y"),
                "date_created": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "licenses": [],
            "images": images,
            "annotations": annotations,
            "categories": categories_list
        }
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(coco_dict, f, indent=4, ensure_ascii=False)
            logger.info(f"VOC → COCO 轉換完成：{output_file}")
        except Exception as e:
            logger.error(f"寫入 COCO JSON {output_file} 時錯誤：{e}")

    # ---------------------------------------------------------
    # 8. COCO → LabelMe
    # ---------------------------------------------------------
    def convert_coco_to_labelme(self, input_file, images_folder, output_folder):
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
        except Exception as e:
            logger.error(f"讀取 COCO JSON {input_file} 失敗：{e}")
            return
        cat_mapping = {cat["id"]: cat["name"] for cat in coco_data.get("categories", [])}
        ann_dict = {}
        for ann in coco_data.get("annotations", []):
            img_id = ann["image_id"]
            ann_dict.setdefault(img_id, []).append(ann)
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        for img in coco_data.get("images", []):
            img_id = img["id"]
            filename = img["file_name"]
            width = img["width"]
            height = img["height"]
            img_path = os.path.join(images_folder, filename)
            if not os.path.exists(img_path):
                logger.warning(f"找不到圖片 {filename}，仍繼續建立 LabelMe JSON")
            shapes = []
            for ann in ann_dict.get(img_id, []):
                cat_id = ann["category_id"]
                label = cat_mapping.get(cat_id, str(cat_id))
                bbox = ann["bbox"]
                xmin, ymin, w, h = bbox
                xmax, ymax = xmin + w, ymin + h
                if self.target_shape == "polygon":
                    if ann.get("segmentation") and len(ann["segmentation"]) > 0:
                        poly = ann["segmentation"][0]
                        if len(poly) % 2 == 0:
                            points = [[poly[i], poly[i+1]] for i in range(0, len(poly), 2)]
                        else:
                            points = get_polygon_from_box(xmin, ymin, xmax, ymax)
                    else:
                        points = get_polygon_from_box(xmin, ymin, xmax, ymax)
                    shape_type = "polygon"
                else:
                    points = [[xmin, ymin], [xmax, ymax]]
                    shape_type = "rectangle"
                shapes.append({
                    "label": label,
                    "points": points,
                    "group_id": None,
                    "shape_type": shape_type,
                    "flags": {}
                })
            labelme_dict = {
                "version": "4.5.6",
                "flags": {},
                "shapes": shapes,
                "imagePath": filename,
                "imageData": None,
                "imageHeight": height,
                "imageWidth": width
            }
            json_filename = os.path.splitext(filename)[0] + '.json'
            output_json_path = os.path.join(output_folder, json_filename)
            try:
                with open(output_json_path, 'w', encoding='utf-8') as f:
                    json.dump(labelme_dict, f, indent=4, ensure_ascii=False)
                logger.info(f"COCO → LabelMe 轉換完成：{filename}")
            except Exception as e:
                logger.error(f"COCO → LabelMe 寫入 {filename} 時錯誤：{e}")

    # ---------------------------------------------------------
    # 9. COCO → VOC
    # ---------------------------------------------------------
    def convert_coco_to_voc(self, input_file, output_folder):
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
        except Exception as e:
            logger.error(f"讀取 COCO JSON {input_file} 失敗：{e}")
            return
        cat_mapping = {cat["id"]: cat["name"] for cat in coco_data.get("categories", [])}
        ann_dict = {}
        for ann in coco_data.get("annotations", []):
            img_id = ann["image_id"]
            ann_dict.setdefault(img_id, []).append(ann)
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        for img in coco_data.get("images", []):
            img_id = img["id"]
            filename = img["file_name"]
            width = img["width"]
            height = img["height"]
            objects = []
            for ann in ann_dict.get(img_id, []):
                cat_id = ann["category_id"]
                label = cat_mapping.get(cat_id, str(cat_id))
                bbox = ann["bbox"]  # [xmin, ymin, w, h]
                xmin, ymin, w_box, h_box = bbox
                xmax, ymax = xmin + w_box, ymin + h_box
                obj = {
                    "name": label,
                    "xmin": xmin,
                    "ymin": ymin,
                    "xmax": xmax,
                    "ymax": ymax
                }
                if self.target_shape == "polygon" and ann.get("segmentation"):
                    obj["polygon_points"] = [ann["segmentation"][0]]
                objects.append(obj)
            xml_str = self.create_voc_xml(filename, width, height, objects)
            output_xml = os.path.join(output_folder, os.path.splitext(filename)[0] + '.xml')
            try:
                with open(output_xml, 'w', encoding='utf-8') as f:
                    f.write(xml_str)
                logger.info(f"COCO → VOC 轉換完成：{filename}")
            except Exception as e:
                logger.error(f"COCO → VOC 寫入 {filename} 時錯誤：{e}")

    # ---------------------------------------------------------
    # 10. LabelMe → COCO
    # ---------------------------------------------------------
    def convert_labelme_to_coco(self, input_folder, output_file):
        images = []
        annotations = []
        categories = {}
        ann_id = 1
        img_id = 1
        json_files = [f for f in os.listdir(input_folder) if f.endswith('.json')]
        for json_file in json_files:
            json_path = os.path.join(input_folder, json_file)
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            except Exception as e:
                logger.error(f"讀取 LabelMe JSON {json_file} 失敗：{e}")
                continue
            filename = data.get("imagePath", json_file)
            width = data.get("imageWidth")
            height = data.get("imageHeight")
            if width is None or height is None:
                logger.error(f"LabelMe JSON {json_file} 無圖片尺寸資訊")
                continue
            images.append({
                "id": img_id,
                "file_name": filename,
                "width": width,
                "height": height
            })
            for shape in data.get("shapes", []):
                label = shape.get("label")
                if label not in categories:
                    categories[label] = len(categories) + 1
                cat_id = categories[label]
                points = shape.get("points")
                if not points or len(points) == 0:
                    logger.error(f"LabelMe JSON {json_file} 物件點資料錯誤")
                    continue
                if len(points) == 2:
                    xmin, ymin = points[0]
                    xmax, ymax = points[1]
                    segmentation = []
                else:
                    xmin, ymin, xmax, ymax = get_box_from_polygon(points)
                    if self.target_shape == "polygon":
                        # 將多邊形點扁平化
                        flat_points = sum(points, [])
                        segmentation = [flat_points]
                    else:
                        segmentation = []
                annotations.append({
                    "id": ann_id,
                    "image_id": img_id,
                    "category_id": cat_id,
                    "bbox": [xmin, ymin, xmax - xmin, ymax - ymin],
                    "area": (xmax - xmin) * (ymax - ymin),
                    "segmentation": segmentation,
                    "iscrowd": 0
                })
                ann_id += 1
            logger.info(f"處理 {json_file} 成功")
            img_id += 1
        categories_list = []
        for label, cid in categories.items():
            categories_list.append({
                "id": cid,
                "name": label,
                "supercategory": ""
            })
        coco_dict = {
            "info": {
                "description": "LabelMe to COCO Conversion",
                "version": "1.0",
                "year": time.strftime("%Y"),
                "date_created": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "licenses": [],
            "images": images,
            "annotations": annotations,
            "categories": categories_list
        }
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(coco_dict, f, indent=4, ensure_ascii=False)
            logger.info(f"LabelMe → COCO 轉換完成：{output_file}")
        except Exception as e:
            logger.error(f"寫入 COCO JSON {output_file} 時錯誤：{e}")

    # ---------------------------------------------------------
    # 11. YOLO → COCO
    # ---------------------------------------------------------
    def convert_yolo_to_coco(self, input_folder, output_file):
        images = []
        annotations = []
        categories = {}
        ann_id = 1
        img_id = 1
        img_extensions = ['.jpg', '.jpeg', '.png']
        files = os.listdir(input_folder)
        for file in files:
            ext = os.path.splitext(file)[1].lower()
            if ext in img_extensions:
                img_path = os.path.join(input_folder, file)
                try:
                    with Image.open(img_path) as img:
                        width, height = img.size
                except Exception as e:
                    logger.error(f"無法讀取圖片 {file}：{e}")
                    continue
                images.append({
                    "id": img_id,
                    "file_name": file,
                    "width": width,
                    "height": height
                })
                label_file = os.path.splitext(file)[0] + '.txt'
                label_path = os.path.join(input_folder, label_file)
                if not os.path.exists(label_path):
                    logger.error(f"找不到 YOLO 標註檔：{label_file}")
                    img_id += 1
                    continue
                with open(label_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                for line in lines:
                    tokens = line.strip().split()
                    if len(tokens) < 5:
                        logger.error(f"YOLO 標註檔 {label_file} 格式錯誤")
                        continue
                    class_id = tokens[0]
                    # 使用 yolo_label_map 轉換 label名稱
                    label = self.yolo_label_map.get(class_id, class_id)
                    if label not in categories:
                        categories[label] = len(categories) + 1
                    cat_id = categories[label]
                    x_center = float(tokens[1]) * width
                    y_center = float(tokens[2]) * height
                    box_w = float(tokens[3]) * width
                    box_h = float(tokens[4]) * height
                    xmin = x_center - box_w/2
                    ymin = y_center - box_h/2
                    segmentation = []
                    if self.target_shape == "polygon" and len(tokens) > 5:
                        poly_tokens = tokens[5:]
                        if len(poly_tokens) % 2 == 0:
                            poly = []
                            for i in range(0, len(poly_tokens), 2):
                                poly.append(float(poly_tokens[i]) * width)
                                poly.append(float(poly_tokens[i+1]) * height)
                            segmentation = [poly]
                    annotations.append({
                        "id": ann_id,
                        "image_id": img_id,
                        "category_id": cat_id,
                        "bbox": [xmin, ymin, box_w, box_h],
                        "area": box_w * box_h,
                        "segmentation": segmentation,
                        "iscrowd": 0
                    })
                    ann_id += 1
                logger.info(f"處理 {file} 成功")
                img_id += 1
        categories_list = []
        for label, cid in categories.items():
            categories_list.append({
                "id": cid,
                "name": label,
                "supercategory": ""
            })
        coco_dict = {
            "info": {
                "description": "YOLO to COCO Conversion",
                "version": "1.0",
                "year": time.strftime("%Y"),
                "date_created": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "licenses": [],
            "images": images,
            "annotations": annotations,
            "categories": categories_list
        }
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(coco_dict, f, indent=4, ensure_ascii=False)
            logger.info(f"YOLO → COCO 轉換完成：{output_file}")
        except Exception as e:
            logger.error(f"寫入 COCO JSON {output_file} 時錯誤：{e}")

    # ---------------------------------------------------------
    # 12. COCO → YOLO
    # ---------------------------------------------------------
    def convert_coco_to_yolo(self, input_file, images_folder, output_folder):
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
        except Exception as e:
            logger.error(f"讀取 COCO JSON {input_file} 失敗：{e}")
            return
        # 建立新類別映射：以 COCO 中 categories 的順序重新編號（從 0 起）
        cat_mapping = {}
        for idx, cat in enumerate(coco_data.get("categories", [])):
            cat_mapping[cat["id"]] = idx
        ann_dict = {}
        for ann in coco_data.get("annotations", []):
            img_id = ann["image_id"]
            ann_dict.setdefault(img_id, []).append(ann)
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        for img in coco_data.get("images", []):
            img_id = img["id"]
            filename = img["file_name"]
            width = img["width"]
            height = img["height"]
            yolo_lines = []
            for ann in ann_dict.get(img_id, []):
                new_class_id = cat_mapping.get(ann["category_id"], 0)
                bbox = ann["bbox"]
                xmin, ymin, bw, bh = bbox
                x_center = (xmin + bw / 2) / width
                y_center = (ymin + bh / 2) / height
                norm_w = bw / width
                norm_h = bh / height
                line = f"{new_class_id} {x_center:.6f} {y_center:.6f} {norm_w:.6f} {norm_h:.6f}"
                if self.target_shape == "polygon" and ann.get("segmentation") and len(ann["segmentation"]) > 0:
                    seg = ann["segmentation"][0]
                    # 將 segmentation 中的點按比例正規化
                    norm_seg = []
                    for i in range(0, len(seg), 2):
                        norm_seg.append(seg[i] / width)
                        norm_seg.append(seg[i+1] / height)
                    seg_str = " ".join(f"{p:.6f}" for p in norm_seg)
                    line += " " + seg_str
                yolo_lines.append(line)
            txt_filename = os.path.splitext(filename)[0] + '.txt'
            output_txt_path = os.path.join(output_folder, txt_filename)
            try:
                with open(output_txt_path, 'w', encoding='utf-8') as f:
                    for l in yolo_lines:
                        f.write(l + "\n")
                logger.info(f"COCO → YOLO 轉換完成：{filename}")
            except Exception as e:
                logger.error(f"COCO → YOLO 寫入 {filename} 時錯誤：{e}")

    # ---------------------------------------------------------
    # 產生 VOC XML 共用函式
    # ---------------------------------------------------------
    def create_voc_xml(self, image_file, img_width, img_height, objects):
        annotation = ET.Element("annotation")
        folder = ET.SubElement(annotation, "folder")
        folder.text = os.path.basename(os.path.dirname(image_file))
        filename_el = ET.SubElement(annotation, "filename")
        filename_el.text = os.path.basename(image_file)
        path = ET.SubElement(annotation, "path")
        path.text = os.path.abspath(image_file)
        source = ET.SubElement(annotation, "source")
        database = ET.SubElement(source, "database")
        database.text = "Unknown"
        size = ET.SubElement(annotation, "size")
        width_el = ET.SubElement(size, "width")
        width_el.text = str(img_width)
        height_el = ET.SubElement(size, "height")
        height_el.text = str(img_height)
        depth_el = ET.SubElement(size, "depth")
        depth_el.text = "3"
        segmented = ET.SubElement(annotation, "segmented")
        segmented.text = "0"
        for obj in objects:
            obj_el = ET.SubElement(annotation, "object")
            name_el = ET.SubElement(obj_el, "name")
            name_el.text = obj['name']
            pose = ET.SubElement(obj_el, "pose")
            pose.text = "Unspecified"
            truncated = ET.SubElement(obj_el, "truncated")
            truncated.text = "0"
            difficult = ET.SubElement(obj_el, "difficult")
            difficult.text = "0"
            bndbox = ET.SubElement(obj_el, "bndbox")
            xmin_el = ET.SubElement(bndbox, "xmin")
            xmin_el.text = str(int(obj['xmin']))
            ymin_el = ET.SubElement(bndbox, "ymin")
            ymin_el.text = str(int(obj['ymin']))
            xmax_el = ET.SubElement(bndbox, "xmax")
            xmax_el.text = str(int(obj['xmax']))
            ymax_el = ET.SubElement(bndbox, "ymax")
            ymax_el.text = str(int(obj['ymax']))
            if self.target_shape == "polygon" and obj.get("polygon_points"):
                poly = ET.SubElement(obj_el, "polygon")
                poly.text = json.dumps(obj["polygon_points"])
        rough_string = ET.tostring(annotation, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="    ")

    # ---------------------------------------------------------
    # 統一執行介面
    # ---------------------------------------------------------
    def run_conversion(self, conversion_type, input_path, output_path, images_folder=None):
        """
        conversion_type 可選模式：
          - 原有： "voc2labelme", "voc2yolo", "yolo2labelme", "yolo2voc", "labelme2voc", "labelme2yolo"
          - COCO 相關：
              "voc2coco"    : input_path 為 VOC XML 資料夾，output_path 為 COCO JSON 檔案
              "coco2labelme": input_path 為 COCO JSON 檔案，output_path 為 LabelMe JSON 資料夾，images_folder 為圖片資料夾（用於取得尺寸，可選）
              "coco2voc"    : input_path 為 COCO JSON 檔案，output_path 為 VOC XML 資料夾
              "labelme2coco": input_path 為 LabelMe JSON 資料夾，output_path 為 COCO JSON 檔案
              "yolo2coco"   : input_path 為含有圖片及 YOLO 標註檔之資料夾，output_path 為 COCO JSON 檔案
              "coco2yolo"   : input_path 為 COCO JSON 檔案，output_path 為 YOLO 標註輸出資料夾，images_folder 為圖片資料夾
        """
        conversion_funcs = {
            "voc2labelme": self.convert_voc_to_labelme,
            "voc2yolo": self.convert_voc_to_yolo,
            "yolo2labelme": self.convert_yolo_to_labelme,
            "yolo2voc": self.convert_yolo_to_voc,
            "labelme2voc": self.convert_labelme_to_voc,
            "labelme2yolo": self.convert_labelme_to_yolo,
            "voc2coco": self.convert_voc_to_coco,
            "coco2labelme": self.convert_coco_to_labelme,
            "coco2voc": self.convert_coco_to_voc,
            "labelme2coco": self.convert_labelme_to_coco,
            "yolo2coco": self.convert_yolo_to_coco,
            "coco2yolo": self.convert_coco_to_yolo,
        }
        if conversion_type in conversion_funcs:
            if conversion_type in ["coco2labelme", "coco2yolo"]:
                if images_folder is None:
                    logger.error(f"轉換模式 {conversion_type} 需要指定 images_folder")
                    return
                conversion_funcs[conversion_type](input_path, images_folder, output_path)
            else:
                conversion_funcs[conversion_type](input_path, output_path)
        else:
            logger.error(f"未知的轉換模式：{conversion_type}")

# -------------------------------
# 主程式：設定轉換模式與路徑
# -------------------------------
if __name__ == "__main__":
    # 選擇下列其中一項轉換模式：
    # "voc2labelme", "voc2yolo", "yolo2labelme", "yolo2voc", 
    # "labelme2voc", "labelme2yolo", "voc2coco", "coco2labelme",
    # "coco2voc", "labelme2coco", "yolo2coco", "coco2yolo"
    conversion_type = "labelme2yolo"  # 例如：此處示範 VOC → COCO

    # 根據轉換模式設定 input_path、output_path，若需要圖片資料夾則設定 images_folder
    input_path = r"D:\image\5_test_image\test_1"  # 例如 VOC XML 資料夾
    output_path = r"D:\image\5_test_image\test_2" # 例如 COCO JSON 輸出完整路徑

    # 若轉換模式需要 images_folder (如 coco2labelme 或 coco2yolo)，請指定；否則設為 None
    images_folder = None  # 如有需要可指定

    # 設定 target_shape，可選 "box" 或 "polygon"
    target_shape = "polygon"

    converter = AnnotationConverter(target_shape=target_shape)
    converter.run_conversion(conversion_type, input_path, output_path, images_folder)
