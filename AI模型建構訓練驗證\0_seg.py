# 模型架構與資料集讀取
# from model_create.encoder.mobilev3seg.mobilenetv3_seg import MobileNetV3Seg
from model_create.full_model.deeplab.modeling import deeplabv3_resnet50

# from model_create.UNet import UNet
# from model_create.Models.CSP_IFormer_final_SegMode import iformer_small
# from model_create.decoder.BiFPN_2_CSPIformer import Bi<PERSON>NDecoder
# from model_create.head.heads import SegmentationHead
# from model_create.util.encoder_decoder_cat import Encoder_decoder

from model_create.util.Dataset_read import YOLODataset ,LabelmeDataset
from model_create.util.metrics import get_metrics, runningScore
from model_create.util.train_function import train, validate, test, inference
from model_create.util.show_img import show_img
from model_create.util.losses import StagedRoadDamageLoss
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint

# import
import logging
import os
import time
import wandb
from IPython.display import clear_output
import datetime
import torch
import numpy as np
import random
import matplotlib.pyplot as plt
import albumentations as A
from albumentations.pytorch import ToTensorV2

# pytorch
from torch.optim import AdamW
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False
# 設定日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Main')

def setup_seed(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.enabled = False


# set random seed
setup_seed(3407)

# 設定參數
use_wandb = True
use_checkpoint = False  # 新增: 是否使用檢查點繼續訓練
modelname = 'mobilenetv3seg'
image_size = 384
learning_rate = 1e-3
train_epochs = 500
batch_size = 48
n_classes = 6
num_workers = 0
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
# class_names = {0: '背景', 1: '伸縮縫', 2: '路面接縫', 3: '裂縫', 4: '坑洞', 5: '補綻'}
class_mapping = {'bird': 0, 'block': 1,
                 'retaining seat': 2, 'treefish': 3, 'tpe': 4}
class_names = {0: '背景', 1: '鳥', 2: '方塊', 3: '擋土座', 4: '樹魚', 5: '台北蓋水特色框蓋'}


# dataset from
data_dir = r'D:\image\5_test_image'
train_dir = os.path.join(data_dir, 'train')  # 訓練資料目錄
val_dir = os.path.join(data_dir, 'val')  # 驗證資料目錄
test_dir = os.path.join(data_dir, 'test_2')  # 測試資料目錄

# 取得當前時間
current_time = datetime.datetime.now()
# 格式化時間戳
timestamp = current_time.strftime("%Y%m%d_%H%M%S")
# running data savepath
path_total = r'.\run_data'
if not os.path.isdir(path_total):
    os.mkdir(path_total)

# 檢查是否使用檢查點
latest_checkpoint_dir = None
if use_checkpoint:
    latest_checkpoint_dir = find_latest_checkpoint(path_total, modelname)

    if latest_checkpoint_dir:
        logger.info(f"找到最新檢查點目錄: {latest_checkpoint_dir}")
        runpath = os.path.basename(latest_checkpoint_dir)
        # 使用現有目錄
        path_img = os.path.join(latest_checkpoint_dir, 'img')
        path_weight = os.path.join(latest_checkpoint_dir, 'weight')
    else:
        logger.info(f"未找到檢查點，將創建新目錄")
        # 使用新時間戳創建新目錄
        runpath = f'/{modelname}_{timestamp}'
        if not os.path.isdir(path_total+runpath):
            os.mkdir(path_total+runpath)
        path_img = path_total+runpath+r'\img'
        if not os.path.isdir(path_img):
            os.mkdir(path_img)
        path_weight = path_total+runpath+r'\weight'
        if not os.path.isdir(path_weight):
            os.mkdir(path_weight)
else:
    # 這次要跑甚麼樣的資料
    runpath = f'/{modelname}_{timestamp}'
    if not os.path.isdir(path_total+runpath):
        os.mkdir(path_total+runpath)
    # 資料存放位置
    path_img = path_total+runpath+r'\img'
    if not os.path.isdir(path_img):
        os.mkdir(path_img)
    path_weight = path_total+runpath+r'\weight'
    if not os.path.isdir(path_weight):
        os.mkdir(path_weight)

logger.info(f"使用設備: {device}")
logger.info(f"類別數量: {n_classes}")
logger.info(f"批次大小: {batch_size}")
logger.info(f"圖像大小: {image_size}x{image_size}")
logger.info(f"圖像存放位置:{path_img}")
logger.info(f"權重存放位置:{path_weight}")

# wandb
if use_wandb:
    wandb.init(project="road_crack", name="mobilenetv3seg")
    wandb.config.update({
        "learning_rate": learning_rate,
        "epochs": train_epochs,
        "batch_size": batch_size,
        "image_size": image_size,
        "n_classes": n_classes,
        "optimizer": "AdamW",
        "loss_function": "StagedRoadDamageLoss"
    })


# train Transform
train_transform = A.Compose([
    A.RandomResizedCrop(size=(image_size, image_size),
                        scale=(0.6, 1.0), p=0.7),
    A.Resize(height=image_size, width=image_size),
    A.VerticalFlip(),
    A.HorizontalFlip(),
    A.RandomBrightnessContrast(p=0.2),
    A.RandomRotate90(p=0.2),
    A.Rotate(),
    A.Normalize(),
    ToTensorV2(),
], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))

# val/test Transform
val_transform = A.Compose([
    A.Resize(height=image_size, width=image_size),
    A.Normalize(),
    ToTensorV2(),
], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))

# 創建資料集
logger.info("建立資料集...")

train_dataset = YOLODataset(
    data_dir=train_dir,
    size=image_size,
    split='train',
    transform=train_transform,
    log_enabled=False
)
train_loader = DataLoader(
    train_dataset, batch_size=batch_size, shuffle=True, pin_memory=True)

val_dataset = YOLODataset(
    data_dir=val_dir,
    size=train_dir,
    split='val',
    transform=val_transform,
    log_enabled=False
)
val_loader = DataLoader(val_dataset, batch_size=batch_size,
                        shuffle=False, pin_memory=True)

test_dataset = YOLODataset(
    data_dir=test_dir,
    size=image_size,
    split='test',
    transform=val_transform,
    log_enabled=False
)
test_loader = DataLoader(test_dataset, batch_size=1,
                         shuffle=False, pin_memory=True)

print(f"train 資料集大小: {len(train_dataset)}")
print(f"valid 資料集大小: {len(val_dataset)}")
print(f"test 資料集大小: {len(test_dataset)}")

# 獲取並視覺化一個資料項
if len(train_dataset) > 0:
    idx = 0
    image, mask, filename = train_dataset[idx]
    print(f"圖片 shape: {image.shape}")
    print(f"掩碼 shape: {mask.shape}")
    print(f'file name: {filename}')

    # 視覺化
    train_dataset.visualize(idx, class_names=class_names)


if len(val_dataset) > 0:
    idx = 0
    image, mask, filename = val_dataset[idx]
    print(f"圖片 shape: {image.shape}")
    print(f"掩碼 shape: {mask.shape}")
    print(f'file name: {filename}')

    # 視覺化
    val_dataset.visualize(idx, class_names=class_names)

if len(test_dataset) > 0:
    idx = 0
    image, mask, filename = test_dataset[idx]
    print(f"圖片 shape: {image.shape}")
    print(f"掩碼 shape: {mask.shape}")
    print(f'file name: {filename}')

    # 視覺化
    test_dataset.visualize(idx, show_n=3, class_names=class_names)

# input = torch.randn(1, 3, 768, 768).to(device)

model = MobileNetV3Seg(n_classes, backbone='mobilenetv3_large',
                       pretrained_base=False).to(device)
# out = model(input)
# for o in out:
#     print(o.shape)
# # 如果有預訓練模型
# pretrain_weight_path=torch.load(r'D:\new_model_pipline\run_data\unet_20250419_095319\weight\epoch0198_f1_0.5010_best.pth')
# # 過濾掉不匹配的輸出層參數
# filtered_weights = {k: v for k, v in pretrain_weight_path.items() if 'outc.conv' not in k}

# model.load_state_dict(pretrain_weight_path, strict=False)


# 如果使用檢查點並找到了最新檢查點
if use_checkpoint and latest_checkpoint_dir:
    model, start_epoch, best_F1 = load_checkpoint(
        model, path_weight, best=True)
    logger.info(f"已加載檢查點，從第 {start_epoch} 輪開始訓練，最佳 F1 分數為 {best_F1:.4f}")

# 設定loss
# 例如，階段 1: 100 epochs, 階段 2: 100 epochs, 階段 3: 100 epochs, 階段 4: 100 epochs, 階段 5: 100 epochs
stage_epochs = [100, 100, 100, 100, 100]
loss_fn = StagedRoadDamageLoss(
    total_epochs=train_epochs, stage_epochs=stage_epochs)
# 設定優化器和學習率
optimizer = AdamW(model.parameters(), lr=learning_rate)
# 設定學習率衰減方法
scheduler = CosineAnnealingWarmRestarts(
    optimizer, T_0=50, T_mult=2, eta_min=1e-8)


logger.info(f"優化器: AdamW, 初始學習率: {optimizer.param_groups[0]['lr']}")
logger.info(f"學習率調度器: CosineAnnealingWarmRestarts")
logger.info(f"損失函數: StagedRoadDamageLoss")

# 開始訓練
best_F1 = 0
lr_list = []

# 用於保存每輪的損失值
loss_all_epochs = []

# 用於保存不同指標
Specificity_ = []
Senstivity_ = []
F1_ = []
acc_ = []
js_ = []

# 訓練循環中增加損失監控
ce_losses = []
dice_losses = []
focal_losses = []
boundary_losses = []


logger.info("開始訓練...")

for epoch in range(train_epochs):

    # 更新損失函數的 epoch
    loss_fn.update_epoch(epoch)
    # 訓練
    print(
        f"-----------------------------Epoch {epoch + 1:04d}|{train_epochs:04d}-----------------------------")
    t1 = time.time()
    input_img, target_img, pred_img, (loss_i, loss_comps) = train(
        train_loader, model, optimizer, epoch, train_epochs, scheduler, device, loss_fn)
    loss_all_epochs.append(sum(loss_i)/len(loss_i))

    # 記錄各組件平均損失
    if loss_comps['ce']:
        ce_losses.append(sum(loss_comps['ce'])/len(loss_comps['ce']))
    if loss_comps['dice']:
        dice_losses.append(sum(loss_comps['dice'])/len(loss_comps['dice']))
    if loss_comps['focal']:
        focal_losses.append(sum(loss_comps['focal'])/len(loss_comps['focal']))
    if loss_comps['boundary']:
        boundary_losses.append(
            sum(loss_comps['boundary'])/len(loss_comps['boundary']))

    t2 = time.time()

    # 驗證
    t3 = time.time()
    val_input, val_mask, val_pred, score = validate(
        val_loader, model, epoch, train_epochs, n_classes, device, get_metrics, runningScore)
    t4 = time.time()

    # 清除輸出並顯示結果
    clear_output(wait=True)
    print(
        f"-----------------------------Epoch {epoch + 1:04d}|{train_epochs:04d}-----------------------------")
    print(f'當前學習率: {optimizer.param_groups[0]["lr"]}')
    lr_list.append(f'{optimizer.param_groups[0]["lr"]:.9f}')
    print(f"訓練時間: {t2-t1:.2f}s, 訓練損失: {(sum(loss_i)/len(loss_i)):.4f}")

    # 顯示訓練樣本
    show_img(epoch+1, input_img, target_img, pred_img,
             path_img, 'train', class_names=class_names)

    print(f"驗證時間: {t4-t3:.2f}s")
    # 顯示驗證樣本
    show_img(epoch+1, val_input, val_mask, val_pred,
             path_img, 'Valid', class_names=class_names)

    # 在顯示訓練和驗證樣本之後添加
    train_img = show_img(epoch+1, input_img, target_img,
                         pred_img, path_img, 'train', return_fig=True, class_names=class_names)
    val_img = show_img(epoch+1, val_input, val_mask, val_pred,
                       path_img, 'Valid', return_fig=True, class_names=class_names)
    if use_wandb:
        wandb.log({
            "train_visualization": wandb.Image(train_img),
            "validation_visualization": wandb.Image(val_img)
        })

    print("各項指標: ", score)
    print(f"當前損失階段: {epoch // (train_epochs // 5) + 1}/5")
    # 打印最新的損失組件值
    print(f"損失組件: CE={ce_losses[-1]:.4f}, Dice={dice_losses[-1]:.4f}, "
          f"Focal={focal_losses[-1]:.4f}, Boundary={boundary_losses[-1]:.4f}")

    # 保存最佳模型
    if score["F1"] > best_F1:
        best_F1 = score["F1"]
        print('最佳 F1: ', best_F1)
        model_path = f'{path_weight}/epoch{epoch + 1:04d}_f1_{best_F1:.4f}_best.pth'
        torch.save(model.state_dict(), model_path)
        print('保存權重...')

    # 保存最後一輪的模型
    if epoch == (train_epochs - 1):
        torch.save(model.state_dict(),
                   f'{path_weight}/epoch{epoch + 1:04d}_{best_F1:.4f}_last.pth')

    # 收集指標
    Specificity_.append(score["Specificity"])
    Senstivity_.append(score["Sensitivity"])
    F1_.append(score["F1"])
    acc_.append(score["acc"])
    js_.append(score["js"])

    # 打印每個類別的指標
    print("\n每個類別的指標:")
    for class_idx in range(n_classes):
        print(f"類別 {class_idx}:")
        print(f"  F1: {score['F1_per_class'][class_idx]:.4f}")
        print(f"  IoU: {score['IoU'][class_idx]:.4f}")
        print(f"  Dice: {score['Dice_per_class'][class_idx]:.4f}")
        print(f"  Precision: {score['Precision_per_class'][class_idx]:.4f}")
        print(
            f"  Sensitivity: {score['Sensitivity_per_class'][class_idx]:.4f}")

    if use_wandb:
        # wandb 追蹤
        wandb_log_dict = {
            "epoch": epoch + 1,
            "loss": sum(loss_i)/len(loss_i),
            "learning_rate": float(optimizer.param_groups[0]["lr"]),
            "F1": score["F1"],
            "Specificity": score["Specificity"],
            "Sensitivity": score["Sensitivity"],
            "Accuracy": score["acc"],
            "Jaccard_Score": score["js"],
            "MIoU": score["MIoU"],
            "Pixel_Accuracy": score["Pixel_Accuracy"]
        }

        # 添加每個類別的指標
        for class_idx in range(n_classes):
            wandb_log_dict[f"class_{class_idx}_F1"] = score['F1_per_class'][class_idx]
            wandb_log_dict[f"class_{class_idx}_IoU"] = score['IoU'][class_idx]
            wandb_log_dict[f"class_{class_idx}_Dice"] = score['Dice_per_class'][class_idx]

        wandb.log(wandb_log_dict)


logger.info("訓練完成，繪製指標曲線...")

# 訓練結束，繪製指標曲線
plt.figure(figsize=(15, 10))
epochs = range(start_epoch + 1, start_epoch + len(loss_all_epochs) + 1)

plt.subplot(2, 2, 1)
plt.plot(epochs, loss_all_epochs, 'b-', label='訓練損失')
plt.title('訓練損失')
plt.xlabel('輪次')
plt.ylabel('損失')
plt.legend()

plt.subplot(2, 2, 2)
plt.plot(epochs, F1_, 'r-', label='F1 分數')
plt.title('F1 分數')
plt.xlabel('輪次')
plt.ylabel('F1')
plt.legend()

plt.subplot(2, 2, 3)
plt.plot(epochs, acc_, 'g-', label='準確率')
plt.plot(epochs, js_, 'c-', label='Jaccard 分數')
plt.title('準確率和 Jaccard 分數')
plt.xlabel('輪次')
plt.ylabel('分數')
plt.legend()

plt.subplot(2, 2, 4)
plt.plot(epochs, Specificity_, 'm-', label='特異性')
plt.plot(epochs, Senstivity_, 'y-', label='敏感性')
plt.title('特異性和敏感性')
plt.xlabel('輪次')
plt.ylabel('分數')
plt.legend()

plt.tight_layout()
plt.savefig(os.path.join(path_img, 'training_metrics.png'))
plt.show()

# 損失組件圖
plt.figure(figsize=(15, 6))

plt.subplot(1, 2, 1)
plt.plot(epochs, ce_losses, 'b-', label='CE 損失')
plt.plot(epochs, dice_losses, 'r-', label='Dice 損失')
plt.title('CE 和 Dice 損失')
plt.xlabel('輪次')
plt.ylabel('損失')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(epochs, focal_losses, 'g-', label='Focal 損失')
plt.plot(epochs, boundary_losses, 'm-', label='邊界損失')
plt.title('Focal 和邊界損失')
plt.xlabel('輪次')
plt.ylabel('損失')
plt.legend()

plt.tight_layout()
plt.savefig(os.path.join(path_img, 'loss_components.png'))
plt.show()

logger.info("開始測試...")

# 加載最佳模型進行測試
best_model_path = f'{path_weight}/epoch{start_epoch + len(F1_):04d}_f1_{best_F1:.4f}_best.pth'
if os.path.exists(best_model_path):
    model.load_state_dict(torch.load(best_model_path))
    logger.info(f"已加載最佳模型: {best_model_path}")

    # 進行測試
    test_score = test(test_loader, model, n_classes, path_img,
                      device, get_metrics, runningScore)
    logger.info("測試完成!")
    logger.info("測試結果:")
    for key, value in test_score.items():
        if isinstance(value, float):
            logger.info(f"{key}: {value:.4f}")

    # 打印每個類別的測試指標
    logger.info("\n每個類別的測試指標:")
    for class_idx in range(n_classes):
        logger.info(f"類別 {class_idx}:")
        logger.info(f"  F1: {test_score['F1_per_class'][class_idx]:.4f}")
        logger.info(f"  IoU: {test_score['IoU_per_class'][class_idx]:.4f}")
        logger.info(f"  Dice: {test_score['Dice_per_class'][class_idx]:.4f}")
        logger.info(
            f"  Precision: {test_score['Precision_per_class'][class_idx]:.4f}")
        logger.info(
            f"  Sensitivity: {test_score['Sensitivity_per_class'][class_idx]:.4f}")
        logger.info(
            f"  Specificity: {test_score['Specificity_per_class'][class_idx]:.4f}")

    if use_wandb:
        # 記錄測試指標到 wandb
        test_log_dict = {
            "test_F1": test_score["F1"],
            "test_MIoU": test_score["MIoU"],
            "test_Pixel_Accuracy": test_score["Pixel_Accuracy"],
            "test_Specificity": test_score["Specificity"],
            "test_Sensitivity": test_score["Sensitivity"],
            "test_Precision": test_score["Precision"]
        }

        # 加入每個類別的測試指標
        for class_idx in range(n_classes):
            test_log_dict[f"test_class_{class_idx}_F1"] = test_score['F1_per_class'][class_idx]
            test_log_dict[f"test_class_{class_idx}_IoU"] = test_score['IoU_per_class'][class_idx]
            test_log_dict[f"test_class_{class_idx}_Dice"] = test_score['Dice_per_class'][class_idx]

        wandb.log(test_log_dict)

logger.info("訓練、驗證和測試完成!")

# 返回訓練結果
results = {
    'best_F1': best_F1,
    'loss_all_epochs': loss_all_epochs,
    'Specificity': Specificity_,
    'Sensitivity': Senstivity_,
    'F1': F1_,
    'acc': acc_,
    'js': js_,
    'ce_losses': ce_losses,
    'dice_losses': dice_losses,
    'focal_losses': focal_losses,
    'boundary_losses': boundary_losses,
    'model_path': best_model_path
}

if use_wandb:
    wandb.finish()


# 加載最佳模型進行測試
modelname = 'mobilenetv3seg'
best_model_path = r'D:\new_model_pipline\run_data\mobilenetv3seg_20250423_111824\weight\epoch0428_f1_0.7944_best.pth'
path_img = f'./{modelname}_test_onroad'

if os.path.exists(best_model_path):
    model.load_state_dict(torch.load(best_model_path))
    logger.info(f"已加載最佳模型: {best_model_path}")

    # 進行測試
    test_score = test(test_loader, model, n_classes, path_img,
                      device, get_metrics, runningScore, class_names)


if use_wandb:
    wandb.finish()

import os
import time
import csv
import torch
import numpy as np
import pandas as pd
import cv2
from tqdm import tqdm
import albumentations as A
from albumentations.pytorch import ToTensorV2
import glob
from torch.utils.data import Dataset, DataLoader

# 1. 創建自定義數據集
class RoadDamageDataset(Dataset):
    def __init__(self, image_dir, transform=None, image_size=512):
        self.image_paths = glob.glob(os.path.join(image_dir, "*.jpg"))
        self.transform = transform
        self.image_size = image_size
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 獲取檔名（不含路徑和副檔名）
        filename = os.path.basename(image_path)
        
        # 初始化空的 bounding boxes 和類別 ID (僅用於測試)
        boxes = []
        category_ids = []
        
        if self.transform:
            transformed = self.transform(
                image=image, 
                bboxes=boxes,  # 空列表，因為是測試集
                category_ids=category_ids  # 空列表，因為是測試集
            )
            image = transformed["image"]
        
        return image, None, filename  # 返回圖像、標籤（測試時為None）和檔名

# 2. 定義推理函數
def inference(test_loader, model, path_img, device, class_names=None):
    """
    推理函數，進行預測並保存結果，同時應用掩碼處理和產生破壞統計CSV
    
    參數:
        test_loader: 測試資料加載器
        model: 模型
        path_img: 圖像保存路徑
        device: 計算設備
        class_names: 類別名稱字典 (可選)
    """
    
    # 設定模型為評估模式
    model.eval()
    total_time = 0
    total_frames = 0
    
    # 創建進度條
    infer_loop = tqdm(enumerate(test_loader), total=len(test_loader))
    
    # 確保輸出路徑存在
    os.makedirs(path_img, exist_ok=True)
    
    # 用於CSV輸出的數據列表
    csv_data = {
        "序號": [],
        "檔案名稱": [],
        "破壞": [],
        "長": [],
        "寬": []
    }
    
    record_count = 0  # 記錄計數
    
    def apply_mask(image, mask, class_name, filename):
        """
        應用掩碼到圖像並計算破損統計資訊
        
        參數:
            image: 原始圖像
            mask: 預測的掩碼
            class_name: 類別名稱
            filename: 檔案名稱
            
        返回:
            應用掩碼後的圖像和破損詳情列表
        """
        nonlocal record_count
        
        # 確保掩碼是二值化的
        binary = (mask > 0.5).astype(np.uint8) * 255
        
        # 尋找輪廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        
        # 破損詳情列表
        damage_details = []
        
        # 為每個輪廓計算統計資訊
        for contour in contours:
            # 計算面積（像素）
            area_pixels = cv2.contourArea(contour)
            
            # 獲取邊界框
            rect = cv2.boundingRect(contour)
            x, y, w, h = rect
            
            # 篩選過小的區域（可自行調整閾值）
            min_area = 100  # 像素
            if area_pixels < min_area:
                continue
            
            # 假設比例尺（每像素對應的實際距離，單位：米）
            # 實際應用時需要根據相機參數和拍攝距離進行校正
            scale = 0.01  # 每像素0.01米，僅為示例
            
            # 計算實際長寬（米）
            length = h * scale
            width = w * scale
            
            # 增加記錄計數
            record_count += 1
            
            # 添加到CSV數據
            csv_data["序號"].append(record_count)
            csv_data["檔案名稱"].append(filename)
            csv_data["破壞"].append(class_name)
            csv_data["長"].append(f"{length:.2f}")
            csv_data["寬"].append(f"{width:.2f}")
            
            # 繪製輪廓到圖像
            color = (0, 255, 0)  # 綠色，可以根據類別設定不同顏色
            cv2.drawContours(image, [contour], -1, color, 2)
            
            # 添加文字標籤
            label = f"{class_name} {length:.2f}x{width:.2f}m"
            cv2.putText(
                image, 
                label, 
                (x, y - 10), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                0.5, 
                color, 
                2
            )
            
            # 添加到破損詳情
            damage_details.append({
                "class": class_name,
                "length": length,
                "width": width
            })
        
        return image, damage_details
    
    with torch.no_grad():
        for image_num, (images, *extra) in infer_loop:
            # 處理可能的不同數據加載格式
            filenames = None
            if len(extra) > 0:
                if len(extra) > 1:
                    filenames = extra[1]
                
            # 將圖像移至設備
            images = images.to(device, non_blocking=True)
            
            # 模型預測
            start_time = time.time()
            # 假設模型輸出包含掩碼和類別
            predictions = model(images)
            end_time = time.time()
            
            inference_time = end_time - start_time
            total_time += inference_time
            total_frames += images.size(0)
            
            # 更新進度條
            infer_loop.set_postfix(
                Progress=f"{image_num+1}/{len(test_loader)}",
                Time=f"{inference_time:.4f}s"
            )
            
            # 處理預測結果
            for i in range(images.size(0)):
                # 獲取圖像
                image = images[i].cpu().numpy().transpose(1, 2, 0)  # [C, H, W] -> [H, W, C]
                
                # 標準化到0-255範圍（如果需要）
                if image.max() <= 1.0:
                    image = (image * 255).astype(np.uint8)
                
                # 處理 Albumentations 標準化的影響
                # 因為 A.Normalize() 會將圖像轉換為均值0標準差1，需要轉回正常範圍
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                image = std.reshape(1, 1, 3) * image + mean.reshape(1, 1, 3)
                image = np.clip(image * 255, 0, 255).astype(np.uint8)
                
                # 獲取檔名
                if filenames:
                    filename = filenames[i]
                else:
                    filename = f"image_{image_num}_{i}"
                
                # 假設predictions包含掩碼和類別
                # 這部分需要根據你的模型輸出格式進行調整
                if isinstance(predictions, tuple) and len(predictions) >= 2:
                    # 例如: logits, masks = predictions
                    logits, masks = predictions[:2]
                    
                    # 獲取類別預測
                    _, class_idx = torch.max(logits[i], dim=0)
                    class_idx = class_idx.item()
                    
                    # 獲取掩碼
                    mask = masks[i].cpu().numpy()
                    
                    # 如果有多類別掩碼，則每個類別獨立處理
                    if len(mask.shape) == 3:  # [num_classes, H, W]
                        processed_image = image.copy()
                        
                        for c in range(mask.shape[0]):
                            # 檢查當前類別是否有掩碼
                            if np.max(mask[c]) > 0.5:
                                # 獲取類別名稱
                                class_name = class_names[c] if class_names and c < len(class_names) else f"Class {c}"
                                
                                # 應用掩碼
                                processed_image, _ = apply_mask(
                                    processed_image, 
                                    mask[c], 
                                    class_name,
                                    filename
                                )
                    else:  # 單一掩碼
                        # 獲取類別名稱
                        class_name = class_names[class_idx] if class_names and class_idx < len(class_names) else f"Class {class_idx}"
                        
                        # 應用掩碼
                        processed_image, _ = apply_mask(
                            image.copy(), 
                            mask, 
                            class_name,
                            filename
                        )
                else:
                    # 如果僅有類別預測，直接保存
                    _, pred = torch.max(predictions[i], dim=0)
                    pred = pred.cpu().numpy()
                    
                    # 創建簡單的可視化
                    processed_image = image.copy()
                    class_idx = np.unique(pred[pred > 0])
                    
                    for c in class_idx:
                        # 獲取類別掩碼
                        mask = (pred == c).astype(np.uint8)
                        
                        # 獲取類別名稱
                        class_name = class_names[c] if class_names and c < len(class_names) else f"Class {c}"
                        
                        # 應用掩碼
                        processed_image, _ = apply_mask(
                            processed_image, 
                            mask, 
                            class_name,
                            filename
                        )
                
                # 保存處理後的圖像
                save_path = os.path.join(path_img, f"{filename}_result.jpg")
                cv2.imwrite(save_path, cv2.cvtColor(processed_image, cv2.COLOR_RGB2BGR))
    
    # 計算並輸出性能
    fps = total_frames / total_time if total_time > 0 else 0
    print(f"\n推理性能指標:")
    print(f"總推理時間: {total_time:.2f} 秒")
    print(f"總幀數: {total_frames}")
    print(f"平均FPS: {fps:.2f}")
    
    # 保存性能指標
    with open(os.path.join(path_img, 'inference_performance.csv'), 'w', newline='', encoding='utf_8_sig') as f:
        writer = csv.writer(f)
        writer.writerow(['總推理時間(秒)', '總幀數', '平均FPS'])
        writer.writerow([f"{total_time:.2f}", total_frames, f"{fps:.2f}"])
    
    # 保存破損統計CSV
    df = pd.DataFrame(csv_data)
    csv_path = os.path.join(path_img, 'damage_statistics.csv')
    df.to_csv(csv_path, index=False, encoding='utf_8_sig')
    
    print(f"推理結果已保存至 {path_img} 目錄")
    print(f"破損統計已保存至 {csv_path}")
    return fps

# 3. 主程式
def main():
    # 設定參數
    test_data_path = r"D:\image\5_test_image_test\0516"  # 替換為你的測試圖像目錄
    model_path = "path/to/model.pth"        # 替換為你的模型權重文件
    output_path = "path/to/output"          # 替換為你想要保存結果的目錄
    image_size = 384                        # 圖像大小
    
    # 類別名稱
    class_names = [
        "背景",  # 索引0通常是背景
        "裂縫",
        "坑洞",
        "龜裂",
        "人孔蓋",
        # 添加其他類別...
    ]
    
    # 設定設備
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用設備: {device}")
    
    # 創建數據轉換
    val_transform = A.Compose([
        A.Resize(height=image_size, width=image_size),
        A.Normalize(),
        ToTensorV2(),
    ], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))
    
    # 創建數據集和數據加載器
    test_dataset = RoadDamageDataset(
        image_dir=test_data_path,
        transform=val_transform,
        image_size=image_size
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=1,  # 通常推理時使用batch_size=1
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    

    
    model.load_state_dict(torch.load(best_model_path))
    model = model.to(device)
    
    # 運行推理
    fps = inference(
        test_loader=test_loader,
        model=model,
        path_img=output_path,
        device=device,
        class_names=class_names
    )
    
    print(f"推理完成！平均FPS: {fps:.2f}")

if __name__ == "__main__":
    main()

# from model_create.encoder.mobilev3seg.mobilenetv3_seg import MobileNetV3Seg
from model_create.deeplab.modeling import deeplabv3_mobilenet
from model_create.util.Dataset_read import YOLODataset ,convert_labelme_to_yolo
from model_create.util.metrics import get_metrics, runningScore
from model_create.util.train_function import train, validate, test, inference
from model_create.util.show_img import show_img
from model_create.util.losses import StagedRoadDamageLoss
from model_create.util.checkpoint import find_latest_checkpoint, load_checkpoint
import albumentations as A
from albumentations.pytorch import ToTensorV2
import os
import time
import csv
import torch
import numpy as np
import pandas as pd
import cv2
from tqdm import tqdm
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import glob
from PIL import Image

def cv_imread(file_path):
    """從檔案路徑載入影像並解碼"""
    cv_img = cv2.imdecode(np.fromfile(file_path, dtype=np.uint8), -1)
    return cv_img

# 1. 創建自定義數據集
class RoadDamageDataset(Dataset):
    def __init__(self, image_dir, transform=None, image_size=512):
        self.image_paths = glob.glob(os.path.join(image_dir, "*.jpg"))
        self.transform = transform
        self.image_size = image_size
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = cv_imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 獲取檔名（不含路徑和副檔名）
        filename = os.path.basename(image_path)
        
        # 應用轉換 - 正確的方式
        if self.transform:
            # 注意：在測試集中，我們沒有 bboxes 和 category_ids
            # 所以我們使用基本的轉換而不是 bbox 轉換
            transformed = self.transform(image=image)
            image = transformed["image"]
        
        return image, filename  # 返回圖像、標籤（測試時為None）和檔名



# 4. 主程式
def main():
    # 設定參數
    # test_data_path = r"D:\image\5_test_image_test\0516"  # 替換為你的測試圖像目錄
    test_data_path = r"D:\image\5_test_image_test\0220台61三車道"  # 替換為你的測試圖像目錄
    
    model_path = r"D:\new_model_pipline\run_data\deeplabV3_Mobilenetv2_2569\weight\epoch0391_f1_0.8454_best.pth"        # 替換為你的模型權重文件
    output_path = r".\deeplabv3Plus_Mobilenetv2_onroad_0220"          # 替換為你想要保存結果的目錄
    n_classes = 6
    image_size = 384
    # 類別名稱
    class_names = {0: '背景', 1: '伸縮縫', 2: '路面接縫', 3: '裂縫', 4: '坑洞', 5: '補綻'}
    
    # 設定設備
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用設備: {device}")
    
    # 創建數據集和數據加載器
    val_transform = A.Compose([
        A.Resize(height=image_size, width=image_size),
        A.Normalize(),
        ToTensorV2(),
    ])
    
    # 創建數據集和數據加載器
    test_dataset = RoadDamageDataset(
        image_dir=test_data_path,
        transform=val_transform,
        image_size=image_size
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=1,  # 通常推理時使用batch_size=1
        shuffle=False,
        num_workers=0,
        pin_memory=True
    )
    
    # 載入模型
    model = deeplabv3_mobilenet(num_classes=n_classes).to(device)   
    model.load_state_dict(torch.load(model_path))
    model = model.to(device)
    
    # 運行推理
    fps = inference(
        test_loader=test_loader,
        model=model,
        path_img=output_path,
        device=device,
        class_names=class_names
    )
    
    print(f"推理完成！平均FPS: {fps:.2f}")

if __name__ == "__main__":
    main()