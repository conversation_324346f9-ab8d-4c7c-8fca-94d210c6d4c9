"""
基礎解碼器類別：為所有解碼器提供統一接口
支持分割、檢測等不同任務的解碼器
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import torch
import torch.nn as nn


class BaseDecoder(nn.Module, ABC):
    """解碼器基礎類別"""
    
    def __init__(self,
                 encoder_channels: List[int],
                 decoder_channels: List[int] = None,
                 num_classes: int = 1,
                 **kwargs):
        """
        初始化解碼器
        
        Args:
            encoder_channels: 編碼器輸出通道數列表
            decoder_channels: 解碼器通道數列表
            num_classes: 輸出類別數
            **kwargs: 其他配置參數
        """
        super().__init__()
        self.encoder_channels = encoder_channels
        self.decoder_channels = decoder_channels or [256, 128, 64, 32]
        self.num_classes = num_classes
        self.config = kwargs
        
        # 構建解碼器
        self._build_decoder()
        
        # 初始化權重
        self._init_weights()
    
    @abstractmethod
    def _build_decoder(self):
        """構建解碼器架構，子類必須實現"""
        pass
    
    @abstractmethod
    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """
        前向傳播
        
        Args:
            features: 編碼器輸出的特徵層列表
            
        Returns:
            解碼器輸出
        """
        pass
    
    def _init_weights(self):
        """權重初始化，默認實現"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    @property
    def decoder_type(self) -> str:
        """解碼器類型標識"""
        return self.__class__.__name__


class SegmentationDecoder(BaseDecoder):
    """分割解碼器基類"""
    
    def __init__(self, **kwargs):
        self.task_type = "segmentation"
        super().__init__(**kwargs)


class DetectionDecoder(BaseDecoder):
    """檢測解碼器基類"""
    
    def __init__(self, **kwargs):
        self.task_type = "detection"
        super().__init__(**kwargs)


class ClassificationDecoder(BaseDecoder):
    """分類解碼器基類"""
    
    def __init__(self, **kwargs):
        self.task_type = "classification"
        super().__init__(**kwargs)


class FPNDecoder(SegmentationDecoder):
    """FPN解碼器實現"""
    
    def _build_decoder(self):
        """構建FPN解碼器"""
        # FPN lateral connections
        self.lateral_convs = nn.ModuleList()
        self.fpn_convs = nn.ModuleList()
        
        for i, in_channels in enumerate(self.encoder_channels):
            # 側邊連接
            lateral_conv = nn.Conv2d(in_channels, self.decoder_channels[0], 1)
            self.lateral_convs.append(lateral_conv)
            
            # FPN輸出卷積
            fpn_conv = nn.Conv2d(self.decoder_channels[0], self.decoder_channels[0], 3, padding=1)
            self.fpn_convs.append(fpn_conv)
        
        # 最終分割頭
        self.seg_head = nn.Sequential(
            nn.Conv2d(self.decoder_channels[0], self.decoder_channels[1], 3, padding=1),
            nn.BatchNorm2d(self.decoder_channels[1]),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.decoder_channels[1], self.num_classes, 1)
        )
    
    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """FPN前向傳播"""
        # 構建FPN特徵
        fpn_features = []
        
        # 自頂向下路徑
        prev_feature = None
        for i in range(len(features) - 1, -1, -1):
            # 側邊連接
            lateral = self.lateral_convs[i](features[i])
            
            # 上採樣並相加
            if prev_feature is not None:
                lateral = lateral + nn.functional.interpolate(
                    prev_feature, size=lateral.shape[-2:], mode='nearest'
                )
            
            # FPN輸出
            fpn_feature = self.fpn_convs[i](lateral)
            fpn_features.insert(0, fpn_feature)
            prev_feature = lateral
        
        # 使用最高解析度特徵進行分割
        output = self.seg_head(fpn_features[0])
        return output


class UNetDecoder(SegmentationDecoder):
    """UNet解碼器實現"""
    
    def _build_decoder(self):
        """構建UNet解碼器"""
        self.up_blocks = nn.ModuleList()
        
        # 構建上採樣塊
        for i in range(len(self.encoder_channels) - 1):
            in_channels = self.encoder_channels[-(i+1)]
            skip_channels = self.encoder_channels[-(i+2)]
            out_channels = self.decoder_channels[i]
            
            up_block = self._make_up_block(in_channels, skip_channels, out_channels)
            self.up_blocks.append(up_block)
        
        # 最終輸出層
        self.final_conv = nn.Conv2d(self.decoder_channels[-2], self.num_classes, 1)
    
    def _make_up_block(self, in_channels: int, skip_channels: int, out_channels: int) -> nn.Module:
        """創建上採樣塊"""
        return nn.Sequential(
            nn.ConvTranspose2d(in_channels, out_channels, 2, stride=2),
            nn.Conv2d(out_channels + skip_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """UNet前向傳播"""
        x = features[-1]  # 最深層特徵
        
        # 逐層上採樣並融合跳躍連接
        for i, up_block in enumerate(self.up_blocks):
            # 上採樣
            x = nn.functional.interpolate(x, scale_factor=2, mode='bilinear', align_corners=False)
            x = up_block[0](x)  # ConvTranspose2d
            
            # 跳躍連接
            skip = features[-(i+2)]
            x = torch.cat([x, skip], dim=1)
            
            # 卷積處理
            for layer in up_block[1:]:
                x = layer(x)
        
        # 最終輸出
        output = self.final_conv(x)
        return output


class DecoderConfig:
    """解碼器配置類"""
    
    def __init__(self,
                 decoder_type: str,
                 decoder_variant: str = "default",
                 encoder_channels: List[int] = None,
                 decoder_channels: List[int] = None,
                 num_classes: int = 1,
                 **kwargs):
        self.decoder_type = decoder_type
        self.decoder_variant = decoder_variant
        self.encoder_channels = encoder_channels or [64, 128, 256, 512]
        self.decoder_channels = decoder_channels or [256, 128, 64, 32]
        self.num_classes = num_classes
        self.config = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'decoder_type': self.decoder_type,
            'decoder_variant': self.decoder_variant,
            'encoder_channels': self.encoder_channels,
            'decoder_channels': self.decoder_channels,
            'num_classes': self.num_classes,
            **self.config
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'DecoderConfig':
        """從字典創建配置"""
        return cls(**config_dict)