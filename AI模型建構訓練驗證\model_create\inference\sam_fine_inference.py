#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAM Fine-tuning推理模組

基於0_seg_SAMFine.ipynb建構的SAM微調推理系統
使用ViT-H backbone，專門用於道路基礎設施分割任務
"""

import os
import sys
import logging
import time
import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
import tempfile
import shutil

# 添加SAM路徑
sys.path.append(r'D:\code\segment-anything-main')
try:
    from segment_anything import sam_model_registry, SamPredictor
except ImportError:
    logging.warning("SAM module not found. Please install segment-anything.")

from ..util.Dataset_read import YOLODataset
from ..core.base_inference import BaseInference


class SAMFineInference(BaseInference):
    """
    SAM Fine-tuning推理類
    
    基於預訓練的SAM模型進行微調後的推理
    支援point prompt和box prompt
    """
    
    def __init__(self, 
                 model_path: str,
                 model_type: str = "vit_h",
                 sam_checkpoint: str = "sam_vit_h_4b8939.pth",
                 device: str = "auto",
                 image_size: int = 384,
                 class_names: Optional[Dict[int, str]] = None):
        """
        初始化SAM Fine-tuning推理器
        
        參數:
            model_path: 微調後的模型權重路徑
            model_type: SAM模型類型 ("vit_h", "vit_l", "vit_b")
            sam_checkpoint: 原始SAM檢查點路徑
            device: 計算設備
            image_size: 圖像大小
            class_names: 類別名稱字典
        """
        super().__init__(device)
        
        self.model_path = model_path
        self.model_type = model_type
        self.sam_checkpoint = sam_checkpoint
        self.image_size = image_size
        self.class_names = class_names or {
            0: '背景', 1: '伸縮縫', 2: '路面接縫', 
            3: '裂縫', 4: '坑洞', 5: '補綻', 6: '其他'
        }
        
        self.model = None
        self.predictor = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 加載模型
        self._load_model()
    
    def _load_model(self):
        """加載SAM模型和微調權重"""
        try:
            # 載入SAM模型
            self.model = sam_model_registry[self.model_type](checkpoint=self.sam_checkpoint)
            self.model.to(self.device)
            
            # 載入微調權重
            if os.path.exists(self.model_path):
                state_dict = torch.load(self.model_path, map_location=self.device)
                self.model.load_state_dict(state_dict, strict=False)
                self.logger.info(f"已載入微調權重: {self.model_path}")
            else:
                self.logger.warning(f"微調權重不存在，使用原始SAM權重: {self.model_path}")
            
            # 創建預測器
            self.predictor = SamPredictor(self.model)
            
            # 設定為評估模式
            self.model.eval()
            
            self.logger.info(f"SAM模型初始化完成: {self.model_type}")
            
        except Exception as e:
            self.logger.error(f"載入SAM模型失敗: {str(e)}")
            raise
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        預處理圖像
        
        參數:
            image: 輸入圖像 (H, W, 3)
            
        返回:
            預處理後的圖像
        """
        # 確保圖像是RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            # 如果是BGR，轉換為RGB
            if image.max() > 1.0:  # 假設是0-255範圍
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        return image
    
    def predict_with_points(self, 
                          image: np.ndarray, 
                          points: List[Tuple[int, int]], 
                          labels: List[int] = None) -> Dict:
        """
        使用點提示進行預測
        
        參數:
            image: 輸入圖像
            points: 點座標列表 [(x, y), ...]
            labels: 點標籤列表 (1為前景，0為背景)
            
        返回:
            預測結果字典
        """
        if labels is None:
            labels = [1] * len(points)  # 默認所有點為前景
        
        # 預處理圖像
        image = self.preprocess_image(image)
        
        # 設定圖像
        self.predictor.set_image(image)
        
        # 轉換為numpy數組
        input_points = np.array(points)
        input_labels = np.array(labels)
        
        # 進行預測
        masks, scores, logits = self.predictor.predict(
            point_coords=input_points,
            point_labels=input_labels,
            multimask_output=True
        )
        
        # 選擇最佳掩碼（最高分數）
        best_mask_idx = np.argmax(scores)
        best_mask = masks[best_mask_idx]
        best_score = scores[best_mask_idx]
        
        return {
            'mask': best_mask,
            'score': best_score,
            'all_masks': masks,
            'all_scores': scores,
            'logits': logits
        }
    
    def predict_with_box(self, 
                        image: np.ndarray, 
                        box: Tuple[int, int, int, int]) -> Dict:
        """
        使用邊界框提示進行預測
        
        參數:
            image: 輸入圖像
            box: 邊界框 (x1, y1, x2, y2)
            
        返回:
            預測結果字典
        """
        # 預處理圖像
        image = self.preprocess_image(image)
        
        # 設定圖像
        self.predictor.set_image(image)
        
        # 轉換為numpy數組
        input_box = np.array(box)
        
        # 進行預測
        masks, scores, logits = self.predictor.predict(
            point_coords=None,
            point_labels=None,
            box=input_box[None, :],
            multimask_output=False
        )
        
        return {
            'mask': masks[0],
            'score': scores[0],
            'all_masks': masks,
            'all_scores': scores,
            'logits': logits
        }
    
    def predict_auto(self, image: np.ndarray) -> Dict:
        """
        自動預測（無提示）
        
        參數:
            image: 輸入圖像
            
        返回:
            預測結果字典
        """
        # 預處理圖像
        image = self.preprocess_image(image)
        
        # 設定圖像
        self.predictor.set_image(image)
        
        # 使用圖像中心點作為提示
        h, w = image.shape[:2]
        center_point = [[w // 2, h // 2]]
        center_label = [1]
        
        # 進行預測
        masks, scores, logits = self.predictor.predict(
            point_coords=np.array(center_point),
            point_labels=np.array(center_label),
            multimask_output=True
        )
        
        # 選擇最佳掩碼
        best_mask_idx = np.argmax(scores)
        best_mask = masks[best_mask_idx]
        best_score = scores[best_mask_idx]
        
        return {
            'mask': best_mask,
            'score': best_score,
            'all_masks': masks,
            'all_scores': scores,
            'logits': logits
        }
    
    def batch_predict(self, 
                     images: List[np.ndarray],
                     prompts: List[Dict] = None,
                     **kwargs) -> List[Dict]:
        """
        批次預測
        
        參數:
            images: 圖像列表
            prompts: 提示列表，每個元素包含 {'type': 'point'/'box', 'data': ...}
            
        返回:
            預測結果列表
        """
        results = []
        
        for i, image in enumerate(images):
            if prompts and i < len(prompts):
                prompt = prompts[i]
                if prompt['type'] == 'point':
                    result = self.predict_with_points(
                        image, 
                        prompt['data']['points'], 
                        prompt['data'].get('labels')
                    )
                elif prompt['type'] == 'box':
                    result = self.predict_with_box(image, prompt['data'])
                else:
                    result = self.predict_auto(image)
            else:
                result = self.predict_auto(image)
            
            results.append(result)
        
        return results
    
    def visualize_prediction(self, 
                           image: np.ndarray,
                           result: Dict,
                           prompts: Dict = None,
                           save_path: str = None) -> np.ndarray:
        """
        可視化預測結果
        
        參數:
            image: 原始圖像
            result: 預測結果
            prompts: 提示信息
            save_path: 保存路徑
            
        返回:
            可視化圖像
        """
        # 創建可視化圖像
        vis_image = image.copy()
        mask = result['mask']
        score = result['score']
        
        # 應用掩碼疊加
        mask_overlay = np.zeros_like(vis_image)
        mask_overlay[mask] = [0, 255, 0]  # 綠色掩碼
        
        # 混合圖像
        alpha = 0.6
        vis_image = cv2.addWeighted(vis_image, 1-alpha, mask_overlay, alpha, 0)
        
        # 繪製提示點
        if prompts:
            if prompts.get('type') == 'point':
                points = prompts['data']['points']
                labels = prompts['data'].get('labels', [1] * len(points))
                
                for point, label in zip(points, labels):
                    color = (255, 0, 0) if label == 1 else (0, 0, 255)  # 前景紅色，背景藍色
                    cv2.circle(vis_image, tuple(point), 5, color, -1)
            
            elif prompts.get('type') == 'box':
                box = prompts['data']
                cv2.rectangle(vis_image, (box[0], box[1]), (box[2], box[3]), (255, 255, 0), 2)
        
        # 添加分數文字
        cv2.putText(vis_image, f"Score: {score:.3f}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 保存圖像
        if save_path:
            cv2.imwrite(save_path, cv2.cvtColor(vis_image, cv2.COLOR_RGB2BGR))
        
        return vis_image
    
    def calculate_mask_stats(self, mask: np.ndarray, scale: float = 1.0) -> Dict:
        """
        計算掩碼統計信息
        
        參數:
            mask: 二值掩碼
            scale: 像素到實際距離的比例（米/像素）
            
        返回:
            統計信息字典
        """
        # 基本統計
        total_pixels = mask.shape[0] * mask.shape[1]
        mask_pixels = np.sum(mask)
        mask_ratio = mask_pixels / total_pixels
        
        # 計算輪廓和面積
        contours, _ = cv2.findContours(
            mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        
        areas = []
        perimeters = []
        bboxes = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            bbox = cv2.boundingRect(contour)
            
            areas.append(area * (scale ** 2))  # 轉換為實際面積
            perimeters.append(perimeter * scale)  # 轉換為實際周長
            bboxes.append(bbox)
        
        return {
            'total_pixels': total_pixels,
            'mask_pixels': mask_pixels,
            'mask_ratio': mask_ratio,
            'num_objects': len(contours),
            'areas': areas,
            'perimeters': perimeters,
            'bboxes': bboxes,
            'total_area': sum(areas),
            'average_area': np.mean(areas) if areas else 0,
            'max_area': max(areas) if areas else 0,
            'min_area': min(areas) if areas else 0
        }


def create_sam_fine_inference(model_path: str, 
                             model_type: str = "vit_h",
                             **kwargs) -> SAMFineInference:
    """
    創建SAM Fine-tuning推理器的工廠函數
    
    參數:
        model_path: 模型權重路徑
        model_type: 模型類型
        **kwargs: 其他參數
        
    返回:
        SAMFineInference實例
    """
    return SAMFineInference(
        model_path=model_path,
        model_type=model_type,
        **kwargs
    )


# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建推理器
    model_path = "path/to/sam_finetuned_weights.pth"
    sam_checkpoint = "sam_vit_h_4b8939.pth"
    
    try:
        inferencer = SAMFineInference(
            model_path=model_path,
            sam_checkpoint=sam_checkpoint,
            model_type="vit_h"
        )
        
        # 載入測試圖像
        test_image = cv2.imread("test_image.jpg")
        test_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        
        # 使用點提示進行預測
        points = [(100, 100), (200, 200)]
        result = inferencer.predict_with_points(test_image, points)
        
        # 可視化結果
        vis_image = inferencer.visualize_prediction(
            test_image, 
            result, 
            prompts={'type': 'point', 'data': {'points': points}},
            save_path="result.jpg"
        )
        
        # 計算統計信息
        stats = inferencer.calculate_mask_stats(result['mask'])
        print(f"檢測到 {stats['num_objects']} 個對象")
        print(f"總面積: {stats['total_area']:.2f} 平方米")
        
    except Exception as e:
        print(f"SAM推理失敗: {str(e)}")