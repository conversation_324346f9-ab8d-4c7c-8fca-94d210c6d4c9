"""
Ray框架整合模組

提供Ray分散式計算框架的整合功能：
- 分散式訓練 (Ray Train)
- 超參數優化 (<PERSON>)
- 資料處理 (Ray Data)
- 模型服務 (Ray Serve)
"""

import os
import logging
from typing import Dict, Any, Optional, Callable, List, Union
from pathlib import Path
import yaml

try:
    import ray
    from ray import train, tune, data, serve
    from ray.train import ScalingConfig, RunConfig
    from ray.train.torch import TorchTrainer
    from ray.tune.schedulers import ASHAScheduler
    from ray.tune.search.optuna import OptunaSearch
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

from ..core.config_manager import ConfigManager
from ..training.trainer import UnifiedTrainer, TrainingConfig
from ..util.dataset import YOLODataset


class RayIntegrationManager:
    """Ray整合管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config_manager = ConfigManager()
        
        if not RAY_AVAILABLE:
            raise ImportError("Ray框架未安裝。請使用 'pip install ray[tune,train,data,serve]' 安裝")
        
        # 載入Ray配置
        self.ray_config = self._load_ray_config(config_path)
        
        # 初始化Ray
        if not ray.is_initialized():
            self._initialize_ray()
    
    def _load_ray_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """載入Ray配置"""
        if config_path and os.path.exists(config_path):
            return self.config_manager.load_config(config_path)
        
        # 預設配置
        return {
            "cluster": {
                "address": None,  # None表示本地模式
                "runtime_env": {
                    "pip": ["torch", "torchvision", "opencv-python", "albumentations"]
                }
            },
            "training": {
                "use_gpu": True,
                "num_workers": 2,
                "resources_per_worker": {"CPU": 2, "GPU": 0.5}
            },
            "tuning": {
                "metric": "val_accuracy",
                "mode": "max",
                "num_samples": 10,
                "max_concurrent_trials": 2
            },
            "serving": {
                "num_replicas": 2,
                "ray_actor_options": {"num_cpus": 1, "num_gpus": 0.1}
            }
        }
    
    def _initialize_ray(self):
        """初始化Ray集群"""
        cluster_config = self.ray_config.get("cluster", {})
        
        try:
            if cluster_config.get("address"):
                # 連接到現有集群
                ray.init(address=cluster_config["address"])
                self.logger.info(f"連接到Ray集群: {cluster_config['address']}")
            else:
                # 本地模式
                ray.init(
                    runtime_env=cluster_config.get("runtime_env", {}),
                    ignore_reinit_error=True
                )
                self.logger.info("Ray以本地模式初始化")
        
        except Exception as e:
            self.logger.error(f"Ray初始化失敗: {e}")
            raise


class RayDistributedTrainer:
    """Ray分散式訓練器"""
    
    def __init__(self, ray_manager: RayIntegrationManager):
        self.ray_manager = ray_manager
        self.logger = logging.getLogger(__name__)
    
    def create_distributed_trainer(self, 
                                 model_factory: Callable,
                                 training_config: TrainingConfig,
                                 dataset_config: Dict[str, Any]) -> TorchTrainer:
        """創建分散式訓練器"""
        
        def train_func(config: Dict[str, Any]):
            """Ray訓練函數"""
            # 創建模型
            model = model_factory(**config.get("model", {}))
            
            # 創建資料集
            train_dataset = self._create_ray_dataset(
                dataset_config["train_path"], 
                config.get("batch_size", 32)
            )
            val_dataset = self._create_ray_dataset(
                dataset_config["val_path"], 
                config.get("batch_size", 32)
            )
            
            # 創建訓練器
            trainer = UnifiedTrainer(
                model=model,
                config=training_config,
                **config.get("trainer", {})
            )
            
            # 執行訓練
            history = trainer.fit(train_dataset, val_dataset)
            
            # 報告指標
            for epoch, metrics in enumerate(history):
                train.report({
                    "epoch": epoch,
                    "train_loss": metrics.get("train_loss", 0),
                    "val_loss": metrics.get("val_loss", 0),
                    "val_accuracy": metrics.get("val_accuracy", 0)
                })
        
        # 配置分散式訓練
        scaling_config = ScalingConfig(
            num_workers=self.ray_manager.ray_config["training"]["num_workers"],
            use_gpu=self.ray_manager.ray_config["training"]["use_gpu"],
            resources_per_worker=self.ray_manager.ray_config["training"]["resources_per_worker"]
        )
        
        # 創建TorchTrainer
        trainer = TorchTrainer(
            train_loop_per_worker=train_func,
            scaling_config=scaling_config,
            run_config=RunConfig(
                storage_path="./ray_results",
                name="distributed_training"
            )
        )
        
        return trainer
    
    def _create_ray_dataset(self, data_path: str, batch_size: int):
        """創建Ray資料集"""
        # 這裡可以整合現有的資料集類
        # 暫時返回模擬資料集
        return data.from_items([{"data": f"item_{i}"} for i in range(100)])


class RayHyperparameterTuner:
    """Ray超參數優化器"""
    
    def __init__(self, ray_manager: RayIntegrationManager):
        self.ray_manager = ray_manager
        self.logger = logging.getLogger(__name__)
    
    def create_tuning_config(self, 
                           search_space: Dict[str, Any],
                           model_factory: Callable,
                           training_config: TrainingConfig) -> Dict[str, Any]:
        """創建調優配置"""
        
        def objective(config: Dict[str, Any]) -> Dict[str, float]:
            """優化目標函數"""
            # 創建並訓練模型
            model = model_factory(**config.get("model", {}))
            
            # 創建訓練器
            trainer = UnifiedTrainer(
                model=model,
                config=training_config,
                **config.get("trainer", {})
            )
            
            # 快速訓練（少數epoch）
            history = trainer.fit_epochs(epochs=config.get("eval_epochs", 5))
            
            # 返回最佳驗證準確率
            best_acc = max([h.get("val_accuracy", 0) for h in history])
            return {"val_accuracy": best_acc}
        
        # 配置搜索算法
        search_alg = OptunaSearch()
        
        # 配置調度器
        scheduler = ASHAScheduler(
            metric=self.ray_manager.ray_config["tuning"]["metric"],
            mode=self.ray_manager.ray_config["tuning"]["mode"]
        )
        
        tuning_config = {
            "objective": objective,
            "config": search_space,
            "num_samples": self.ray_manager.ray_config["tuning"]["num_samples"],
            "scheduler": scheduler,
            "search_alg": search_alg,
            "resources": {"cpu": 2, "gpu": 0.5}
        }
        
        return tuning_config
    
    def run_hyperparameter_search(self, 
                                 search_space: Dict[str, Any],
                                 model_factory: Callable,
                                 training_config: TrainingConfig) -> Any:
        """執行超參數搜索"""
        
        tuning_config = self.create_tuning_config(
            search_space, model_factory, training_config
        )
        
        # 執行調優
        analysis = tune.run(
            tuning_config["objective"],
            config=tuning_config["config"],
            num_samples=tuning_config["num_samples"],
            scheduler=tuning_config["scheduler"],
            search_alg=tuning_config["search_alg"],
            resources_per_trial=tuning_config["resources"],
            local_dir="./ray_results"
        )
        
        return analysis


class RayDataProcessor:
    """Ray資料處理器"""
    
    def __init__(self, ray_manager: RayIntegrationManager):
        self.ray_manager = ray_manager
        self.logger = logging.getLogger(__name__)
    
    def create_distributed_dataset(self, 
                                  data_paths: List[str],
                                  transforms: Optional[Callable] = None) -> Any:
        """創建分散式資料集"""
        
        # 從文件路徑創建Ray資料集
        dataset = data.from_items([
            {"path": path} for path in data_paths
        ])
        
        # 應用轉換
        if transforms:
            dataset = dataset.map(transforms)
        
        return dataset
    
    def process_annotations_distributed(self, 
                                      annotation_paths: List[str],
                                      output_format: str = "yolo") -> Any:
        """分散式標註處理"""
        
        def convert_annotation(item: Dict[str, Any]) -> Dict[str, Any]:
            """轉換單個標註"""
            # 這裡整合現有的標註轉換邏輯
            from ...資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2
            
            converter = AnnotationConverterV2(
                input_dir=item["path"],
                output_dir=f"./converted_{output_format}",
                input_format="auto",
                output_format=output_format
            )
            
            result = converter.run()
            return {"status": result.get("success", False), "path": item["path"]}
        
        # 創建資料集並處理
        dataset = data.from_items([
            {"path": path} for path in annotation_paths
        ])
        
        processed = dataset.map(convert_annotation)
        return processed


class RayModelServer:
    """Ray模型服務器"""
    
    def __init__(self, ray_manager: RayIntegrationManager):
        self.ray_manager = ray_manager
        self.logger = logging.getLogger(__name__)
    
    @serve.deployment(
        num_replicas=2,
        ray_actor_options={"num_cpus": 1, "num_gpus": 0.1}
    )
    class ModelServeDeployment:
        """模型服務部署"""
        
        def __init__(self, model_path: str):
            import torch
            self.model = torch.load(model_path, map_location="cpu")
            self.model.eval()
        
        async def __call__(self, request):
            """處理推理請求"""
            import torch
            import numpy as np
            
            # 解析請求
            image_data = np.array(request.json()["image"])
            
            # 預處理
            input_tensor = torch.from_numpy(image_data).float()
            
            # 推理
            with torch.no_grad():
                prediction = self.model(input_tensor)
            
            # 後處理
            result = prediction.cpu().numpy().tolist()
            
            return {"prediction": result}
    
    def deploy_model(self, model_path: str, deployment_name: str = "road_damage_detector"):
        """部署模型服務"""
        
        # 配置部署
        serving_config = self.ray_manager.ray_config.get("serving", {})
        
        deployment = self.ModelServeDeployment.options(
            num_replicas=serving_config.get("num_replicas", 2),
            ray_actor_options=serving_config.get("ray_actor_options", {})
        )
        
        # 部署模型
        serve.run(deployment.bind(model_path), name=deployment_name)
        
        self.logger.info(f"模型已部署: {deployment_name}")
        return f"http://localhost:8000/{deployment_name}"


def create_ray_search_space() -> Dict[str, Any]:
    """創建Ray Tune搜索空間"""
    return {
        "model": {
            "num_classes": tune.choice([5, 10, 15]),
            "backbone": tune.choice(["resnet50", "mobilenet_v3", "csp_iformer"]),
            "dropout_rate": tune.uniform(0.1, 0.5)
        },
        "trainer": {
            "learning_rate": tune.loguniform(1e-5, 1e-2),
            "batch_size": tune.choice([16, 32, 64]),
            "weight_decay": tune.uniform(1e-6, 1e-3)
        },
        "eval_epochs": tune.choice([3, 5, 7])
    }


# 範例使用方式
def example_usage():
    """Ray整合使用範例"""
    
    # 1. 初始化Ray管理器
    ray_manager = RayIntegrationManager()
    
    # 2. 分散式訓練
    distributed_trainer = RayDistributedTrainer(ray_manager)
    
    def model_factory(**kwargs):
        # 返回模型實例
        pass
    
    training_config = TrainingConfig()
    dataset_config = {
        "train_path": "./data/train",
        "val_path": "./data/val"
    }
    
    trainer = distributed_trainer.create_distributed_trainer(
        model_factory, training_config, dataset_config
    )
    
    # 執行分散式訓練
    result = trainer.fit()
    
    # 3. 超參數優化
    tuner = RayHyperparameterTuner(ray_manager)
    search_space = create_ray_search_space()
    
    analysis = tuner.run_hyperparameter_search(
        search_space, model_factory, training_config
    )
    
    # 4. 模型服務
    model_server = RayModelServer(ray_manager)
    service_url = model_server.deploy_model("./best_model.pth")
    
    print(f"模型服務URL: {service_url}")


if __name__ == "__main__":
    example_usage()