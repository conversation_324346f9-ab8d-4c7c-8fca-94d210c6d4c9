#!/usr/bin/env python3
"""
驗證工具函數
用於驗證模型、配置和依賴項
"""

import os
import sys
import importlib
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional

from ..config.yolo_config import SimplifiedYOLOConfig


def validate_model_path(model_path: str) -> Tuple[bool, str]:
    """
    驗證模型路徑的有效性
    
    Args:
        model_path: 模型文件路徑
        
    Returns:
        Tuple[bool, str]: (是否有效, 錯誤信息)
    """
    if not model_path:
        return False, "模型路徑為空"
    
    if not os.path.exists(model_path):
        return False, f"模型文件不存在: {model_path}"
    
    # 檢查文件副檔名
    valid_extensions = ['.pt', '.onnx', '.engine']
    file_ext = Path(model_path).suffix.lower()
    
    if file_ext not in valid_extensions:
        return False, f"不支援的模型格式: {file_ext}，支援的格式: {valid_extensions}"
    
    # 檢查文件大小
    file_size = os.path.getsize(model_path)
    if file_size < 1024:  # 小於1KB可能是無效文件
        return False, f"模型文件過小: {file_size} bytes"
    
    return True, ""


def validate_directory(directory_path: str, 
                      check_writable: bool = False) -> Tuple[bool, str]:
    """
    驗證目錄路徑的有效性
    
    Args:
        directory_path: 目錄路徑
        check_writable: 是否檢查寫入權限
        
    Returns:
        Tuple[bool, str]: (是否有效, 錯誤信息)
    """
    if not directory_path:
        return False, "目錄路徑為空"
    
    if not os.path.exists(directory_path):
        return False, f"目錄不存在: {directory_path}"
    
    if not os.path.isdir(directory_path):
        return False, f"路徑不是目錄: {directory_path}"
    
    if check_writable and not os.access(directory_path, os.W_OK):
        return False, f"目錄無寫入權限: {directory_path}"
    
    return True, ""


def validate_config(config: SimplifiedYOLOConfig) -> Tuple[bool, List[str]]:
    """
    驗證配置對象的有效性
    
    Args:
        config: 配置對象
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 錯誤信息列表)
    """
    errors = []
    
    # 檢查模型路徑
    if config.detection_model_path:
        is_valid, error = validate_model_path(config.detection_model_path)
        if not is_valid:
            errors.append(f"檢測模型: {error}")
    
    if config.segmentation_model_path:
        is_valid, error = validate_model_path(config.segmentation_model_path)
        if not is_valid:
            errors.append(f"分割模型: {error}")
    
    # 檢查是否至少有一個模型
    if not config.detection_model_path and not config.segmentation_model_path:
        errors.append("必須提供至少一個模型路徑")
    
    # 檢查參數範圍
    if not (1 <= config.img_size <= 2048):
        errors.append(f"圖像大小應在1-2048範圍內: {config.img_size}")
    
    if not (0.0 <= config.global_conf <= 1.0):
        errors.append(f"置信度閾值應在0-1範圍內: {config.global_conf}")
    
    if not (0.0 <= config.iou_threshold <= 1.0):
        errors.append(f"IoU閾值應在0-1範圍內: {config.iou_threshold}")
    
    if config.max_det <= 0:
        errors.append(f"最大檢測數必須為正數: {config.max_det}")
    
    # 檢查SAHI配置
    if config.enable_sahi:
        if not (64 <= config.slice_size <= 2048):
            errors.append(f"SAHI切片大小應在64-2048範圍內: {config.slice_size}")
        
        if not (0.0 <= config.overlap_ratio < 1.0):
            errors.append(f"SAHI重疊比例應在0-1範圍內: {config.overlap_ratio}")
        
        if not (0.0 <= config.sahi_conf <= 1.0):
            errors.append(f"SAHI置信度應在0-1範圍內: {config.sahi_conf}")
    
    # 檢查設備設置
    valid_devices = ['auto', 'cpu', 'cuda', 'mps']
    if config.device not in valid_devices and not config.device.startswith('cuda:'):
        errors.append(f"無效的設備設置: {config.device}，有效選項: {valid_devices}")
    
    return len(errors) == 0, errors


def check_dependencies() -> Dict[str, Dict[str, Any]]:
    """
    檢查系統依賴項
    
    Returns:
        Dict: 依賴項檢查結果
    """
    dependencies = {
        'required': {
            'numpy': {'installed': False, 'version': None, 'error': None},
            'opencv-python': {'installed': False, 'version': None, 'error': None},
            'ultralytics': {'installed': False, 'version': None, 'error': None},
        },
        'optional': {
            'sahi': {'installed': False, 'version': None, 'error': None},
            'torch': {'installed': False, 'version': None, 'error': None},
            'torchvision': {'installed': False, 'version': None, 'error': None},
        }
    }
    
    # 檢查必需依賴
    for dep_name in dependencies['required']:
        try:
            if dep_name == 'opencv-python':
                module = importlib.import_module('cv2')
            else:
                module = importlib.import_module(dep_name)
            
            dependencies['required'][dep_name]['installed'] = True
            if hasattr(module, '__version__'):
                dependencies['required'][dep_name]['version'] = module.__version__
        except ImportError as e:
            dependencies['required'][dep_name]['error'] = str(e)
    
    # 檢查可選依賴
    for dep_name in dependencies['optional']:
        try:
            module = importlib.import_module(dep_name)
            dependencies['optional'][dep_name]['installed'] = True
            if hasattr(module, '__version__'):
                dependencies['optional'][dep_name]['version'] = module.__version__
        except ImportError as e:
            dependencies['optional'][dep_name]['error'] = str(e)
    
    return dependencies


def print_dependency_report():
    """打印依賴項檢查報告"""
    deps = check_dependencies()
    
    print("🔍 依賴項檢查報告")
    print("=" * 50)
    
    # 必需依賴
    print("\n📋 必需依賴:")
    all_required_ok = True
    for name, info in deps['required'].items():
        if info['installed']:
            version = info['version'] or 'unknown'
            print(f"  ✅ {name:<15} v{version}")
        else:
            print(f"  ❌ {name:<15} 未安裝")
            if info['error']:
                print(f"     錯誤: {info['error']}")
            all_required_ok = False
    
    # 可選依賴
    print("\n📋 可選依賴:")
    for name, info in deps['optional'].items():
        if info['installed']:
            version = info['version'] or 'unknown'
            print(f"  ✅ {name:<15} v{version}")
        else:
            print(f"  ⚠️  {name:<15} 未安裝 (可選)")
    
    # 總結
    print("\n" + "=" * 50)
    if all_required_ok:
        print("✅ 所有必需依賴已安裝，系統就緒！")
    else:
        print("❌ 缺少必需依賴，請安裝後再使用")
        print("\n安裝命令:")
        print("pip install numpy opencv-python ultralytics")
        print("pip install sahi torch torchvision  # 可選")
    
    return all_required_ok


def validate_system_requirements() -> Tuple[bool, List[str]]:
    """
    驗證系統需求
    
    Returns:
        Tuple[bool, List[str]]: (是否滿足需求, 錯誤信息列表)
    """
    errors = []
    
    # 檢查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        errors.append(f"需要Python 3.8+，當前版本: {python_version.major}.{python_version.minor}")
    
    # 檢查依賴項
    deps = check_dependencies()
    for name, info in deps['required'].items():
        if not info['installed']:
            errors.append(f"缺少必需依賴: {name}")
    
    # 檢查GPU可用性（如果有torch）
    if deps['optional']['torch']['installed']:
        try:
            import torch
            if torch.cuda.is_available():
                print(f"✅ 檢測到GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("⚠️  未檢測到CUDA GPU，將使用CPU運行")
        except Exception as e:
            print(f"⚠️  GPU檢查失敗: {e}")
    
    return len(errors) == 0, errors


def suggest_optimization(config: SimplifiedYOLOConfig) -> List[str]:
    """
    根據配置提供優化建議
    
    Args:
        config: 配置對象
        
    Returns:
        List[str]: 優化建議列表
    """
    suggestions = []
    
    # 圖像大小建議
    if config.img_size > 1024:
        suggestions.append(f"圖像大小較大({config.img_size})，可能影響推理速度，建議使用640-1024")
    elif config.img_size < 640:
        suggestions.append(f"圖像大小較小({config.img_size})，可能影響檢測精度，建議使用640+")
    
    # 置信度建議
    if config.global_conf < 0.1:
        suggestions.append(f"置信度閾值過低({config.global_conf})，可能產生過多誤檢")
    elif config.global_conf > 0.8:
        suggestions.append(f"置信度閾值過高({config.global_conf})，可能導致漏檢")
    
    # SAHI建議
    if config.enable_sahi:
        if config.slice_size > config.img_size:
            suggestions.append("SAHI切片大小大於圖像大小，建議設置為圖像大小的一半")
        
        if config.overlap_ratio > 0.5:
            suggestions.append("SAHI重疊比例過高，可能影響推理速度")
        elif config.overlap_ratio < 0.1:
            suggestions.append("SAHI重疊比例過低，可能影響檢測效果")
    
    # 性能建議
    if config.max_det > 1000:
        suggestions.append("最大檢測數較高，可能影響推理速度")
    
    return suggestions


def create_validation_report(config: SimplifiedYOLOConfig) -> str:
    """
    創建完整的驗證報告
    
    Args:
        config: 配置對象
        
    Returns:
        str: 格式化的驗證報告
    """
    lines = []
    lines.append("🔍 配置驗證報告")
    lines.append("=" * 50)
    
    # 配置驗證
    is_valid, errors = validate_config(config)
    if is_valid:
        lines.append("✅ 配置驗證: 通過")
    else:
        lines.append("❌ 配置驗證: 失敗")
        for error in errors:
            lines.append(f"   - {error}")
    
    lines.append("")
    
    # 系統需求驗證
    sys_ok, sys_errors = validate_system_requirements()
    if sys_ok:
        lines.append("✅ 系統需求: 滿足")
    else:
        lines.append("❌ 系統需求: 不滿足")
        for error in sys_errors:
            lines.append(f"   - {error}")
    
    lines.append("")
    
    # 優化建議
    suggestions = suggest_optimization(config)
    if suggestions:
        lines.append("💡 優化建議:")
        for suggestion in suggestions:
            lines.append(f"   - {suggestion}")
    else:
        lines.append("✅ 配置已優化，無需調整")
    
    return "\n".join(lines)