#!/usr/bin/env python3
"""
簡單測試GT和預測顏色一致性
"""

def test_color_logic():
    """測試顏色計算邏輯"""
    
    # 模擬類別配置
    class_configs = {
        0: {'name': 'expansion_joint_伸縮縫', 'color': (255, 100, 100)},  # 紅色系
        1: {'name': 'joint_路面接縫', 'color': (100, 255, 100)},          # 綠色系  
        2: {'name': 'linear_crack_裂縫', 'color': (100, 100, 255)},        # 藍色系
    }
    
    print("=== GT和預測顏色邏輯測試 ===")
    
    for class_id, config in class_configs.items():
        class_name = config['name']
        base_color = config['color']
        
        # 預測顏色（原始）
        pred_color = base_color
        
        # GT顏色（80%亮度）
        gt_color = tuple(max(0, min(255, int(c * 0.8))) for c in base_color)
        
        # GT多邊形顏色（60%亮度）
        gt_polygon_color = tuple(max(0, min(255, int(c * 0.6))) for c in base_color)
        
        print(f"\n類別 {class_id}: {class_name}")
        print(f"  預測顏色:     {pred_color}")
        print(f"  GT邊界框:     {gt_color}")
        print(f"  GT多邊形:     {gt_polygon_color}")
        
        # 驗證顏色關係
        same_family = all(abs(pred_color[i] - gt_color[i]) <= pred_color[i] * 0.25 for i in range(3))
        print(f"  顏色系一致:   {same_family}")

if __name__ == "__main__":
    test_color_logic()