import os
import xml.etree.ElementTree as ET
import json

# ------------------------------
# 替換規則解析函式：
# 輸入格式例如：w, pothole, pathole:potholes; x,y:new_label
# 若 numeric=True 則將鍵值轉為整數（用於 YOLO）。
# ------------------------------
def parse_replacement_rules(user_input, numeric=False):
    replacement_dict = {}
    # 如果純空白則回傳空字典
    if not user_input.strip():
        return replacement_dict
    groups = user_input.split(";")
    for group in groups:
        group = group.strip()
        if not group:
            continue
        try:
            old_part, new_label = group.split(":")
        except ValueError:
            print(f"規則 '{group}' 格式錯誤，請使用 格式: 舊標籤1,舊標籤2:新標籤")
            continue
        old_labels = old_part.split(",")
        for lbl in old_labels:
            lbl = lbl.strip()
            if not lbl:
                continue
            if numeric:
                try:
                    replacement_dict[int(lbl)] = int(new_label.strip())
                except ValueError:
                    print(f"無法將 '{lbl}' 或 '{new_label}' 轉換成整數")
            else:
                replacement_dict[lbl] = new_label.strip()
    return replacement_dict

# ------------------------------
# 檔案格式偵測：根據檔案副檔名及內容檢查（排除 classes.txt 等特殊檔案）
# ------------------------------
def detect_format(file_path):
    base_name = os.path.basename(file_path)
    ext = os.path.splitext(base_name)[1].lower()

    if base_name.lower() == "classes.txt":
        return None

    if ext == ".xml":
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            if root.tag.lower() == "annotation":
                return "VOC"
        except Exception:
            return None
    elif ext == ".json":
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            if isinstance(data, dict) and "shapes" in data:
                return "Labelme"
        except Exception:
            return None
    elif ext == ".txt":
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                parts = line.split()
                if len(parts) >= 5:
                    try:
                        int(parts[0])
                        return "YOLO"
                    except ValueError:
                        continue
        except Exception:
            return None

    return None

# ------------------------------
# VOC 格式檔案處理：讀取 XML、進行替換及統計
# ------------------------------
def process_voc_file(file_path, replacement_mapping):
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
    except Exception as e:
        print(f"解析 {file_path} 發生錯誤：{e}")
        return {}
    
    classes_found = {}
    for obj in root.findall("object"):
        name_node = obj.find("name")
        if name_node is None:
            continue

        original_label = name_node.text.strip()
        if original_label in replacement_mapping:
            new_label = replacement_mapping[original_label]
            print(f"[VOC] {file_path}: 將標籤 {original_label} 替換為 {new_label}")
            name_node.text = new_label
            final_label = new_label
        else:
            final_label = original_label

        classes_found[final_label] = classes_found.get(final_label, 0) + 1

    tree.write(file_path, encoding="utf-8")
    return classes_found

# ------------------------------
# YOLO 格式檔案處理：讀取並替換首欄數字 id，並統計
# ------------------------------
def process_yolo_file(file_path, mapping_changes):
    classes_found = {}
    new_lines = []
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
    except Exception as e:
        print(f"讀取 {file_path} 時發生錯誤：{e}")
        return {}
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        parts = line.split()
        if len(parts) < 5:
            continue
        try:
            original_id = int(parts[0])
        except ValueError:
            continue
        if original_id in mapping_changes:
            final_id = mapping_changes[original_id]
            print(f"[YOLO] {file_path}: 將標籤 id {original_id} 替換為 {final_id}")
            parts[0] = str(final_id)
        else:
            final_id = original_id

        classes_found[final_id] = classes_found.get(final_id, 0) + 1
        new_lines.append(" ".join(parts))
    
    with open(file_path, "w", encoding="utf-8") as f:
        f.write("\n".join(new_lines))
    return classes_found

# ------------------------------
# Labelme 格式檔案處理：讀取 JSON、更新 shapes 中的 label，並統計
# ------------------------------
def process_labelme_file(file_path, replacement_mapping):
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
    except Exception as e:
        print(f"讀取 {file_path} 時發生錯誤：{e}")
        return {}
    
    classes_found = {}
    for shape in data.get("shapes", []):
        original_label = shape.get("label", "").strip()
        if original_label in replacement_mapping:
            final_label = replacement_mapping[original_label]
            print(f"[Labelme] {file_path}: 將標籤 {original_label} 替換為 {final_label}")
            shape["label"] = final_label
        else:
            final_label = original_label

        classes_found[final_label] = classes_found.get(final_label, 0) + 1

    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=4, ensure_ascii=False)
    return classes_found

# ------------------------------
# 預覽掃描：遍歷目錄並統計各格式標籤分布
# ------------------------------
def preview_scan_directory(root_dir):
    preview = {"VOC": {}, "YOLO": {}, "Labelme": {}}
    for dirpath, _, files in os.walk(root_dir):
        for filename in files:
            file_path = os.path.join(dirpath, filename)
            fmt = detect_format(file_path)
            if fmt == "VOC":
                try:
                    tree = ET.parse(file_path)
                    root = tree.getroot()
                except Exception:
                    continue
                for obj in root.findall("object"):
                    name_node = obj.find("name")
                    if name_node is None:
                        continue
                    label = name_node.text.strip()
                    preview["VOC"][label] = preview["VOC"].get(label, 0) + 1
            elif fmt == "YOLO":
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        lines = f.readlines()
                except Exception:
                    continue
                for line in lines:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        try:
                            label_id = int(parts[0])
                            preview["YOLO"][label_id] = preview["YOLO"].get(label_id, 0) + 1
                        except Exception:
                            continue
            elif fmt == "Labelme":
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                except Exception:
                    continue
                for shape in data.get("shapes", []):
                    label = shape.get("label", "").strip()
                    preview["Labelme"][label] = preview["Labelme"].get(label, 0) + 1
    return preview

# ------------------------------
# 刪除對應圖像檔案（依據檔名尋找 jpg、jpeg、png 等圖檔）
# ------------------------------
def delete_corresponding_image(ann_file_path):
    dir_path = os.path.dirname(ann_file_path)
    base = os.path.splitext(os.path.basename(ann_file_path))[0]
    possible_exts = [".jpg", ".jpeg", ".png"]
    for ext in possible_exts:
        img_path = os.path.join(dir_path, base + ext)
        if os.path.exists(img_path):
            try:
                os.remove(img_path)
                print(f"已刪除對應圖像檔案: {img_path}")
            except Exception as e:
                print(f"刪除圖像 {img_path} 時發生錯誤: {e}")

# ------------------------------
# 刪除功能：遍歷目錄，若某標註中包含使用者欲刪除的類別，則刪除該標註檔及對應圖像檔
# ------------------------------
def delete_files_by_class(root_dir, voc_del, yolo_del, labelme_del):
    delete_stats = {"VOC": 0, "YOLO": 0, "Labelme": 0}
    for dirpath, _, files in os.walk(root_dir):
        for filename in files:
            file_path = os.path.join(dirpath, filename)
            fmt = detect_format(file_path)
            if fmt == "VOC":
                try:
                    tree = ET.parse(file_path)
                    root = tree.getroot()
                    objects = root.findall("object")
                    should_delete = False
                    for obj in objects:
                        name_node = obj.find("name")
                        if name_node is not None and name_node.text.strip() in voc_del:
                            should_delete = True
                            break
                    if should_delete:
                        print(f"[VOC deletion] 刪除 {file_path} 中包含欲刪除的類別")
                        os.remove(file_path)
                        delete_stats["VOC"] += 1
                        delete_corresponding_image(file_path)
                except Exception:
                    continue
            elif fmt == "YOLO":
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        lines = f.readlines()
                    should_delete = False
                    for line in lines:
                        parts = line.strip().split()
                        if parts:
                            try:
                                id_val = int(parts[0])
                                if id_val in yolo_del:
                                    should_delete = True
                                    break
                            except ValueError:
                                continue
                    if should_delete:
                        print(f"[YOLO deletion] 刪除 {file_path} 中包含欲刪除的類別")
                        os.remove(file_path)
                        delete_stats["YOLO"] += 1
                        delete_corresponding_image(file_path)
                except Exception:
                    continue
            elif fmt == "Labelme":
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    should_delete = False
                    for shape in data.get("shapes", []):
                        if shape.get("label", "").strip() in labelme_del:
                            should_delete = True
                            break
                    if should_delete:
                        print(f"[Labelme deletion] 刪除 {file_path} 中包含欲刪除的類別")
                        os.remove(file_path)
                        delete_stats["Labelme"] += 1
                        delete_corresponding_image(file_path)
                except Exception:
                    continue
    return delete_stats

# ------------------------------
# 統一掃描目錄：依據各格式呼叫對應處理函式進行替換與統計
# ------------------------------
def scan_directory(root_dir, replacement_mappings_voc={}, mapping_changes_yolo={}, replacement_mapping_labelme={}):
    total_classes = {}
    for dirpath, _, files in os.walk(root_dir):
        for filename in files:
            file_path = os.path.join(dirpath, filename)
            fmt = detect_format(file_path)
            if fmt == "VOC":
                classes = process_voc_file(file_path, replacement_mappings_voc)
            elif fmt == "YOLO":
                classes = process_yolo_file(file_path, mapping_changes_yolo)
            elif fmt == "Labelme":
                classes = process_labelme_file(file_path, replacement_mapping_labelme)
            else:
                continue
            for k, v in classes.items():
                total_classes[k] = total_classes.get(k, 0) + v
    return total_classes

# ------------------------------
# 主程式：
# 1. 先依據使用者輸入的刪除規則刪除檔案與對應圖像
# 2. 刪除後更新並顯示預覽
# 3. 詢問是否進行標籤替換，若輸入 "no" 則跳過替換
# 4. 各格式替換規則提示中，若輸入 "no" 或留空，則視同不替換
# ------------------------------
def main():
    directory = input("請輸入圖像與標註檔所在的資料夾路徑：").strip()
    
    # 統計各格式檔案數量
    format_counts = {}
    for dirpath, _, files in os.walk(directory):
        for filename in files:
            file_path = os.path.join(dirpath, filename)
            fmt = detect_format(file_path)
            if fmt:
                format_counts[fmt] = format_counts.get(fmt, 0) + 1

    print("\n偵測到以下標註格式：")
    for fmt, count in format_counts.items():
        print(f"  {fmt}: {count} 個檔案")
    
    # --- 刪除階段 ---
    print("\n=== 刪除設定 ===")
    voc_deletions = []
    yolo_deletions = []
    labelme_deletions = []
    
    if format_counts.get("VOC", 0) > 0:
        user_input = input("請輸入要刪除的 VOC 標籤（以逗號分隔，若不刪除請留空）：")
        voc_deletions = [token.strip() for token in user_input.split(",") if token.strip()]
    
    if format_counts.get("YOLO", 0) > 0:
        user_input = input("請輸入要刪除的 YOLO 標籤 id（以逗號分隔，若不刪除請留空）：")
        for token in user_input.split(","):
            token = token.strip()
            if token:
                try:
                    yolo_deletions.append(int(token))
                except ValueError:
                    print(f"無法將 '{token}' 轉換為整數，跳過刪除 YOLO 該標籤")
    
    if format_counts.get("Labelme", 0) > 0:
        user_input = input("請輸入要刪除的 Labelme 標籤（以逗號分隔，若不刪除請留空）：")
        labelme_deletions = [token.strip() for token in user_input.split(",") if token.strip()]
    
    if voc_deletions or yolo_deletions or labelme_deletions:
        print("\n=== 開始依據刪除規則刪除檔案 ===")
        deletion_stats = delete_files_by_class(directory, voc_deletions, yolo_deletions, labelme_deletions)
        print("\n=== 刪除結果 ===")
        for key, count in deletion_stats.items():
            print(f"  {key}: {count} 份標註檔被刪除")
    else:
        print("\n未設定刪除規則，略過刪除流程。")
    
    # 刪除後重新更新預覽
    print("\n=== 刪除後更新預覽 ===")
    preview_after = preview_scan_directory(directory)
    if preview_after["VOC"]:
        print("\n【VOC 預覽】")
        for label, count in preview_after["VOC"].items():
            print(f"  {label}: {count}")
    if preview_after["YOLO"]:
        print("\n【YOLO 預覽】")
        for label_id, count in preview_after["YOLO"].items():
            print(f"  {label_id}: {count}")
    if preview_after["Labelme"]:
        print("\n【Labelme 預覽】")
        for label, count in preview_after["Labelme"].items():
            print(f"  {label}: {count}")
    
    # --- 替換階段 ---
    # 詢問是否進行替換：若輸入 "no" 則整體跳過替換
    user_choice = input("\n是否進行標籤替換？若不需要請輸入 'no'，按其他鍵則繼續：").strip().lower()
    if user_choice == "no":
        print("不進行標籤替換，程式結束。")
        return

    # 若使用者選擇替換，則依格式詢問替換規則；各提示中直接輸入 "no" 或留空，視同不替換
    voc_replace = {}
    yolo_replace = {}
    labelme_replace = {}

    if preview_after["VOC"]:
        print("\n【請輸入 VOC 標籤替換規則】")
        print("※ 格式：舊標籤1,舊標籤2:新標籤 （若不替換請留空或輸入 'no'）")
        user_input = input("請輸入替換規則：").strip()
        if user_input.lower() != "no":
            voc_replace = parse_replacement_rules(user_input, numeric=False)
    
    if preview_after["YOLO"]:
        print("\n【請輸入 YOLO 標籤替換規則】")
        print("※ 格式：舊id1,舊id2:新id （若不替換請留空或輸入 'no'）")
        user_input = input("請輸入替換規則：").strip()
        if user_input.lower() != "no":
            yolo_replace = parse_replacement_rules(user_input, numeric=True)
    
    if preview_after["Labelme"]:
        print("\n【請輸入 Labelme 標籤替換規則】")
        print("※ 格式：舊標籤1,舊標籤2:新標籤 （若不替換請留空或輸入 'no'）")
        user_input = input("請輸入替換規則：").strip()
        if user_input.lower() != "no":
            labelme_replace = parse_replacement_rules(user_input, numeric=False)
    
    print("\n=== 開始掃描資料夾並處理檔案（替換標籤） ===")
    stats = scan_directory(directory, voc_replace, yolo_replace, labelme_replace)
    
    print("\n=== 處理後統計結果 ===")
    for key, count in stats.items():
        print(f"  {key}: {count}")

if __name__ == "__main__":
    main()
