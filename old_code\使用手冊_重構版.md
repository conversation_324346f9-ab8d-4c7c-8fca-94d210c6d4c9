# 資料前處理模組 - 重構版使用手冊

## 📖 目錄
1. [概述](#概述)
2. [重構架構](#重構架構)
3. [快速開始](#快速開始)
4. [核心組件使用](#核心組件使用)
5. [重構後的標註轉換器](#重構後的標註轉換器)
6. [向後兼容性](#向後兼容性)
7. [最佳實踐](#最佳實踐)
8. [故障排除](#故障排除)

## 概述

資料前處理模組提供了完整的圖像數據集處理工具集，專注於標註格式轉換、圖像增強和全景圖像處理。經過重構後，模組具有更好的可維護性、擴展性和性能。

### 重構成果
- ✅ **階段1完成**: shared基礎模組建立
- ✅ **階段2完成**: AnnotationConverter核心重構
- 🎯 **策略模式**: 支援多種格式轉換的可擴展架構
- 🔧 **職責分離**: 格式檢測、轉換、圖像處理分離
- 📊 **統一管理**: 配置驅動、錯誤處理、日誌記錄

### 主要特點
- 🏗️ **模組化設計**：BaseTool統一基類
- 🔍 **智能檢測**：自動格式檢測
- 🔄 **策略模式**：可擴展的轉換架構
- 📸 **圖像處理**：完整的圖像處理管線
- 🌐 **全景支援**：360度圖像專用處理
- ⚡ **性能優化**：並行處理、記憶體管理

## 重構架構

### 目錄結構
```
資料前處理/
├── shared/                     # 基礎架構（階段1完成）
│   ├── base_tool.py           # 工具基類
│   ├── config_manager.py      # 配置管理
│   ├── exceptions.py          # 統一異常處理
│   ├── file_utils.py          # 文件工具
│   └── logger_utils.py        # 日誌工具
├── tools/                     # 重構後的工具（階段2完成）
│   ├── format_detector.py     # 格式檢測器
│   ├── conversion_strategy.py # 轉換策略
│   ├── image_processor.py     # 圖像處理器
│   └── annotation_converter_v2.py # 重構後的主轉換器
├── annotation_converter_compat.py # 向後兼容適配器
├── test_refactored_converter.py   # 測試腳本
├── pyqt_gui_application.py   # GUI應用程式
├── panorama_augmenter.py      # 全景圖像增強器
└── 使用手冊_重構版.md         # 本文件
```

### 架構設計原則
1. **單一職責**: 每個類別專注單一功能
2. **策略模式**: 支援多種轉換格式的可擴展架構
3. **依賴注入**: 使用組合模式而非繼承
4. **統一接口**: 所有工具繼承BaseTool基類

## 快速開始

### 環境設置
```bash
cd 資料前處理
pip install -r requirements.txt

# 安裝圖像處理依賴（可選，用於完整功能）
pip install Pillow
```

### 驗證安裝
```bash
# 運行測試腳本
python3 test_refactored_converter.py
```

### GUI應用程式（推薦）
```bash
# 啟動PyQt6 GUI界面
python pyqt_gui_application.py
```

### 命令行快速使用
```bash
# 全景圖像擴增
python panorama_augmenter.py \
    --input panorama.jpg \
    --output results \
    --omega 15.0 --phi -10.0 --kappa 45.0 \
    --methods orientation \
    --visualize
```

## 核心組件使用

### 1. 重構後的標註轉換器

#### 使用新的AnnotationConverterV2
```python
from tools.annotation_converter_v2 import AnnotationConverterV2

# 創建轉換器
converter = AnnotationConverterV2(
    input_dir="./labelme_data",
    output_dir="./yolo_data",
    input_format="auto",        # 自動檢測格式
    output_format="yolo",       # 輸出YOLO格式
    config={
        'resize': 0.5,          # 圖像縮放比例
        'quality': 85,          # 圖像品質
        'enable_parallel': True, # 啟用並行處理
        'max_workers': 8        # 最大工作線程
    }
)

# 執行轉換
result = converter.run()
print(f"轉換完成: {result}")
```

#### 策略模式使用
```python
from tools.conversion_strategy import LabelMeToYOLOConverter

# 直接使用轉換策略
converter_strategy = LabelMeToYOLOConverter(
    class_mapping={'裂縫': 0, '坑洞': 1, '路面損壞': 2}
)

# 轉換單個文件
success = converter_strategy.convert_file(
    input_file=Path("annotation.json"),
    output_dir=Path("./output")
)

# 獲取統計信息
stats = converter_strategy.get_stats()
print(f"轉換統計: {stats}")
```

### 2. 格式檢測器

#### 自動格式檢測
```python
from tools.format_detector import FormatDetector

detector = FormatDetector()

# 檢測單個文件格式
format_name = detector.detect_format(Path("annotation.json"))
print(f"檢測到格式: {format_name}")

# 檢測目錄格式
dir_format = detector.detect_format(Path("./dataset"))
print(f"目錄格式: {dir_format}")

# 獲取格式可信度
confidence = detector.get_format_confidence(Path("./dataset"))
print(f"各格式可信度: {confidence}")

# 驗證轉換兼容性
is_compatible = detector.validate_format_compatibility('labelme', 'yolo')
print(f"LabelMe -> YOLO 兼容: {is_compatible}")
```

### 3. 圖像處理器

#### 圖像處理和調整
```python
from tools.image_processor import ImageProcessor

processor = ImageProcessor()

# 處理單個圖像和標註
success = processor.process_image_with_annotation(
    image_path=Path("image.jpg"),
    annotation_path=Path("annotation.json"),
    output_dir=Path("./output"),
    resize=0.5,                    # 縮放比例
    quality=85,                    # 圖像品質
    format_convert='jpg'           # 格式轉換
)

# 批次處理圖像
image_files = list(Path("./images").glob("*.jpg"))
annotation_files = list(Path("./annotations").glob("*.json"))

stats = processor.batch_process_images(
    image_files=image_files,
    annotation_files=annotation_files,
    output_dir=Path("./output"),
    resize=(512, 512),             # 指定尺寸
    quality=90
)

print(f"批次處理統計: {stats}")
```

### 4. 共享基礎組件

#### 使用BaseTool創建自定義工具
```python
from shared.base_tool import BaseTool
from shared.exceptions import ProcessingError

class MyCustomTool(BaseTool):
    def validate_inputs(self) -> bool:
        """驗證輸入"""
        if not self.input_dir or not self.input_dir.exists():
            self.logger.error("輸入目錄不存在")
            return False
        return True
    
    def _execute_main_logic(self, **kwargs):
        """執行主要邏輯"""
        files = list(self.input_dir.glob("*.txt"))
        self.stats['total_files'] = len(files)
        
        for file_path in files:
            try:
                # 處理文件
                self._process_file(file_path)
                self.stats['processed_files'] += 1
            except Exception as e:
                self.logger.error(f"處理失敗: {file_path}, {e}")
                self.stats['failed_files'] += 1
        
        return self.stats
    
    def _process_file(self, file_path):
        """處理單個文件"""
        # 實現具體處理邏輯
        pass

# 使用自定義工具
tool = MyCustomTool(
    input_dir="./input",
    output_dir="./output"
)
result = tool.run()
```

#### 配置管理
```python
from shared.config_manager import ConfigManager

# 創建配置管理器
config_manager = ConfigManager("./configs/converter_config.yaml")

# 獲取配置
converter_config = config_manager.get_converter_config("default")
print(f"轉換器配置: {converter_config}")

# 更新配置
config_manager.set('converter.quality', 90)
config_manager.save_config()
```

#### 結構化日誌
```python
from shared.logger_utils import setup_logger

# 創建日誌記錄器
logger = setup_logger("my_tool", level="INFO", log_file="./logs/my_tool.log")

# 結構化日誌記錄
logger.log_processing_start("file.txt", "轉換", format="labelme")
logger.log_processing_success("file.txt", "轉換", duration="1.5s")
logger.log_processing_error(Exception("錯誤"), "file.txt", step="格式檢測")

# 進度記錄
logger.log_progress(current=50, total=100, operation="轉換")

# 統計記錄
logger.log_statistics({
    'total': 100,
    'success': 95,
    'failed': 5
}, operation="轉換")
```

## 重構後的標註轉換器

### 支援的轉換格式

#### 輸入格式
- **LabelMe**: JSON格式，支援多邊形和矩形標註
- **VOC**: XML格式，Pascal VOC格式
- **YOLO**: TXT格式，歸一化邊界框
- **COCO**: JSON格式，MS COCO格式

#### 輸出格式
- **LabelMe**: 統一輸出格式
- **YOLO**: 檢測任務格式（新增）

### 進階配置

#### 完整配置示例
```python
config = {
    # 基本設置
    'resize': 0.5,                    # 圖像縮放
    'quality': 85,                    # 圖像品質
    'batch_size': 20,                 # 批次大小
    'resume': True,                   # 斷點續傳
    
    # 性能設置
    'enable_parallel': True,          # 並行處理
    'max_workers': 8,                 # 最大線程數
    'enable_image_processing': True,  # 圖像處理
    
    # 轉換設置
    'class_mapping': {               # 類別映射
        '裂縫': 0,
        '坑洞': 1,
        '路面損壞': 2
    },
    'target_shape': 'polygon'        # 目標形狀
}

converter = AnnotationConverterV2(
    input_dir="./input",
    output_dir="./output",
    input_format="auto",
    output_format="yolo",
    config=config
)
```

#### 批次轉換不同格式
```python
# VOC to LabelMe
voc_converter = AnnotationConverterV2(
    input_dir="./voc_data",
    output_dir="./labelme_data",
    input_format="voc",
    output_format="labelme"
)

# YOLO to LabelMe
yolo_converter = AnnotationConverterV2(
    input_dir="./yolo_data",
    output_dir="./labelme_data",
    input_format="yolo",
    output_format="labelme"
)

# 執行轉換
voc_result = voc_converter.run()
yolo_result = yolo_converter.run()
```

### 轉換結果分析

#### 獲取詳細統計
```python
# 執行轉換
result = converter.run()

# 獲取詳細統計
detailed_stats = converter.get_conversion_stats()

print(f"基本統計: {result}")
print(f"詳細統計: {detailed_stats}")

# 包含的統計信息：
# - 格式檢測時間
# - 轉換時間
# - 圖像處理時間
# - 各轉換器統計
# - 圖像處理器統計
```

## 向後兼容性

### 使用原始API
```python
# 使用向後兼容適配器
from annotation_converter_compat import AnnotationConverter

# 原始API繼續可用
converter = AnnotationConverter()

# 使用原始方法
result = converter.convert_format(
    input_path="./input",
    output_path="./output",
    source_format="auto",
    target_shape="polygon",
    resize=0.5,
    quality=85
)

print(f"轉換結果: {result}")
```

### 從舊版本遷移

#### 遷移步驟
1. **保持原始代碼**：舊代碼可以繼續使用
2. **逐步測試**：在測試環境中驗證新功能
3. **漸進遷移**：逐步替換為新API
4. **性能優化**：利用新功能提升性能

#### 遷移示例
```python
# 舊代碼
from annotation_converter import AnnotationConverter
converter = AnnotationConverter()
result = converter.convert_format("./input", "./output")

# 新代碼（推薦）
from tools.annotation_converter_v2 import AnnotationConverterV2
converter = AnnotationConverterV2("./input", "./output")
result = converter.run()

# 或使用兼容適配器
from annotation_converter_compat import AnnotationConverter
converter = AnnotationConverter()
result = converter.convert_format("./input", "./output")
```

## 全景圖像處理

### 全景圖像擴增器
```bash
# 基本擴增
python panorama_augmenter.py \
    --input panorama.jpg \
    --output results \
    --omega 15.0 --phi -10.0 --kappa 45.0 \
    --methods orientation \
    --visualize

# 多種方法組合
python panorama_augmenter.py \
    --input panorama.jpg \
    --output results \
    --methods orientation perspective tilt \
    --visualize --save-visualization

# 批量處理
python panorama_augmenter.py \
    --input input_directory \
    --output results \
    --excel orientations.xlsx \
    --methods orientation \
    --batch
```

### 全景圖像參數說明
- **Omega**: 俯仰角（相機繞X軸旋轉）
- **Phi**: 橫滾角（相機繞Y軸旋轉）
- **Kappa**: 偏航角（相機繞Z軸旋轉）

## 最佳實踐

### 1. 性能優化

#### 大數據集處理
```python
# 配置優化設置
config = {
    'enable_parallel': True,
    'max_workers': min(32, os.cpu_count() * 2),  # 根據CPU調整
    'batch_size': 50,                            # 增加批次大小
    'enable_image_processing': False,            # 禁用圖像處理（如不需要）
    'resume': True                               # 啟用斷點續傳
}

converter = AnnotationConverterV2(
    input_dir="./large_dataset",
    output_dir="./output",
    config=config
)
```

#### 記憶體管理
```python
# 處理大圖像時的設置
image_config = {
    'resize': (512, 512),     # 固定尺寸避免記憶體波動
    'quality': 75,            # 適中的品質
    'format_convert': 'jpg'   # 轉換為壓縮格式
}

processor = ImageProcessor()
```

### 2. 錯誤處理

#### 完整的錯誤處理
```python
from shared.exceptions import ConversionError, FormatDetectionError

try:
    converter = AnnotationConverterV2(
        input_dir="./input",
        output_dir="./output"
    )
    result = converter.run()
    
except FormatDetectionError as e:
    print(f"格式檢測失敗: {e}")
except ConversionError as e:
    print(f"轉換失敗: {e}")
except Exception as e:
    print(f"未知錯誤: {e}")
```

### 3. 日誌和監控

#### 結構化日誌配置
```python
from shared.logger_utils import setup_logger

# 設置詳細日誌
logger = setup_logger(
    "annotation_converter",
    level="DEBUG",
    log_file="./logs/conversion.log"
)

converter = AnnotationConverterV2(
    input_dir="./input",
    output_dir="./output",
    logger=logger
)
```

## 故障排除

### 常見問題

#### 1. PIL/Pillow未安裝
**症狀**: 提示"No module named 'PIL'"
**解決方案**:
```bash
pip install Pillow
```

#### 2. 格式檢測失敗
**症狀**: FormatDetectionError
**解決方案**:
```python
# 手動指定格式
converter = AnnotationConverterV2(
    input_dir="./input",
    output_dir="./output",
    input_format="labelme",  # 明確指定而非auto
    output_format="yolo"
)
```

#### 3. 轉換失敗
**症狀**: 部分文件轉換失敗
**解決方案**:
```python
# 檢查轉換統計
result = converter.run()
print(f"成功: {result['processed_files']}")
print(f"失敗: {result['failed_files']}")

# 查看詳細日誌
converter.logger.setLevel("DEBUG")
```

#### 4. 記憶體不足
**症狀**: 處理大圖像時記憶體不足
**解決方案**:
```python
# 減少並行數量
config = {
    'max_workers': 2,
    'batch_size': 10,
    'resize': 0.3  # 更小的縮放比例
}
```

#### 5. 全景圖像比例錯誤
**症狀**: "圖片比例不正確！應為 2:1 比例"
**解決方案**:
- 確保輸入的是等距圓柱投影全景圖像
- 檢查圖像寬度是否等於高度的2倍
- 使用圖像編輯軟體調整比例

### 性能調優

#### 1. 並行處理優化
```python
import os

# 根據任務類型調整線程數
if is_io_intensive:
    max_workers = min(32, os.cpu_count() * 4)  # IO密集型
else:
    max_workers = os.cpu_count()               # CPU密集型

config = {'max_workers': max_workers}
```

#### 2. 圖像處理優化
```python
# 批次處理而非逐個處理
processor = ImageProcessor()
stats = processor.batch_process_images(
    image_files=all_images,
    annotation_files=all_annotations,
    output_dir=output_dir,
    resize=0.5,
    quality=85
)
```

## 開發指南

### 添加新的轉換格式

#### 1. 創建新的轉換策略
```python
from tools.conversion_strategy import ConversionStrategy

class MyFormatToLabelMeConverter(ConversionStrategy):
    def convert_file(self, input_file, output_dir, **kwargs):
        # 實現轉換邏輯
        pass
    
    def validate_input(self, input_file):
        # 實現輸入驗證
        pass
```

#### 2. 註冊新格式
```python
# 在format_detector.py中添加檢測邏輯
def _detect_my_format(self, file_path):
    # 實現格式檢測
    pass

# 在annotation_converter_v2.py中添加策略映射
strategy_map = {
    ('my_format', 'labelme'): lambda: MyFormatToLabelMeConverter(logger=self.logger),
    # ... 其他映射
}
```

## 版本歷史

### v3.0 (重構版)
- ✅ **階段1**: shared基礎模組建立
- ✅ **階段2**: AnnotationConverter核心重構
- 🎯 **策略模式**: 支援多種格式轉換
- 🔧 **職責分離**: 格式檢測、轉換、圖像處理分離
- 📊 **統一管理**: 配置驅動、錯誤處理、日誌記錄

### v2.0 (GUI版本)
- PyQt6 GUI界面
- 全景圖像擴增器
- 基礎的標註轉換功能

### v1.0 (命令行版本)
- 基礎命令行工具
- 簡單的格式轉換

---

## 📞 技術支援

如有問題，請參考：
1. **重構指南**: `重構階段指南.md`
2. **測試腳本**: `test_refactored_converter.py`
3. **配置文件**: `configs/` 目錄
4. **原始手冊**: `使用手冊.md`

© 2024 資料前處理重構團隊