# 增強YOLO推理配置文件

# 模型配置
detection_model_path: ""      # YOLO12檢測模型路徑
segmentation_model_path: "D:\\4_road_crack\\best.pt" # YOLO11分割模型路徑
device: "auto"  # auto, cuda, cpu

# 推理配置
img_size: 640
global_conf: 0.05        # 全局置信度閾值
iou_threshold: 0.45      # IoU閾值
max_det: 1000           # 最大檢測數量

# 類別配置 - 道路基礎設施檢測
class_configs:
  0:
    name: "expansion_joint_伸縮縫"
    conf_threshold: 0.5
    color: [255, 0, 0]    # 紅色
    enabled: true
    description: ""
  
  1:
    name: "joint_路面接縫"
    conf_threshold: 0.4
    color: [0, 255, 0]    # 綠色
    enabled: true
    description: ""
  
  2:
    name: "linear_crack_裂縫"
    conf_threshold: 0.6
    color: [0, 0, 255]    # 藍色
    enabled: true
    description: ""
  
  3:
    name: "Alligator_crack_龜裂"
    conf_threshold: 0.3
    color: [255, 255, 0]  # 黃色
    enabled: true
    description: ""
  
  4:
    name: "potholes_坑洞"
    conf_threshold: 0.5
    color: [255, 0, 255]  # 洋紅色
    enabled: true
    description: ""

  5:
    name: "patch_補綻"
    conf_threshold: 0.5
    color: [128, 0, 128]  # 紫色
    enabled: true
    description: ""

  6:
    name: "manhole_人孔蓋或排水溝"
    conf_threshold: 0.5
    color: [255, 165, 0]  # 橙色
    enabled: true
    description: ""

  7:
    name: "deformation_變形"
    conf_threshold: 0.5
    color: [0, 255, 255]  # 青色
    enabled: true
    description: ""

  8:
    name: "dirt_污垢"
    conf_threshold: 0.5
    color: [128, 128, 128]  # 灰色
    enabled: true
    description: ""
    
# SAHI配置 - 大圖像切片推理
enable_sahi: true
slice_height: 512
slice_width: 512
overlap_height_ratio: 0.2
overlap_width_ratio: 0.2

# 輸出配置
save_visualizations: true
save_predictions: true
save_statistics: true
output_format: "both"  # detection, segmentation, both

# 標註轉換配置
auto_convert_annotations: true
input_annotation_format: "auto"  # auto, labelme, yolo, coco, voc

# 高級功能
enable_tracking: false          # 物件追蹤
enable_pose_estimation: false   # 姿態估計
enable_classification: false    # 分類
enable_batch_processing: true   # 批次處理

# 性能配置
performance:
  half_precision: true          # 半精度推理
  tensorrt: false              # TensorRT加速
  openvino: false              # OpenVINO加速
  
# 後處理配置
post_processing:
  nms_method: "standard"       # standard, fast, merge
  agnostic_nms: false          # 類別無關NMS
  multi_label: false           # 多標籤分類

# 可視化配置
visualization:
  line_thickness: 2
  font_size: 0.6
  show_confidence: true
  show_class_names: true
  transparency: 0.3

# 統計配置
statistics:
  enable_timing: true          # 啟用時間統計
  enable_memory_tracking: true # 啟用記憶體追蹤
  save_detailed_stats: true    # 保存詳細統計

# 品質控制
quality_control:
  min_detection_size: 10       # 最小檢測尺寸（像素）
  max_detection_size: 5000     # 最大檢測尺寸（像素）
  confidence_smoothing: false  # 置信度平滑
  
# 輸出格式配置
output_formats:
  json: true                   # JSON格式
  csv: true                    # CSV格式
  xml: false                   # XML格式
  coco: false                  # COCO格式

# 道路檢測特定配置
road_detection:
  crack_sensitivity: 0.7       # 裂縫檢測敏感度
  pothole_min_area: 100       # 坑洞最小面積
  manhole_aspect_ratio: [0.8, 1.2]  # 人孔蓋長寬比範圍
  
# 企業級配置
enterprise:
  enable_logging: true
  log_level: "INFO"
  enable_metrics: true
  enable_alerts: false