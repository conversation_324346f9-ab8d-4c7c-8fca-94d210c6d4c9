#!/usr/bin/env python3
"""
修復 _save_results 方法錯誤的完整解決方案
"""

import os
import sys
from pathlib import Path

def clean_python_cache():
    """清理Python緩存文件"""
    print("🧹 清理Python緩存文件...")
    
    project_root = Path(__file__).parent
    
    # 清理 __pycache__ 目錄
    pycache_dirs = list(project_root.rglob("__pycache__"))
    for pycache_dir in pycache_dirs:
        try:
            import shutil
            shutil.rmtree(pycache_dir)
            print(f"   ✅ 清理: {pycache_dir}")
        except Exception as e:
            print(f"   ⚠️  無法清理 {pycache_dir}: {e}")
    
    # 清理 .pyc 文件
    pyc_files = list(project_root.rglob("*.pyc"))
    for pyc_file in pyc_files:
        try:
            pyc_file.unlink()
            print(f"   ✅ 清理: {pyc_file}")
        except Exception as e:
            print(f"   ⚠️  無法清理 {pyc_file}: {e}")
    
    print(f"🎉 清理完成! 清理了 {len(pycache_dirs)} 個緩存目錄和 {len(pyc_files)} 個.pyc文件")

def verify_fix():
    """驗證修復"""
    print("🔍 驗證 _save_results 修復...")
    
    enhanced_yolo_file = Path(__file__).parent / "AI模型建構訓練驗證" / "model_create" / "inference" / "enhanced_yolo_inference.py"
    
    if not enhanced_yolo_file.exists():
        print(f"❌ 文件不存在: {enhanced_yolo_file}")
        return False
    
    # 檢查方法簽名
    with open(enhanced_yolo_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查是否有正確的方法簽名
    correct_signature = "def _save_results(self, results: Dict[str, Any], image_path: str, output_dir: str, annotation_path: str = None):"
    
    if correct_signature in content:
        print("✅ _save_results方法簽名正確")
    else:
        print("❌ _save_results方法簽名不正確")
        print("正在嘗試修復...")
        
        # 嘗試修復
        old_signature = "def _save_results(self, results: Dict[str, Any], image_path: str, output_dir: str):"
        if old_signature in content:
            content = content.replace(old_signature, correct_signature)
            with open(enhanced_yolo_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 方法簽名已修復")
        else:
            print("❌ 無法找到方法定義進行修復")
            return False
    
    # 檢查調用
    correct_call = "self._save_results(results, image_path, output_dir, annotation_path)"
    if correct_call in content:
        print("✅ _save_results調用正確")
    else:
        print("❌ _save_results調用不正確")
        return False
    
    return True

def main():
    print("🔧 Enhanced YOLO _save_results 錯誤修復工具")
    print("=" * 50)
    
    # 步驟1: 清理緩存
    clean_python_cache()
    print()
    
    # 步驟2: 驗證修復
    if verify_fix():
        print("✅ 修復驗證成功!")
        print()
        print("📋 解決方案總結:")
        print("  1. ✅ 清理了Python緩存文件")
        print("  2. ✅ 驗證了方法簽名正確")
        print("  3. ✅ 驗證了方法調用正確")
        print()
        print("🚀 請重新運行您的程序，錯誤應該已經解決!")
        print()
        print("💡 如果問題仍然存在，請:")
        print("   - 重啟Python程序")
        print("   - 檢查是否有多個版本的文件")
        print("   - 確保沒有其他進程正在使用舊版本")
        
    else:
        print("❌ 修復驗證失敗!")
        print("請手動檢查 enhanced_yolo_inference.py 文件")

if __name__ == "__main__":
    main()