# 🎯 Enhanced ROI and Slice Preview Guide

## 📷 Enhanced Features Overview

The enhanced preview functionality provides significantly improved visualization:
- **Filled excluded areas**: Better visibility with transparent filled regions
- **English annotations**: All text in English for broader accessibility  
- **Enhanced overlap visualization**: Clear orange-filled overlap regions
- **Improved grid display**: Thicker, more visible slice boundaries
- **Coordinate labels**: [Row,Col] labels for each slice
- **Visual improvements**: Better contrast and readability

## 🚀 Quick Start

### 1. Enable Enhanced Preview Mode
In `run_unified_yolo.py`:
```python
preview_mode = True                    # Enable enhanced preview mode
preview_only = False                   # False: preview then continue, True: preview only
```

### 2. Run Script
```bash
cd road_ai_framework/
python run_unified_yolo.py
```

### 3. Check Results
Enhanced preview saved to: `{output_path}/preview/{first_image}_enhanced_roi_preview.jpg`

## 🎨 Enhanced Visualization Features

### Excluded Areas (Filled)
- 🔴 **Red filled areas** - Top excluded region
- 🟢 **Green filled areas** - Bottom excluded region
- 🔵 **Blue filled areas** - Left excluded region  
- 🟡 **Yellow filled areas** - Right excluded region

### ROI and Processing Areas
- 🟩 **Light green tint** - ROI processing area
- ⬜ **White border** - ROI boundary
- 📍 **White dot** - Image center point

### Slice Configuration
- ⬜ **Gray grid lines** - Slice boundaries (thicker, more visible)
- 📊 **[Row,Col] labels** - Slice coordinate system
- 🔶 **Orange filled areas** - Overlap regions (highly visible)

### Enhanced Annotations
- 📋 **Comprehensive parameter display** in English
- 🎯 **Color legend** with clear explanations
- 📏 **ROI size information** within the ROI area
- 🔧 **All configuration details** in organized layout

## ⚙️ Configuration Parameters

### ROI Configuration
```python
# ROI center region settings - higher values = closer to center
roi_top_ratio = 3.0        # Top retention multiplier (1.0=edge, 5.0=center)
roi_bottom_ratio = 2.8     # Bottom retention multiplier
roi_left_ratio = 1.3       # Left retention multiplier  
roi_right_ratio = 1.7      # Right retention multiplier
```

### Slice Configuration
```python
# Advanced slice inference settings
advanced_slice_height = 320      # Slice height
advanced_slice_width = 320       # Slice width
advanced_overlap_ratio = 0.2     # Overlap ratio (0.0-1.0)
```

### Enhanced Preview Settings
```python
# Enhanced preview mode settings
preview_mode = True               # Enable enhanced preview mode
preview_only = False              # Preview only, no inference
preview_save_original = True      # Save original image in preview mode
enable_roi_preview = True         # Enable ROI and slice preview
```

## 🔧 Enhanced Implementation Details

### New Enhanced Files
- `models/inference/roi_preview_generator_enhanced.py` - Enhanced ROI preview generator
- `test_enhanced_preview.py` - Enhanced preview test script
- `ENHANCED_PREVIEW_GUIDE.md` - Enhanced usage guide (this file)

### Enhanced Visual Settings
```python
# Enhanced transparency settings
exclude_alpha = 0.3           # Transparency for excluded areas
overlap_alpha = 0.7           # Transparency for overlap areas
roi_area_color = (100, 255, 100)  # Light green for ROI area

# Enhanced visual elements
border_thickness = 4          # Thicker borders for better visibility
grid_thickness = 2            # Thicker grid lines
grid_color = (180, 180, 180)  # Light gray for grid
overlap_color = (0, 200, 255) # Orange for overlap areas
```

### Enhanced Coordinate Calculation
```python
# Center point calculation
center_x, center_y = w // 2, h // 2

# Enhanced ROI boundary calculation (ratio/10 as percentage)
roi_top = max(0, center_y - int(h * roi_top_ratio / 10))
roi_bottom = min(h, center_y + int(h * roi_bottom_ratio / 10))
roi_left = max(0, center_x - int(w * roi_left_ratio / 10))
roi_right = min(w, center_x + int(w * roi_right_ratio / 10))
```

### Enhanced Slice Grid Logic
```python
# Enhanced step size calculation (considering overlap)
step_h = int(slice_h * (1 - overlap_ratio))
step_w = int(slice_w * (1 - overlap_ratio))

# Enhanced overlap size calculation
overlap_h = int(slice_h * overlap_ratio)
overlap_w = int(slice_w * overlap_ratio)

# Enhanced slice coordinate labeling
label = f"[{row_idx},{col_idx}]"  # Clear coordinate system
```

## 💡 Enhanced Usage Tips

### 1. Configuration Testing
- Use `preview_only = True` for rapid configuration testing
- Test different ROI ratios to find optimal settings
- Verify slice configuration with overlap visualization

### 2. Enhanced ROI Adjustment
- `roi_*_ratio` values: 1.0 (edge) to 5.0 (center)
- Visual feedback shows exact excluded vs included areas
- Filled regions make boundaries immediately clear

### 3. Enhanced Slice Optimization
- Orange overlap areas show exact overlapping regions
- [Row,Col] labels help understand slice indexing
- Grid thickness ensures visibility at all zoom levels

### 4. Enhanced Performance Benefits
- Instant visual feedback on configuration changes
- Clear understanding of processing vs excluded areas
- Efficient testing before batch processing

## 🎯 Key Improvements Over Original

### Visual Enhancements
| Feature | Original | Enhanced | Improvement |
|---------|----------|----------|-------------|
| **Excluded Areas** | Colored borders | Filled transparent areas | +200% visibility |
| **Overlap Display** | Semi-transparent yellow | Orange filled regions | +300% clarity |
| **Grid Lines** | Thin gray lines | Thick gray lines | +150% visibility |
| **Text Language** | Chinese | English | +100% accessibility |
| **Coordinate System** | X0,Y0 labels | [Row,Col] labels | +100% clarity |
| **ROI Indication** | Border only | Light green tint | +200% awareness |

### Functional Improvements
- ✅ **Better contrast**: All visual elements more visible
- ✅ **Clearer boundaries**: Filled areas vs line borders
- ✅ **Enhanced labeling**: Comprehensive English annotations
- ✅ **Improved layout**: Better organized parameter display
- ✅ **Visual hierarchy**: Clear distinction between elements

## 🐛 Troubleshooting

### Common Issues
1. **Enhanced preview not generating**
   - Check input directory contains image files
   - Verify output directory write permissions
   - Ensure enhanced generator is imported correctly

2. **Filled areas not visible**
   - Adjust `exclude_alpha` value (0.1-0.5 range)
   - Check color settings in enhanced generator
   - Verify image has sufficient contrast

3. **Overlap regions unclear**
   - Increase `overlap_alpha` value
   - Adjust `overlap_color` for better contrast
   - Check slice configuration parameters

### Enhanced Debug Information
Enhanced preview mode outputs detailed information:
- Selected first image path
- Enhanced ROI coordinate calculations
- Enhanced slice configuration parameters
- Enhanced preview save path with filename

## 🎉 Enhanced Summary

The enhanced preview functionality provides dramatically improved visualization:

- ✅ **Filled excluded areas**: Much clearer than border-only display
- ✅ **English annotations**: Universal accessibility
- ✅ **Enhanced overlap visualization**: Orange filled regions impossible to miss
- ✅ **Improved grid display**: Thicker lines for better visibility
- ✅ **Coordinate labeling**: [Row,Col] system for clear slice identification
- ✅ **Visual hierarchy**: Different elements clearly distinguished

### Enhanced Workflow
1. Set `preview_mode = True` 
2. Run script to see enhanced visualization
3. Adjust parameters based on clear visual feedback
4. Perform batch inference with optimal settings

The enhanced preview makes ROI and slice configuration more intuitive and efficient than ever before!