#!/usr/bin/env python3
"""
圖像處理工具函數
簡化的圖像處理功能，專注於基本需求
"""

import os
from pathlib import Path
from typing import Tuple, Optional, List

import numpy as np
import cv2


def load_image(image_path: str) -> Optional[np.ndarray]:
    """
    載入圖像
    
    Args:
        image_path: 圖像路徑
        
    Returns:
        np.ndarray: 圖像數組，載入失敗時返回None
    """
    if not os.path.exists(image_path):
        return None
    
    try:
        image = cv2.imread(image_path)
        return image
    except Exception:
        return None


def save_image(image: np.ndarray, output_path: str) -> bool:
    """
    保存圖像
    
    Args:
        image: 圖像數組
        output_path: 輸出路徑
        
    Returns:
        bool: 保存是否成功
    """
    try:
        # 創建輸出目錄
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        return cv2.imwrite(output_path, image)
    except Exception:
        return False


def resize_image(image: np.ndarray, 
                target_size: Tuple[int, int],
                keep_aspect_ratio: bool = True) -> np.ndarray:
    """
    調整圖像大小
    
    Args:
        image: 輸入圖像
        target_size: 目標大小 (width, height)
        keep_aspect_ratio: 是否保持長寬比
        
    Returns:
        np.ndarray: 調整後的圖像
    """
    if not keep_aspect_ratio:
        return cv2.resize(image, target_size)
    
    h, w = image.shape[:2]
    target_w, target_h = target_size
    
    # 計算縮放比例
    scale = min(target_w / w, target_h / h)
    new_w, new_h = int(w * scale), int(h * scale)
    
    # 調整大小
    resized = cv2.resize(image, (new_w, new_h))
    
    # 創建目標大小的畫布
    canvas = np.zeros((target_h, target_w, 3), dtype=np.uint8)
    
    # 計算置中位置
    y_offset = (target_h - new_h) // 2
    x_offset = (target_w - new_w) // 2
    
    # 將調整後的圖像放到畫布中央
    canvas[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
    
    return canvas


def validate_image_path(image_path: str, 
                       valid_extensions: List[str] = None) -> bool:
    """
    驗證圖像路徑的有效性
    
    Args:
        image_path: 圖像路徑
        valid_extensions: 有效的副檔名列表
        
    Returns:
        bool: 路徑是否有效
    """
    if valid_extensions is None:
        valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    
    # 檢查文件是否存在
    if not os.path.exists(image_path):
        return False
    
    # 檢查副檔名
    file_ext = Path(image_path).suffix.lower()
    if file_ext not in valid_extensions:
        return False
    
    # 嘗試載入圖像
    image = load_image(image_path)
    return image is not None


def get_image_info(image_path: str) -> Optional[dict]:
    """
    獲取圖像信息
    
    Args:
        image_path: 圖像路徑
        
    Returns:
        dict: 圖像信息，載入失敗時返回None
    """
    image = load_image(image_path)
    if image is None:
        return None
    
    h, w = image.shape[:2]
    channels = image.shape[2] if len(image.shape) > 2 else 1
    
    return {
        'path': image_path,
        'width': w,
        'height': h,
        'channels': channels,
        'size': (w, h),
        'file_size': os.path.getsize(image_path)
    }


def create_color_palette(num_colors: int, seed: int = 42) -> List[Tuple[int, int, int]]:
    """
    創建顏色調色板
    
    Args:
        num_colors: 需要的顏色數量
        seed: 隨機種子
        
    Returns:
        List[Tuple]: RGB顏色列表
    """
    np.random.seed(seed)
    colors = []
    
    for i in range(num_colors):
        # 生成HSV顏色，然後轉換為RGB
        hue = (i * 360 / num_colors) % 360
        saturation = 0.7 + (i % 3) * 0.1  # 0.7, 0.8, 0.9
        value = 0.8 + (i % 2) * 0.2       # 0.8, 1.0
        
        # HSV to RGB conversion
        hsv = np.array([[[hue, saturation * 255, value * 255]]], dtype=np.uint8)
        rgb = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)[0, 0]
        
        colors.append((int(rgb[0]), int(rgb[1]), int(rgb[2])))
    
    return colors


def draw_bbox(image: np.ndarray, 
              bbox: List[float],
              label: str = "",
              color: Tuple[int, int, int] = (0, 255, 0),
              thickness: int = 2,
              font_scale: float = 0.6) -> np.ndarray:
    """
    在圖像上繪製邊界框
    
    Args:
        image: 輸入圖像
        bbox: 邊界框 [x1, y1, x2, y2]
        label: 標籤文字
        color: 顏色 (R, G, B)
        thickness: 線條粗細
        font_scale: 字體大小
        
    Returns:
        np.ndarray: 繪製後的圖像
    """
    x1, y1, x2, y2 = map(int, bbox)
    
    # 繪製邊界框
    cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
    
    # 繪製標籤
    if label:
        # 計算文字大小
        (text_w, text_h), baseline = cv2.getTextSize(
            label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness
        )
        
        # 繪製文字背景
        cv2.rectangle(image, (x1, y1 - text_h - baseline - 5),
                     (x1 + text_w + 5, y1), color, -1)
        
        # 繪製文字
        cv2.putText(image, label, (x1 + 2, y1 - baseline - 2),
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), thickness)
    
    return image


def draw_mask(image: np.ndarray,
              mask: np.ndarray,
              color: Tuple[int, int, int] = (0, 255, 0),
              alpha: float = 0.3) -> np.ndarray:
    """
    在圖像上繪製半透明遮罩
    
    Args:
        image: 輸入圖像
        mask: 二值遮罩
        color: 遮罩顏色 (R, G, B)
        alpha: 透明度
        
    Returns:
        np.ndarray: 繪製後的圖像
    """
    colored_mask = np.zeros_like(image)
    colored_mask[mask > 0] = color
    
    # 混合圖像和遮罩
    result = cv2.addWeighted(image, 1 - alpha, colored_mask, alpha, 0)
    return result


def create_grid_image(images: List[np.ndarray], 
                     grid_size: Optional[Tuple[int, int]] = None,
                     padding: int = 10) -> np.ndarray:
    """
    創建圖像網格
    
    Args:
        images: 圖像列表
        grid_size: 網格大小 (rows, cols)，None表示自動計算
        padding: 圖像間距
        
    Returns:
        np.ndarray: 網格圖像
    """
    if not images:
        return np.zeros((100, 100, 3), dtype=np.uint8)
    
    num_images = len(images)
    
    # 自動計算網格大小
    if grid_size is None:
        cols = int(np.ceil(np.sqrt(num_images)))
        rows = int(np.ceil(num_images / cols))
        grid_size = (rows, cols)
    
    rows, cols = grid_size
    
    # 獲取統一的圖像大小
    max_h = max(img.shape[0] for img in images)
    max_w = max(img.shape[1] for img in images)
    
    # 創建網格畫布
    grid_h = rows * max_h + (rows + 1) * padding
    grid_w = cols * max_w + (cols + 1) * padding
    grid = np.ones((grid_h, grid_w, 3), dtype=np.uint8) * 255
    
    # 放置圖像
    for i, img in enumerate(images):
        row = i // cols
        col = i % cols
        
        if row >= rows:
            break
        
        # 調整圖像大小
        img_resized = cv2.resize(img, (max_w, max_h))
        
        # 計算位置
        y_start = row * max_h + (row + 1) * padding
        x_start = col * max_w + (col + 1) * padding
        
        # 放置圖像
        grid[y_start:y_start+max_h, x_start:x_start+max_w] = img_resized
    
    return grid