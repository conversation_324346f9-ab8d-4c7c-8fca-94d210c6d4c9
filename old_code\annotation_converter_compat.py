#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
標註轉換器向後兼容適配器

保持原有API不變，內部使用重構後的實現
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Union, Tuple, Optional, Any

from tools.annotation_converter_v2 import AnnotationConverterV2


class AnnotationConverter:
    """
    原始API的向後兼容適配器
    
    保持與原始 AnnotationConverter 相同的接口，
    內部使用重構後的 AnnotationConverterV2 實現
    """
    
    def __init__(self, logger=None, max_workers: int = None):
        """
        初始化標籤轉換器（向後兼容）
        
        參數:
            logger: 日誌記錄器，如果為None則創建新的
            max_workers: 最大並行工作線程數量，None表示根據CPU數量自動決定
        """
        self.logger = logger or logging.getLogger(__name__)
        self.max_workers = max_workers or min(32, os.cpu_count() * 2)
        
        # 內部使用新的實現，但不初始化
        self._converter_v2 = None
        
        # 保持原有的統計格式
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }
        
        # 支持的格式（保持原有）
        self.supported_formats = ['voc', 'yolo', 'coco', 'labelme']
        
        # 檢查點文件名（保持原有）
        self.checkpoint_file = ".converter_checkpoint.json"
    
    def convert_format(self, 
                      input_path: Union[str, Path], 
                      output_path: Union[str, Path], 
                      source_format: str = 'auto', 
                      target_shape: str = 'polygon', 
                      resize: Union[float, Tuple[int, int]] = 0.3, 
                      quality: int = 75,
                      batch_size: int = 10,
                      resume: bool = True) -> Dict[str, Any]:
        """
        將各種格式轉換為LabelMe格式（向後兼容接口）
        
        參數:
            input_path: 輸入文件或目錄路徑
            output_path: 輸出目錄路徑
            source_format: 輸入格式 ('auto', 'voc', 'yolo', 'coco', 'labelme')
            target_shape: 轉換目標形狀 ('box', 'polygon')
            resize: 圖像縮放參數，可以是縮放比例(如0.3)或(寬,高)元組
            quality: 圖像品質參數 (1-100)
            batch_size: 批處理大小
            resume: 是否從中斷點恢復
        
        返回:
            轉換統計信息字典
        """
        try:
            # 創建配置
            config = {
                'resize': resize,
                'quality': quality,
                'batch_size': batch_size,
                'resume': resume,
                'max_workers': self.max_workers,
                'target_shape': target_shape,
                'enable_image_processing': True,
                'enable_parallel': True
            }
            
            # 創建新的轉換器實例
            self._converter_v2 = AnnotationConverterV2(
                input_dir=str(input_path),
                output_dir=str(output_path),
                input_format=source_format,
                output_format='labelme',  # 原始API總是轉換為LabelMe
                config=config,
                logger=self.logger
            )
            
            # 執行轉換
            result = self._converter_v2.run()
            
            # 轉換統計格式以保持向後兼容
            self.stats = self._convert_stats_format(result)
            
            return self.stats
            
        except Exception as e:
            self.logger.error(f"轉換失敗: {e}")
            self.stats['failed'] = self.stats.get('total', 0)
            return self.stats
    
    def validate_annotations(self, 
                           input_path: Union[str, Path], 
                           resize: Union[float, Tuple[int, int]] = None,
                           quality: int = 75) -> Dict[str, Any]:
        """
        檢查和處理LabelMe格式文件（向後兼容接口）
        
        參數:
            input_path: 輸入目錄路徑
            resize: 圖像縮放參數
            quality: 圖像品質參數
        
        返回:
            處理統計信息字典
        """
        try:
            # 創建配置
            config = {
                'resize': resize,
                'quality': quality,
                'max_workers': self.max_workers,
                'enable_image_processing': True,
                'enable_parallel': True
            }
            
            # 創建新的轉換器實例（LabelMe自處理）
            self._converter_v2 = AnnotationConverterV2(
                input_dir=str(input_path),
                output_dir=str(input_path),  # 原地處理
                input_format='labelme',
                output_format='labelme',
                config=config,
                logger=self.logger
            )
            
            # 執行處理
            result = self._converter_v2.run()
            
            # 轉換統計格式
            return self._convert_stats_format(result)
            
        except Exception as e:
            self.logger.error(f"驗證失敗: {e}")
            return {"total": 0, "success": 0, "failed": 1, "skipped": 0}
    
    def process_multiple_inputs(self, 
                              input_paths: List[Union[str, Path]], 
                              output_path: Union[str, Path],
                              source_format: str = 'auto',
                              **kwargs) -> Dict[str, Any]:
        """
        處理多個輸入路徑（向後兼容接口）
        
        參數:
            input_paths: 輸入路徑列表
            output_path: 輸出目錄路徑
            source_format: 輸入格式
            **kwargs: 其他參數
        
        返回:
            處理統計信息字典
        """
        total_stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }
        
        for input_path in input_paths:
            try:
                result = self.convert_format(
                    input_path, output_path, source_format, **kwargs
                )
                
                # 累積統計
                total_stats["total"] += result.get("total", 0)
                total_stats["success"] += result.get("success", 0)
                total_stats["failed"] += result.get("failed", 0)
                total_stats["skipped"] += result.get("skipped", 0)
                
                # 合併格式統計
                if "formats" in result:
                    for fmt, count in result["formats"].items():
                        total_stats["formats"][fmt] = total_stats["formats"].get(fmt, 0) + count
                
            except Exception as e:
                self.logger.error(f"處理路徑失敗 {input_path}: {e}")
                total_stats["failed"] += 1
        
        self.stats = total_stats
        return self.stats
    
    def _convert_stats_format(self, new_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        將新統計格式轉換為舊格式
        
        Args:
            new_stats: 新的統計數據
            
        Returns:
            Dict[str, Any]: 舊格式統計數據
        """
        return {
            "total": new_stats.get("total_files", 0),
            "success": new_stats.get("processed_files", 0),
            "failed": new_stats.get("failed_files", 0),
            "skipped": new_stats.get("skipped_files", 0),
            "formats": {}  # 可以根據需要添加格式統計
        }
    
    def _reset_stats(self):
        """重置統計信息（向後兼容）"""
        self.stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "formats": {}
        }
    
    def _detect_format(self, input_path: Union[str, Path]) -> str:
        """
        檢測輸入路徑的標註格式（向後兼容）
        
        Args:
            input_path: 輸入路徑
            
        Returns:
            str: 檢測到的格式
        """
        try:
            if self._converter_v2 is None:
                # 創建臨時實例用於格式檢測
                temp_converter = AnnotationConverterV2(
                    input_dir=str(input_path),
                    output_dir="/tmp",  # 臨時路徑
                    logger=self.logger
                )
                return temp_converter.format_detector.detect_format(Path(input_path))
            else:
                return self._converter_v2.format_detector.detect_format(Path(input_path))
                
        except Exception as e:
            self.logger.error(f"格式檢測失敗: {e}")
            raise
    
    # 以下方法保持向後兼容，委託給新實現或提供基本功能
    
    def _detect_directory_format(self, dir_path: Path) -> str:
        """檢測目錄格式（向後兼容）"""
        return self._detect_format(dir_path)
    
    def _detect_file_format(self, file_path: Path) -> str:
        """檢測文件格式（向後兼容）"""
        return self._detect_format(file_path)
    
    def _is_labelme_format(self, data: Dict[str, Any]) -> bool:
        """檢查是否為LabelMe格式（向後兼容）"""
        required_fields = ['version', 'flags', 'shapes']
        return all(field in data for field in required_fields[:3])
    
    def _is_coco_format(self, data: Dict[str, Any]) -> bool:
        """檢查是否為COCO格式（向後兼容）"""
        required_fields = ['images', 'annotations', 'categories']
        return all(field in data for field in required_fields)
    
    # 提供對新功能的訪問（可選）
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """
        獲取詳細統計信息（新功能）
        
        Returns:
            Dict[str, Any]: 詳細統計信息
        """
        if self._converter_v2:
            return self._converter_v2.get_conversion_stats()
        else:
            return self.stats
    
    def get_format_detector(self):
        """
        獲取格式檢測器實例（新功能）
        
        Returns:
            FormatDetector: 格式檢測器實例
        """
        if self._converter_v2:
            return self._converter_v2.format_detector
        else:
            # 創建臨時實例
            temp_converter = AnnotationConverterV2(logger=self.logger)
            return temp_converter.format_detector
    
    def get_image_processor(self):
        """
        獲取圖像處理器實例（新功能）
        
        Returns:
            ImageProcessor: 圖像處理器實例
        """
        if self._converter_v2:
            return self._converter_v2.image_processor
        else:
            # 創建臨時實例
            temp_converter = AnnotationConverterV2(logger=self.logger)
            return temp_converter.image_processor


# 為了完全向後兼容，我們也可以提供原始的異常類型
class ConverterError(Exception):
    """標籤轉換器基本異常類型（向後兼容）"""
    pass

class FormatDetectionError(ConverterError):
    """格式檢測錯誤（向後兼容）"""
    pass

class ConversionError(ConverterError):
    """轉換過程中的錯誤（向後兼容）"""
    pass

class ImageProcessingError(ConverterError):
    """圖像處理錯誤（向後兼容）"""
    pass