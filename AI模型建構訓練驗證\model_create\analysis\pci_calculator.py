#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCI (Pavement Condition Index) 計算模組

基於簡易型柔性鋪面PCI計算系統
支援道路損壞的自動分級和PCI指標計算
"""

import math
import pandas as pd
import numpy as np
import logging
from typing import List, Tuple, Dict, Optional, Union
from pathlib import Path
import json


class PCICalculator:
    """
    簡易型柔性鋪面PCI計算系統
    
    支援基於實地測量數據的自動PCI計算
    包含損壞類型自動分級和統計分析功能
    """
    
    def __init__(self, sample_area: float = 300.0):
        """
        初始化PCI計算器
        
        參數:
            sample_area: 樣本單位面積，預設300平方公尺
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.sample_area = sample_area
        
        # DV值係數表 (根據規範文檔)
        self.dv_coefficients = {
            # 龜裂 (單位：m²)
            '1L': {'coeff1': 11.1, 'coeff2': 16.27, 'coeff3': 7.34, 'coeff4': -1.45},
            '1M': {'coeff1': 21.8, 'coeff2': 20.92, 'coeff3': 4.68, 'coeff4': -0.55},
            '1H': {'coeff1': 31.0, 'coeff2': 27.17, 'coeff3': 6.46, 'coeff4': -2.5},
            
            # 縱向及橫向裂縫 (單位：m)
            '2L': {'coeff1': -1.7, 'coeff2': 4.45, 'coeff3': 5.18, 'coeff4': 0},
            '2M': {'coeff1': 2.1, 'coeff2': 11.51, 'coeff3': 4.93, 'coeff4': 0},
            '2H': {'coeff1': 8.3, 'coeff2': 14.06, 'coeff3': 12.96, 'coeff4': 0},
            
            # 坑洞 (單位：m)
            '3L': {'coeff1': 21.2, 'coeff2': 27.15, 'coeff3': 6.41, 'coeff4': 0},
            '3M': {'coeff1': 31.4, 'coeff2': 40.77, 'coeff3': 14.14, 'coeff4': 0},
            '3H': {'coeff1': 52.3, 'coeff2': 43.87, 'coeff3': 10.22, 'coeff4': 0},
            
            # 補綻
            '4L': {'coeff1': 2.1, 'coeff2': 8.06, 'coeff3': 6.15, 'coeff4': 0},
            '4M': {'coeff1': 10.0, 'coeff2': 12.71, 'coeff3': 6.97, 'coeff4': 1.3},
            '4H': {'coeff1': 19.4, 'coeff2': 22.44, 'coeff3': 0, 'coeff4': 9},
            
            # 變形
            '5L': {'coeff1': 2.0, 'coeff2': 4.76, 'coeff3': 4.95, 'coeff4': 1},
            '5M': {'coeff1': 15.0, 'coeff2': 17.16, 'coeff3': 6.25, 'coeff4': 0},
            '5H': {'coeff1': 33.6, 'coeff2': 25.19, 'coeff3': 2.62, 'coeff4': 0},
        }
        
        # 鋪面狀況等級對照表
        self.rating_scale = [
            (85, 100, "令人滿意(Good)"),
            (70, 85, "良好(Satisfactory)"),
            (55, 70, "尚可(Fair)"),
            (40, 55, "差(Poor)"),
            (25, 40, "很差(Very Poor)"),
            (10, 25, "嚴重(Serious)"),
            (0, 10, "不及格(Failed)")
        ]
        
        # 損壞類型名稱對照
        self.damage_types = {
            1: '龜裂',
            2: '縱向及橫向裂縫', 
            3: '坑洞',
            4: '修補、修補變壞或管線回填',
            5: '變形'
        }
        
        # 嚴重程度對照
        self.severity_levels = {
            'L': '輕級',
            'M': '中級',
            'H': '重級'
        }
        
        self.logger.info(f"PCI計算器初始化完成，樣本面積: {sample_area} m²")

    def determine_severity_and_quantity(self, 
                                      length: float, 
                                      width: float, 
                                      area: float, 
                                      damage_type: int) -> Tuple[str, float]:
        """
        根據長寬面積和損壞類型自動判定嚴重程度和計算數量
        
        參數:
            length: 長度 (m)
            width: 寬度 (m)  
            area: 面積 (m²)
            damage_type: 損壞類型 (1-5)
            
        返回:
            (損壞類型代碼, 計算數量)
        """
        if damage_type == 1:  # 龜裂 (按面積分級，數量用面積)
            if area < 2:
                severity = 'L'
            elif area <= 5:
                severity = 'M'
            else:
                severity = 'H'
            quantity = area
            
        elif damage_type == 2:  # 縱向及橫向裂縫 (按裂縫寬度分級，數量用長度)
            crack_width = width  # 裂縫寬度決定嚴重程度
            if crack_width < 0.06:
                severity = 'L'
            elif crack_width <= 0.2:
                severity = 'M'
            else:
                severity = 'H'
            quantity = length  # 裂縫以長度計算數量
            
        elif damage_type == 3:  # 坑洞 (按長寬分級，數量用長度)
            max_dimension = max(length, width)
            if max_dimension < 0.1:
                severity = 'L'
            elif max_dimension <= 0.2:
                severity = 'M'
            else:
                severity = 'H'
            quantity = max_dimension  # 坑洞以最大長寬計算
            
        elif damage_type == 4:  # 補綻 (固定M級，數量用面積)
            severity = 'M'
            quantity = area
            
        elif damage_type == 5:  # 變形 (按面積分級，數量用面積)
            if area < 2:
                severity = 'L'
            elif area <= 5:
                severity = 'M'
            else:
                severity = 'H'
            quantity = area
            
        else:
            raise ValueError(f"無效的損壞類型: {damage_type}，應為1-5之間")
        
        damage_code = f"{damage_type}{severity}"
        return damage_code, quantity

    def calculate_density(self, quantity: float) -> float:
        """計算損壞密度"""
        return (quantity / self.sample_area) * 100

    def calculate_dv(self, damage_code: str, quantity: float) -> float:
        """
        計算折減值(DV)
        
        參數:
            damage_code: 損壞類型代碼 (如: '2H', '1M')
            quantity: 損壞數量
            
        返回:
            折減值
        """
        if damage_code not in self.dv_coefficients:
            raise ValueError(f"未知的損壞類型: {damage_code}")
        
        # 計算密度
        density = self.calculate_density(quantity)
        
        # 計算log(密度)
        if density <= 0:
            return 0
        
        x = math.log10(density)
        
        # 取得係數
        coeffs = self.dv_coefficients[damage_code]
        
        # 計算DV = Coeff1 + Coeff2*x + Coeff3*x² + Coeff4*x³
        dv = (coeffs['coeff1'] + 
              coeffs['coeff2'] * x + 
              coeffs['coeff3'] * x**2 + 
              coeffs['coeff4'] * x**3)
        
        return max(0, dv)  # DV不能為負值

    def calculate_m_value(self, hdv: float) -> int:
        """計算最大容許損壞折減值數量(m)"""
        m = 1 + (9/98) * (100 - hdv)
        return min(10, math.ceil(m))  # 最多只能選取10個折減值

    def calculate_cdv(self, total: float, q: int) -> float:
        """
        計算修正折減值(CDV)
        
        參數:
            total: 折減值總和
            q: 大於2.0的折減值個數
            
        返回:
            修正折減值
        """
        if q == 1:
            return total
        elif q == 2:
            return -3.6 + 0.91 * total - 0.0017 * total**2
        elif q == 3:
            return -6.4 + 0.82 * total - 0.0013 * total**2
        elif q == 4:
            return -13 + 0.86 * total - 0.0015 * total**2
        elif q == 5:
            return -12 + 0.76 * total - 0.0011 * total**2
        elif q == 6:
            return -14.7 + 0.75 * total - 0.0011 * total**2
        elif q == 7:
            return -18.5 + 0.86 * total - 0.0018 * total**2
        else:
            # 對於q>7的情況，使用q=7的公式
            return -18.5 + 0.86 * total - 0.0018 * total**2

    def get_rating(self, pci: float) -> str:
        """根據PCI值獲取鋪面狀況等級"""
        for min_val, max_val, rating in self.rating_scale:
            if min_val <= pci <= max_val:
                return rating
        return "未知等級"

    def calculate_pci_from_measurements(self, damage_measurements: List[Dict]) -> Dict:
        """
        根據實地測量數據計算PCI指標
        
        參數:
            damage_measurements: 損壞測量數據列表，每個元素包含:
                {
                    'length': 長度(m),
                    'width': 寬度(m), 
                    'area': 面積(m²),
                    'damage_type': 損壞類型(1-5)
                }
                
        返回:
            計算結果字典
        """
        # Step 1: 處理測量數據，自動分級並合併同類型損壞
        damage_summary = {}
        measurement_details = []
        
        for measurement in damage_measurements:
            length = measurement.get('length', 0)
            width = measurement.get('width', 0)
            area = measurement.get('area', 0)
            damage_type = measurement['damage_type']
            
            # 如果沒有提供面積但有長寬，自動計算面積
            if area == 0 and length > 0 and width > 0:
                area = length * width
            
            # 自動判定嚴重程度和數量
            damage_code, quantity = self.determine_severity_and_quantity(
                length, width, area, damage_type
            )
            
            # 記錄測量詳情
            measurement_details.append({
                'length': length,
                'width': width,
                'area': area,
                'damage_type': damage_type,
                'damage_name': self.damage_types[damage_type],
                'damage_code': damage_code,
                'severity': self.severity_levels[damage_code[-1]],
                'quantity': quantity
            })
            
            # 合併同類型損壞
            if damage_code in damage_summary:
                damage_summary[damage_code] += quantity
            else:
                damage_summary[damage_code] = quantity
        
        # Step 2: 計算各項折減值
        dv_list = []
        damage_details = []
        
        for damage_code, total_quantity in damage_summary.items():
            dv = self.calculate_dv(damage_code, total_quantity)
            density = self.calculate_density(total_quantity)
            
            dv_list.append(dv)
            damage_details.append({
                'damage_code': damage_code,
                'damage_name': f"{self.damage_types[int(damage_code[0])]}-{self.severity_levels[damage_code[1]]}",
                'total_quantity': total_quantity,
                'density': density,
                'dv': dv
            })
        
        # 按DV值從大到小排序
        dv_list.sort(reverse=True)
        
        if not dv_list:
            return {
                'pci': 100,
                'rating': self.get_rating(100),
                'max_cdv': 0,
                'measurement_details': measurement_details,
                'damage_summary': damage_details,
                'cdv_calculation': []
            }
        
        # Step 3: 計算m值
        hdv = max(dv_list)
        m = self.calculate_m_value(hdv)
        
        # Step 4: 取前m個或全部DV值
        selected_dv = dv_list[:min(m, len(dv_list))]
        
        # Step 5: 計算CDV
        cdv_calculations = []
        max_cdv = 0
        
        # 初始計算
        current_dv = selected_dv.copy()
        
        while True:
            # 計算大於2.0的DV個數
            q = sum(1 for dv in current_dv if dv > 2.0)
            if q == 0:
                break
                
            # 計算總和
            total = sum(current_dv)
            
            # 計算CDV
            cdv = self.calculate_cdv(total, q)
            max_cdv = max(max_cdv, cdv)
            
            cdv_calculations.append({
                'dv_values': current_dv.copy(),
                'total': total,
                'q': q,
                'cdv': cdv
            })
            
            if q == 1:
                break
                
            # 將最後一個大於2.0的值替換為2.0
            for i in range(len(current_dv) - 1, -1, -1):
                if current_dv[i] > 2.0:
                    current_dv[i] = 2.0
                    break
        
        # Step 6: 計算最終PCI
        pci = 100 - max_cdv
        rating = self.get_rating(pci)
        
        return {
            'pci': round(pci, 1),
            'rating': rating,
            'max_cdv': round(max_cdv, 1),
            'hdv': round(hdv, 1),
            'm_value': m,
            'measurement_details': measurement_details,
            'damage_summary': damage_details,
            'cdv_calculation': cdv_calculations
        }

    def create_measurement_table(self, damage_measurements: List[Dict]) -> pd.DataFrame:
        """建立測量數據表"""
        table_data = []
        
        for i, measurement in enumerate(damage_measurements, 1):
            length = measurement.get('length', 0)
            width = measurement.get('width', 0)
            area = measurement.get('area', 0)
            damage_type = measurement['damage_type']
            
            # 自動計算面積（如果未提供）
            if area == 0 and length > 0 and width > 0:
                area = length * width
            
            # 判定損壞等級
            damage_code, quantity = self.determine_severity_and_quantity(
                length, width, area, damage_type
            )
            
            table_data.append({
                '序號': i,
                '損壞類型': self.damage_types[damage_type],
                '長度(m)': length if length > 0 else '-',
                '寬度(m)': width if width > 0 else '-',
                '面積(m²)': round(area, 2) if area > 0 else '-',
                '嚴重程度': self.severity_levels[damage_code[-1]],
                '損壞代碼': damage_code,
                '計算數量': round(quantity, 2)
            })
        
        return pd.DataFrame(table_data)

    def batch_calculate_pci(self, 
                           csv_file: str,
                           length_col: str = '長度',
                           width_col: str = '寬度', 
                           area_col: str = '面積',
                           damage_type_col: str = '損壞類型') -> Dict:
        """
        批次計算CSV檔案中的PCI指標
        
        參數:
            csv_file: CSV檔案路徑
            length_col: 長度欄位名稱
            width_col: 寬度欄位名稱
            area_col: 面積欄位名稱
            damage_type_col: 損壞類型欄位名稱
            
        返回:
            計算結果字典
        """
        try:
            # 讀取CSV檔案
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            
            # 轉換為測量數據格式
            damage_measurements = []
            for _, row in df.iterrows():
                measurement = {
                    'length': float(row.get(length_col, 0)),
                    'width': float(row.get(width_col, 0)),
                    'area': float(row.get(area_col, 0)),
                    'damage_type': int(row.get(damage_type_col, 1))
                }
                damage_measurements.append(measurement)
            
            # 計算PCI
            result = self.calculate_pci_from_measurements(damage_measurements)
            self.logger.info(f"批次處理完成，PCI值: {result['pci']}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"批次計算PCI失敗: {e}")
            raise

    def save_calculation_results(self, results: Dict, output_path: str):
        """保存計算結果到檔案"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"計算結果已保存至: {output_path}")
        except Exception as e:
            self.logger.error(f"保存計算結果失敗: {e}")
            raise

    def get_calculation_summary(self) -> Dict:
        """獲取計算器摘要信息"""
        return {
            'sample_area': self.sample_area,
            'supported_damage_types': list(self.damage_types.keys()),
            'damage_type_names': self.damage_types,
            'severity_levels': self.severity_levels,
            'rating_scale': self.rating_scale
        }


# 工廠函數
def create_pci_calculator(sample_area: float = 300.0) -> PCICalculator:
    """
    創建PCI計算器的工廠函數
    
    參數:
        sample_area: 樣本面積
        
    返回:
        PCICalculator實例
    """
    return PCICalculator(sample_area=sample_area)


# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建PCI計算器
    calculator = create_pci_calculator(sample_area=300.0)
    
    # 範例測量數據
    example_measurements = [
        {
            'length': 25.0,      # 25m長的裂縫
            'width': 0.15,       # 0.15m寬 -> M級縱橫向裂縫
            'area': 0,           # 裂縫不用面積
            'damage_type': 2     # 縱橫向裂縫
        },
        {
            'length': 3.0,       # 龜裂區域
            'width': 2.5,        
            'area': 7.5,         # 7.5m² -> H級龜裂
            'damage_type': 1     # 龜裂
        },
        {
            'length': 0.8,       # 坑洞
            'width': 0.15,       # 0.15m -> M級坑洞
            'area': 0,
            'damage_type': 3     # 坑洞
        }
    ]
    
    # 計算PCI
    result = calculator.calculate_pci_from_measurements(example_measurements)
    
    print(f"PCI計算結果:")
    print(f"PCI值: {result['pci']}")
    print(f"鋪面狀況等級: {result['rating']}")
    print(f"最大修正折減值: {result['max_cdv']}")