"""
Transformer編碼器族：統一實現所有Transformer架構
整合CSP_IFormer、IFormer、EffSegformer等系列
"""

import math
import logging
from functools import partial
from collections import OrderedDict
from copy import deepcopy
from typing import List, Dict, Any, Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F

from timm.models.layers import PatchEmbed, Mlp, DropPath, trunc_normal_, lecun_normal_
from timm.models.layers.helpers import to_2tuple

from ..core.base_encoder import TransformerEncoder, register_encoder


def _init_vit_weights(module: nn.Module, name: str = '', head_bias: float = 0.):
    """ViT權重初始化"""
    if isinstance(module, nn.Linear):
        if name.startswith('head'):
            nn.init.zeros_(module.weight)
            nn.init.constant_(module.bias, head_bias)
        elif name.startswith('pre_logits'):
            lecun_normal_(module.weight)
            nn.init.zeros_(module.bias)
        else:
            trunc_normal_(module.weight, std=.02)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    elif isinstance(module, (nn.LayerNorm, nn.GroupNorm, nn.BatchNorm2d)):
        nn.init.zeros_(module.bias)
        nn.init.ones_(module.weight)
    elif isinstance(module, nn.Conv2d):
        trunc_normal_(module.weight, std=.02)
        if module.bias is not None:
            nn.init.constant_(module.bias, 0)


def channel_shuffle(x, groups):
    """通道洗牌操作"""
    batchsize, num_channels, height, width = x.data.size()
    channels_per_group = num_channels // groups
    
    # 重塑並轉置
    x = x.view(batchsize, groups, channels_per_group, height, width)
    x = torch.transpose(x, 1, 2).contiguous()
    
    # 展平
    x = x.view(batchsize, -1, height, width)
    return x


class CustomPatchEmbed(nn.Module):
    """自定義Patch嵌入模組"""
    
    def __init__(self, img_size=224, kernel_size=16, stride=16, padding=0, 
                 in_chans=3, embed_dim=768, embed_type='standard'):
        super().__init__()
        kernel_size = to_2tuple(kernel_size)
        stride = to_2tuple(stride)
        padding = to_2tuple(padding)
        
        self.embed_type = embed_type
        
        if embed_type == 'iformer_first':
            # IFormer首層嵌入
            self.proj1 = nn.Conv2d(in_chans, embed_dim//2, kernel_size=3, stride=2, padding=1)
            self.norm1 = nn.BatchNorm2d(embed_dim // 2)
            self.gelu1 = nn.GELU()
            self.proj2 = nn.Conv2d(embed_dim//2, embed_dim, kernel_size=3, stride=2, padding=1)
            self.norm2 = nn.BatchNorm2d(embed_dim)
        else:
            # 標準嵌入
            self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=kernel_size, 
                                stride=stride, padding=padding)
            self.norm = nn.BatchNorm2d(embed_dim)
    
    def forward(self, x):
        if self.embed_type == 'iformer_first':
            x = self.proj1(x)
            x = self.norm1(x)
            x = self.gelu1(x)
            x = self.proj2(x)
            x = self.norm2(x)
        else:
            x = self.proj(x)
            x = self.norm(x)
        
        x = x.permute(0, 2, 3, 1)
        return x


class HighMixer(nn.Module):
    """高頻特徵提取器"""
    
    def __init__(self, embed_dim):
        super().__init__()
        self.linear = nn.Linear(embed_dim, embed_dim)
        self.pool = nn.AdaptiveMaxPool2d((1, 1))
    
    def forward(self, x):
        B, H, W, C = x.shape
        x_reshaped = x.view(B, H*W, C)
        x_linear = self.linear(x_reshaped)
        x_reshaped = x_linear.view(B, H, W, C)
        
        # 最大池化
        x_pooled = x_reshaped.permute(0, 3, 1, 2)  # B, C, H, W
        x_pooled = self.pool(x_pooled)  # B, C, 1, 1
        x_pooled = x_pooled.expand_as(x_reshaped.permute(0, 3, 1, 2))
        x_pooled = x_pooled.permute(0, 2, 3, 1)  # B, H, W, C
        
        return x_pooled


class LowMixer(nn.Module):
    """低頻特徵混合器（注意力機制）"""
    
    def __init__(self, dim, num_heads=8, attention_head=None, head_dim=None, 
                 qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0., 
                 enable_dropkey=False, dropkey_rate=0.1, low_dim_ratio=0.5):
        super().__init__()
        self.num_heads = num_heads
        self.enable_dropkey = enable_dropkey
        self.dropkey_rate = dropkey_rate
        
        # 計算注意力維度
        if attention_head is not None:
            self.attention_head = attention_head
            if head_dim is not None:
                self.head_dim = head_dim
                self.low_dim = int(attention_head * head_dim * low_dim_ratio)
            else:
                self.head_dim = dim // attention_head
                self.low_dim = int(attention_head * self.head_dim * low_dim_ratio)
        else:
            self.head_dim = dim // num_heads
            self.attention_head = num_heads
            self.low_dim = int(dim * low_dim_ratio)
        
        self.scale = qk_scale or self.head_dim ** -0.5
        
        # QKV投影
        self.qkv = nn.Linear(dim, self.low_dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(self.low_dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
    
    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.attention_head, 
                                  self.low_dim // self.attention_head).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 計算注意力
        attn = (q @ k.transpose(-2, -1)) * self.scale
        
        # DropKey技術
        if self.enable_dropkey and self.training:
            m_r = torch.ones_like(attn) * self.dropkey_rate
            attn = attn + torch.bernoulli(m_r) * -1e12
        
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, N, self.low_dim)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


class CSP_Inception_token_mixer(nn.Module):
    """CSP Inception Token混合器"""
    
    def __init__(self, dim, img_size=224, num_heads=8, attention_head=None, 
                 head_dim=None, qkv_bias=False, qk_scale=None, attn_drop=0., 
                 proj_drop=0., enable_dropkey=False, dropkey_rate=0.1,
                 enable_channel_shuffle=False, channel_shuffle_groups=8,
                 low_dim_ratio=0.5):
        super().__init__()
        
        self.img_size = img_size
        self.enable_channel_shuffle = enable_channel_shuffle
        self.channel_shuffle_groups = channel_shuffle_groups
        
        # 高頻和低頻混合器
        self.HighMixer = HighMixer(dim)
        self.LowMixer = LowMixer(
            dim, num_heads, attention_head, head_dim, qkv_bias, qk_scale,
            attn_drop, proj_drop, enable_dropkey, dropkey_rate, low_dim_ratio
        )
        
        # 歸一化層
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        
        # MLP
        self.mlp = Mlp(in_features=dim, hidden_features=int(dim * 4), 
                      act_layer=nn.GELU, drop=proj_drop)
    
    def forward(self, x):
        B, N, C = x.shape
        H = W = int(math.sqrt(N))
        
        # 重塑為圖像格式
        x_img = x.view(B, H, W, C)
        
        # 高頻特徵
        high_freq = self.HighMixer(x_img)
        high_freq = high_freq.view(B, N, C)
        
        # 低頻特徵（注意力）
        x_norm = self.norm1(x)
        low_freq = self.LowMixer(x_norm)
        
        # 特徵融合
        x = x + high_freq + low_freq
        
        # 通道洗牌
        if self.enable_channel_shuffle:
            x_reshaped = x.view(B, H, W, C).permute(0, 3, 1, 2)
            x_reshaped = channel_shuffle(x_reshaped, self.channel_shuffle_groups)
            x = x_reshaped.permute(0, 2, 3, 1).view(B, N, C)
        
        # MLP
        x = x + self.mlp(self.norm2(x))
        
        return x


class IFormerBlock(nn.Module):
    """IFormer基礎塊"""
    
    def __init__(self, dim, img_size=224, num_heads=8, attention_head=None,
                 head_dim=None, mlp_ratio=4., qkv_bias=False, qk_scale=None, 
                 drop=0., attn_drop=0., drop_path=0., act_layer=nn.GELU, 
                 norm_layer=nn.LayerNorm, enable_dropkey=False, dropkey_rate=0.1,
                 enable_channel_shuffle=False, channel_shuffle_groups=8,
                 low_dim_ratio=0.5):
        super().__init__()
        
        self.token_mixer = CSP_Inception_token_mixer(
            dim, img_size, num_heads, attention_head, head_dim, qkv_bias, 
            qk_scale, attn_drop, drop, enable_dropkey, dropkey_rate,
            enable_channel_shuffle, channel_shuffle_groups, low_dim_ratio
        )
        
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
    
    def forward(self, x):
        x = self.drop_path(self.token_mixer(x))
        return x


@register_encoder("csp_iformer")
class CSPIFormerEncoder(TransformerEncoder):
    """統一的CSP IFormer編碼器實現"""
    
    def __init__(self, 
                 input_channels: int = 3,
                 output_channels: List[int] = None,
                 variant: str = "default",
                 mode: str = "classification",  # "classification" or "segmentation"
                 img_size: int = 256,
                 enable_channel_shuffle: bool = False,
                 enable_dropkey: bool = False,
                 enable_efficient_ffn: bool = False,
                 channel_shuffle_groups: int = 8,
                 channel_shuffle_times: int = 1,
                 dropkey_rate: float = 0.1,
                 part_ratio: float = 0.5,
                 skip_stages: List[int] = None,
                 depths: List[int] = None,
                 embed_dims: List[int] = None,
                 num_heads: List[int] = None,
                 attention_heads: List[int] = None,
                 mlp_ratios: List[float] = None,
                 qkv_bias: bool = True,
                 qk_scale: float = None,
                 drop_rate: float = 0.,
                 attn_drop_rate: float = 0.,
                 drop_path_rate: float = 0.1,
                 embed_layer: nn.Module = CustomPatchEmbed,
                 norm_layer: nn.Module = None,
                 act_layer: nn.Module = None,
                 **kwargs):
        
        # 設置默認參數
        self.mode = mode
        self.img_size = img_size
        self.enable_channel_shuffle = enable_channel_shuffle
        self.enable_dropkey = enable_dropkey
        self.channel_shuffle_groups = channel_shuffle_groups
        self.channel_shuffle_times = channel_shuffle_times
        self.dropkey_rate = dropkey_rate
        self.skip_stages = skip_stages or []
        
        # 根據變體設置參數
        self._setup_variant_params(variant, depths, embed_dims, num_heads, 
                                 attention_heads, mlp_ratios)
        
        # 設置輸出通道
        if output_channels is None:
            output_channels = [dim for dim in self.embed_dims if dim is not None]
        
        super().__init__(input_channels=input_channels, 
                        output_channels=output_channels, **kwargs)
        
        # 規範化和激活層
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)
        act_layer = act_layer or nn.GELU
        
        self.num_classes = kwargs.get('num_classes', 1000)
        self.num_features = self.embed_dims[-1]
        self.drop_rate = drop_rate
        
        # Patch嵌入層
        self.patch_embed1 = embed_layer(
            img_size=img_size, kernel_size=3, stride=2, padding=1,
            in_chans=input_channels, embed_dim=self.embed_dims[0],
            embed_type='iformer_first'
        )
        
        # 構建階段
        self._build_stages(norm_layer, act_layer, drop_path_rate)
        
        # 分類頭（如果需要）
        if mode == "classification":
            self.norm = norm_layer(self.num_features)
            self.head = nn.Linear(self.num_features, self.num_classes) if self.num_classes > 0 else nn.Identity()
        
        # 應用權重初始化
        self.apply(self._init_weights)
    
    def _setup_variant_params(self, variant, depths, embed_dims, num_heads, 
                            attention_heads, mlp_ratios):
        """根據變體設置參數"""
        
        # 預定義的變體配置
        variant_configs = {
            "default": {
                "depths": [3, 3, 9, 3],
                "embed_dims": [96, 192, 320, 384],
                "num_heads": [3, 6, 10, 12],
                "attention_heads": [3, 6, 10, 12],
                "mlp_ratios": [4, 4, 4, 4]
            },
            "simplified_1": {  # 對應CSP_IFormer_1（跳過stage1）
                "depths": [0, 3, 9, 3],
                "embed_dims": [None, 192, 320, 384],
                "num_heads": [None, 6, 10, 12],
                "attention_heads": [None, 6, 10, 12],
                "mlp_ratios": [None, 4, 4, 4],
                "skip_stages": [0]
            },
            "simplified_2": {  # 對應CSP_IFormer_2（只有stage3+4）
                "depths": [0, 0, 9, 3],
                "embed_dims": [None, None, 320, 384],
                "num_heads": [None, None, 10, 12],
                "attention_heads": [None, None, 10, 12],
                "mlp_ratios": [None, None, 4, 4],
                "skip_stages": [0, 1]
            },
            "simplified_3": {  # 對應CSP_IFormer_3（只有stage4）
                "depths": [0, 0, 0, 3],
                "embed_dims": [None, None, None, 384],
                "num_heads": [None, None, None, 12],
                "attention_heads": [None, None, None, 12],
                "mlp_ratios": [None, None, None, 4],
                "skip_stages": [0, 1, 2]
            },
            "high_res": {  # 對應768尺寸版本
                "depths": [3, 3, 9, 3],
                "embed_dims": [96, 192, 320, 384],
                "num_heads": [3, 6, 10, 12],
                "attention_heads": [3, 6, 10, 12], 
                "mlp_ratios": [4, 4, 4, 4],
                "img_size": 768
            }
        }
        
        # 獲取變體配置
        config = variant_configs.get(variant, variant_configs["default"])
        
        # 設置參數
        self.depths = depths or config["depths"]
        self.embed_dims = embed_dims or config["embed_dims"]
        self.num_heads = num_heads or config["num_heads"]
        self.attention_heads = attention_heads or config["attention_heads"]
        self.mlp_ratios = mlp_ratios or config["mlp_ratios"]
        
        # 更新跳過階段
        if "skip_stages" in config:
            self.skip_stages.extend(config["skip_stages"])
        
        # 更新圖像尺寸
        if "img_size" in config:
            self.img_size = config["img_size"]
    
    def _build_stages(self, norm_layer, act_layer, drop_path_rate):
        """構建Transformer階段"""
        self.stages = nn.ModuleList()
        self.norms = nn.ModuleList()
        
        # 計算drop path率
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(self.depths))]
        cur = 0
        
        for i in range(4):  # 4個階段
            if i in self.skip_stages or self.depths[i] == 0:
                # 跳過的階段添加空模組
                self.stages.append(nn.Identity())
                self.norms.append(nn.Identity())
                continue
            
            # Patch merging（除了第一階段）
            if i > 0:
                patch_merge = PatchMerging(
                    input_resolution=(self.img_size // (2**(i+1)), self.img_size // (2**(i+1))),
                    dim=self.embed_dims[i-1],
                    norm_layer=norm_layer
                )
            else:
                patch_merge = nn.Identity()
            
            # 構建塊
            blocks = nn.ModuleList()
            for j in range(self.depths[i]):
                blocks.append(IFormerBlock(
                    dim=self.embed_dims[i],
                    img_size=self.img_size // (2**(i+2)),
                    num_heads=self.num_heads[i],
                    attention_head=self.attention_heads[i],
                    mlp_ratio=self.mlp_ratios[i],
                    qkv_bias=True,
                    drop_path=dpr[cur + j],
                    enable_dropkey=self.enable_dropkey,
                    dropkey_rate=self.dropkey_rate,
                    enable_channel_shuffle=self.enable_channel_shuffle,
                    channel_shuffle_groups=self.channel_shuffle_groups
                ))
            
            stage = nn.Sequential(patch_merge, *blocks)
            self.stages.append(stage)
            self.norms.append(norm_layer(self.embed_dims[i]))
            
            cur += self.depths[i]
    
    def _build_encoder(self):
        """構建編碼器架構（由__init__處理）"""
        pass
    
    def _init_weights(self, m):
        """初始化權重"""
        _init_vit_weights(m)
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """前向傳播"""
        # Patch嵌入
        x = self.patch_embed1(x)
        B, H, W, C = x.shape
        x = x.view(B, H * W, C)
        
        outputs = []
        
        # 通過各階段
        for i, (stage, norm) in enumerate(zip(self.stages, self.norms)):
            if i in self.skip_stages:
                continue
                
            x = stage(x)
            
            # 通道洗牌（在某些變體中多次應用）
            if self.enable_channel_shuffle and self.channel_shuffle_times > 1:
                for _ in range(self.channel_shuffle_times - 1):
                    B, N, C = x.shape
                    H = W = int(math.sqrt(N))
                    x_reshaped = x.view(B, H, W, C).permute(0, 3, 1, 2)
                    x_reshaped = channel_shuffle(x_reshaped, self.channel_shuffle_groups)
                    x = x_reshaped.permute(0, 2, 3, 1).view(B, N, C)
            
            x = norm(x)
            
            if self.mode == "segmentation":
                # 為分割任務重塑輸出
                B, N, C = x.shape
                H = W = int(math.sqrt(N))
                output = x.view(B, H, W, C).permute(0, 3, 1, 2)
                outputs.append(output)
        
        if self.mode == "classification":
            # 分類模式：全局平均池化
            return [x.mean(1)]  # [B, C]
        else:
            # 分割模式：返回多尺度特徵
            return outputs


class PatchMerging(nn.Module):
    """Patch合併層（下採樣）"""
    
    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)
    
    def forward(self, x):
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "輸入特徵維度不匹配"
        assert H % 2 == 0 and W % 2 == 0, f"x尺寸 ({H}*{W}) 不是2的倍數"
        
        x = x.view(B, H, W, C)
        
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C
        
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C
        
        x = self.norm(x)
        x = self.reduction(x)
        
        return x


@register_encoder("iformer")
class IFormerEncoder(CSPIFormerEncoder):
    """基礎IFormer編碼器（CSP_IFormer的簡化版本）"""
    
    def __init__(self, **kwargs):
        # 禁用CSP特有功能
        kwargs.setdefault('enable_channel_shuffle', False)
        kwargs.setdefault('enable_dropkey', False)
        super().__init__(**kwargs)


@register_encoder("effsegformer")
class EffSegformerEncoder(TransformerEncoder):
    """高效分割Transformer編碼器"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # TODO: 實現EffSegformer特定架構
        pass
    
    def _build_encoder(self):
        # TODO: 實現EffSegformer構建邏輯
        pass
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        # TODO: 實現EffSegformer前向傳播
        return [x]


@register_encoder("transception")
class TransceptionEncoder(TransformerEncoder):
    """Transception編碼器"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # TODO: 實現Transception特定架構
        pass
    
    def _build_encoder(self):
        # TODO: 實現Transception構建邏輯
        pass
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        # TODO: 實現Transception前向傳播
        return [x]