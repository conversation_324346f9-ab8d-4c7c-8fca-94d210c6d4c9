"""
配置管理模組
提供統一的配置管理功能，支援YAML和JSON格式
"""

import os
import yaml
import json
import logging
from typing import Any, Dict, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict, field
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """訓練配置"""
    # 基本參數
    epochs: int = 100
    batch_size: int = 16
    learning_rate: float = 1e-3
    weight_decay: float = 1e-4
    
    # 模型參數
    model_name: str = "segmentation_model"
    num_classes: int = 2
    input_size: int = 224
    
    # 訓練策略
    mixed_precision: bool = True
    gradient_accumulation_steps: int = 1
    max_grad_norm: float = 1.0
    early_stopping_patience: int = 10
    early_stopping_min_delta: float = 1e-4
    
    # 檢查點
    checkpoint_frequency: int = 5
    save_best_only: bool = True
    max_checkpoints: int = 5
    
    # 日誌和監控
    log_frequency: int = 10
    wandb_project: Optional[str] = None
    tensorboard_log_dir: Optional[str] = None
    
    # 設備配置
    device: str = "auto"  # "auto", "cuda", "cpu"
    num_workers: int = 4
    pin_memory: bool = True

@dataclass
class DataConfig:
    """數據配置"""
    # 數據路徑
    data_dir: str = "./data"
    train_split: str = "train"
    val_split: str = "val"
    test_split: str = "test"
    
    # 數據格式
    dataset_type: str = "yolo"  # "yolo", "labelme", "coco"
    image_extensions: list = field(default_factory=lambda: ['.jpg', '.jpeg', '.png', '.bmp'])
    label_extensions: list = field(default_factory=lambda: ['.txt', '.json'])
    
    # 數據處理
    input_size: int = 224
    normalize: bool = True
    normalize_mean: list = field(default_factory=lambda: [0.485, 0.456, 0.406])
    normalize_std: list = field(default_factory=lambda: [0.229, 0.224, 0.225])
    
    # 數據增強
    horizontal_flip: float = 0.5
    vertical_flip: float = 0.0
    rotation: float = 0.1
    brightness: float = 0.2
    contrast: float = 0.2
    saturation: float = 0.1
    hue: float = 0.1
    
    # 類別配置
    class_names: Dict[int, str] = field(default_factory=dict)
    class_weights: Optional[list] = None
    ignore_index: int = 255

@dataclass  
class LossConfig:
    """損失函數配置"""
    # 主要損失
    primary_loss: str = "cross_entropy"
    primary_weight: float = 1.0
    
    # 輔助損失
    auxiliary_losses: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 損失參數
    focal_alpha: float = 0.25
    focal_gamma: float = 2.0
    dice_smooth: float = 1.0
    boundary_weight: float = 0.2
    
    # 類別權重
    class_weights: Optional[list] = None
    ignore_index: int = 255

@dataclass
class OptimizerConfig:
    """優化器配置"""
    # 優化器類型
    optimizer_type: str = "adam"  # "adam", "sgd", "adamw"
    learning_rate: float = 1e-3
    weight_decay: float = 1e-4
    
    # SGD參數
    momentum: float = 0.9
    nesterov: bool = True
    
    # Adam參數
    beta1: float = 0.9
    beta2: float = 0.999
    eps: float = 1e-8
    
    # 學習率調度
    scheduler_type: str = "cosine"  # "cosine", "step", "plateau", "none"
    scheduler_params: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AugmentationConfig:
    """數據增強配置"""
    # 基本增強
    resize: int = 224
    horizontal_flip: float = 0.5
    vertical_flip: float = 0.0
    
    # 顏色增強
    brightness: float = 0.2
    contrast: float = 0.2
    saturation: float = 0.1
    hue: float = 0.1
    
    # 幾何增強
    rotation: float = 10.0
    scale: tuple = (0.8, 1.2)
    shear: float = 0.1
    
    # 噪聲增強
    gaussian_noise: float = 0.0
    dropout: float = 0.0
    
    # 高級增強
    mixup: float = 0.0
    cutmix: float = 0.0
    mosaic: float = 0.0

@dataclass
class EvaluationConfig:
    """評估配置"""
    # 評估指標
    metrics: list = field(default_factory=lambda: ["accuracy", "iou", "dice", "f1"])
    
    # 評估參數
    num_classes: int = 2
    average: str = "macro"  # "macro", "micro", "weighted"
    
    # 可視化
    save_predictions: bool = True
    save_confusion_matrix: bool = True
    save_class_metrics: bool = True
    
    # 測試時增強
    tta: bool = False
    tta_transforms: list = field(default_factory=list)

@dataclass
class Config:
    """主配置類"""
    # 基本信息
    name: str = "default_config"
    version: str = "1.0.0"
    description: str = ""
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # 子配置
    training: TrainingConfig = field(default_factory=TrainingConfig)
    data: DataConfig = field(default_factory=DataConfig)
    loss: LossConfig = field(default_factory=LossConfig)
    optimizer: OptimizerConfig = field(default_factory=OptimizerConfig)
    augmentation: AugmentationConfig = field(default_factory=AugmentationConfig)
    evaluation: EvaluationConfig = field(default_factory=EvaluationConfig)
    
    # 自定義參數
    custom: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "./configs"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目錄
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 當前加載的配置
        self.current_config: Optional[Config] = None
        
    def create_default_config(self, name: str = "default") -> Config:
        """
        創建默認配置
        
        Args:
            name: 配置名稱
            
        Returns:
            默認配置對象
        """
        config = Config(
            name=name,
            description=f"Default configuration created at {datetime.now()}"
        )
        
        return config
    
    def save_config(self, config: Config, filename: Optional[str] = None) -> str:
        """
        保存配置到文件
        
        Args:
            config: 配置對象
            filename: 文件名（可選）
            
        Returns:
            保存的文件路徑
        """
        if filename is None:
            filename = f"{config.name}.yaml"
        
        if not filename.endswith(('.yaml', '.yml', '.json')):
            filename += '.yaml'
        
        file_path = self.config_dir / filename
        
        try:
            # 轉換為字典
            config_dict = asdict(config)
            
            if filename.endswith('.json'):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            
            logger.info(f"配置已保存到: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
            raise
    
    def load_config(self, filename: str) -> Config:
        """
        從文件加載配置
        
        Args:
            filename: 配置文件名
            
        Returns:
            配置對象
        """
        file_path = self.config_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        try:
            if filename.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_dict = yaml.safe_load(f)
            
            # 轉換為配置對象
            config = self._dict_to_config(config_dict)
            self.current_config = config
            
            logger.info(f"配置已加載: {file_path}")
            return config
            
        except Exception as e:
            logger.error(f"加載配置失敗: {e}")
            raise
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> Config:
        """將字典轉換為配置對象"""
        # 提取子配置
        training_dict = config_dict.get('training', {})
        data_dict = config_dict.get('data', {})
        loss_dict = config_dict.get('loss', {})
        optimizer_dict = config_dict.get('optimizer', {})
        augmentation_dict = config_dict.get('augmentation', {})
        evaluation_dict = config_dict.get('evaluation', {})
        
        # 創建配置對象
        config = Config(
            name=config_dict.get('name', 'loaded_config'),
            version=config_dict.get('version', '1.0.0'),
            description=config_dict.get('description', ''),
            created_at=config_dict.get('created_at', datetime.now().isoformat()),
            training=TrainingConfig(**training_dict),
            data=DataConfig(**data_dict),
            loss=LossConfig(**loss_dict),
            optimizer=OptimizerConfig(**optimizer_dict),
            augmentation=AugmentationConfig(**augmentation_dict),
            evaluation=EvaluationConfig(**evaluation_dict),
            custom=config_dict.get('custom', {})
        )
        
        return config
    
    def update_config(self, 
                     config: Config, 
                     updates: Dict[str, Any],
                     save: bool = True) -> Config:
        """
        更新配置
        
        Args:
            config: 原配置對象
            updates: 更新字典
            save: 是否自動保存
            
        Returns:
            更新後的配置對象
        """
        # 深拷貝配置
        config_dict = asdict(config)
        
        # 遞歸更新
        self._recursive_update(config_dict, updates)
        
        # 轉換回配置對象
        updated_config = self._dict_to_config(config_dict)
        
        if save:
            self.save_config(updated_config)
        
        return updated_config
    
    def _recursive_update(self, base_dict: Dict, update_dict: Dict):
        """遞歸更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._recursive_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def list_configs(self) -> list:
        """列出所有可用的配置文件"""
        config_files = []
        
        for file_path in self.config_dir.iterdir():
            if file_path.suffix in ['.yaml', '.yml', '.json']:
                config_files.append(file_path.name)
        
        return sorted(config_files)
    
    def get_current_config(self) -> Optional[Config]:
        """獲取當前配置"""
        return self.current_config
    
    def merge_configs(self, *configs: Config) -> Config:
        """
        合併多個配置
        
        Args:
            *configs: 要合併的配置對象
            
        Returns:
            合併後的配置對象
        """
        if not configs:
            return self.create_default_config()
        
        base_config = configs[0]
        base_dict = asdict(base_config)
        
        for config in configs[1:]:
            config_dict = asdict(config)
            self._recursive_update(base_dict, config_dict)
        
        return self._dict_to_config(base_dict)
    
    def validate_config(self, config: Config) -> bool:
        """
        驗證配置有效性
        
        Args:
            config: 要驗證的配置
            
        Returns:
            是否有效
        """
        try:
            # 基本驗證
            assert config.training.epochs > 0, "epochs必須大於0"
            assert config.training.batch_size > 0, "batch_size必須大於0"
            assert config.training.learning_rate > 0, "learning_rate必須大於0"
            assert config.data.input_size > 0, "input_size必須大於0"
            
            # 數據路徑驗證
            if not os.path.exists(config.data.data_dir):
                logger.warning(f"數據目錄不存在: {config.data.data_dir}")
            
            # 類別配置驗證
            if config.training.num_classes != len(config.data.class_names):
                logger.warning("num_classes與class_names數量不匹配")
            
            logger.info("配置驗證通過")
            return True
            
        except Exception as e:
            logger.error(f"配置驗證失敗: {e}")
            return False


# 預設配置模板
class ConfigTemplates:
    """配置模板類"""
    
    @staticmethod
    def segmentation_config() -> Config:
        """分割任務配置模板"""
        config = Config(
            name="segmentation_template",
            description="Segmentation task configuration template"
        )
        
        # 訓練配置
        config.training.epochs = 200
        config.training.batch_size = 8
        config.training.learning_rate = 1e-3
        config.training.num_classes = 3  # 背景 + 2個前景類別
        
        # 數據配置
        config.data.dataset_type = "labelme"
        config.data.input_size = 512
        config.data.class_names = {0: "background", 1: "crack", 2: "pothole"}
        
        # 損失配置
        config.loss.primary_loss = "combined"
        config.loss.auxiliary_losses = {
            "dice": {"type": "dice", "weight": 0.6},
            "focal": {"type": "focal", "weight": 0.4}
        }
        
        return config
    
    @staticmethod
    def classification_config() -> Config:
        """分類任務配置模板"""
        config = Config(
            name="classification_template",
            description="Classification task configuration template"
        )
        
        # 訓練配置
        config.training.epochs = 100
        config.training.batch_size = 32
        config.training.learning_rate = 1e-3
        config.training.num_classes = 10
        
        # 數據配置
        config.data.input_size = 224
        
        # 損失配置
        config.loss.primary_loss = "cross_entropy"
        
        return config
    
    @staticmethod
    def detection_config() -> Config:
        """檢測任務配置模板"""
        config = Config(
            name="detection_template",
            description="Object detection configuration template"
        )
        
        # 訓練配置
        config.training.epochs = 300
        config.training.batch_size = 16
        config.training.learning_rate = 1e-3
        
        # 數據配置
        config.data.dataset_type = "yolo"
        config.data.input_size = 640
        
        return config


# 使用示例
if __name__ == "__main__":
    # 創建配置管理器
    config_manager = ConfigManager("./configs")
    
    # 創建分割任務配置
    seg_config = ConfigTemplates.segmentation_config()
    
    # 保存配置
    config_manager.save_config(seg_config, "segmentation.yaml")
    
    # 加載配置
    loaded_config = config_manager.load_config("segmentation.yaml")
    
    # 更新配置
    updates = {
        "training": {"epochs": 300, "batch_size": 16},
        "data": {"input_size": 768}
    }
    updated_config = config_manager.update_config(loaded_config, updates)
    
    # 驗證配置
    is_valid = config_manager.validate_config(updated_config)
    print(f"配置有效性: {is_valid}")
    
    # 列出所有配置
    configs = config_manager.list_configs()
    print(f"可用配置: {configs}")