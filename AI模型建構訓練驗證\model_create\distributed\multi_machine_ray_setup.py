#!/usr/bin/env python3
"""
多台電腦Ray分散式訓練設置
支持跨多台機器的GPU訓練集群
"""

import os
import json
import logging
import subprocess
import socket
from typing import Dict, List, Optional, Any
from pathlib import Path
import yaml

try:
    import ray
    from ray.cluster_utils import Cluster
    from ray.train import ScalingConfig, RunConfig
    from ray.train.torch import TorchTrainer
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False


class MultiMachineRaySetup:
    """多台電腦Ray集群設置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        
        if not RAY_AVAILABLE:
            raise ImportError("Ray框架未安裝。請使用 'pip install ray[tune,train,data,serve]' 安裝")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """載入多機配置"""
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        
        # 預設多機配置
        return {
            "head_node": {
                "ip": "*************",  # 頭節點IP
                "port": 10001,
                "dashboard_port": 8265,
                "object_store_memory": 2000000000,  # 2GB
                "num_cpus": 8,
                "num_gpus": 2
            },
            "worker_nodes": [
                {
                    "ip": "*************",
                    "num_cpus": 8,
                    "num_gpus": 2,
                    "object_store_memory": 2000000000
                },
                {
                    "ip": "*************", 
                    "num_cpus": 8,
                    "num_gpus": 1,
                    "object_store_memory": 1000000000
                }
            ],
            "shared_storage": {
                "type": "nfs",  # 或 "s3", "gcs"
                "path": "/shared/ray_storage",
                "mount_point": "/mnt/shared"
            },
            "training": {
                "total_workers": 4,
                "use_gpu": True,
                "resources_per_worker": {"CPU": 2, "GPU": 1},
                "placement_strategy": "SPREAD"  # PACK, SPREAD, STRICT_SPREAD
            },
            "runtime_env": {
                "pip": [
                    "torch>=1.12.0",
                    "torchvision",
                    "ultralytics",
                    "opencv-python",
                    "albumentations"
                ],
                "env_vars": {
                    "CUDA_VISIBLE_DEVICES": "all"
                }
            }
        }
    
    def generate_cluster_config(self) -> str:
        """生成Ray集群配置文件"""
        cluster_config = {
            "cluster_name": "ai_training_cluster",
            "min_workers": len(self.config["worker_nodes"]),
            "max_workers": len(self.config["worker_nodes"]),
            "upscaling_speed": 1.0,
            "idle_timeout_minutes": 5,
            
            "provider": {
                "type": "local",
                "head_ip": self.config["head_node"]["ip"],
                "worker_ips": [node["ip"] for node in self.config["worker_nodes"]]
            },
            
            "auth": {
                "ssh_user": "ubuntu",  # 根據實際情況修改
                "ssh_private_key": "~/.ssh/ray-cluster-key"
            },
            
            "head_node": {
                "node_config": {
                    "ray_start_commands": [
                        f"ray start --head --port={self.config['head_node']['port']} "
                        f"--dashboard-port={self.config['head_node']['dashboard_port']} "
                        f"--object-store-memory={self.config['head_node']['object_store_memory']} "
                        f"--num-cpus={self.config['head_node']['num_cpus']} "
                        f"--num-gpus={self.config['head_node']['num_gpus']}"
                    ]
                }
            },
            
            "worker_nodes": {
                "node_config": {
                    "ray_start_commands": [
                        f"ray start --address={self.config['head_node']['ip']}:{self.config['head_node']['port']} "
                        "--num-cpus={num_cpus} --num-gpus={num_gpus} "
                        "--object-store-memory={object_store_memory}"
                    ]
                }
            },
            
            "file_mounts": {
                "/mnt/shared": self.config["shared_storage"]["path"]
            },
            
            "setup_commands": [
                "sudo apt-get update",
                "sudo apt-get install -y python3-pip",
                "pip3 install -U pip",
                "pip3 install ray[tune,train,data,serve]"
            ],
            
            "head_setup_commands": [
                "mkdir -p /mnt/shared/models",
                "mkdir -p /mnt/shared/datasets", 
                "mkdir -p /mnt/shared/logs"
            ]
        }
        
        # 保存配置文件
        config_path = Path("ray_cluster_config.yaml")
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(cluster_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Ray集群配置已生成: {config_path}")
        return str(config_path)
    
    def start_head_node(self) -> str:
        """啟動頭節點"""
        head_config = self.config["head_node"]
        
        cmd = [
            "ray", "start", "--head",
            f"--port={head_config['port']}",
            f"--dashboard-port={head_config['dashboard_port']}",
            f"--object-store-memory={head_config['object_store_memory']}",
            f"--num-cpus={head_config['num_cpus']}",
            f"--num-gpus={head_config['num_gpus']}",
            "--include-dashboard=true"
        ]
        
        self.logger.info(f"啟動頭節點命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self.logger.info("頭節點啟動成功")
            
            # 提取連接命令
            connect_cmd = None
            for line in result.stdout.split('\n'):
                if 'ray start --address' in line:
                    connect_cmd = line.strip()
                    break
            
            return connect_cmd
        except subprocess.CalledProcessError as e:
            self.logger.error(f"頭節點啟動失敗: {e}")
            raise
    
    def generate_worker_start_commands(self, head_address: str) -> List[str]:
        """生成工作節點啟動命令"""
        commands = []
        
        for i, worker in enumerate(self.config["worker_nodes"]):
            cmd = (
                f"ray start --address={head_address} "
                f"--num-cpus={worker['num_cpus']} "
                f"--num-gpus={worker['num_gpus']} "
                f"--object-store-memory={worker['object_store_memory']}"
            )
            commands.append(cmd)
        
        return commands
    
    def check_cluster_status(self) -> Dict[str, Any]:
        """檢查集群狀態"""
        try:
            # 連接到集群
            if not ray.is_initialized():
                head_config = self.config["head_node"]
                ray.init(address=f"{head_config['ip']}:{head_config['port']}")
            
            # 獲取集群資源
            resources = ray.cluster_resources()
            nodes = ray.nodes()
            
            status = {
                "connected": True,
                "total_cpus": resources.get("CPU", 0),
                "total_gpus": resources.get("GPU", 0),
                "total_nodes": len(nodes),
                "alive_nodes": len([n for n in nodes if n["Alive"]]),
                "resources": resources,
                "nodes": nodes
            }
            
            self.logger.info(f"集群狀態: {status}")
            return status
            
        except Exception as e:
            self.logger.error(f"無法連接到集群: {e}")
            return {"connected": False, "error": str(e)}
    
    def create_distributed_trainer(self, 
                                 train_func: callable,
                                 datasets: Dict[str, Any]) -> TorchTrainer:
        """創建分散式訓練器"""
        
        # 計算總資源
        total_workers = self.config["training"]["total_workers"]
        resources_per_worker = self.config["training"]["resources_per_worker"]
        
        # 創建縮放配置
        scaling_config = ScalingConfig(
            num_workers=total_workers,
            use_gpu=self.config["training"]["use_gpu"],
            resources_per_worker=resources_per_worker,
            placement_strategy=self.config["training"]["placement_strategy"]
        )
        
        # 創建運行配置
        run_config = RunConfig(
            name="multi_machine_training",
            storage_path=self.config["shared_storage"]["mount_point"],
            checkpoint_config=ray.train.CheckpointConfig(
                checkpoint_score_attribute="val_accuracy",
                checkpoint_score_order="max",
                num_to_keep=3
            )
        )
        
        # 創建訓練器
        trainer = TorchTrainer(
            train_loop_per_worker=train_func,
            train_loop_config={
                "datasets": datasets,
                "config": self.config
            },
            scaling_config=scaling_config,
            run_config=run_config,
            runtime_env=self.config["runtime_env"]
        )
        
        return trainer
    
    def generate_setup_guide(self) -> str:
        """生成設置指南"""
        guide = f"""
# 多台電腦Ray分散式訓練設置指南

## 1. 硬體需求
- 頭節點: {self.config['head_node']['num_cpus']} CPU, {self.config['head_node']['num_gpus']} GPU
- 工作節點: {len(self.config['worker_nodes'])} 台機器
  {chr(10).join([f"  - 節點 {i+1}: {node['num_cpus']} CPU, {node['num_gpus']} GPU" 
                 for i, node in enumerate(self.config['worker_nodes'])])}

## 2. 網路設置
- 確保所有機器在同一網段
- 開放端口: {self.config['head_node']['port']}, {self.config['head_node']['dashboard_port']}
- 配置SSH密鑰認證

## 3. 共享存儲設置
類型: {self.config['shared_storage']['type']}
路徑: {self.config['shared_storage']['path']}
掛載點: {self.config['shared_storage']['mount_point']}

## 4. 啟動步驟

### 頭節點 ({self.config['head_node']['ip']})
```bash
# 安裝Ray
pip install ray[tune,train,data,serve]

# 啟動頭節點
ray start --head \\
  --port={self.config['head_node']['port']} \\
  --dashboard-port={self.config['head_node']['dashboard_port']} \\
  --object-store-memory={self.config['head_node']['object_store_memory']} \\
  --num-cpus={self.config['head_node']['num_cpus']} \\
  --num-gpus={self.config['head_node']['num_gpus']} \\
  --include-dashboard=true
```

### 工作節點
{chr(10).join([f'''節點 {i+1} ({node['ip']}):
```bash
ray start --address={self.config['head_node']['ip']}:{self.config['head_node']['port']} \\
  --num-cpus={node['num_cpus']} \\
  --num-gpus={node['num_gpus']} \\
  --object-store-memory={node['object_store_memory']}
```''' for i, node in enumerate(self.config['worker_nodes'])])}

## 5. 驗證集群
- 訪問 Ray Dashboard: http://{self.config['head_node']['ip']}:{self.config['head_node']['dashboard_port']}
- 運行: `ray status`

## 6. 使用範例

```python
from multi_machine_ray_setup import MultiMachineRaySetup

# 初始化設置
setup = MultiMachineRaySetup('ray_config.yaml')

# 檢查集群狀態
status = setup.check_cluster_status()
print(f"集群狀態: {{status}}")

# 創建分散式訓練器
trainer = setup.create_distributed_trainer(train_func, datasets)

# 開始訓練
result = trainer.fit()
```

## 7. 故障排除
- 確保所有機器時間同步
- 檢查防火牆設置
- 驗證共享存儲權限
- 監控網路延遲和頻寬
"""
        
        guide_path = Path("multi_machine_ray_setup_guide.md")
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide)
        
        self.logger.info(f"設置指南已生成: {guide_path}")
        return str(guide_path)
    
    def save_config_template(self, output_path: str = "ray_multi_machine_config.yaml"):
        """保存配置模板"""
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"配置模板已保存: {output_path}")


def create_multi_machine_ray_setup(config_path: Optional[str] = None) -> MultiMachineRaySetup:
    """創建多機Ray設置"""
    return MultiMachineRaySetup(config_path)


# 命令行接口
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="多台電腦Ray集群設置")
    parser.add_argument("--config", help="配置文件路徑")
    parser.add_argument("--action", choices=["setup", "start-head", "status", "guide"], 
                       default="guide", help="執行動作")
    
    args = parser.parse_args()
    
    setup = MultiMachineRaySetup(args.config)
    
    if args.action == "setup":
        setup.generate_cluster_config()
        setup.save_config_template()
    elif args.action == "start-head":
        connect_cmd = setup.start_head_node()
        print(f"工作節點連接命令: {connect_cmd}")
    elif args.action == "status":
        status = setup.check_cluster_status()
        print(json.dumps(status, indent=2, ensure_ascii=False))
    elif args.action == "guide":
        guide_path = setup.generate_setup_guide()
        print(f"設置指南已生成: {guide_path}")