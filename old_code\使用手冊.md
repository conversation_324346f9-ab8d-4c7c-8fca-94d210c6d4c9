# 圖像數據集處理工具集 v2.0 使用手冊

## 📖 目
1. [安裝指南](#安裝指南)
2. [快速開始](#快速開始)
3. [GUI應用程式使用](#gui應用程式使用)
4. [命令行工具使用](#命令行工具使用)
5. [配置文件說明](#配置文件說明)
6. [各工具詳細說明](#各工具詳細說明)
7. [故障排解](#故障排解)
8. [常見問題](#常見問題)

## 🚀 安裝指南

### 系統要求
- Python 3.7 或更高版本
- 足夠的磁盤空間用於處理大型數據集
- 對於GUI版本：支援tkinter的系統（大多數Python安裝都包含）

### 安裝步驟

1. **下載源碼**
   ```bash
   # 將所有文件下載到同一目錄
   cd your-project-directory
   ```

2. **安裝依賴**
   ```bash
   pip install -r requirements.txt
   ```

3. **驗證安裝**
   ```bash
   # 測試命令行工具
   python panorama_augmenter.py --help
   
   # 測試GUI應用程式
   python gui_application.py
   ```

## ⚡ 快速開始

### 使用GUI應用程式（推薦）

1. **啟動應用程式**
   ```bash
   python gui_application.py
   ```

2. **選擇工具**
   - 在標籤頁中選擇需要的工具
   - 目前完整實現了"全景圖像擴增器"

3. **設置參數**
   - 填入輸入圖像路徑
   - 設置外方位參數（如果有）
   - 選擇擴增方法
   - 設置輸出目錄

4. **開始處理**
   - 點擊"開始處理"按鈕
   - 查看底部日誌區域的進度信息

### 使用命令行工具

**全景圖像擴增示例：**
```bash
python panorama_augmenter.py \
    --input your_panorama.jpg \
    --output output_dir \
    --omega 15.0 --phi -10.0 --kappa 45.0 \
    --methods orientation \
    --visualize
```

## 🖥️ GUI應用程式使用

### 主界面介紹

#### 1. 菜單欄
- **文件菜單**：新建/載入/保存配置，退出應用程式
- **工具菜單**：清除日誌、開啟輸出目錄
- **幫助菜單**：使用手冊、關於信息

#### 2. 工具標籤頁
- **標籤格式轉換器**：轉換各種標籤格式
- **標籤編輯器**：編輯和管理標籤
- **數據集分割器**：分割數據集
- **圖像增強器**：區域融合增強
- **全景圖像擴增器**：全景圖像專用擴增（✅ 已實現）
- **整合流程**：一鍵式完整處理流程

#### 3. 底部面板
- **進度條**：顯示處理進度
- **日誌區域**：實時顯示處理信息和錯誤

### 全景圖像擴增器詳細使用

#### 輸入設置
1. **圖像路徑**：選擇2:1比例的全景圖像文件
   - 支援格式：JPG, JPEG, PNG, BMP
   - 確保圖像寬度是高度的2倍

2. **標籤路徑**（可選）：選擇對應的LabelMe格式標籤文件
   - 如果沒有標籤，工具會創建空的標籤結構

3. **Excel外方位**（可選）：用於批量處理的外方位參數文件
   - Excel格式：包含 image_name, omega, phi, kappa 列

#### 外方位參數
- **Omega (俯仰角)**：相機繞X軸的旋轉角度（度）
- **Phi (橫滾角)**：相機繞Y軸的旋轉角度（度）
- **Kappa (偏航角)**：相機繞Z軸的旋轉角度（度）

💡 **提示**：如果輸入的是座標值而非角度值，工具會自動識別並使用零值替代

#### 擴增方法
- ✅ **orientation**：基於外方位的視角擴增（推薦）
  - 先校正相機姿態，再生成等間隔視角變化
  - 需要外方位參數

- ✅ **rotation**：隨機旋轉擴增
  - 在校正基礎上添加隨機擾動
  - 需要外方位參數

- ✅ **perspective**：隨機視角變換
  - 生成隨機觀看視角
  - 不需要外方位參數

- ✅ **tilt**：傾斜擴增
  - 模擬相機傾斜效果
  - 可設置最大傾斜角度

- ✅ **multi_angle**：多角度擴增
  - 等間隔角度旋轉
  - 可設置角度步長

#### 參數設置
- **變化數量**：每種方法生成的變化數量（1-20）
- **最大傾斜角度**：傾斜擴增的最大角度（度）
- **角度步長**：多角度擴增的角度間隔（度）

#### 輸出設置
- **輸出目錄**：結果保存位置
- **顯示可視化結果**：處理完成後顯示預覽窗口
- **保存可視化圖像**：將可視化結果保存為PNG文件

#### 處理模式
- **單張處理**：處理指定的單張圖像
- **批量處理**：處理目錄中的所有圖像（需要Excel外方位文件）

## 💻 命令行工具使用

### 全景圖像擴增器

#### 基本語法
```bash
python panorama_augmenter.py [OPTIONS]
```

#### 常用參數
- `--input PATH`：輸入圖像路徑
- `--output PATH`：輸出目錄
- `--label PATH`：標籤文件路徑（可選）
- `--omega FLOAT`：俯仰角（度）
- `--phi FLOAT`：橫滾角（度）
- `--kappa FLOAT`：偏航角（度）
- `--methods LIST`：擴增方法列表
- `--visualize`：顯示可視化結果
- `--save-visualization`：保存可視化圖像
- `--interactive`：交互式模式

#### 使用示例

**1. 基於外方位的擴增**
```bash
python panorama_augmenter.py \
    --input panorama.jpg \
    --output results \
    --omega 15.0 --phi -10.0 --kappa 45.0 \
    --methods orientation \
    --visualize
```

**2. 多種方法組合**
```bash
python panorama_augmenter.py \
    --input panorama.jpg \
    --output results \
    --methods orientation perspective tilt \
    --visualize --save-visualization
```

**3. 批量處理**
```bash
python panorama_augmenter.py \
    --input input_directory \
    --output results \
    --excel orientations.xlsx \
    --methods orientation \
    --batch
```

**4. 交互式模式**
```bash
python panorama_augmenter.py \
    --output results \
    --interactive
```

## ⚙️ 配置文件說明

### GUI配置文件 (gui_config.json)
```json
{
  "window_size": "1200x800",
  "theme": "default",
  "last_input_dir": "",
  "last_output_dir": "",
  "log_level": "INFO",
  
  "panorama_methods": ["orientation"],
  "panorama_num_variations": 8,
  "panorama_auto_visualize": false,
  "panorama_save_visualization": false,
  "panorama_max_tilt": 20.0,
  "panorama_angle_step": 45.0
}
```

### 外方位Excel文件格式
```
| image_name           | omega    | phi      | kappa    |
|---------------------|----------|----------|----------|
| panorama_001.jpg    | 15.0     | -10.0    | 45.0     |
| panorama_002.jpg    | -5.2     | 8.7      | -30.1    |
| panorama_003.jpg    | 0.0      | 0.0      | 90.0     |
```

## 🔧 各工具詳細說明

### 全景圖像擴增器

#### 功能特點
- 支援攝影測量標準OPK旋轉矩陣
- 精確的球面座標轉換
- 多種擴增策略
- 標籤座標自動轉換
- 實時可視化預覽

#### 輸入要求
- **圖像格式**：JPG, JPEG, PNG, BMP
- **圖像比例**：2:1（寬度 = 高度 × 2）
- **標籤格式**：LabelMe JSON格式（可選）
- **外方位格式**：Excel文件或直接輸入

#### 輸出結果
- **擴增圖像**：按方法分類保存在不同子目錄
- **轉換標籤**：對應的LabelMe格式標籤文件
- **可視化圖像**：網格布局的預覽圖（可選）

#### 技術細節
- **座標系統**：球面座標系統
- **旋轉矩陣**：標準攝影測量OPK順序
- **插值方法**：三次插值（INTER_CUBIC）
- **邊界處理**：環繞模式（BORDER_WRAP）

### 其他工具（開發中）
- 標籤格式轉換器
- 標籤編輯器
- 數據集分割器
- 圖像增強器

## 🔍 故障排解

### 常見錯誤及解決方案

#### 1. 圖像比例錯誤
**錯誤信息**：`圖片比例不正確！高度:X, 寬度:Y, 應為 2:1 比例`

**解決方案**：
- 確保輸入圖像是全景圖像（等距圓柱投影）
- 檢查圖像寬度是否等於高度的2倍
- 如需調整比例，請使用圖像編輯軟體

#### 2. 外方位參數過大
**錯誤信息**：`外方位參數值過大，可能不是角度值`

**解決方案**：
- 確認輸入的是角度值（-180到180度）而非座標值
- 如果是座標系統的數值，請轉換為相對角度
- 工具會自動使用零值替代異常大的數值

#### 3. 可視化顯示問題
**症狀**：無法顯示matplotlib窗口

**解決方案**：
```bash
# 安裝matplotlib後端
pip install matplotlib
# 對於Linux系統
sudo apt-get install python3-tk
# 對於macOS
brew install python-tk
```

#### 4. 內存不足
**症狀**：處理大圖像時崩潰

**解決方案**：
- 使用較小的圖像進行測試
- 減少同時處理的變化數量
- 關閉可視化功能
- 增加系統虛擬內存

#### 5. 文件路徑問題
**症狀**：無法讀取中文路徑的文件

**解決方案**：
- 確保文件路徑不包含特殊字符
- 使用英文路徑名稱
- 檢查文件權限

## ❓ 常見問題

### Q1: 如何選擇合適的擴增方法？
**A**: 
- 有外方位參數：使用 `orientation` 方法（推薦）
- 無外方位參數：使用 `perspective` 和 `tilt` 方法
- 需要特定角度：使用 `multi_angle` 方法

### Q2: 外方位參數的單位是什麼？
**A**: 外方位參數使用度（degrees）為單位，範圍通常在-180到180度之間。

### Q3: 可以處理沒有標籤的圖像嗎？
**A**: 可以！工具會自動創建空的標籤結構，即使沒有標註也能進行圖像擴增。

### Q4: 批量處理需要什麼格式的Excel文件？
**A**: Excel文件需要包含以下列：
- `image_name` 或 `filename`：圖像文件名
- `omega`：俯仰角
- `phi`：橫滾角  
- `kappa`：偏航角

### Q5: 可視化功能不工作怎麼辦？
**A**: 
1. 確保安裝了matplotlib：`pip install matplotlib`
2. 檢查是否有GUI後端支持
3. 嘗試使用 `--save-visualization` 參數保存圖像而不是顯示

### Q6: 如何提高處理速度？
**A**:
- 使用較小的圖像尺寸
- 減少變化數量
- 關閉可視化功能
- 使用SSD硬盤

### Q7: 輸出的標籤座標準確嗎？
**A**: 是的，工具使用精確的球面座標轉換和旋轉矩陣，確保標籤座標與圖像變換完全同步。

### Q8: 支援哪些圖像格式？
**A**: 支援常見的圖像格式：JPG, JPEG, PNG, BMP。建議使用JPG格式以節省存儲空間。

### Q9: 如何自定義配置？
**A**: 
- GUI模式：使用界面上的"保存配置"按鈕
- 命令行模式：編輯相應的配置文件
- 配置會自動保存在 `gui_config.json` 文件中

### Q10: 處理失敗時如何查看詳細錯誤？
**A**: 查看以下位置的錯誤信息：
- GUI模式：底部日誌區域
- 命令行模式：終端輸出
- 日誌文件：自動生成的 `.log` 文件

## 📞 技術支持

### 獲取幫助
1. **查看日誌**：詳細的錯誤信息都會記錄在日誌中
2. **檢查文檔**：參考本使用手冊和工具內建幫助
3. **運行測試**：使用提供的示例腳本進行測試

### 反饋問題
報告問題時請提供：
- 操作系統版本
- Python版本
- 完整的錯誤信息
- 使用的命令或設置
- 示例數據（如果可能）

### 更新日誌
- v2.0：新增GUI應用程式和全景圖像擴增器
- v1.0：基礎命令行工具集

---

© 2024 圖像數據集處理工具集團隊 | 最後更新：2024年12月