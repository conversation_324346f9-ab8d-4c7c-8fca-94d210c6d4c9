#!/usr/bin/env python3
"""
增強型輸出管理器
支援三視圖輸出、報告生成和舊版兼容格式
"""

import os
import json
import csv
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime
import pandas as pd


class EnhancedOutputManager:
    """增強型輸出管理器"""
    
    def __init__(self, output_path: str, config=None):
        self.output_path = Path(output_path)
        self.config = config
        
        # 創建輸出目錄結構
        self.images_dir = self.output_path / "images"
        self.reports_dir = self.output_path / "reports"
        self.temp_dir = self.output_path / "temp_csv"
        
        # 創建目錄
        self.images_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化統計數據
        self.image_metrics = []
        self.class_metrics = {}
        self.batch_statistics = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "failed_files": [],
            "start_time": datetime.now().isoformat(),
            "inference_stats": {
                "total_images": 0,
                "total_detections": 0,
                "processing_time": 0,
                "class_counts": {}
            }
        }
        
        # 類別顏色映射
        self.class_colors = self._initialize_class_colors()
        
        print(f"✅ 輸出管理器初始化完成")
        print(f"  📁 主目錄: {self.output_path}")
        print(f"  🖼️ 圖像目錄: {self.images_dir}")
        print(f"  📊 報告目錄: {self.reports_dir}")
    
    def _initialize_class_colors(self) -> Dict[int, Tuple[int, int, int]]:
        """初始化類別顏色映射"""
        default_colors = [
            (255, 0, 0),     # 紅色
            (0, 255, 0),     # 綠色
            (0, 0, 255),     # 藍色
            (255, 255, 0),   # 黃色
            (255, 0, 255),   # 洋紅色
            (0, 255, 255),   # 青色
            (128, 0, 128),   # 紫色
            (255, 165, 0),   # 橘色
            (139, 69, 19),   # 棕色
            (0, 128, 255),   # 淺藍色
        ]
        
        colors = {}
        if self.config and hasattr(self.config, 'class_configs'):
            for class_id, class_config in self.config.class_configs.items():
                if class_config.color:
                    colors[class_id] = class_config.color
                else:
                    colors[class_id] = default_colors[class_id % len(default_colors)]
        else:
            for i in range(10):  # 預設10個類別
                colors[i] = default_colors[i % len(default_colors)]
                
        return colors
    
    def create_three_view_output(self, 
                               image_path: str, 
                               predictions: Dict[str, Any],
                               gt_annotations: Optional[Dict[str, Any]] = None) -> str:
        """創建三視圖輸出 (原圖/GT/預測)"""
        
        try:
            # 讀取原圖
            original_img = cv2.imread(image_path)
            if original_img is None:
                raise ValueError(f"無法讀取圖像: {image_path}")
            
            img_height, img_width = original_img.shape[:2]
            
            # 創建三個視圖
            view1 = original_img.copy()  # 原圖
            view2 = original_img.copy()  # GT視圖
            view3 = original_img.copy()  # 預測視圖
            
            font_size = self.config.text_font_size if self.config else 2.0
            
            # 添加GT標註 (如果有的話)
            if gt_annotations and self.config and self.config.enable_gt_comparison:
                view2 = self._draw_gt_annotations(view2, gt_annotations, font_size)
            
            # 添加預測結果
            view3 = self._draw_predictions(view3, predictions, font_size)
            
            # 創建標題
            title_height = 60
            combined_height = img_height + title_height
            combined_width = img_width * 3
            
            # 創建組合圖像
            combined_img = np.zeros((combined_height, combined_width, 3), dtype=np.uint8)
            
            # 添加標題背景
            combined_img[:title_height, :] = (50, 50, 50)  # 深灰色背景
            
            # 放置三個視圖
            combined_img[title_height:, :img_width] = view1
            combined_img[title_height:, img_width:img_width*2] = view2
            combined_img[title_height:, img_width*2:] = view3
            
            # 添加標題文字
            self._add_chinese_text(combined_img, "原始圖像", (img_width//4, 35), font_size, (255, 255, 255))
            gt_title = "GT標註" if gt_annotations else "GT未提供"
            self._add_chinese_text(combined_img, gt_title, (img_width + img_width//4, 35), font_size, (255, 255, 255))
            self._add_chinese_text(combined_img, "預測結果", (img_width*2 + img_width//4, 35), font_size, (255, 255, 255))
            
            # 保存圖像
            image_name = Path(image_path).stem
            output_filename = f"three_view_{image_name}.jpg"
            output_path = self.images_dir / output_filename
            
            # 設定圖像品質
            quality = self.config.output_image_quality if self.config else 95
            cv2.imwrite(str(output_path), combined_img, [cv2.IMWRITE_JPEG_QUALITY, quality])
            
            print(f"✅ 三視圖已保存: {output_filename}")
            return str(output_path)
            
        except Exception as e:
            print(f"❌ 三視圖創建失敗: {e}")
            return ""
    
    def _draw_gt_annotations(self, img: np.ndarray, gt_annotations: Dict[str, Any], font_size: float) -> np.ndarray:
        """繪製GT標註"""
        if not gt_annotations or 'boxes' not in gt_annotations:
            return img
        
        for i, (box, class_id, label) in enumerate(zip(
            gt_annotations['boxes'], 
            gt_annotations.get('class_ids', []), 
            gt_annotations.get('labels', [])
        )):
            # 獲取顏色 (GT使用較暗的顏色)
            if class_id in self.class_colors:
                color = self.class_colors[class_id]
                gt_color = tuple(int(c * 0.8) for c in color)  # GT顏色較暗
            else:
                gt_color = (0, 128, 0)  # 預設深綠色
            
            # 繪製邊界框
            x1, y1, x2, y2 = map(int, box)
            cv2.rectangle(img, (x1, y1), (x2, y2), gt_color, 2)
            
            # 添加標籤
            label_text = f"GT: {label}"
            self._add_chinese_text(img, label_text, (x1, y1 - 10), font_size * 0.8, gt_color)
        
        return img
    
    def _draw_predictions(self, img: np.ndarray, predictions: Dict[str, Any], font_size: float) -> np.ndarray:
        """繪製預測結果"""
        if not predictions or 'boxes' not in predictions:
            return img
        
        for i, (box, class_id, conf, label) in enumerate(zip(
            predictions['boxes'],
            predictions.get('class_ids', []),
            predictions.get('confidences', []),
            predictions.get('labels', [])
        )):
            # 獲取顏色
            if class_id in self.class_colors:
                color = self.class_colors[class_id]
            else:
                color = (255, 255, 255)  # 預設白色
            
            # 繪製邊界框
            x1, y1, x2, y2 = map(int, box)
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
            
            # 添加標籤和置信度
            label_text = f"{label}: {conf:.3f}"
            self._add_chinese_text(img, label_text, (x1, y1 - 10), font_size * 0.8, color)
        
        return img
    
    def _add_chinese_text(self, img: np.ndarray, text: str, position: Tuple[int, int], 
                         font_size: float, color: Tuple[int, int, int]):
        """添加中文文字支援"""
        try:
            # 簡化版本：使用OpenCV基本字體
            font = cv2.FONT_HERSHEY_SIMPLEX
            thickness = max(1, int(font_size))
            cv2.putText(img, text, position, font, font_size, color, thickness)
        except Exception as e:
            print(f"⚠️ 文字添加失敗: {e}")
    
    def add_image_result(self, image_path: str, predictions: Dict[str, Any], 
                        gt_annotations: Optional[Dict[str, Any]] = None,
                        processing_time: float = 0.0):
        """添加圖像結果到統計"""
        
        image_name = Path(image_path).stem
        
        # 創建三視圖輸出
        if self.config and self.config.enable_three_view_output:
            three_view_path = self.create_three_view_output(image_path, predictions, gt_annotations)
        
        # 統計檢測結果
        num_detections = len(predictions.get('boxes', []))
        class_ids = predictions.get('class_ids', [])
        
        # 更新批次統計
        self.batch_statistics["total_processed"] += 1
        self.batch_statistics["successful"] += 1
        self.batch_statistics["inference_stats"]["total_images"] += 1
        self.batch_statistics["inference_stats"]["total_detections"] += num_detections
        self.batch_statistics["inference_stats"]["processing_time"] += processing_time
        
        # 更新類別計數
        for class_id in class_ids:
            class_key = str(class_id)
            if class_key not in self.batch_statistics["inference_stats"]["class_counts"]:
                self.batch_statistics["inference_stats"]["class_counts"][class_key] = 0
            self.batch_statistics["inference_stats"]["class_counts"][class_key] += 1
        
        # 創建圖像級別記錄
        if predictions.get('boxes'):
            for i, (box, class_id, conf, label) in enumerate(zip(
                predictions['boxes'],
                predictions.get('class_ids', []),
                predictions.get('confidences', []),
                predictions.get('labels', [])
            )):
                x1, y1, x2, y2 = box
                width = x2 - x1
                height = y2 - y1
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                # 計算TP/FP/FN (簡化版本)
                tp, fp, fn = self._calculate_metrics(box, class_id, gt_annotations)
                
                self.image_metrics.append({
                    "圖像名稱": image_name,
                    "圖像路徑": str(image_path),
                    "類別": label,
                    "類別的長": f"{width:.1f}",
                    "類別的寬": f"{height:.1f}",
                    "類別的座標": f"{{{center_x:.1f},{center_y:.1f}}}",
                    "TP": tp,
                    "FP": fp,
                    "FN": fn,
                    "置信度": f"{conf:.3f}",
                    "處理時間": f"{processing_time:.3f}"
                })
        else:
            # 無檢測結果的記錄
            self.image_metrics.append({
                "圖像名稱": image_name,
                "圖像路徑": str(image_path),
                "類別": "無檢測結果",
                "類別的長": "0",
                "類別的寬": "0",
                "類別的座標": "{0,0}",
                "TP": 0,
                "FP": 0,
                "FN": 1 if gt_annotations and gt_annotations.get('boxes') else 0,
                "置信度": "0.000",
                "處理時間": f"{processing_time:.3f}"
            })
        
        print(f"📊 已記錄圖像結果: {image_name} ({num_detections} 個檢測)")
    
    def _calculate_metrics(self, pred_box: List[float], pred_class_id: int, 
                          gt_annotations: Optional[Dict[str, Any]]) -> Tuple[int, int, int]:
        """計算TP/FP/FN指標 (簡化版本)"""
        if not gt_annotations or not gt_annotations.get('boxes'):
            return 0, 1, 0  # 無GT時，預測為FP
        
        # 簡化版本：基於IoU閾值判斷
        iou_threshold = 0.5
        max_iou = 0
        best_match_class = -1
        
        for gt_box, gt_class_id in zip(gt_annotations['boxes'], gt_annotations.get('class_ids', [])):
            iou = self._calculate_iou(pred_box, gt_box)
            if iou > max_iou:
                max_iou = iou
                best_match_class = gt_class_id
        
        if max_iou >= iou_threshold and best_match_class == pred_class_id:
            return 1, 0, 0  # TP
        else:
            return 0, 1, 0  # FP
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def add_failed_image(self, image_path: str, error: str):
        """添加失敗的圖像記錄"""
        self.batch_statistics["total_processed"] += 1
        self.batch_statistics["failed"] += 1
        self.batch_statistics["failed_files"].append({
            "path": str(image_path),
            "error": str(error),
            "timestamp": datetime.now().isoformat()
        })
        print(f"❌ 記錄失敗圖像: {Path(image_path).name} - {error}")
    
    def generate_reports(self):
        """生成完整報告"""
        if not (self.config and self.config.enable_reports):
            print("📊 報告生成已停用")
            return
        
        print("📊 開始生成報告...")
        
        try:
            # 1. 生成圖像級別報告
            self._generate_image_metrics_report()
            
            # 2. 生成類別級別報告
            self._generate_class_metrics_report()
            
            # 3. 生成批次統計報告
            self._generate_batch_statistics_report()
            
            # 4. 生成檢測指標報告
            self._generate_detection_metrics_report()
            
            print("✅ 所有報告生成完成")
            print(f"📁 報告位置: {self.reports_dir}")
            
        except Exception as e:
            print(f"❌ 報告生成失敗: {e}")
    
    def _generate_image_metrics_report(self):
        """生成圖像級別報告"""
        if not self.image_metrics:
            print("⚠️ 無圖像數據，跳過圖像報告")
            return
        
        # 保存為CSV
        csv_path = self.reports_dir / "image_metrics.csv"
        
        df = pd.DataFrame(self.image_metrics)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        print(f"✅ 圖像級別報告已保存: {csv_path.name}")
    
    def _generate_class_metrics_report(self):
        """生成類別級別報告"""
        if not self.image_metrics:
            print("⚠️ 無數據，跳過類別報告")
            return
        
        # 統計每個類別的指標
        class_stats = {}
        
        for record in self.image_metrics:
            class_name = record["類別"]
            if class_name == "無檢測結果":
                continue
                
            if class_name not in class_stats:
                class_stats[class_name] = {"TP": 0, "FP": 0, "FN": 0, "總數": 0}
            
            class_stats[class_name]["TP"] += record["TP"]
            class_stats[class_name]["FP"] += record["FP"]
            class_stats[class_name]["FN"] += record["FN"]
            class_stats[class_name]["總數"] += 1
        
        # 計算指標
        class_metrics = []
        total_tp = total_fp = total_fn = 0
        
        for class_name, stats in class_stats.items():
            tp, fp, fn = stats["TP"], stats["FP"], stats["FN"]
            total_tp += tp
            total_fp += fp
            total_fn += fn
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
            
            miss_rate = fn / (tp + fn) if (tp + fn) > 0 else 0.0
            false_positive_rate = fp / (fp + tp) if (fp + tp) > 0 else 0.0
            
            class_metrics.append({
                "各類別名稱": class_name,
                "TP": tp,
                "FP": fp,
                "FN": fn,
                "Precision": f"{precision:.3f}",
                "Recall": f"{recall:.3f}",
                "F1": f"{f1:.3f}",
                "類別總數": stats["總數"],
                "誤判率": f"{false_positive_rate:.3f}",
                "漏判率": f"{miss_rate:.3f}"
            })
        
        # 添加整體平均
        if total_tp + total_fp + total_fn > 0:
            overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
            overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
            overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
            
            class_metrics.append({
                "各類別名稱": "整體平均",
                "TP": total_tp,
                "FP": total_fp,
                "FN": total_fn,
                "Precision": f"{overall_precision:.3f}",
                "Recall": f"{overall_recall:.3f}",
                "F1": f"{overall_f1:.3f}",
                "類別總數": len(class_stats),
                "誤判率": f"{total_fp / (total_fp + total_tp) if (total_fp + total_tp) > 0 else 0.0:.3f}",
                "漏判率": f"{total_fn / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0:.3f}"
            })
        
        # 保存為CSV
        csv_path = self.reports_dir / "class_metrics.csv"
        df = pd.DataFrame(class_metrics)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        print(f"✅ 類別級別報告已保存: {csv_path.name}")
    
    def _generate_batch_statistics_report(self):
        """生成批次統計報告"""
        # 更新結束時間
        self.batch_statistics["end_time"] = datetime.now().isoformat()
        
        # 保存為JSON
        json_path = self.reports_dir / "batch_statistics.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.batch_statistics, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 批次統計報告已保存: {json_path.name}")
    
    def _generate_detection_metrics_report(self):
        """生成檢測指標報告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_path = self.reports_dir / f"detection_metrics_{timestamp}.csv"
        
        # 創建詳細的檢測指標
        detection_metrics = []
        
        for record in self.image_metrics:
            if record["類別"] != "無檢測結果":
                detection_metrics.append({
                    "時間戳": timestamp,
                    "圖像名稱": record["圖像名稱"],
                    "類別": record["類別"],
                    "座標": record["類別的座標"],
                    "尺寸": f"{record['類別的長']}x{record['類別的寬']}",
                    "置信度": record["置信度"],
                    "TP": record["TP"],
                    "FP": record["FP"],
                    "FN": record["FN"],
                    "處理時間": record["處理時間"]
                })
        
        if detection_metrics:
            df = pd.DataFrame(detection_metrics)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✅ 檢測指標報告已保存: {csv_path.name}")
        else:
            print("⚠️ 無檢測數據，跳過檢測指標報告")
    
    def get_summary(self) -> Dict[str, Any]:
        """獲取處理摘要"""
        total_images = self.batch_statistics["inference_stats"]["total_images"]
        total_detections = self.batch_statistics["inference_stats"]["total_detections"]
        successful = self.batch_statistics["successful"]
        failed = self.batch_statistics["failed"]
        
        return {
            "total_processed": self.batch_statistics["total_processed"],
            "successful": successful,
            "failed": failed,
            "success_rate": f"{successful / (successful + failed) * 100:.1f}%" if (successful + failed) > 0 else "0%",
            "total_detections": total_detections,
            "avg_detections_per_image": f"{total_detections / total_images:.1f}" if total_images > 0 else "0",
            "output_directories": {
                "images": str(self.images_dir),
                "reports": str(self.reports_dir)
            }
        }
    
    def finalize(self):
        """完成處理並生成最終報告"""
        print("\n📊 完成處理，生成最終報告...")
        
        # 生成所有報告
        self.generate_reports()
        
        # 顯示摘要
        summary = self.get_summary()
        print("\n" + "=" * 50)
        print("🎉 處理完成摘要:")
        print(f"  📊 總處理數量: {summary['total_processed']}")
        print(f"  ✅ 成功: {summary['successful']}")
        print(f"  ❌ 失敗: {summary['failed']}")
        print(f"  📈 成功率: {summary['success_rate']}")
        print(f"  🎯 總檢測數: {summary['total_detections']}")
        print(f"  📊 平均檢測數/圖: {summary['avg_detections_per_image']}")
        print(f"  📁 輸出目錄:")
        print(f"    🖼️ 圖像: {summary['output_directories']['images']}")
        print(f"    📊 報告: {summary['output_directories']['reports']}")
        print("=" * 50)