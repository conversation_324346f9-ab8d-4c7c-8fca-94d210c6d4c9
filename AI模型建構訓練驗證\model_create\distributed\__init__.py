"""
Ray分散式計算框架整合模組

提供Ray框架與AI模型建構的深度整合：
- 分散式訓練: 與UnifiedTrainer無縫整合的多GPU、多節點訓練
- 超參數優化: 基於ModelFactory的自動化模型調優  
- 資料處理: 整合現有資料管線的分散式處理
- 模型服務: 可擴展的推理服務

核心特性：
- 與現有架構完全兼容
- 配置驅動的分散式訓練
- 統一的訓練和調優接口
"""

# 基礎Ray整合
from .ray_integration import (
    RayIntegrationManager,
    RayDistributedTrainer,
    RayHyperparameterTuner,
    RayDataProcessor,
    RayModelServer,
    create_ray_search_space
)

# 深度AI整合
from .ray_ai_integration import (
    RayAIIntegrationManager,
    RayDistributedTrainingSystem,
    RayHyperparameterOptimizer,
    RayTrainingConfig,
    create_ray_training_system,
    create_search_space_for_model
)

__all__ = [
    # 基礎Ray框架
    'RayIntegrationManager',
    'RayDistributedTrainer', 
    'RayHyperparameterTuner',
    'RayDataProcessor',
    'RayModelServer',
    'create_ray_search_space',
    
    # AI模型深度整合
    'RayAIIntegrationManager',
    'RayDistributedTrainingSystem',
    'RayHyperparameterOptimizer',
    'RayTrainingConfig',
    'create_ray_training_system',
    'create_search_space_for_model'
]

__version__ = "1.0.0"