# 模型架構API文檔

*自動生成於: 2025-06-20 22:16:15*

## 概覽

本框架提供了 **16** 個模型類和網路組件。

### TestModel

**模組**: `scripts/ci_cd_automation.py`

**說明**: 無文檔說明...

---

### ModelTestMixin

**模組**: `tests/base_test.py`

**說明**: 模型測試混合類...

---

### ComprehensiveModelTest

**模組**: `tests/test_comprehensive.py`

**說明**: 全面的模型測試...

---

### SimpleModel

**模組**: `tests/test_optimization_integration.py`

**說明**: 無文檔說明...

---

### ModelArchitectureTest

**模組**: `tests/test_suite.py`

**說明**: AI模型架構測試...

---

### SimpleTestModel

**模組**: `AI模型建構訓練驗證/model_create/benchmark/performance_benchmark.py`

**說明**: 無文檔說明...

---

### EncoderDecoderModel

**模組**: `AI模型建構訓練驗證/model_create/core/model_factory.py`

**說明**: 編碼器-解碼器模型...

---

### BestModelCreator

**模組**: `AI模型建構訓練驗證/model_create/core/unified_registry.py`

**說明**: 自動選擇最佳模型配置...

---

### RayModelServer

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_integration.py`

**說明**: Ray模型服務器...

---

### ModelServeDeployment

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_integration.py`

**說明**: 模型服務部署...

---

### ModelCheckpoint

**模組**: `AI模型建構訓練驗證/model_create/training/callbacks.py`

**說明**: 模型檢查點回調...

---

### BaseModelConverter

**模組**: `AI模型建構訓練驗證/model_create/util/model_converters.py`

**說明**: 模型轉換器基類...

---

### BaseModel

**模組**: `AI模型建構訓練驗證/model_create/encoder/CNN/mobilev3seg/base.py`

**說明**: 無文檔說明...

---

### _SimpleSegmentationModel

**模組**: `AI模型建構訓練驗證/model_create/full_model/deeplab/utils.py`

**說明**: 無文檔說明...

---

### ParallelModel

**模組**: `AI模型建構訓練驗證/model_create/full_model/mrcnn/parallel_model.py`

**說明**: Subclasses the standard Keras Model and adds multi-GPU support.
It works by creating a copy of the model on each GPU. Then it slices
the inputs and sends a slice to each copy of the model, and then
me...

---

### SegMANSegmentationModel

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Complete SegMAN segmentation model combining encoder and decoder...

---

