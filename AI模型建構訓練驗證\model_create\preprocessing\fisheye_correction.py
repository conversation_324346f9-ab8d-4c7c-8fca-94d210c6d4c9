#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
魚眼鏡頭校正模組

基於test_model_confusionmatrix_v3_testbind3.py建構的魚眼鏡頭校正系統
支援魚眼裁切和正射校正功能
"""

import os
import cv2
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
import json


class FisheyeCorrector:
    """
    魚眼鏡頭校正器
    
    提供魚眼圖像的畸變校正和正射投影功能
    """
    
    def __init__(self,
                 mapx_path: Optional[str] = None,
                 mapy_path: Optional[str] = None,
                 mtx_path: Optional[str] = None,
                 xy_mtx_path: Optional[str] = None,
                 crop_params: Optional[Dict] = None,
                 perspective_params: Optional[Dict] = None):
        """
        初始化魚眼校正器
        
        參數:
            mapx_path: X方向重映射矩陣路徑
            mapy_path: Y方向重映射矩陣路徑  
            mtx_path: 透視變換矩陣路徑
            xy_mtx_path: XY重映射矩陣路徑（numpy格式）
            crop_params: 裁切參數字典
            perspective_params: 透視變換參數字典
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 默認參數（來自原始代碼）
        self.default_crop_params = {
            'standard': [1540, 2880, 2200, 3700],  # [y1, y2, x1, x2]
            'fmo': [1140, 2480, 2200, 3700]       # FMO模式
        }
        
        self.mapx = None
        self.mapy = None
        self.mtx = None
        
        # 載入校正參數
        self._load_correction_parameters(
            mapx_path, mapy_path, mtx_path, xy_mtx_path
        )
        
        # 設置裁切參數
        self.crop_params = crop_params or self.default_crop_params
        self.perspective_params = perspective_params
        
        self.logger.info("魚眼校正器初始化完成")
    
    def _load_correction_parameters(self,
                                  mapx_path: Optional[str],
                                  mapy_path: Optional[str], 
                                  mtx_path: Optional[str],
                                  xy_mtx_path: Optional[str]):
        """載入校正參數"""
        try:
            # 優先使用xy_mtx_path（包含mapx和mapy）
            if xy_mtx_path and os.path.exists(xy_mtx_path):
                self.mapx, self.mapy = np.load(xy_mtx_path)
                self.logger.info(f"已載入XY重映射矩陣: {xy_mtx_path}")
            elif mapx_path and mapy_path:
                if os.path.exists(mapx_path) and os.path.exists(mapy_path):
                    self.mapx = np.load(mapx_path)
                    self.mapy = np.load(mapy_path)
                    self.logger.info(f"已載入重映射矩陣: {mapx_path}, {mapy_path}")
            else:
                self.logger.warning("未提供重映射矩陣路徑，將使用恆等映射")
                # 創建恆等映射作為備用
                self.mapx = None
                self.mapy = None
            
            # 載入透視變換矩陣
            if mtx_path and os.path.exists(mtx_path):
                self.mtx = np.load(mtx_path)
                self.logger.info(f"已載入透視變換矩陣: {mtx_path}")
            else:
                self.logger.warning("未提供透視變換矩陣，將使用恆等變換")
                self.mtx = None
                
        except Exception as e:
            self.logger.error(f"載入校正參數失敗: {e}")
            raise
    
    def fisheye_correction(self, 
                          image: np.ndarray,
                          mode: str = 'standard',
                          custom_crop: Optional[List[int]] = None) -> np.ndarray:
        """
        執行魚眼校正（裁切 + 正射校正）
        
        參數:
            image: 輸入圖像
            mode: 校正模式 ('standard' 或 'fmo')
            custom_crop: 自定義裁切參數 [y1, y2, x1, x2]
            
        返回:
            校正後的圖像
        """
        if image is None or image.size == 0:
            raise ValueError("輸入圖像無效")
        
        try:
            # 第一步：魚眼畸變校正
            corrected_image = self._apply_fisheye_correction(image)
            
            # 第二步：裁切
            cropped_image = self._apply_crop(corrected_image, mode, custom_crop)
            
            # 第三步：正射校正
            ortho_image = self._apply_perspective_correction(cropped_image)
            
            return ortho_image
            
        except Exception as e:
            self.logger.error(f"魚眼校正失敗: {e}")
            raise
    
    def _apply_fisheye_correction(self, image: np.ndarray) -> np.ndarray:
        """應用魚眼畸變校正"""
        if self.mapx is None or self.mapy is None:
            self.logger.warning("未載入重映射矩陣，跳過魚眼校正")
            return image
        
        try:
            # 使用cv2.remap進行畸變校正
            corrected = cv2.remap(image, self.mapx, self.mapy, cv2.INTER_LINEAR)
            return corrected
            
        except Exception as e:
            self.logger.error(f"魚眼畸變校正失敗: {e}")
            return image
    
    def _apply_crop(self, 
                   image: np.ndarray, 
                   mode: str,
                   custom_crop: Optional[List[int]]) -> np.ndarray:
        """應用圖像裁切"""
        try:
            if custom_crop:
                y1, y2, x1, x2 = custom_crop
            elif mode in self.crop_params:
                y1, y2, x1, x2 = self.crop_params[mode]
            else:
                self.logger.warning(f"未知的裁切模式: {mode}，跳過裁切")
                return image
            
            # 確保裁切參數在圖像範圍內
            h, w = image.shape[:2]
            y1 = max(0, min(y1, h))
            y2 = max(y1, min(y2, h))
            x1 = max(0, min(x1, w))
            x2 = max(x1, min(x2, w))
            
            cropped = image[y1:y2, x1:x2]
            return cropped
            
        except Exception as e:
            self.logger.error(f"圖像裁切失敗: {e}")
            return image
    
    def _apply_perspective_correction(self, image: np.ndarray) -> np.ndarray:
        """應用正射校正（透視變換）"""
        if self.mtx is None:
            self.logger.warning("未載入透視變換矩陣，跳過正射校正")
            return image
        
        try:
            # 使用cv2.warpPerspective進行透視變換
            corrected = cv2.warpPerspective(
                image, self.mtx, (image.shape[1], image.shape[0])
            )
            return corrected
            
        except Exception as e:
            self.logger.error(f"正射校正失敗: {e}")
            return image
    
    def batch_correction(self, 
                        image_paths: List[str],
                        output_dir: str,
                        mode: str = 'standard',
                        save_format: str = 'jpg') -> Dict[str, str]:
        """
        批次執行魚眼校正
        
        參數:
            image_paths: 圖像路徑列表
            output_dir: 輸出目錄
            mode: 校正模式
            save_format: 保存格式
            
        返回:
            處理結果字典 {原始路徑: 輸出路徑}
        """
        os.makedirs(output_dir, exist_ok=True)
        results = {}
        
        for image_path in image_paths:
            try:
                # 載入圖像
                image = cv2.imread(image_path)
                if image is None:
                    self.logger.warning(f"無法載入圖像: {image_path}")
                    continue
                
                # 執行校正
                corrected = self.fisheye_correction(image, mode)
                
                # 保存結果
                filename = Path(image_path).stem
                output_path = os.path.join(
                    output_dir, f"{filename}_corrected.{save_format}"
                )
                
                cv2.imwrite(output_path, corrected)
                results[image_path] = output_path
                
                self.logger.info(f"已處理: {image_path} -> {output_path}")
                
            except Exception as e:
                self.logger.error(f"處理圖像失敗 {image_path}: {e}")
                continue
        
        return results
    
    def get_correction_info(self) -> Dict:
        """獲取校正器信息"""
        info = {
            'has_remap_matrix': self.mapx is not None and self.mapy is not None,
            'has_perspective_matrix': self.mtx is not None,
            'crop_modes': list(self.crop_params.keys()),
            'remap_matrix_shape': None,
            'perspective_matrix_shape': None
        }
        
        if info['has_remap_matrix']:
            info['remap_matrix_shape'] = {
                'mapx': self.mapx.shape,
                'mapy': self.mapy.shape
            }
        
        if info['has_perspective_matrix']:
            info['perspective_matrix_shape'] = self.mtx.shape
        
        return info
    
    def save_correction_params(self, save_dir: str):
        """保存校正參數"""
        os.makedirs(save_dir, exist_ok=True)
        
        if self.mapx is not None and self.mapy is not None:
            xy_path = os.path.join(save_dir, 'xy_mtx.npy')
            np.save(xy_path, [self.mapx, self.mapy])
            self.logger.info(f"已保存重映射矩陣: {xy_path}")
        
        if self.mtx is not None:
            mtx_path = os.path.join(save_dir, 'perspective_mtx.npy')
            np.save(mtx_path, self.mtx)
            self.logger.info(f"已保存透視變換矩陣: {mtx_path}")
        
        # 保存配置信息
        config = {
            'crop_params': self.crop_params,
            'perspective_params': self.perspective_params,
            'correction_info': self.get_correction_info()
        }
        
        config_path = os.path.join(save_dir, 'correction_config.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"已保存配置文件: {config_path}")


def create_fisheye_corrector(
    parameters_dir: str = "./parameters",
    mtx_dir: str = "mtx",
    xy_mtx_filename: str = "xy_mtx_12_22_pick.npy"
) -> FisheyeCorrector:
    """
    創建魚眼校正器的工廠函數
    
    參數:
        parameters_dir: 參數目錄
        mtx_dir: 矩陣子目錄
        xy_mtx_filename: XY矩陣檔名
        
    返回:
        FisheyeCorrector實例
    """
    try:
        # 構建路徑
        xy_mtx_path = os.path.join(parameters_dir, mtx_dir, xy_mtx_filename)
        
        # 載入透視變換相關參數
        # 這裡需要根據實際的參數檔案結構調整
        mtx_path = None  # 需要指定實際的透視變換矩陣路徑
        
        corrector = FisheyeCorrector(
            xy_mtx_path=xy_mtx_path,
            mtx_path=mtx_path
        )
        
        return corrector
        
    except Exception as e:
        logging.error(f"創建魚眼校正器失敗: {e}")
        raise


# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 創建校正器
        corrector = create_fisheye_corrector()
        
        # 單張圖像校正
        test_image = cv2.imread("test_fisheye.jpg")
        if test_image is not None:
            corrected = corrector.fisheye_correction(test_image, mode='standard')
            cv2.imwrite("corrected_output.jpg", corrected)
            print("魚眼校正完成")
        
        # 批次校正
        image_list = ["image1.jpg", "image2.jpg", "image3.jpg"]
        results = corrector.batch_correction(
            image_list, 
            output_dir="./corrected_images",
            mode='standard'
        )
        
        print(f"批次處理完成，共處理 {len(results)} 張圖像")
        
        # 顯示校正器信息
        info = corrector.get_correction_info()
        print(f"校正器信息: {info}")
        
    except Exception as e:
        print(f"魚眼校正失敗: {e}")