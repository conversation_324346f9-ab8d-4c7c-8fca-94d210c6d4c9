import os
import json
import numpy as np
import cv2
import math
import pandas as pd
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Union, Tuple, Optional, Any
import random
from datetime import datetime
import shutil
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec


class StandardEquirectRotate:
    """標準攝影測量OPK旋轉矩陣實現 - 支持多重旋轉組合"""

    def __init__(self, height: int, width: int):
        """
        初始化實例

        Args:
            height: 圖像高度
            width: 圖像寬度  
        """
        assert height * \
            2 == width, f"圖片比例不正確！高度:{height}, 寬度:{width}, 應為 2:1 比例"

        self.height = height
        self.width = width

        self.Interpolation_Methods = cv2.INTER_CUBIC

        # 建立輸出圖像的球面座標（這部分不依賴旋轉參數）
        out_img = np.zeros((height, width))
        self.out_xyz = self.pixel_to_sphere(out_img)

    def calculate_combined_rotation_matrix(self, rotations):
        """計算多個旋轉的合成矩陣"""
        combined_matrix = np.eye(3)

        for rotation_spec in rotations:
            # 解析不同的輸入格式
            if isinstance(rotation_spec, (tuple, list)) and len(rotation_spec) == 4:
                omega, phi, kappa, use_inverse = rotation_spec
                rotation = (omega, phi, kappa)
            elif isinstance(rotation_spec, (tuple, list)) and len(rotation_spec) == 2:
                rotation, use_inverse = rotation_spec
            elif isinstance(rotation_spec, dict):
                rotation = rotation_spec["rotation"]
                use_inverse = rotation_spec["use_inverse"]
            else:
                raise ValueError(f"不支持的旋轉格式: {rotation_spec}")

            R = self.get_standard_rotation_matrix(np.array(rotation))

            if use_inverse:
                R = R.T

            combined_matrix = combined_matrix @ R

        return combined_matrix

    def rotate(self, image, rotations):
        """執行單次或多次旋轉"""
        assert image.shape[:2] == (self.height, self.width), "圖片尺寸不匹配"

        if isinstance(rotations[0], (int, float)):
            rotations = [rotations]

        combined_matrix = self.calculate_combined_rotation_matrix(rotations)

        src_xyz = np.einsum('ijk,kl->ijl', self.out_xyz, combined_matrix.T)
        src_lonlat = self.sphere_to_lonlat(src_xyz)
        src_pixel = self.lonlat_to_pixel(src_lonlat)

        return cv2.remap(
            image,
            src_pixel[..., 1].astype(np.float32),
            src_pixel[..., 0].astype(np.float32),
            self.Interpolation_Methods,
            borderMode=cv2.BORDER_WRAP
        )

    @staticmethod
    def get_standard_rotation_matrix(rotation_deg: tuple) -> np.ndarray:
        """計算標準攝影測量旋轉矩陣"""
        omega, phi, kappa = np.radians(rotation_deg)

        cos_omega = np.cos(omega)
        sin_omega = np.sin(omega)
        cos_phi = np.cos(phi)
        sin_phi = np.sin(phi)
        cos_kappa = np.cos(kappa)
        sin_kappa = np.sin(kappa)

        return np.array([
            [cos_phi * cos_kappa,
             -cos_phi * sin_kappa,
             sin_phi],
            [cos_omega * sin_kappa + sin_omega * sin_phi * cos_kappa,
             cos_omega * cos_kappa - sin_omega * sin_phi * sin_kappa,
             -sin_omega * cos_phi],
            [sin_omega * sin_kappa - cos_omega * sin_phi * cos_kappa,
             sin_omega * cos_kappa + cos_omega * sin_phi * sin_kappa,
             cos_omega * cos_phi]
        ])

    def pixel_to_sphere(self, equirect):
        """像素座標轉球面座標"""
        h, w = equirect.shape

        lat = (0.5 - np.arange(h)[:, None] / h) * np.pi
        lon = (np.arange(w)[None, :] / w - 0.5) * 2 * np.pi

        lat = np.broadcast_to(lat, (h, w))
        lon = np.broadcast_to(lon, (h, w))

        x = np.cos(lat) * np.sin(lon)
        y = np.cos(lat) * np.cos(lon)
        z = np.sin(lat)

        return np.stack([x, y, z], axis=-1)

    def sphere_to_lonlat(self, xyz):
        """球面座標轉經緯度"""
        norm = np.linalg.norm(xyz, axis=-1, keepdims=True)
        xyz_norm = xyz / (norm + 1e-8)

        lat = np.arcsin(np.clip(xyz_norm[..., 2], -1, 1))
        lon = np.arctan2(xyz_norm[..., 0], xyz_norm[..., 1])

        return np.stack([lat, lon], axis=-1)

    def lonlat_to_pixel(self, lonlat):
        """經緯度轉像素座標"""
        lat, lon = lonlat[..., 0], lonlat[..., 1]

        pixel_y = (0.5 - lat / np.pi) * self.height
        pixel_x = (0.5 + lon / (2 * np.pi)) * self.width

        pixel_y = np.clip(pixel_y, 0, self.height - 1)
        pixel_x = pixel_x % self.width

        return np.stack([pixel_y, pixel_x], axis=-1).astype(np.int32)


class LabelMeTransformer:
    """LabelMe標籤座標轉換器"""

    def __init__(self, height: int, width: int):
        self.height = height
        self.width = width
        self.rotator = StandardEquirectRotate(height, width)

    def transform_labelme(self, labelme_data: dict, rotation_matrix: np.ndarray) -> dict:
        """
        轉換LabelMe標籤中的座標點

        Args:
            labelme_data: LabelMe格式的標籤數據
            rotation_matrix: 3x3旋轉矩陣

        Returns:
            轉換後的LabelMe數據
        """
        transformed_data = labelme_data.copy()

        for shape in transformed_data.get('shapes', []):
            if 'points' in shape:
                # 轉換每個形狀的點座標
                transformed_points = []
                for point in shape['points']:
                    x, y = point
                    # 像素座標轉球面座標
                    sphere_coord = self._pixel_to_sphere_single(x, y)
                    # 應用旋轉（注意：使用轉置矩陣以保持與圖像變換的一致性）
                    rotated_coord = rotation_matrix.T @ sphere_coord
                    # 球面座標轉回像素座標
                    new_x, new_y = self._sphere_to_pixel_single(rotated_coord)
                    transformed_points.append([new_x, new_y])

                shape['points'] = transformed_points

        return transformed_data

    def _pixel_to_sphere_single(self, x: float, y: float) -> np.ndarray:
        """單點像素座標轉球面座標"""
        lon = (x / self.width - 0.5) * 2 * np.pi
        lat = (0.5 - y / self.height) * np.pi

        sphere_x = np.cos(lat) * np.sin(lon)
        sphere_y = np.cos(lat) * np.cos(lon)
        sphere_z = np.sin(lat)
        # sphere_x = np.sin(lat) * np.sin(lon)
        # sphere_y = np.sin(lat)
        # sphere_z = np.cos(lat) * np.cos(lon)
        return np.array([sphere_x, sphere_y, sphere_z])

    def _sphere_to_pixel_single(self, xyz: np.ndarray) -> Tuple[float, float]:
        """單點球面座標轉像素座標"""
        xyz_norm = xyz / (np.linalg.norm(xyz) + 1e-8)

        lat = np.arcsin(np.clip(xyz_norm[2], -1, 1))
        lon = np.arctan2(xyz_norm[0], xyz_norm[1])

        pixel_x = (0.5 + lon / (2 * np.pi)) * self.width
        pixel_y = (0.5 - lat / np.pi) * self.height

        pixel_x = pixel_x % self.width
        pixel_y = np.clip(pixel_y, 0, self.height - 1)

        return float(pixel_x), float(pixel_y)


class PanoramaAugmenter:
    """全景圖像擴增器"""

    def __init__(self, logger=None, config=None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = config or {}
        
        # 從配置文件設置默認參數
        self.default_methods = self.config.get('panorama_methods', ['orientation'])
        self.default_num_variations = self.config.get('panorama_num_variations', 8)
        self.auto_visualize = self.config.get('panorama_auto_visualize', False)
        self.save_visualization = self.config.get('panorama_save_visualization', False)
        self.max_tilt = self.config.get('panorama_max_tilt', 20.0)
        self.angle_step = self.config.get('panorama_angle_step', 45.0)
        
    def get_default_config(self):
        """獲取默認配置"""
        return {
            "panorama_methods": ["orientation"],
            "panorama_num_variations": 8,
            "panorama_auto_visualize": False,
            "panorama_save_visualization": False,
            "panorama_max_tilt": 20.0,
            "panorama_angle_step": 45.0,
            "panorama_interactive": True
        }
    
    def validate_orientation(self, omega: float, phi: float, kappa: float) -> Tuple[float, float, float]:
        """
        驗證和標準化外方位參數
        
        Args:
            omega, phi, kappa: 外方位角度
            
        Returns:
            標準化後的角度（確保在合理範圍內）
        """
        # 檢查是否為大數值（可能是座標而非角度）
        if abs(omega) > 1000 or abs(phi) > 1000 or abs(kappa) > 1000:
            self.logger.warning(f"外方位參數值過大，可能不是角度值: omega={omega}, phi={phi}, kappa={kappa}")
            self.logger.warning("將使用零值替代，請檢查輸入參數")
            return 0.0, 0.0, 0.0
        
        # 將角度標準化到合理範圍
        omega = ((omega + 180) % 360) - 180  # -180 到 180
        phi = ((phi + 180) % 360) - 180      # -180 到 180  
        kappa = ((kappa + 180) % 360) - 180  # -180 到 180
        
        return omega, phi, kappa

    def calculate_heading(self, omega: float, phi: float) -> float:
        """計算航向角"""
        R = -math.sin(math.radians(phi))
        S = math.sin(math.radians(omega)) * math.cos(math.radians(phi))

        if R > 0 and S > 0:
            return math.degrees(math.atan(R / S))
        elif R > 0 and S == 0:
            return 90
        elif R > 0 and S < 0:
            return 180 + math.degrees(math.atan(R / S))
        elif R == 0 and S > 0:
            return 0
        elif R == 0 and S == 0:
            return 0
        elif R == 0 and S < 0:
            return 180
        elif R < 0 and S > 0:
            return 360 + math.degrees(math.atan(R / S))
        elif R < 0 and S == 0:
            return 270
        elif R < 0 and S < 0:
            return 180 + math.degrees(math.atan(R / S))
        else:
            return 0

    def augment_orientation_based(self, image: np.ndarray, labelme_data: dict,
                                omega: float, phi: float, kappa: float,
                                num_variations: int = 8) -> List[Tuple[np.ndarray, dict]]:
        """
        基於外方位的擴增方法 - 先校正相機姿態，再生成不同視角
        
        Args:
            image: 輸入圖像
            labelme_data: 標籤數據
            omega, phi, kappa: 外方位角度
            num_variations: 生成變化數量
            
        Returns:
            擴增結果列表 [(圖像, 標籤), ...]
        """
        # 驗證外方位參數
        omega, phi, kappa = self.validate_orientation(omega, phi, kappa)
        
        h, w = image.shape[:2]
        rotator = StandardEquirectRotate(h, w)
        label_transformer = LabelMeTransformer(h, w)
        
        results = []
        
        # 基準旋轉：校正相機姿態
        base_rotation = (omega, phi, kappa)
        heading = self.calculate_heading(omega, phi)
        north_alignment = (90, -heading, 0)
        
        # 基本校正（0度參考視角）
        base_sequence = [
            [base_rotation, True],      # 校正相機姿態
            [north_alignment, False]    # 對齊北向
        ]
        
        # 應用基本校正
        base_corrected = rotator.rotate(image, base_sequence)
        base_matrix = rotator.calculate_combined_rotation_matrix(base_sequence)
        base_labelme = label_transformer.transform_labelme(labelme_data, base_matrix)
        results.append((base_corrected, base_labelme))
        
        # 生成不同視角的變化
        angle_step = 360 / num_variations
        for i in range(1, num_variations):
            view_angle = i * angle_step
            
            # 組合旋轉序列：校正 + 對齊 + 視角變換
            rotation_sequence = base_sequence + [[(0, 0, view_angle), False]]
            
            # 應用旋轉
            aug_image = rotator.rotate(image, rotation_sequence)
            
            # 轉換標籤
            combined_matrix = rotator.calculate_combined_rotation_matrix(rotation_sequence)
            aug_labelme = label_transformer.transform_labelme(labelme_data, combined_matrix)
            
            results.append((aug_image, aug_labelme))
        
        return results

    def augment_rotation(self, image: np.ndarray, labelme_data: dict,
                         omega: float, phi: float, kappa: float,
                         num_variations: int = 5) -> List[Tuple[np.ndarray, dict]]:
        """
        旋轉擴增方法

        Args:
            image: 輸入圖像
            labelme_data: 標籤數據
            omega, phi, kappa: 外方位角度
            num_variations: 生成變化數量

        Returns:
            擴增結果列表 [(圖像, 標籤), ...]
        """
        h, w = image.shape[:2]
        rotator = StandardEquirectRotate(h, w)
        label_transformer = LabelMeTransformer(h, w)

        results = []

        # 驗證外方位參數
        omega, phi, kappa = self.validate_orientation(omega, phi, kappa)
        
        # 基準旋轉：校正相機姿態
        base_rotation = (omega, phi, kappa)
        heading = self.calculate_heading(omega, phi)
        north_alignment = (90, -heading, 0)

        # 生成隨機旋轉變化
        for i in range(num_variations):
            # 添加隨機擾動
            random_kappa = random.uniform(-30, 30)  # 隨機偏航
            random_tilt = random.uniform(-15, 15)   # 隨機傾斜

            augment_rotation = (random_tilt, 0, random_kappa)

            # 組合旋轉序列
            rotation_sequence = [
                [base_rotation, True],      # 校正相機姿態
                [north_alignment, False],   # 對齊北向
                [augment_rotation, False]   # 擴增旋轉
            ]

            # 應用圖像旋轉
            aug_image = rotator.rotate(image, rotation_sequence)

            # 計算合成旋轉矩陣用於標籤轉換
            combined_matrix = rotator.calculate_combined_rotation_matrix(
                rotation_sequence)
            aug_labelme = label_transformer.transform_labelme(
                labelme_data, combined_matrix)

            results.append((aug_image, aug_labelme))

        return results

    def augment_perspective(self, image: np.ndarray, labelme_data: dict,
                            num_variations: int = 5) -> List[Tuple[np.ndarray, dict]]:
        """
        視角變換擴增方法

        Args:
            image: 輸入圖像
            labelme_data: 標籤數據
            num_variations: 生成變化數量

        Returns:
            擴增結果列表
        """
        h, w = image.shape[:2]
        rotator = StandardEquirectRotate(h, w)
        label_transformer = LabelMeTransformer(h, w)

        results = []

        # 生成不同視角
        for i in range(num_variations):
            # 隨機視角參數
            view_omega = random.uniform(-45, 45)    # 俯仰角
            view_phi = random.uniform(-45, 45)      # 橫滾角
            view_kappa = random.uniform(0, 360)     # 方位角

            view_rotation = (view_omega, view_phi, view_kappa)

            # 應用視角變換
            aug_image = rotator.rotate(image, [[view_rotation, False]])

            # 轉換標籤
            rotation_matrix = rotator.get_standard_rotation_matrix(
                np.array(view_rotation))
            aug_labelme = label_transformer.transform_labelme(
                labelme_data, rotation_matrix)

            results.append((aug_image, aug_labelme))

        return results

    def augment_tilt(self, image: np.ndarray, labelme_data: dict,
                     max_tilt: float = 20.0, num_variations: int = 5) -> List[Tuple[np.ndarray, dict]]:
        """
        傾斜擴增方法

        Args:
            image: 輸入圖像
            labelme_data: 標籤數據
            max_tilt: 最大傾斜角度
            num_variations: 生成變化數量

        Returns:
            擴增結果列表
        """
        h, w = image.shape[:2]
        rotator = StandardEquirectRotate(h, w)
        label_transformer = LabelMeTransformer(h, w)

        results = []

        for i in range(num_variations):
            # 隨機傾斜參數
            tilt_omega = random.uniform(-max_tilt, max_tilt)
            tilt_phi = random.uniform(-max_tilt, max_tilt)
            tilt_rotation = (tilt_omega, tilt_phi, 0)

            # 應用傾斜
            aug_image = rotator.rotate(image, [[tilt_rotation, False]])

            # 轉換標籤
            rotation_matrix = rotator.get_standard_rotation_matrix(
                np.array(tilt_rotation))
            aug_labelme = label_transformer.transform_labelme(
                labelme_data, rotation_matrix)

            results.append((aug_image, aug_labelme))

        return results

    def augment_multi_angle(self, image: np.ndarray, labelme_data: dict,
                            angle_step: float = 45.0) -> List[Tuple[np.ndarray, dict]]:
        """
        多角度擴增方法

        Args:
            image: 輸入圖像
            labelme_data: 標籤數據
            angle_step: 角度步長

        Returns:
            擴增結果列表
        """
        h, w = image.shape[:2]
        rotator = StandardEquirectRotate(h, w)
        label_transformer = LabelMeTransformer(h, w)

        results = []

        # 生成等間隔角度變化
        num_angles = int(360 / angle_step)
        for i in range(num_angles):
            angle = i * angle_step
            rotation = (0, 0, angle)

            # 應用旋轉
            aug_image = rotator.rotate(image, [[rotation, False]])

            # 轉換標籤
            rotation_matrix = rotator.get_standard_rotation_matrix(
                np.array(rotation))
            aug_labelme = label_transformer.transform_labelme(
                labelme_data, rotation_matrix)

            results.append((aug_image, aug_labelme))

        return results

    def visualize_results(self, original_image: np.ndarray, results: Dict, 
                         method_name: str = None, save_path: str = None) -> None:
        """
        可視化擴增結果
        
        Args:
            original_image: 原始圖像
            results: 擴增結果字典
            method_name: 指定要顯示的方法，None表示顯示所有
            save_path: 保存路徑，None表示只顯示不保存
        """
        # 設置中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 決定要顯示的方法
        methods_to_show = [method_name] if method_name and method_name in results else list(results.keys())
        
        for method in methods_to_show:
            if method not in results:
                continue
                
            aug_results = results[method]
            num_results = len(aug_results)
            
            if num_results == 0:
                continue
            
            # 計算網格大小
            cols = min(4, num_results + 1)  # +1 for original
            rows = (num_results + cols) // cols
            
            # 創建圖形
            fig = plt.figure(figsize=(4*cols, 3*rows))
            gs = gridspec.GridSpec(rows, cols, figure=fig)
            
            # 顯示原始圖像
            ax_orig = fig.add_subplot(gs[0, 0])
            ax_orig.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
            ax_orig.set_title('原始圖像', fontsize=12, fontweight='bold')
            ax_orig.axis('off')
            
            # 顯示擴增結果
            for i, (aug_img, aug_label) in enumerate(aug_results):
                row = (i + 1) // cols
                col = (i + 1) % cols
                
                ax = fig.add_subplot(gs[row, col])
                ax.imshow(cv2.cvtColor(aug_img, cv2.COLOR_BGR2RGB))
                ax.set_title(f'{method} #{i+1}', fontsize=10)
                ax.axis('off')
            
            plt.suptitle(f'全景圖像擴增結果 - {method}', fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # 保存或顯示
            if save_path:
                vis_path = f"{save_path}_{method}_visualization.png"
                plt.savefig(vis_path, dpi=150, bbox_inches='tight')
                self.logger.info(f"可視化結果已保存: {vis_path}")
            
            plt.show()

    def create_comparison_view(self, original_image: np.ndarray, augmented_image: np.ndarray,
                             title: str = "擴增對比", save_path: str = None) -> None:
        """
        創建原始圖像和擴增圖像的對比視圖
        
        Args:
            original_image: 原始圖像
            augmented_image: 擴增後圖像
            title: 圖像標題
            save_path: 保存路徑
        """
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 原始圖像
        ax1.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        ax1.set_title('原始圖像', fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        # 擴增圖像
        ax2.imshow(cv2.cvtColor(augmented_image, cv2.COLOR_BGR2RGB))
        ax2.set_title('擴增圖像', fontsize=14, fontweight='bold')
        ax2.axis('off')
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            self.logger.info(f"對比圖已保存: {save_path}")
        
        plt.show()

    def process_single_image(self, image_path: str, label_path: str = None,
                             orientation: Tuple[float, float, float] = None,
                             methods: List[str] = None, output_dir: str = None,
                             visualize: bool = False, save_visualization: bool = False) -> Dict:
        """
        處理單張圖像

        Args:
            image_path: 圖像路徑
            label_path: 標籤路徑（可選）
            orientation: 外方位 (omega, phi, kappa)
            methods: 擴增方法列表
            output_dir: 輸出目錄
            visualize: 是否顯示可視化結果
            save_visualization: 是否保存可視化圖像

        Returns:
            處理結果
        """
        if methods is None:
            methods = ['orientation'] if orientation else ['perspective', 'tilt']

        # 讀取圖像
        image = self._cv_imread(image_path)
        if image is None:
            raise ValueError(f"無法讀取圖像: {image_path}")

        # 讀取標籤（如果沒有標籤文件，創建空的標籤結構）
        labelme_data = {
            "version": "5.0.1",
            "flags": {},
            "shapes": [],
            "imagePath": os.path.basename(image_path),
            "imageData": None,
            "imageHeight": image.shape[0],
            "imageWidth": image.shape[1]
        }
        
        if label_path and os.path.exists(label_path):
            with open(label_path, 'r', encoding='utf-8') as f:
                labelme_data = json.load(f)

        results = {}

        # 應用不同擴增方法
        if 'orientation' in methods and orientation:
            omega, phi, kappa = orientation
            results['orientation'] = self.augment_orientation_based(
                image, labelme_data, omega, phi, kappa)
        
        if 'rotation' in methods and orientation:
            omega, phi, kappa = orientation
            results['rotation'] = self.augment_rotation(
                image, labelme_data, omega, phi, kappa)

        if 'perspective' in methods:
            results['perspective'] = self.augment_perspective(
                image, labelme_data)

        if 'tilt' in methods:
            results['tilt'] = self.augment_tilt(image, labelme_data)

        if 'multi_angle' in methods:
            results['multi_angle'] = self.augment_multi_angle(
                image, labelme_data)

        # 保存結果
        if output_dir:
            self._save_results(results, image_path, label_path, output_dir)

        # 可視化結果
        if visualize and results:
            vis_save_path = None
            if save_visualization and output_dir:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                vis_save_path = os.path.join(output_dir, f"{base_name}_visualization")
            
            self.visualize_results(image, results, save_path=vis_save_path)

        return results

    def process_batch(self, input_dir: str, output_dir: str, excel_path: str = None,
                      methods: List[str] = None) -> Dict:
        """
        批量處理

        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            excel_path: Excel文件路徑（包含外方位信息）
            methods: 擴增方法列表

        Returns:
            處理統計
        """
        os.makedirs(output_dir, exist_ok=True)

        # 讀取Excel外方位數據
        orientation_data = {}
        if excel_path and os.path.exists(excel_path):
            df = pd.read_excel(excel_path)
            for _, row in df.iterrows():
                image_name = row.get('image_name', row.get('filename', ''))
                omega = row.get('omega', 0)
                phi = row.get('phi', 0)
                kappa = row.get('kappa', 0)
                orientation_data[image_name] = (omega, phi, kappa)

        # 處理每張圖像
        processed_count = 0
        total_augmented = 0

        for filename in os.listdir(input_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_path = os.path.join(input_dir, filename)

                # 尋找對應的標籤文件
                label_name = os.path.splitext(filename)[0] + '.json'
                label_path = os.path.join(input_dir, label_name)

                # 獲取外方位
                orientation = orientation_data.get(filename, None)

                try:
                    results = self.process_single_image(
                        image_path, label_path, orientation, methods, output_dir)

                    processed_count += 1
                    for method, aug_results in results.items():
                        total_augmented += len(aug_results)

                    self.logger.info(f"已處理: {filename}")

                except Exception as e:
                    self.logger.error(f"處理 {filename} 時發生錯誤: {e}")

        return {
            'processed_images': processed_count,
            'total_augmented': total_augmented
        }

    def _cv_imread(self, file_path: str) -> np.ndarray:
        """處理中文路徑的圖片讀取"""
        try:
            cv_img = cv2.imdecode(np.fromfile(file_path, dtype=np.uint8), -1)
            return cv_img
        except Exception as e:
            self.logger.error(f"讀取圖片錯誤: {e}")
            return None

    def _save_results(self, results: Dict, image_path: str, label_path: str, output_dir: str):
        """保存擴增結果"""
        base_name = os.path.splitext(os.path.basename(image_path))[0]

        for method, aug_results in results.items():
            method_dir = os.path.join(output_dir, method)
            os.makedirs(method_dir, exist_ok=True)

            for i, (aug_image, aug_label) in enumerate(aug_results):
                # 保存圖像
                img_filename = f"{base_name}_{method}_{i:03d}.jpg"
                img_path = os.path.join(method_dir, img_filename)
                cv2.imwrite(img_path, aug_image)

                # 保存標籤（即使沒有標註形狀也保存標籤文件）
                if aug_label:
                    label_filename = f"{base_name}_{method}_{i:03d}.json"
                    label_save_path = os.path.join(method_dir, label_filename)

                    # 更新標籤中的圖像路徑
                    aug_label['imagePath'] = img_filename

                    with open(label_save_path, 'w', encoding='utf-8') as f:
                        json.dump(aug_label, f, ensure_ascii=False, indent=2)


def setup_logging(log_path: str = None, level=logging.INFO):
    """設置日誌記錄"""
    if log_path is None:
        log_path = f"panorama_augment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_path, mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ],
        force=True
    )
    return logging.getLogger("PanoramaAugmenter")


def main():
    parser = argparse.ArgumentParser(description="全景圖像擴增工具")

    # 基本參數
    parser.add_argument('--input', type=str, help='輸入圖像路徑或目錄')
    parser.add_argument('--output', type=str, required=True, help='輸出目錄')
    parser.add_argument('--label', type=str, help='標籤文件路徑（單張處理時）')
    parser.add_argument('--excel', type=str, help='包含外方位信息的Excel文件')

    # 外方位參數（單張處理時使用）
    parser.add_argument('--omega', type=float, help='俯仰角（度）')
    parser.add_argument('--phi', type=float, help='橫滾角（度）')
    parser.add_argument('--kappa', type=float, help='偏航角（度）')

    # 擴增參數
    parser.add_argument('--methods', nargs='+',
                        choices=['orientation', 'rotation', 'perspective',
                                 'tilt', 'multi_angle'],
                        default=['orientation'],
                        help='擴增方法。orientation: 基於外方位的視角擴增（推薦）')
    parser.add_argument('--num-variations', type=int, default=5,
                        help='每種方法生成的變化數量')

    # 其他參數
    parser.add_argument('--batch', action='store_true', help='批量處理模式')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    parser.add_argument('--visualize', action='store_true', help='顯示可視化結果')
    parser.add_argument('--save-visualization', action='store_true', help='保存可視化圖像')
    parser.add_argument('--log', type=str, help='日誌文件路徑')

    args = parser.parse_args()

    # 設置日誌
    logger = setup_logging(args.log)

    # 創建擴增器
    augmenter = PanoramaAugmenter(logger)

    try:
        if args.interactive:
            # 交互式模式
            print("=== 全景圖像擴增工具 ===")
            print("請選擇處理模式：")
            print("1. 單張處理")
            print("2. 批量處理")

            mode = input("請輸入選項 (1-2): ").strip()

            if mode == '1':
                # 單張處理交互模式
                image_path = input("請輸入圖像路徑: ").strip()
                label_path = input("請輸入標籤路徑（可選，直接回車跳過）: ").strip() or None

                # 外方位輸入
                print("請輸入外方位參數（可選）：")
                try:
                    omega = float(input("俯仰角 omega（度）: ") or "0")
                    phi = float(input("橫滾角 phi（度）: ") or "0")
                    kappa = float(input("偏航角 kappa（度）: ") or "0")
                    orientation = (omega, phi, kappa)
                except:
                    orientation = None

                # 選擇擴增方法
                print("可用的擴增方法：")
                print("  orientation - 基於外方位的視角擴增（推薦，需要外方位參數）")
                print("  rotation - 隨機旋轉擴增（需要外方位參數）")
                print("  perspective - 隨機視角變換")
                print("  tilt - 傾斜擴增")
                print("  multi_angle - 多角度擴增")
                methods_input = input("請輸入要使用的方法（用空格分隔，直接回車使用orientation）: ").strip()
                methods = methods_input.split() if methods_input else ['orientation']
                
                # 詢問是否顯示可視化
                show_viz = input("是否顯示可視化結果？(y/N): ").strip().lower()
                visualize = show_viz in ['y', 'yes', '是']
                
                save_viz = False
                if visualize:
                    save_viz_input = input("是否保存可視化圖像？(y/N): ").strip().lower()
                    save_viz = save_viz_input in ['y', 'yes', '是']

                results = augmenter.process_single_image(
                    image_path, label_path, orientation, methods, args.output,
                    visualize=visualize, save_visualization=save_viz)

                print(f"處理完成，結果保存在: {args.output}")

            elif mode == '2':
                # 批量處理交互模式
                input_dir = input("請輸入輸入目錄: ").strip()
                excel_path = input("請輸入Excel文件路徑（可選）: ").strip() or None

                stats = augmenter.process_batch(
                    input_dir, args.output, excel_path, args.methods)
                print(f"批量處理完成：")
                print(f"  處理圖像數: {stats['processed_images']}")
                print(f"  生成擴增圖像數: {stats['total_augmented']}")

        else:
            # 命令行模式
            if args.batch or os.path.isdir(args.input):
                # 批量處理
                orientation = None
                if args.omega is not None and args.phi is not None and args.kappa is not None:
                    orientation = (args.omega, args.phi, args.kappa)

                stats = augmenter.process_batch(
                    args.input, args.output, args.excel, args.methods)
                logger.info(f"批量處理完成：處理 {stats['processed_images']} 張圖像，"
                            f"生成 {stats['total_augmented']} 張擴增圖像")

            else:
                # 單張處理
                orientation = None
                if args.omega is not None and args.phi is not None and args.kappa is not None:
                    orientation = (args.omega, args.phi, args.kappa)

                results = augmenter.process_single_image(
                    args.input, args.label, orientation, args.methods, args.output,
                    visualize=args.visualize, save_visualization=args.save_visualization)

                total_generated = sum(len(aug_list)
                                      for aug_list in results.values())
                logger.info(f"單張處理完成，生成 {total_generated} 張擴增圖像")

    except Exception as e:
        logger.error(f"處理過程中發生錯誤: {e}")
        raise


if __name__ == "__main__":
    main()
