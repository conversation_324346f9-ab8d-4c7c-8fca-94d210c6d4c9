#!/usr/bin/env python3
"""
簡化YOLO推理系統配置模組
"""

from .yolo_config import (
    SimplifiedYOLOConfig,
    ClassConfig,
    CLASS_NAMES,
    LABEL_ALIASES,
    set_global_class_names,
    set_global_label_aliases,
    get_default_config,
    create_detection_config,
    create_segmentation_config,
    create_sahi_config
)

from .class_detector import (
    auto_detect_classes_from_model,
    scan_labelme_directory,
    create_auto_class_configs,
    apply_label_mapping,
    validate_class_configs,
    print_class_summary
)

__all__ = [
    # 配置類
    'SimplifiedYOLOConfig',
    'ClassConfig',
    
    # 全域常數
    'CLASS_NAMES',
    'LABEL_ALIASES',
    
    # 配置函數
    'set_global_class_names',
    'set_global_label_aliases',
    'get_default_config',
    'create_detection_config',
    'create_segmentation_config',
    'create_sahi_config',
    
    # 類別檢測函數
    'auto_detect_classes_from_model',
    'scan_labelme_directory',
    'create_auto_class_configs',
    'apply_label_mapping',
    'validate_class_configs',
    'print_class_summary'
]