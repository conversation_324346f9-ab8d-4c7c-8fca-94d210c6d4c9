#!/usr/bin/env python3
"""
圖像數據集處理工具集 PyQt GUI 應用程式
支援明亮/黑暗模式切換的現代化界面
"""

import sys
import os
import json
import logging
import threading
import traceback
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, List

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QPushButton, QMenuBar, QMenu, QStatusBar,
    QTextEdit, QProgressBar, QGroupBox, QFormLayout, QLineEdit,
    QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox, QFileDialog,
    QMessageBox, QDialog, QDialogButtonBox, QFrame, QScrollArea,
    QSplitter, QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QHeaderView, QSlider, QGridLayout, QButtonGroup, QRadioButton, QListWidget,
    QAbstractItemView
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSettings, QUrl, QSize,
    QPropertyAnimation, QEasingCurve, QRect, QEvent, QRectF, QPointF
)
from PyQt6.QtGui import (
    QIcon, QPixmap, QFont, QColor, QPalette, QAction, QDesktopServices,
    QPainter, QBrush, QLinearGradient, QFontMetrics, QMovie, QPolygonF, QPen
)

try:
    import qdarkstyle
    QDARKSTYLE_AVAILABLE = True
except ImportError:
    QDARKSTYLE_AVAILABLE = False

# 導入各個工具模組
try:
    from annotation_converter_compat import AnnotationConverter  # 使用兼容層
    from annotation_editor import AnnotationEditor
    from dataset_divider import DatasetDivider
    from img_augmenter import ImageAugmenter
    from panorama_augmenter import PanoramaAugmenter
except ImportError as e:
    print(f"警告：無法導入某些模組: {e}")
    AnnotationConverter = None  # 設置為None以避免後續錯誤


class LogHandler(logging.Handler):
    """自定義日誌處理器，將日誌輸出到GUI"""

    def __init__(self, log_widget):
        super().__init__()
        self.log_widget = log_widget

    def emit(self, record):
        msg = self.format(record)
        # 使用QTimer確保在主線程中更新UI
        QTimer.singleShot(0, lambda: self._append_log(msg))

    def _append_log(self, msg):
        self.log_widget.append(msg)
        self.log_widget.ensureCursorVisible()


class WorkerThread(QThread):
    """工作線程基類"""
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, tool_class, params):
        super().__init__()
        self.tool_class = tool_class
        self.params = params

    def run(self):
        try:
            self.log.emit(f"開始執行 {self.tool_class.__name__}...")

            # 創建工具實例並執行
            tool_instance = self.tool_class(**self.params)
            result = self._execute_tool(tool_instance)

            self.finished.emit(result or {})
            self.log.emit(f"{self.tool_class.__name__} 執行完成")

        except Exception as e:
            error_msg = f"執行過程中發生錯誤: {str(e)}"
            self.error.emit(error_msg)
            self.log.emit(error_msg)
            self.log.emit(f"詳細錯誤信息: {traceback.format_exc()}")

    def _execute_tool(self, tool_instance):
        """子類需要實現的具體執行邏輯"""
        raise NotImplementedError


class ThemeManager:
    """主題管理器"""

    def __init__(self, app: QApplication):
        self.app = app
        self.is_dark_mode = True  # 設置初始為黑暗模式

    def toggle_theme(self):
        """切換主題"""
        self.is_dark_mode = not self.is_dark_mode
        self.apply_theme()

    def apply_theme(self):
        """應用主題"""
        if self.is_dark_mode and QDARKSTYLE_AVAILABLE:
            self.app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
        else:
            self.app.setStyleSheet(self._get_light_theme())

    def _get_light_theme(self):
        """獲取明亮主題樣式"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
            color: #333333;
        }
        
        QWidget {
            color: #333333;
            background-color: #f5f5f5;
        }
        
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
            border-radius: 5px;
        }
        
        QTabWidget::tab-bar {
            left: 5px;
        }
        
        QTabBar::tab {
            background-color: #e1e1e1;
            color: #333333;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            color: #333333;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #e8e8e8;
            color: #333333;
        }
        
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
            color: #333333;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #333333;
        }
        
        QLabel {
            color: #333333;
        }
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
            border: 1px solid #cccccc;
            border-radius: 3px;
            padding: 4px;
            background-color: white;
            color: #333333;
        }
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
            border-color: #0078d4;
        }
        
        QTextEdit {
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: white;
            color: #333333;
        }
        
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 3px;
            text-align: center;
            color: #333333;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        
        QCheckBox {
            color: #333333;
        }
        
        QTreeWidget {
            border: 1px solid #cccccc;
            background-color: white;
            color: #333333;
        }
        
        QTableWidget {
            border: 1px solid #cccccc;
            background-color: white;
            color: #333333;
        }
        
        QListWidget {
            border: 1px solid #cccccc;
            background-color: white;
            color: #333333;
        }
        """


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file="pyqt_gui_config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """載入配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"載入配置失敗: {e}")
                return self.get_default_config()
        else:
            return self.get_default_config()

    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失敗: {e}")

    def get_default_config(self) -> Dict[str, Any]:
        """獲取默認配置"""
        return {
            "window_geometry": {"width": 1400, "height": 900, "x": 100, "y": 100},
            "theme": "light",
            "last_input_dir": "",
            "last_output_dir": "",
            "log_level": "INFO",

            # 標籤轉換器配置
            "converter": {
                "resize": 1.0,
                "quality": 75,
                "format": "auto",
                "max_workers": os.cpu_count()
            },

            # 標籤編輯器配置
            "editor": {
                "interactive": True,
                "recursive": True
            },

            # 數據集分割器配置
            "divider": {
                "train_ratio": 0.7,
                "val_ratio": 0.15,
                "test_ratio": 0.15,
                "max_workers": os.cpu_count()
            },

            # 圖像增強器配置
            "augmenter": {
                "num_generations": 200,
                "task_type": "both",
                "resize": None,
                "quality": 75
            },

            # 全景增強器配置
            "panorama": {
                "methods": ["orientation"],
                "num_variations": 8,
                "max_tilt": 20.0,
                "angle_step": 45.0,
                "auto_visualize": False,
                "save_visualization": False
            }
        }


class BaseToolWidget(QWidget):
    """工具界面基類"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = None
        self.worker_thread = None
        self.setup_ui()

    def set_main_window(self, main_window):
        """設置主窗口引用"""
        self.main_window = main_window

    def setup_ui(self):
        """設置UI，子類需要實現"""
        layout = QVBoxLayout(self)

        # 工具標題
        title = self.get_tool_title()
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 滾動區域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 內容窗口部件
        content_widget = QWidget()
        self.content_layout = QVBoxLayout(content_widget)

        # 子類實現具體內容
        self.create_content()

        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

        # 操作按鈕區域
        self.create_action_buttons(layout)

    def get_tool_title(self) -> str:
        """獲取工具標題，子類需要實現"""
        return "工具名稱"

    def create_content(self):
        """創建內容區域，子類需要實現"""
        pass

    def create_action_buttons(self, parent_layout):
        """創建操作按鈕"""
        button_layout = QHBoxLayout()

        # 執行按鈕
        self.run_button = QPushButton("開始處理")
        self.run_button.setMinimumHeight(40)
        self.run_button.clicked.connect(self.run_tool)
        button_layout.addWidget(self.run_button)

        # 重置按鈕
        reset_button = QPushButton("重置設置")
        reset_button.setMinimumHeight(40)
        reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(reset_button)

        # 保存配置按鈕
        save_button = QPushButton("保存配置")
        save_button.setMinimumHeight(40)
        save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(save_button)

        button_layout.addStretch()
        parent_layout.addLayout(button_layout)

    def validate_inputs(self) -> bool:
        """驗證輸入，子類需要實現"""
        return True

    def get_tool_params(self) -> Dict[str, Any]:
        """獲取工具參數，子類需要實現"""
        return {}

    def run_tool(self):
        """運行工具"""
        if not self.validate_inputs():
            return

        # 禁用運行按鈕
        self.run_button.setEnabled(False)
        self.run_button.setText("處理中...")

        # 獲取參數並啟動工作線程
        params = self.get_tool_params()
        self.start_worker_thread(params)

    def start_worker_thread(self, params):
        """啟動工作線程，子類需要實現"""
        pass

    def on_worker_finished(self, result):
        """工作線程完成回調"""
        self.run_button.setEnabled(True)
        self.run_button.setText("開始處理")

        if self.main_window:
            self.main_window.log("處理完成！")

    def on_worker_error(self, error_msg):
        """工作線程錯誤回調"""
        self.run_button.setEnabled(True)
        self.run_button.setText("開始處理")

        QMessageBox.critical(self, "錯誤", error_msg)

    def reset_settings(self):
        """重置設置，子類需要實現"""
        pass

    def save_settings(self):
        """保存設置，子類需要實現"""
        if self.main_window:
            self.main_window.config_manager.save_config()
            QMessageBox.information(self, "成功", "配置已保存")

    def create_file_input_group(self, title: str, file_var_name: str,
                                file_filter: str = "所有文件 (*.*)") -> QGroupBox:
        """創建文件輸入組"""
        group = QGroupBox(title)
        layout = QFormLayout(group)

        # 文件路徑輸入
        line_edit = QLineEdit()
        setattr(self, file_var_name, line_edit)

        # 瀏覽按鈕
        browse_button = QPushButton("瀏覽")
        browse_button.clicked.connect(
            lambda: self.browse_file(line_edit, file_filter)
        )

        # 水平布局
        file_layout = QHBoxLayout()
        file_layout.addWidget(line_edit)
        file_layout.addWidget(browse_button)

        layout.addRow("文件路徑:", file_layout)
        return group

    def create_directory_input_group(self, title: str, dir_var_name: str) -> QGroupBox:
        """創建目錄輸入組"""
        group = QGroupBox(title)
        layout = QFormLayout(group)

        # 目錄路徑輸入
        line_edit = QLineEdit()
        setattr(self, dir_var_name, line_edit)

        # 瀏覽按鈕
        browse_button = QPushButton("瀏覽")
        browse_button.clicked.connect(
            lambda: self.browse_directory(line_edit)
        )

        # 水平布局
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(line_edit)
        dir_layout.addWidget(browse_button)

        layout.addRow("目錄路徑:", dir_layout)
        return group

    def browse_file(self, line_edit: QLineEdit, file_filter: str):
        """瀏覽文件"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "選擇文件", line_edit.text(), file_filter
        )
        if filename:
            line_edit.setText(filename)

    def browse_directory(self, line_edit: QLineEdit):
        """瀏覽目錄"""
        dirname = QFileDialog.getExistingDirectory(
            self, "選擇目錄", line_edit.text()
        )
        if dirname:
            line_edit.setText(dirname)


class ConverterWorkerThread(WorkerThread):
    """標籤轉換器工作線程"""

    def __init__(self, tool_class, params):
        super().__init__(tool_class, params)

    def _execute_tool(self, tool_instance):
        """執行標籤轉換"""
        try:
            # 獲取參數
            input_dirs = self.params.get('input_dirs', [])
            output_dir = self.params.get('output_dir')

            if not input_dirs or not output_dir:
                raise ValueError("缺少必要的輸入或輸出目錄")

            # 執行轉換
            stats = tool_instance.process_multiple_inputs(
                input_paths=input_dirs,
                output_path=output_dir,
                resize=self.params.get('resize', 1.0),
                quality=self.params.get('quality', 75)
            )

            return {
                'processed': stats.get('total_success', 0),
                'total': stats.get('total_processed', 0),
                'skipped': stats.get('total_skipped', 0)
            }

        except Exception as e:
            self.error.emit(str(e))
            return None

    def run(self):
        """重寫run方法以正確創建工具實例"""
        try:
            self.log.emit(f"開始執行 {self.tool_class.__name__}...")

            # 創建AnnotationConverter實例
            tool_instance = self.tool_class(
                logger=self.params.get('logger'),
                max_workers=self.params.get('max_workers', os.cpu_count())
            )

            result = self._execute_tool(tool_instance)

            self.finished.emit(result or {})
            self.log.emit(f"{self.tool_class.__name__} 執行完成")

        except Exception as e:
            error_msg = f"執行過程中發生錯誤: {str(e)}"
            self.error.emit(error_msg)
            self.log.emit(error_msg)


class AnnotationConverterWidget(BaseToolWidget):
    """標籤格式轉換器界面"""

    def get_tool_title(self) -> str:
        return "標籤格式轉換器"

    def create_content(self):
        """創建內容區域"""
        # 輸入設置組
        input_group = QGroupBox("輸入設置")
        input_layout = QFormLayout(input_group)

        # 輸入目錄列表
        self.input_dirs_text = QTextEdit()
        self.input_dirs_text.setMaximumHeight(80)
        self.input_dirs_text.setPlaceholderText("每行一個目錄路徑，或使用下方的添加按鈕")
        input_layout.addRow("輸入目錄:", self.input_dirs_text)

        # 添加目錄按鈕
        add_dir_layout = QHBoxLayout()
        add_dir_button = QPushButton("添加目錄")
        add_dir_button.clicked.connect(self.add_input_directory)
        clear_dir_button = QPushButton("清除列表")
        clear_dir_button.clicked.connect(self.clear_input_directories)
        add_dir_layout.addWidget(add_dir_button)
        add_dir_layout.addWidget(clear_dir_button)
        add_dir_layout.addStretch()
        input_layout.addRow("", add_dir_layout)

        # 源格式選擇
        self.source_format_combo = QComboBox()
        self.source_format_combo.addItems([
            "auto - 自動檢測",
            "labelme - LabelMe格式",
            "yolo - YOLO格式",
            "voc - VOC格式",
            "coco - COCO格式"
        ])
        input_layout.addRow("源格式:", self.source_format_combo)

        self.content_layout.addWidget(input_group)

        # 輸出設置組
        output_group = QGroupBox("輸出設置")
        output_layout = QFormLayout(output_group)

        # 輸出目錄
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        browse_output_button = QPushButton("瀏覽")
        browse_output_button.clicked.connect(
            lambda: self.browse_directory(self.output_dir_edit)
        )
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(browse_output_button)
        output_layout.addRow("輸出目錄:", output_dir_layout)

        # 目標形狀類型
        self.target_shape_combo = QComboBox()
        self.target_shape_combo.addItems(["polygon", "rectangle"])
        output_layout.addRow("目標形狀:", self.target_shape_combo)

        self.content_layout.addWidget(output_group)

        # 處理設置組
        process_group = QGroupBox("處理設置")
        process_layout = QFormLayout(process_group)

        # 圖像縮放比例
        self.resize_spinbox = QDoubleSpinBox()
        self.resize_spinbox.setRange(0.1, 5.0)
        self.resize_spinbox.setSingleStep(0.1)
        self.resize_spinbox.setValue(1.0)
        self.resize_spinbox.setDecimals(1)
        process_layout.addRow("縮放比例:", self.resize_spinbox)

        # 圖像品質
        self.quality_spinbox = QSpinBox()
        self.quality_spinbox.setRange(1, 100)
        self.quality_spinbox.setValue(75)
        process_layout.addRow("圖像品質:", self.quality_spinbox)

        # 最大並行工作數
        self.max_workers_spinbox = QSpinBox()
        self.max_workers_spinbox.setRange(1, 32)
        self.max_workers_spinbox.setValue(os.cpu_count())
        process_layout.addRow("並行工作數:", self.max_workers_spinbox)

        self.content_layout.addWidget(process_group)

        # 載入配置
        self.load_widget_config()

    def add_input_directory(self):
        """添加輸入目錄"""
        directory = QFileDialog.getExistingDirectory(self, "選擇輸入目錄")
        if directory:
            current_text = self.input_dirs_text.toPlainText()
            if current_text:
                self.input_dirs_text.setPlainText(
                    current_text + "\n" + directory)
            else:
                self.input_dirs_text.setPlainText(directory)

    def clear_input_directories(self):
        """清除輸入目錄列表"""
        self.input_dirs_text.clear()

    def validate_inputs(self) -> bool:
        """驗證輸入"""
        # 檢查輸入目錄
        input_dirs = self.get_input_directories()
        if not input_dirs:
            QMessageBox.warning(self, "警告", "請至少添加一個輸入目錄")
            return False

        # 檢查目錄是否存在
        for dir_path in input_dirs:
            if not os.path.exists(dir_path):
                QMessageBox.warning(self, "警告", f"輸入目錄不存在: {dir_path}")
                return False

        # 檢查輸出目錄
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "請選擇輸出目錄")
            return False

        return True

    def get_input_directories(self) -> List[str]:
        """獲取輸入目錄列表"""
        text = self.input_dirs_text.toPlainText().strip()
        if not text:
            return []
        return [line.strip() for line in text.split('\n') if line.strip()]

    def get_tool_params(self) -> Dict[str, Any]:
        """獲取工具參數"""
        source_format = self.source_format_combo.currentText().split(' ')[0]

        return {
            'input_dirs': self.get_input_directories(),
            'output_dir': self.output_dir_edit.text().strip(),
            'source_format': source_format,
            'target_shape': self.target_shape_combo.currentText(),
            'resize': self.resize_spinbox.value(),
            'quality': self.quality_spinbox.value(),
            'max_workers': self.max_workers_spinbox.value()
        }

    def start_worker_thread(self, params):
        """啟動工作線程"""
        # 創建AnnotationConverter實例參數
        converter_params = {
            'logger': self.main_window.logger,
            'max_workers': params['max_workers']
        }

        # 添加日誌記錄器到參數
        params['logger'] = self.main_window.logger

        self.worker_thread = ConverterWorkerThread(AnnotationConverter, params)
        self.worker_thread.log.connect(self.main_window.log)
        self.worker_thread.progress.connect(self.main_window.show_progress)
        self.worker_thread.finished.connect(self.on_worker_finished)
        self.worker_thread.error.connect(self.on_worker_error)
        self.worker_thread.start()

    def reset_settings(self):
        """重置設置"""
        self.input_dirs_text.clear()
        self.output_dir_edit.clear()
        self.source_format_combo.setCurrentIndex(0)
        self.target_shape_combo.setCurrentIndex(0)
        self.resize_spinbox.setValue(1.0)
        self.quality_spinbox.setValue(75)
        self.max_workers_spinbox.setValue(os.cpu_count())

    def save_settings(self):
        """保存設置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            config["converter"]["resize"] = self.resize_spinbox.value()
            config["converter"]["quality"] = self.quality_spinbox.value()
            config["converter"]["format"] = self.source_format_combo.currentText().split(' ')[
                0]
            config["converter"]["max_workers"] = self.max_workers_spinbox.value()
            config["last_output_dir"] = self.output_dir_edit.text().strip()

            super().save_settings()

    def load_widget_config(self):
        """載入界面配置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            converter_config = config.get("converter", {})

            self.resize_spinbox.setValue(converter_config.get("resize", 1.0))
            self.quality_spinbox.setValue(converter_config.get("quality", 75))
            self.max_workers_spinbox.setValue(
                converter_config.get("max_workers", os.cpu_count()))

            # 設置源格式
            format_text = converter_config.get("format", "auto")
            for i in range(self.source_format_combo.count()):
                if self.source_format_combo.itemText(i).startswith(format_text):
                    self.source_format_combo.setCurrentIndex(i)
                    break

            # 設置輸出目錄
            last_output = config.get("last_output_dir", "")
            if last_output:
                self.output_dir_edit.setText(last_output)


class EditorWorkerThread(WorkerThread):
    """標籤編輯器工作線程"""

    def _execute_tool(self, tool_instance):
        """執行標籤編輯"""
        try:
            # 載入數據
            files_count = tool_instance.load_data()
            self.log.emit(f"載入了 {files_count} 個標籤文件")

            # 執行待處理操作
            operations = getattr(self, 'operations', [])
            for operation in operations:
                op_type = operation['type']
                op_data = operation['data']

                if op_type == 'merge':
                    tool_instance.add_merge_operation(op_data)
                elif op_type == 'rename':
                    tool_instance.add_rename_operation(op_data)
                elif op_type == 'delete':
                    tool_instance.add_delete_operation(op_data)

            # 執行所有操作
            success = tool_instance.execute_pending_operations()
            if not success:
                raise RuntimeError("操作執行失敗")

            # 保存結果
            stats = tool_instance.save_results()

            return stats

        except Exception as e:
            self.error.emit(str(e))
            return None


class AnnotationEditorWidget(BaseToolWidget):
    """標籤編輯器界面"""

    def get_tool_title(self) -> str:
        return "標籤編輯器"

    def create_content(self):
        """創建內容區域"""
        # 創建左右分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStretchFactor(0, 7)  # 左邊佔7成
        main_splitter.setStretchFactor(1, 3)  # 右邊佔3成

        # 左側面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # 輸入設置組
        input_group = QGroupBox("輸入設置")
        input_layout = QFormLayout(input_group)

        # 輸入目錄
        input_dir_layout = QHBoxLayout()
        self.input_dir_edit = QLineEdit()
        browse_input_button = QPushButton("瀏覽")
        browse_input_button.clicked.connect(
            lambda: self.browse_directory(self.input_dir_edit)
        )
        input_dir_layout.addWidget(self.input_dir_edit)
        input_dir_layout.addWidget(browse_input_button)
        input_layout.addRow("輸入目錄:", input_dir_layout)

        # 資料格式說明
        format_info = QLabel(
            "資料格式說明：需要包含圖像文件(.jpg/.png)和對應的LabelMe標籤文件(.json)，檔名需相同")
        format_info.setWordWrap(True)
        format_info.setStyleSheet(
            "color: #666666; font-style: italic; padding: 5px; background-color: #f8f9fa; border-radius: 3px;")
        input_layout.addRow("", format_info)

        # 輸出目錄
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        browse_output_button = QPushButton("瀏覽")
        browse_output_button.clicked.connect(
            lambda: self.browse_directory(self.output_dir_edit)
        )
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(browse_output_button)
        input_layout.addRow("輸出目錄:", output_dir_layout)

        # 遞迴處理選項
        self.recursive_checkbox = QCheckBox("遞迴處理子目錄")
        self.recursive_checkbox.setChecked(True)
        input_layout.addRow("", self.recursive_checkbox)

        left_layout.addWidget(input_group)

        # 標籤操作組
        operations_group = QGroupBox("標籤操作")
        operations_layout = QVBoxLayout(operations_group)

        # 操作標籤頁
        self.operations_tab = QTabWidget()

        # 合併標籤頁
        merge_widget = QWidget()
        self.create_merge_tab(merge_widget)
        self.operations_tab.addTab(merge_widget, "合併標籤")

        # 重命名標籤頁
        rename_widget = QWidget()
        self.create_rename_tab(rename_widget)
        self.operations_tab.addTab(rename_widget, "重命名標籤")

        # 刪除標籤頁
        delete_widget = QWidget()
        self.create_delete_tab(delete_widget)
        self.operations_tab.addTab(delete_widget, "刪除標籤")

        # 快速操作標籤頁
        quick_widget = QWidget()
        self.create_quick_operations_tab(quick_widget)
        self.operations_tab.addTab(quick_widget, "快速操作")

        operations_layout.addWidget(self.operations_tab)
        left_layout.addWidget(operations_group)

        # 操作隊列組
        queue_group = QGroupBox("操作隊列")
        queue_layout = QVBoxLayout(queue_group)

        # 操作列表
        self.operations_list = QTextEdit()
        self.operations_list.setMaximumHeight(100)
        self.operations_list.setReadOnly(True)
        self.operations_list.setPlaceholderText("尚無待處理的操作")
        queue_layout.addWidget(QLabel("待處理操作:"))
        queue_layout.addWidget(self.operations_list)

        # 操作按鈕
        queue_buttons_layout = QHBoxLayout()
        clear_queue_button = QPushButton("清空隊列")
        clear_queue_button.clicked.connect(self.clear_operations_queue)
        preview_button = QPushButton("預覽結果")
        preview_button.clicked.connect(self.preview_operations)
        queue_buttons_layout.addWidget(clear_queue_button)
        queue_buttons_layout.addWidget(preview_button)
        queue_buttons_layout.addStretch()
        queue_layout.addLayout(queue_buttons_layout)

        left_layout.addWidget(queue_group)

        # 右側面板 - overlap圖像顯示
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 類別預覽組
        preview_group = QGroupBox("類別預覽")
        preview_layout = QVBoxLayout(preview_group)

        # 掃描按鈕
        scan_button = QPushButton("掃描輸入目錄")
        scan_button.clicked.connect(self.scan_input_directory)
        preview_layout.addWidget(scan_button)

        # 類別列表
        preview_layout.addWidget(QLabel("類別列表:"))
        self.category_list = QListWidget()
        self.category_list.itemClicked.connect(self.on_category_selected)
        # 添加單擊延遲以避免重複觸發
        self.category_list.setSelectionMode(
            QAbstractItemView.SelectionMode.SingleSelection)
        preview_layout.addWidget(self.category_list)

        # 圖像顯示區域
        preview_layout.addWidget(QLabel("overlap圖像:"))
        self.image_label = QLabel()
        self.image_label.setMinimumHeight(700)
        self.image_label.setMaximumHeight(700)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet(
            "border: 1px solid #cccccc; background-color: #f8f9fa;")
        self.image_label.setText("請選擇類別以顯示overlap圖像")
        preview_layout.addWidget(self.image_label)

        # 圖像信息標籤
        self.image_info_label = QLabel()
        self.image_info_label.setWordWrap(True)
        self.image_info_label.setStyleSheet("color: #666666; font-size: 12px;")
        preview_layout.addWidget(self.image_info_label)

        right_layout.addWidget(preview_group)
        right_layout.addStretch()

        # 添加到分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)

        self.content_layout.addWidget(main_splitter)

        # 初始化操作隊列和類別數據
        self.pending_operations = []
        self.categories_data = {}  # 存儲類別和對應的圖像文件
        self._updating_preview = False  # 預覽更新標誌
        self._current_pixmap = None  # 當前顯示的pixmap

        # 載入配置
        self.load_widget_config()

    def create_merge_tab(self, parent):
        """創建合併標籤頁"""
        layout = QVBoxLayout(parent)

        # 說明
        info_label = QLabel("將多個標籤合併為一個新標籤")
        info_label.setStyleSheet("color: #666666; font-style: italic;")
        layout.addWidget(info_label)

        # 合併規則表格
        self.merge_table = QTableWidget()
        self.merge_table.setColumnCount(2)
        self.merge_table.setHorizontalHeaderLabels(["目標標籤", "源標籤 (用逗號分隔)"])
        self.merge_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.merge_table)

        # 表格操作按鈕
        table_buttons_layout = QHBoxLayout()
        add_merge_row_button = QPushButton("添加規則")
        add_merge_row_button.clicked.connect(
            lambda: self.add_table_row(self.merge_table))
        remove_merge_row_button = QPushButton("刪除選中")
        remove_merge_row_button.clicked.connect(
            lambda: self.remove_table_row(self.merge_table))
        add_merge_button = QPushButton("添加到隊列")
        add_merge_button.clicked.connect(self.add_merge_operation)

        table_buttons_layout.addWidget(add_merge_row_button)
        table_buttons_layout.addWidget(remove_merge_row_button)
        table_buttons_layout.addStretch()
        table_buttons_layout.addWidget(add_merge_button)
        layout.addLayout(table_buttons_layout)

        # 添加一個默認行
        self.add_table_row(self.merge_table)

    def create_rename_tab(self, parent):
        """創建重命名標籤頁"""
        layout = QVBoxLayout(parent)

        # 說明
        info_label = QLabel("將標籤重命名為新名稱")
        info_label.setStyleSheet("color: #666666; font-style: italic;")
        layout.addWidget(info_label)

        # 重命名規則表格
        self.rename_table = QTableWidget()
        self.rename_table.setColumnCount(2)
        self.rename_table.setHorizontalHeaderLabels(["原標籤", "新標籤"])
        self.rename_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.rename_table)

        # 表格操作按鈕
        table_buttons_layout = QHBoxLayout()
        add_rename_row_button = QPushButton("添加規則")
        add_rename_row_button.clicked.connect(
            lambda: self.add_table_row(self.rename_table))
        remove_rename_row_button = QPushButton("刪除選中")
        remove_rename_row_button.clicked.connect(
            lambda: self.remove_table_row(self.rename_table))
        add_rename_button = QPushButton("添加到隊列")
        add_rename_button.clicked.connect(self.add_rename_operation)

        table_buttons_layout.addWidget(add_rename_row_button)
        table_buttons_layout.addWidget(remove_rename_row_button)
        table_buttons_layout.addStretch()
        table_buttons_layout.addWidget(add_rename_button)
        layout.addLayout(table_buttons_layout)

        # 添加一個默認行
        self.add_table_row(self.rename_table)

    def create_delete_tab(self, parent):
        """創建刪除標籤頁"""
        layout = QVBoxLayout(parent)

        # 說明
        info_label = QLabel("刪除指定的標籤")
        info_label.setStyleSheet("color: #666666; font-style: italic;")
        layout.addWidget(info_label)

        # 刪除標籤列表
        self.delete_labels_text = QTextEdit()
        self.delete_labels_text.setMaximumHeight(80)
        self.delete_labels_text.setPlaceholderText("每行一個要刪除的標籤名稱")
        layout.addWidget(QLabel("要刪除的標籤:"))
        layout.addWidget(self.delete_labels_text)

        # 操作按鈕
        delete_buttons_layout = QHBoxLayout()
        clear_delete_button = QPushButton("清空列表")
        clear_delete_button.clicked.connect(self.delete_labels_text.clear)
        add_delete_button = QPushButton("添加到隊列")
        add_delete_button.clicked.connect(self.add_delete_operation)

        delete_buttons_layout.addWidget(clear_delete_button)
        delete_buttons_layout.addStretch()
        delete_buttons_layout.addWidget(add_delete_button)
        layout.addLayout(delete_buttons_layout)

    def create_quick_operations_tab(self, widget):
        """創建快速操作標籤頁"""
        layout = QVBoxLayout(widget)

        # 快速合併小類別
        merge_group = QGroupBox("快速合併小類別")
        merge_layout = QFormLayout(merge_group)

        self.min_samples_spin = QSpinBox()
        self.min_samples_spin.setRange(1, 1000)
        self.min_samples_spin.setValue(10)
        merge_layout.addRow("最小樣本數量:", self.min_samples_spin)

        self.merge_target_edit = QLineEdit()
        self.merge_target_edit.setPlaceholderText("目標類別名稱 (例: small_objects)")
        merge_layout.addRow("合併目標:", self.merge_target_edit)

        quick_merge_button = QPushButton("快速合併小類別")
        quick_merge_button.clicked.connect(self.quick_merge_small_classes)
        merge_layout.addRow(quick_merge_button)

        layout.addWidget(merge_group)

        # 快速清理無效標籤
        clean_group = QGroupBox("快速清理無效標籤")
        clean_layout = QVBoxLayout(clean_group)

        self.remove_empty_checkbox = QCheckBox("移除空標籤")
        self.remove_empty_checkbox.setChecked(True)
        clean_layout.addWidget(self.remove_empty_checkbox)

        self.remove_small_area_checkbox = QCheckBox("移除面積過小的標註")
        self.remove_small_area_checkbox.setChecked(True)
        clean_layout.addWidget(self.remove_small_area_checkbox)

        self.min_area_spin = QDoubleSpinBox()
        self.min_area_spin.setRange(0.1, 10000.0)
        self.min_area_spin.setValue(10.0)
        self.min_area_spin.setSuffix(" 像素²")

        area_layout = QHBoxLayout()
        area_layout.addWidget(QLabel("最小面積:"))
        area_layout.addWidget(self.min_area_spin)
        area_layout.addStretch()
        clean_layout.addLayout(area_layout)

        quick_clean_button = QPushButton("快速清理標籤")
        quick_clean_button.clicked.connect(self.quick_clean_labels)
        clean_layout.addWidget(quick_clean_button)

        layout.addWidget(clean_group)

        # 批次重命名
        batch_rename_group = QGroupBox("批次重命名")
        batch_layout = QFormLayout(batch_rename_group)

        self.prefix_edit = QLineEdit()
        self.prefix_edit.setPlaceholderText("例: road_")
        batch_layout.addRow("添加前綴:", self.prefix_edit)

        self.suffix_edit = QLineEdit()
        self.suffix_edit.setPlaceholderText("例: _damage")
        batch_layout.addRow("添加後綴:", self.suffix_edit)

        self.replace_text_edit = QLineEdit()
        self.replace_text_edit.setPlaceholderText("要替換的文字")
        batch_layout.addRow("查找文字:", self.replace_text_edit)

        self.replace_with_edit = QLineEdit()
        self.replace_with_edit.setPlaceholderText("替換為")
        batch_layout.addRow("替換為:", self.replace_with_edit)

        batch_rename_button = QPushButton("批次重命名")
        batch_rename_button.clicked.connect(self.batch_rename_labels)
        batch_layout.addRow(batch_rename_button)

        layout.addWidget(batch_rename_group)

        layout.addStretch()

    def add_table_row(self, table: QTableWidget):
        """添加表格行"""
        row_count = table.rowCount()
        table.setRowCount(row_count + 1)
        table.setItem(row_count, 0, QTableWidgetItem(""))
        table.setItem(row_count, 1, QTableWidgetItem(""))

    def remove_table_row(self, table: QTableWidget):
        """刪除選中的表格行"""
        current_row = table.currentRow()
        if current_row >= 0:
            table.removeRow(current_row)

    def add_merge_operation(self):
        """添加合併操作到隊列"""
        merge_rules = {}

        for row in range(self.merge_table.rowCount()):
            target_item = self.merge_table.item(row, 0)
            sources_item = self.merge_table.item(row, 1)

            if target_item and sources_item:
                target = target_item.text().strip()
                sources_text = sources_item.text().strip()

                if target and sources_text:
                    sources = [s.strip()
                               for s in sources_text.split(',') if s.strip()]
                    if sources:
                        merge_rules[target] = sources

        if merge_rules:
            operation = {
                'type': 'merge',
                'data': merge_rules,
                'description': f"合併: {dict(list(merge_rules.items())[:3])}{'...' if len(merge_rules) > 3 else ''}"
            }
            self.pending_operations.append(operation)
            self.update_operations_display()
            QMessageBox.information(
                self, "成功", f"已添加 {len(merge_rules)} 個合併規則到隊列")
        else:
            QMessageBox.warning(self, "警告", "請填寫有效的合併規則")

    def add_rename_operation(self):
        """添加重命名操作到隊列"""
        rename_rules = {}

        for row in range(self.rename_table.rowCount()):
            old_item = self.rename_table.item(row, 0)
            new_item = self.rename_table.item(row, 1)

            if old_item and new_item:
                old_name = old_item.text().strip()
                new_name = new_item.text().strip()

                if old_name and new_name:
                    rename_rules[old_name] = new_name

        if rename_rules:
            operation = {
                'type': 'rename',
                'data': rename_rules,
                'description': f"重命名: {dict(list(rename_rules.items())[:3])}{'...' if len(rename_rules) > 3 else ''}"
            }
            self.pending_operations.append(operation)
            self.update_operations_display()
            QMessageBox.information(
                self, "成功", f"已添加 {len(rename_rules)} 個重命名規則到隊列")
        else:
            QMessageBox.warning(self, "警告", "請填寫有效的重命名規則")

    def add_delete_operation(self):
        """添加刪除操作到隊列"""
        text = self.delete_labels_text.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "請輸入要刪除的標籤")
            return

        labels_to_delete = [line.strip()
                            for line in text.split('\n') if line.strip()]

        if labels_to_delete:
            operation = {
                'type': 'delete',
                'data': labels_to_delete,
                'description': f"刪除: {', '.join(labels_to_delete[:3])}{'...' if len(labels_to_delete) > 3 else ''}"
            }
            self.pending_operations.append(operation)
            self.update_operations_display()
            QMessageBox.information(
                self, "成功", f"已添加 {len(labels_to_delete)} 個刪除規則到隊列")
        else:
            QMessageBox.warning(self, "警告", "請輸入有效的標籤名稱")

    def update_operations_display(self):
        """更新操作顯示"""
        if not self.pending_operations:
            self.operations_list.setPlainText("尚無待處理的操作")
        else:
            operations_text = []
            for i, op in enumerate(self.pending_operations, 1):
                operations_text.append(f"{i}. {op['description']}")
            self.operations_list.setPlainText('\n'.join(operations_text))

    def clear_operations_queue(self):
        """清空操作隊列"""
        reply = QMessageBox.question(
            self, "確認", "確定要清空所有待處理的操作嗎？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            self.pending_operations.clear()
            self.update_operations_display()

    def preview_operations(self):
        """預覽操作結果"""
        if not self.pending_operations:
            QMessageBox.information(self, "提示", "沒有待處理的操作")
            return

        # 這裡可以實現預覽邏輯
        preview_text = "預覽功能待實現\n\n當前待處理操作:\n"
        for i, op in enumerate(self.pending_operations, 1):
            preview_text += f"{i}. {op['description']}\n"

        QMessageBox.information(self, "操作預覽", preview_text)

    def validate_inputs(self) -> bool:
        """驗證輸入"""
        input_dir = self.input_dir_edit.text().strip()
        if not input_dir:
            QMessageBox.warning(self, "警告", "請選擇輸入目錄")
            return False

        if not os.path.exists(input_dir):
            QMessageBox.warning(self, "警告", "輸入目錄不存在")
            return False

        if not self.pending_operations:
            QMessageBox.warning(self, "警告", "請至少添加一個操作到隊列")
            return False

        return True

    def quick_merge_small_classes(self):
        """快速合併小類別"""
        target_name = self.merge_target_edit.text().strip()
        min_samples = self.min_samples_spin.value()

        if not target_name:
            QMessageBox.warning(self, "警告", "請輸入目標類別名稱")
            return

        # 獲取當前類別統計
        if not hasattr(self, 'categories') or not self.categories:
            QMessageBox.warning(self, "警告", "請先掃描輸入目錄")
            return

        small_classes = []
        for category, count in self.categories.items():
            if count < min_samples:
                small_classes.append(category)

        if not small_classes:
            QMessageBox.information(
                self, "信息", f"沒有找到樣本數量少於 {min_samples} 的類別")
            return

        # 創建合併操作
        merge_rules = {cls: target_name for cls in small_classes}
        operation = {
            'type': 'merge',
            'data': merge_rules,
            'description': f"快速合併 {len(small_classes)} 個小類別到 '{target_name}'"
        }
        self.pending_operations.append(operation)
        self.update_operations_display()

        QMessageBox.information(
            self, "成功",
            f"已添加合併操作: {len(small_classes)} 個類別將合併到 '{target_name}'"
        )

    def quick_clean_labels(self):
        """快速清理無效標籤"""
        operations_added = []

        if self.remove_empty_checkbox.isChecked():
            operation = {
                'type': 'clean_empty',
                'data': {'remove_empty': True},
                'description': "清理空標籤"
            }
            self.pending_operations.append(operation)
            operations_added.append("清理空標籤")

        if self.remove_small_area_checkbox.isChecked():
            min_area = self.min_area_spin.value()
            operation = {
                'type': 'clean_small_area',
                'data': {'min_area': min_area},
                'description': f"清理面積小於 {min_area} 像素²的標註"
            }
            self.pending_operations.append(operation)
            operations_added.append(f"清理小面積標註 (<{min_area} 像素²)")

        if operations_added:
            self.update_operations_display()
            QMessageBox.information(
                self, "成功",
                f"已添加清理操作: {', '.join(operations_added)}"
            )
        else:
            QMessageBox.warning(self, "警告", "請至少選擇一個清理選項")

    def batch_rename_labels(self):
        """批次重命名標籤"""
        prefix = self.prefix_edit.text().strip()
        suffix = self.suffix_edit.text().strip()
        find_text = self.replace_text_edit.text().strip()
        replace_text = self.replace_with_edit.text().strip()

        if not any([prefix, suffix, find_text]):
            QMessageBox.warning(self, "警告", "請至少填寫一個重命名規則")
            return

        # 獲取當前所有類別
        if not hasattr(self, 'categories') or not self.categories:
            QMessageBox.warning(self, "警告", "請先掃描輸入目錄")
            return

        rename_rules = {}

        for category in self.categories.keys():
            new_name = category

            # 應用查找替換
            if find_text and find_text in new_name:
                new_name = new_name.replace(find_text, replace_text)

            # 添加前綴
            if prefix:
                new_name = prefix + new_name

            # 添加後綴
            if suffix:
                new_name = new_name + suffix

            if new_name != category:
                rename_rules[category] = new_name

        if rename_rules:
            operation = {
                'type': 'rename',
                'data': rename_rules,
                'description': f"批次重命名 {len(rename_rules)} 個類別"
            }
            self.pending_operations.append(operation)
            self.update_operations_display()

            QMessageBox.information(
                self, "成功",
                f"已添加批次重命名操作: {len(rename_rules)} 個類別"
            )
        else:
            QMessageBox.information(self, "信息", "沒有類別需要重命名")

    def get_tool_params(self) -> Dict[str, Any]:
        """獲取工具參數"""
        return {
            'input_dir': self.input_dir_edit.text().strip(),
            'output_dir': self.output_dir_edit.text().strip() or None,
            'recursive': self.recursive_checkbox.isChecked(),
            'operations': self.pending_operations.copy()
        }

    def start_worker_thread(self, params):
        """啟動工作線程"""
        # 創建AnnotationEditor實例參數
        editor_params = {
            'input_dir': params['input_dir'],
            'output_dir': params['output_dir'],
            'logger': self.main_window.logger,
            'recursive': params['recursive']
        }

        self.worker_thread = EditorWorkerThread(
            AnnotationEditor, editor_params)
        # 传递原始参数中的operations给worker thread
        self.worker_thread.operations = params.get('operations', [])
        self.worker_thread.log.connect(self.main_window.log)
        self.worker_thread.progress.connect(self.main_window.show_progress)
        self.worker_thread.finished.connect(self.on_worker_finished)
        self.worker_thread.error.connect(self.on_worker_error)
        self.worker_thread.start()

    def on_worker_finished(self, result):
        """工作完成回調"""
        super().on_worker_finished(result)

        # 清空操作隊列
        self.pending_operations.clear()
        self.update_operations_display()

        # 顯示結果
        if result:
            stats_text = f"""
處理完成！

統計信息:
- 處理文件數: {result.get('processed', 0)}
- 跳過文件數: {result.get('skipped', 0)}
- 刪除標註數: {result.get('deleted_annotations', 0)}
- 空文件數: {result.get('empty_files', 0)}
            """
            QMessageBox.information(self, "處理完成", stats_text)

    def reset_settings(self):
        """重置設置"""
        self.input_dir_edit.clear()
        self.output_dir_edit.clear()
        self.recursive_checkbox.setChecked(True)

        # 清空表格
        self.merge_table.setRowCount(0)
        self.rename_table.setRowCount(0)
        self.add_table_row(self.merge_table)
        self.add_table_row(self.rename_table)

        # 清空刪除列表
        self.delete_labels_text.clear()

        # 清空操作隊列
        self.pending_operations.clear()
        self.update_operations_display()

    def save_settings(self):
        """保存設置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            config["editor"]["recursive"] = self.recursive_checkbox.isChecked()
            config["last_input_dir"] = self.input_dir_edit.text().strip()
            if self.output_dir_edit.text().strip():
                config["last_output_dir"] = self.output_dir_edit.text().strip()

            super().save_settings()

    def load_widget_config(self):
        """載入界面配置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            editor_config = config.get("editor", {})

            self.recursive_checkbox.setChecked(
                editor_config.get("recursive", True))

            # 設置目錄
            last_input = config.get("last_input_dir", "")
            if last_input:
                self.input_dir_edit.setText(last_input)

            last_output = config.get("last_output_dir", "")
            if last_output:
                self.output_dir_edit.setText(last_output)

    def scan_input_directory(self):
        """掃描輸入目錄，收集所有類別信息"""
        input_dir = self.input_dir_edit.text().strip()
        if not input_dir or not os.path.exists(input_dir):
            QMessageBox.warning(self, "警告", "請先選擇有效的輸入目錄")
            return

        try:
            import json
            import random

            self.categories_data = {}
            self.category_list.clear()

            # 遞歸掃描目錄
            for root, dirs, files in os.walk(input_dir):
                for file in files:
                    if file.lower().endswith('.json'):
                        json_path = os.path.join(root, file)
                        image_name = os.path.splitext(file)[0]

                        # 查找對應的圖像文件
                        image_path = None
                        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                            potential_path = os.path.join(
                                root, image_name + ext)
                            if os.path.exists(potential_path):
                                image_path = potential_path
                                break

                        if image_path:
                            # 讀取JSON標籤文件
                            try:
                                with open(json_path, 'r', encoding='utf-8') as f:
                                    label_data = json.load(f)

                                # 提取類別
                                if 'shapes' in label_data:
                                    for shape in label_data['shapes']:
                                        category = shape.get(
                                            'label', 'unknown')
                                        if category not in self.categories_data:
                                            self.categories_data[category] = []
                                        self.categories_data[category].append({
                                            'image_path': image_path,
                                            'json_path': json_path,
                                            'shape': shape
                                        })
                            except Exception as e:
                                print(f"讀取標籤文件錯誤 {json_path}: {e}")

            # 更新類別列表
            for category in sorted(self.categories_data.keys()):
                count = len(self.categories_data[category])
                item_text = f"{category} ({count}個)"
                self.category_list.addItem(item_text)

            if self.categories_data:
                QMessageBox.information(self, "掃描完成",
                                        f"找到 {len(self.categories_data)} 個類別，共 {sum(len(items) for items in self.categories_data.values())} 個標註")
            else:
                QMessageBox.warning(self, "掃描結果", "未找到任何有效的標籤文件")

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"掃描目錄時發生錯誤: {str(e)}")

    def on_category_selected(self, item):
        """當選擇類別時顯示隨機overlap圖像"""
        try:
            # 防止重複觸發和繪圖衝突
            if hasattr(self, '_updating_preview') and self._updating_preview:
                return

            self._updating_preview = True

            category_text = item.text()
            category = category_text.split(' (')[0]  # 提取類別名稱

            if category not in self.categories_data:
                self._updating_preview = False
                return

            # 清理之前的圖像顯示
            self.image_label.clear()
            self.image_label.setText("正在載入圖像...")

            # 強制處理事件，確保界面更新
            QApplication.processEvents()

            # 隨機選擇一個該類別的標註
            import random
            category_items = self.categories_data[category]
            selected_item = random.choice(category_items)

            # 顯示overlap圖像
            self.show_overlap_image(selected_item)

            self._updating_preview = False

        except Exception as e:
            self._updating_preview = False
            self.image_label.setText("圖像載入失敗")
            self.image_info_label.setText("")
            QMessageBox.critical(self, "錯誤", f"顯示圖像時發生錯誤: {str(e)}")

    def show_overlap_image(self, item_data):
        """顯示overlap圖像"""
        try:
            import json

            image_path = item_data['image_path']
            json_path = item_data['json_path']
            shape = item_data['shape']

            # 載入原始圖像
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                self.image_label.setText("無法載入圖像")
                return

            # 確保在開始繪圖前清理之前的 pixmap
            if hasattr(self, '_current_pixmap'):
                del self._current_pixmap

            # 創建pixmap的副本以避免繪圖衝突
            self._current_pixmap = QPixmap(pixmap)

            # 創建帶有標註的圖像
            painter = None
            try:
                painter = QPainter(self._current_pixmap)
                painter.setRenderHint(QPainter.RenderHint.Antialiasing)

                # 設置畫筆
                pen = QPen(QColor(255, 0, 0))  # 紅色邊框
                pen.setWidth(3)
                painter.setPen(pen)

                # 設置半透明填充
                brush = QBrush(QColor(255, 0, 0, 50))  # 半透明紅色
                painter.setBrush(brush)

                # 繪製標註
                if shape['shape_type'] == 'rectangle' and len(shape['points']) >= 2:
                    x1, y1 = shape['points'][0]
                    x2, y2 = shape['points'][1]
                    rect = QRectF(min(x1, x2), min(y1, y2),
                                  abs(x2-x1), abs(y2-y1))
                    painter.drawRect(rect)
                elif shape['shape_type'] == 'polygon' and len(shape['points']) >= 3:
                    # 修復 QPolygonF 創建
                    points = [QPointF(p[0], p[1]) for p in shape['points']]
                    polygon = QPolygonF(points)
                    painter.drawPolygon(polygon)

                # 安全結束繪畫
                if painter and painter.isActive():
                    painter.end()
                    painter = None  # 標記為已釋放

            except Exception as e:
                print(f"繪圖錯誤: {e}")
                if painter and painter.isActive():
                    try:
                        painter.end()
                    except:
                        pass

            # 縮放圖像以適應顯示區域
            scaled_pixmap = self._current_pixmap.scaled(
                self.image_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # 安全地設置pixmap
            self.image_label.setPixmap(scaled_pixmap)

            # 更新圖像信息
            info_text = f"圖像: {os.path.basename(image_path)}\n"
            info_text += f"類別: {shape['label']}\n"
            info_text += f"形狀: {shape['shape_type']}\n"
            info_text += f"尺寸: {self._current_pixmap.width()}x{self._current_pixmap.height()}"
            self.image_info_label.setText(info_text)

        except Exception as e:
            self.image_label.setText(f"顯示圖像錯誤: {str(e)}")
            self.image_info_label.setText("")
            # 清理可能的繪圖資源
            if hasattr(self, '_current_pixmap'):
                del self._current_pixmap


class DividerWorkerThread(WorkerThread):
    """數據集分割器工作線程"""

    def _execute_tool(self, tool_instance):
        """執行數據集分割"""
        try:
            # 執行分割
            batch_size = getattr(self, 'batch_size', 50)
            resume = getattr(self, 'resume', True)

            stats = tool_instance.run(batch_size=batch_size, resume=resume)

            return stats

        except Exception as e:
            self.error.emit(str(e))
            return None


class DatasetDividerWidget(BaseToolWidget):
    """數據集分割器界面"""

    def get_tool_title(self) -> str:
        return "數據集分割器"

    def create_content(self):
        """創建內容區域"""
        # 輸入輸出設置組
        io_group = QGroupBox("輸入輸出設置")
        io_layout = QFormLayout(io_group)

        # 輸入目錄
        input_dir_layout = QHBoxLayout()
        self.input_dir_edit = QLineEdit()
        browse_input_button = QPushButton("瀏覽")
        browse_input_button.clicked.connect(
            lambda: self.browse_directory(self.input_dir_edit)
        )
        input_dir_layout.addWidget(self.input_dir_edit)
        input_dir_layout.addWidget(browse_input_button)
        io_layout.addRow("輸入目錄:", input_dir_layout)

        # 輸出目錄
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        browse_output_button = QPushButton("瀏覽")
        browse_output_button.clicked.connect(
            lambda: self.browse_directory(self.output_dir_edit)
        )
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(browse_output_button)
        io_layout.addRow("輸出目錄:", output_dir_layout)

        self.content_layout.addWidget(io_group)

        # 分割比例設置組
        ratio_group = QGroupBox("分割比例設置")
        ratio_layout = QFormLayout(ratio_group)

        # 訓練集比例
        self.train_ratio_spinbox = QDoubleSpinBox()
        self.train_ratio_spinbox.setRange(0.1, 0.9)
        self.train_ratio_spinbox.setSingleStep(0.05)
        self.train_ratio_spinbox.setValue(0.7)
        self.train_ratio_spinbox.setDecimals(2)
        self.train_ratio_spinbox.valueChanged.connect(
            self.update_ratio_display)
        ratio_layout.addRow("訓練集比例:", self.train_ratio_spinbox)

        # 驗證集比例
        self.val_ratio_spinbox = QDoubleSpinBox()
        self.val_ratio_spinbox.setRange(0.05, 0.5)
        self.val_ratio_spinbox.setSingleStep(0.05)
        self.val_ratio_spinbox.setValue(0.15)
        self.val_ratio_spinbox.setDecimals(2)
        self.val_ratio_spinbox.valueChanged.connect(self.update_ratio_display)
        ratio_layout.addRow("驗證集比例:", self.val_ratio_spinbox)

        # 測試集比例
        self.test_ratio_spinbox = QDoubleSpinBox()
        self.test_ratio_spinbox.setRange(0.05, 0.5)
        self.test_ratio_spinbox.setSingleStep(0.05)
        self.test_ratio_spinbox.setValue(0.15)
        self.test_ratio_spinbox.setDecimals(2)
        self.test_ratio_spinbox.valueChanged.connect(self.update_ratio_display)
        ratio_layout.addRow("測試集比例:", self.test_ratio_spinbox)

        # 比例總和顯示
        self.ratio_sum_label = QLabel("總和: 1.00")
        self.ratio_sum_label.setStyleSheet("font-weight: bold;")
        ratio_layout.addRow("", self.ratio_sum_label)

        # 自動調整按鈕
        auto_adjust_layout = QHBoxLayout()
        equal_split_button = QPushButton("平均分割")
        equal_split_button.clicked.connect(self.set_equal_split)
        common_split_button = QPushButton("常用分割 (7:1.5:1.5)")
        common_split_button.clicked.connect(self.set_common_split)
        auto_adjust_layout.addWidget(equal_split_button)
        auto_adjust_layout.addWidget(common_split_button)
        auto_adjust_layout.addStretch()
        ratio_layout.addRow("", auto_adjust_layout)

        self.content_layout.addWidget(ratio_group)

        # 類別限制設置組
        class_limits_group = QGroupBox("類別限制設置（可選）")
        class_limits_layout = QVBoxLayout(class_limits_group)

        # 說明
        info_label = QLabel("設置每個類別的最大樣本數量。留空表示不限制。")
        info_label.setStyleSheet("color: #666666; font-style: italic;")
        class_limits_layout.addWidget(info_label)

        # 類別限制表格
        self.class_limits_table = QTableWidget()
        self.class_limits_table.setColumnCount(2)
        self.class_limits_table.setHorizontalHeaderLabels(["類別名稱", "最大數量"])
        self.class_limits_table.horizontalHeader().setStretchLastSection(True)
        self.class_limits_table.setMaximumHeight(150)
        class_limits_layout.addWidget(self.class_limits_table)

        # 表格操作按鈕
        class_limits_buttons_layout = QHBoxLayout()
        add_class_button = QPushButton("添加類別")
        add_class_button.clicked.connect(
            lambda: self.add_table_row(self.class_limits_table))
        remove_class_button = QPushButton("刪除選中")
        remove_class_button.clicked.connect(
            lambda: self.remove_table_row(self.class_limits_table))
        clear_class_button = QPushButton("清空所有")
        clear_class_button.clicked.connect(self.clear_class_limits_table)

        class_limits_buttons_layout.addWidget(add_class_button)
        class_limits_buttons_layout.addWidget(remove_class_button)
        class_limits_buttons_layout.addWidget(clear_class_button)
        class_limits_buttons_layout.addStretch()
        class_limits_layout.addLayout(class_limits_buttons_layout)

        self.content_layout.addWidget(class_limits_group)

        # 處理設置組
        process_group = QGroupBox("處理設置")
        process_layout = QFormLayout(process_group)

        # 批次大小
        self.batch_size_spinbox = QSpinBox()
        self.batch_size_spinbox.setRange(10, 1000)
        self.batch_size_spinbox.setValue(50)
        process_layout.addRow("批次大小:", self.batch_size_spinbox)

        # 最大並行工作數
        self.max_workers_spinbox = QSpinBox()
        self.max_workers_spinbox.setRange(1, 32)
        self.max_workers_spinbox.setValue(os.cpu_count())
        process_layout.addRow("並行工作數:", self.max_workers_spinbox)

        # 斷點恢復
        self.resume_checkbox = QCheckBox("啟用斷點恢復")
        self.resume_checkbox.setChecked(True)
        process_layout.addRow("", self.resume_checkbox)

        self.content_layout.addWidget(process_group)

        # 添加默認類別限制行
        self.add_table_row(self.class_limits_table)

        # 載入配置
        self.load_widget_config()

    def add_table_row(self, table: QTableWidget):
        """添加表格行"""
        row_count = table.rowCount()
        table.setRowCount(row_count + 1)
        table.setItem(row_count, 0, QTableWidgetItem(""))

        # 為數量列設置數字輸入
        spinbox = QSpinBox()
        spinbox.setRange(1, 99999)
        spinbox.setValue(100)
        table.setCellWidget(row_count, 1, spinbox)

    def remove_table_row(self, table: QTableWidget):
        """刪除選中的表格行"""
        current_row = table.currentRow()
        if current_row >= 0:
            table.removeRow(current_row)

    def clear_class_limits_table(self):
        """清空類別限制表格"""
        self.class_limits_table.setRowCount(0)
        self.add_table_row(self.class_limits_table)

    def update_ratio_display(self):
        """更新比例總和顯示"""
        train_ratio = self.train_ratio_spinbox.value()
        val_ratio = self.val_ratio_spinbox.value()
        test_ratio = self.test_ratio_spinbox.value()

        ratio_sum = train_ratio + val_ratio + test_ratio

        # 更新顯示
        self.ratio_sum_label.setText(f"總和: {ratio_sum:.2f}")

        # 如果總和不為1，則標紅
        if abs(ratio_sum - 1.0) > 0.001:
            self.ratio_sum_label.setStyleSheet(
                "font-weight: bold; color: red;")
        else:
            self.ratio_sum_label.setStyleSheet(
                "font-weight: bold; color: green;")

    def set_equal_split(self):
        """設置平均分割 (1/3 each)"""
        ratio = 1.0 / 3.0
        self.train_ratio_spinbox.setValue(ratio)
        self.val_ratio_spinbox.setValue(ratio)
        self.test_ratio_spinbox.setValue(ratio)

    def set_common_split(self):
        """設置常用分割 (7:1.5:1.5)"""
        self.train_ratio_spinbox.setValue(0.70)
        self.val_ratio_spinbox.setValue(0.15)
        self.test_ratio_spinbox.setValue(0.15)

    def get_class_limits(self) -> Dict[str, int]:
        """獲取類別限制字典"""
        class_limits = {}

        for row in range(self.class_limits_table.rowCount()):
            class_item = self.class_limits_table.item(row, 0)
            limit_widget = self.class_limits_table.cellWidget(row, 1)

            if class_item and limit_widget:
                class_name = class_item.text().strip()
                limit_value = limit_widget.value()

                if class_name and limit_value > 0:
                    class_limits[class_name] = limit_value

        return class_limits

    def validate_inputs(self) -> bool:
        """驗證輸入"""
        # 檢查輸入目錄
        input_dir = self.input_dir_edit.text().strip()
        if not input_dir:
            QMessageBox.warning(self, "警告", "請選擇輸入目錄")
            return False

        if not os.path.exists(input_dir):
            QMessageBox.warning(self, "警告", "輸入目錄不存在")
            return False

        # 檢查輸出目錄
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "請選擇輸出目錄")
            return False

        # 檢查比例總和
        train_ratio = self.train_ratio_spinbox.value()
        val_ratio = self.val_ratio_spinbox.value()
        test_ratio = self.test_ratio_spinbox.value()
        ratio_sum = train_ratio + val_ratio + test_ratio

        if abs(ratio_sum - 1.0) > 0.001:
            QMessageBox.warning(self, "警告", f"分割比例總和必須為1.0，當前為{ratio_sum:.2f}")
            return False

        return True

    def get_tool_params(self) -> Dict[str, Any]:
        """獲取工具參數"""
        class_limits = self.get_class_limits()

        return {
            'input_dir': self.input_dir_edit.text().strip(),
            'output_dir': self.output_dir_edit.text().strip(),
            'train_ratio': self.train_ratio_spinbox.value(),
            'val_ratio': self.val_ratio_spinbox.value(),
            'test_ratio': self.test_ratio_spinbox.value(),
            'class_limits': class_limits if class_limits else None,
            'max_workers': self.max_workers_spinbox.value(),
            'batch_size': self.batch_size_spinbox.value(),
            'resume': self.resume_checkbox.isChecked()
        }

    def start_worker_thread(self, params):
        """啟動工作線程"""
        # 創建DatasetDivider實例參數
        divider_params = {
            'input_dir': params['input_dir'],
            'output_dir': params['output_dir'],
            'train_ratio': params['train_ratio'],
            'val_ratio': params['val_ratio'],
            'test_ratio': params['test_ratio'],
            'class_limits': params['class_limits'],
            'logger': self.main_window.logger,
            'max_workers': params['max_workers']
        }

        self.worker_thread = DividerWorkerThread(
            DatasetDivider, divider_params)
        # 傳遞batch_size和resume參數給worker thread
        self.worker_thread.batch_size = params.get('batch_size', 50)
        self.worker_thread.resume = params.get('resume', True)
        self.worker_thread.log.connect(self.main_window.log)
        self.worker_thread.progress.connect(self.main_window.show_progress)
        self.worker_thread.finished.connect(self.on_worker_finished)
        self.worker_thread.error.connect(self.on_worker_error)
        self.worker_thread.start()

    def on_worker_finished(self, result):
        """工作完成回調"""
        super().on_worker_finished(result)

        # 顯示詳細結果
        if result:
            samples = result.get('samples', {})
            class_dist = result.get('class_distribution', {})

            stats_text = f"""
分割完成！

樣本分佈:
- 總樣本數: {samples.get('total', 0)}
- 訓練集: {samples.get('train', 0)} ({samples.get('train', 0)/samples.get('total', 1)*100:.1f}%)
- 驗證集: {samples.get('val', 0)} ({samples.get('val', 0)/samples.get('total', 1)*100:.1f}%)
- 測試集: {samples.get('test', 0)} ({samples.get('test', 0)/samples.get('total', 1)*100:.1f}%)

類別數量: {len([k for k in class_dist.keys() if not k.endswith('_total')])}

詳細統計信息已保存到輸出目錄中的 split_statistics.json 和 split_report.txt 文件。
            """
            QMessageBox.information(self, "分割完成", stats_text)

    def reset_settings(self):
        """重置設置"""
        self.input_dir_edit.clear()
        self.output_dir_edit.clear()
        self.train_ratio_spinbox.setValue(0.7)
        self.val_ratio_spinbox.setValue(0.15)
        self.test_ratio_spinbox.setValue(0.15)
        self.batch_size_spinbox.setValue(50)
        self.max_workers_spinbox.setValue(os.cpu_count())
        self.resume_checkbox.setChecked(True)

        # 清空類別限制表格
        self.class_limits_table.setRowCount(0)
        self.add_table_row(self.class_limits_table)

    def save_settings(self):
        """保存設置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            config["divider"]["train_ratio"] = self.train_ratio_spinbox.value()
            config["divider"]["val_ratio"] = self.val_ratio_spinbox.value()
            config["divider"]["test_ratio"] = self.test_ratio_spinbox.value()
            config["divider"]["max_workers"] = self.max_workers_spinbox.value()
            config["last_input_dir"] = self.input_dir_edit.text().strip()
            config["last_output_dir"] = self.output_dir_edit.text().strip()

            super().save_settings()

    def load_widget_config(self):
        """載入界面配置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            divider_config = config.get("divider", {})

            self.train_ratio_spinbox.setValue(
                divider_config.get("train_ratio", 0.7))
            self.val_ratio_spinbox.setValue(
                divider_config.get("val_ratio", 0.15))
            self.test_ratio_spinbox.setValue(
                divider_config.get("test_ratio", 0.15))
            self.max_workers_spinbox.setValue(
                divider_config.get("max_workers", os.cpu_count()))

            # 設置目錄
            last_input = config.get("last_input_dir", "")
            if last_input:
                self.input_dir_edit.setText(last_input)

            last_output = config.get("last_output_dir", "")
            if last_output:
                self.output_dir_edit.setText(last_output)


class AugmenterWorkerThread(WorkerThread):
    """圖像增強器工作線程"""

    def _execute_tool(self, tool_instance):
        """執行圖像增強"""
        try:
            # 執行增強
            params = self.params
            stats = tool_instance.run(
                num_generations=params.get('num_generations', 100),
                regions_per_image=params.get('regions_per_image', (2, 3)),
                scale_range=params.get('scale_range', (0.6, 1.2)),
                margins=params.get('margins', (0, 0, 0, 0)),
                grid_placement=params.get('grid_placement', True),
                grid_size=params.get('grid_size', 8),
                max_placement_attempts=params.get(
                    'max_placement_attempts', 50),
                avoid_similar_positions=params.get(
                    'avoid_similar_positions', True),
                avoid_overlap=params.get('avoid_overlap', True),
                iou_threshold=params.get('iou_threshold', 0.1),
                visualize_region=params.get('visualize_region', False)
            )

            return stats

        except Exception as e:
            self.error.emit(str(e))
            return None


class ImageAugmenterWidget(BaseToolWidget):
    """圖像增強器界面"""

    def get_tool_title(self) -> str:
        return "圖像增強器"

    def create_content(self):
        """創建內容區域"""
        # 創建左右分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStretchFactor(0, 7)  # 左邊佔7成
        main_splitter.setStretchFactor(1, 3)  # 右邊佔3成

        # 左側面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # 目錄設置組
        dirs_group = QGroupBox("目錄設置")
        dirs_layout = QVBoxLayout(dirs_group)

        # 來源目錄
        source_layout = QFormLayout()
        self.source_dirs_text = QTextEdit()
        self.source_dirs_text.setMaximumHeight(80)
        self.source_dirs_text.setPlaceholderText("每行一個來源目錄路徑")
        source_layout.addRow("來源目錄:", self.source_dirs_text)

        # 來源目錄操作按鈕
        source_buttons_layout = QHBoxLayout()
        add_source_button = QPushButton("添加目錄")
        add_source_button.clicked.connect(self.add_source_directory)
        clear_source_button = QPushButton("清除列表")
        clear_source_button.clicked.connect(self.source_dirs_text.clear)
        source_buttons_layout.addWidget(add_source_button)
        source_buttons_layout.addWidget(clear_source_button)
        source_buttons_layout.addStretch()
        source_layout.addRow("", source_buttons_layout)

        dirs_layout.addLayout(source_layout)

        # 目標目錄
        target_layout = QFormLayout()
        target_dir_layout = QHBoxLayout()
        self.target_dir_edit = QLineEdit()
        self.target_dir_edit.textChanged.connect(self.update_preview)  # 添加預覽更新
        browse_target_button = QPushButton("瀏覽")
        browse_target_button.clicked.connect(
            lambda: self.browse_directory(self.target_dir_edit)
        )
        target_dir_layout.addWidget(self.target_dir_edit)
        target_dir_layout.addWidget(browse_target_button)
        target_layout.addRow("目標目錄:", target_dir_layout)

        # 輸出目錄
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        browse_output_button = QPushButton("瀏覽")
        browse_output_button.clicked.connect(
            lambda: self.browse_directory(self.output_dir_edit)
        )
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(browse_output_button)
        target_layout.addRow("輸出目錄:", output_dir_layout)

        dirs_layout.addLayout(target_layout)
        left_layout.addWidget(dirs_group)

        # 增強參數組
        params_group = QGroupBox("增強參數")
        params_layout = QFormLayout(params_group)

        # 生成數量
        self.num_generations_spinbox = QSpinBox()
        self.num_generations_spinbox.setRange(1, 10000)
        self.num_generations_spinbox.setValue(100)
        self.num_generations_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        params_layout.addRow("生成數量:", self.num_generations_spinbox)

        # 任務類型
        self.task_type_combo = QComboBox()
        self.task_type_combo.addItems([
            "both - 分割和檢測",
            "seg - 僅分割",
            "det - 僅檢測"
        ])
        self.task_type_combo.currentTextChanged.connect(
            self.update_preview)  # 添加預覽更新
        params_layout.addRow("任務類型:", self.task_type_combo)

        # 圖像品質
        self.quality_spinbox = QSpinBox()
        self.quality_spinbox.setRange(1, 100)
        self.quality_spinbox.setValue(75)
        self.quality_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        params_layout.addRow("圖像品質:", self.quality_spinbox)

        # 網格大小
        self.grid_size_spinbox = QSpinBox()
        self.grid_size_spinbox.setRange(4, 32)
        self.grid_size_spinbox.setValue(8)
        self.grid_size_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        params_layout.addRow("網格大小:", self.grid_size_spinbox)

        # IoU閾值
        self.iou_threshold_spinbox = QDoubleSpinBox()
        self.iou_threshold_spinbox.setRange(0.0, 1.0)
        self.iou_threshold_spinbox.setSingleStep(0.1)
        self.iou_threshold_spinbox.setValue(0.1)
        self.iou_threshold_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        params_layout.addRow("IoU閾值:", self.iou_threshold_spinbox)

        # Margins參數
        margins_label = QLabel("邊界留白 (像素):")
        margins_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        params_layout.addRow(margins_label)

        # 創建margins控制項的水平佈局
        margins_widget = QWidget()
        margins_layout = QGridLayout(margins_widget)
        margins_layout.setContentsMargins(0, 0, 0, 0)

        # 上邊界
        self.margin_top_spinbox = QSpinBox()
        self.margin_top_spinbox.setRange(0, 500)
        self.margin_top_spinbox.setValue(50)
        self.margin_top_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        margins_layout.addWidget(QLabel("上:"), 0, 0)
        margins_layout.addWidget(self.margin_top_spinbox, 0, 1)

        # 下邊界
        self.margin_bottom_spinbox = QSpinBox()
        self.margin_bottom_spinbox.setRange(0, 500)
        self.margin_bottom_spinbox.setValue(50)
        self.margin_bottom_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        margins_layout.addWidget(QLabel("下:"), 0, 2)
        margins_layout.addWidget(self.margin_bottom_spinbox, 0, 3)

        # 左邊界
        self.margin_left_spinbox = QSpinBox()
        self.margin_left_spinbox.setRange(0, 500)
        self.margin_left_spinbox.setValue(50)
        self.margin_left_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        margins_layout.addWidget(QLabel("左:"), 1, 0)
        margins_layout.addWidget(self.margin_left_spinbox, 1, 1)

        # 右邊界
        self.margin_right_spinbox = QSpinBox()
        self.margin_right_spinbox.setRange(0, 500)
        self.margin_right_spinbox.setValue(50)
        self.margin_right_spinbox.valueChanged.connect(
            self.update_preview)  # 添加預覽更新
        margins_layout.addWidget(QLabel("右:"), 1, 2)
        margins_layout.addWidget(self.margin_right_spinbox, 1, 3)

        params_layout.addRow(margins_widget)

        left_layout.addWidget(params_group)

        # 進階選項組
        advanced_group = QGroupBox("進階選項")
        advanced_layout = QVBoxLayout(advanced_group)

        # 選項複選框
        self.grid_placement_checkbox = QCheckBox("啟用網格放置策略")
        self.grid_placement_checkbox.setChecked(True)
        self.grid_placement_checkbox.toggled.connect(
            self.update_preview)  # 添加預覽更新
        advanced_layout.addWidget(self.grid_placement_checkbox)

        self.avoid_similar_checkbox = QCheckBox("避免相似位置")
        self.avoid_similar_checkbox.setChecked(True)
        self.avoid_similar_checkbox.toggled.connect(
            self.update_preview)  # 添加預覽更新
        advanced_layout.addWidget(self.avoid_similar_checkbox)

        self.avoid_overlap_checkbox = QCheckBox("避免標籤重疊")
        self.avoid_overlap_checkbox.setChecked(True)
        self.avoid_overlap_checkbox.toggled.connect(
            self.update_preview)  # 添加預覽更新
        advanced_layout.addWidget(self.avoid_overlap_checkbox)

        self.visualize_region_checkbox = QCheckBox("生成放置區域可視化")
        self.visualize_region_checkbox.setChecked(False)
        self.visualize_region_checkbox.toggled.connect(
            self.update_preview)  # 添加預覽更新
        advanced_layout.addWidget(self.visualize_region_checkbox)

        left_layout.addWidget(advanced_group)

        # 類別目標設置組
        class_targets_group = QGroupBox("類別目標設置（可選）")
        class_targets_layout = QVBoxLayout(class_targets_group)

        # 說明
        info_label = QLabel("設置每個類別的目標生成數量。留空表示使用默認值。")
        info_label.setStyleSheet("color: #666666; font-style: italic;")
        class_targets_layout.addWidget(info_label)

        # 類別目標表格
        self.class_targets_table = QTableWidget()
        self.class_targets_table.setColumnCount(2)
        self.class_targets_table.setHorizontalHeaderLabels(["類別名稱", "目標數量"])
        self.class_targets_table.horizontalHeader().setStretchLastSection(True)
        self.class_targets_table.setMaximumHeight(120)
        class_targets_layout.addWidget(self.class_targets_table)

        # 表格操作按鈕
        class_targets_buttons_layout = QHBoxLayout()
        add_class_target_button = QPushButton("添加類別")
        add_class_target_button.clicked.connect(
            lambda: self.add_table_row(self.class_targets_table))
        remove_class_target_button = QPushButton("刪除選中")
        remove_class_target_button.clicked.connect(
            lambda: self.remove_table_row(self.class_targets_table))
        clear_class_targets_button = QPushButton("清空所有")
        clear_class_targets_button.clicked.connect(
            self.clear_class_targets_table)

        class_targets_buttons_layout.addWidget(add_class_target_button)
        class_targets_buttons_layout.addWidget(remove_class_target_button)
        class_targets_buttons_layout.addWidget(clear_class_targets_button)
        class_targets_buttons_layout.addStretch()
        class_targets_layout.addLayout(class_targets_buttons_layout)

        left_layout.addWidget(class_targets_group)

        # 添加默認類別目標行
        self.add_table_row(self.class_targets_table)

        # 右側面板 - 圖像預覽
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 預覽組
        preview_group = QGroupBox("生成範圍預覽")
        preview_layout = QVBoxLayout(preview_group)

        # 預覽控制按鈕
        preview_control_layout = QHBoxLayout()
        self.load_sample_button = QPushButton("載入範例圖像")
        self.load_sample_button.clicked.connect(self.load_sample_image)
        self.update_preview_button = QPushButton("更新預覽")
        self.update_preview_button.clicked.connect(self.update_preview)
        preview_control_layout.addWidget(self.load_sample_button)
        preview_control_layout.addWidget(self.update_preview_button)
        preview_control_layout.addStretch()
        preview_layout.addLayout(preview_control_layout)

        # 圖像顯示區域
        preview_layout.addWidget(QLabel("預覽圖像:"))
        self.preview_image_label = QLabel()
        self.preview_image_label.setMinimumHeight(400)
        self.preview_image_label.setMaximumHeight(400)
        self.preview_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_image_label.setStyleSheet(
            "border: 1px solid #cccccc; background-color: #f8f9fa;")
        self.preview_image_label.setText("請載入範例圖像以顯示生成範圍預覽")
        preview_layout.addWidget(self.preview_image_label)

        # 預覽信息標籤
        self.preview_info_label = QLabel()
        self.preview_info_label.setWordWrap(True)
        self.preview_info_label.setStyleSheet(
            "color: #666666; font-size: 12px;")
        preview_layout.addWidget(self.preview_info_label)

        # 統計信息區域
        stats_layout = QFormLayout()
        self.stats_grid_size_label = QLabel("未設定")
        self.stats_generation_count_label = QLabel("未設定")
        self.stats_placement_regions_label = QLabel("未設定")
        self.stats_margin_settings_label = QLabel("未設定")

        stats_layout.addRow("網格大小:", self.stats_grid_size_label)
        stats_layout.addRow("生成數量:", self.stats_generation_count_label)
        stats_layout.addRow("放置區域:", self.stats_placement_regions_label)
        stats_layout.addRow("邊距設定:", self.stats_margin_settings_label)

        preview_layout.addLayout(stats_layout)

        right_layout.addWidget(preview_group)
        right_layout.addStretch()

        # 添加到分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)

        self.content_layout.addWidget(main_splitter)

        # 初始化預覽變數
        self._current_preview_pixmap = None
        self._sample_image_path = None
        self._updating_preview = False

        # 添加防抖定時器
        self._preview_timer = QTimer()
        self._preview_timer.setSingleShot(True)
        self._preview_timer.timeout.connect(self._delayed_update_preview)

        # 載入配置
        self.load_widget_config()

    def load_sample_image(self):
        """載入範例圖像"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "選擇範例圖像",
            "",
            "圖像文件 (*.jpg *.jpeg *.png *.bmp);;所有文件 (*.*)"
        )

        if file_path:
            self._sample_image_path = file_path
            self.update_preview()

    def update_preview(self):
        """更新預覽（防抖版本）"""
        # 使用定時器防抖，避免頻繁更新
        self._preview_timer.stop()
        self._preview_timer.start(150)  # 150ms延遲

    def _delayed_update_preview(self):
        """延遲的預覽更新"""
        if self._updating_preview:
            return

        self._updating_preview = True

        try:
            # 首先清理之前的預覽資源
            self._cleanup_preview_pixmap()

            # 更新統計信息
            self.update_stats_display()

            # 如果有範例圖像，顯示生成範圍預覽
            if self._sample_image_path and os.path.exists(self._sample_image_path):
                self.show_generation_preview()
            else:
                self.preview_image_label.setText("請載入範例圖像以顯示生成範圍預覽")
                self.preview_info_label.setText("")

        except Exception as e:
            self.preview_image_label.setText(f"預覽錯誤: {str(e)}")
            self.preview_info_label.setText("")
            # 確保清理資源
            self._cleanup_preview_pixmap()
        finally:
            self._updating_preview = False

    def update_stats_display(self):
        """更新統計顯示"""
        # 網格大小
        grid_size = self.grid_size_spinbox.value()
        self.stats_grid_size_label.setText(f"{grid_size}x{grid_size}")

        # 生成數量
        gen_count = self.num_generations_spinbox.value()
        self.stats_generation_count_label.setText(str(gen_count))

        # 放置區域
        if self.grid_placement_checkbox.isChecked():
            placement_text = f"網格放置 ({grid_size}x{grid_size})"
        else:
            placement_text = "隨機放置"

        if self.avoid_overlap_checkbox.isChecked():
            placement_text += " + 避免重疊"
        if self.avoid_similar_checkbox.isChecked():
            placement_text += " + 避免相似"

        self.stats_placement_regions_label.setText(placement_text)

        # 邊距設定
        margins = (
            self.margin_top_spinbox.value(),
            self.margin_right_spinbox.value(),
            self.margin_bottom_spinbox.value(),
            self.margin_left_spinbox.value()
        )
        margin_text = f"上:{margins[0]} 右:{margins[1]} 下:{margins[2]} 左:{margins[3]}"
        self.stats_margin_settings_label.setText(margin_text)

    def show_generation_preview(self):
        """顯示生成範圍預覽"""
        painter = None
        try:
            # 載入原始圖像
            pixmap = QPixmap(self._sample_image_path)
            if pixmap.isNull():
                self.preview_image_label.setText("無法載入圖像")
                return

            # 創建預覽圖像副本
            self._current_preview_pixmap = QPixmap(pixmap)

            # 創建畫家來繪製生成範圍
            painter = QPainter(self._current_preview_pixmap)
            if not painter.isActive():
                self.preview_image_label.setText("無法創建繪圖設備")
                return

            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 獲取圖像尺寸
            img_width = pixmap.width()
            img_height = pixmap.height()

            # 獲取邊距設定
            margins = (
                self.margin_top_spinbox.value(),
                self.margin_right_spinbox.value(),
                self.margin_bottom_spinbox.value(),
                self.margin_left_spinbox.value()
            )

            # 計算有效生成區域
            effective_x = margins[3]  # 左邊距
            effective_y = margins[0]  # 上邊距
            effective_width = img_width - \
                margins[1] - margins[3]  # 寬度 - 右邊距 - 左邊距
            effective_height = img_height - \
                margins[0] - margins[2]  # 高度 - 上邊距 - 下邊距

            # 繪製邊距區域（半透明紅色）
            painter.setPen(QPen(QColor(255, 0, 0, 100)))
            painter.setBrush(QBrush(QColor(255, 0, 0, 50)))

            # 上邊距
            if margins[0] > 0:
                painter.drawRect(0, 0, img_width, margins[0])
            # 下邊距
            if margins[2] > 0:
                painter.drawRect(
                    0, img_height - margins[2], img_width, margins[2])
            # 左邊距
            if margins[3] > 0:
                painter.drawRect(0, 0, margins[3], img_height)
            # 右邊距
            if margins[1] > 0:
                painter.drawRect(
                    img_width - margins[1], 0, margins[1], img_height)

            # 繪製有效生成區域邊框（綠色）
            painter.setPen(QPen(QColor(0, 255, 0), 3))
            painter.setBrush(QBrush())  # 無填充
            painter.drawRect(effective_x, effective_y,
                             effective_width, effective_height)

            # 如果啟用網格放置，繪製網格
            if self.grid_placement_checkbox.isChecked():
                grid_size = self.grid_size_spinbox.value()
                cell_width = effective_width / grid_size
                cell_height = effective_height / grid_size

                painter.setPen(QPen(QColor(0, 0, 255, 128), 1))  # 半透明藍色網格線

                # 繪製垂直網格線
                for i in range(1, grid_size):
                    x = int(effective_x + i * cell_width)
                    painter.drawLine(x, int(effective_y), x,
                                     int(effective_y + effective_height))

                # 繪製水平網格線
                for i in range(1, grid_size):
                    y = int(effective_y + i * cell_height)
                    painter.drawLine(
                        int(effective_x), y, int(effective_x + effective_width), y)

            # 安全結束繪畫
            if painter and painter.isActive():
                painter.end()
                painter = None  # 標記為已釋放

            # 縮放圖像以適應顯示區域
            scaled_pixmap = self._current_preview_pixmap.scaled(
                self.preview_image_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # 設置pixmap
            self.preview_image_label.setPixmap(scaled_pixmap)

            # 更新信息
            info_text = f"範例圖像: {os.path.basename(self._sample_image_path)}\n"
            info_text += f"圖像尺寸: {img_width}x{img_height}\n"
            info_text += f"有效生成區域: {effective_width}x{effective_height}\n"
            info_text += f"邊距設定: 上{margins[0]} 右{margins[1]} 下{margins[2]} 左{margins[3]}"

            if self.grid_placement_checkbox.isChecked():
                grid_size = self.grid_size_spinbox.value()
                info_text += f"\n網格: {grid_size}x{grid_size} ({grid_size*grid_size}個位置)"

            self.preview_info_label.setText(info_text)

        except Exception as e:
            self.preview_image_label.setText(f"顯示預覽錯誤: {str(e)}")
            self.preview_info_label.setText("")
        finally:
            # 確保painter被正確釋放（避免重複釋放）
            if painter and painter.isActive():
                try:
                    painter.end()
                except:
                    pass  # 忽略重複釋放錯誤
            # 在painter釋放後再清理pixmap
            if hasattr(self, '_current_preview_pixmap'):
                self._cleanup_preview_pixmap()

    def _cleanup_preview_pixmap(self):
        """安全清理預覽pixmap"""
        if hasattr(self, '_current_preview_pixmap') and self._current_preview_pixmap is not None:
            self._current_preview_pixmap = None

    def add_source_directory(self):
        """添加來源目錄"""
        directory = QFileDialog.getExistingDirectory(self, "選擇來源目錄")
        if directory:
            current_text = self.source_dirs_text.toPlainText()
            if current_text:
                self.source_dirs_text.setPlainText(
                    current_text + "\n" + directory)
            else:
                self.source_dirs_text.setPlainText(directory)

    def add_table_row(self, table: QTableWidget):
        """添加表格行"""
        row_count = table.rowCount()
        table.setRowCount(row_count + 1)
        table.setItem(row_count, 0, QTableWidgetItem(""))

        # 為數量列設置數字輸入
        spinbox = QSpinBox()
        spinbox.setRange(1, 99999)
        spinbox.setValue(100)
        table.setCellWidget(row_count, 1, spinbox)

    def remove_table_row(self, table: QTableWidget):
        """刪除選中的表格行"""
        current_row = table.currentRow()
        if current_row >= 0:
            table.removeRow(current_row)

    def clear_class_targets_table(self):
        """清空類別目標表格"""
        self.class_targets_table.setRowCount(0)
        self.add_table_row(self.class_targets_table)

    def get_source_directories(self) -> List[str]:
        """獲取來源目錄列表"""
        text = self.source_dirs_text.toPlainText().strip()
        if not text:
            return []
        return [line.strip() for line in text.split('\n') if line.strip()]

    def get_class_targets(self) -> Dict[str, int]:
        """獲取類別目標字典"""
        class_targets = {}

        for row in range(self.class_targets_table.rowCount()):
            class_item = self.class_targets_table.item(row, 0)
            target_widget = self.class_targets_table.cellWidget(row, 1)

            if class_item and target_widget:
                class_name = class_item.text().strip()
                target_value = target_widget.value()

                if class_name and target_value > 0:
                    class_targets[class_name] = target_value

        return class_targets

    def validate_inputs(self) -> bool:
        """驗證輸入"""
        # 檢查來源目錄
        source_dirs = self.get_source_directories()
        if not source_dirs:
            QMessageBox.warning(self, "警告", "請至少添加一個來源目錄")
            return False

        # 檢查目錄是否存在
        for dir_path in source_dirs:
            if not os.path.exists(dir_path):
                QMessageBox.warning(self, "警告", f"來源目錄不存在: {dir_path}")
                return False

        # 檢查目標目錄
        target_dir = self.target_dir_edit.text().strip()
        if not target_dir:
            QMessageBox.warning(self, "警告", "請選擇目標目錄")
            return False

        if not os.path.exists(target_dir):
            QMessageBox.warning(self, "警告", "目標目錄不存在")
            return False

        # 檢查輸出目錄
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "請選擇輸出目錄")
            return False

        return True

    def get_tool_params(self) -> Dict[str, Any]:
        """獲取工具參數"""
        task_type = self.task_type_combo.currentText().split(' ')[0]
        class_targets = self.get_class_targets()

        return {
            'source_dirs': self.get_source_directories(),
            'target_dir': self.target_dir_edit.text().strip(),
            'output_dir': self.output_dir_edit.text().strip(),
            'num_generations': self.num_generations_spinbox.value(),
            'task_type': task_type,
            'quality': self.quality_spinbox.value(),
            'class_targets': class_targets if class_targets else None,
            'grid_placement': self.grid_placement_checkbox.isChecked(),
            'grid_size': self.grid_size_spinbox.value(),
            'avoid_similar_positions': self.avoid_similar_checkbox.isChecked(),
            'avoid_overlap': self.avoid_overlap_checkbox.isChecked(),
            'iou_threshold': self.iou_threshold_spinbox.value(),
            'visualize_region': self.visualize_region_checkbox.isChecked(),
            'regions_per_image': (2, 3),  # 固定範圍
            'scale_range': (0.6, 1.2),    # 固定範圍
            'margins': (
                self.margin_top_spinbox.value(),
                self.margin_right_spinbox.value(),
                self.margin_bottom_spinbox.value(),
                self.margin_left_spinbox.value()
            ),  # 邊距: (上, 右, 下, 左)
            'max_placement_attempts': 50   # 固定嘗試次數
        }

    def start_worker_thread(self, params):
        """啟動工作線程"""
        # 創建ImageAugmenter實例參數
        augmenter_params = {
            'source_dirs': params['source_dirs'],
            'target_dir': params['target_dir'],
            'output_dir': params['output_dir'],
            'resize': None,  # 暫時固定為None
            'quality': params['quality'],
            'class_targets': params['class_targets'],
            'task_type': params['task_type'],
            'logger': self.main_window.logger
        }

        self.worker_thread = AugmenterWorkerThread(ImageAugmenter, params)
        self.worker_thread.log.connect(self.main_window.log)
        self.worker_thread.progress.connect(self.main_window.show_progress)
        self.worker_thread.finished.connect(self.on_worker_finished)
        self.worker_thread.error.connect(self.on_worker_error)
        self.worker_thread.start()

    def on_worker_finished(self, result):
        """工作完成回調"""
        super().on_worker_finished(result)

        # 顯示詳細結果
        if result:
            generated = result.get('generated', 0)
            failures = result.get('failures', 0)
            class_counts = result.get('class_counts', {})

            stats_text = f"""
增強完成！

生成統計:
- 成功生成: {generated} 張圖像
- 失败數量: {failures}
- 成功率: {generated/(generated+failures)*100:.1f}%

類別分佈:
{self._format_class_counts(class_counts)}

詳細統計信息已保存到輸出目錄中的 augmentation_stats.json 和 augmentation_report.txt 文件。
            """
            QMessageBox.information(self, "增強完成", stats_text)

    def _format_class_counts(self, class_counts: Dict[str, int]) -> str:
        """格式化類別計數顯示"""
        if not class_counts:
            return "無類別數據"

        lines = []
        for class_name, count in class_counts.items():
            lines.append(f"- {class_name}: {count}")

        return '\n'.join(lines[:10])  # 最多顯示10個類別

    def reset_settings(self):
        """重置設置"""
        self.source_dirs_text.clear()
        self.target_dir_edit.clear()
        self.output_dir_edit.clear()
        self.num_generations_spinbox.setValue(100)
        self.task_type_combo.setCurrentIndex(0)
        self.quality_spinbox.setValue(75)
        self.grid_size_spinbox.setValue(8)
        self.iou_threshold_spinbox.setValue(0.1)

        # 重置複選框
        self.grid_placement_checkbox.setChecked(True)
        self.avoid_similar_checkbox.setChecked(True)
        self.avoid_overlap_checkbox.setChecked(True)
        self.visualize_region_checkbox.setChecked(False)

        # 清空類別目標表格
        self.class_targets_table.setRowCount(0)
        self.add_table_row(self.class_targets_table)

    def save_settings(self):
        """保存設置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            config["augmenter"]["num_generations"] = self.num_generations_spinbox.value()
            config["augmenter"]["task_type"] = self.task_type_combo.currentText().split(' ')[
                0]
            config["augmenter"]["quality"] = self.quality_spinbox.value()
            config["last_output_dir"] = self.output_dir_edit.text().strip()

            super().save_settings()

    def load_widget_config(self):
        """載入界面配置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            augmenter_config = config.get("augmenter", {})

            self.num_generations_spinbox.setValue(
                augmenter_config.get("num_generations", 100))
            self.quality_spinbox.setValue(augmenter_config.get("quality", 75))

            # 設置任務類型
            task_type = augmenter_config.get("task_type", "both")
            for i in range(self.task_type_combo.count()):
                if self.task_type_combo.itemText(i).startswith(task_type):
                    self.task_type_combo.setCurrentIndex(i)
                    break

            # 設置輸出目錄
            last_output = config.get("last_output_dir", "")
            if last_output:
                self.output_dir_edit.setText(last_output)


class PanoramaWorkerThread(WorkerThread):
    """全景圖像擴增器工作線程"""

    def _execute_tool(self, tool_instance):
        """執行全景圖像擴增"""
        try:
            params = self.params

            if params.get('batch_mode', False):
                # 批量處理
                stats = tool_instance.process_batch(
                    input_dir=os.path.dirname(params.get('image_path', '.')),
                    output_dir=params['output_dir'],
                    excel_path=params.get('excel_path'),
                    methods=params['methods']
                )
                return stats
            else:
                # 單張處理
                orientation = None
                if any([params.get('omega'), params.get('phi'), params.get('kappa')]):
                    orientation = (
                        params.get('omega', 0.0),
                        params.get('phi', 0.0),
                        params.get('kappa', 0.0)
                    )

                results = tool_instance.process_single_image(
                    image_path=params['image_path'],
                    label_path=params.get('label_path'),
                    orientation=orientation,
                    methods=params['methods'],
                    output_dir=params['output_dir'],
                    visualize=params.get('visualize', False),
                    save_visualization=params.get('save_visualization', False)
                )

                total_generated = sum(len(aug_list)
                                      for aug_list in results.values())
                return {'total_generated': total_generated, 'results': results}

        except Exception as e:
            self.error.emit(str(e))
            return None


class PanoramaAugmenterWidget(BaseToolWidget):
    """全景圖像擴增器界面"""

    def get_tool_title(self) -> str:
        return "全景圖像擴增器"

    def create_content(self):
        """創建內容區域"""
        # 創建左右分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStretchFactor(0, 7)  # 左邊佔7成
        main_splitter.setStretchFactor(1, 3)  # 右邊佔3成

        # 左側面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # 輸入設置組
        input_group = QGroupBox("輸入設置")
        input_layout = QFormLayout(input_group)

        # 圖像路徑
        image_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.textChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        browse_image_button = QPushButton("瀏覽")
        browse_image_button.clicked.connect(
            lambda: self.browse_file(
                self.image_path_edit,
                "圖像文件 (*.jpg *.jpeg *.png *.bmp);;所有文件 (*.*)"
            )
        )
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(browse_image_button)
        input_layout.addRow("圖像路徑:", image_layout)

        # 標籤路徑（可選）
        label_layout = QHBoxLayout()
        self.label_path_edit = QLineEdit()
        browse_label_button = QPushButton("瀏覽")
        browse_label_button.clicked.connect(
            lambda: self.browse_file(
                self.label_path_edit,
                "JSON文件 (*.json);;所有文件 (*.*)"
            )
        )
        label_layout.addWidget(self.label_path_edit)
        label_layout.addWidget(browse_label_button)
        input_layout.addRow("標籤路徑 (可選):", label_layout)

        # Excel路徑（批量處理）
        excel_layout = QHBoxLayout()
        self.excel_path_edit = QLineEdit()
        self.excel_path_edit.textChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        browse_excel_button = QPushButton("瀏覽")
        browse_excel_button.clicked.connect(
            lambda: self.browse_file(
                self.excel_path_edit,
                "Excel文件 (*.xlsx *.xls);;所有文件 (*.*)"
            )
        )
        excel_layout.addWidget(self.excel_path_edit)
        excel_layout.addWidget(browse_excel_button)
        input_layout.addRow("Excel外方位 (批量):", excel_layout)

        left_layout.addWidget(input_group)

        # 外方位參數組
        orientation_group = QGroupBox("外方位參數")
        orientation_layout = QFormLayout(orientation_group)

        # Omega (俯仰角)
        self.omega_spinbox = QDoubleSpinBox()
        self.omega_spinbox.setRange(-180.0, 180.0)
        self.omega_spinbox.setValue(0.0)
        self.omega_spinbox.setDecimals(2)
        self.omega_spinbox.valueChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        orientation_layout.addRow("Omega (俯仰角, 度):", self.omega_spinbox)

        # Phi (橫滾角)
        self.phi_spinbox = QDoubleSpinBox()
        self.phi_spinbox.setRange(-180.0, 180.0)
        self.phi_spinbox.setValue(0.0)
        self.phi_spinbox.setDecimals(2)
        self.phi_spinbox.valueChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        orientation_layout.addRow("Phi (橫滾角, 度):", self.phi_spinbox)

        # Kappa (偏航角)
        self.kappa_spinbox = QDoubleSpinBox()
        self.kappa_spinbox.setRange(-180.0, 180.0)
        self.kappa_spinbox.setValue(0.0)
        self.kappa_spinbox.setDecimals(2)
        self.kappa_spinbox.valueChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        orientation_layout.addRow("Kappa (偏航角, 度):", self.kappa_spinbox)

        left_layout.addWidget(orientation_group)

        # 擴增設置組
        augment_group = QGroupBox("擴增設置")
        augment_layout = QVBoxLayout(augment_group)

        # 擴增方法
        methods_label = QLabel("擴增方法:")
        augment_layout.addWidget(methods_label)

        self.method_checkboxes = {}
        methods = [
            ("orientation", "基於外方位的視角擴增（推薦）"),
            ("rotation", "隨機旋轉擴增"),
            ("perspective", "隨機視角變換"),
            ("tilt", "傾斜擴增"),
            ("multi_angle", "多角度擴增")
        ]

        for method, description in methods:
            checkbox = QCheckBox(f"{method} - {description}")
            if method == "orientation":
                checkbox.setChecked(True)
            checkbox.toggled.connect(self.update_panorama_preview)  # 添加預覽更新
            self.method_checkboxes[method] = checkbox
            augment_layout.addWidget(checkbox)

        # 變化數量
        params_layout = QFormLayout()
        self.num_variations_spinbox = QSpinBox()
        self.num_variations_spinbox.setRange(1, 20)
        self.num_variations_spinbox.setValue(8)
        self.num_variations_spinbox.valueChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        params_layout.addRow("變化數量:", self.num_variations_spinbox)

        # 最大傾斜角度
        self.max_tilt_spinbox = QDoubleSpinBox()
        self.max_tilt_spinbox.setRange(0.0, 90.0)
        self.max_tilt_spinbox.setValue(20.0)
        self.max_tilt_spinbox.valueChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        params_layout.addRow("最大傾斜角度:", self.max_tilt_spinbox)

        # 角度步長
        self.angle_step_spinbox = QDoubleSpinBox()
        self.angle_step_spinbox.setRange(1.0, 90.0)
        self.angle_step_spinbox.setValue(45.0)
        self.angle_step_spinbox.valueChanged.connect(
            self.update_panorama_preview)  # 添加預覽更新
        params_layout.addRow("角度步長:", self.angle_step_spinbox)

        augment_layout.addLayout(params_layout)
        left_layout.addWidget(augment_group)

        # 輸出設置組
        output_group = QGroupBox("輸出設置")
        output_layout = QFormLayout(output_group)

        # 輸出目錄
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        browse_output_button = QPushButton("瀏覽")
        browse_output_button.clicked.connect(
            lambda: self.browse_directory(self.output_dir_edit)
        )
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(browse_output_button)
        output_layout.addRow("輸出目錄:", output_dir_layout)

        # 可視化選項
        self.visualize_checkbox = QCheckBox("顯示可視化結果")
        self.visualize_checkbox.toggled.connect(
            self.update_panorama_preview)  # 添加預覽更新
        output_layout.addRow("", self.visualize_checkbox)

        self.save_visualization_checkbox = QCheckBox("保存可視化圖像")
        output_layout.addRow("", self.save_visualization_checkbox)

        # 批量處理模式
        self.batch_mode_checkbox = QCheckBox("批量處理模式")
        self.batch_mode_checkbox.toggled.connect(
            self.update_panorama_preview)  # 添加預覽更新
        output_layout.addRow("", self.batch_mode_checkbox)

        left_layout.addWidget(output_group)

        # 右側面板 - 全景圖像預覽
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 預覽組
        preview_group = QGroupBox("全景圖像預覽")
        preview_layout = QVBoxLayout(preview_group)

        # 預覽控制按鈕
        preview_control_layout = QHBoxLayout()
        self.update_preview_button = QPushButton("更新預覽")
        self.update_preview_button.clicked.connect(
            self.update_panorama_preview)
        preview_control_layout.addWidget(self.update_preview_button)
        preview_control_layout.addStretch()
        preview_layout.addLayout(preview_control_layout)

        # 圖像顯示區域
        preview_layout.addWidget(QLabel("預覽圖像:"))
        self.preview_image_label = QLabel()
        self.preview_image_label.setMinimumHeight(400)
        self.preview_image_label.setMaximumHeight(400)
        self.preview_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_image_label.setStyleSheet(
            "border: 1px solid #cccccc; background-color: #f8f9fa;")
        self.preview_image_label.setText("請設定圖像路徑和擴增方法以顯示預覽")
        preview_layout.addWidget(self.preview_image_label)

        # 預覽信息標籤
        self.preview_info_label = QLabel()
        self.preview_info_label.setWordWrap(True)
        self.preview_info_label.setStyleSheet(
            "color: #666666; font-size: 12px;")
        preview_layout.addWidget(self.preview_info_label)

        # 參數顯示區域
        params_display_layout = QFormLayout()
        self.params_omega_label = QLabel("未設定")
        self.params_phi_label = QLabel("未設定")
        self.params_kappa_label = QLabel("未設定")
        self.params_methods_label = QLabel("未設定")
        self.params_variations_label = QLabel("未設定")

        params_display_layout.addRow("Omega:", self.params_omega_label)
        params_display_layout.addRow("Phi:", self.params_phi_label)
        params_display_layout.addRow("Kappa:", self.params_kappa_label)
        params_display_layout.addRow("擴增方法:", self.params_methods_label)
        params_display_layout.addRow("變化數量:", self.params_variations_label)

        preview_layout.addLayout(params_display_layout)

        right_layout.addWidget(preview_group)
        right_layout.addStretch()

        # 添加到分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)

        self.content_layout.addWidget(main_splitter)

        # 初始化預覽變數
        self._current_preview_pixmap = None
        self._updating_preview = False

        # 添加防抖定時器
        self._preview_timer = QTimer()
        self._preview_timer.setSingleShot(True)
        self._preview_timer.timeout.connect(
            self._delayed_update_panorama_preview)

        # 載入配置
        self.load_widget_config()

    def update_panorama_preview(self):
        """更新全景圖像預覽（防抖版本）"""
        # 使用定時器防抖，避免頻繁更新
        self._preview_timer.stop()
        self._preview_timer.start(150)  # 150ms延遲

    def _delayed_update_panorama_preview(self):
        """延遲的全景預覽更新"""
        if self._updating_preview:
            return

        self._updating_preview = True

        try:
            # 首先清理之前的預覽資源
            self._cleanup_preview_pixmap()

            # 更新參數顯示
            self.update_params_display()

            # 檢查是否滿足預覽條件
            if self.should_show_preview():
                self.show_panorama_preview()
            else:
                self.preview_image_label.setText("請設定圖像路徑和擴增方法以顯示預覽")
                self.preview_info_label.setText("")

        except Exception as e:
            self.preview_image_label.setText(f"預覽錯誤: {str(e)}")
            self.preview_info_label.setText("")
            # 確保清理資源
            self._cleanup_preview_pixmap()
        finally:
            self._updating_preview = False

    def update_params_display(self):
        """更新參數顯示"""
        # 外方位參數
        omega = self.omega_spinbox.value()
        phi = self.phi_spinbox.value()
        kappa = self.kappa_spinbox.value()

        self.params_omega_label.setText(f"{omega:.2f}°")
        self.params_phi_label.setText(f"{phi:.2f}°")
        self.params_kappa_label.setText(f"{kappa:.2f}°")

        # 擴增方法
        selected_methods = self.get_selected_methods()
        if selected_methods:
            methods_text = ", ".join(selected_methods)
        else:
            methods_text = "未選擇"
        self.params_methods_label.setText(methods_text)

        # 變化數量
        variations = self.num_variations_spinbox.value()
        self.params_variations_label.setText(str(variations))

    def should_show_preview(self):
        """檢查是否應該顯示預覽"""
        # 檢查是否有選擇的擴增方法
        selected_methods = self.get_selected_methods()
        if not selected_methods:
            return False

        # 檢查是否有圖像路徑或Excel路徑
        has_image = bool(self.image_path_edit.text().strip())
        has_excel = bool(self.excel_path_edit.text().strip())

        if not (has_image or has_excel):
            return False

        # 如果有圖像路徑，檢查文件是否存在
        if has_image:
            image_path = self.image_path_edit.text().strip()
            return os.path.exists(image_path)

        # 如果有Excel路徑，檢查文件是否存在
        if has_excel:
            excel_path = self.excel_path_edit.text().strip()
            return os.path.exists(excel_path)

        return False

    def show_panorama_preview(self):
        """顯示全景圖像預覽"""
        painter = None
        try:
            # 獲取圖像路徑
            image_path = self.image_path_edit.text().strip()

            # 如果沒有直接圖像路徑但有Excel，暫時顯示文字說明
            if not image_path or not os.path.exists(image_path):
                excel_path = self.excel_path_edit.text().strip()
                if excel_path and os.path.exists(excel_path):
                    self.preview_image_label.setText(
                        "Excel批量模式：請在單張圖像路徑中指定範例圖像以顯示預覽")
                    self.preview_info_label.setText(
                        f"Excel檔案: {os.path.basename(excel_path)}")
                else:
                    self.preview_image_label.setText("找不到有效的圖像檔案")
                    self.preview_info_label.setText("")
                return

            # 載入原始圖像
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                self.preview_image_label.setText("無法載入圖像")
                return

            # 創建預覽圖像副本
            self._current_preview_pixmap = QPixmap(pixmap)

            # 創建畫家來繪製擴增效果預覽
            painter = QPainter(self._current_preview_pixmap)
            if not painter.isActive():
                self.preview_image_label.setText("無法創建繪圖設備")
                return

            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 獲取圖像尺寸
            img_width = pixmap.width()
            img_height = pixmap.height()

            # 獲取外方位參數
            omega = self.omega_spinbox.value()
            phi = self.phi_spinbox.value()
            kappa = self.kappa_spinbox.value()

            # 獲取選中的方法
            selected_methods = self.get_selected_methods()

            # 在圖像上疊加參數信息
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.setBrush(QBrush(QColor(0, 0, 0, 128)))  # 半透明黑色背景

            # 繪製信息框
            info_rect = QRectF(10, 10, 300, 120)
            painter.drawRect(info_rect)

            # 繪製文字信息
            painter.setPen(QPen(QColor(255, 255, 255)))
            text_y = 30
            line_height = 20

            painter.drawText(20, text_y, f"Omega: {omega:.2f}°")
            text_y += line_height
            painter.drawText(20, text_y, f"Phi: {phi:.2f}°")
            text_y += line_height
            painter.drawText(20, text_y, f"Kappa: {kappa:.2f}°")
            text_y += line_height
            painter.drawText(20, text_y, f"方法: {', '.join(selected_methods)}")
            text_y += line_height
            painter.drawText(
                20, text_y, f"變化數: {self.num_variations_spinbox.value()}")

            # 如果選擇了特定方法，繪製相應的視覺提示
            if "orientation" in selected_methods:
                # 繪製方向指示器
                painter.setPen(QPen(QColor(0, 255, 0), 3))
                center_x = img_width // 2
                center_y = img_height // 2
                radius = min(img_width, img_height) // 8

                # 根據kappa角度繪製方向箭頭
                import math
                arrow_x = int(center_x + radius * math.cos(math.radians(kappa)))
                arrow_y = int(center_y + radius * math.sin(math.radians(kappa)))
                painter.drawLine(int(center_x), int(center_y), arrow_x, arrow_y)

                # 繪製箭頭頭部
                arrow_size = 10
                painter.drawLine(arrow_x, arrow_y,
                                 int(arrow_x - arrow_size *
                                 math.cos(math.radians(kappa - 30))),
                                 int(arrow_y - arrow_size * math.sin(math.radians(kappa - 30))))
                painter.drawLine(arrow_x, arrow_y,
                                 int(arrow_x - arrow_size *
                                 math.cos(math.radians(kappa + 30))),
                                 int(arrow_y - arrow_size * math.sin(math.radians(kappa + 30))))

            # 安全結束繪畫
            if painter and painter.isActive():
                painter.end()
                painter = None  # 標記為已釋放

            # 縮放圖像以適應顯示區域
            scaled_pixmap = self._current_preview_pixmap.scaled(
                self.preview_image_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # 設置pixmap
            self.preview_image_label.setPixmap(scaled_pixmap)

            # 更新信息
            info_text = f"全景圖像: {os.path.basename(image_path)}\n"
            info_text += f"圖像尺寸: {img_width}x{img_height}\n"
            info_text += f"外方位: Ω={omega:.1f}° Φ={phi:.1f}° Κ={kappa:.1f}°\n"
            info_text += f"選擇方法: {', '.join(selected_methods)}\n"
            info_text += f"將產生 {self.num_variations_spinbox.value()} 個變化"

            self.preview_info_label.setText(info_text)

        except Exception as e:
            self.preview_image_label.setText(f"顯示預覽錯誤: {str(e)}")
            self.preview_info_label.setText("")
        finally:
            # 確保painter被正確釋放（避免重複釋放）
            if painter and painter.isActive():
                try:
                    painter.end()
                except:
                    pass  # 忽略重複釋放錯誤
            # 在painter釋放後再清理pixmap
            if hasattr(self, '_current_preview_pixmap'):
                self._cleanup_preview_pixmap()

    def _cleanup_preview_pixmap(self):
        """安全清理預覽pixmap"""
        if hasattr(self, '_current_preview_pixmap') and self._current_preview_pixmap is not None:
            self._current_preview_pixmap = None

    def get_selected_methods(self) -> List[str]:
        """獲取選中的擴增方法"""
        methods = []
        for method, checkbox in self.method_checkboxes.items():
            if checkbox.isChecked():
                methods.append(method)
        return methods

    def validate_inputs(self) -> bool:
        """驗證輸入"""
        # 檢查圖像路徑（單張模式）或Excel路徑（批量模式）
        if self.batch_mode_checkbox.isChecked():
            excel_path = self.excel_path_edit.text().strip()
            if not excel_path:
                QMessageBox.warning(self, "警告", "批量模式需要指定Excel文件")
                return False
            if not os.path.exists(excel_path):
                QMessageBox.warning(self, "警告", "Excel文件不存在")
                return False
        else:
            image_path = self.image_path_edit.text().strip()
            if not image_path:
                QMessageBox.warning(self, "警告", "請選擇圖像文件")
                return False
            if not os.path.exists(image_path):
                QMessageBox.warning(self, "警告", "圖像文件不存在")
                return False

        # 檢查輸出目錄
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "請選擇輸出目錄")
            return False

        # 檢查擴增方法
        if not self.get_selected_methods():
            QMessageBox.warning(self, "警告", "請至少選擇一種擴增方法")
            return False

        return True

    def get_tool_params(self) -> Dict[str, Any]:
        """獲取工具參數"""
        return {
            'image_path': self.image_path_edit.text().strip(),
            'label_path': self.label_path_edit.text().strip() or None,
            'excel_path': self.excel_path_edit.text().strip() or None,
            'output_dir': self.output_dir_edit.text().strip(),
            'omega': self.omega_spinbox.value(),
            'phi': self.phi_spinbox.value(),
            'kappa': self.kappa_spinbox.value(),
            'methods': self.get_selected_methods(),
            'num_variations': self.num_variations_spinbox.value(),
            'max_tilt': self.max_tilt_spinbox.value(),
            'angle_step': self.angle_step_spinbox.value(),
            'visualize': self.visualize_checkbox.isChecked(),
            'save_visualization': self.save_visualization_checkbox.isChecked(),
            'batch_mode': self.batch_mode_checkbox.isChecked()
        }

    def start_worker_thread(self, params):
        """啟動工作線程"""
        # 創建PanoramaAugmenter實例參數
        panorama_params = {
            'logger': self.main_window.logger,
            'config': self.main_window.config_manager.config
        }

        self.worker_thread = PanoramaWorkerThread(PanoramaAugmenter, params)
        self.worker_thread.log.connect(self.main_window.log)
        self.worker_thread.progress.connect(self.main_window.show_progress)
        self.worker_thread.finished.connect(self.on_worker_finished)
        self.worker_thread.error.connect(self.on_worker_error)
        self.worker_thread.start()

    def on_worker_finished(self, result):
        """工作完成回調"""
        super().on_worker_finished(result)

        # 顯示詳細結果
        if result:
            if 'processed_images' in result:
                # 批量處理結果
                stats_text = f"""
批量處理完成！

統計信息:
- 處理圖像數: {result.get('processed_images', 0)}
- 生成擴增圖像: {result.get('total_augmented', 0)}

詳細結果已保存到輸出目錄。
                """
            else:
                # 單張處理結果
                total_generated = result.get('total_generated', 0)
                stats_text = f"""
單張處理完成！

統計信息:
- 生成擴增圖像: {total_generated}

詳細結果已保存到輸出目錄。
                """

            QMessageBox.information(self, "處理完成", stats_text)

    def reset_settings(self):
        """重置設置"""
        self.image_path_edit.clear()
        self.label_path_edit.clear()
        self.excel_path_edit.clear()
        self.output_dir_edit.clear()
        self.omega_spinbox.setValue(0.0)
        self.phi_spinbox.setValue(0.0)
        self.kappa_spinbox.setValue(0.0)
        self.num_variations_spinbox.setValue(8)
        self.max_tilt_spinbox.setValue(20.0)
        self.angle_step_spinbox.setValue(45.0)
        self.visualize_checkbox.setChecked(False)
        self.save_visualization_checkbox.setChecked(False)
        self.batch_mode_checkbox.setChecked(False)

        # 重置方法選擇
        for method, checkbox in self.method_checkboxes.items():
            checkbox.setChecked(method == "orientation")

    def save_settings(self):
        """保存設置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            config["panorama"]["num_variations"] = self.num_variations_spinbox.value()
            config["panorama"]["max_tilt"] = self.max_tilt_spinbox.value()
            config["panorama"]["angle_step"] = self.angle_step_spinbox.value()
            config["panorama"]["auto_visualize"] = self.visualize_checkbox.isChecked()
            config["panorama"]["save_visualization"] = self.save_visualization_checkbox.isChecked()
            config["panorama"]["methods"] = self.get_selected_methods()
            config["last_output_dir"] = self.output_dir_edit.text().strip()

            super().save_settings()

    def load_widget_config(self):
        """載入界面配置"""
        if self.main_window:
            config = self.main_window.config_manager.config
            panorama_config = config.get("panorama", {})

            self.num_variations_spinbox.setValue(
                panorama_config.get("num_variations", 8))
            self.max_tilt_spinbox.setValue(
                panorama_config.get("max_tilt", 20.0))
            self.angle_step_spinbox.setValue(
                panorama_config.get("angle_step", 45.0))
            self.visualize_checkbox.setChecked(
                panorama_config.get("auto_visualize", False))
            self.save_visualization_checkbox.setChecked(
                panorama_config.get("save_visualization", False))

            # 設置方法選擇
            methods = panorama_config.get("methods", ["orientation"])
            for method, checkbox in self.method_checkboxes.items():
                checkbox.setChecked(method in methods)

            # 設置輸出目錄
            last_output = config.get("last_output_dir", "")
            if last_output:
                self.output_dir_edit.setText(last_output)


class AboutDialog(QDialog):
    """關於對話框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("關於")
        self.setFixedSize(400, 300)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 標題
        title = QLabel("圖像數據集處理工具集")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 版本信息
        version = QLabel("v2.0 PyQt Edition")
        version.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version)

        layout.addWidget(QLabel(""))  # 空行

        # 功能描述
        description = QLabel("""
一個專為計算機視覺任務設計的完整數據集處理解決方案

主要功能：
• 標籤格式轉換
• 標籤編輯和管理  
• 數據集分割
• 圖像增強
• 全景圖像擴增

開發語言: Python
GUI框架: PyQt6
圖像處理: OpenCV, PIL
數據處理: pandas, numpy

© 2024 圖像數據集處理工具集團隊
        """)
        description.setWordWrap(True)
        layout.addWidget(description)

        # 確定按鈕
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)


class MainWindow(QMainWindow):
    """主窗口類"""

    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.theme_manager = ThemeManager(QApplication.instance())
        self.setup_logging()
        self.init_ui()
        self.load_window_settings()

        # 應用主題 - 默認使用黑暗模式
        theme = self.config_manager.config.get("theme", "dark")
        self.theme_manager.is_dark_mode = (theme == "dark")
        self.theme_manager.apply_theme()

        # 更新主題菜單文本
        if self.theme_manager.is_dark_mode:
            self.theme_action.setText("切換到明亮模式")
        else:
            self.theme_action.setText("切換到黑暗模式")

    def setup_logging(self):
        """設置日誌"""
        self.logger = logging.getLogger("ImageDatasetGUI")
        self.logger.setLevel(logging.INFO)

    def init_ui(self):
        """初始化用戶界面"""
        self.setWindowTitle("圖像數據集處理工具集 v2.0 - PyQt Edition")
        self.setMinimumSize(1000, 700)

        # 創建菜單欄
        self.create_menu_bar()

        # 創建狀態欄
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 創建主窗口部件
        self.create_main_widget()

    def create_menu_bar(self):
        """創建菜單欄"""
        menubar = self.menuBar()

        # 文件菜單
        file_menu = menubar.addMenu("文件")

        new_config_action = QAction("新建配置", self)
        new_config_action.triggered.connect(self.new_config)
        file_menu.addAction(new_config_action)

        load_config_action = QAction("載入配置", self)
        load_config_action.triggered.connect(self.load_config)
        file_menu.addAction(load_config_action)

        save_config_action = QAction("保存配置", self)
        save_config_action.triggered.connect(self.save_config)
        file_menu.addAction(save_config_action)

        file_menu.addSeparator()

        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 視圖菜單
        view_menu = menubar.addMenu("視圖")

        self.theme_action = QAction("切換到黑暗模式", self)
        self.theme_action.triggered.connect(self.toggle_theme)
        view_menu.addAction(self.theme_action)

        # 工具菜單
        tools_menu = menubar.addMenu("工具")

        clear_log_action = QAction("清除日誌", self)
        clear_log_action.triggered.connect(self.clear_log)
        tools_menu.addAction(clear_log_action)

        open_output_action = QAction("開啟輸出目錄", self)
        open_output_action.triggered.connect(self.open_output_directory)
        tools_menu.addAction(open_output_action)

        # 幫助菜單
        help_menu = menubar.addMenu("幫助")

        about_action = QAction("關於", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_main_widget(self):
        """創建主窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 創建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：工具標籤頁
        self.tab_widget = QTabWidget()
        self.create_tool_tabs()
        splitter.addWidget(self.tab_widget)

        # 下半部分：日誌和進度
        bottom_widget = self.create_bottom_panel()
        splitter.addWidget(bottom_widget)

        # 設置分割器比例
        splitter.setSizes([600, 200])

        main_layout.addWidget(splitter)

    def create_tool_tabs(self):
        """創建工具標籤頁"""
        # 標籤格式轉換器
        self.converter_widget = AnnotationConverterWidget()
        self.converter_widget.set_main_window(self)
        self.tab_widget.addTab(self.converter_widget, "標籤格式轉換器")

        # 標籤編輯器
        self.editor_widget = AnnotationEditorWidget()
        self.editor_widget.set_main_window(self)
        self.tab_widget.addTab(self.editor_widget, "標籤編輯器")

        # 數據集分割器
        self.divider_widget = DatasetDividerWidget()
        self.divider_widget.set_main_window(self)
        self.tab_widget.addTab(self.divider_widget, "數據集分割器")

        # 圖像增強器
        self.augmenter_widget = ImageAugmenterWidget()
        self.augmenter_widget.set_main_window(self)
        self.tab_widget.addTab(self.augmenter_widget, "圖像增強器")

        # 全景圖像擴增器
        self.panorama_widget = PanoramaAugmenterWidget()
        self.panorama_widget.set_main_window(self)
        self.tab_widget.addTab(self.panorama_widget, "全景圖像擴增器")

    def create_bottom_panel(self) -> QWidget:
        """創建底部面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 日誌區域
        log_group = QGroupBox("處理日誌")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_group)

        # 設置日誌處理器
        log_handler = LogHandler(self.log_text)
        log_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(log_handler)

        return widget

    def log(self, message: str):
        """記錄日誌"""
        self.logger.info(message)

    def show_progress(self, value: int):
        """顯示進度"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(value)
        if value >= 100:
            QTimer.singleShot(
                2000, lambda: self.progress_bar.setVisible(False))

    def toggle_theme(self):
        """切換主題"""
        self.theme_manager.toggle_theme()

        # 更新菜單文本
        if self.theme_manager.is_dark_mode:
            self.theme_action.setText("切換到明亮模式")
            self.config_manager.config["theme"] = "dark"
        else:
            self.theme_action.setText("切換到黑暗模式")
            self.config_manager.config["theme"] = "light"

        self.config_manager.save_config()

    def new_config(self):
        """新建配置"""
        reply = QMessageBox.question(
            self, "新建配置",
            "這將重置所有設置為默認值，確定繼續嗎？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.config_manager.config = self.config_manager.get_default_config()
            self.config_manager.save_config()
            QMessageBox.information(self, "成功", "配置已重置為默認值")

    def load_config(self):
        """載入配置"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "載入配置文件", "", "JSON文件 (*.json);;所有文件 (*.*)"
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config_manager.config = json.load(f)
                QMessageBox.information(self, "成功", "配置已載入")
            except Exception as e:
                QMessageBox.critical(self, "錯誤", f"載入配置失敗: {e}")

    def save_config(self):
        """保存配置"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存配置文件", "", "JSON文件 (*.json);;所有文件 (*.*)"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config_manager.config, f,
                              ensure_ascii=False, indent=2)
                QMessageBox.information(self, "成功", "配置已保存")
            except Exception as e:
                QMessageBox.critical(self, "錯誤", f"保存配置失敗: {e}")

    def clear_log(self):
        """清除日誌"""
        self.log_text.clear()

    def open_output_directory(self):
        """開啟輸出目錄"""
        output_dir = self.config_manager.config.get("last_output_dir", "")
        if output_dir and os.path.exists(output_dir):
            QDesktopServices.openUrl(QUrl.fromLocalFile(output_dir))
        else:
            QMessageBox.warning(self, "警告", "輸出目錄不存在")

    def show_about(self):
        """顯示關於對話框"""
        dialog = AboutDialog(self)
        dialog.exec()

    def load_window_settings(self):
        """載入窗口設置"""
        geometry = self.config_manager.config.get("window_geometry", {})
        if geometry:
            self.resize(geometry.get("width", 1400),
                        geometry.get("height", 900))
            self.move(geometry.get("x", 100), geometry.get("y", 100))

    def save_window_settings(self):
        """保存窗口設置"""
        self.config_manager.config["window_geometry"] = {
            "width": self.width(),
            "height": self.height(),
            "x": self.x(),
            "y": self.y()
        }
        self.config_manager.save_config()

    def closeEvent(self, event):
        """窗口關閉事件"""
        self.save_window_settings()
        event.accept()


def main():
    """主函數"""
    app = QApplication(sys.argv)
    app.setApplicationName("圖像數據集處理工具集")
    app.setApplicationVersion("2.0")

    # 設置應用程式圖標（如果有的話）
    # app.setWindowIcon(QIcon("icon.png"))

    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
