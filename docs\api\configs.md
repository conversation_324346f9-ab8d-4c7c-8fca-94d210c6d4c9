# 配置系統API文檔

*自動生成於: 2025-06-20 22:16:15*

## 概覽

本框架提供了 **37** 個配置類，支援完整的配置驅動開發。

### CIConfig

**模組**: `scripts/ci_cd_automation.py`

**說明**: CI/CD配置...

---

### BenchmarkConfig

**模組**: `AI模型建構訓練驗證/model_create/benchmark/performance_benchmark.py`

**說明**: 基準測試配置...

---

### DecoderConfig

**模組**: `AI模型建構訓練驗證/model_create/core/base_decoder.py`

**說明**: 解碼器配置類...

---

### EncoderConfig

**模組**: `AI模型建構訓練驗證/model_create/core/base_encoder.py`

**說明**: 編碼器配置類...

---

### EnhancedModelConfig

**模組**: `AI模型建構訓練驗證/model_create/core/enhanced_factory.py`

**說明**: 增強模型配置類...

---

### DatasetConfig

**模組**: `AI模型建構訓練驗證/model_create/data/unified_dataset.py`

**說明**: 統一的資料集配置類...

---

### RayTrainingConfig

**模組**: `AI模型建構訓練驗證/model_create/distributed/ray_ai_integration.py`

**說明**: Ray分散式訓練配置...

---

### EncoderConfig

**模組**: `AI模型建構訓練驗證/model_create/encoder/unified_encoder_factory.py`

**說明**: 統一編碼器配置...

---

### InvertedResidualConfig

**模組**: `AI模型建構訓練驗證/model_create/encoders/cnn_family.py`

**說明**: 倒殘差塊配置...

---

### ClassConfig

**模組**: `AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py`

**說明**: 類別配置...

---

### EnhancedYOLOConfig

**模組**: `AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py`

**說明**: 增強YOLO配置...

---

### DetectionConfig

**模組**: `AI模型建構訓練驗證/model_create/inference/object_detection.py`

**說明**: 檢測配置類...

---

### MetricsConfig

**模組**: `AI模型建構訓練驗證/model_create/training/metrics.py`

**說明**: 指標配置...

---

### OptimizerConfig

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 優化器配置...

---

### SchedulerConfig

**模組**: `AI模型建構訓練驗證/model_create/training/optimizers.py`

**說明**: 調度器配置...

---

### SAMTrainingConfig

**模組**: `AI模型建構訓練驗證/model_create/training/sam_finetuning_trainer.py`

**說明**: SAM訓練配置...

---

### TrainingConfig

**模組**: `AI模型建構訓練驗證/model_create/training/trainer.py`

**說明**: 訓練配置...

---

### DatasetConfig

**模組**: `AI模型建構訓練驗證/model_create/util/base_dataset.py`

**說明**: 數據集配置類...

---

### TrainingConfig

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 訓練配置...

---

### DataConfig

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 數據配置...

---

### LossConfig

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 損失函數配置...

---

### OptimizerConfig

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 優化器配置...

---

### AugmentationConfig

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 數據增強配置...

---

### EvaluationConfig

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 評估配置...

---

### Config

**模組**: `AI模型建構訓練驗證/model_create/util/config_manager.py`

**說明**: 主配置類...

---

### DatasetConfig

**模組**: `AI模型建構訓練驗證/model_create/util/dataset.py`

**說明**: 數據集配置類...

---

### DocConfig

**模組**: `AI模型建構訓練驗證/model_create/util/doc_generator.py`

**說明**: 文檔生成配置...

---

### InvertedResidualConfig

**模組**: `AI模型建構訓練驗證/model_create/encoder/CNN/CSP_Mobilenet.py`

**說明**: 無文檔說明...

---

### InvertedResidualConfig

**模組**: `AI模型建構訓練驗證/model_create/encoder/CNN/Mobilenet.py`

**說明**: 無文檔說明...

---

### VisionMambaConfig

**模組**: `AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py`

**說明**: Vision Mamba配置...

---

### CSPIFormerConfig

**模組**: `AI模型建構訓練驗證/model_create/encoder/VIT/unified_csp_iformer.py`

**說明**: 統一的CSP_IFormer配置類...

---

### Config

**模組**: `AI模型建構訓練驗證/model_create/full_model/mrcnn/config.py`

**說明**: Base configuration class. For custom configurations, create a
sub-class that inherits from this one and override properties
that need to be changed....

---

### SegMANConfig

**模組**: `AI模型建構訓練驗證/model_create/full_model/segman/segman_factory.py`

**說明**: Configuration class for SegMAN models...

---

### BaseConfig

**模組**: `資料前處理/shared/config_manager.py`

**說明**: 基礎配置類...

---

### ConverterConfig

**模組**: `資料前處理/shared/config_manager.py`

**說明**: 轉換器配置...

---

### AugmenterConfig

**模組**: `資料前處理/shared/config_manager.py`

**說明**: 增強器配置...

---

### DividerConfig

**模組**: `資料前處理/shared/config_manager.py`

**說明**: 數據集分割器配置...

---

