#!/usr/bin/env python3
"""
🧪 小批次LabelMe JSON測試腳本
只處理前3張圖像來快速測試LabelMe功能
"""

import shutil
import os
from pathlib import Path

# 設定路徑
source_dir = Path(r"D:\image\5_test_image\test_2_org")
test_dir = Path(r"D:\image\5_test_image\test_labelme_small")
output_dir = Path(r"D:\image\5_test_image\test_labelme_out")

def create_small_test_batch():
    """創建小批次測試資料"""
    print("🧪 創建小批次測試資料...")
    
    # 清理並創建測試目錄
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir(parents=True, exist_ok=True)
    
    if output_dir.exists():
        shutil.rmtree(output_dir)
    
    # 複製前3張圖像
    image_files = list(source_dir.glob("*.jpg"))[:3]
    
    for img_file in image_files:
        shutil.copy2(img_file, test_dir)
        
        # 如果有對應的json文件也複製
        json_file = source_dir / f"{img_file.stem}.json"
        if json_file.exists():
            shutil.copy2(json_file, test_dir)
    
    print(f"✅ 已複製 {len(image_files)} 張圖像到測試目錄")
    print(f"📁 測試輸入: {test_dir}")
    print(f"📁 測試輸出: {output_dir}")
    
    return test_dir, output_dir

def main():
    """主函數"""
    print("🧪 小批次LabelMe JSON測試")
    print("=" * 50)
    
    test_input, test_output = create_small_test_batch()
    
    print(f"\n🚀 請修改 run_unified_yolo.py 中的路徑:")
    print(f"input_path = r\"{test_input}\"")
    print(f"output_path = r\"{test_output}\"")
    print(f"\n然後運行: python run_unified_yolo.py")
    print(f"\n這樣只需要處理3張圖像，可以快速看到LabelMe JSON結果！")

if __name__ == "__main__":
    main()