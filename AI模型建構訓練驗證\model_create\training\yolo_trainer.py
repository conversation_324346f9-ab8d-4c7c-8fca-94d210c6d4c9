#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO訓練器模組

基於0_yolo.ipynb建構的YOLOv12x訓練系統
支援目標檢測、完整的資料格式轉換和評估管線
"""

import os
import yaml
import logging
import datetime
import torch
from typing import Dict, Any, Optional, List
from pathlib import Path
import numpy as np
from ultralytics import YOLO

from ..util.Dataset_read import convert_labelme_to_yolo
from .base_trainer import BaseTrainer


class YOLOTrainer(BaseTrainer):
    """
    YOLO訓練器類
    
    基於Ultralytics框架的YOLOv12x訓練系統
    支援從LabelMe格式自動轉換到YOLO格式並進行訓練
    """
    
    def __init__(self,
                 model_name: str = "yolov12x",
                 image_size: int = 640,
                 batch_size: int = 4,
                 epochs: int = 500,
                 learning_rate: float = 1e-3,
                 device: str = "auto",
                 output_dir: str = "./run_data",
                 class_mapping: Optional[Dict[str, int]] = None,
                 pretrained_weights: Optional[str] = None):
        """
        初始化YOLO訓練器
        
        參數:
            model_name: 模型名稱 (yolov12x, yolov11s, 等)
            image_size: 圖像大小
            batch_size: 批次大小
            epochs: 訓練輪數
            learning_rate: 學習率
            device: 計算設備
            output_dir: 輸出目錄
            class_mapping: 類別映射字典
            pretrained_weights: 預訓練權重路徑
        """
        super().__init__(device)
        
        self.model_name = model_name
        self.image_size = image_size
        self.batch_size = batch_size
        self.epochs = epochs
        self.learning_rate = learning_rate
        self.output_dir = Path(output_dir)
        self.pretrained_weights = pretrained_weights
        
        # 默認類別映射（可以覆蓋）
        self.class_mapping = class_mapping or {
            'bird': 0, 'block': 1, 'retaining seat': 2, 
            'treefish': 3, 'tpe': 4
        }
        
        # 設置輸出路徑
        current_time = datetime.datetime.now()
        timestamp = current_time.strftime("%Y%m%d_%H%M%S")
        self.run_name = f"{model_name}_{timestamp}"
        self.run_dir = self.output_dir / self.run_name
        
        # 創建目錄結構
        self._setup_directories()
        
        # 初始化模型
        self.model = None
        self.dataset_yaml = None
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"YOLO訓練器初始化完成: {self.run_name}")
    
    def _setup_directories(self):
        """設置目錄結構"""
        self.run_dir.mkdir(parents=True, exist_ok=True)
        (self.run_dir / "img").mkdir(exist_ok=True)
        (self.run_dir / "weight").mkdir(exist_ok=True)
        (self.run_dir / "results").mkdir(exist_ok=True)
    
    def prepare_dataset(self, data_paths: Dict[str, str]) -> str:
        """
        準備YOLO格式資料集
        
        參數:
            data_paths: 資料路徑字典 {'train': path, 'val': path, 'test': path}
            
        返回:
            dataset.yaml路徑
        """
        self.logger.info("開始準備YOLO格式資料集...")
        
        # 檢查並轉換每個資料集
        for split, data_dir in data_paths.items():
            if not os.path.exists(data_dir):
                self.logger.warning(f"{split} 資料目錄不存在: {data_dir}")
                continue
            
            # 檢查是否已是YOLO格式
            labels_dir = os.path.join(data_dir, 'labels')
            images_dir = os.path.join(data_dir, 'images')
            
            if not (os.path.exists(labels_dir) and os.path.exists(images_dir)):
                self.logger.info(f"轉換 {split} 資料集從 LabelMe 到 YOLO 格式")
                
                success_count, mapping = convert_labelme_to_yolo(
                    input_dir=data_dir,
                    output_root=data_dir,
                    class_mapping=self.class_mapping,
                    copy_images=True,
                    log_enabled=True
                )
                
                self.logger.info(f"已轉換 {success_count} 個 {split} 標註檔案")
            else:
                self.logger.info(f"{split} 資料集已是 YOLO 格式")
        
        # 創建dataset.yaml
        return self._create_dataset_yaml(data_paths)
    
    def _create_dataset_yaml(self, data_paths: Dict[str, str]) -> str:
        """
        創建YOLO資料集配置檔案
        
        參數:
            data_paths: 資料路徑字典
            
        返回:
            yaml檔案路徑
        """
        yaml_path = self.run_dir / 'dataset.yaml'
        
        # 獲取資料集根目錄（假設train, val, test在同一根目錄下）
        root_dir = str(Path(data_paths['train']).parent)
        
        # 建立資料集設定檔內容
        dataset_config = {
            'path': root_dir,  # 資料集根目錄
            'train': os.path.join(os.path.basename(data_paths['train']), 'images'),
            'val': os.path.join(os.path.basename(data_paths['val']), 'images'),
            'names': {v: k for k, v in self.class_mapping.items()}  # 反轉映射
        }
        
        # 如果有測試集，也加入
        if 'test' in data_paths:
            dataset_config['test'] = os.path.join(os.path.basename(data_paths['test']), 'images')
        
        # 寫入YAML檔案
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"已建立資料集設定檔: {yaml_path}")
        self.dataset_yaml = str(yaml_path)
        return str(yaml_path)
    
    def setup_model(self, resume: bool = False) -> YOLO:
        """
        設置YOLO模型
        
        參數:
            resume: 是否從檢查點恢復
            
        返回:
            YOLO模型實例
        """
        self.logger.info(f"設置 {self.model_name} 模型...")
        
        if resume and self.pretrained_weights and os.path.exists(self.pretrained_weights):
            # 從檢查點恢復
            self.model = YOLO(self.pretrained_weights)
            self.logger.info(f"從檢查點恢復模型: {self.pretrained_weights}")
        else:
            # 載入預訓練模型或創建新模型
            if self.model_name.endswith('.pt') and os.path.exists(self.model_name):
                self.model = YOLO(self.model_name)
            else:
                # 使用默認的預訓練權重
                self.model = YOLO(f'{self.model_name}.pt')
            
            self.logger.info(f"載入預訓練 {self.model_name} 模型")
        
        return self.model
    
    def train(self, 
              data_paths: Dict[str, str],
              resume: bool = False,
              **kwargs) -> Dict[str, Any]:
        """
        執行YOLO模型訓練
        
        參數:
            data_paths: 資料路徑字典
            resume: 是否恢復訓練
            **kwargs: 其他訓練參數
            
        返回:
            訓練結果字典
        """
        # 準備資料集
        dataset_yaml = self.prepare_dataset(data_paths)
        
        # 設置模型
        self.setup_model(resume=resume)
        
        # 設定訓練參數
        train_args = {
            'data': dataset_yaml,
            'epochs': self.epochs,
            'imgsz': self.image_size,
            'batch': self.batch_size,
            'device': 0 if torch.cuda.is_available() else 'cpu',
            'project': str(self.output_dir),
            'name': self.run_name,
            'exist_ok': True,
            'pretrained': True,
            'save': True,
            'plots': True,
            'auto_augment': None,
            'hsv_h': 0.015,
            'hsv_s': 1.0,
            'hsv_v': 0.7,
            'degrees': 180.0,
            'translate': 0.4,
            'scale': 0.1,
            'shear': 30,
            'perspective': 0.0005,
            'flipud': 0.5,
            'fliplr': 0.5,
            'erasing': 0.4,
            'mosaic': 0.5,
            'cutmix': 0,
            'patience': self.epochs,
            'cos_lr': True,
            'seed': 3407,
            'resume': resume
        }
        
        # 更新自定義參數
        train_args.update(kwargs)
        
        self.logger.info(f"開始訓練 {self.model_name} 模型...")
        self.logger.info(f"訓練參數: {train_args}")
        
        # 執行訓練
        try:
            results = self.model.train(**train_args)
            
            self.logger.info(f"{self.model_name} 訓練完成")
            
            # 保存訓練結果
            result_dict = {
                'model_name': self.model_name,
                'run_name': self.run_name,
                'epochs': self.epochs,
                'best_weights': str(self.run_dir / 'weights' / 'best.pt'),
                'last_weights': str(self.run_dir / 'weights' / 'last.pt'),
                'results': results
            }
            
            return result_dict
            
        except Exception as e:
            self.logger.error(f"訓練失敗: {str(e)}")
            raise
    
    def validate(self, 
                 weights_path: str,
                 data_yaml: str = None,
                 **kwargs) -> Dict[str, Any]:
        """
        驗證模型性能
        
        參數:
            weights_path: 權重檔案路徑
            data_yaml: 資料集配置檔案
            **kwargs: 其他驗證參數
            
        返回:
            驗證結果字典
        """
        self.logger.info(f"開始驗證模型: {weights_path}")
        
        # 載入模型
        model = YOLO(weights_path)
        
        # 使用訓練時的資料集配置
        if data_yaml is None:
            data_yaml = self.dataset_yaml
        
        # 執行驗證
        val_args = {
            'data': data_yaml,
            'imgsz': self.image_size,
            'batch': self.batch_size,
            'device': 0 if torch.cuda.is_available() else 'cpu',
            'save_json': True,
            'save_hybrid': True,
            'conf': 0.001,
            'iou': 0.6,
            'max_det': 300,
            'half': False,
            'plots': True
        }
        
        val_args.update(kwargs)
        
        try:
            results = model.val(**val_args)
            
            self.logger.info("模型驗證完成")
            
            return {
                'validation_results': results,
                'metrics': results.results_dict if hasattr(results, 'results_dict') else {}
            }
            
        except Exception as e:
            self.logger.error(f"驗證失敗: {str(e)}")
            raise
    
    def predict(self,
                source: str,
                weights_path: str,
                **kwargs) -> List[Dict]:
        """
        使用訓練好的模型進行預測
        
        參數:
            source: 輸入圖像路徑或目錄
            weights_path: 權重檔案路徑
            **kwargs: 其他預測參數
            
        返回:
            預測結果列表
        """
        self.logger.info(f"開始預測: {source}")
        
        # 載入模型
        model = YOLO(weights_path)
        
        # 設定預測參數
        predict_args = {
            'source': source,
            'imgsz': self.image_size,
            'conf': 0.25,
            'iou': 0.7,
            'max_det': 300,
            'save': True,
            'save_txt': True,
            'save_conf': True,
            'project': str(self.run_dir),
            'name': 'predict',
            'exist_ok': True
        }
        
        predict_args.update(kwargs)
        
        try:
            results = model.predict(**predict_args)
            
            self.logger.info("預測完成")
            
            # 處理結果
            predictions = []
            for result in results:
                pred_dict = {
                    'image_path': result.path,
                    'boxes': result.boxes,
                    'names': result.names,
                    'orig_img': result.orig_img,
                    'save_dir': result.save_dir
                }
                predictions.append(pred_dict)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"預測失敗: {str(e)}")
            raise
    
    def export_model(self,
                     weights_path: str,
                     format: str = 'onnx',
                     **kwargs) -> str:
        """
        導出模型到其他格式
        
        參數:
            weights_path: 權重檔案路徑
            format: 導出格式 ('onnx', 'tensorrt', 'coreml', 等)
            **kwargs: 其他導出參數
            
        返回:
            導出檔案路徑
        """
        self.logger.info(f"導出模型到 {format} 格式")
        
        # 載入模型
        model = YOLO(weights_path)
        
        # 設定導出參數
        export_args = {
            'format': format,
            'imgsz': self.image_size,
            'optimize': True,
            'half': False,
            'int8': False,
            'dynamic': False,
            'simplify': True,
            'opset': 17
        }
        
        export_args.update(kwargs)
        
        try:
            exported_path = model.export(**export_args)
            
            self.logger.info(f"模型已導出至: {exported_path}")
            
            return exported_path
            
        except Exception as e:
            self.logger.error(f"模型導出失敗: {str(e)}")
            raise
    
    def get_training_summary(self) -> Dict[str, Any]:
        """
        獲取訓練摘要信息
        
        返回:
            訓練摘要字典
        """
        summary = {
            'model_name': self.model_name,
            'run_name': self.run_name,
            'run_dir': str(self.run_dir),
            'parameters': {
                'epochs': self.epochs,
                'batch_size': self.batch_size,
                'image_size': self.image_size,
                'learning_rate': self.learning_rate,
                'device': str(self.device)
            },
            'class_mapping': self.class_mapping,
            'dataset_yaml': self.dataset_yaml
        }
        
        # 檢查權重檔案是否存在
        best_weights = self.run_dir / 'weights' / 'best.pt'
        last_weights = self.run_dir / 'weights' / 'last.pt'
        
        summary['weights'] = {
            'best_exists': best_weights.exists(),
            'last_exists': last_weights.exists(),
            'best_path': str(best_weights) if best_weights.exists() else None,
            'last_path': str(last_weights) if last_weights.exists() else None
        }
        
        return summary


def create_yolo_trainer(model_name: str = "yolov12x", **kwargs) -> YOLOTrainer:
    """
    創建YOLO訓練器的工廠函數
    
    參數:
        model_name: 模型名稱
        **kwargs: 其他參數
        
    返回:
        YOLOTrainer實例
    """
    return YOLOTrainer(model_name=model_name, **kwargs)


# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建訓練器
    trainer = YOLOTrainer(
        model_name="yolov12x",
        epochs=100,
        batch_size=8,
        image_size=640
    )
    
    # 設定資料路徑
    data_paths = {
        'train': 'path/to/train',
        'val': 'path/to/val',
        'test': 'path/to/test'
    }
    
    try:
        # 執行訓練
        results = trainer.train(data_paths)
        print(f"訓練完成: {results}")
        
        # 驗證模型
        val_results = trainer.validate(results['best_weights'])
        print(f"驗證結果: {val_results}")
        
        # 獲取訓練摘要
        summary = trainer.get_training_summary()
        print(f"訓練摘要: {summary}")
        
    except Exception as e:
        print(f"訓練失敗: {str(e)}")