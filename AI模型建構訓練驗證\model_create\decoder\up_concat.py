# !/usr/bin/env python
# -- coding: utf-8 --
# @Time : 2022/12/14 17:22
# <AUTHOR> liumin
# @File : up_concat_head.py

import torch
import torch.nn as nn
import torch.nn.functional as F

# from decoder.conv_module import ConvModule
# from decoder.base_seg_head import BaseSegHead


class UpConcat(nn.Module):
    """
       IncepFormer: Efficient Inception Transformer with Pyramid Pooling for Semantic Segmentation
    """

    def __init__(self, encoder_channels,segmentation_channels=128):
        super(UpConcat, self).__init__()
        encoder_channels = sum(encoder_channels)
        self.fuse = nn.Conv2d(encoder_channels, segmentation_channels, kernel_size=1)
        # self.linear_fuse = ConvModule(in_channels=sum(self.in_channels), out_channels=self.channels, kernel_size=1, norm_cfg=self.norm_cfg)

    def forward(self, x):
        x = [F.interpolate(xx, size=x[0].size()[2:], mode='bilinear', align_corners=False) for xx in x]
        x = torch.cat(x, dim=1)
        x = self.fuse(x)
        # x = self.linear_fuse(x)
        return x
