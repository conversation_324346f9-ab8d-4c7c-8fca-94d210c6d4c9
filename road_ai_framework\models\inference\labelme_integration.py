#!/usr/bin/env python3
"""
🔧 LabelMe Integration Module
整合LabelMe JSON輸出功能到推理系統中

功能：
- 支援一般推理和切片推理模式
- 自動創建輸出目錄結構
- 批次處理和單張圖像處理
- 與現有推理系統無縫整合
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import numpy as np
import cv2

from .labelme_json_generator import create_labelme_json_generator


class LabelMeIntegration:
    """LabelMe JSON輸出整合器"""

    def __init__(self, config_manager):
        """
        初始化LabelMe整合器

        Args:
            config_manager: 配置管理器，包含labelme_output配置
        """
        self.config = config_manager
        self.enabled = getattr(config_manager.labelme_output,
                               'enable_labelme_output', False)
        self.include_base64_image = getattr(config_manager.labelme_output,
                                            'labelme_include_base64_image', False)
        self.copy_images = getattr(config_manager.labelme_output,
                                   'labelme_copy_images', False)  # 🆕 圖像複製配置

        if self.enabled:
            # 創建LabelMe JSON生成器
            self.generator = create_labelme_json_generator(
                simplify_tolerance=getattr(
                    config_manager.labelme_output, 'labelme_simplify_tolerance', 2.0)
            )

            # 設置輸出目錄
            output_dir_name = getattr(
                config_manager.labelme_output, 'labelme_output_dir', 'labelme_json')
            self.output_dir = Path(
                config_manager.paths.output_path) / output_dir_name

            # 其他配置
            self.min_polygon_points = getattr(
                config_manager.labelme_output, 'labelme_min_polygon_points', 3)
            self.include_confidence = getattr(
                config_manager.labelme_output, 'labelme_include_confidence', False)

            # 準備類別名稱映射
            self.class_names = self._prepare_class_names()

            # 創建輸出目錄
            self._ensure_output_directory()

            logging.info(f"✅ LabelMe JSON output enabled: {self.output_dir}")
        else:
            logging.info("ℹ️ LabelMe JSON output disabled")

    def _prepare_class_names(self) -> Dict[int, str]:
        """準備類別ID到名稱的映射"""
        class_names = {}

        for class_id, class_info in self.config.classes.items():
            if hasattr(class_info, 'name'):
                class_name = class_info.name
            elif isinstance(class_info, dict):
                class_name = class_info.get('name', f'class_{class_id}')
            else:
                class_name = f'class_{class_id}'

            class_names[class_id] = class_name

        return class_names

    def _ensure_output_directory(self):
        """確保輸出目錄存在"""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            logging.info(
                f"📁 LabelMe JSON output directory ready: {self.output_dir}")
        except Exception as e:
            logging.error(f"❌ Failed to create LabelMe output directory: {e}")
            self.enabled = False

    def _process_detection_results(self, detections: List[Dict], image_path: str, resize_ratio: float, resized_image_data: Optional[np.ndarray] = None) -> List[Dict]:
        """
        處理檢測結果，準備用於LabelMe JSON生成

        Args:
            detections: 檢測結果列表
            image_path: 圖像路徑
            resize_ratio: 縮放比例，用於將座標轉換回原始尺寸
            resized_image_data: (可選) 縮放後的圖像數據

        Returns:
            處理後的檢測結果
        """
        processed_detections = []

        excluded_ids = getattr(self.config, 'excluded_class_ids', [])
        excluded_names = getattr(self.config, 'excluded_class_names', [])
        included_ids = getattr(self.config, 'included_class_ids', None)

        for i, detection in enumerate(detections):
            cid = detection.get('class_id', None)
            cname = detection.get('class_name', '')
            if cid is not None and cid in excluded_ids:
                continue
            if cname in excluded_names:
                continue
            if included_ids is not None and (cid is None or cid not in included_ids):
                continue

            has_bbox = 'bbox' in detection and detection['bbox'] is not None and len(
                detection.get('bbox', [])) == 4
            has_mask = 'mask' in detection and detection['mask'] is not None

            if not (has_bbox and has_mask):
                continue

            # 🔥 重要修復：座標已經在unified_yolo_inference.py中正確變換
            # 這裡傳入的檢測結果已經是最終LabelMe座標系統，直接使用即可
            # 不應該進行任何額外的座標變換，避免破壞已經正確的座標
            scaled_bbox = detection['bbox']  # 已變換的座標，直接使用
            print(f"🔍 LabelMe整合器接收座標: bbox={scaled_bbox}, class={detection.get('class_name', 'unknown')}")
            
            # polygon 處理（如有）
            if 'points' in detection:
                scaled_points = detection['points']  # 已變換的座標，直接使用
                if scaled_points:
                    sample_points = scaled_points[:2] if len(scaled_points) > 2 else scaled_points
                    print(f"🔍 LabelMe整合器接收points示例: {sample_points}{'...' if len(scaled_points) > 2 else ''}")
            else:
                scaled_points = None
            # 🔥 修復mask處理：確保mask是numpy array並匹配圖像尺寸
            mask_data = detection['mask']
            
            # 🔍 檢查mask數據類型並進行轉換
            if isinstance(mask_data, list):
                print(f"⚠️ Mask是list類型，長度: {len(mask_data)}, 轉換為numpy array")
                mask_data = np.array(mask_data, dtype=np.uint8)
                print(f"✅ 轉換後mask shape: {mask_data.shape}")
            elif not isinstance(mask_data, np.ndarray):
                print(f"⚠️ Mask是 {type(mask_data)} 類型，嘗試轉換為numpy array")
                try:
                    mask_data = np.array(mask_data, dtype=np.uint8)
                    print(f"✅ 轉換後mask shape: {mask_data.shape}")
                except Exception as e:
                    print(f"❌ Mask轉換失敗: {e}")
                    continue
            else:
                print(f"✅ Mask已是numpy array，shape: {mask_data.shape}")
            
            if resized_image_data is not None:
                # 確保mask尺寸與最終LabelMe圖像一致
                target_h, target_w = resized_image_data.shape[:2]
                if mask_data.shape[:2] != (target_h, target_w):
                    scaled_mask = cv2.resize(
                        mask_data, (target_w, target_h), interpolation=cv2.INTER_NEAREST)
                    print(f"🔧 Mask縮放: {mask_data.shape[:2]} -> {scaled_mask.shape[:2]}")
                else:
                    scaled_mask = mask_data
                    print(f"ℹ️ Mask尺寸已匹配: {mask_data.shape[:2]}")
            else:
                # 沒有提供resize圖像數據，直接使用原始mask
                scaled_mask = mask_data
                print(f"ℹ️ 使用原始mask: {mask_data.shape[:2]}")
            processed_detection = {
                'class_id': detection.get('class_id', 0),
                'confidence': detection.get('confidence', 0.0),
                'bbox': scaled_bbox,
                'mask': scaled_mask
            }
            if scaled_points is not None:
                processed_detection['points'] = scaled_points

            if self.include_confidence:
                class_name = self.class_names.get(
                    processed_detection['class_id'], f"class_{processed_detection['class_id']}")
                confidence = processed_detection['confidence']
                processed_detection['custom_label'] = f"{class_name}_{confidence:.2f}"

            processed_detections.append(processed_detection)

        return processed_detections

    def process_single_image_result(self, image_path: str, detections: List[Dict], resized_image_data: Optional[np.ndarray] = None, resize_ratio: float = 1.0) -> Optional[str]:
        """
        處理單張圖像的結果並生成LabelMe JSON

        Args:
            image_path: 圖像路徑
            detections: 檢測結果列表
            resized_image_data: (可選) 縮小後的圖像數據 (NumPy array)
            resize_ratio: (可選) 縮放比例

        Returns:
            生成的JSON檔案路徑，如果失敗則返回None
        """
        print(f"\n🏷️ LabelMe JSON生成開始...")
        print(f"   📷 圖像: {Path(image_path).name}")
        print(f"   🔍 輸入檢測數量: {len(detections) if detections else 0}")
        print(f"   ⚙️  整合器狀態: {'啟用' if self.enabled else '禁用'}")

        if not self.enabled:
            print(f"❌ LabelMe整合器未啟用，跳過JSON生成")
            return None

        if not detections:
            print(f"❌ 沒有檢測結果，跳過JSON生成")
            return None

        try:
            # 只產生縮小圖像與LabelMe JSON，且座標與縮小圖像一致
            use_resized = True

            # 處理檢測結果，傳入正確的resize_ratio和resized_image_data
            processed_detections = self._process_detection_results(
                detections, image_path, resize_ratio, resized_image_data)

            if not processed_detections:
                print(f"❌ 沒有有效的檢測結果用於LabelMe JSON生成")
                logging.info(
                    f"No valid detections for LabelMe JSON: {Path(image_path).name}")
                return None

            print(f"✅ 檢測結果處理完成，準備生成JSON...")
            print(f"   📊 有效檢測數量: {len(processed_detections)}")
            print(f"   📁 輸出目錄: {self.output_dir}")

            # 只保存縮小後的圖像
            if resized_image_data is not None:
                target_image_path = str(
                    self.output_dir / Path(image_path).name)
                image_data_for_json = resized_image_data
                cv2.imwrite(target_image_path, resized_image_data)
                print(f"   🖼️ 已保存縮小版圖像: {Path(target_image_path).name}")
            else:
                target_image_path = image_path
                image_data_for_json = None

            # 生成JSON檔案
            json_path = self.generator.process_image_results(
                image_path=target_image_path,
                results=processed_detections,
                output_dir=str(self.output_dir),
                class_names=self.class_names,
                include_base64_image=self.include_base64_image
            )

            if json_path:
                logging.info(
                    f"✅ LabelMe JSON generated: {Path(json_path).name}")
                print(f"\n🎉 LabelMe JSON生成成功!")
                print(f"   💾 檔案名稱: {Path(json_path).name}")
                print(f"   📁 完整路徑: {json_path}")
                print(f"   📊 包含檢測: {len(processed_detections)} 個物件")
                return json_path
            else:
                print(f"❌ LabelMe JSON生成失敗")
                logging.warning(
                    f"❌ Failed to generate LabelMe JSON for: {Path(image_path).name}")
                return None

        except Exception as e:
            print(f"❌ LabelMe JSON生成過程發生錯誤: {e}")
            logging.error(f"❌ Error processing single image result: {e}")
            import traceback
            logging.error(f"❌ Detailed error: {traceback.format_exc()}")
            return None

    def process_batch_results(self, batch_results: List[Dict]) -> List[str]:
        """
        批次處理多張圖像的結果

        Args:
            batch_results: 批次結果列表，每個包含image_path和detections

        Returns:
            生成的JSON檔案路徑列表
        """
        if not self.enabled:
            return []

        generated_files = []

        try:
            # 準備批次處理數據
            processed_batch = []

            for result in batch_results:
                image_path = result.get('image_path')
                detections = result.get('detections', [])

                if not image_path or not detections:
                    continue

                # 處理檢測結果
                processed_detections = self._process_detection_results(
                    detections, image_path, 1.0)

                if processed_detections:
                    processed_batch.append({
                        'image_path': image_path,
                        'detections': processed_detections
                    })

            # 使用批次處理方法
            if processed_batch:
                generated_files = self.generator.batch_process_results(
                    batch_results=processed_batch,
                    output_dir=str(self.output_dir),
                    class_names=self.class_names
                )

                logging.info(
                    f"✅ Batch LabelMe JSON generation completed: {len(generated_files)} files")
                print(f"🎊 批次處理完成！總共生成 {len(generated_files)} 個LabelMe JSON檔案")
                print(f"   📁 儲存目錄: {self.output_dir}")

                # 統計檢測物件總數
                total_objects = 0
                for batch_item in processed_batch:
                    total_objects += len(batch_item['detections'])
                print(f"   📊 總檢測物件: {total_objects} 個")

            else:
                logging.info(
                    "ℹ️ No valid detections for batch LabelMe JSON generation")
                print("ℹ️ 批次處理完成，但沒有有效的檢測結果用於生成JSON檔案")

        except Exception as e:
            logging.error(f"❌ Error in batch LabelMe JSON processing: {e}")

        return generated_files

    def _copy_image_to_labelme_dir(self, image_path: str) -> Optional[str]:
        """
        複製圖像到LabelMe目錄，確保JSON和圖像在同一目錄中

        Args:
            image_path: 原始圖像路徑

        Returns:
            複製後的圖像路徑，如果失敗則返回None
        """
        try:
            from shutil import copy2

            # 獲取圖像檔名
            image_file = Path(image_path)
            if not image_file.exists():
                logging.warning(f"原始圖像不存在: {image_path}")
                return None

            # 構建目標路徑
            target_image_path = self.output_dir / image_file.name

            # 如果目標檔案已存在且內容相同，跳過複製
            if target_image_path.exists():
                # 簡單檢查檔案大小是否相同
                if target_image_path.stat().st_size == image_file.stat().st_size:
                    logging.info(f"圖像已存在，跳過複製: {image_file.name}")
                    return str(target_image_path)

            # 複製圖像文件
            copy2(str(image_file), str(target_image_path))

            logging.info(f"✅ 圖像複製成功: {image_file.name} -> {target_image_path}")

            # 驗證複製結果
            if target_image_path.exists() and target_image_path.stat().st_size > 0:
                return str(target_image_path)
            else:
                logging.error(f"圖像複製驗證失敗: {target_image_path}")
                return None

        except Exception as e:
            logging.error(f"圖像複製過程發生錯誤: {e}")
            return None

    def get_output_stats(self) -> Dict[str, Any]:
        """
        獲取輸出統計信息

        Returns:
            統計信息字典
        """
        if not self.enabled:
            return {'enabled': False}

        try:
            # 統計生成的JSON檔案數量
            json_files = list(self.output_dir.glob("*.json"))

            stats = {
                'enabled': True,
                'output_directory': str(self.output_dir),
                'total_json_files': len(json_files),
                'class_count': len(self.class_names),
                'settings': {
                    'simplify_tolerance': self.generator.simplify_tolerance,
                    'min_polygon_points': self.min_polygon_points,
                    'include_confidence': self.include_confidence
                }
            }

            return stats

        except Exception as e:
            logging.error(f"Error getting LabelMe output stats: {e}")
            return {'enabled': True, 'error': str(e)}


def create_labelme_integration(config_manager) -> LabelMeIntegration:
    """
    創建LabelMe整合器的工廠函數

    Args:
        config_manager: 配置管理器

    Returns:
        LabelMe整合器實例
    """
    return LabelMeIntegration(config_manager)


if __name__ == "__main__":
    print("🔧 LabelMe Integration Module")
    print("整合LabelMe JSON輸出功能到推理系統中")
