"""
Ray與AI模型建構深度整合模組

將Ray框架深度整合到現有的AI模型建構架構中：
- 與UnifiedTrainer的無縫整合
- 與ModelFactory的配置驅動整合
- 與現有資料處理管線的整合
- 統一的分散式訓練管理
"""

import os
import logging
from typing import Dict, Any, Optional, Callable, List, Union, Tuple
from pathlib import Path
import torch
from dataclasses import dataclass

try:
    import ray
    from ray import train, tune, data
    from ray.train import ScalingConfig, RunConfig, CheckpointConfig
    from ray.train.torch import TorchTrainer
    from ray.tune.schedulers import ASHAScheduler
    from ray.tune.search.optuna import OptunaSearch
    from ray.air import session
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

# 整合現有架構
from ..core.model_factory import ModelFactory
from ..core.config_manager import ConfigManager
from ..training.trainer import UnifiedTrainer, TrainingConfig
from ..training.optimizers import OptimizerFactory, SchedulerFactory
from ..training.metrics import UnifiedMetricsCalculator
from ..util.dataset import YOLODataset, DatasetConfig
from ..util.checkpoint import CheckpointManager


@dataclass
class RayTrainingConfig:
    """Ray分散式訓練配置"""
    # 分散式配置
    num_workers: int = 2
    use_gpu: bool = True
    resources_per_worker: Dict[str, float] = None
    
    # 訓練配置
    training_config: TrainingConfig = None
    
    # Ray特定配置
    storage_path: str = "./ray_results"
    experiment_name: str = "distributed_training"
    checkpoint_frequency: int = 5
    
    def __post_init__(self):
        if self.resources_per_worker is None:
            self.resources_per_worker = {"CPU": 2, "GPU": 0.5 if self.use_gpu else 0}
        
        if self.training_config is None:
            self.training_config = TrainingConfig()


class RayAIIntegrationManager:
    """Ray與AI模型深度整合管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        if not RAY_AVAILABLE:
            raise ImportError("Ray框架未安裝。請使用 'pip install ray[tune,train,data,serve]' 安裝")
        
        # 整合現有組件
        self.model_factory = ModelFactory()
        self.config_manager = ConfigManager()
        
        # 載入配置
        self.config = self._load_config(config_path)
        
        # 初始化Ray
        if not ray.is_initialized():
            self._initialize_ray()
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """載入整合配置"""
        if config_path and os.path.exists(config_path):
            return self.config_manager.load_config(config_path)
        
        return {
            "cluster": {
                "address": None,
                "runtime_env": {
                    "pip": ["torch", "torchvision", "opencv-python", "albumentations", "ultralytics"]
                }
            },
            "default_training": {
                "num_workers": 2,
                "use_gpu": True,
                "resources_per_worker": {"CPU": 2, "GPU": 0.5}
            },
            "hyperparameter_tuning": {
                "num_samples": 20,
                "max_concurrent_trials": 4,
                "metric": "val_accuracy",
                "mode": "max"
            }
        }
    
    def _initialize_ray(self):
        """初始化Ray"""
        cluster_config = self.config.get("cluster", {})
        
        try:
            if cluster_config.get("address"):
                ray.init(address=cluster_config["address"])
                self.logger.info(f"連接到Ray集群: {cluster_config['address']}")
            else:
                ray.init(
                    runtime_env=cluster_config.get("runtime_env", {}),
                    ignore_reinit_error=True
                )
                self.logger.info("Ray本地模式初始化完成")
        except Exception as e:
            self.logger.error(f"Ray初始化失敗: {e}")
            raise


class RayDistributedTrainingSystem:
    """Ray分散式訓練系統 - 與現有架構深度整合"""
    
    def __init__(self, integration_manager: RayAIIntegrationManager):
        self.integration_manager = integration_manager
        self.logger = logging.getLogger(__name__)
    
    def create_distributed_training_function(self, 
                                           model_config: Dict[str, Any],
                                           dataset_config: DatasetConfig,
                                           ray_config: RayTrainingConfig) -> Callable:
        """創建分散式訓練函數 - 整合現有UnifiedTrainer"""
        
        def train_func(train_loop_config: Dict[str, Any]):
            """Ray訓練循環函數"""
            # 1. 使用ModelFactory創建模型
            model = self.integration_manager.model_factory.create_model(model_config)
            
            # 2. 創建優化器和調度器
            optimizer = OptimizerFactory.create_optimizer(
                model, 
                train_loop_config.get("optimizer_config", OptimizerFactory.get_default_config("adamw"))
            )
            
            scheduler = None
            if train_loop_config.get("scheduler_config"):
                scheduler = SchedulerFactory.create_scheduler(
                    optimizer, 
                    train_loop_config["scheduler_config"]
                )
            
            # 3. 創建資料載入器
            train_dataset = self._create_ray_dataset(dataset_config.train_path, train_loop_config)
            val_dataset = self._create_ray_dataset(dataset_config.val_path, train_loop_config)
            
            # 4. 整合Ray的分散式資料載入
            train_loader = train.torch.prepare_data_loader(train_dataset)
            val_loader = train.torch.prepare_data_loader(val_dataset)
            
            # 5. 準備模型和優化器供分散式訓練
            model = train.torch.prepare_model(model)
            
            # 6. 創建指標計算器
            metrics_calculator = UnifiedMetricsCalculator(
                task_type=train_loop_config.get("task_type", "classification"),
                num_classes=train_loop_config.get("num_classes", 10)
            )
            
            # 7. 使用UnifiedTrainer進行訓練（適配分散式環境）
            unified_trainer = UnifiedTrainer(
                model=model,
                optimizer=optimizer,
                scheduler=scheduler,
                config=ray_config.training_config
            )
            
            # 8. 訓練循環
            for epoch in range(ray_config.training_config.epochs):
                # 訓練階段
                train_metrics = unified_trainer._train_epoch(train_loader, epoch)
                
                # 驗證階段
                val_metrics = unified_trainer._validate_epoch(val_loader, epoch)
                
                # 計算詳細指標
                detailed_metrics = metrics_calculator.compute(
                    val_metrics.get("predictions", []),
                    val_metrics.get("targets", [])
                )
                
                # 合併指標
                epoch_metrics = {
                    **train_metrics,
                    **{f"val_{k}": v for k, v in val_metrics.items()},
                    **{f"detailed_{k}": v for k, v in detailed_metrics.items()},
                    "epoch": epoch
                }
                
                # 報告給Ray
                train.report(epoch_metrics)
                
                # 儲存檢查點
                if epoch % ray_config.checkpoint_frequency == 0:
                    checkpoint_data = {
                        "model_state_dict": model.state_dict(),
                        "optimizer_state_dict": optimizer.state_dict(),
                        "epoch": epoch,
                        "metrics": epoch_metrics
                    }
                    
                    checkpoint = train.Checkpoint.from_dict(checkpoint_data)
                    train.report(epoch_metrics, checkpoint=checkpoint)
        
        return train_func
    
    def _create_ray_dataset(self, data_path: str, config: Dict[str, Any]):
        """創建Ray資料集"""
        # 這裡可以整合現有的YOLODataset
        from torch.utils.data import DataLoader
        
        # 創建標準PyTorch資料集
        dataset = YOLODataset(
            data_path=data_path,
            transforms=config.get("transforms"),
            **config.get("dataset_kwargs", {})
        )
        
        # 轉換為DataLoader
        return DataLoader(
            dataset,
            batch_size=config.get("batch_size", 32),
            shuffle=config.get("shuffle", True),
            num_workers=config.get("num_workers", 4)
        )
    
    def run_distributed_training(self,
                                model_config: Dict[str, Any],
                                dataset_config: DatasetConfig,
                                ray_config: RayTrainingConfig,
                                train_loop_config: Dict[str, Any]) -> Any:
        """執行分散式訓練"""
        
        # 創建訓練函數
        train_func = self.create_distributed_training_function(
            model_config, dataset_config, ray_config
        )
        
        # 配置分散式訓練
        scaling_config = ScalingConfig(
            num_workers=ray_config.num_workers,
            use_gpu=ray_config.use_gpu,
            resources_per_worker=ray_config.resources_per_worker
        )
        
        # 配置檢查點
        checkpoint_config = CheckpointConfig(
            num_to_keep=5,
            checkpoint_score_attribute="val_accuracy",
            checkpoint_score_order="max"
        )
        
        # 配置運行
        run_config = RunConfig(
            storage_path=ray_config.storage_path,
            name=ray_config.experiment_name,
            checkpoint_config=checkpoint_config
        )
        
        # 創建TorchTrainer
        trainer = TorchTrainer(
            train_loop_per_worker=train_func,
            train_loop_config=train_loop_config,
            scaling_config=scaling_config,
            run_config=run_config
        )
        
        # 執行訓練
        self.logger.info(f"開始分散式訓練: {ray_config.experiment_name}")
        result = trainer.fit()
        
        self.logger.info("分散式訓練完成")
        return result


class RayHyperparameterOptimizer:
    """Ray超參數優化器 - 整合現有模型工廠"""
    
    def __init__(self, integration_manager: RayAIIntegrationManager):
        self.integration_manager = integration_manager
        self.logger = logging.getLogger(__name__)
    
    def create_objective_function(self,
                                base_model_config: Dict[str, Any],
                                dataset_config: DatasetConfig,
                                training_config: TrainingConfig) -> Callable:
        """創建優化目標函數"""
        
        def objective(trial_config: Dict[str, Any]) -> Dict[str, float]:
            """優化目標函數"""
            
            # 1. 合併試驗配置和基礎配置
            model_config = {**base_model_config, **trial_config.get("model", {})}
            
            # 2. 創建模型
            model = self.integration_manager.model_factory.create_model(model_config)
            
            # 3. 創建優化器
            optimizer_config = trial_config.get("optimizer", OptimizerFactory.get_default_config("adamw"))
            optimizer = OptimizerFactory.create_optimizer(model, optimizer_config)
            
            # 4. 創建調度器
            scheduler = None
            if trial_config.get("scheduler"):
                scheduler = SchedulerFactory.create_scheduler(optimizer, trial_config["scheduler"])
            
            # 5. 創建訓練器
            eval_training_config = TrainingConfig(
                epochs=trial_config.get("eval_epochs", 5),  # 快速評估
                **training_config.__dict__
            )
            
            trainer = UnifiedTrainer(
                model=model,
                optimizer=optimizer,
                scheduler=scheduler,
                config=eval_training_config
            )
            
            # 6. 快速訓練和評估
            train_loader = self._create_dataloader(dataset_config.train_path, trial_config)
            val_loader = self._create_dataloader(dataset_config.val_path, trial_config)
            
            history = trainer.fit(train_loader, val_loader)
            
            # 7. 返回最佳指標
            if history:
                best_metric = max([h.get("val_accuracy", 0) for h in history])
                return {"val_accuracy": best_metric}
            else:
                return {"val_accuracy": 0.0}
        
        return objective
    
    def _create_dataloader(self, data_path: str, config: Dict[str, Any]):
        """創建資料載入器"""
        from torch.utils.data import DataLoader
        
        dataset = YOLODataset(data_path=data_path)
        return DataLoader(
            dataset,
            batch_size=config.get("batch_size", 32),
            shuffle=True,
            num_workers=2
        )
    
    def run_hyperparameter_optimization(self,
                                      search_space: Dict[str, Any],
                                      base_model_config: Dict[str, Any],
                                      dataset_config: DatasetConfig,
                                      training_config: TrainingConfig,
                                      num_samples: int = 20) -> Any:
        """執行超參數優化"""
        
        # 創建目標函數
        objective = self.create_objective_function(
            base_model_config, dataset_config, training_config
        )
        
        # 配置調度器
        scheduler = ASHAScheduler(
            metric=self.integration_manager.config["hyperparameter_tuning"]["metric"],
            mode=self.integration_manager.config["hyperparameter_tuning"]["mode"]
        )
        
        # 配置搜索算法
        search_alg = OptunaSearch()
        
        # 執行調優
        analysis = tune.run(
            objective,
            config=search_space,
            num_samples=num_samples,
            scheduler=scheduler,
            search_alg=search_alg,
            resources_per_trial={"cpu": 2, "gpu": 0.5},
            local_dir="./ray_tune_results"
        )
        
        return analysis


class RayDataProcessor:
    """Ray資料處理器 - 整合現有資料處理管線"""
    
    def __init__(self, integration_manager: RayAIIntegrationManager):
        self.integration_manager = integration_manager
        self.logger = logging.getLogger(__name__)
    
    def process_dataset_distributed(self,
                                  input_paths: List[str],
                                  output_dir: str,
                                  processing_func: Callable,
                                  batch_size: int = 100) -> Any:
        """分散式資料集處理"""
        
        # 創建Ray資料集
        dataset = data.from_items([{"path": path} for path in input_paths])
        
        # 分散式處理
        processed_dataset = dataset.map_batches(
            processing_func,
            batch_size=batch_size,
            num_cpus=2
        )
        
        # 儲存結果
        processed_dataset.write_parquet(output_dir)
        
        return processed_dataset
    
    def convert_annotations_distributed(self,
                                      annotation_paths: List[str],
                                      output_format: str = "yolo") -> Any:
        """分散式標註轉換 - 整合現有轉換器"""
        
        def convert_batch(batch: Dict[str, Any]) -> Dict[str, Any]:
            """批次轉換函數"""
            results = []
            
            for item in batch["path"]:
                try:
                    # 這裡可以整合第三階段重構後的AnnotationConverter
                    from ...資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2
                    
                    converter = AnnotationConverterV2(
                        input_dir=str(Path(item).parent),
                        output_dir=f"./converted_{output_format}",
                        input_format="auto",
                        output_format=output_format
                    )
                    
                    result = converter.run()
                    results.append({"status": "success", "path": item, "result": result})
                    
                except Exception as e:
                    results.append({"status": "error", "path": item, "error": str(e)})
            
            return {"results": results}
        
        # 創建資料集並處理
        dataset = data.from_items([{"path": path} for path in annotation_paths])
        processed = dataset.map_batches(convert_batch, batch_size=10)
        
        return processed


# 工廠函數和便利接口
def create_ray_training_system(config_path: Optional[str] = None) -> Tuple[RayAIIntegrationManager, RayDistributedTrainingSystem]:
    """創建Ray訓練系統"""
    manager = RayAIIntegrationManager(config_path)
    training_system = RayDistributedTrainingSystem(manager)
    return manager, training_system


def create_search_space_for_model(model_type: str) -> Dict[str, Any]:
    """為特定模型類型創建搜索空間"""
    
    if model_type == "csp_iformer":
        return {
            "model": {
                "backbone_config": {
                    "enable_channel_shuffle": tune.choice([True, False]),
                    "enable_dropkey": tune.choice([True, False]),
                    "dropout_rate": tune.uniform(0.1, 0.5)
                }
            },
            "optimizer": {
                "lr": tune.loguniform(1e-5, 1e-2),
                "weight_decay": tune.uniform(1e-6, 1e-3)
            },
            "batch_size": tune.choice([16, 32, 64]),
            "eval_epochs": tune.choice([3, 5, 7])
        }
    
    elif model_type == "mobilenet":
        return {
            "model": {
                "backbone_config": {
                    "width_multiplier": tune.choice([0.5, 0.75, 1.0, 1.25]),
                    "dropout_rate": tune.uniform(0.1, 0.3)
                }
            },
            "optimizer": {
                "lr": tune.loguniform(1e-4, 1e-2),
                "weight_decay": tune.uniform(1e-5, 1e-3)
            },
            "batch_size": tune.choice([32, 64, 128]),
            "eval_epochs": tune.choice([5, 7, 10])
        }
    
    else:
        # 通用搜索空間
        return {
            "optimizer": {
                "lr": tune.loguniform(1e-5, 1e-2),
                "weight_decay": tune.uniform(1e-6, 1e-3)
            },
            "batch_size": tune.choice([16, 32, 64]),
            "eval_epochs": tune.choice([3, 5, 7])
        }


# 使用範例
def example_distributed_training():
    """分散式訓練使用範例"""
    
    # 1. 創建整合系統
    manager, training_system = create_ray_training_system()
    
    # 2. 配置模型
    model_config = {
        "model_type": "csp_iformer",
        "variant": "final_segmentation",
        "num_classes": 5,
        "backbone_config": {
            "enable_channel_shuffle": True,
            "enable_dropkey": True
        }
    }
    
    # 3. 配置資料集
    dataset_config = DatasetConfig(
        train_path="./data/train",
        val_path="./data/val",
        transforms=None
    )
    
    # 4. 配置Ray訓練
    ray_config = RayTrainingConfig(
        num_workers=4,
        use_gpu=True,
        resources_per_worker={"CPU": 2, "GPU": 0.25},
        training_config=TrainingConfig(epochs=50)
    )
    
    # 5. 執行分散式訓練
    train_loop_config = {
        "batch_size": 32,
        "task_type": "segmentation",
        "num_classes": 5
    }
    
    result = training_system.run_distributed_training(
        model_config, dataset_config, ray_config, train_loop_config
    )
    
    print(f"分散式訓練完成，最佳檢查點: {result.best_checkpoints}")


if __name__ == "__main__":
    example_distributed_training()