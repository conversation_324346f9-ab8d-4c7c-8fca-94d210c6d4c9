#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV資料處理模組

基於convert_csv.py建構的CSV資料轉換和抽樣系統
支援裂縫/龜裂自動分類、資料抽樣和圖像處理
"""

import pandas as pd
import os
import cv2
import numpy as np
import shutil
import random
import datetime
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
import json


class CSVProcessor:
    """
    CSV資料處理器
    
    提供CSV資料的轉換、分類和抽樣功能
    支援裂縫/龜裂自動分類和圖像抽樣
    """
    
    def __init__(self,
                 aspect_ratio_threshold: float = 0.5,
                 area_ratio_threshold: float = 0.5,
                 sample_rate: float = 0.05):
        """
        初始化CSV處理器
        
        參數:
            aspect_ratio_threshold: 長寬比閾值，較小值/較大值低於此閾值視為細長形
            area_ratio_threshold: 面積比閾值，掩碼面積/框面積低於此閾值視為裂縫
            sample_rate: 抽樣比例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.aspect_ratio_threshold = aspect_ratio_threshold
        self.area_ratio_threshold = area_ratio_threshold
        self.sample_rate = sample_rate
        
        # 支援的圖像格式
        self.supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
        
        # 預設類別名稱
        self.default_classes = ["裂縫", "龜裂", "補綻", "坑洞"]
        
        self.logger.info("CSV處理器初始化完成")
    
    def convert_crack_classification(self,
                                   input_csv_path: str,
                                   output_csv_path: str,
                                   sample_csv_path: Optional[str] = None,
                                   image_dir: Optional[str] = None,
                                   sample_image_dir: Optional[str] = None) -> bool:
        """
        轉換CSV資料，根據條件將裂縫分類為裂縫或龜裂
        
        參數:
            input_csv_path: 輸入CSV檔案路徑
            output_csv_path: 輸出CSV檔案路徑
            sample_csv_path: 抽樣檔案路徑
            image_dir: 原始圖像目錄路徑
            sample_image_dir: 抽樣圖像保存目錄路徑
            
        返回:
            處理是否成功
        """
        try:
            # 檢查圖像目錄
            has_image_dir = False
            if image_dir and os.path.isdir(image_dir):
                has_image_dir = True
                self.logger.info(f"將從 {image_dir} 抽取圖像樣本")
                
                # 確保抽樣圖像目錄存在
                if sample_image_dir:
                    os.makedirs(sample_image_dir, exist_ok=True)
                    # 為每個類別建立子目錄
                    for cls in self.default_classes:
                        os.makedirs(os.path.join(sample_image_dir, cls), exist_ok=True)
            else:
                self.logger.info("未提供有效的圖像目錄，將只處理CSV資料")
            
            # 讀取CSV檔案
            df = self._load_csv(input_csv_path)
            if df is None:
                return False
            
            # 驗證必要欄位
            required_columns = ["類別", "長", "寬", "面積", "檔案名稱"]
            if not self._validate_columns(df, required_columns):
                return False
            
            # 數據預處理
            df = self._preprocess_data(df)
            
            # 建立檔案名稱到路徑的映射
            file_to_path = {}
            if has_image_dir:
                file_to_path = self._build_file_mapping(image_dir)
            
            # 執行分類轉換和抽樣
            df, class_counts, converted_counts, samples = self._process_classifications(
                df, file_to_path, sample_image_dir, has_image_dir
            )
            
            # 保存結果
            self._save_results(df, output_csv_path, samples, sample_csv_path)
            
            # 輸出統計資訊
            self._print_statistics(class_counts, converted_counts, samples, 
                                 output_csv_path, sample_csv_path, sample_image_dir, has_image_dir)
            
            return True
            
        except Exception as e:
            self.logger.error(f"處理CSV檔案時發生錯誤: {str(e)}")
            return False
    
    def _load_csv(self, csv_path: str) -> Optional[pd.DataFrame]:
        """載入CSV檔案"""
        try:
            # 嘗試用不同編碼讀取
            for encoding in ['utf_8_sig', 'utf-8', 'big5', 'gbk']:
                try:
                    df = pd.read_csv(csv_path, encoding=encoding)
                    self.logger.info(f"成功讀取CSV檔案: {csv_path} (編碼: {encoding})")
                    return df
                except UnicodeDecodeError:
                    continue
            
            self.logger.error(f"無法讀取CSV檔案: {csv_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"讀取CSV檔案失敗: {e}")
            return None
    
    def _validate_columns(self, df: pd.DataFrame, required_columns: List[str]) -> bool:
        """驗證CSV檔案必要欄位"""
        for col in required_columns:
            if col not in df.columns:
                self.logger.error(f"CSV檔案缺少必要欄位 '{col}'")
                return False
        return True
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """數據預處理"""
        # 轉換欄位為數值型別
        numeric_cols = ["長", "寬", "面積"]
        for col in numeric_cols:
            df[col] = pd.to_numeric(
                df[col].astype(str).str.replace(',', ''), 
                errors='coerce'
            )
        
        # 確認座標欄位
        coord_columns = ["x1", "y1", "x2", "y2"]
        for col in coord_columns:
            if col not in df.columns:
                df[col] = np.nan
        
        # 確認置信度欄位
        if "置信度" not in df.columns:
            df["置信度"] = np.nan
        
        # 建立分析欄位
        df['長寬比'] = ''
        df['面積比'] = ''
        
        return df
    
    def _build_file_mapping(self, image_dir: str) -> Dict[str, str]:
        """建立檔案名稱到路徑的映射"""
        file_to_path = {}
        self.logger.info("正在掃描圖像目錄...")
        
        for root, _, files in os.walk(image_dir):
            for file in files:
                if file.lower().endswith(self.supported_formats):
                    file_to_path[file] = os.path.join(root, file)
        
        self.logger.info(f"找到 {len(file_to_path)} 個圖像檔案")
        return file_to_path
    
    def _process_classifications(self, 
                               df: pd.DataFrame,
                               file_to_path: Dict[str, str],
                               sample_image_dir: Optional[str],
                               has_image_dir: bool) -> Tuple[pd.DataFrame, Dict, Dict, Dict]:
        """處理分類轉換和抽樣"""
        class_counts = {}
        converted_counts = {
            "裂縫→裂縫": 0,
            "裂縫→龜裂": 0
        }
        samples = {cls: [] for cls in self.default_classes}
        
        # 第一次遍歷：執行分類轉換
        for index, row in df.iterrows():
            class_name = row["類別"]
            
            # 更新類別計數
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            # 處理裂縫類別
            if class_name == "裂縫":
                df, converted_counts, class_counts = self._process_crack_classification(
                    df, index, row, converted_counts, class_counts
                )
        
        # 第二次遍歷：執行抽樣
        for index, row in df.iterrows():
            class_name = row["類別"]
            filename = row["檔案名稱"]
            
            # 對指定類別進行抽樣
            if class_name in self.default_classes:
                if random.random() < self.sample_rate:
                    sample_data = self._create_sample_data(row, index)
                    samples[class_name].append(sample_data)
                    
                    # 複製圖像檔案
                    if has_image_dir and sample_image_dir:
                        self._copy_sample_image(
                            filename, file_to_path, sample_image_dir, 
                            class_name, index, samples[class_name]
                        )
        
        return df, class_counts, converted_counts, samples
    
    def _process_crack_classification(self, 
                                    df: pd.DataFrame, 
                                    index: int, 
                                    row: pd.Series,
                                    converted_counts: Dict,
                                    class_counts: Dict) -> Tuple[pd.DataFrame, Dict, Dict]:
        """處理裂縫分類邏輯"""
        # 計算長寬比 (確保使用較小值除以較大值)
        longer = max(row["長"], row["寬"])
        shorter = min(row["長"], row["寬"])
        aspect_ratio = shorter / longer if longer > 0 else 0
        df.at[index, "長寬比"] = aspect_ratio
        
        # 計算框面積與掩碼面積的比率
        box_area = row["長"] * row["寬"]
        mask_to_box_ratio = row["面積"] / box_area if box_area > 0 else 0
        df.at[index, "面積比"] = mask_to_box_ratio
        
        # 根據條件決定類型
        if (aspect_ratio < self.aspect_ratio_threshold and 
            mask_to_box_ratio < self.area_ratio_threshold):
            # 符合裂縫條件
            converted_counts["裂縫→裂縫"] += 1
        else:
            # 變更為龜裂
            df.at[index, "類別"] = "龜裂"
            converted_counts["裂縫→龜裂"] += 1
            
            # 更新類別計數
            class_counts["龜裂"] = class_counts.get("龜裂", 0) + 1
            class_counts["裂縫"] -= 1
        
        return df, converted_counts, class_counts
    
    def _create_sample_data(self, row: pd.Series, index: int) -> Dict:
        """創建抽樣數據"""
        sample_data = row.to_dict()
        sample_data["原始索引"] = index
        sample_data["抽樣時間"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        sample_data["檢查結果"] = ""  # 用於人工檢查標註
        return sample_data
    
    def _copy_sample_image(self, 
                          filename: str,
                          file_to_path: Dict[str, str], 
                          sample_image_dir: str,
                          class_name: str,
                          index: int,
                          sample_list: List[Dict]):
        """複製抽樣圖像"""
        if filename in file_to_path:
            src_path = file_to_path[filename]
            base_name = Path(filename).stem
            ext = Path(filename).suffix
            target_filename = f"{base_name}_{index}{ext}"
            
            dst_path = os.path.join(sample_image_dir, class_name, target_filename)
            
            if os.path.exists(src_path):
                try:
                    shutil.copy2(src_path, dst_path)
                    sample_list[-1]["抽樣圖像路徑"] = dst_path
                except Exception as e:
                    self.logger.error(f"複製圖像 {src_path} 時出錯: {str(e)}")
            else:
                self.logger.warning(f"圖像檔案不存在: {src_path}")
    
    def _save_results(self, 
                     df: pd.DataFrame,
                     output_csv_path: str,
                     samples: Dict,
                     sample_csv_path: Optional[str]):
        """保存處理結果"""
        # 保存主要結果
        os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
        df.to_csv(output_csv_path, index=False, encoding='utf_8_sig')
        
        # 保存抽樣結果
        if sample_csv_path and any(samples.values()):
            all_samples = []
            for class_name, items in samples.items():
                all_samples.extend(items)
            
            if all_samples:
                sample_df = pd.DataFrame(all_samples)
                sample_df.to_csv(sample_csv_path, index=False, encoding='utf_8_sig')
                self.logger.info(f"抽樣檔案已保存至: {sample_csv_path}")
    
    def _print_statistics(self, 
                         class_counts: Dict,
                         converted_counts: Dict, 
                         samples: Dict,
                         output_csv_path: str,
                         sample_csv_path: Optional[str],
                         sample_image_dir: Optional[str],
                         has_image_dir: bool):
        """輸出統計資訊"""
        self.logger.info("\n轉換完成！以下是詳細統計：")
        
        # 類別轉換統計
        self.logger.info("類別轉換:")
        for conversion, count in converted_counts.items():
            if count > 0:
                self.logger.info(f"  {conversion}: {count}")
        
        # 各類別數量
        self.logger.info("\n各類別數量:")
        for cls, count in sorted(class_counts.items()):
            self.logger.info(f"  {cls}: {count}")
        
        # 抽樣統計
        sample_counts = {cls: len(items) for cls, items in samples.items()}
        self.logger.info(f"\n抽樣數量: {sample_counts}")
        
        if has_image_dir and sample_image_dir:
            for cls in self.default_classes:
                cls_dir = os.path.join(sample_image_dir, cls)
                if os.path.exists(cls_dir):
                    count = len([f for f in os.listdir(cls_dir) 
                               if f.lower().endswith(self.supported_formats)])
                    self.logger.info(f"{cls}圖像: {count} 張")
        
        self.logger.info(f"\n結果已保存至: {output_csv_path}")
    
    def find_image_for_region(self, 
                             image_dir: str,
                             filename: str, 
                             x1: float, y1: float, 
                             x2: float, y2: float,
                             padding: int = 50) -> Tuple[Optional[np.ndarray], str]:
        """
        根據文件名和區域坐標找到圖像並裁剪感興趣區域
        
        參數:
            image_dir: 圖像目錄
            filename: 文件名
            x1, y1, x2, y2: 區域坐標
            padding: 周圍填充像素
            
        返回:
            (裁剪後的圖像, 狀態訊息)
        """
        try:
            # 尋找圖像文件
            image_path = None
            for root, _, files in os.walk(image_dir):
                if filename in files:
                    image_path = os.path.join(root, filename)
                    break
            
            if not image_path:
                return None, f"找不到圖像: {filename}"
            
            # 讀取原始圖像
            img = cv2.imread(image_path)
            if img is None:
                return None, f"無法讀取圖像: {image_path}"
            
            # 獲取圖像尺寸
            height, width = img.shape[:2]
            
            # 轉換坐標為整數並添加填充
            x1 = max(0, int(x1) - padding)
            y1 = max(0, int(y1) - padding)
            x2 = min(width, int(x2) + padding)
            y2 = min(height, int(y2) + padding)
            
            # 裁剪圖像
            cropped_img = img[y1:y2, x1:x2]
            
            return cropped_img, f"成功裁剪圖像: {image_path}"
            
        except Exception as e:
            return None, f"處理圖像時發生錯誤: {str(e)}"
    
    def batch_process_directory(self, 
                               input_dir: str,
                               output_dir: str,
                               pattern: str = "*.csv") -> Dict[str, bool]:
        """
        批次處理目錄中的所有CSV檔案
        
        參數:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            pattern: 檔案模式
            
        返回:
            處理結果字典
        """
        results = {}
        csv_files = list(Path(input_dir).glob(pattern))
        
        for csv_file in csv_files:
            try:
                # 生成輸出路徑
                output_csv = os.path.join(
                    output_dir, f"{csv_file.stem}_processed{csv_file.suffix}"
                )
                sample_csv = os.path.join(
                    output_dir, f"{csv_file.stem}_samples{csv_file.suffix}"
                )
                
                # 處理檔案
                success = self.convert_crack_classification(
                    str(csv_file), output_csv, sample_csv
                )
                results[str(csv_file)] = success
                
            except Exception as e:
                self.logger.error(f"處理檔案 {csv_file} 失敗: {e}")
                results[str(csv_file)] = False
        
        return results
    
    def get_processing_summary(self) -> Dict:
        """獲取處理器摘要信息"""
        return {
            'aspect_ratio_threshold': self.aspect_ratio_threshold,
            'area_ratio_threshold': self.area_ratio_threshold,
            'sample_rate': self.sample_rate,
            'supported_formats': self.supported_formats,
            'default_classes': self.default_classes
        }


# 工廠函數
def create_csv_processor(aspect_ratio_threshold: float = 0.5,
                        area_ratio_threshold: float = 0.5,
                        sample_rate: float = 0.05) -> CSVProcessor:
    """
    創建CSV處理器的工廠函數
    
    參數:
        aspect_ratio_threshold: 長寬比閾值
        area_ratio_threshold: 面積比閾值
        sample_rate: 抽樣比例
        
    返回:
        CSVProcessor實例
    """
    return CSVProcessor(
        aspect_ratio_threshold=aspect_ratio_threshold,
        area_ratio_threshold=area_ratio_threshold,
        sample_rate=sample_rate
    )


# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建CSV處理器
    processor = create_csv_processor(
        aspect_ratio_threshold=0.5,
        area_ratio_threshold=0.5,
        sample_rate=0.05
    )
    
    # 範例使用
    input_csv = "example_input.csv"
    output_csv = "example_output.csv"
    sample_csv = "example_samples.csv"
    
    # 執行轉換
    success = processor.convert_crack_classification(
        input_csv_path=input_csv,
        output_csv_path=output_csv,
        sample_csv_path=sample_csv,
        image_dir="./images",
        sample_image_dir="./sample_images"
    )
    
    if success:
        print("CSV處理完成")
        print(f"處理器摘要: {processor.get_processing_summary()}")
    else:
        print("CSV處理失敗")