#!/usr/bin/env python3
"""
簡單結構測試
不依賴外部庫的基礎測試
"""

import unittest
import sys
import os
from pathlib import Path


class TestProjectStructure(unittest.TestCase):
    """項目結構測試"""
    
    def setUp(self):
        self.project_root = Path(__file__).parent.parent
    
    def test_main_directories_exist(self):
        """測試主要目錄存在"""
        required_dirs = [
            "AI模型建構訓練驗證",
            "資料前處理",
            "tests"
        ]
        
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            self.assertTrue(dir_path.exists(), f"目錄不存在: {dir_name}")
    
    def test_core_modules_exist(self):
        """測試核心模組存在"""
        required_files = [
            "AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py",
            "AI模型建構訓練驗證/model_create/inference/enhanced_yolo_inference.py",
            "AI模型建構訓練驗證/enhanced_yolo_gui.py",
            "AI模型建構訓練驗證/model_create/util/doc_generator.py",
            "AI模型建構訓練驗證/model_create/util/test_framework.py"
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            self.assertTrue(full_path.exists(), f"文件不存在: {file_path}")
    
    def test_documentation_exists(self):
        """測試文檔存在"""
        claude_md = self.project_root / "CLAUDE.md"
        self.assertTrue(claude_md.exists(), "CLAUDE.md文件不存在")
        
        # 檢查文檔目錄
        docs_dir = self.project_root / "docs" / "api"
        if docs_dir.exists():
            index_file = docs_dir / "index.md"
            self.assertTrue(index_file.exists(), "API文檔索引不存在")
    
    def test_python_files_syntax(self):
        """測試Python文件語法"""
        import ast
        
        test_files = [
            "AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py",
            "AI模型建構訓練驗證/model_create/util/doc_generator.py",
            "AI模型建構訓練驗證/model_create/util/test_framework.py",
            "generate_docs_simple.py",
            "run_tests.py"
        ]
        
        for file_path in test_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                with self.subTest(file=file_path):
                    try:
                        with open(full_path, 'r', encoding='utf-8') as f:
                            source = f.read()
                        ast.parse(source)
                    except SyntaxError as e:
                        self.fail(f"語法錯誤 {file_path}: {e}")


class TestConfigFiles(unittest.TestCase):
    """配置文件測試"""
    
    def setUp(self):
        self.project_root = Path(__file__).parent.parent
    
    def test_enhanced_yolo_config(self):
        """測試增強YOLO配置"""
        config_path = self.project_root / "AI模型建構訓練驗證/model_create/configs/enhanced_yolo_config.yaml"
        
        if config_path.exists():
            try:
                import yaml
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                # 檢查必要配置項
                self.assertIn('class_configs', config)
                self.assertIn('img_size', config)
                self.assertIn('global_conf', config)
                
            except ImportError:
                # 如果沒有yaml，至少檢查文件存在
                self.assertTrue(config_path.stat().st_size > 0, "配置文件為空")


class TestCodeQuality(unittest.TestCase):
    """代碼品質測試"""
    
    def setUp(self):
        self.project_root = Path(__file__).parent.parent
    
    def test_no_syntax_errors_in_key_files(self):
        """測試關鍵文件無語法錯誤"""
        import ast
        
        key_files = []
        
        # 掃描主要目錄
        for py_file in self.project_root.rglob("*.py"):
            # 跳過特定目錄
            if any(exclude in str(py_file) for exclude in ['old_code', '__pycache__', '.git']):
                continue
            key_files.append(py_file)
        
        error_files = []
        
        for py_file in key_files[:20]:  # 只檢查前20個文件
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    source = f.read()
                ast.parse(source)
            except (SyntaxError, UnicodeDecodeError) as e:
                error_files.append(f"{py_file.relative_to(self.project_root)}: {e}")
        
        if error_files:
            self.fail(f"發現語法錯誤:\n" + "\n".join(error_files))
    
    def test_imports_structure(self):
        """測試導入結構"""
        # 檢查關鍵模組是否可以被導入（不執行）
        key_modules = [
            "AI模型建構訓練驗證.model_create.encoder.mamba.vision_mamba_core",
            "AI模型建構訓練驗證.model_create.util.doc_generator",
            "AI模型建構訓練驗證.model_create.util.test_framework"
        ]
        
        sys.path.insert(0, str(self.project_root))
        
        for module_name in key_modules:
            with self.subTest(module=module_name):
                try:
                    # 只檢查模組文件存在，不實際導入
                    module_path = module_name.replace('.', '/')
                    py_file = self.project_root / f"{module_path}.py"
                    self.assertTrue(py_file.exists(), f"模組文件不存在: {module_path}.py")
                except Exception as e:
                    self.fail(f"模組結構錯誤 {module_name}: {e}")


class TestShortTermGoals(unittest.TestCase):
    """短期優化目標完成測試"""
    
    def setUp(self):
        self.project_root = Path(__file__).parent.parent
    
    def test_vision_mamba_implementation(self):
        """測試Vision Mamba實現"""
        vision_mamba_file = self.project_root / "AI模型建構訓練驗證/model_create/encoder/mamba/vision_mamba_core.py"
        self.assertTrue(vision_mamba_file.exists(), "Vision Mamba核心文件不存在")
        
        # 檢查文件內容包含關鍵類
        with open(vision_mamba_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        key_classes = [
            "class VisionMambaConfig",
            "class VisionMamba", 
            "class SSMLayer",
            "def create_vision_mamba_tiny"
        ]
        
        for key_class in key_classes:
            self.assertIn(key_class, content, f"未找到關鍵類/函數: {key_class}")
    
    def test_enhanced_yolo_gui(self):
        """測試增強YOLO GUI"""
        gui_file = self.project_root / "AI模型建構訓練驗證/enhanced_yolo_gui.py"
        self.assertTrue(gui_file.exists(), "增強YOLO GUI文件不存在")
        
        # 檢查GUI相關內容
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        gui_elements = [
            "PyQt6",
            "class InferenceWorker",
            "class ClassConfigWidget",
            "class EnhancedYOLOGUI"
        ]
        
        for element in gui_elements:
            self.assertIn(element, content, f"未找到GUI元素: {element}")
    
    def test_api_documentation_generator(self):
        """測試API文檔生成器"""
        doc_gen_file = self.project_root / "AI模型建構訓練驗證/model_create/util/doc_generator.py"
        self.assertTrue(doc_gen_file.exists(), "API文檔生成器不存在")
        
        # 檢查文檔生成器內容
        with open(doc_gen_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        doc_elements = [
            "class DocGenerator",
            "class DocParser",
            "def generate_api_docs",
            "def create_doc_generator"
        ]
        
        for element in doc_elements:
            self.assertIn(element, content, f"未找到文檔生成元素: {element}")
    
    def test_unified_test_framework(self):
        """測試統一測試框架"""
        test_framework_file = self.project_root / "AI模型建構訓練驗證/model_create/util/test_framework.py"
        self.assertTrue(test_framework_file.exists(), "統一測試框架不存在")
        
        # 檢查測試框架內容
        with open(test_framework_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        test_elements = [
            "class TestRunner",
            "class BaseTestCase", 
            "class CIRunner",
            "def run_all_tests"
        ]
        
        for element in test_elements:
            self.assertIn(element, content, f"未找到測試框架元素: {element}")
    
    def test_docs_generated(self):
        """測試文檔已生成"""
        docs_dir = self.project_root / "docs" / "api"
        
        if docs_dir.exists():
            # 檢查主要文檔文件
            doc_files = ["index.md", "factories.md", "configs.md", "models.md"]
            
            for doc_file in doc_files:
                doc_path = docs_dir / doc_file
                if doc_path.exists():
                    self.assertGreater(doc_path.stat().st_size, 100, f"文檔文件太小: {doc_file}")


if __name__ == '__main__':
    unittest.main(verbosity=2)