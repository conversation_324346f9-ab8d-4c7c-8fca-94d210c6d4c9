#!/usr/bin/env python3
"""
🚀 高精度分類器使用示例

這個腳本展示如何使用高精度分類器進行訓練和推理
"""

import os
import json
from pathlib import Path
from high_accuracy_classifier import ClassifierConfig, ClassificationTrainer

def main():
    """主函數 - 展示分類器的基本使用方法"""
    
    print("🏆 高精度分類器使用示例")
    print("=" * 50)
    
    # ================== 方法1: 使用默認配置 ==================
    print("\n📋 方法1: 使用默認配置")
    
    # 創建基本配置
    config = ClassifierConfig(
        data_dir="./data",                    # 你的數據目錄
        output_dir="./classifier_output",     # 輸出目錄
        model_name="efficientnet_v2_l",       # 最高accuracy的模型
        batch_size=16,                        # 根據你的GPU記憶體調整
        num_epochs=50,                        # 訓練輪數
        learning_rate=0.001,                  # 學習率
        image_size=480,                       # 圖像尺寸
        device="auto"                         # 自動選擇GPU/CPU
    )
    
    print(f"✅ 配置完成:")
    print(f"   數據目錄: {config.data_dir}")
    print(f"   模型: {config.model_name}")
    print(f"   圖像尺寸: {config.image_size}")
    print(f"   批次大小: {config.batch_size}")
    print(f"   訓練輪數: {config.num_epochs}")
    
    # ================== 方法2: 從配置文件載入 ==================
    print("\n📋 方法2: 從配置文件載入")
    
    config_file = "classifier_config_example.json"
    if Path(config_file).exists():
        with open(config_file, "r", encoding="utf-8") as f:
            config_dict = json.load(f)
        
        # 更新你的具體路徑
        config_dict["data_dir"] = "./your_data_directory"
        config_dict["output_dir"] = "./your_output_directory"
        
        config_from_file = ClassifierConfig.from_dict(config_dict)
        print(f"✅ 從配置文件載入: {config_file}")
        print(f"   模型: {config_from_file.model_name}")
        print(f"   高級增強: {config_from_file.use_advanced_augmentation}")
        print(f"   混合精度: {config_from_file.mixed_precision}")
    
    # ================== 數據目錄結構說明 ==================
    print("\n📁 數據目錄結構要求:")
    print("""
    your_data_directory/
    ├── train/
    │   ├── class1/
    │   │   ├── image1.jpg
    │   │   ├── image2.jpg
    │   │   └── ...
    │   ├── class2/
    │   │   ├── image3.jpg
    │   │   ├── image4.jpg
    │   │   └── ...
    │   └── class3/
    │       └── ...
    ├── val/
    │   ├── class1/
    │   ├── class2/
    │   └── class3/
    └── test/ (可選)
        ├── class1/
        ├── class2/
        └── class3/
    """)
    
    # ================== 開始訓練 ==================
    print("\n🚀 訓練流程演示:")
    
    # 檢查數據目錄是否存在
    if not Path(config.data_dir).exists():
        print(f"❌ 數據目錄不存在: {config.data_dir}")
        print("💡 請先準備你的數據，按照上述目錄結構組織")
        print("🔧 然後修改 config.data_dir 為你的實際數據路徑")
        return
    
    print("✅ 數據目錄檢查通過")
    
    # 創建訓練器
    trainer = ClassificationTrainer(config)
    
    try:
        # 設置數據載入器
        print("📊 設置數據載入器...")
        trainer.setup_data()
        
        # 設置模型
        print("🏗️ 設置模型...")
        trainer.setup_model()
        
        print("🎯 開始訓練...")
        # 注意：實際訓練時取消這行註釋
        # trainer.train()
        
        print("🧪 開始測試...")
        # 注意：實際測試時取消這行註釋
        # test_results = trainer.test()
        
        print("🎉 訓練和測試完成！")
        
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        print("💡 請檢查數據路徑和環境配置")
    
    # ================== 不同模型選擇 ==================
    print("\n🤖 可選的高精度模型:")
    
    models_info = [
        ("efficientnet_v2_l", "88.4%", "最高accuracy，推薦使用"),
        ("convnext_large", "87.8%", "ConvNeXt架構，性能優秀"),
        ("swin_v2_t", "87.3%", "Swin Transformer，注意力機制")
    ]
    
    for model_name, accuracy, description in models_info:
        print(f"  🏆 {model_name}")
        print(f"      ImageNet Top-1: {accuracy}")
        print(f"      說明: {description}")
    
    # ================== 使用建議 ==================
    print("\n💡 使用建議:")
    print("1. 🖼️  圖像尺寸: EfficientNet V2 L 建議使用 480x480")
    print("2. 💾 批次大小: 根據GPU記憶體調整 (8-32)")
    print("3. ⚡ 混合精度: 開啟以節省記憶體和加速訓練")
    print("4. 🔄 數據增強: 啟用高級增強以提升泛化能力")
    print("5. ⏰ 早停機制: 防止過擬合，提升測試性能")
    print("6. 📈 學習率調度: 使用餘弦退火獲得更好收斂")
    
    # ================== 輸出文件說明 ==================
    print("\n📂 輸出文件說明:")
    print("  best_model.pth - 最佳模型權重")
    print("  training_curves.png - 訓練曲線圖")
    print("  confusion_matrix.png - 混淆矩陣")
    print("  test_results.json - 測試結果")
    print("  class_mapping.json - 類別映射")
    print("  config.json - 完整配置")
    print("  training_history.csv - 訓練歷史數據")
    
    print("\n✅ 示例演示完成！")
    print("🚀 修改數據路徑後即可開始訓練")


def create_sample_data_structure():
    """創建示例數據結構"""
    print("\n🛠️ 創建示例數據結構...")
    
    base_dir = Path("./sample_data")
    
    # 創建目錄結構
    for split in ["train", "val", "test"]:
        for class_name in ["cat", "dog", "bird"]:
            class_dir = base_dir / split / class_name
            class_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 示例數據結構已創建: {base_dir}")
    print("💡 請將你的圖像文件放入對應的類別目錄中")


def quick_start_training():
    """快速開始訓練的簡化版本"""
    print("\n🚀 快速開始訓練")
    
    # 檢查是否有數據
    data_dir = "./data"
    if not Path(data_dir).exists():
        print(f"❌ 找不到數據目錄: {data_dir}")
        print("💡 使用以下命令創建示例結構:")
        print("   python classifier_usage_example.py --create-sample")
        return
    
    # 創建簡化配置
    config = ClassifierConfig(
        data_dir=data_dir,
        output_dir="./quick_output",
        model_name="efficientnet_v2_l",
        batch_size=8,           # 較小的batch size
        num_epochs=20,          # 較少的epochs用於快速測試
        learning_rate=0.001,
        image_size=224,         # 較小的圖像尺寸加速訓練
        mixed_precision=True,
        use_advanced_augmentation=False  # 關閉高級增強以加速
    )
    
    trainer = ClassificationTrainer(config)
    
    try:
        trainer.setup_data()
        trainer.setup_model()
        
        print("🎯 開始快速訓練...")
        # trainer.train()  # 取消註釋開始訓練
        
        print("✅ 快速訓練完成！")
        
    except Exception as e:
        print(f"❌ 訓練失敗: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-sample":
        create_sample_data_structure()
    elif len(sys.argv) > 1 and sys.argv[1] == "--quick-start":
        quick_start_training()
    else:
        main()