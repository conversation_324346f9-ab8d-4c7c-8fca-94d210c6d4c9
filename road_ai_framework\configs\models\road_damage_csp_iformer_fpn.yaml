# 道路損壞檢測模型配置
# CSP IFormer + FPN 組合

model_name: CSP_IFormer_FPN_RoadDamage
task_type: segmentation
description: "使用CSP-IFormer編碼器和FPN解碼器的道路損壞分割模型"

# 編碼器配置
encoder:
  encoder_type: csp_iformer
  encoder_variant: final_segmentation
  input_channels: 3
  mode: segmentation
  img_size: 512  # 道路圖像需要較高解析度
  
  # 啟用高級功能以提升道路損壞檢測精度
  enable_channel_shuffle: true
  enable_dropkey: true
  dropkey_rate: 0.15  # 略高的dropout以提升泛化
  
  # 架構參數
  depths: [3, 3, 9, 3]
  embed_dims: [96, 192, 320, 384]
  num_heads: [3, 6, 10, 12]
  attention_heads: [3, 6, 10, 12]

# 解碼器配置
decoder:
  decoder_type: fpn
  decoder_variant: road_damage
  encoder_channels: [96, 192, 320, 384]
  decoder_channels: [256, 128, 64, 32]
  num_classes: 5  # 裂縫、坑洞、剝落、修補、正常
  dropout: 0.15
  use_batchnorm: true

# 訓練配置
training:
  # 損失函數
  loss_type: combined
  loss_weights:
    dice: 0.6
    focal: 0.4
  
  # 優化器
  optimizer: adamw
  learning_rate: 1e-4
  weight_decay: 0.01
  
  # 學習率調度
  scheduler: cosine
  scheduler_params:
    T_max: 100
    eta_min: 1e-6
  
  # 數據增強
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.2
    rotation: 15
    color_jitter: 0.2
    gaussian_blur: 0.1

# 評估配置
evaluation:
  metrics:
    - iou
    - dice
    - pixel_accuracy
    - class_accuracy
  
  # 類別名稱
  class_names:
    - background
    - crack
    - pothole
    - spalling  
    - patch

# 推理配置
inference:
  input_size: [512, 512]
  batch_size: 8
  overlap: 0.1  # 滑動窗口重疊
  threshold: 0.5