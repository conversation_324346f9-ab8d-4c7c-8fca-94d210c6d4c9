#!/usr/bin/env python3
"""
全景圖像擴增工具可視化示例
"""

import os
import numpy as np
import cv2
from panorama_augmenter import PanoramaAugmenter, setup_logging


def create_demo_panorama():
    """創建示例全景圖像"""
    width, height = 1800, 900  # 2:1比例，較小尺寸便於快速處理
    
    # 創建漸變背景
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 天空漸變（藍色）
    for y in range(height//2):
        intensity = int(200 * (1 - y / (height//2)))
        image[y, :] = [255, intensity, intensity//2]
    
    # 地面漸變（綠色）
    for y in range(height//2, height):
        intensity = int(150 * (y - height//2) / (height//2))
        image[y, :] = [intensity//2, 255 - intensity, intensity//3]
    
    # 添加地平線
    cv2.line(image, (0, height//2), (width, height//2), (255, 255, 255), 3)
    
    # 添加太陽
    sun_x, sun_y = width//4, height//4
    cv2.circle(image, (sun_x, sun_y), 60, (100, 200, 255), -1)
    
    # 添加建築物剪影
    buildings = [
        (100, height//2, 250, height-50),
        (400, height//2, 600, height-80),
        (800, height//2, 950, height-60),
        (1200, height//2, 1400, height-90),
        (1500, height//2, 1650, height-70),
    ]
    
    for x1, y1, x2, y2 in buildings:
        cv2.rectangle(image, (x1, y1), (x2, y2), (80, 80, 80), -1)
        # 添加窗戶
        for wx in range(x1+10, x2-10, 25):
            for wy in range(y1+15, y2-15, 40):
                cv2.rectangle(image, (wx, wy), (wx+8, wy+8), (200, 200, 150), -1)
    
    # 添加指北針
    cv2.arrowedLine(image, (80, 80), (80, 40), (0, 0, 255), 3, tipLength=0.3)
    cv2.putText(image, 'N', (70, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    
    # 添加角度標記
    cv2.putText(image, '0°', (width//2-20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(image, '90°', (width*3//4-30, height//2), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(image, '180°', (width//2-30, height-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(image, '270°', (width//4-30, height//2), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return image


def demo_orientation_based_augmentation():
    """示例：基於外方位的擴增並可視化"""
    print("=== 基於外方位的擴增可視化示例 ===")
    
    # 創建示例圖像
    image = create_demo_panorama()
    image_path = "demo_panorama.jpg"
    cv2.imwrite(image_path, image)
    print(f"已創建示例全景圖像: {image_path}")
    
    # 設置日誌
    logger = setup_logging("demo_visualization.log")
    augmenter = PanoramaAugmenter(logger)
    
    # 測試不同的外方位參數
    test_cases = [
        {
            "name": "輕微傾斜",
            "orientation": (10.0, -5.0, 30.0),
            "description": "模擬相機稍微傾斜的情況"
        },
        {
            "name": "明顯傾斜",
            "orientation": (25.0, -15.0, 60.0),
            "description": "模擬相機明顯傾斜的情況"
        },
        {
            "name": "極端傾斜",
            "orientation": (-40.0, 30.0, -45.0),
            "description": "模擬相機極端傾斜的情況"
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n{i+1}. 測試 {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        print(f"   外方位: omega={test_case['orientation'][0]}, phi={test_case['orientation'][1]}, kappa={test_case['orientation'][2]}")
        
        try:
            results = augmenter.process_single_image(
                image_path=image_path,
                label_path=None,
                orientation=test_case['orientation'],
                methods=['orientation'],
                output_dir=f"demo_output_{test_case['name']}",
                visualize=True,  # 顯示可視化
                save_visualization=True  # 保存可視化圖像
            )
            
            for method, aug_results in results.items():
                print(f"   ✅ {method}: 生成 {len(aug_results)} 個變化")
                print(f"   輸出目錄: demo_output_{test_case['name']}")
                
        except Exception as e:
            print(f"   ❌ 失敗: {e}")


def demo_comparison_view():
    """示例：對比視圖"""
    print("\n=== 對比視圖示例 ===")
    
    # 創建示例圖像
    image = create_demo_panorama()
    image_path = "demo_panorama_compare.jpg"
    cv2.imwrite(image_path, image)
    
    logger = setup_logging("demo_compare.log")
    augmenter = PanoramaAugmenter(logger)
    
    # 生成一個擴增結果
    results = augmenter.process_single_image(
        image_path=image_path,
        orientation=(20.0, -10.0, 45.0),
        methods=['orientation'],
        visualize=False  # 這次不顯示網格視圖
    )
    
    if results and 'orientation' in results:
        aug_image, _ = results['orientation'][0]  # 取第一個結果
        
        # 創建對比視圖
        augmenter.create_comparison_view(
            original_image=image,
            augmented_image=aug_image,
            title="全景圖像擴增前後對比",
            save_path="comparison_demo.png"
        )
        
        print("✅ 已顯示並保存對比視圖")


def demo_command_line_examples():
    """顯示命令行使用示例"""
    print("\n=== 命令行可視化使用示例 ===")
    
    print("1. 基本擴增並顯示可視化:")
    print("python panorama_augmenter.py \\")
    print("    --input demo_panorama.jpg \\")
    print("    --output output_dir \\")
    print("    --omega 15.0 --phi -10.0 --kappa 45.0 \\")
    print("    --methods orientation \\")
    print("    --visualize")
    
    print("\n2. 擴增並保存可視化圖像:")
    print("python panorama_augmenter.py \\")
    print("    --input demo_panorama.jpg \\")
    print("    --output output_dir \\")
    print("    --omega 15.0 --phi -10.0 --kappa 45.0 \\")
    print("    --methods orientation \\")
    print("    --visualize --save-visualization")
    
    print("\n3. 交互式模式（會詢問是否顯示可視化）:")
    print("python panorama_augmenter.py \\")
    print("    --output output_dir \\")
    print("    --interactive")


def main():
    """主函數"""
    print("🖼️  全景圖像擴增工具可視化示例")
    print("=" * 60)
    
    # 清理舊文件
    old_files = ["demo_panorama.jpg", "demo_panorama_compare.jpg", "comparison_demo.png"]
    for file in old_files:
        if os.path.exists(file):
            os.remove(file)
    
    try:
        # 運行示例
        demo_orientation_based_augmentation()
        demo_comparison_view()
        demo_command_line_examples()
        
        print("\n" + "=" * 60)
        print("🎉 可視化示例完成！")
        print("\n現在你可以：")
        print("• 查看彈出的matplotlib視窗來觀看擴增結果")
        print("• 檢查生成的輸出目錄中的圖像文件")
        print("• 查看保存的可視化圖像")
        print("• 使用命令行參數 --visualize 來在處理時顯示結果")
        
        print("\n💡 提示：")
        print("• 如果圖像顯示有問題，請確保已安裝matplotlib和相關GUI後端")
        print("• 可視化窗口可能會在其他窗口後面，請檢查任務欄")
        print("• 使用 --save-visualization 參數可以保存可視化圖像而不僅僅是顯示")
        
    except Exception as e:
        print(f"❌ 示例運行中發生錯誤: {e}")
        print("請確保已安裝所有必需的依賴：matplotlib, opencv-python等")
        raise


if __name__ == "__main__":
    main()