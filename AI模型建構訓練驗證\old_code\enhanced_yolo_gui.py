#!/usr/bin/env python3
"""
增強YOLO推理系統GUI界面
提供完整的參數配置和推理功能，無需命令行操作
"""

import sys
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import traceback

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QTabWidget, QLabel, QPushButton, QGroupBox, QFormLayout, QLineEdit,
        QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox, QFileDialog,
        QMessageBox, QTextEdit, QProgressBar, QTableWidget, QTableWidgetItem,
        QHeaderView, QSlider, QGridLayout, QFrame, QScrollArea, QColorDialog,
        QSplitter, QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QSettings
    from PyQt6.QtGui import QFont, QColor, QPalette, QPixmap, QIcon
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

try:
    import qdarkstyle
    QDARKSTYLE_AVAILABLE = True
except ImportError:
    QDARKSTYLE_AVAILABLE = False

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

try:
    from model_create.inference.enhanced_yolo_inference import (
        EnhancedYOLOConfig, EnhancedYOLOInference, ClassConfig,
        create_enhanced_yolo_inference, scan_labelme_annotations,
        generate_class_configs_from_labelme
    )
    ENHANCED_YOLO_AVAILABLE = True
except ImportError:
    ENHANCED_YOLO_AVAILABLE = False


class InferenceWorker(QThread):
    """推理工作線程"""
    
    progress = pyqtSignal(int)
    log_message = pyqtSignal(str)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, config, input_path, output_path, task_type, is_batch=False):
        super().__init__()
        self.config = config
        self.input_path = input_path
        self.output_path = output_path
        self.task_type = task_type
        self.is_batch = is_batch
        
    def run(self):
        try:
            self.log_message.emit("正在初始化推理器...")
            inference = create_enhanced_yolo_inference(self.config)
            
            if self.is_batch:
                self.log_message.emit("開始批次推理...")
                results = inference.batch_predict(
                    input_dir=self.input_path,
                    output_dir=self.output_path,
                    task_type=self.task_type
                )
                self.log_message.emit(f"批次推理完成，處理了 {len(results)} 張圖像")
            else:
                self.log_message.emit("開始單張圖像推理...")
                results = inference.predict_single_image(
                    image_path=self.input_path,
                    output_dir=self.output_path,
                    task_type=self.task_type
                )
                self.log_message.emit("單張圖像推理完成")
            
            self.finished.emit(results)
            
        except Exception as e:
            self.error.emit(f"推理失敗: {str(e)}\n{traceback.format_exc()}")


class ClassConfigWidget(QWidget):
    """類別配置管理組件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.class_configs = {}
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 頂部控制按鈕
        control_layout = QHBoxLayout()
        
        self.scan_labelme_btn = QPushButton("掃描LabelMe標註")
        self.scan_labelme_btn.clicked.connect(self.scan_labelme_directory)
        
        self.add_class_btn = QPushButton("添加類別")
        self.add_class_btn.clicked.connect(self.add_class)
        
        self.remove_class_btn = QPushButton("移除類別")
        self.remove_class_btn.clicked.connect(self.remove_class)
        
        self.export_btn = QPushButton("導出配置")
        self.export_btn.clicked.connect(self.export_config)
        
        self.import_btn = QPushButton("導入配置")
        self.import_btn.clicked.connect(self.import_config)
        
        control_layout.addWidget(self.scan_labelme_btn)
        control_layout.addWidget(self.add_class_btn)
        control_layout.addWidget(self.remove_class_btn)
        control_layout.addWidget(self.export_btn)
        control_layout.addWidget(self.import_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 類別列表表格
        self.class_table = QTableWidget()
        self.class_table.setColumnCount(6)
        self.class_table.setHorizontalHeaderLabels([
            "ID", "名稱", "置信度閾值", "顏色", "啟用", "描述"
        ])
        
        header = self.class_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)
        
        self.class_table.setColumnWidth(0, 50)
        self.class_table.setColumnWidth(2, 100)
        self.class_table.setColumnWidth(3, 80)
        self.class_table.setColumnWidth(4, 60)
        
        layout.addWidget(self.class_table)
        
        # 統計資訊
        self.stats_label = QLabel("尚未掃描類別")
        layout.addWidget(self.stats_label)
    
    def scan_labelme_directory(self):
        """掃描LabelMe目錄"""
        dir_path = QFileDialog.getExistingDirectory(self, "選擇LabelMe標註目錄")
        if not dir_path:
            return
        
        try:
            # 掃描標註
            scan_result = scan_labelme_annotations(dir_path)
            
            # 生成類別配置
            self.class_configs = generate_class_configs_from_labelme(dir_path)
            
            # 更新表格
            self.update_class_table()
            
            # 更新統計
            stats_text = (
                f"掃描結果: 找到 {scan_result['unique_classes']} 個類別, "
                f"{scan_result['total_annotations']} 個標註, "
                f"{scan_result['total_files']} 個文件"
            )
            self.stats_label.setText(stats_text)
            
            QMessageBox.information(self, "掃描完成", 
                                  f"成功生成 {len(self.class_configs)} 個類別配置")
            
        except Exception as e:
            QMessageBox.critical(self, "掃描失敗", f"掃描LabelMe目錄失敗:\n{str(e)}")
    
    def update_class_table(self):
        """更新類別表格"""
        self.class_table.setRowCount(len(self.class_configs))
        
        for row, (class_id, config) in enumerate(self.class_configs.items()):
            # ID
            self.class_table.setItem(row, 0, QTableWidgetItem(str(class_id)))
            
            # 名稱
            name_item = QTableWidgetItem(config.name)
            self.class_table.setItem(row, 1, name_item)
            
            # 置信度閾值
            conf_spinbox = QDoubleSpinBox()
            conf_spinbox.setRange(0.0, 1.0)
            conf_spinbox.setSingleStep(0.05)
            conf_spinbox.setValue(config.conf_threshold)
            conf_spinbox.valueChanged.connect(
                lambda v, cid=class_id: self.update_confidence(cid, v)
            )
            self.class_table.setCellWidget(row, 2, conf_spinbox)
            
            # 顏色
            color_btn = QPushButton()
            color_btn.setStyleSheet(f"background-color: rgb{config.color}; border: 1px solid black;")
            color_btn.clicked.connect(lambda checked, cid=class_id: self.change_color(cid))
            self.class_table.setCellWidget(row, 3, color_btn)
            
            # 啟用
            enable_checkbox = QCheckBox()
            enable_checkbox.setChecked(config.enabled)
            enable_checkbox.toggled.connect(
                lambda checked, cid=class_id: self.toggle_enabled(cid, checked)
            )
            self.class_table.setCellWidget(row, 4, enable_checkbox)
            
            # 描述
            desc_item = QTableWidgetItem(config.description)
            self.class_table.setItem(row, 5, desc_item)
    
    def add_class(self):
        """添加新類別"""
        class_id = max(self.class_configs.keys()) + 1 if self.class_configs else 0
        
        self.class_configs[class_id] = ClassConfig(
            name=f"新類別_{class_id}",
            conf_threshold=0.5,
            color=(255, 0, 0),
            enabled=True,
            description="手動添加的類別"
        )
        
        self.update_class_table()
    
    def remove_class(self):
        """移除選中的類別"""
        current_row = self.class_table.currentRow()
        if current_row >= 0:
            class_ids = list(self.class_configs.keys())
            if current_row < len(class_ids):
                class_id = class_ids[current_row]
                del self.class_configs[class_id]
                self.update_class_table()
    
    def update_confidence(self, class_id: int, value: float):
        """更新置信度閾值"""
        if class_id in self.class_configs:
            self.class_configs[class_id].conf_threshold = value
    
    def change_color(self, class_id: int):
        """更改類別顏色"""
        if class_id in self.class_configs:
            current_color = QColor(*self.class_configs[class_id].color)
            color = QColorDialog.getColor(current_color, self)
            
            if color.isValid():
                new_color = (color.red(), color.green(), color.blue())
                self.class_configs[class_id].color = new_color
                self.update_class_table()
    
    def toggle_enabled(self, class_id: int, enabled: bool):
        """切換類別啟用狀態"""
        if class_id in self.class_configs:
            self.class_configs[class_id].enabled = enabled
    
    def export_config(self):
        """導出類別配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存類別配置", "", "YAML files (*.yaml);;JSON files (*.json)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.yaml'):
                    import yaml
                    config_data = {
                        'class_configs': {
                            str(k): {
                                'name': v.name,
                                'conf_threshold': v.conf_threshold,
                                'color': list(v.color),
                                'enabled': v.enabled,
                                'description': v.description
                            }
                            for k, v in self.class_configs.items()
                        }
                    }
                    with open(file_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                else:
                    config_data = {
                        str(k): {
                            'name': v.name,
                            'conf_threshold': v.conf_threshold,
                            'color': list(v.color),
                            'enabled': v.enabled,
                            'description': v.description
                        }
                        for k, v in self.class_configs.items()
                    }
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, indent=2, ensure_ascii=False)
                
                QMessageBox.information(self, "導出成功", f"類別配置已保存到:\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "導出失敗", f"保存配置失敗:\n{str(e)}")
    
    def import_config(self):
        """導入類別配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "載入類別配置", "", "YAML files (*.yaml);;JSON files (*.json)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.yaml'):
                    import yaml
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    if 'class_configs' in config_data:
                        class_data = config_data['class_configs']
                    else:
                        class_data = config_data
                else:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        class_data = json.load(f)
                
                # 轉換為ClassConfig對象
                self.class_configs = {}
                for class_id, config in class_data.items():
                    self.class_configs[int(class_id)] = ClassConfig(
                        name=config['name'],
                        conf_threshold=config['conf_threshold'],
                        color=tuple(config['color']),
                        enabled=config['enabled'],
                        description=config.get('description', '')
                    )
                
                self.update_class_table()
                QMessageBox.information(self, "導入成功", f"成功載入 {len(self.class_configs)} 個類別配置")
                
            except Exception as e:
                QMessageBox.critical(self, "導入失敗", f"載入配置失敗:\n{str(e)}")
    
    def get_class_configs(self) -> Dict[int, ClassConfig]:
        """獲取當前類別配置"""
        return self.class_configs.copy()


class EnhancedYOLOGUI(QMainWindow):
    """增強YOLO推理系統主界面"""
    
    def __init__(self):
        super().__init__()
        
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt6 not available")
        
        if not ENHANCED_YOLO_AVAILABLE:
            raise ImportError("Enhanced YOLO module not available")
        
        self.config = EnhancedYOLOConfig()
        self.inference_worker = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_settings()
    
    def setup_ui(self):
        """設置用戶界面"""
        self.setWindowTitle("增強YOLO推理系統 - GUI配置界面")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QHBoxLayout(central_widget)
        
        # 創建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左側配置面板
        config_scroll = QScrollArea()
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        
        # 模型配置組
        model_group = self.create_model_config_group()
        config_layout.addWidget(model_group)
        
        # 推理配置組
        inference_group = self.create_inference_config_group()
        config_layout.addWidget(inference_group)
        
        # SAHI配置組
        sahi_group = self.create_sahi_config_group()
        config_layout.addWidget(sahi_group)
        
        # 輸出配置組
        output_group = self.create_output_config_group()
        config_layout.addWidget(output_group)
        
        # 高級配置組
        advanced_group = self.create_advanced_config_group()
        config_layout.addWidget(advanced_group)
        
        config_layout.addStretch()
        
        config_scroll.setWidget(config_widget)
        config_scroll.setWidgetResizable(True)
        config_scroll.setMaximumWidth(400)
        
        # 右側選項卡面板
        tab_widget = QTabWidget()
        
        # 類別配置選項卡
        self.class_config_widget = ClassConfigWidget()
        tab_widget.addTab(self.class_config_widget, "類別配置")
        
        # 推理選項卡
        inference_widget = self.create_inference_widget()
        tab_widget.addTab(inference_widget, "推理執行")
        
        # 日誌選項卡
        log_widget = self.create_log_widget()
        tab_widget.addTab(log_widget, "執行日誌")
        
        # 添加到分割器
        splitter.addWidget(config_scroll)
        splitter.addWidget(tab_widget)
        splitter.setSizes([400, 800])
        
        main_layout.addWidget(splitter)
        
        # 狀態欄
        self.statusBar().showMessage("就緒")
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_model_config_group(self) -> QGroupBox:
        """創建模型配置組"""
        group = QGroupBox("模型配置")
        layout = QFormLayout(group)
        
        # 檢測模型路徑
        self.detection_model_edit = QLineEdit()
        detection_btn = QPushButton("瀏覽")
        detection_btn.clicked.connect(
            lambda: self.browse_file(self.detection_model_edit, "選擇檢測模型", "PyTorch模型 (*.pt)")
        )
        
        detection_layout = QHBoxLayout()
        detection_layout.addWidget(self.detection_model_edit)
        detection_layout.addWidget(detection_btn)
        layout.addRow("檢測模型:", detection_layout)
        
        # 分割模型路徑
        self.segmentation_model_edit = QLineEdit()
        segmentation_btn = QPushButton("瀏覽")
        segmentation_btn.clicked.connect(
            lambda: self.browse_file(self.segmentation_model_edit, "選擇分割模型", "PyTorch模型 (*.pt)")
        )
        
        segmentation_layout = QHBoxLayout()
        segmentation_layout.addWidget(self.segmentation_model_edit)
        segmentation_layout.addWidget(segmentation_btn)
        layout.addRow("分割模型:", segmentation_layout)
        
        # 設備選擇
        self.device_combo = QComboBox()
        self.device_combo.addItems(["auto", "cuda", "cpu"])
        layout.addRow("計算設備:", self.device_combo)
        
        return group
    
    def create_inference_config_group(self) -> QGroupBox:
        """創建推理配置組"""
        group = QGroupBox("推理配置")
        layout = QFormLayout(group)
        
        # 圖像尺寸
        self.img_size_spin = QSpinBox()
        self.img_size_spin.setRange(320, 2048)
        self.img_size_spin.setValue(640)
        self.img_size_spin.setSingleStep(32)
        layout.addRow("圖像尺寸:", self.img_size_spin)
        
        # 全局置信度閾值
        self.global_conf_spin = QDoubleSpinBox()
        self.global_conf_spin.setRange(0.0, 1.0)
        self.global_conf_spin.setSingleStep(0.05)
        self.global_conf_spin.setValue(0.25)
        layout.addRow("全局置信度:", self.global_conf_spin)
        
        # IoU閾值
        self.iou_threshold_spin = QDoubleSpinBox()
        self.iou_threshold_spin.setRange(0.0, 1.0)
        self.iou_threshold_spin.setSingleStep(0.05)
        self.iou_threshold_spin.setValue(0.45)
        layout.addRow("IoU閾值:", self.iou_threshold_spin)
        
        # 最大檢測數量
        self.max_det_spin = QSpinBox()
        self.max_det_spin.setRange(1, 10000)
        self.max_det_spin.setValue(1000)
        layout.addRow("最大檢測數:", self.max_det_spin)
        
        return group
    
    def create_sahi_config_group(self) -> QGroupBox:
        """創建SAHI配置組"""
        group = QGroupBox("SAHI配置")
        layout = QFormLayout(group)
        
        # 啟用SAHI
        self.enable_sahi_check = QCheckBox()
        layout.addRow("啟用SAHI:", self.enable_sahi_check)
        
        # 切片高度
        self.slice_height_spin = QSpinBox()
        self.slice_height_spin.setRange(256, 2048)
        self.slice_height_spin.setValue(512)
        self.slice_height_spin.setSingleStep(32)
        layout.addRow("切片高度:", self.slice_height_spin)
        
        # 切片寬度
        self.slice_width_spin = QSpinBox()
        self.slice_width_spin.setRange(256, 2048)
        self.slice_width_spin.setValue(512)
        self.slice_width_spin.setSingleStep(32)
        layout.addRow("切片寬度:", self.slice_width_spin)
        
        # 重疊比例
        self.overlap_height_spin = QDoubleSpinBox()
        self.overlap_height_spin.setRange(0.0, 0.5)
        self.overlap_height_spin.setSingleStep(0.05)
        self.overlap_height_spin.setValue(0.2)
        layout.addRow("高度重疊比例:", self.overlap_height_spin)
        
        self.overlap_width_spin = QDoubleSpinBox()
        self.overlap_width_spin.setRange(0.0, 0.5)
        self.overlap_width_spin.setSingleStep(0.05)
        self.overlap_width_spin.setValue(0.2)
        layout.addRow("寬度重疊比例:", self.overlap_width_spin)
        
        return group
    
    def create_output_config_group(self) -> QGroupBox:
        """創建輸出配置組"""
        group = QGroupBox("輸出配置")
        layout = QFormLayout(group)
        
        # 保存可視化
        self.save_visualizations_check = QCheckBox()
        self.save_visualizations_check.setChecked(True)
        layout.addRow("保存可視化:", self.save_visualizations_check)
        
        # 保存預測結果
        self.save_predictions_check = QCheckBox()
        self.save_predictions_check.setChecked(True)
        layout.addRow("保存預測結果:", self.save_predictions_check)
        
        # 保存統計
        self.save_statistics_check = QCheckBox()
        self.save_statistics_check.setChecked(True)
        layout.addRow("保存統計:", self.save_statistics_check)
        
        # 輸出格式
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["both", "detection", "segmentation"])
        layout.addRow("輸出格式:", self.output_format_combo)
        
        # 自動轉換標註
        self.auto_convert_check = QCheckBox()
        self.auto_convert_check.setChecked(True)
        layout.addRow("自動轉換標註:", self.auto_convert_check)
        
        return group
    
    def create_advanced_config_group(self) -> QGroupBox:
        """創建高級配置組"""
        group = QGroupBox("高級功能")
        layout = QFormLayout(group)
        
        # 啟用追蹤
        self.enable_tracking_check = QCheckBox()
        layout.addRow("啟用追蹤:", self.enable_tracking_check)
        
        # 啟用姿態估計
        self.enable_pose_check = QCheckBox()
        layout.addRow("啟用姿態估計:", self.enable_pose_check)
        
        # 啟用分類
        self.enable_classification_check = QCheckBox()
        layout.addRow("啟用分類:", self.enable_classification_check)
        
        # 啟用批次處理
        self.enable_batch_check = QCheckBox()
        self.enable_batch_check.setChecked(True)
        layout.addRow("啟用批次處理:", self.enable_batch_check)
        
        return group
    
    def create_inference_widget(self) -> QWidget:
        """創建推理執行組件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 輸入配置
        input_group = QGroupBox("輸入配置")
        input_layout = QFormLayout(input_group)
        
        # 輸入路徑
        self.input_path_edit = QLineEdit()
        input_btn = QPushButton("瀏覽")
        input_btn.clicked.connect(self.browse_input)
        
        input_path_layout = QHBoxLayout()
        input_path_layout.addWidget(self.input_path_edit)
        input_path_layout.addWidget(input_btn)
        input_layout.addRow("輸入路徑:", input_path_layout)
        
        # 輸出路徑
        self.output_path_edit = QLineEdit()
        output_btn = QPushButton("瀏覽")
        output_btn.clicked.connect(self.browse_output)
        
        output_path_layout = QHBoxLayout()
        output_path_layout.addWidget(self.output_path_edit)
        output_path_layout.addWidget(output_btn)
        input_layout.addRow("輸出路徑:", output_path_layout)
        
        # 任務類型
        self.task_type_combo = QComboBox()
        self.task_type_combo.addItems(["both", "detection", "segmentation"])
        input_layout.addRow("任務類型:", self.task_type_combo)
        
        # 處理模式
        self.process_mode_combo = QComboBox()
        self.process_mode_combo.addItems(["auto", "single", "batch"])
        input_layout.addRow("處理模式:", self.process_mode_combo)
        
        layout.addWidget(input_group)
        
        # 執行按鈕
        button_layout = QHBoxLayout()
        
        self.start_inference_btn = QPushButton("開始推理")
        self.start_inference_btn.clicked.connect(self.start_inference)
        self.start_inference_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        self.stop_inference_btn = QPushButton("停止推理")
        self.stop_inference_btn.clicked.connect(self.stop_inference)
        self.stop_inference_btn.setEnabled(False)
        self.stop_inference_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        
        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.clicked.connect(self.save_config)
        
        self.load_config_btn = QPushButton("載入配置")
        self.load_config_btn.clicked.connect(self.load_config)
        
        button_layout.addWidget(self.start_inference_btn)
        button_layout.addWidget(self.stop_inference_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.load_config_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 結果顯示
        result_group = QGroupBox("推理結果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
        
        layout.addStretch()
        
        return widget
    
    def create_log_widget(self) -> QWidget:
        """創建日誌組件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 日誌控制
        control_layout = QHBoxLayout()
        
        clear_btn = QPushButton("清除日誌")
        clear_btn.clicked.connect(lambda: self.log_text.clear())
        
        save_log_btn = QPushButton("保存日誌")
        save_log_btn.clicked.connect(self.save_log)
        
        control_layout.addWidget(clear_btn)
        control_layout.addWidget(save_log_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 日誌顯示
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        return widget
    
    def setup_connections(self):
        """設置信號連接"""
        # 當模型路徑改變時，檢查模型有效性
        self.detection_model_edit.textChanged.connect(self.validate_models)
        self.segmentation_model_edit.textChanged.connect(self.validate_models)
    
    def browse_file(self, line_edit: QLineEdit, title: str, file_filter: str):
        """瀏覽文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, title, "", file_filter)
        if file_path:
            line_edit.setText(file_path)
    
    def browse_input(self):
        """瀏覽輸入路徑"""
        if self.process_mode_combo.currentText() == "batch":
            path = QFileDialog.getExistingDirectory(self, "選擇輸入目錄")
        else:
            path, _ = QFileDialog.getOpenFileName(
                self, "選擇輸入圖像", "", 
                "圖像文件 (*.jpg *.jpeg *.png *.bmp *.tiff)"
            )
        
        if path:
            self.input_path_edit.setText(path)
    
    def browse_output(self):
        """瀏覽輸出路徑"""
        path = QFileDialog.getExistingDirectory(self, "選擇輸出目錄")
        if path:
            self.output_path_edit.setText(path)
    
    def validate_models(self):
        """驗證模型路徑"""
        detection_path = self.detection_model_edit.text()
        segmentation_path = self.segmentation_model_edit.text()
        
        has_detection = detection_path and os.path.exists(detection_path)
        has_segmentation = segmentation_path and os.path.exists(segmentation_path)
        
        if has_detection or has_segmentation:
            self.start_inference_btn.setEnabled(True)
            if has_detection and has_segmentation:
                self.statusBar().showMessage("檢測和分割模型就緒")
            elif has_detection:
                self.statusBar().showMessage("檢測模型就緒")
            else:
                self.statusBar().showMessage("分割模型就緒")
        else:
            self.start_inference_btn.setEnabled(False)
            self.statusBar().showMessage("請選擇至少一個有效的模型文件")
    
    def get_config_from_ui(self) -> EnhancedYOLOConfig:
        """從UI獲取配置"""
        config = EnhancedYOLOConfig(
            # 模型配置
            detection_model_path=self.detection_model_edit.text(),
            segmentation_model_path=self.segmentation_model_edit.text(),
            device=self.device_combo.currentText(),
            
            # 推理配置
            img_size=self.img_size_spin.value(),
            global_conf=self.global_conf_spin.value(),
            iou_threshold=self.iou_threshold_spin.value(),
            max_det=self.max_det_spin.value(),
            
            # 類別配置
            class_configs=self.class_config_widget.get_class_configs(),
            
            # SAHI配置
            enable_sahi=self.enable_sahi_check.isChecked(),
            slice_height=self.slice_height_spin.value(),
            slice_width=self.slice_width_spin.value(),
            overlap_height_ratio=self.overlap_height_spin.value(),
            overlap_width_ratio=self.overlap_width_spin.value(),
            
            # 輸出配置
            save_visualizations=self.save_visualizations_check.isChecked(),
            save_predictions=self.save_predictions_check.isChecked(),
            save_statistics=self.save_statistics_check.isChecked(),
            output_format=self.output_format_combo.currentText(),
            auto_convert_annotations=self.auto_convert_check.isChecked(),
            
            # 高級功能
            enable_tracking=self.enable_tracking_check.isChecked(),
            enable_pose_estimation=self.enable_pose_check.isChecked(),
            enable_classification=self.enable_classification_check.isChecked(),
            enable_batch_processing=self.enable_batch_check.isChecked()
        )
        
        return config
    
    def start_inference(self):
        """開始推理"""
        try:
            # 獲取配置
            config = self.get_config_from_ui()
            
            # 驗證輸入
            input_path = self.input_path_edit.text().strip()
            output_path = self.output_path_edit.text().strip()
            
            if not input_path:
                QMessageBox.warning(self, "警告", "請選擇輸入路徑")
                return
            
            if not output_path:
                QMessageBox.warning(self, "警告", "請選擇輸出路徑")
                return
            
            if not os.path.exists(input_path):
                QMessageBox.warning(self, "警告", "輸入路徑不存在")
                return
            
            # 確定處理模式
            if self.process_mode_combo.currentText() == "auto":
                is_batch = os.path.isdir(input_path)
            else:
                is_batch = self.process_mode_combo.currentText() == "batch"
            
            # 創建輸出目錄
            os.makedirs(output_path, exist_ok=True)
            
            # 啟動推理線程
            self.inference_worker = InferenceWorker(
                config, input_path, output_path, 
                self.task_type_combo.currentText(), is_batch
            )
            
            self.inference_worker.progress.connect(self.progress_bar.setValue)
            self.inference_worker.log_message.connect(self.add_log)
            self.inference_worker.finished.connect(self.inference_finished)
            self.inference_worker.error.connect(self.inference_error)
            
            self.inference_worker.start()
            
            # 更新UI狀態
            self.start_inference_btn.setEnabled(False)
            self.stop_inference_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.statusBar().showMessage("推理進行中...")
            
            self.add_log("推理開始...")
            
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"啟動推理失敗:\n{str(e)}")
    
    def stop_inference(self):
        """停止推理"""
        if self.inference_worker and self.inference_worker.isRunning():
            self.inference_worker.terminate()
            self.inference_worker.wait()
            
            self.inference_finished({})
            self.add_log("推理已停止")
    
    def inference_finished(self, results: dict):
        """推理完成"""
        self.start_inference_btn.setEnabled(True)
        self.stop_inference_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("推理完成")
        
        # 顯示結果
        if results:
            result_text = "推理結果:\n"
            
            if isinstance(results, dict):
                if len(results) > 1:  # 批次結果
                    result_text += f"處理了 {len(results)} 張圖像\n"
                    
                    # 統計檢測數量
                    total_detections = 0
                    for image_result in results.values():
                        if 'detection' in image_result:
                            total_detections += len(image_result['detection']['detections'])
                        if 'segmentation' in image_result:
                            total_detections += len(image_result['segmentation']['segments'])
                    
                    result_text += f"總檢測數量: {total_detections}\n"
                else:  # 單張圖像結果
                    for image_name, image_result in results.items():
                        result_text += f"圖像: {image_name}\n"
                        
                        if 'detection' in image_result:
                            det_count = len(image_result['detection']['detections'])
                            result_text += f"  檢測: {det_count} 個物件\n"
                        
                        if 'segmentation' in image_result:
                            seg_count = len(image_result['segmentation']['segments'])
                            result_text += f"  分割: {seg_count} 個區域\n"
                        
                        if 'sahi' in image_result:
                            sahi_count = len(image_result['sahi']['sahi_detections'])
                            result_text += f"  SAHI: {sahi_count} 個檢測\n"
            
            self.result_text.setText(result_text)
        
        self.add_log("推理任務完成")
    
    def inference_error(self, error_msg: str):
        """推理錯誤"""
        self.start_inference_btn.setEnabled(True)
        self.stop_inference_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("推理失敗")
        
        self.add_log(f"錯誤: {error_msg}")
        QMessageBox.critical(self, "推理錯誤", error_msg)
    
    def add_log(self, message: str):
        """添加日誌"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        self.log_text.append(log_entry)
        self.log_text.ensureCursorVisible()
    
    def save_log(self):
        """保存日誌"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日誌", "", "文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                
                QMessageBox.information(self, "保存成功", f"日誌已保存到:\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "保存失敗", f"保存日誌失敗:\n{str(e)}")
    
    def save_config(self):
        """保存完整配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置", "", "YAML文件 (*.yaml)"
        )
        
        if file_path:
            try:
                config = self.get_config_from_ui()
                
                # 轉換為可序列化格式
                config_data = {
                    'detection_model_path': config.detection_model_path,
                    'segmentation_model_path': config.segmentation_model_path,
                    'device': config.device,
                    'img_size': config.img_size,
                    'global_conf': config.global_conf,
                    'iou_threshold': config.iou_threshold,
                    'max_det': config.max_det,
                    'enable_sahi': config.enable_sahi,
                    'slice_height': config.slice_height,
                    'slice_width': config.slice_width,
                    'overlap_height_ratio': config.overlap_height_ratio,
                    'overlap_width_ratio': config.overlap_width_ratio,
                    'save_visualizations': config.save_visualizations,
                    'save_predictions': config.save_predictions,
                    'save_statistics': config.save_statistics,
                    'output_format': config.output_format,
                    'auto_convert_annotations': config.auto_convert_annotations,
                    'enable_tracking': config.enable_tracking,
                    'enable_pose_estimation': config.enable_pose_estimation,
                    'enable_classification': config.enable_classification,
                    'enable_batch_processing': config.enable_batch_processing,
                    'class_configs': {
                        str(k): {
                            'name': v.name,
                            'conf_threshold': v.conf_threshold,
                            'color': list(v.color),
                            'enabled': v.enabled,
                            'description': v.description
                        }
                        for k, v in config.class_configs.items()
                    }
                }
                
                import yaml
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                
                QMessageBox.information(self, "保存成功", f"配置已保存到:\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "保存失敗", f"保存配置失敗:\n{str(e)}")
    
    def load_config(self):
        """載入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "載入配置", "", "YAML文件 (*.yaml)"
        )
        
        if file_path:
            try:
                import yaml
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                # 更新UI
                if 'detection_model_path' in config_data:
                    self.detection_model_edit.setText(config_data['detection_model_path'])
                if 'segmentation_model_path' in config_data:
                    self.segmentation_model_edit.setText(config_data['segmentation_model_path'])
                if 'device' in config_data:
                    self.device_combo.setCurrentText(config_data['device'])
                if 'img_size' in config_data:
                    self.img_size_spin.setValue(config_data['img_size'])
                if 'global_conf' in config_data:
                    self.global_conf_spin.setValue(config_data['global_conf'])
                if 'iou_threshold' in config_data:
                    self.iou_threshold_spin.setValue(config_data['iou_threshold'])
                if 'max_det' in config_data:
                    self.max_det_spin.setValue(config_data['max_det'])
                if 'enable_sahi' in config_data:
                    self.enable_sahi_check.setChecked(config_data['enable_sahi'])
                
                # 載入類別配置
                if 'class_configs' in config_data:
                    class_configs = {}
                    for class_id, class_data in config_data['class_configs'].items():
                        class_configs[int(class_id)] = ClassConfig(
                            name=class_data['name'],
                            conf_threshold=class_data['conf_threshold'],
                            color=tuple(class_data['color']),
                            enabled=class_data['enabled'],
                            description=class_data.get('description', '')
                        )
                    
                    self.class_config_widget.class_configs = class_configs
                    self.class_config_widget.update_class_table()
                
                QMessageBox.information(self, "載入成功", f"配置已從以下位置載入:\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "載入失敗", f"載入配置失敗:\n{str(e)}")
    
    def load_settings(self):
        """載入設置"""
        settings = QSettings("EnhancedYOLO", "GUI")
        
        # 恢復窗口幾何
        self.restoreGeometry(settings.value("geometry", b""))
        
        # 恢復最後使用的路徑
        last_detection_model = settings.value("detection_model", "")
        last_segmentation_model = settings.value("segmentation_model", "")
        last_input_path = settings.value("input_path", "")
        last_output_path = settings.value("output_path", "")
        
        if last_detection_model:
            self.detection_model_edit.setText(last_detection_model)
        if last_segmentation_model:
            self.segmentation_model_edit.setText(last_segmentation_model)
        if last_input_path:
            self.input_path_edit.setText(last_input_path)
        if last_output_path:
            self.output_path_edit.setText(last_output_path)
    
    def save_settings(self):
        """保存設置"""
        settings = QSettings("EnhancedYOLO", "GUI")
        
        # 保存窗口幾何
        settings.setValue("geometry", self.saveGeometry())
        
        # 保存路徑
        settings.setValue("detection_model", self.detection_model_edit.text())
        settings.setValue("segmentation_model", self.segmentation_model_edit.text())
        settings.setValue("input_path", self.input_path_edit.text())
        settings.setValue("output_path", self.output_path_edit.text())
    
    def closeEvent(self, event):
        """關閉事件"""
        # 停止推理線程
        if self.inference_worker and self.inference_worker.isRunning():
            self.inference_worker.terminate()
            self.inference_worker.wait()
        
        # 保存設置
        self.save_settings()
        
        event.accept()


def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("增強YOLO推理系統")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("EnhancedYOLO")
    
    # 設置樣式
    if QDARKSTYLE_AVAILABLE:
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
    
    # 創建並顯示主窗口
    try:
        window = EnhancedYOLOGUI()
        window.show()
        
        return app.exec()
        
    except Exception as e:
        QMessageBox.critical(None, "啟動錯誤", f"應用程式啟動失敗:\n{str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())