#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圖像樣本抽取工具 - EXE打包腳本
使用PyInstaller將Python應用程式打包為獨立的exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """檢查必要的套件是否已安裝"""
    print("🔍 檢查必要套件...")
    
    required_packages = [
        'PyQt6',
        'pyinstaller',
        'pillow'  # 可能需要的圖像處理套件
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少套件: {', '.join(missing_packages)}")
        print("請執行以下命令安裝:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有必要套件已安裝\n")
    return True

def create_spec_file():
    """創建PyInstaller spec文件"""
    print("📝 創建PyInstaller配置文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['image_sample_extractor.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'json',
        'pathlib',
        'shutil',
        'random'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='圖像樣本抽取工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 設為False隱藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 如果有圖標文件，在此指定路徑
    version_file=None,
)
'''
    
    with open('image_sample_extractor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已創建 image_sample_extractor.spec")

def create_requirements_file():
    """創建requirements.txt文件"""
    print("📝 創建requirements.txt...")
    
    requirements = '''PyQt6>=6.0.0
pyinstaller>=5.0.0
pathlib2>=2.3.0
Pillow>=8.0.0
'''
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements)
    
    print("✅ 已創建 requirements.txt")

def build_executable():
    """使用PyInstaller建構exe文件"""
    print("🔨 開始建構exe文件...")
    
    # 清理之前的建構文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理舊的build資料夾")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')  
        print("🧹 清理舊的dist資料夾")
    
    # 執行PyInstaller
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'image_sample_extractor.spec'
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        print("✅ 建構成功!")
        
        # 檢查生成的文件
        exe_path = Path('dist') / '圖像樣本抽取工具.exe'
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"📦 生成的exe文件: {exe_path}")
            print(f"📏 文件大小: {file_size:.1f} MB")
        else:
            print("⚠️ 未找到生成的exe文件")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 建構失敗:")
        print(f"錯誤輸出: {e.stderr}")
        return False
    
    return True

def create_batch_script():
    """創建便捷的批次文件"""
    print("📝 創建批次文件...")
    
    batch_content = '''@echo off
chcp 65001 >nul
echo 圖像樣本抽取工具 - 一鍵打包腳本
echo =====================================
echo.

echo 正在檢查Python環境...
python --version
if errorlevel 1 (
    echo 錯誤: 未找到Python環境
    pause
    exit /b 1
)

echo.
echo 正在執行打包腳本...
python build_exe.py

echo.
echo 打包完成! 請檢查 dist 資料夾中的exe文件
pause
'''
    
    with open('build.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 已創建 build.bat")

def main():
    """主要執行函數"""
    print("🚀 圖像樣本抽取工具 - EXE打包工具")
    print("=" * 50)
    
    # 檢查當前目錄
    if not os.path.exists('image_sample_extractor.py'):
        print("❌ 找不到 image_sample_extractor.py 文件")
        print("請確保在正確的目錄中執行此腳本")
        return False
    
    # 1. 檢查必要套件
    if not check_requirements():
        return False
    
    # 2. 創建配置文件
    create_spec_file()
    create_requirements_file()
    create_batch_script()
    
    # 3. 建構exe
    if not build_executable():
        return False
    
    print("\n🎉 打包完成!")
    print("=" * 50)
    print("📁 輸出文件位置:")
    print(f"   EXE文件: dist/圖像樣本抽取工具.exe")
    print(f"   spec文件: image_sample_extractor.spec")
    print(f"   需求文件: requirements.txt")
    print(f"   批次文件: build.bat")
    
    print("\n💡 使用說明:")
    print("1. 將 dist 資料夾中的exe文件複製到目標電腦")
    print("2. 雙擊exe文件即可運行（無需安裝Python）")
    print("3. 如需重新打包，執行 build.bat 即可")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 打包失敗，請檢查錯誤信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏸️ 用戶中斷操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 發生未預期的錯誤: {str(e)}")
        sys.exit(1)