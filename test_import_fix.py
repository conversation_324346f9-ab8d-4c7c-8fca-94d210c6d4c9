#\!/usr/bin/env python3
"""
測試enhanced_yolo_inference.py中的函數導入是否修復成功
"""

import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🔧 測試導入修復...")

try:
    # 測試所有需要的函數導入
    from AI模型建構訓練驗證.model_create.inference.enhanced_yolo_inference import (
        modify_class_confidence,
        create_quick_class_configs,
        create_manual_class_configs,
        create_balanced_class_configs,
        EnhancedYOLOConfig,
        create_enhanced_yolo_inference,
        scan_labelme_annotations,
        generate_class_configs_from_labelme,
        validate_and_display_class_configs
    )
    
    print("✅ 所有函數導入成功\!")
    
    # 測試create_quick_class_configs函數
    print("\n🧪 測試 create_quick_class_configs 函數...")
    class_names = ["test_class1", "test_class2"]
    conf_thresholds = [0.3, 0.7]
    
    result = create_quick_class_configs(class_names, conf_thresholds)
    print(f"✅ create_quick_class_configs 測試成功，返回了 {len(result)} 個配置")
    
    # 測試modify_class_confidence函數
    print("\n🧪 測試 modify_class_confidence 函數...")
    adjustments = {"test_class1": 0.15}
    
    modified_result = modify_class_confidence(result, adjustments)
    print(f"✅ modify_class_confidence 測試成功")
    
    print("\n🎉 所有測試通過\! 導入問題已修復\!")
    
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 測試失敗: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF < /dev/null
