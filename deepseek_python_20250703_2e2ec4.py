import os
import time
import random
import requests
import json
from openpyxl import load_workbook
from urllib.parse import urlparse

# 設定檔案路徑
EXCEL_FILE = r"C:\Users\<USER>\Downloads\巡檢紀錄分析_20250623.xlsx"  # 輸入的Excel檔案
OUTPUT_DIR = r"D:\5_Hole_cover\total_image"  # 圖片輸出目錄
LOG_FILE = os.path.join(OUTPUT_DIR, "download_log.json")  # 下載記錄檔

def load_download_log():
    """載入已下載的URL記錄"""
    if os.path.exists(LOG_FILE):
        with open(LOG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return {"downloaded_urls": [], "failed_urls": []}

def save_download_log(log_data):
    """保存下載記錄"""
    with open(LOG_FILE, "w", encoding="utf-8") as f:
        json.dump(log_data, f, ensure_ascii=False, indent=2)

def get_filename_from_url(url):
    """從URL提取原始檔名"""
    try:
        filename = os.path.basename(urlparse(url).path.split("?")[0])
        return filename if filename else None
    except:
        return None

def download_image(url, folder_path, log_data):
    """
    下載單張圖片（支援中斷恢復）
    :return: 下載成功返回檔案路徑，失敗返回None
    """
    if url in log_data["downloaded_urls"]:
        print(f"已跳過（先前已下載）: {url}")
        return None

    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

    try:
        time.sleep(random.uniform(1, 10))  # 隨機延遲

        # 獲取原始檔名
        filename = None
        try:
            head_resp = requests.head(url, headers=headers, timeout=5, allow_redirects=True)
            if "Content-Disposition" in head_resp.headers:
                filename = head_resp.headers["Content-Disposition"].split("filename=")[1].strip('"\'')
        except:
            pass

        if not filename:
            filename = get_filename_from_url(url) or f"image_{int(time.time())}.jpg"

        # 處理副檔名和重複檔名
        if not os.path.splitext(filename)[1]:
            filename += ".jpg"
        save_path = os.path.join(folder_path, filename)
        counter = 1
        while os.path.exists(save_path):
            name, ext = os.path.splitext(filename)
            save_path = os.path.join(folder_path, f"{name}_{counter}{ext}")
            counter += 1

        # 正式下載
        response = requests.get(url, headers=headers, stream=True, timeout=10)
        response.raise_for_status()
        with open(save_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        # 更新記錄
        log_data["downloaded_urls"].append(url)
        if url in log_data["failed_urls"]:
            log_data["failed_urls"].remove(url)
        save_download_log(log_data)

        print(f"下載成功: {save_path}")
        return save_path

    except Exception as e:
        print(f"下載失敗: {url} - 錯誤: {str(e)}")
        if url not in log_data["failed_urls"]:
            log_data["failed_urls"].append(url)
            save_download_log(log_data)
        return None

def process_excel():
    """主處理函數"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    log_data = load_download_log()
    total_count = success_count = skip_count = 0

    try:
        wb = load_workbook(EXCEL_FILE)
        ws = wb.active
        headers = [cell.value for cell in ws[1]]
        photo_col = headers.index("巡檢照片(遠照)") + 1
        type1_col = headers.index("外巡-外觀圖樣") + 1
        type2_col = headers.index("外巡-框蓋形式") + 1
    except Exception as e:
        print(f"Excel讀取失敗: {str(e)}")
        return

    for row in ws.iter_rows(min_row=2):
        photo_url = row[photo_col-1].value
        if not photo_url:
            continue

        total_count += 1
        type1 = str(row[type1_col-1].value or "").strip()
        type2 = str(row[type2_col-1].value or "").strip()

        # 關鍵修改：當兩個欄位都為空時直接跳過
        if not type1 and not type2:
            print(f"已跳過（孔蓋下地）: {photo_url}")
            skip_count += 1
            continue

        # 建立分類資料夾
        folder_name = f"{type1}-{type2}".strip("-")
        folder_path = os.path.join(OUTPUT_DIR, "".join(
            c for c in folder_name if c.isalnum() or c in (" ", "-", "_")
        ))
        os.makedirs(folder_path, exist_ok=True)

        if download_image(photo_url, folder_path, log_data):
            success_count += 1

    print(f"\n== 處理完成 ==")
    print(f"總圖片數: {total_count}")
    print(f"成功下載: {success_count}")
    print(f"跳過（孔蓋下地）: {skip_count}")
    print(f"失敗數量: {total_count - success_count - skip_count}")
    print(f"記錄檔: {LOG_FILE}")

if __name__ == "__main__":
    print("=== 圖片下載程式（跳過孔蓋下地）===")
    start_time = time.time()
    process_excel()
    print(f"總執行時間: {time.time() - start_time:.2f} 秒")