import os
import numpy as np
import logging
import cv2
import matplotlib.pyplot as plt
from PIL import Image ,ImageDraw, ImageFont
import torch
from torch.utils.data import Dataset
import albumentations as A
from albumentations.pytorch import ToTensorV2
import json

"""設置繪圖樣式為英文，避免中文顯示問題"""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
plt.rcParams['axes.unicode_minus'] = False

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('YOLODataset')


def denormalize(img):
    """反歸一化圖像以進行視覺化
    
    Args:
        img: 歸一化後的圖像張量或數組
        
    Returns:
        反歸一化後的圖像 (0-255 範圍)
    """
    if isinstance(img, torch.Tensor):
        img = img.clone().detach().cpu().numpy()
        
    # 如果是 CHW 格式，轉換為 HWC
    if img.ndim == 3 and img.shape[0] == 3:
        img = np.transpose(img, (1, 2, 0))
    
    # 反歸一化 (使用與 A.Normalize() 相同但反向的參數)
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    
    img = img * std + mean  # 反歸一化
    img = np.clip(img, 0, 1)  # 確保值在 [0, 1] 範圍內
    img = (img * 255).astype(np.uint8)  # 轉換到 [0, 255] 範圍
    
    return img

def convert_labelme_to_yolo(input_dir, output_root=None, class_mapping=None, copy_images=False, log_enabled=True):
    """
    將 labelme JSON 格式的標註檔案轉換為 YOLO 格式
    
    參數:
        input_dir (str): 輸入目錄，包含 labelme json 檔案和圖像
        output_root (str): 輸出根目錄，將在此目錄下建立 'labels' 和 'images' 子目錄
                         若為 None，則使用 input_dir 作為輸出根目錄
        class_mapping (dict): 類別名稱到數字 ID 的映射，例如 {'class1': 0, 'class2': 1}
                             若為 None，則自動從 JSON 檔案中提取所有類別並按字母順序編號
        copy_images (bool): 是否複製圖像檔案到輸出目錄
        log_enabled (bool): 是否啟用日誌輸出
        
    返回:
        tuple: (成功轉換的檔案數量, 類別映射字典)
    """
    import json
    import os
    import shutil
    from collections import defaultdict
    
    # 設定日誌
    if log_enabled:
        import logging
        logger = logging.getLogger('LabelmeConverter')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logger.addHandler(handler)
    
    if not os.path.exists(input_dir):
        raise FileNotFoundError(f"找不到輸入目錄: {input_dir}")
        
    # 設定輸出目錄
    if output_root is None:
        output_root = input_dir
        
    # 檢查輸入目錄中的子目錄
    subdirs = [d for d in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, d))]
    
    # 檢測輸入目錄結構 - 看是否已經有 train/val/test 等子目錄
    has_splits = any(d in ['train', 'val', 'test'] for d in subdirs)
    
    if has_splits:
        if log_enabled:
            logger.info(f"檢測到資料集分割: {[d for d in subdirs if d in ['train', 'val', 'test']]}")
            
        # 對每個分割處理
        splits = [d for d in subdirs if d in ['train', 'val', 'test']]
        total_success = 0
        all_class_mapping = None
        
        for split in splits:
            split_input_dir = os.path.join(input_dir, split)
            split_output_dir = os.path.join(output_root, split)
            
            # 確保輸出目錄存在
            os.makedirs(os.path.join(split_output_dir, 'labels'), exist_ok=True)
            if copy_images:
                os.makedirs(os.path.join(split_output_dir, 'images'), exist_ok=True)
                
            # 處理此分割
            success_count, split_class_mapping = _convert_labelme_dir(
                split_input_dir, 
                os.path.join(split_output_dir, 'labels'),
                os.path.join(split_output_dir, 'images') if copy_images else None,
                class_mapping,
                log_enabled
            )
            
            total_success += success_count
            
            # 保存所有類別映射
            if all_class_mapping is None:
                all_class_mapping = split_class_mapping
            elif split_class_mapping:
                # 合併字典
                for k, v in split_class_mapping.items():
                    if k not in all_class_mapping:
                        all_class_mapping[k] = v
        
        # 將類別映射保存為 JSON 檔案
        mapping_path = os.path.join(output_root, 'class_mapping.json')
        with open(mapping_path, 'w', encoding='utf-8') as f:
            json.dump(all_class_mapping, f, ensure_ascii=False, indent=2)
            
        return total_success, all_class_mapping
    else:
        # 沒有分割，直接處理整個目錄
        labels_dir = os.path.join(output_root, 'labels')
        images_dir = os.path.join(output_root, 'images') if copy_images else None
        
        # 確保輸出目錄存在
        os.makedirs(labels_dir, exist_ok=True)
        if copy_images:
            os.makedirs(images_dir, exist_ok=True)
            
        return _convert_labelme_dir(input_dir, labels_dir, images_dir, class_mapping, log_enabled)

def _convert_labelme_dir(input_dir, labels_dir, images_dir=None, class_mapping=None, log_enabled=True):
    """
    處理單個目錄中的 labelme JSON 檔案
    
    參數:
        input_dir (str): 輸入目錄，包含 labelme json 檔案和圖像
        labels_dir (str): 輸出標籤目錄
        images_dir (str): 輸出圖像目錄，若為 None 則不複製圖像
        class_mapping (dict): 類別名稱到數字 ID 的映射
        log_enabled (bool): 是否啟用日誌輸出
        
    返回:
        tuple: (成功轉換的檔案數量, 類別映射字典)
    """
    import json
    import os
    import shutil
    from collections import defaultdict
    
    # 設定日誌
    if log_enabled:
        import logging
        logger = logging.getLogger('LabelmeConverter')
    
    # 如果沒有提供類別映射，自動生成
    if class_mapping is None:
        # 先掃描所有 JSON 檔案收集類別
        class_names = set()
        for filename in os.listdir(input_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(input_dir, filename), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    for shape in data.get('shapes', []):
                        class_names.add(shape.get('label', ''))
                except Exception as e:
                    if log_enabled:
                        logger.error(f"讀取 JSON 檔案 {filename} 時發生錯誤: {e}")
                        
        # 移除空標籤
        if '' in class_names:
            class_names.remove('')
            
        # 按字母順序排序並分配數字 ID
        sorted_names = sorted(list(class_names))
        class_mapping = {name: idx for idx, name in enumerate(sorted_names)}
        
        if log_enabled:
            logger.info(f"自動生成類別映射: {class_mapping}")
            
    # 統計每個類別的數量
    class_counts = defaultdict(int)
    success_count = 0
    
    # 處理每個 JSON 檔案
    for filename in os.listdir(input_dir):
        if not filename.endswith('.json'):
            continue
            
        try:
            json_path = os.path.join(input_dir, filename)
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            image_width = data.get('imageWidth', 0)
            image_height = data.get('imageHeight', 0)
            
            # 確保有有效的圖像尺寸
            if image_width <= 0 or image_height <= 0:
                # 如果 JSON 中沒有寬高信息，嘗試從對應的圖像檔案獲取
                img_filename = data.get('imagePath', '').split('/')[-1]
                img_path = os.path.join(input_dir, img_filename)
                
                if os.path.exists(img_path):
                    from PIL import Image
                    with Image.open(img_path) as img:
                        image_width, image_height = img.size
                else:
                    if log_enabled:
                        logger.warning(f"無法獲取圖像尺寸: {filename}")
                    continue
                    
            # 建立 YOLO 格式的輸出檔名
            base_name = os.path.splitext(filename)[0]
            output_filename = f"{base_name}.txt"
            output_path = os.path.join(labels_dir, output_filename)
            
            # 收集 YOLO 格式的標註
            yolo_annotations = []
            for shape in data.get('shapes', []):
                label = shape.get('label', '')
                shape_type = shape.get('shape_type', '')
                points = shape.get('points', [])
                
                # 跳過無效標籤
                if not label or label not in class_mapping:
                    if log_enabled:
                        logger.warning(f"跳過未知標籤 '{label}' 在檔案 {filename}")
                    continue
                    
                class_id = class_mapping[label]
                class_counts[label] += 1
                
                # 處理矩形（轉換為 YOLO 格式的邊界框）
                if shape_type == 'rectangle' and len(points) == 2:
                    # labelme 矩形格式：[[x1, y1], [x2, y2]]
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    
                    # 確保坐標順序正確（左上到右下）
                    xmin = min(x1, x2)
                    ymin = min(y1, y2)
                    xmax = max(x1, x2)
                    ymax = max(y1, y2)
                    
                    # 計算 YOLO 格式的中心點和寬高
                    x_center = (xmin + xmax) / 2 / image_width
                    y_center = (ymin + ymax) / 2 / image_height
                    width = (xmax - xmin) / image_width
                    height = (ymax - ymin) / image_height
                    
                    # 限制在 [0, 1] 範圍內
                    x_center = max(0, min(1, x_center))
                    y_center = max(0, min(1, y_center))
                    width = max(0, min(1, width))
                    height = max(0, min(1, height))
                    
                    # 添加 YOLO 格式的標註
                    yolo_annotations.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
                    
                # 處理多邊形
                elif shape_type == 'polygon' and len(points) >= 3:
                    # YOLO 格式的多邊形標註：class_id x1 y1 x2 y2 ... xn yn
                    yolo_polygon = [f"{class_id}"]
                    
                    for x, y in points:
                        # 歸一化座標
                        x_norm = max(0, min(1, x / image_width))
                        y_norm = max(0, min(1, y / image_height))
                        yolo_polygon.extend([f"{x_norm:.6f}", f"{y_norm:.6f}"])
                        
                    yolo_annotations.append(" ".join(yolo_polygon))
                
                # 處理點
                elif shape_type == 'point' and len(points) == 1:
                    x, y = points[0]
                    x_norm = max(0, min(1, x / image_width))
                    y_norm = max(0, min(1, y / image_height))
                    
                    # 點可以表示為非常小的框
                    size = 0.005  # 相對於圖像大小的 0.5%
                    yolo_annotations.append(f"{class_id} {x_norm:.6f} {y_norm:.6f} {size:.6f} {size:.6f}")
                
                # 處理線條 (可選)
                elif shape_type == 'line' and len(points) >= 2:
                    # 將線條轉換為多邊形格式
                    yolo_line = [f"{class_id}"]
                    
                    for x, y in points:
                        x_norm = max(0, min(1, x / image_width))
                        y_norm = max(0, min(1, y / image_height))
                        yolo_line.extend([f"{x_norm:.6f}", f"{y_norm:.6f}"])
                        
                    yolo_annotations.append(" ".join(yolo_line))
                    
                elif log_enabled:
                    logger.warning(f"未支援的形狀類型 '{shape_type}' 在檔案 {filename}")
                    
            # 寫入 YOLO 格式檔案
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))
                
            success_count += 1
            
            # 如果指定了圖像目錄，也可以複製圖像
            if images_dir and data.get('imagePath'):
                img_filename = os.path.basename(data.get('imagePath'))
                img_src = os.path.join(input_dir, img_filename)
                img_dst = os.path.join(images_dir, img_filename)
                
                if os.path.exists(img_src) and not os.path.exists(img_dst):
                    shutil.copy(img_src, img_dst)
                    
        except Exception as e:
            if log_enabled:
                logger.error(f"處理檔案 {filename} 時發生錯誤: {e}")
                
    # 輸出統計信息
    if log_enabled:
        logger.info(f"成功轉換 {success_count} 個標註檔案")
        logger.info(f"類別統計: {dict(class_counts)}")
        
    # 將類別映射保存為 JSON 檔案
    mapping_path = os.path.join(labels_dir, 'class_mapping.json')
    with open(mapping_path, 'w', encoding='utf-8') as f:
        json.dump(class_mapping, f, ensure_ascii=False, indent=2)
        
    return success_count, class_mapping

class YOLODataset(Dataset):
    def __init__(self, data_dir, size, split='train', transform=None, log_enabled=True):
        """
        初始化 YOLO 格式的資料集
        
        參數:
            data_dir (str): 資料目錄路徑
            size (int): 輸出圖像大小
            split (str): 資料集分割 ('train', 'val', 'test')
            transform: albumentations 轉換
            log_enabled (bool): 是否啟用日誌
        """
        self.size = size
        self.split = split
        self.data_dir = data_dir
        self.transform = transform
        self.log_enabled = log_enabled
        self.use_tensor = False  # 檢查是否使用 ToTensorV2
        
        # 檢查轉換中是否含有 ToTensorV2
        if transform is not None:
            for t in transform:
                if isinstance(t, ToTensorV2):
                    self.use_tensor = True
                    if self.log_enabled:
                        logger.info("檢測到 ToTensorV2 轉換")
                    break
        
        self.img_files = []
        self.label_files = []
        
        # 記錄開始讀取資料
        if self.log_enabled:
            logger.info(f"開始初始化 {split} 資料集")
        
        # 檢查資料目錄結構
        if not os.path.exists(data_dir):
            raise FileNotFoundError(f"找不到資料目錄: {data_dir}")
        
        # 先尋找圖像檔案
        self._find_images_and_labels()
        
        if self.log_enabled:
            logger.info(f"找到 {len(self.img_files)} 筆資料")
    
    def _find_images_and_labels(self):
        """尋找所有圖像和對應的標籤檔案"""
        # 圖像可能在的目錄
        image_dirs = [
            os.path.join(self.data_dir, 'images', self.split),
            os.path.join(self.data_dir, 'JPEGImages'),
            self.data_dir
        ]
        
        # 標籤可能在的目錄
        label_dirs = [
            os.path.join(self.data_dir, 'labels', self.split),
            os.path.join(self.data_dir, 'labels'),
            os.path.join(self.data_dir, 'Annotations'),
            self.data_dir
        ]
        
        # 檢查分割檔案
        split_file = None
        possible_split_paths = [
            os.path.join(self.data_dir, f'images/{self.split}.txt'),
            os.path.join(self.data_dir, f'ImageSets/Main/{self.split}.txt'),
            os.path.join(self.data_dir, f'{self.split}.txt')
        ]
        
        for path in possible_split_paths:
            if os.path.exists(path):
                split_file = path
                break
        
        if split_file:
            # 從分割檔案讀取檔名
            if self.log_enabled:
                logger.info(f"讀取分割檔案: {split_file}")
                
            with open(split_file, 'r') as f:
                lines = f.readlines()
                
            for line in lines:
                img_name = line.strip()
                img_file = self._find_file(img_name, image_dirs, ['.jpg', '.png', '.jpeg'])
                label_file = self._find_file(img_name, label_dirs, ['.txt'])
                
                if img_file and label_file:
                    self.img_files.append(img_file)
                    self.label_files.append(label_file)
        else:
            # 直接掃描目錄尋找圖像和標籤
            if self.log_enabled:
                logger.info("找不到分割檔案，將掃描目錄尋找圖像和標籤")
                
            # 掃描所有可能的圖像目錄
            for image_dir in image_dirs:
                if not os.path.exists(image_dir):
                    continue
                    
                for filename in os.listdir(image_dir):
                    if filename.endswith(('.jpg', '.png', '.jpeg')):
                        base_name = os.path.splitext(filename)[0]
                        img_file = os.path.join(image_dir, filename)
                        
                        # 尋找對應的標籤檔案
                        label_file = None
                        for label_dir in label_dirs:
                            if not os.path.exists(label_dir):
                                continue
                                
                            possible_label = os.path.join(label_dir, f"{base_name}.txt")
                            if os.path.exists(possible_label):
                                label_file = possible_label
                                break
                                
                        if label_file:
                            self.img_files.append(img_file)
                            self.label_files.append(label_file)
    
    def _find_file(self, base_name, dirs, extensions):
        """在多個目錄中尋找檔案"""
        for dir_path in dirs:
            if not os.path.exists(dir_path):
                continue
                
            for ext in extensions:
                file_path = os.path.join(dir_path, f"{base_name}{ext}")
                if os.path.exists(file_path):
                    return file_path
                    
        return None
    
    def __len__(self):
        """返回資料集大小"""
        return len(self.img_files)
        
    def __getitem__(self, idx):
        """獲取資料項，並返回檔名"""
        if self.log_enabled:
            logger.info(f"讀取第 {idx} 筆資料")
            
        # 獲取檔案路徑
        img_file = self.img_files[idx]
        label_file = self.label_files[idx]
        
        # 獲取檔名（不含路徑和副檔名）
        filename = os.path.basename(img_file)
        
        # 讀取圖片
        image = np.array(Image.open(img_file).convert('RGB'))
        original_height, original_width = image.shape[:2]
        
        if self.log_enabled:
            logger.info(f"圖片尺寸: {original_width}x{original_height}")
        
        # 讀取標籤檔案
        mask, boxes, labels = self._read_yolo_label(label_file, original_width, original_height)
        
        # 保存原始圖片和掩碼用於視覺化
        self.last_processed = {
            'image': image.copy(),
            'mask': mask.copy(),
            'original_image': image.copy(),
            'boxes': boxes.copy() if isinstance(boxes, np.ndarray) else boxes.copy() if boxes else [],
            'labels': labels.copy() if isinstance(labels, np.ndarray) else labels.copy() if labels else [],
            'filename': filename  # 保存檔名
        }
        
        # 應用轉換
        if self.transform:
            if self.log_enabled:
                logger.info("應用資料增強")
            
            # 如果有邊界框，使用 bbox_params
            if boxes and len(boxes) > 0:
                transformed = self.transform(
                    image=image,
                    mask=mask,
                    bboxes=boxes,
                    category_ids=labels
                )
                image = transformed["image"]
                mask = transformed["mask"]
                boxes = transformed.get("bboxes", [])
                labels = transformed.get("category_ids", [])
            else:
                transformed = self.transform(image=image, mask=mask)
                image = transformed["image"]
                mask = transformed["mask"]
            
            # 保存處理後的圖像和掩碼 (若是張量則先轉回 NumPy 以供視覺化)
            if self.use_tensor:
                if self.log_enabled:
                    logger.info("檢測到張量輸出，為視覺化轉換回 NumPy 格式")
                # 保存轉換後但尚未轉為張量的版本 (用於視覺化)
                if isinstance(image, torch.Tensor):
                    # 如果是張量，將其轉為 NumPy
                    self.last_processed['processed_image'] = image.permute(1, 2, 0).cpu().numpy()
                    
                if isinstance(mask, torch.Tensor):
                    self.last_processed['processed_mask'] = mask.cpu().numpy()
            else:
                self.last_processed['processed_image'] = image
                self.last_processed['processed_mask'] = mask
            
            self.last_processed['processed_boxes'] = boxes
            self.last_processed['processed_labels'] = labels
        else:
            # 如果沒有轉換，調整大小
            resized_image = cv2.resize(image, (self.size, self.size))
            resized_mask = cv2.resize(mask, (self.size, self.size), interpolation=cv2.INTER_NEAREST)
            
            # 調整邊界框
            resized_boxes = []
            if boxes and len(boxes) > 0:
                for box in boxes:
                    # 將邊界框從 [x_min, y_min, x_max, y_max] 轉換為相對座標
                    x_min, y_min, x_max, y_max = box
                    x_scale = self.size / original_width
                    y_scale = self.size / original_height
                    
                    x_min_new = x_min * x_scale
                    y_min_new = y_min * y_scale
                    x_max_new = x_max * x_scale
                    y_max_new = y_max * y_scale
                    
                    resized_boxes.append([x_min_new, y_min_new, x_max_new, y_max_new])
            
            # 將圖片轉換為 PyTorch tensor
            image_tensor = torch.from_numpy(resized_image.transpose(2, 0, 1)).float() / 255.0
            mask_tensor = torch.from_numpy(resized_mask).long()
            
            self.last_processed['processed_image'] = resized_image
            self.last_processed['processed_mask'] = resized_mask
            self.last_processed['processed_boxes'] = resized_boxes
            self.last_processed['processed_labels'] = labels
            
            # 如果使用邊界框，可以返回更多資訊
            return image_tensor, mask_tensor, filename
        
        if self.log_enabled:
            if isinstance(image, torch.Tensor):
                logger.info(f"處理完成，返回 shape: image={image.shape}, mask={mask.shape}")
            else:
                logger.info(f"處理完成，返回 shape: image={image.shape}, mask={mask.shape}")
        
        # 返回圖像、掩碼和檔名
        return image, mask, filename
    
    def _read_yolo_label(self, label_file, img_width, img_height):
        """讀取 YOLO 格式的標籤檔案"""
        # 初始化掩碼、邊界框和標籤
        mask = np.zeros((img_height, img_width), dtype=np.uint8)
        boxes = []
        labels = []
        
        try:
            with open(label_file, 'r') as f:
                lines = f.readlines()
                
            for line in lines:
                parts = line.strip().split()
                if not parts:
                    continue
                    
                class_id = int(parts[0])
                
                # 檢查是否為邊界框格式 (class_id x_center y_center width height)
                if len(parts) == 5:
                    # YOLO 邊界框格式
                    x_center = float(parts[1]) * img_width
                    y_center = float(parts[2]) * img_height
                    width = float(parts[3]) * img_width
                    height = float(parts[4]) * img_height
                    
                    # 轉換為 [x_min, y_min, x_max, y_max] 格式
                    x_min = max(0, x_center - width / 2)
                    y_min = max(0, y_center - height / 2)
                    x_max = min(img_width, x_center + width / 2)
                    y_max = min(img_height, y_center + height / 2)
                    
                    # 添加到列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                    
                    # 在掩碼上繪製框
                    cv2.rectangle(
                        mask,
                        (int(x_min), int(y_min)),
                        (int(x_max), int(y_max)),
                        class_id + 1,  # 加 1，因為 0 通常保留給背景
                        -1  # 填充矩形
                    )
                    
                # 檢查是否為多邊形格式 (class_id x1 y1 x2 y2 ... xn yn)
                elif len(parts) > 5 and len(parts) % 2 == 1:
                    # YOLO 多邊形格式 (class_id x1 y1 x2 y2 ... xn yn)
                    coords = [float(p) for p in parts[1:]]
                    
                    # 確保坐標數量是偶數
                    if len(coords) % 2 != 0:
                        coords = coords[:-1]
                        
                    # 將坐標轉換為多邊形點
                    points = []
                    for i in range(0, len(coords), 2):
                        x = coords[i] * img_width
                        y = coords[i + 1] * img_height
                        points.append([int(x), int(y)])
                        
                    # 在掩碼上繪製多邊形
                    points_array = np.array(points, dtype=np.int32)
                    cv2.fillPoly(mask, [points_array], class_id + 1)
                    
                    # 計算多邊形的邊界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min = max(0, min(x_coords))
                    y_min = max(0, min(y_coords))
                    x_max = min(img_width, max(x_coords))
                    y_max = min(img_height, max(y_coords))
                    
                    # 添加到列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
        except Exception as e:
            if self.log_enabled:
                logger.error(f"讀取標籤檔案 {label_file} 錯誤: {e}")
        
        return mask, boxes, labels
    
    def visualize(self, idx, show_n=3, figsize=(15, 5), class_names=None):
        """
        視覺化資料，簡化為三張圖：原始圖片、真實標籤疊加和類別說明
        
        參數:
            idx (int): 資料索引
            show_n (int): 顯示的圖片數量 (1-3)，視需要保留
            figsize (tuple): 圖形大小
            class_names (dict): 類別名稱字典，例如 {0: '背景', 1: '裂縫', 2: '坑洞'}
        """

        
        # 檢查是否有處理過的資料
        if not hasattr(self, 'last_processed'):
            # 如果沒有，則處理資料
            self[idx]
            
        # 獲取圖片和掩碼
        original_image = self.last_processed['original_image']
        processed_image = self.last_processed.get('processed_image', self.last_processed['image'])
        mask = self.last_processed.get('processed_mask', self.last_processed['mask'])
        boxes = self.last_processed.get('processed_boxes', self.last_processed.get('boxes', []))
        labels = self.last_processed.get('processed_labels', self.last_processed.get('labels', []))
        filename = self.last_processed.get('filename', 'unknown')
        
        # 確保圖像和掩碼為 NumPy 陣列
        if isinstance(processed_image, torch.Tensor):
            processed_image = processed_image.permute(1, 2, 0).cpu().numpy()
        
        if isinstance(mask, torch.Tensor):
            mask = mask.cpu().numpy()
        
        processed_image = denormalize(processed_image)
        
        # 創建圖形 (簡化為3個子圖)
        fig, axes = plt.subplots(1, 3, figsize=figsize)
        
        # 顯示原圖
        axes[0].imshow(original_image)
        axes[0].set_title(f'原始圖片: {filename}')
        axes[0].axis('off')
        
        # 產生顏色映射，確保每個類別顏色固定
        np.random.seed(42)  # 保持顏色一致
        
        # 找出所有存在的類別
        unique_classes = np.unique(mask)
        unique_classes = [c for c in unique_classes if c > 0]  # 排除背景類別
        
        # 為每個類別分配顏色
        class_colors = {}
        for cls in unique_classes:
            class_colors[cls] = np.random.randint(0, 255, 3).tolist()
        
        # 函數：使用PIL繪製中文文字
        def draw_chinese_text(img, text, position, font_size=20, text_color=(0, 0, 0)):
            # 將OpenCV圖像轉換為PIL圖像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)
            
            # 嘗試載入系統字體
            try:
                # 嘗試多種字體，以確保至少有一個可用
                font_paths = [
                    '/System/Library/Fonts/PingFang.ttc',         # macOS
                    '/usr/share/fonts/truetype/arphic/uming.ttc', # Ubuntu
                    'C:/Windows/Fonts/msyh.ttc',                  # Windows
                    'C:/Windows/Fonts/mingliu.ttc',               # Windows
                    'C:/Windows/Fonts/simsun.ttc',                # Windows
                    'C:/Windows/Fonts/kaiu.ttf',                  # Windows
                    '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
                ]
                
                font = None
                for path in font_paths:
                    try:
                        font = ImageFont.truetype(path, font_size)
                        break
                    except IOError:
                        continue
                
                # 如果沒有找到支援中文的字體，使用默認字體
                if font is None:
                    font = ImageFont.load_default()
                    
            except Exception as e:
                # 如果載入字體失敗，使用默認字體
                print(f"載入字體失敗: {e}")
                font = ImageFont.load_default()
            
            # 繪製文字
            draw.text(position, text, font=font, fill=text_color)
            
            # 將PIL圖像轉回OpenCV格式
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # 顯示標籤掩碼
        if unique_classes:
            # 創建彩色掩碼圖
            colored_mask = np.zeros_like(processed_image)
            for cls in unique_classes:
                mask_class = (mask == cls)
                for c in range(3):
                    colored_mask[:, :, c][mask_class] = class_colors[cls][c]
                    
            axes[1].imshow(colored_mask)
            axes[1].set_title('標籤分類')
            axes[1].axis('off')
        else:
            # 沒有類別時顯示空白掩碼
            axes[1].imshow(np.zeros_like(processed_image))
            axes[1].set_title('無標籤')
            axes[1].axis('off')
        
        # 創建疊加圖像 - 標籤疊加在原圖上
        overlap = processed_image.copy()
        colored_mask = np.zeros_like(overlap)
        
        # 為每個類別著色
        for cls in unique_classes:
            colored_mask[mask == cls] = class_colors[cls]
            
        # 疊加掩碼
        alpha = 0.5
        overlap = cv2.addWeighted(overlap, 1, colored_mask, alpha, 0)
        
        # 添加圖例
        if unique_classes:
            # 繪製半透明的圖例背景
            legend_x = overlap.shape[1] - 150
            legend_y = 20
            legend_height = 25 * len(unique_classes)
            
            # 半透明白色背景
            cv2.rectangle(
                overlap,
                (legend_x - 10, legend_y - 10),
                (legend_x + 140, legend_y + legend_height),
                (255, 255, 255),
                -1
            )
            # 黑色邊框
            cv2.rectangle(
                overlap,
                (legend_x - 10, legend_y - 10),
                (legend_x + 140, legend_y + legend_height),
                (0, 0, 0),
                1
            )
            
            # 繪製每個類別的顏色方塊和名稱
            for i, cls in enumerate(sorted(unique_classes)):
                # 顏色方塊
                cv2.rectangle(
                    overlap,
                    (legend_x, legend_y + i * 25),
                    (legend_x + 20, legend_y + i * 25 + 20),
                    class_colors[cls],
                    -1
                )
                
                # 類別名稱/編號
                if class_names and cls in class_names:
                    class_text = f"{class_names[cls]}"
                else:
                    class_text = f"類別 {cls}"
                    
                # 使用PIL繪製中文
                overlap = draw_chinese_text(
                    overlap, 
                    class_text, 
                    (legend_x + 30, legend_y + i * 25), 
                    font_size=16
                )
        
        # 繪製邊界框
        for i, box in enumerate(boxes):
            if len(box) != 4:
                continue
            
            x_min, y_min, x_max, y_max = box
            color = (255, 0, 0)  # 紅色
            cv2.rectangle(
                overlap,
                (int(x_min), int(y_min)),
                (int(x_max), int(y_max)),
                color,
                2
            )
            
            # 繪製類別標籤
            if i < len(labels):
                label_id = labels[i]
                if class_names and label_id in class_names:
                    label_text = f"{class_names[label_id]}"
                else:
                    label_text = f"類別 {label_id}"
                
                # 使用PIL繪製中文
                overlap = draw_chinese_text(
                    overlap,
                    label_text,
                    (int(x_min), int(y_min) - 20),
                    font_size=14,
                    text_color=(255, 0, 0)
                )
        
        axes[2].imshow(overlap)
        axes[2].set_title('標籤疊加')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return fig


class LabelmeDataset(Dataset):
    def __init__(self, data_dir, size, split='train', transform=None, log_enabled=True, class_mapping=None):
        """
        初始化 labelme 格式的資料集
        
        參數:
            data_dir (str): 資料目錄路徑，包含 labelme json 檔案和圖像
            size (int): 輸出圖像大小
            split (str): 資料集分割 ('train', 'val', 'test')
            transform: albumentations 轉換
            log_enabled (bool): 是否啟用日誌
            class_mapping (dict): 類別名稱到數字 ID 的映射，例如 {'class1': 0, 'class2': 1}
        """
        self.size = size
        self.split = split
        self.data_dir = data_dir
        self.transform = transform
        self.log_enabled = log_enabled
        self.use_tensor = False  # 檢查是否使用 ToTensorV2
        
        # 檢查轉換中是否含有 ToTensorV2
        if transform is not None:
            for t in transform:
                if isinstance(t, ToTensorV2):
                    self.use_tensor = True
                    if self.log_enabled:
                        logger.info("檢測到 ToTensorV2 轉換")
                    break
        
        self.img_files = []
        self.json_files = []
        
        # 記錄開始讀取資料
        if self.log_enabled:
            logger.info(f"開始初始化 {split} 資料集")
        
        # 檢查資料目錄結構
        if not os.path.exists(data_dir):
            raise FileNotFoundError(f"找不到資料目錄: {data_dir}")
        
        # 載入或創建類別映射
        self.class_mapping = class_mapping
        if self.class_mapping is None:
            self.class_mapping = self._create_class_mapping()
            
        # 尋找所有 labelme json 檔案和對應的圖像檔案
        self._find_labelme_files()
        
        if self.log_enabled:
            logger.info(f"找到 {len(self.img_files)} 筆資料")
            logger.info(f"類別映射: {self.class_mapping}")
    
    def _create_class_mapping(self):
        """從所有 JSON 檔案自動創建類別映射"""
        class_names = set()
        
        # 遍歷所有可能的目錄尋找 json 檔案
        input_dir = self._get_split_directory()
        
        if not os.path.exists(input_dir):
            logger.warning(f"找不到目錄: {input_dir}")
            return {}
            
        for filename in os.listdir(input_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(input_dir, filename), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    for shape in data.get('shapes', []):
                        class_names.add(shape.get('label', ''))
                except Exception as e:
                    if self.log_enabled:
                        logger.error(f"讀取 JSON 檔案 {filename} 時發生錯誤: {e}")
                        
        # 移除空標籤
        if '' in class_names:
            class_names.remove('')
            
        # 按字母順序排序並分配數字 ID
        sorted_names = sorted(list(class_names))
        class_mapping = {name: idx for idx, name in enumerate(sorted_names)}
        
        if self.log_enabled:
            logger.info(f"自動生成類別映射: {class_mapping}")
            
        return class_mapping
    
    def _get_split_directory(self):
        """獲取對應分割的目錄路徑"""
        # 嘗試找到分割對應的目錄
        possible_dirs = [
            os.path.join(self.data_dir, self.split),
            os.path.join(self.data_dir, 'images', self.split),
            self.data_dir  # 如果沒有分割子目錄，則使用主目錄
        ]
        
        for dir_path in possible_dirs:
            if os.path.exists(dir_path):
                return dir_path
                
        # 如果都找不到，返回主目錄
        return self.data_dir
    
    def _find_labelme_files(self):
        """尋找所有 labelme json 檔案和對應的圖像檔案"""
        input_dir = self._get_split_directory()
        
        if self.log_enabled:
            logger.info(f"掃描目錄: {input_dir}")
            
        if not os.path.exists(input_dir):
            logger.warning(f"找不到目錄: {input_dir}")
            return
            
        for filename in os.listdir(input_dir):
            if filename.endswith('.json'):
                json_path = os.path.join(input_dir, filename)
                try:
                    # 讀取 json 檔案以獲取對應的圖像路徑
                    with open(json_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        
                    # 獲取圖像檔案路徑
                    img_file = None
                    img_path = data.get('imagePath', '')
                    
                    # 處理相對路徑或絕對路徑
                    if img_path:
                        if os.path.isabs(img_path):
                            # 絕對路徑
                            if os.path.exists(img_path):
                                img_file = img_path
                        else:
                            # 相對路徑
                            img_file = os.path.join(input_dir, os.path.basename(img_path))
                            if not os.path.exists(img_file):
                                # 嘗試在相同目錄下尋找
                                base_name = os.path.splitext(filename)[0]
                                for ext in ['.jpg', '.jpeg', '.png']:
                                    test_path = os.path.join(input_dir, f"{base_name}{ext}")
                                    if os.path.exists(test_path):
                                        img_file = test_path
                                        break
                    else:
                        # 如果 json 未指定圖像路徑，嘗試尋找同名圖像檔案
                        base_name = os.path.splitext(filename)[0]
                        for ext in ['.jpg', '.jpeg', '.png']:
                            test_path = os.path.join(input_dir, f"{base_name}{ext}")
                            if os.path.exists(test_path):
                                img_file = test_path
                                break
                                
                    if img_file and os.path.exists(img_file):
                        self.json_files.append(json_path)
                        self.img_files.append(img_file)
                    else:
                        if self.log_enabled:
                            logger.warning(f"找不到對應的圖像檔案: {filename}")
                            
                except Exception as e:
                    if self.log_enabled:
                        logger.error(f"處理 JSON 檔案 {filename} 時發生錯誤: {e}")
    
    def __len__(self):
        """返回資料集大小"""
        return len(self.img_files)
        
    def __getitem__(self, idx):
        """獲取資料項"""
        if self.log_enabled:
            logger.info(f"讀取第 {idx} 筆資料")
            
        # 獲取檔案路徑
        img_file = self.img_files[idx]
        json_file = self.json_files[idx]
        
        # 獲取檔名（不含路徑和副檔名）
        filename = os.path.basename(img_file)
        
        # 讀取圖片
        image = np.array(Image.open(img_file).convert('RGB'))
        original_height, original_width = image.shape[:2]
        
        if self.log_enabled:
            logger.info(f"圖片尺寸: {original_width}x{original_height}")
        
        # 讀取 labelme JSON 檔案
        mask, polygons, boxes, labels = self._read_labelme_json(json_file, original_width, original_height)
        
        # 保存原始圖片和掩碼用於視覺化
        self.last_processed = {
            'image': image.copy(),
            'mask': mask.copy(),
            'original_image': image.copy(),
            'polygons': polygons.copy() if isinstance(polygons, np.ndarray) else polygons.copy() if polygons else [],
            'boxes': boxes.copy() if isinstance(boxes, np.ndarray) else boxes.copy() if boxes else [],
            'labels': labels.copy() if isinstance(labels, np.ndarray) else labels.copy() if labels else [],
            'filename': filename  # 保存檔名
        }
        
        # 應用轉換
        if self.transform:
            if self.log_enabled:
                logger.info("應用資料增強")
            
            # 如果有邊界框，使用 bbox_params
            if boxes and len(boxes) > 0:
                transformed = self.transform(
                    image=image,
                    mask=mask,
                    bboxes=boxes,
                    category_ids=labels
                )
                image = transformed["image"]
                mask = transformed["mask"]
                boxes = transformed.get("bboxes", [])
                labels = transformed.get("category_ids", [])
            else:
                transformed = self.transform(image=image, mask=mask)
                image = transformed["image"]
                mask = transformed["mask"]
            
            # 保存處理後的圖像和掩碼 (若是張量則先轉回 NumPy 以供視覺化)
            if self.use_tensor:
                if self.log_enabled:
                    logger.info("檢測到張量輸出，為視覺化轉換回 NumPy 格式")
                # 保存轉換後但尚未轉為張量的版本 (用於視覺化)
                if isinstance(image, torch.Tensor):
                    # 如果是張量，將其轉為 NumPy
                    self.last_processed['processed_image'] = image.permute(1, 2, 0).cpu().numpy()
                    
                if isinstance(mask, torch.Tensor):
                    self.last_processed['processed_mask'] = mask.cpu().numpy()
            else:
                self.last_processed['processed_image'] = image
                self.last_processed['processed_mask'] = mask
            
            self.last_processed['processed_boxes'] = boxes
            self.last_processed['processed_labels'] = labels
        else:
            # 如果沒有轉換，調整大小
            resized_image = cv2.resize(image, (self.size, self.size))
            resized_mask = cv2.resize(mask, (self.size, self.size), interpolation=cv2.INTER_NEAREST)
            
            # 調整邊界框
            resized_boxes = []
            if boxes and len(boxes) > 0:
                for box in boxes:
                    # 將邊界框從 [x_min, y_min, x_max, y_max] 轉換為相對座標
                    x_min, y_min, x_max, y_max = box
                    x_scale = self.size / original_width
                    y_scale = self.size / original_height
                    
                    x_min_new = x_min * x_scale
                    y_min_new = y_min * y_scale
                    x_max_new = x_max * x_scale
                    y_max_new = y_max * y_scale
                    
                    resized_boxes.append([x_min_new, y_min_new, x_max_new, y_max_new])
            
            # 將圖片轉換為 PyTorch tensor
            image_tensor = torch.from_numpy(resized_image.transpose(2, 0, 1)).float() / 255.0
            mask_tensor = torch.from_numpy(resized_mask).long()
            
            self.last_processed['processed_image'] = resized_image
            self.last_processed['processed_mask'] = resized_mask
            self.last_processed['processed_boxes'] = resized_boxes
            self.last_processed['processed_labels'] = labels
            
            image = image_tensor
            mask = mask_tensor
        
        if self.log_enabled:
            if isinstance(image, torch.Tensor):
                logger.info(f"處理完成，返回 shape: image={image.shape}, mask={mask.shape}")
            else:
                logger.info(f"處理完成，返回 shape: image={image.shape}, mask={mask.shape}")
        
        # 返回圖像、掩碼和檔名
        return image, mask, filename
    
    def _read_labelme_json(self, json_file, img_width, img_height):
        """直接讀取 labelme JSON 檔案並生成掩碼"""
        # 初始化掩碼、多邊形、邊界框和標籤
        mask = np.zeros((img_height, img_width), dtype=np.uint8)
        polygons = []
        boxes = []
        labels = []
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            shapes = data.get('shapes', [])
            for shape in shapes:
                label = shape.get('label', '')
                shape_type = shape.get('shape_type', '')
                points = shape.get('points', [])
                
                # 跳過無效標籤
                if not label or label not in self.class_mapping:
                    if self.log_enabled:
                        logger.warning(f"跳過未知標籤 '{label}' 在檔案 {json_file}")
                    continue
                    
                class_id = self.class_mapping[label]
                
                # 處理矩形
                if shape_type == 'rectangle' and len(points) == 2:
                    # labelme 矩形格式：[[x1, y1], [x2, y2]]
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    
                    # 確保坐標順序正確（左上到右下）
                    xmin = min(x1, x2)
                    ymin = min(y1, y2)
                    xmax = max(x1, x2)
                    ymax = max(y1, y2)
                    
                    # 添加到邊界框列表
                    boxes.append([xmin, ymin, xmax, ymax])
                    labels.append(class_id)
                    
                    # 創建矩形點列表（用於繪製多邊形）
                    rect_points = np.array([
                        [xmin, ymin],
                        [xmax, ymin],
                        [xmax, ymax],
                        [xmin, ymax]
                    ], dtype=np.int32)
                    
                    polygons.append(rect_points)
                    
                    # 在掩碼上繪製框
                    cv2.rectangle(
                        mask,
                        (int(xmin), int(ymin)),
                        (int(xmax), int(ymax)),
                        class_id + 1,  # 加 1，因為 0 通常保留給背景
                        -1  # 填充矩形
                    )
                    
                # 處理多邊形
                elif shape_type == 'polygon' and len(points) >= 3:
                    # 將坐標轉換為整數點陣列
                    poly_points = np.array(points, dtype=np.int32)
                    polygons.append(poly_points)
                    
                    # 在掩碼上繪製多邊形
                    cv2.fillPoly(mask, [poly_points], class_id + 1)
                    
                    # 計算多邊形的邊界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min = max(0, min(x_coords))
                    y_min = max(0, min(y_coords))
                    x_max = min(img_width, max(x_coords))
                    y_max = min(img_height, max(y_coords))
                    
                    # 添加到邊界框列表
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                    
                # 處理點
                elif shape_type == 'point' and len(points) == 1:
                    x, y = points[0]
                    
                    # 繪製一個小圓形或點
                    radius = max(3, min(img_width, img_height) // 100)  # 根據圖像大小調整半徑
                    cv2.circle(mask, (int(x), int(y)), radius, class_id + 1, -1)
                    
                    # 以點為中心創建一個小方框
                    point_size = radius * 2
                    x_min = max(0, x - point_size)
                    y_min = max(0, y - point_size)
                    x_max = min(img_width, x + point_size)
                    y_max = min(img_height, y + point_size)
                    
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                    
                    # 建立一個圓形多邊形點列表（用於視覺化）
                    circle_points = []
                    for i in range(20):  # 20點近似圓形
                        angle = 2 * np.pi * i / 20
                        px = x + radius * np.cos(angle)
                        py = y + radius * np.sin(angle)
                        circle_points.append([px, py])
                    
                    polygons.append(np.array(circle_points, dtype=np.int32))
                    
                # 處理線條
                elif shape_type == 'line' and len(points) >= 2:
                    # 將線條轉換為帶寬度的多邊形
                    line_width = max(2, min(img_width, img_height) // 200)  # 根據圖像大小調整線寬
                    line_points = np.array(points, dtype=np.int32)
                    
                    # 繪製線條（用線寬繪製）
                    for i in range(len(line_points) - 1):
                        pt1 = tuple(line_points[i])
                        pt2 = tuple(line_points[i + 1])
                        cv2.line(mask, pt1, pt2, class_id + 1, line_width)
                    
                    # 計算線條的邊界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    x_min = max(0, min(x_coords) - line_width)
                    y_min = max(0, min(y_coords) - line_width)
                    x_max = min(img_width, max(x_coords) + line_width)
                    y_max = min(img_height, max(y_coords) + line_width)
                    
                    boxes.append([x_min, y_min, x_max, y_max])
                    labels.append(class_id)
                    polygons.append(line_points)
                    
                elif self.log_enabled:
                    logger.warning(f"未支援的形狀類型 '{shape_type}' 在檔案 {json_file}")
                    
        except Exception as e:
            if self.log_enabled:
                logger.error(f"讀取 JSON 檔案 {json_file} 錯誤: {e}")
        
        return mask, np.array(polygons, dtype=object), boxes, labels
    
    def visualize(self, idx, figsize=(15, 5), class_names=None):
        """
        視覺化資料：原始圖片、標籤掩碼和疊加效果
        
        參數:
            idx (int): 資料索引
            figsize (tuple): 圖形大小
            class_names (dict): 類別名稱字典，例如 {0: '背景', 1: '裂縫', 2: '坑洞'}
        """
        # 如果沒有提供類別名稱，使用數字 ID
        if class_names is None and hasattr(self, 'class_mapping'):
            # 反轉類別映射: 數字ID -> 名稱
            class_names = {v: k for k, v in self.class_mapping.items()}
            
        # 檢查是否有處理過的資料
        if not hasattr(self, 'last_processed') or idx != getattr(self, 'last_idx', None):
            # 如果沒有，則處理資料
            self[idx]
            self.last_idx = idx
            
        # 獲取圖片和掩碼
        original_image = self.last_processed['original_image']
        processed_image = self.last_processed.get('processed_image', self.last_processed['image'])
        mask = self.last_processed.get('processed_mask', self.last_processed['mask'])
        boxes = self.last_processed.get('processed_boxes', self.last_processed.get('boxes', []))
        labels = self.last_processed.get('processed_labels', self.last_processed.get('labels', []))
        filename = self.last_processed.get('filename', 'unknown')
        
        # 確保圖像和掩碼為 NumPy 陣列
        if isinstance(processed_image, torch.Tensor):
            processed_image = processed_image.permute(1, 2, 0).cpu().numpy()
        
        if isinstance(mask, torch.Tensor):
            mask = mask.cpu().numpy()
        
        processed_image = denormalize(processed_image)
        
        # 創建圖形
        fig, axes = plt.subplots(1, 3, figsize=figsize)
        
        # 顯示原圖
        axes[0].imshow(original_image)
        axes[0].set_title(f'原始圖片: {filename}')
        axes[0].axis('off')
        
        # 產生顏色映射，確保每個類別顏色固定
        np.random.seed(42)  # 保持顏色一致
        
        # 找出所有存在的類別
        unique_classes = np.unique(mask)
        unique_classes = [c for c in unique_classes if c > 0]  # 排除背景類別
        
        # 為每個類別分配顏色
        class_colors = {}
        for cls in unique_classes:
            class_colors[cls] = np.random.randint(0, 255, 3).tolist()
        
        # 函數：使用PIL繪製中文文字
        def draw_chinese_text(img, text, position, font_size=20, text_color=(0, 0, 0)):
            # 將OpenCV圖像轉換為PIL圖像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)
            
            # 嘗試載入系統字體
            try:
                # 嘗試多種字體，以確保至少有一個可用
                font_paths = [
                    '/System/Library/Fonts/PingFang.ttc',         # macOS
                    '/usr/share/fonts/truetype/arphic/uming.ttc', # Ubuntu
                    'C:/Windows/Fonts/msyh.ttc',                  # Windows
                    'C:/Windows/Fonts/mingliu.ttc',               # Windows
                    'C:/Windows/Fonts/simsun.ttc',                # Windows
                    'C:/Windows/Fonts/kaiu.ttf',                  # Windows
                    '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'  # Linux
                ]
                
                font = None
                for path in font_paths:
                    try:
                        font = ImageFont.truetype(path, font_size)
                        break
                    except IOError:
                        continue
                
                # 如果沒有找到支援中文的字體，使用默認字體
                if font is None:
                    font = ImageFont.load_default()
                    
            except Exception as e:
                # 如果載入字體失敗，使用默認字體
                print(f"載入字體失敗: {e}")
                font = ImageFont.load_default()
            
            # 繪製文字
            draw.text(position, text, font=font, fill=text_color)
            
            # 將PIL圖像轉回OpenCV格式
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # 顯示標籤掩碼
        if unique_classes:
            # 創建彩色掩碼圖
            colored_mask = np.zeros_like(processed_image)
            for cls in unique_classes:
                mask_class = (mask == cls)
                for c in range(3):
                    colored_mask[:, :, c][mask_class] = class_colors[cls][c]
                    
            axes[1].imshow(colored_mask)
            axes[1].set_title('標籤分類')
            axes[1].axis('off')
        else:
            # 沒有類別時顯示空白掩碼
            axes[1].imshow(np.zeros_like(processed_image))
            axes[1].set_title('無標籤')
            axes[1].axis('off')
        
        # 創建疊加圖像 - 標籤疊加在原圖上
        overlap = processed_image.copy()
        colored_mask = np.zeros_like(overlap)
        
        # 為每個類別著色
        for cls in unique_classes:
            colored_mask[mask == cls] = class_colors[cls]
            
        # 疊加掩碼
        alpha = 0.5
        overlap = cv2.addWeighted(overlap, 1, colored_mask, alpha, 0)
        
        # 添加圖例
        if unique_classes:
            # 繪製半透明的圖例背景
            legend_x = overlap.shape[1] - 150
            legend_y = 20
            legend_height = 25 * len(unique_classes)
            
            # 半透明白色背景
            cv2.rectangle(
                overlap,
                (legend_x - 10, legend_y - 10),
                (legend_x + 140, legend_y + legend_height),
                (255, 255, 255),
                -1
            )
            # 黑色邊框
            cv2.rectangle(
                overlap,
                (legend_x - 10, legend_y - 10),
                (legend_x + 140, legend_y + legend_height),
                (0, 0, 0),
                1
            )
            
            # 繪製每個類別的顏色方塊和名稱
            for i, cls in enumerate(sorted(unique_classes)):
                # 顏色方塊
                cv2.rectangle(
                    overlap,
                    (legend_x, legend_y + i * 25),
                    (legend_x + 20, legend_y + i * 25 + 20),
                    class_colors[cls],
                    -1
                )
                
                # 類別名稱/編號
                if class_names and cls in class_names:
                    class_text = f"{class_names[cls]}"
                else:
                    class_text = f"類別 {cls}"
                    
                # 使用PIL繪製中文
                overlap = draw_chinese_text(
                    overlap, 
                    class_text, 
                    (legend_x + 30, legend_y + i * 25), 
                    font_size=16
                )
        
        # 繪製邊界框
        for i, box in enumerate(boxes):
            if len(box) != 4:
                continue
            
            x_min, y_min, x_max, y_max = box
            color = (255, 0, 0)  # 紅色
            cv2.rectangle(
                overlap,
                (int(x_min), int(y_min)),
                (int(x_max), int(y_max)),
                color,
                2
            )
            
            # 繪製類別標籤
            if i < len(labels):
                label_id = labels[i]
                if class_names and label_id in class_names:
                    label_text = f"{class_names[label_id]}"
                else:
                    label_text = f"類別 {label_id}"
                
                # 使用PIL繪製中文
                overlap = draw_chinese_text(
                    overlap,
                    label_text,
                    (int(x_min), int(y_min) - 20),
                    font_size=14,
                    text_color=(255, 0, 0)
                )
        
        axes[2].imshow(overlap)
        axes[2].set_title('標籤疊加')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return fig



# 範例使用
if __name__ == "__main__":
    # 定義轉換
    transform = A.Compose([
        A.Resize(height=224, width=224),
        A.HorizontalFlip(p=0.5),
        A.RandomBrightnessContrast(p=0.2),
        ToTensorV2(),  # 轉換為 PyTorch 張量
    ], bbox_params=A.BboxParams(format='pascal_voc', label_fields=['category_ids']))
    
    # 創建資料集 (使用 YOLODataset)
    dataset = YOLODataset(
        data_dir='./AI_train_dataset',
        size=224,
        split='train',
        transform=transform
    )

    # 創建資料集 (使用 LabelmeDataset)
    # dataset = LabelmeDataset(
    #     data_dir='./AI_train_dataset',
    #     size=224,
    #     split='train',
    #     transform=transform
    # )
    
    
    # 視覺化示例
    print(f"資料集大小: {len(dataset)}")
    
    # 獲取並視覺化一個資料項
    if len(dataset) > 0:
        idx = 0
        image, mask = dataset[idx]
        print(f"圖片類型: {type(image)}")
        if isinstance(image, torch.Tensor):
            print(f"圖片 shape: {image.shape}")
        else:
            print(f"圖片 shape: {image.shape}")
        
        # 視覺化
        dataset.visualize(idx, show_n=3)