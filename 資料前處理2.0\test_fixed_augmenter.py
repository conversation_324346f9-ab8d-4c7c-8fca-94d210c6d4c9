#!/usr/bin/env python3
"""
測試修復後的全景圖像擴增工具
"""

import cv2
import numpy as np
import os
from panorama_augmenter import PanoramaAugmenter, setup_logging


def create_test_panorama():
    """創建測試用的全景圖像"""
    width, height = 3600, 1800  # 2:1比例
    
    # 創建漸變背景
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 創建水平漸變（天空到地面）
    for y in range(height):
        intensity = int(255 * (1 - y / height))
        image[y, :] = [intensity//3, intensity//2, intensity]  # 藍色漸變
    
    # 添加地平線
    cv2.line(image, (0, height//2), (width, height//2), (255, 255, 255), 5)
    
    # 添加一些建築物剪影
    buildings = [
        (200, height//2, 400, height-100),
        (800, height//2, 1200, height-200),
        (1500, height//2, 1800, height-150),
        (2200, height//2, 2600, height-180),
        (3000, height//2, 3400, height-120),
    ]
    
    for x1, y1, x2, y2 in buildings:
        cv2.rectangle(image, (x1, y1), (x2, y2), (50, 50, 50), -1)
        # 添加一些窗戶
        for wx in range(x1+20, x2-20, 40):
            for wy in range(y1+20, y2-20, 60):
                cv2.rectangle(image, (wx, wy), (wx+15, wy+15), (200, 200, 100), -1)
    
    # 添加太陽
    cv2.circle(image, (width//4, height//4), 80, (255, 255, 200), -1)
    
    # 添加指北針標記（紅色箭頭指向北）
    cv2.arrowedLine(image, (100, 100), (100, 50), (0, 0, 255), 5, tipLength=0.3)
    cv2.putText(image, 'N', (90, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    
    return image


def test_orientation_validation():
    """測試外方位參數驗證"""
    print("=== 測試外方位參數驗證 ===")
    
    augmenter = PanoramaAugmenter()
    
    # 測試正常角度值
    omega, phi, kappa = augmenter.validate_orientation(45.0, -30.0, 120.0)
    print(f"正常角度: 45.0, -30.0, 120.0 -> {omega}, {phi}, {kappa}")
    
    # 測試大數值（座標類型）
    omega, phi, kappa = augmenter.validate_orientation(310322.42, 2772117.47, 11.16)
    print(f"大數值: 310322.42, 2772117.47, 11.16 -> {omega}, {phi}, {kappa}")
    
    # 測試角度標準化
    omega, phi, kappa = augmenter.validate_orientation(450.0, -200.0, 380.0)
    print(f"超範圍角度: 450.0, -200.0, 380.0 -> {omega}, {phi}, {kappa}")
    
    print("✅ 外方位參數驗證測試完成\n")


def test_no_label_processing():
    """測試沒有標籤時的處理"""
    print("=== 測試沒有標籤的處理 ===")
    
    # 創建測試圖像
    image = create_test_panorama()
    cv2.imwrite("test_panorama.jpg", image)
    
    # 設置日誌
    logger = setup_logging("test_no_label.log")
    augmenter = PanoramaAugmenter(logger)
    
    # 測試處理（沒有標籤文件）
    try:
        results = augmenter.process_single_image(
            image_path="test_panorama.jpg",
            label_path=None,  # 沒有標籤
            orientation=(10.0, -5.0, 30.0),  # 合理的外方位
            methods=['orientation'],
            output_dir="test_output_no_label"
        )
        
        print(f"✅ 成功處理沒有標籤的圖像")
        for method, aug_results in results.items():
            print(f"  {method}: 生成 {len(aug_results)} 個變化")
            
    except Exception as e:
        print(f"❌ 處理失敗: {e}")
    
    print()


def test_orientation_based_augmentation():
    """測試基於外方位的擴增"""
    print("=== 測試基於外方位的擴增 ===")
    
    # 創建測試圖像
    image = create_test_panorama()
    cv2.imwrite("test_panorama_orient.jpg", image)
    
    # 設置日誌
    logger = setup_logging("test_orientation.log")
    augmenter = PanoramaAugmenter(logger)
    
    # 測試不同的外方位設置
    test_orientations = [
        (0.0, 0.0, 0.0, "零度參考"),
        (15.0, -10.0, 45.0, "輕微傾斜"),
        (-30.0, 20.0, -90.0, "明顯傾斜"),
    ]
    
    for omega, phi, kappa, description in test_orientations:
        print(f"測試 {description}: omega={omega}, phi={phi}, kappa={kappa}")
        
        try:
            results = augmenter.process_single_image(
                image_path="test_panorama_orient.jpg",
                label_path=None,
                orientation=(omega, phi, kappa),
                methods=['orientation'],
                output_dir=f"test_output_{description.replace(' ', '_')}"
            )
            
            for method, aug_results in results.items():
                print(f"  ✅ {method}: 生成 {len(aug_results)} 個變化")
                
        except Exception as e:
            print(f"  ❌ 失敗: {e}")
    
    print()


def test_command_line_equivalents():
    """顯示命令行使用示例"""
    print("=== 命令行使用示例 ===")
    
    print("1. 基於外方位的擴增（推薦）:")
    print("python panorama_augmenter.py \\")
    print("    --input test_panorama.jpg \\")
    print("    --output output_dir \\")
    print("    --omega 15.0 --phi -10.0 --kappa 45.0 \\")
    print("    --methods orientation")
    
    print("\n2. 多種擴增方法組合:")
    print("python panorama_augmenter.py \\")
    print("    --input test_panorama.jpg \\")
    print("    --output output_dir \\")
    print("    --omega 15.0 --phi -10.0 --kappa 45.0 \\")
    print("    --methods orientation perspective tilt")
    
    print("\n3. 交互式模式:")
    print("python panorama_augmenter.py \\")
    print("    --output output_dir \\")
    print("    --interactive")
    
    print()


def main():
    """主測試函數"""
    print("🔧 測試修復後的全景圖像擴增工具")
    print("=" * 50)
    
    # 清理舊的測試文件
    test_files = [
        "test_panorama.jpg", "test_panorama_orient.jpg"
    ]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
    
    try:
        # 運行測試
        test_orientation_validation()
        test_no_label_processing()
        test_orientation_based_augmentation()
        test_command_line_equivalents()
        
        print("🎉 所有測試完成！")
        print("\n修復內容:")
        print("✅ 外方位參數驗證和標準化")
        print("✅ 沒有標籤文件時的正確處理")
        print("✅ 新的 'orientation' 擴增方法（推薦使用）")
        print("✅ 改善的默認參數選擇")
        print("✅ 更好的交互式提示")
        
        print("\n使用建議:")
        print("• 使用 'orientation' 方法進行基於外方位的擴增")
        print("• 確保外方位參數是角度值（-180到180度）")
        print("• 即使沒有標籤文件也可以進行圖像擴增")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        raise


if __name__ == "__main__":
    main()