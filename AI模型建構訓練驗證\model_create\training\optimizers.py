"""
優化器和調度器工廠

提供統一的優化器和學習率調度器創建接口
"""

import torch
import torch.optim as optim
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class OptimizerConfig:
    """優化器配置"""
    name: str = "adam"
    lr: float = 1e-3
    weight_decay: float = 1e-4
    momentum: float = 0.9
    beta1: float = 0.9
    beta2: float = 0.999
    eps: float = 1e-8
    
    # AdamW特定參數
    amsgrad: bool = False
    
    # SGD特定參數
    nesterov: bool = False
    dampening: float = 0.0
    
    # RMSprop特定參數
    alpha: float = 0.99
    centered: bool = False


@dataclass 
class SchedulerConfig:
    """調度器配置"""
    name: str = "cosine"
    
    # StepLR參數
    step_size: int = 30
    gamma: float = 0.1
    
    # MultiStepLR參數
    milestones: List[int] = None
    
    # ExponentialLR參數
    # gamma已在上面定義
    
    # CosineAnnealingLR參數
    T_max: int = 100
    eta_min: float = 0
    
    # ReduceLROnPlateau參數
    mode: str = 'min'
    factor: float = 0.1
    patience: int = 10
    threshold: float = 1e-4
    threshold_mode: str = 'rel'
    cooldown: int = 0
    min_lr: float = 0
    
    # CosineAnnealingWarmRestarts參數
    T_0: int = 10
    T_mult: int = 1
    
    # OneCycleLR參數
    max_lr: float = 1e-2
    total_steps: Optional[int] = None
    epochs: Optional[int] = None
    steps_per_epoch: Optional[int] = None
    pct_start: float = 0.3
    anneal_strategy: str = 'cos'
    cycle_momentum: bool = True
    base_momentum: float = 0.85
    max_momentum: float = 0.95
    div_factor: float = 25.0
    final_div_factor: float = 1e4
    three_phase: bool = False


class OptimizerFactory:
    """優化器工廠"""
    
    @staticmethod
    def create_optimizer(model: torch.nn.Module, 
                        config: OptimizerConfig) -> torch.optim.Optimizer:
        """
        創建優化器
        
        Args:
            model: 要優化的模型
            config: 優化器配置
            
        Returns:
            torch.optim.Optimizer: 優化器實例
        """
        params = model.parameters()
        name = config.name.lower()
        
        if name == "adam":
            return optim.Adam(
                params,
                lr=config.lr,
                betas=(config.beta1, config.beta2),
                eps=config.eps,
                weight_decay=config.weight_decay,
                amsgrad=config.amsgrad
            )
        
        elif name == "adamw":
            return optim.AdamW(
                params,
                lr=config.lr,
                betas=(config.beta1, config.beta2),
                eps=config.eps,
                weight_decay=config.weight_decay,
                amsgrad=config.amsgrad
            )
        
        elif name == "sgd":
            return optim.SGD(
                params,
                lr=config.lr,
                momentum=config.momentum,
                weight_decay=config.weight_decay,
                dampening=config.dampening,
                nesterov=config.nesterov
            )
        
        elif name == "rmsprop":
            return optim.RMSprop(
                params,
                lr=config.lr,
                alpha=config.alpha,
                eps=config.eps,
                weight_decay=config.weight_decay,
                momentum=config.momentum,
                centered=config.centered
            )
        
        elif name == "adagrad":
            return optim.Adagrad(
                params,
                lr=config.lr,
                lr_decay=0,
                weight_decay=config.weight_decay,
                eps=config.eps
            )
        
        elif name == "adadelta":
            return optim.Adadelta(
                params,
                lr=config.lr,
                rho=0.9,
                eps=config.eps,
                weight_decay=config.weight_decay
            )
        
        else:
            raise ValueError(f"不支援的優化器: {name}")
    
    @staticmethod
    def create_optimizer_from_dict(model: torch.nn.Module, 
                                  config_dict: Dict[str, Any]) -> torch.optim.Optimizer:
        """
        從字典創建優化器
        
        Args:
            model: 要優化的模型
            config_dict: 配置字典
            
        Returns:
            torch.optim.Optimizer: 優化器實例
        """
        config = OptimizerConfig(**config_dict)
        return OptimizerFactory.create_optimizer(model, config)
    
    @staticmethod
    def get_default_config(optimizer_name: str) -> OptimizerConfig:
        """
        獲取默認配置
        
        Args:
            optimizer_name: 優化器名稱
            
        Returns:
            OptimizerConfig: 默認配置
        """
        defaults = {
            "adam": OptimizerConfig(name="adam", lr=1e-3, weight_decay=1e-4),
            "adamw": OptimizerConfig(name="adamw", lr=1e-3, weight_decay=1e-2),
            "sgd": OptimizerConfig(name="sgd", lr=1e-2, momentum=0.9, weight_decay=1e-4),
            "rmsprop": OptimizerConfig(name="rmsprop", lr=1e-3, weight_decay=1e-4),
        }
        
        return defaults.get(optimizer_name.lower(), OptimizerConfig())


class SchedulerFactory:
    """學習率調度器工廠"""
    
    @staticmethod
    def create_scheduler(optimizer: torch.optim.Optimizer,
                        config: SchedulerConfig) -> torch.optim.lr_scheduler._LRScheduler:
        """
        創建學習率調度器
        
        Args:
            optimizer: 優化器
            config: 調度器配置
            
        Returns:
            torch.optim.lr_scheduler._LRScheduler: 調度器實例
        """
        name = config.name.lower()
        
        if name == "step" or name == "steplr":
            return optim.lr_scheduler.StepLR(
                optimizer,
                step_size=config.step_size,
                gamma=config.gamma
            )
        
        elif name == "multistep" or name == "multisteplr":
            milestones = config.milestones or [30, 60, 90]
            return optim.lr_scheduler.MultiStepLR(
                optimizer,
                milestones=milestones,
                gamma=config.gamma
            )
        
        elif name == "exponential" or name == "exponentiallr":
            return optim.lr_scheduler.ExponentialLR(
                optimizer,
                gamma=config.gamma
            )
        
        elif name == "cosine" or name == "cosineannealinglr":
            return optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=config.T_max,
                eta_min=config.eta_min
            )
        
        elif name == "plateau" or name == "reducelronplateau":
            return optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode=config.mode,
                factor=config.factor,
                patience=config.patience,
                threshold=config.threshold,
                threshold_mode=config.threshold_mode,
                cooldown=config.cooldown,
                min_lr=config.min_lr,
                eps=1e-8
            )
        
        elif name == "cosine_restarts" or name == "cosineannealingwarmrestarts":
            return optim.lr_scheduler.CosineAnnealingWarmRestarts(
                optimizer,
                T_0=config.T_0,
                T_mult=config.T_mult,
                eta_min=config.eta_min
            )
        
        elif name == "onecycle" or name == "onecyclelr":
            total_steps = config.total_steps
            if total_steps is None and config.epochs and config.steps_per_epoch:
                total_steps = config.epochs * config.steps_per_epoch
            
            if total_steps is None:
                raise ValueError("OneCycleLR需要指定total_steps或(epochs和steps_per_epoch)")
            
            return optim.lr_scheduler.OneCycleLR(
                optimizer,
                max_lr=config.max_lr,
                total_steps=total_steps,
                pct_start=config.pct_start,
                anneal_strategy=config.anneal_strategy,
                cycle_momentum=config.cycle_momentum,
                base_momentum=config.base_momentum,
                max_momentum=config.max_momentum,
                div_factor=config.div_factor,
                final_div_factor=config.final_div_factor,
                three_phase=config.three_phase
            )
        
        elif name == "linear" or name == "linearlr":
            return optim.lr_scheduler.LinearLR(
                optimizer,
                start_factor=1.0,
                end_factor=0.1,
                total_iters=config.T_max
            )
        
        elif name == "none" or name is None:
            # 返回一個不做任何事的調度器
            return optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=lambda epoch: 1.0)
        
        else:
            raise ValueError(f"不支援的調度器: {name}")
    
    @staticmethod
    def create_scheduler_from_dict(optimizer: torch.optim.Optimizer,
                                  config_dict: Dict[str, Any]) -> torch.optim.lr_scheduler._LRScheduler:
        """
        從字典創建調度器
        
        Args:
            optimizer: 優化器
            config_dict: 配置字典
            
        Returns:
            torch.optim.lr_scheduler._LRScheduler: 調度器實例
        """
        config = SchedulerConfig(**config_dict)
        return SchedulerFactory.create_scheduler(optimizer, config)
    
    @staticmethod
    def get_default_config(scheduler_name: str, 
                          epochs: int = 100,
                          steps_per_epoch: Optional[int] = None) -> SchedulerConfig:
        """
        獲取默認配置
        
        Args:
            scheduler_name: 調度器名稱
            epochs: 訓練總epoch數
            steps_per_epoch: 每個epoch的步數
            
        Returns:
            SchedulerConfig: 默認配置
        """
        defaults = {
            "step": SchedulerConfig(
                name="step", 
                step_size=max(epochs // 3, 1), 
                gamma=0.1
            ),
            "multistep": SchedulerConfig(
                name="multistep", 
                milestones=[epochs // 3, epochs * 2 // 3], 
                gamma=0.1
            ),
            "cosine": SchedulerConfig(
                name="cosine", 
                T_max=epochs, 
                eta_min=1e-6
            ),
            "plateau": SchedulerConfig(
                name="plateau", 
                patience=epochs // 10, 
                factor=0.5
            ),
            "onecycle": SchedulerConfig(
                name="onecycle",
                max_lr=1e-2,
                epochs=epochs,
                steps_per_epoch=steps_per_epoch,
                pct_start=0.3
            )
        }
        
        return defaults.get(scheduler_name.lower(), SchedulerConfig())


class OptimizerSchedulerBuilder:
    """優化器和調度器建構器"""
    
    def __init__(self):
        self.optimizer_config = OptimizerConfig()
        self.scheduler_config = SchedulerConfig()
    
    def set_optimizer(self, name: str, **kwargs) -> 'OptimizerSchedulerBuilder':
        """設置優化器"""
        self.optimizer_config.name = name
        for key, value in kwargs.items():
            if hasattr(self.optimizer_config, key):
                setattr(self.optimizer_config, key, value)
        return self
    
    def set_scheduler(self, name: str, **kwargs) -> 'OptimizerSchedulerBuilder':
        """設置調度器"""
        self.scheduler_config.name = name
        for key, value in kwargs.items():
            if hasattr(self.scheduler_config, key):
                setattr(self.scheduler_config, key, value)
        return self
    
    def build(self, model: torch.nn.Module) -> tuple:
        """
        建構優化器和調度器
        
        Args:
            model: 模型
            
        Returns:
            tuple: (optimizer, scheduler)
        """
        optimizer = OptimizerFactory.create_optimizer(model, self.optimizer_config)
        scheduler = SchedulerFactory.create_scheduler(optimizer, self.scheduler_config)
        return optimizer, scheduler
    
    @classmethod
    def from_configs(cls, 
                    optimizer_config: Dict[str, Any],
                    scheduler_config: Dict[str, Any]) -> 'OptimizerSchedulerBuilder':
        """
        從配置字典創建建構器
        
        Args:
            optimizer_config: 優化器配置
            scheduler_config: 調度器配置
            
        Returns:
            OptimizerSchedulerBuilder: 建構器實例
        """
        builder = cls()
        builder.optimizer_config = OptimizerConfig(**optimizer_config)
        builder.scheduler_config = SchedulerConfig(**scheduler_config)
        return builder
    
    @classmethod
    def get_common_setups(cls) -> Dict[str, 'OptimizerSchedulerBuilder']:
        """
        獲取常用的優化器+調度器組合
        
        Returns:
            Dict[str, OptimizerSchedulerBuilder]: 預設組合
        """
        setups = {}
        
        # Adam + CosineAnnealing (常用於Vision Transformer)
        setups['adam_cosine'] = (cls()
                                 .set_optimizer('adam', lr=1e-3, weight_decay=1e-4)
                                 .set_scheduler('cosine', T_max=100, eta_min=1e-6))
        
        # AdamW + OneCycle (常用於現代訓練)
        setups['adamw_onecycle'] = (cls()
                                   .set_optimizer('adamw', lr=1e-3, weight_decay=1e-2)
                                   .set_scheduler('onecycle', max_lr=1e-2))
        
        # SGD + MultiStep (經典組合)
        setups['sgd_multistep'] = (cls()
                                  .set_optimizer('sgd', lr=1e-1, momentum=0.9, weight_decay=1e-4)
                                  .set_scheduler('multistep', milestones=[30, 60, 90], gamma=0.1))
        
        # Adam + Plateau (自適應調整)
        setups['adam_plateau'] = (cls()
                                 .set_optimizer('adam', lr=1e-3, weight_decay=1e-4)
                                 .set_scheduler('plateau', patience=10, factor=0.5))
        
        return setups