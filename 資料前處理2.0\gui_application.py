#!/usr/bin/env python3
"""
圖像數據集處理工具集 GUI 應用程式
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import threading
import queue
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import logging

# 導入各個工具模組
try:
    from annotation_converter import AnnotationConverter
    from annotation_editor import AnnotationEditor
    from dataset_divider import DatasetDivider
    from img_augmenter import ImageAugmenter
    from panorama_augmenter import PanoramaAugmenter, setup_logging
except ImportError as e:
    print(f"警告：無法導入某些模組: {e}")


class LogHandler(logging.Handler):
    """自定義日誌處理器，將日誌輸出到GUI"""

    def __init__(self, log_widget):
        super().__init__()
        self.log_widget = log_widget

    def emit(self, record):
        msg = self.format(record)
        self.log_widget.after(0, self._append_log, msg)

    def _append_log(self, msg):
        self.log_widget.config(state='normal')
        self.log_widget.insert(tk.END, msg + '\n')
        self.log_widget.see(tk.END)
        self.log_widget.config(state='disabled')


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file="gui_config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        """載入配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"載入配置失敗: {e}")
                return self.get_default_config()
        else:
            return self.get_default_config()

    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失敗: {e}")

    def get_default_config(self):
        """獲取默認配置"""
        return {
            "window_size": "1200x800",
            "theme": "default",
            "last_input_dir": "",
            "last_output_dir": "",
            "log_level": "INFO",

            # 標籤轉換器配置
            "converter_resize": 1.0,
            "converter_quality": 75,
            "converter_format": "auto",

            # 標籤編輯器配置
            "editor_interactive": True,

            # 數據集分割器配置
            "divider_train_ratio": 0.7,
            "divider_val_ratio": 0.15,
            "divider_test_ratio": 0.15,

            # 圖像增強器配置
            "augmenter_num_generations": 200,
            "augmenter_task_type": "both",

            # 全景增強器配置
            "panorama_methods": ["orientation"],
            "panorama_num_variations": 8,
            "panorama_auto_visualize": False,
            "panorama_save_visualization": False,
            "panorama_max_tilt": 20.0,
            "panorama_angle_step": 45.0
        }


class ToolFrame(ttk.Frame):
    """工具界面基類"""

    def __init__(self, parent, app):
        super().__init__(parent)
        self.app = app
        self.setup_ui()

    def setup_ui(self):
        """設置UI，子類需要實現"""
        pass

    def validate_inputs(self):
        """驗證輸入，子類需要實現"""
        return True

    def run_tool(self):
        """運行工具，子類需要實現"""
        pass


class PanoramaAugmenterFrame(ToolFrame):
    """全景圖像擴增器界面"""

    def setup_ui(self):
        # 主標題
        title_label = ttk.Label(self, text="全景圖像擴增器",
                                font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # 輸入設置框架
        input_frame = ttk.LabelFrame(self, text="輸入設置", padding=10)
        input_frame.pack(fill="x", padx=10, pady=5)

        # 圖像路徑
        ttk.Label(input_frame, text="圖像路徑:").grid(
            row=0, column=0, sticky="w", pady=2)
        self.image_path_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.image_path_var,
                  width=50).grid(row=0, column=1, padx=5)
        ttk.Button(input_frame, text="瀏覽",
                   command=self.browse_image).grid(row=0, column=2)

        # 標籤路徑（可選）
        ttk.Label(input_frame, text="標籤路徑:").grid(
            row=1, column=0, sticky="w", pady=2)
        self.label_path_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.label_path_var,
                  width=50).grid(row=1, column=1, padx=5)
        ttk.Button(input_frame, text="瀏覽",
                   command=self.browse_label).grid(row=1, column=2)

        # Excel路徑（批量處理）
        ttk.Label(input_frame, text="Excel外方位:").grid(
            row=2, column=0, sticky="w", pady=2)
        self.excel_path_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.excel_path_var,
                  width=50).grid(row=2, column=1, padx=5)
        ttk.Button(input_frame, text="瀏覽",
                   command=self.browse_excel).grid(row=2, column=2)

        # 外方位參數框架
        orientation_frame = ttk.LabelFrame(self, text="外方位參數", padding=10)
        orientation_frame.pack(fill="x", padx=10, pady=5)

        # Omega (俯仰角)
        ttk.Label(orientation_frame, text="Omega (俯仰角, 度):").grid(
            row=0, column=0, sticky="w", pady=2)
        self.omega_var = tk.DoubleVar(value=0.0)
        ttk.Entry(orientation_frame, textvariable=self.omega_var,
                  width=15).grid(row=0, column=1, padx=5)

        # Phi (橫滾角)
        ttk.Label(orientation_frame, text="Phi (橫滾角, 度):").grid(
            row=0, column=2, sticky="w", pady=2, padx=(20, 0))
        self.phi_var = tk.DoubleVar(value=0.0)
        ttk.Entry(orientation_frame, textvariable=self.phi_var,
                  width=15).grid(row=0, column=3, padx=5)

        # Kappa (偏航角)
        ttk.Label(orientation_frame, text="Kappa (偏航角, 度):").grid(
            row=0, column=4, sticky="w", pady=2, padx=(20, 0))
        self.kappa_var = tk.DoubleVar(value=0.0)
        ttk.Entry(orientation_frame, textvariable=self.kappa_var,
                  width=15).grid(row=0, column=5, padx=5)

        # 擴增設置框架
        augment_frame = ttk.LabelFrame(self, text="擴增設置", padding=10)
        augment_frame.pack(fill="x", padx=10, pady=5)

        # 擴增方法
        ttk.Label(augment_frame, text="擴增方法:").grid(
            row=0, column=0, sticky="w", pady=2)
        self.methods_frame = ttk.Frame(augment_frame)
        self.methods_frame.grid(row=0, column=1, sticky="w", padx=5)

        self.method_vars = {}
        methods = [
            ("orientation", "基於外方位的視角擴增（推薦）"),
            ("rotation", "隨機旋轉擴增"),
            ("perspective", "隨機視角變換"),
            ("tilt", "傾斜擴增"),
            ("multi_angle", "多角度擴增")
        ]

        for i, (method, description) in enumerate(methods):
            var = tk.BooleanVar(value=(method == "orientation"))
            self.method_vars[method] = var
            cb = ttk.Checkbutton(
                self.methods_frame, text=f"{method} ({description})", variable=var)
            cb.grid(row=i, column=0, sticky="w", pady=1)

        # 變化數量
        ttk.Label(augment_frame, text="變化數量:").grid(
            row=1, column=0, sticky="w", pady=2)
        self.num_variations_var = tk.IntVar(
            value=self.app.config_manager.config.get("panorama_num_variations", 8))
        ttk.Spinbox(augment_frame, from_=1, to=20, textvariable=self.num_variations_var,
                    width=10).grid(row=1, column=1, sticky="w", padx=5)

        # 最大傾斜角度
        ttk.Label(augment_frame, text="最大傾斜角度:").grid(
            row=2, column=0, sticky="w", pady=2)
        self.max_tilt_var = tk.DoubleVar(
            value=self.app.config_manager.config.get("panorama_max_tilt", 20.0))
        ttk.Entry(augment_frame, textvariable=self.max_tilt_var,
                  width=10).grid(row=2, column=1, sticky="w", padx=5)

        # 角度步長
        ttk.Label(augment_frame, text="角度步長:").grid(
            row=3, column=0, sticky="w", pady=2)
        self.angle_step_var = tk.DoubleVar(
            value=self.app.config_manager.config.get("panorama_angle_step", 45.0))
        ttk.Entry(augment_frame, textvariable=self.angle_step_var,
                  width=10).grid(row=3, column=1, sticky="w", padx=5)

        # 輸出設置框架
        output_frame = ttk.LabelFrame(self, text="輸出設置", padding=10)
        output_frame.pack(fill="x", padx=10, pady=5)

        # 輸出目錄
        ttk.Label(output_frame, text="輸出目錄:").grid(
            row=0, column=0, sticky="w", pady=2)
        self.output_path_var = tk.StringVar()
        ttk.Entry(output_frame, textvariable=self.output_path_var,
                  width=50).grid(row=0, column=1, padx=5)
        ttk.Button(output_frame, text="瀏覽",
                   command=self.browse_output).grid(row=0, column=2)

        # 可視化選項
        self.visualize_var = tk.BooleanVar(
            value=self.app.config_manager.config.get("panorama_auto_visualize", False))
        ttk.Checkbutton(output_frame, text="顯示可視化結果", variable=self.visualize_var).grid(
            row=1, column=0, sticky="w", pady=2)

        self.save_visualization_var = tk.BooleanVar(
            value=self.app.config_manager.config.get("panorama_save_visualization", False))
        ttk.Checkbutton(output_frame, text="保存可視化圖像", variable=self.save_visualization_var).grid(
            row=1, column=1, sticky="w", pady=2)

        # 處理模式框架
        mode_frame = ttk.LabelFrame(self, text="處理模式", padding=10)
        mode_frame.pack(fill="x", padx=10, pady=5)

        self.batch_mode_var = tk.BooleanVar()
        ttk.Checkbutton(mode_frame, text="批量處理模式", variable=self.batch_mode_var).grid(
            row=0, column=0, sticky="w")

        # 執行按鈕框架
        button_frame = ttk.Frame(self)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(button_frame, text="開始處理", command=self.run_tool,
                   style="Accent.TButton").pack(side="left", padx=5)
        ttk.Button(button_frame, text="重置設置",
                   command=self.reset_settings).pack(side="left", padx=5)
        ttk.Button(button_frame, text="保存配置",
                   command=self.save_settings).pack(side="left", padx=5)

    def browse_image(self):
        filename = filedialog.askopenfilename(
            title="選擇圖像文件",
            filetypes=[("圖像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        if filename:
            self.image_path_var.set(filename)

    def browse_label(self):
        filename = filedialog.askopenfilename(
            title="選擇標籤文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            self.label_path_var.set(filename)

    def browse_excel(self):
        filename = filedialog.askopenfilename(
            title="選擇Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if filename:
            self.excel_path_var.set(filename)

    def browse_output(self):
        dirname = filedialog.askdirectory(title="選擇輸出目錄")
        if dirname:
            self.output_path_var.set(dirname)

    def validate_inputs(self):
        if not self.image_path_var.get() and not self.batch_mode_var.get():
            messagebox.showerror("錯誤", "請選擇圖像文件或啟用批量處理模式")
            return False

        if not self.output_path_var.get():
            messagebox.showerror("錯誤", "請選擇輸出目錄")
            return False

        # 檢查至少選擇了一種擴增方法
        selected_methods = [method for method,
                            var in self.method_vars.items() if var.get()]
        if not selected_methods:
            messagebox.showerror("錯誤", "請至少選擇一種擴增方法")
            return False

        return True

    def run_tool(self):
        if not self.validate_inputs():
            return

        # 準備參數
        params = {
            'image_path': self.image_path_var.get(),
            'label_path': self.label_path_var.get() if self.label_path_var.get() else None,
            'excel_path': self.excel_path_var.get() if self.excel_path_var.get() else None,
            'output_dir': self.output_path_var.get(),
            'omega': self.omega_var.get(),
            'phi': self.phi_var.get(),
            'kappa': self.kappa_var.get(),
            'methods': [method for method, var in self.method_vars.items() if var.get()],
            'num_variations': self.num_variations_var.get(),
            'max_tilt': self.max_tilt_var.get(),
            'angle_step': self.angle_step_var.get(),
            'visualize': self.visualize_var.get(),
            'save_visualization': self.save_visualization_var.get(),
            'batch_mode': self.batch_mode_var.get()
        }

        # 在後台線程中運行
        threading.Thread(target=self._run_panorama_augmenter,
                         args=(params,), daemon=True).start()

    def _run_panorama_augmenter(self, params):
        try:
            self.app.log("開始全景圖像擴增處理...")

            # 創建擴增器
            augmenter = PanoramaAugmenter(
                logger=self.app.logger, config=self.app.config_manager.config)

            if params['batch_mode']:
                # 批量處理
                stats = augmenter.process_batch(
                    input_dir=os.path.dirname(
                        params['image_path']) if params['image_path'] else ".",
                    output_dir=params['output_dir'],
                    excel_path=params['excel_path'],
                    methods=params['methods']
                )
                self.app.log(
                    f"批量處理完成：處理 {stats['processed_images']} 張圖像，生成 {stats['total_augmented']} 張擴增圖像")
            else:
                # 單張處理
                orientation = (params['omega'], params['phi'], params['kappa']) if any(
                    [params['omega'], params['phi'], params['kappa']]) else None

                results = augmenter.process_single_image(
                    image_path=params['image_path'],
                    label_path=params['label_path'],
                    orientation=orientation,
                    methods=params['methods'],
                    output_dir=params['output_dir'],
                    visualize=params['visualize'],
                    save_visualization=params['save_visualization']
                )

                total_generated = sum(len(aug_list)
                                      for aug_list in results.values())
                self.app.log(f"單張處理完成，生成 {total_generated} 張擴增圖像")

            self.app.log("全景圖像擴增處理完成！")

        except Exception as e:
            self.app.log(f"處理過程中發生錯誤: {e}")

    def reset_settings(self):
        """重置設置為默認值"""
        self.image_path_var.set("")
        self.label_path_var.set("")
        self.excel_path_var.set("")
        self.output_path_var.set("")
        self.omega_var.set(0.0)
        self.phi_var.set(0.0)
        self.kappa_var.set(0.0)
        self.num_variations_var.set(8)
        self.max_tilt_var.set(20.0)
        self.angle_step_var.set(45.0)
        self.visualize_var.set(False)
        self.save_visualization_var.set(False)
        self.batch_mode_var.set(False)

        # 重置方法選擇
        for method, var in self.method_vars.items():
            var.set(method == "orientation")

    def save_settings(self):
        """保存當前設置到配置文件"""
        config = self.app.config_manager.config
        config["panorama_num_variations"] = self.num_variations_var.get()
        config["panorama_max_tilt"] = self.max_tilt_var.get()
        config["panorama_angle_step"] = self.angle_step_var.get()
        config["panorama_auto_visualize"] = self.visualize_var.get()
        config["panorama_save_visualization"] = self.save_visualization_var.get()
        config["panorama_methods"] = [method for method,
                                      var in self.method_vars.items() if var.get()]

        self.app.config_manager.save_config()
        messagebox.showinfo("成功", "配置已保存")


class MainApplication:
    """主應用程式類"""

    def __init__(self):
        self.root = tk.Tk()
        self.config_manager = ConfigManager()
        self.setup_logging()
        self.setup_ui()
        self.setup_styles()

    def setup_logging(self):
        """設置日誌"""
        self.logger = logging.getLogger("ImageDatasetGUI")
        self.logger.setLevel(logging.INFO)

    def setup_ui(self):
        """設置主界面"""
        self.root.title("圖像數據集處理工具集 v2.0")
        self.root.geometry(self.config_manager.config.get(
            "window_size", "1200x800"))

        # 創建主菜單
        self.create_menu()

        # 創建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 創建筆記本控件（標籤頁）
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill="both", expand=True)

        # 創建各個工具的標籤頁
        self.create_tool_tabs()

        # 創建底部狀態和日誌區域
        self.create_bottom_panel()

    def create_menu(self):
        """創建主菜單"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜單
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建配置", command=self.new_config)
        file_menu.add_command(label="載入配置", command=self.load_config)
        file_menu.add_command(label="保存配置", command=self.save_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 工具菜單
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清除日誌", command=self.clear_log)
        tools_menu.add_command(
            label="開啟輸出目錄", command=self.open_output_directory)

        # 幫助菜單
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="幫助", menu=help_menu)
        help_menu.add_command(label="使用手冊", command=self.show_manual)
        help_menu.add_command(label="關於", command=self.show_about)

    def create_tool_tabs(self):
        """創建工具標籤頁"""
        # 標籤格式轉換器
        converter_frame = ttk.Frame(self.notebook)
        self.notebook.add(converter_frame, text="標籤格式轉換器")
        ttk.Label(converter_frame, text="標籤格式轉換器界面開發中...",
                  font=("Arial", 14)).pack(pady=50)

        # 標籤編輯器
        editor_frame = ttk.Frame(self.notebook)
        self.notebook.add(editor_frame, text="標籤編輯器")
        ttk.Label(editor_frame, text="標籤編輯器界面開發中...",
                  font=("Arial", 14)).pack(pady=50)

        # 數據集分割器
        divider_frame = ttk.Frame(self.notebook)
        self.notebook.add(divider_frame, text="數據集分割器")
        ttk.Label(divider_frame, text="數據集分割器界面開發中...",
                  font=("Arial", 14)).pack(pady=50)

        # 圖像增強器
        augmenter_frame = ttk.Frame(self.notebook)
        self.notebook.add(augmenter_frame, text="圖像增強器")
        ttk.Label(augmenter_frame, text="圖像增強器界面開發中...",
                  font=("Arial", 14)).pack(pady=50)

        # 全景圖像擴增器
        panorama_frame = ttk.Frame(self.notebook)
        self.notebook.add(panorama_frame, text="全景圖像擴增器")
        self.panorama_tool = PanoramaAugmenterFrame(panorama_frame, self)
        self.panorama_tool.pack(fill="both", expand=True)

        # 整合流程
        pipeline_frame = ttk.Frame(self.notebook)
        self.notebook.add(pipeline_frame, text="整合流程")
        ttk.Label(pipeline_frame, text="整合流程界面開發中...",
                  font=("Arial", 14)).pack(pady=50)

    def create_bottom_panel(self):
        """創建底部面板"""
        # 創建底部框架
        bottom_frame = ttk.Frame(self.main_frame)
        bottom_frame.pack(fill="x", pady=(10, 0))

        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            bottom_frame, variable=self.progress_var, mode='determinate')
        self.progress_bar.pack(fill="x", pady=(0, 5))

        # 日誌區域
        log_frame = ttk.LabelFrame(bottom_frame, text="處理日誌", padding=5)
        log_frame.pack(fill="both", expand=True)

        self.log_text = scrolledtext.ScrolledText(
            log_frame, height=8, state='disabled')
        self.log_text.pack(fill="both", expand=True)

        # 設置日誌處理器
        log_handler = LogHandler(self.log_text)
        log_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(log_handler)

    def setup_styles(self):
        """設置樣式"""
        style = ttk.Style()

        # 設置主題
        try:
            style.theme_use('clam')
        except:
            pass

        # 自定義樣式
        style.configure("Accent.TButton", foreground="white",
                        background="#0078d4")
        style.map("Accent.TButton", background=[('active', '#106ebe')])

    def log(self, message):
        """記錄日誌"""
        self.logger.info(message)

    def new_config(self):
        """新建配置"""
        result = messagebox.askyesno("新建配置", "這將重置所有設置為默認值，確定繼續嗎？")
        if result:
            self.config_manager.config = self.config_manager.get_default_config()
            self.config_manager.save_config()
            messagebox.showinfo("成功", "配置已重置為默認值")

    def load_config(self):
        """載入配置"""
        filename = filedialog.askopenfilename(
            title="載入配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config_manager.config = json.load(f)
                messagebox.showinfo("成功", "配置已載入")
            except Exception as e:
                messagebox.showerror("錯誤", f"載入配置失敗: {e}")

    def save_config(self):
        """保存配置"""
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config_manager.config, f,
                              ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("錯誤", f"保存配置失敗: {e}")

    def clear_log(self):
        """清除日誌"""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

    def open_output_directory(self):
        """開啟輸出目錄"""
        output_dir = self.config_manager.config.get("last_output_dir", "")
        if output_dir and os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
        else:
            messagebox.showwarning("警告", "輸出目錄不存在")

    def show_manual(self):
        """顯示使用手冊"""
        manual_window = tk.Toplevel(self.root)
        manual_window.title("使用手冊")
        manual_window.geometry("800x600")

        manual_text = scrolledtext.ScrolledText(manual_window, wrap=tk.WORD)
        manual_text.pack(fill="both", expand=True, padx=10, pady=10)

        manual_content = """
# 圖像數據集處理工具集使用手冊

## 概述
本工具集提供了完整的圖像數據集處理解決方案，包含5個主要工具：

1. **標籤格式轉換器** - 轉換各種標籤格式為LabelMe格式
2. **標籤編輯器** - 編輯、合併、重命名標籤
3. **數據集分割器** - 分割數據集為訓練/驗證/測試集
4. **圖像增強器** - 通過區域融合生成增強數據
5. **全景圖像擴增器** - 專門針對全景圖像的擴增工具

## 全景圖像擴增器使用說明

### 基本設置
1. **圖像路徑**: 選擇要處理的全景圖像文件
2. **標籤路徑**: 可選，選擇對應的LabelMe標籤文件
3. **Excel外方位**: 可選，用於批量處理時的外方位參數文件

### 外方位參數
- **Omega**: 俯仰角（度），相機繞X軸的旋轉
- **Phi**: 橫滾角（度），相機繞Y軸的旋轉
- **Kappa**: 偏航角（度），相機繞Z軸的旋轉

### 擴增方法
- **orientation**: 基於外方位的視角擴增（推薦）
- **rotation**: 隨機旋轉擴增
- **perspective**: 隨機視角變換
- **tilt**: 傾斜擴增
- **multi_angle**: 多角度擴增

### 參數設置
- **變化數量**: 每種方法生成的變化數量
- **最大傾斜角度**: 傾斜擴增的最大角度
- **角度步長**: 多角度擴增的角度間隔

### 輸出選項
- **顯示可視化結果**: 處理完成後顯示結果預覽
- **保存可視化圖像**: 將可視化結果保存為圖像文件

## 使用技巧
1. 對於有外方位參數的圖像，建議使用"orientation"方法
2. 外方位參數應為角度值（-180到180度）
3. 可以同時選擇多種擴增方法
4. 批量處理時需要準備Excel文件包含外方位信息

## 快捷鍵
- Ctrl+N: 新建配置
- Ctrl+O: 載入配置
- Ctrl+S: 保存配置
- F1: 顯示幫助

## 問題排解
1. 如果圖像比例不正確，請確保圖像為2:1比例（全景圖像標準）
2. 如果外方位參數過大，工具會自動使用零值替代
3. 可視化功能需要matplotlib支持

## 聯繫支持
如有問題，請查看日誌輸出或聯繫開發團隊。
        """

        manual_text.insert(1.0, manual_content)
        manual_text.config(state='disabled')

    def show_about(self):
        """顯示關於對話框"""
        about_text = """
圖像數據集處理工具集 v2.0

一個專為計算機視覺任務設計的完整數據集處理解決方案

主要功能：
• 標籤格式轉換
• 標籤編輯和管理
• 數據集分割
• 圖像增強
• 全景圖像擴增

開發語言: Python
GUI框架: tkinter
圖像處理: OpenCV, PIL
數據處理: pandas, numpy

© 2024 圖像數據集處理工具集團隊
        """
        messagebox.showinfo("關於", about_text)

    def run(self):
        """運行應用程式"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """應用程式關閉時的處理"""
        # 保存窗口大小
        self.config_manager.config["window_size"] = self.root.geometry()
        self.config_manager.save_config()
        self.root.destroy()


if __name__ == "__main__":
    app = MainApplication()
    app.run()
