#!/usr/bin/env python3
"""
🛠️ LabelMe JSON輸出修復工具
針對檢測到的問題提供具體的修復方案
"""

from pathlib import Path
import json

def fix_advanced_inference_wrapper():
    """修復AdvancedInferenceWrapper中的mask數據保留問題"""
    
    wrapper_file = Path("models/inference/advanced_inference_wrapper.py")
    
    if not wrapper_file.exists():
        print(f"❌ 檔案不存在: {wrapper_file}")
        return
    
    print(f"🔧 修復 {wrapper_file}")
    
    # 讀取原檔案
    content = wrapper_file.read_text(encoding='utf-8')
    
    # 檢查是否已經修復
    if "# LABELME_FIX: 保留mask數據" in content:
        print("   ℹ️ 檔案已經修復過")
        return
    
    # 找到並替換關鍵代碼段
    old_code = """            # 關鍵修復：調用AdvancedSliceInference的_convert_pred_annotations方法
            # 這將正確應用label_aliases轉換和class_configs映射
            detections = self.advanced_inference._convert_pred_annotations(
                raw_detections)"""
    
    new_code = """            # LABELME_FIX: 保留mask數據
            # 先調用轉換方法處理類別映射
            converted_detections = self.advanced_inference._convert_pred_annotations(
                raw_detections)
            
            # 然後手動保留原始的mask數據
            detections = []
            for i, converted_det in enumerate(converted_detections):
                if i < len(raw_detections):
                    # 保留原始mask數據
                    converted_det['mask'] = raw_detections[i].get('mask', None)
                detections.append(converted_det)"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        wrapper_file.write_text(content, encoding='utf-8')
        print("   ✅ 修復完成: 保留mask數據在類別轉換過程中")
    else:
        print("   ⚠️ 未找到目標代碼段，請手動檢查")

def fix_labelme_integration():
    """修復LabelMe整合器的錯誤處理"""
    
    integration_file = Path("models/inference/labelme_integration.py")
    
    if not integration_file.exists():
        print(f"❌ 檔案不存在: {integration_file}")
        return
    
    print(f"🔧 修復 {integration_file}")
    
    # 讀取原檔案
    content = integration_file.read_text(encoding='utf-8')
    
    # 檢查是否已經修復
    if "# LABELME_FIX: 增強錯誤檢查" in content:
        print("   ℹ️ 檔案已經修復過")
        return
    
    # 找到並替換關鍵代碼段
    old_code = """            try:
                # 檢查必要的字段
                if 'mask' not in detection or detection['mask'] is None:
                    continue"""
    
    new_code = """            try:
                # LABELME_FIX: 增強錯誤檢查
                # 檢查必要的字段
                if 'mask' not in detection or detection['mask'] is None:
                    logging.warning(f"Detection missing mask data: class_id={detection.get('class_id', 'unknown')}, confidence={detection.get('confidence', 'unknown')}")
                    continue"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        integration_file.write_text(content, encoding='utf-8')
        print("   ✅ 修復完成: 增強mask數據檢查的日誌輸出")
    else:
        print("   ⚠️ 未找到目標代碼段，請手動檢查")
    
    # 修復另一個關鍵錯誤處理點
    old_code2 = """        except Exception as e:
            logging.error(f"❌ Error processing single image result: {e}")
            return None"""
    
    new_code2 = """        except Exception as e:
            logging.error(f"❌ Error processing single image result: {e}")
            import traceback
            logging.error(f"❌ Detailed error: {traceback.format_exc()}")
            return None"""
    
    if old_code2 in content:
        content = content.replace(old_code2, new_code2)
        integration_file.write_text(content, encoding='utf-8')
        print("   ✅ 修復完成: 增強錯誤處理的詳細日誌")

def add_debug_logging():
    """添加詳細的調試日誌到主腳本"""
    
    main_file = Path("run_unified_yolo.py")
    
    if not main_file.exists():
        print(f"❌ 檔案不存在: {main_file}")
        return
    
    print(f"🔧 添加調試日誌到 {main_file}")
    
    # 讀取原檔案
    content = main_file.read_text(encoding='utf-8')
    
    # 檢查是否已經添加
    if "# LABELME_DEBUG: 詳細日誌" in content:
        print("   ℹ️ 調試日誌已經添加過")
        return
    
    # 找到LabelMe處理區域並添加日誌
    old_code = """            # 🏷️ 生成LabelMe JSON檔案 (批次模式)
            if labelme_integration.enabled:
                print(f"\\n🏷️ 生成LabelMe JSON檔案...")
                labelme_files = labelme_integration.process_batch_results(results)
                if labelme_files:
                    print(f"✅ LabelMe JSON生成完成: {len(labelme_files)} 個檔案")
                    print(f"📁 JSON輸出目錄: {labelme_integration.output_dir}")
                else:
                    print("ℹ️ 無有效檢測結果，未生成LabelMe JSON檔案")"""
    
    new_code = """            # 🏷️ 生成LabelMe JSON檔案 (批次模式)
            if labelme_integration.enabled:
                print(f"\\n🏷️ 生成LabelMe JSON檔案...")
                
                # LABELME_DEBUG: 詳細日誌
                print(f"🔍 LabelMe整合器狀態: enabled={labelme_integration.enabled}")
                print(f"📁 輸出目錄: {labelme_integration.output_dir}")
                print(f"📊 批次結果數量: {len(results)}")
                
                # 檢查每個結果的mask數據
                mask_count = 0
                for i, result in enumerate(results):
                    detections = result.get('detections', [])
                    for j, det in enumerate(detections):
                        if 'mask' in det and det['mask'] is not None:
                            mask_count += 1
                        else:
                            print(f"⚠️ Result[{i}] Detection[{j}]: 無mask數據 - class_id={det.get('class_id', 'unknown')}")
                
                print(f"🎯 有效mask數量: {mask_count}")
                
                labelme_files = labelme_integration.process_batch_results(results)
                if labelme_files:
                    print(f"✅ LabelMe JSON生成完成: {len(labelme_files)} 個檔案")
                    print(f"📁 JSON輸出目錄: {labelme_integration.output_dir}")
                    for file_path in labelme_files:
                        print(f"   📄 {Path(file_path).name}")
                else:
                    print("❌ 無有效檢測結果，未生成LabelMe JSON檔案")
                    print("🔍 可能原因:")
                    print("   1. 模型不是分割模型，沒有輸出mask")
                    print("   2. 檢測結果在轉換過程中丟失了mask數據")
                    print("   3. mask數據格式不正確")"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        main_file.write_text(content, encoding='utf-8')
        print("   ✅ 添加完成: 批次模式調試日誌")
    
    # 單張圖像模式也添加類似日誌
    old_code_single = """            # 🏷️ 生成LabelMe JSON檔案 (單張模式)
            if labelme_integration.enabled and result.get('detections'):
                print(f"\\n🏷️ 生成LabelMe JSON檔案...")
                labelme_file = labelme_integration.process_single_image_result(
                    image_path=_input_path,
                    detections=result['detections']
                )
                if labelme_file:
                    print(f"✅ LabelMe JSON生成完成: {Path(labelme_file).name}")
                    print(f"📁 JSON檔案位置: {labelme_file}")
                else:
                    print("ℹ️ 無有效檢測結果，未生成LabelMe JSON檔案")"""
    
    new_code_single = """            # 🏷️ 生成LabelMe JSON檔案 (單張模式)
            if labelme_integration.enabled and result.get('detections'):
                print(f"\\n🏷️ 生成LabelMe JSON檔案...")
                
                # LABELME_DEBUG: 詳細日誌
                detections = result['detections']
                print(f"🔍 檢測結果數量: {len(detections)}")
                
                mask_count = 0
                for i, det in enumerate(detections):
                    if 'mask' in det and det['mask'] is not None:
                        mask_count += 1
                        print(f"✅ Detection[{i}]: 有mask數據 - class_id={det.get('class_id', 'unknown')}, shape={det['mask'].shape if hasattr(det['mask'], 'shape') else 'unknown'}")
                    else:
                        print(f"❌ Detection[{i}]: 無mask數據 - class_id={det.get('class_id', 'unknown')}")
                
                print(f"🎯 有效mask數量: {mask_count}/{len(detections)}")
                
                labelme_file = labelme_integration.process_single_image_result(
                    image_path=_input_path,
                    detections=result['detections']
                )
                if labelme_file:
                    print(f"✅ LabelMe JSON生成完成: {Path(labelme_file).name}")
                    print(f"📁 JSON檔案位置: {labelme_file}")
                else:
                    print("❌ 無有效檢測結果，未生成LabelMe JSON檔案")"""
    
    if old_code_single in content:
        content = content.replace(old_code_single, new_code_single)
        main_file.write_text(content, encoding='utf-8')
        print("   ✅ 添加完成: 單張模式調試日誌")

def create_test_script():
    """創建簡化的測試腳本"""
    
    test_file = Path("test_labelme_simple.py")
    
    test_content = '''#!/usr/bin/env python3
"""
🧪 簡化的LabelMe JSON生成測試
直接測試核心功能，繞過複雜的推理鏈
"""

import cv2
import numpy as np
from pathlib import Path
import sys

# 添加項目路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_direct_labelme_generation():
    """直接測試LabelMe JSON生成"""
    
    try:
        from models.inference.labelme_json_generator import LabelMeJSONGenerator
        
        # 創建測試數據
        generator = LabelMeJSONGenerator()
        
        # 模擬檢測結果
        test_image_path = r"D:\\image\\5_test_image\\test_2_org\\BMP-2291_20241126_130131461.jpg"
        
        if not Path(test_image_path).exists():
            print(f"❌ 測試圖像不存在: {test_image_path}")
            return
            
        # 讀取圖像
        image = cv2.imread(test_image_path)
        if image is None:
            print(f"❌ 無法讀取圖像: {test_image_path}")
            return
            
        h, w = image.shape[:2]
        print(f"📐 圖像尺寸: {w}x{h}")
        
        # 創建測試mask
        test_mask = np.zeros((h, w), dtype=np.uint8)
        test_mask[100:200, 100:300] = 255  # 創建一個矩形區域
        
        # 模擬檢測結果
        fake_detections = [
            {
                'class_id': 2,
                'confidence': 0.85,
                'mask': test_mask
            }
        ]
        
        # 類別名稱映射
        class_names = {
            2: 'linear_crack'
        }
        
        # 生成LabelMe JSON
        output_dir = r"D:\\image\\5_test_image\\test_2_out\\labelme_json_test"
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        json_path = generator.process_image_results(
            image_path=test_image_path,
            results=fake_detections,
            output_dir=output_dir,
            class_names=class_names
        )
        
        if json_path:
            print(f"✅ 測試成功! LabelMe JSON已生成: {json_path}")
            
            # 檢查生成的JSON內容
            import json
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"📝 JSON內容摘要:")
            print(f"   版本: {json_data.get('version', 'unknown')}")
            print(f"   圖像檔名: {json_data.get('imagePath', 'unknown')}")
            print(f"   形狀數量: {len(json_data.get('shapes', []))}")
            
            if json_data.get('shapes'):
                shape = json_data['shapes'][0]
                print(f"   第一個形狀: {shape.get('label', 'unknown')}, 點數: {len(shape.get('points', []))}")
        else:
            print(f"❌ 測試失敗! 未生成LabelMe JSON檔案")
            
    except Exception as e:
        print(f"❌ 測試過程出錯: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 簡化LabelMe JSON生成測試")
    print("=" * 40)
    test_direct_labelme_generation()
'''
    
    test_file.write_text(test_content, encoding='utf-8')
    print(f"✅ 創建測試腳本: {test_file}")

def main():
    """主修復函數"""
    print("🛠️ LabelMe JSON輸出修復工具")
    print("=" * 40)
    
    print("📝 執行修復步驟:")
    
    # 1. 修復高級推理包裝器
    fix_advanced_inference_wrapper()
    
    # 2. 修復LabelMe整合器
    fix_labelme_integration()
    
    # 3. 添加調試日誌
    add_debug_logging()
    
    # 4. 創建測試腳本
    create_test_script()
    
    print("\n✅ 修復完成!")
    print("\n📋 後續步驟:")
    print("1. 運行診斷工具: python debug_labelme_output.py")
    print("2. 運行簡化測試: python test_labelme_simple.py")
    print("3. 重新運行主程序並查看詳細日誌")
    print("4. 如果仍有問題，檢查模型是否為分割模型")

if __name__ == "__main__":
    main()