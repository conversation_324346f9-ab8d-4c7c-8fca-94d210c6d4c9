# 🚀 統一YOLO推理工具 (Unified YOLO Inference Tool)

## 📖 概述

統一YOLO推理工具整合了 **Enhanced YOLO** 和 **Simple Model Labeling Tool** 的所有優勢功能，提供完整的道路基礎設施檢測解決方案。

### ✨ 核心特色

- **🔥 雙引擎驅動**: Enhanced YOLO (YOLO12+YOLO11) + Simple Tool (雙模型協同)
- **🧩 完整SAHI支援**: 23個精細參數，支援大圖切片推理
- **🎯 智能檢測優化**: 類別特定閾值、檢測合併、類別間過濾
- **📊 多模式可視化**: 標準/對比/組合三種可視化模式，支援GT對比
- **⚡ 高級系統管理**: 智能記憶體管理、性能監控、GPU優化
- **🏷️ 自動化標註**: LabelMe自動類別生成、多格式輸出

## 📁 文件結構

```
examples/
├── unified_yolo_inference.py       # 🎯 主要整合工具
├── unified_quick_start.py          # 🚀 快速開始示例  
├── unified_config_example.json     # ⚙️ 配置文件示例
├── README_unified.md               # 📚 本說明文件
├── enhanced_yolo_usage_backup.py   # 💾 Enhanced YOLO備份
└── simple_model_labeling_tool_backup.py  # 💾 Simple Tool備份
```

## 🚀 快速開始

### 1. 基礎使用

```python
from unified_yolo_inference import UnifiedYOLOConfig, create_unified_inference, TaskType

# 創建配置
config = UnifiedYOLOConfig()
config.model_config.detection_model_path = "path/to/your/model.pt"

# 創建推理器
inference = create_unified_inference(config)

# 執行推理
results = inference.predict_single_image(
    image_path="test.jpg",
    output_dir="./output"
)
```

### 2. 使用快速開始腳本

```python
# 運行預定義模式
python unified_quick_start.py
```

### 3. 批次處理

```python
# 批次處理目錄
results = inference.batch_predict(
    input_dir="./input_images",
    output_dir="./output",
    task_type=TaskType.BOTH
)
```

## ⚙️ 配置系統

### 📊 配置結構

```python
UnifiedYOLOConfig:
├── model_config      # 模型配置 (Enhanced YOLO + Simple Tool)
├── sahi_config       # SAHI配置 (23個完整參數)
├── processing_config # 處理配置 (可視化+輸出)
└── annotation_config # 標註配置 (LabelMe+GT對比)
```

### 🎯 關鍵配置選項

#### 模型配置 (model_config)
```python
detection_model_path: str          # YOLO12檢測模型
segmentation_model_path: str       # YOLO11分割模型
secondary_model_path: str          # 雙模型協同副模型
enable_consensus: bool             # 啟用共識機制
class_thresholds: Dict[int, float] # 類別特定閾值
enable_class_filtering: bool       # 智能類別過濾
```

#### SAHI配置 (sahi_config) - 23個完整參數
```python
enable_sahi: bool                  # 啟用SAHI
slice_height: int                  # 切片高度
slice_width: int                   # 切片寬度
overlap_height_ratio: float        # 高度重疊比例
overlap_width_ratio: float         # 寬度重疊比例
postprocess_type: str              # 後處理類型: "GREEDYNMM"/"NMM"/"NMS"
roi_ratio: Tuple[float, ...]       # ROI區域比例
exclude_classes_by_name: List[str] # 按名稱排除類別
exclude_classes_by_id: List[int]   # 按ID排除類別
# ... 更多參數見配置文件
```

#### 可視化配置 (processing_config)
```python
visualization_mode: VisualizationMode  # 可視化模式
  - STANDARD: 原圖+檢測+分割+統計 (Enhanced YOLO風格)
  - COMPARISON: 原圖+GT+預測對比 (新增功能)
  - COMBINED: 完整6面板展示 (整合模式)
  
output_format: OutputFormat           # 輸出格式
  - BBOX: 邊界框格式
  - MASK: 遮罩格式  
  - BOTH: 雙格式輸出
  
smart_label_counting: bool            # 智能label計數
target_classes: List[int]             # 指定保存類別
```

## 🎯 使用模式

### 1. 🏃 快速檢測模式 (Simple Tool優勢)
適用於：純檢測任務，追求精度
```python
config = quick_detection_only()
# 特色：類別特定閾值、智能過濾、檢測合併
```

### 2. 🚀 進階檢測+分割模式 (Enhanced YOLO優勢)  
適用於：複合任務，需要檢測和分割
```python
config = advanced_detection_segmentation()  
# 特色：YOLO12+YOLO11雙模型、SAHI支援、完整可視化
```

### 3. 🤝 雙模型協同模式 (Simple Tool核心優勢)
適用於：高精度要求，模型融合
```python
config = dual_model_consensus()
# 特色：共識機制、類別特定閾值、智能合併
```

### 4. 🧩 SAHI大圖處理模式
適用於：高解析度圖像，大尺寸檢測
```python
config = sahi_large_image()
# 特色：23個精細參數、ROI控制、後處理優化
```

### 5. 📊 GT對比模式 (新增功能)
適用於：模型評估，精度分析
```python
config = gt_comparison_mode()
# 特色：GT載入、對比可視化、性能評估
```

## 📊 功能對比

| 功能類別 | Enhanced YOLO | Simple Tool | **統一工具** |
|---------|---------------|-------------|-------------|
| **雙模型支援** | YOLO12+YOLO11 | 雙模型協同 | ✅ **兩者都支援** |
| **SAHI參數** | 基礎參數 | 標準SAHI | ✅ **23個完整參數** |
| **類別特定閾值** | ❌ | ✅ | ✅ **繼承Simple優勢** |
| **智能過濾** | ❌ | ✅ | ✅ **繼承Simple優勢** |
| **可視化模式** | 2x2網格 | 自定義 | ✅ **3種模式可選** |
| **GT對比** | ❌ | ❌ | ✅ **新增功能** |
| **記憶體管理** | 基礎 | 高級 | ✅ **繼承Simple優勢** |
| **批次智能計數** | ❌ | ❌ | ✅ **新增功能** |

## 🔧 進階配置

### 1. 從配置文件載入
```python
config = UnifiedYOLOConfig.load_config("unified_config_example.json")
```

### 2. 自定義類別閾值
```python
config.model_config.class_thresholds = {
    0: 0.2,  # 背景 - 低閾值
    1: 0.5,  # 裂縫 - 高閾值  
    2: 0.4,  # 坑洞 - 中等閾值
}
```

### 3. SAHI大圖優化
```python
config.sahi_config.enable_sahi = True
config.sahi_config.slice_height = 256          # 小切片提高精度
config.sahi_config.overlap_height_ratio = 0.3  # 高重疊避免遺漏
config.sahi_config.roi_ratio = (0.1, 0.1, 0.9, 0.9)  # 排除邊緣噪聲
config.sahi_config.exclude_classes_by_id = [0] # 排除背景類別
```

### 4. 選擇性類別保存
```python
config.processing_config.target_classes = [1, 2, 3]  # 只保存裂縫、坑洞、龜裂
config.processing_config.skip_empty = True            # 跳過無目標圖像
```

## 📈 性能優化

### 1. GPU記憶體優化
```python
config.model_config.device = "cuda"           # 指定GPU
config.processing_config.batch_size = 8       # 調整批次大小
```

### 2. 推理速度優化
```python
config.sahi_config.enable_sahi = False        # 關閉SAHI提速
config.processing_config.save_visualizations = False  # 關閉可視化提速
```

### 3. 精度優化
```python
config.model_config.enable_consensus = True   # 啟用雙模型共識
config.model_config.enable_class_filtering = True  # 啟用智能過濾
config.sahi_config.overlap_height_ratio = 0.3      # 提高重疊比例
```

## 🛠️ 故障排除

### 1. 常見問題

**Q: 找不到模型文件**
```python
# 確保模型路徑正確
config.model_config.detection_model_path = "絕對路徑/model.pt"
```

**Q: CUDA記憶體不足**
```python
# 降低批次大小或使用CPU
config.model_config.device = "cpu"
config.processing_config.batch_size = 4
```

**Q: 可視化中文亂碼**
```python
# 已內建字體設定，如仍有問題請檢查系統字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
```

### 2. 調試模式
```python
import logging
logging.getLogger('UnifiedYOLO').setLevel(logging.DEBUG)
```

## 📝 更新日誌

### v1.0.0 (最新)
- ✅ 整合Enhanced YOLO和Simple Tool所有功能
- ✅ 新增GT對比可視化功能  
- ✅ 智能label計數功能
- ✅ 統一配置系統
- ✅ 23個完整SAHI參數支援
- ✅ 3種可視化模式
- ✅ 快速開始示例和配置文件

### 備份文件
- `enhanced_yolo_usage_backup.py` - Enhanced YOLO原始版本
- `simple_model_labeling_tool_backup.py` - Simple Tool原始版本

## 🤝 貢獻

歡迎提交問題和改進建議！統一工具整合了兩個優秀工具的精華，持續改進中。

## 📞 支援

如有問題，請檢查：
1. 配置文件是否正確
2. 模型路徑是否存在  
3. 輸入目錄是否包含有效圖像
4. 系統環境是否滿足要求

---

**統一YOLO推理工具 - 整合精華，效能卓越！** 🚀