#!/usr/bin/env python3
"""
增強YOLO推理系統
支持YOLO11分割+YOLO12檢測，自動轉換，SAHI功能等
"""

# 使用統一導入管理
import sys
from pathlib import Path

current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, DATA_PROCESSING_AVAILABLE
setup_project_paths()

import os
import json
import yaml
import logging
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import random
import colorsys
import numpy as np
from collections import defaultdict

# 基本導入（用於類型註釋）
try:
    pass
except ImportError:
    # 創建模擬的numpy模組用於類型註釋
    class MockNumpy:
        class ndarray:
            pass
    np = MockNumpy()

try:
    import cv2
    import torch
    import matplotlib.pyplot as plt
    import seaborn as sns
    from tqdm import tqdm
    CV_AVAILABLE = True
    # 重新導入真實的numpy
    import numpy as np
except ImportError:
    CV_AVAILABLE = False

try:
    from ultralytics import YOLO
    from ultralytics.utils.plotting import Annotator, colors
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False

try:
    from sahi import AutoDetectionModel
    from sahi.predict import get_sliced_prediction
    from sahi.utils.cv import read_image
    from sahi.utils.file import download_from_url
    SAHI_AVAILABLE = True
except ImportError:
    SAHI_AVAILABLE = False

try:
    from sklearn.metrics import (
        precision_score, recall_score, f1_score, 
        average_precision_score, confusion_matrix
    )
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

if DATA_PROCESSING_AVAILABLE:
    try:
        from 資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2
        from 資料前處理.tools.format_detector import FormatDetector
        CONVERTER_AVAILABLE = True
    except ImportError:
        CONVERTER_AVAILABLE = False
else:
    CONVERTER_AVAILABLE = False


def scan_labelme_annotations(labelme_dir: str) -> Dict[str, Any]:
    """
    掃描LabelMe標註文件，提取所有類別資訊
    
    Args:
        labelme_dir: LabelMe標註文件目錄
    
    Returns:
        包含類別統計的字典
    """
    labelme_path = Path(labelme_dir)
    json_files = list(labelme_path.glob("*.json"))
    
    if not json_files:
        raise ValueError(f"在 {labelme_dir} 中未找到LabelMe JSON文件")
    
    class_stats = {}
    all_labels = set()
    total_annotations = 0
    
    for json_file in tqdm(json_files, desc="掃描LabelMe標註"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'shapes' in data:
                for shape in data['shapes']:
                    label = shape.get('label', '')
                    if label:
                        all_labels.add(label)
                        if label not in class_stats:
                            class_stats[label] = {
                                'count': 0,
                                'shape_types': set(),
                                'files': set()
                            }
                        
                        class_stats[label]['count'] += 1
                        class_stats[label]['shape_types'].add(shape.get('shape_type', 'unknown'))
                        class_stats[label]['files'].add(json_file.name)
                        total_annotations += 1
                        
        except Exception as e:
            logging.warning(f"讀取文件失敗 {json_file}: {e}")
    
    # 轉換set為list以便JSON序列化
    for label in class_stats:
        class_stats[label]['shape_types'] = list(class_stats[label]['shape_types'])
        class_stats[label]['files'] = list(class_stats[label]['files'])
    
    return {
        'labels': sorted(list(all_labels)),
        'class_stats': class_stats,
        'total_files': len(json_files),
        'total_annotations': total_annotations,
        'unique_classes': len(all_labels)
    }


def generate_distinct_colors(num_colors: int, seed: int = 42) -> List[Tuple[int, int, int]]:
    """
    生成視覺上區別明顯的顏色 (使用seed確保一致性)
    
    Args:
        num_colors: 需要的顏色數量
        seed: 隨機種子，確保顏色生成一致性
    
    Returns:
        RGB顏色列表
    """
    # 設置隨機種子確保顏色一致性
    random.seed(seed)
    colors = []
    
    # 預定義一些常用顏色
    predefined_colors = [
        (255, 0, 0),     # 紅色
        (0, 255, 0),     # 綠色
        (0, 0, 255),     # 藍色
        (255, 255, 0),   # 黃色
        (255, 0, 255),   # 洋紅色
        (0, 255, 255),   # 青色
        (255, 165, 0),   # 橙色
        (128, 0, 128),   # 紫色
        (255, 192, 203), # 粉色
        (128, 128, 128), # 灰色
        (165, 42, 42),   # 棕色
        (0, 128, 0),     # 深綠色
        (0, 0, 128),     # 深藍色
        (128, 128, 0),   # 橄欖色
        (128, 0, 0),     # 暗紅色
    ]
    
    # 使用預定義顏色
    for i in range(min(num_colors, len(predefined_colors))):
        colors.append(predefined_colors[i])
    
    # 如果需要更多顏色，使用黃金比例生成 (Simple Tool方式)
    if num_colors > len(predefined_colors):
        remaining = num_colors - len(predefined_colors)
        h = random.random()  # 隨機起始色調
        phi = 0.618033988749  # 黃金比例
        
        for i in range(remaining):
            h = (h + phi) % 1.0
            saturation = 0.7 + random.random() * 0.3  # 0.7-1.0
            value = 0.8 + random.random() * 0.2       # 0.8-1.0
            
            rgb = colorsys.hsv_to_rgb(h, saturation, value)
            colors.append(tuple(int(c * 255) for c in rgb))
    
    # 重置隨機種子
    random.seed()
    return colors


def generate_class_configs_from_labelme(labelme_dir: str, 
                                       default_conf: float = 0.5) -> Dict[int, 'ClassConfig']:
    """
    從LabelMe標註自動生成類別配置
    
    Args:
        labelme_dir: LabelMe標註目錄
        default_conf: 預設置信度閾值
    
    Returns:
        類別配置字典
    """
    # 掃描標註
    scan_result = scan_labelme_annotations(labelme_dir)
    labels = scan_result['labels']
    class_stats = scan_result['class_stats']
    
    # 生成顏色 (使用seed確保一致性)
    colors = generate_distinct_colors(len(labels), seed=hash(str(sorted(labels))) % 10000)
    
    # 創建類別配置
    class_configs = {}
    
    for i, label in enumerate(labels):
        # 根據標註數量調整置信度閾值
        count = class_stats[label]['count']
        if count > 100:
            conf_threshold = default_conf + 0.1
        elif count > 50:
            conf_threshold = default_conf
        elif count > 10:
            conf_threshold = default_conf - 0.1
        else:
            conf_threshold = default_conf - 0.2
        
        # 確保閾值在合理範圍內
        conf_threshold = max(0.1, min(0.9, conf_threshold))
        
        class_configs[i] = ClassConfig(
            name=label,
            conf_threshold=conf_threshold,
            color=colors[i],
            enabled=True,
            description=f"自動從LabelMe生成，共{count}個標註"
        )
    
    return class_configs


@dataclass
class ClassConfig:
    """類別配置"""
    name: str
    conf_threshold: float = 0.5
    color: Tuple[int, int, int] = (255, 0, 0)
    enabled: bool = True
    description: str = ""


@dataclass
class EnhancedYOLOConfig:
    """增強YOLO配置"""
    
    # 模型配置
    detection_model_path: str = ""  # YOLO12檢測模型
    segmentation_model_path: str = ""  # YOLO11分割模型
    device: str = "auto"
    
    # 推理配置
    img_size: int = 640
    global_conf: float = 0.25
    iou_threshold: float = 0.45
    max_det: int = 1000
    
    # 類別配置
    class_configs: Dict[int, ClassConfig] = field(default_factory=dict)
    labelme_dir: str = ""  # LabelMe標註目錄，用於自動生成類別配置
    auto_generate_classes: bool = False  # 是否從LabelMe自動生成類別
    
    # SAHI配置 - 完整23個參數
    enable_sahi: bool = False
    slice_height: int = 512
    slice_width: int = 512
    overlap_height_ratio: float = 0.2
    overlap_width_ratio: float = 0.2
    
    # SAHI進階參數
    auto_slice_resolution: bool = True
    perform_standard_pred: bool = True
    roi_ratio: Tuple[float, float, float, float] = (0.0, 0.0, 1.0, 1.0)
    
    # SAHI後處理參數
    postprocess_type: str = "GREEDYNMM"  # "GREEDYNMM", "NMM", "NMS"
    postprocess_match_threshold: float = 0.1
    postprocess_class_agnostic: bool = False
    
    # SAHI類別過濾參數
    exclude_classes_by_name: List[str] = field(default_factory=list)
    exclude_classes_by_id: List[int] = field(default_factory=list)
    
    # SAHI輸出控制
    no_standard_prediction: bool = False
    no_sliced_prediction: bool = False
    export_pickle: bool = False
    export_crop: bool = False
    
    # Simple Tool功能整合 - 雙模型協同
    secondary_detection_model_path: str = ""
    secondary_segmentation_model_path: str = ""
    enable_dual_model_consensus: bool = False
    consensus_threshold: float = 0.3
    consensus_iou_threshold: float = 0.3
    
    # Simple Tool功能整合 - 智能檢測過濾
    enable_intelligent_filtering: bool = False
    enable_detection_merge: bool = False
    iou_merge_threshold: float = 0.3
    
    # Simple Tool功能整合 - 選擇性類別保存
    target_classes: Optional[List[int]] = None
    save_all_when_target_found: bool = True
    skip_empty_results: bool = True
    
    # 輸出配置
    save_visualizations: bool = True
    save_predictions: bool = True
    save_statistics: bool = True
    output_format: str = "both"  # detection, segmentation, both
    
    # 轉換配置
    auto_convert_annotations: bool = True
    input_annotation_format: str = "auto"  # auto, labelme, yolo, coco, voc
    
    # 高級功能
    enable_tracking: bool = False
    enable_pose_estimation: bool = False
    enable_classification: bool = False
    enable_batch_processing: bool = True
    
    def __post_init__(self):
        # 如果指定了LabelMe目錄且啟用自動生成，從LabelMe生成類別配置
        if self.auto_generate_classes and self.labelme_dir and os.path.exists(self.labelme_dir):
            try:
                self.class_configs = generate_class_configs_from_labelme(
                    self.labelme_dir, 
                    default_conf=self.global_conf
                )
                logging.info(f"從LabelMe自動生成了 {len(self.class_configs)} 個類別配置")
            except Exception as e:
                logging.warning(f"從LabelMe生成類別配置失敗: {e}")
                self._set_default_classes()
        elif not self.class_configs:
            self._set_default_classes()
    
    def _set_default_classes(self):
        """設置預設類別配置"""
        self.class_configs = {
            0: ClassConfig("裂縫", 0.5, (255, 0, 0), True, "道路表面裂縫"),
            1: ClassConfig("坑洞", 0.4, (0, 255, 0), True, "路面坑洞"),
            2: ClassConfig("人孔蓋", 0.6, (0, 0, 255), True, "下水道人孔蓋"),
            3: ClassConfig("標線", 0.3, (255, 255, 0), True, "道路標線"),
            4: ClassConfig("其他", 0.5, (255, 0, 255), True, "其他基礎設施")
        }
    
    def generate_classes_from_labelme(self, labelme_dir: str, default_conf: float = None) -> Dict[str, Any]:
        """
        手動從LabelMe目錄生成類別配置
        
        Args:
            labelme_dir: LabelMe標註目錄
            default_conf: 預設置信度閾值
        
        Returns:
            生成結果統計
        """
        if default_conf is None:
            default_conf = self.global_conf
        
        try:
            # 掃描LabelMe標註
            scan_result = scan_labelme_annotations(labelme_dir)
            
            # 生成類別配置
            self.class_configs = generate_class_configs_from_labelme(labelme_dir, default_conf)
            
            return {
                'success': True,
                'generated_classes': len(self.class_configs),
                'scan_result': scan_result
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_class_config(self, output_path: str):
        """導出類別配置到YAML文件"""
        config_data = {
            'class_configs': {}
        }
        
        for class_id, class_config in self.class_configs.items():
            config_data['class_configs'][class_id] = {
                'name': class_config.name,
                'conf_threshold': class_config.conf_threshold,
                'color': list(class_config.color),
                'enabled': class_config.enabled,
                'description': class_config.description
            }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
    
    def load_class_config(self, config_path: str):
        """從YAML文件載入類別配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        if 'class_configs' in config_data:
            self.class_configs = {}
            for class_id, class_data in config_data['class_configs'].items():
                self.class_configs[int(class_id)] = ClassConfig(
                    name=class_data['name'],
                    conf_threshold=class_data['conf_threshold'],
                    color=tuple(class_data['color']),
                    enabled=class_data['enabled'],
                    description=class_data.get('description', '')
                )


class EnhancedYOLOInference:
    """增強YOLO推理系統"""
    
    def __init__(self, config: Union[EnhancedYOLOConfig, str, Dict]):
        """初始化推理系統"""
        
        if isinstance(config, str):
            with open(config, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            self.config = EnhancedYOLOConfig(**config_dict)
        elif isinstance(config, dict):
            self.config = EnhancedYOLOConfig(**config)
        else:
            self.config = config
        
        self.logger = logging.getLogger(__name__)
        
        if not ULTRALYTICS_AVAILABLE:
            raise ImportError("Ultralytics YOLO not available. Please install: pip install ultralytics")
        
        # 初始化模型
        self.detection_model = None
        self.segmentation_model = None
        self._load_models()
        
        # 初始化雙模型支持 (Simple Tool功能)
        self.secondary_detection_model = None
        self.secondary_segmentation_model = None
        if self.config.enable_dual_model_consensus:
            self._load_secondary_models()
        
        # 初始化SAHI
        self.sahi_model = None
        if self.config.enable_sahi and SAHI_AVAILABLE:
            self._setup_sahi()
        
        # 初始化轉換器
        self.converter = None
        if self.config.auto_convert_annotations and CONVERTER_AVAILABLE:
            self._setup_converter()
        
        # 統計資料
        self.inference_stats = {
            "total_images": 0,
            "total_detections": 0,
            "processing_time": 0,
            "class_counts": {cls_id: 0 for cls_id in self.config.class_configs.keys()}
        }
    
    def _load_secondary_models(self):
        """載入副模型 (Simple Tool功能)"""
        
        # 載入副檢測模型
        if self.config.secondary_detection_model_path and os.path.exists(self.config.secondary_detection_model_path):
            try:
                self.secondary_detection_model = YOLO(self.config.secondary_detection_model_path)
                self.logger.info(f"載入副檢測模型: {self.config.secondary_detection_model_path}")
            except Exception as e:
                self.logger.error(f"載入副檢測模型失敗: {e}")
        
        # 載入副分割模型
        if self.config.secondary_segmentation_model_path and os.path.exists(self.config.secondary_segmentation_model_path):
            try:
                self.secondary_segmentation_model = YOLO(self.config.secondary_segmentation_model_path)
                self.logger.info(f"載入副分割模型: {self.config.secondary_segmentation_model_path}")
            except Exception as e:
                self.logger.error(f"載入副分割模型失敗: {e}")
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU (Simple Tool功能)"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # 計算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # 計算聯集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0
    
    def _apply_class_thresholds(self, predictions: List[Dict]) -> List[Dict]:
        """應用類別特定閾值 (Simple Tool功能)"""
        filtered_predictions = []

        for pred in predictions:
            cls_id = pred['class_id']
            confidence = pred['confidence']
            
            # 使用class_configs中的閾值，或全局閾值作為後備
            if cls_id in self.config.class_configs:
                threshold = self.config.class_configs[cls_id].conf_threshold
            else:
                threshold = self.config.global_conf

            if confidence >= threshold:
                filtered_predictions.append(pred)

        return filtered_predictions
    
    def _apply_intelligent_filtering(self, predictions: List[Dict]) -> List[Dict]:
        """應用智能檢測過濾 (Simple Tool功能)"""
        if not self.config.enable_intelligent_filtering:
            return predictions
        
        # 獲取類別名稱映射
        class_names = {cls_id: config.name for cls_id, config in self.config.class_configs.items()}
        
        # Step 1: linear_crack vs Alligator_crack (增強型過濾邏輯)
        filtered_predictions = []
        keep_indices = list(range(len(predictions)))

        # 先收集所有 id2 (linear_crack) 和 id3 (Alligator_crack) 的檢測
        linear_predictions = []
        alligator_predictions = []
        
        for i, pred in enumerate(predictions):
            cls_id = pred['class_id']
            name = class_names.get(cls_id, str(cls_id)).split('_')[0].lower()
            
            if cls_id == 2 or name == 'linear':  # linear_crack
                linear_predictions.append((i, pred))
            elif cls_id == 3 or name == 'alligator':  # Alligator_crack
                alligator_predictions.append((i, pred))

        print(f"🔍 Step1 過濾: 發現 {len(linear_predictions)} 個 linear_crack, {len(alligator_predictions)} 個 Alligator_crack")

        # 檢查 linear_crack 和 Alligator_crack 之間的重疊
        for i, (linear_idx, linear_pred) in enumerate(linear_predictions):
            if linear_idx not in keep_indices:
                continue

            linear_box = linear_pred['bbox']
            linear_area = linear_pred['area']
            
            # 計算 linear_crack 的長寬比
            linear_width = linear_box[2] - linear_box[0]
            linear_height = linear_box[3] - linear_box[1]
            linear_aspect_ratio = min(linear_width, linear_height) / max(linear_width, linear_height)

            for j, (alligator_idx, alligator_pred) in enumerate(alligator_predictions):
                if alligator_idx not in keep_indices or alligator_idx <= linear_idx:
                    continue

                alligator_box = alligator_pred['bbox']
                alligator_area = alligator_pred['area']
                iou = self._calculate_iou(linear_box, alligator_box)

                if iou > 0.0:  # 有重疊
                    # 計算面積比 (較小面積 / 較大面積)
                    area_ratio = min(linear_area, alligator_area) / max(linear_area, alligator_area)
                    
                    print(f"  🔍 重疊檢測: IoU={iou:.3f}, 長寬比={linear_aspect_ratio:.3f}, 面積比={area_ratio:.3f}")
                    
                    # 新的判斷邏輯：
                    # 如果長寬比 < 0.8 且面積比 < 0.4，保留 linear_crack
                    # 否則保留 Alligator_crack
                    if linear_aspect_ratio < 0.8 and area_ratio < 0.4:
                        # 保留 linear_crack，移除 Alligator_crack
                        if alligator_idx in keep_indices:
                            keep_indices.remove(alligator_idx)
                            print(f"    ✅ 保留 linear_crack (長寬比={linear_aspect_ratio:.3f} < 0.8, 面積比={area_ratio:.3f} < 0.4)")
                    else:
                        # 保留 Alligator_crack，移除 linear_crack
                        if linear_idx in keep_indices:
                            keep_indices.remove(linear_idx)
                            print(f"    ✅ 保留 Alligator_crack (長寬比={linear_aspect_ratio:.3f} >= 0.8 或面積比={area_ratio:.3f} >= 0.4)")
                            break

        # 保留第一步過濾後的結果
        step1_predictions = [predictions[i] for i in keep_indices]

        # Step 2: linear_crack vs joint (mask 或 IoU 覆蓋 > 0.3 時刪 linear)
        final_keep_indices = list(range(len(step1_predictions)))

        for i, pred_i in enumerate(step1_predictions):
            if i not in final_keep_indices:
                continue

            cls_i = pred_i['class_id']
            name_i = class_names.get(cls_i, str(cls_i)).split('_')[0].lower()

            if name_i == 'linear':
                for j, pred_j in enumerate(step1_predictions):
                    if j == i or j not in final_keep_indices:
                        continue

                    cls_j = pred_j['class_id']
                    name_j = class_names.get(cls_j, str(cls_j)).split('_')[0].lower()

                    if name_j == 'joint':
                        # 如果有 mask，使用 mask 計算覆蓋率
                        if pred_i.get('mask') is not None and pred_j.get('mask') is not None:
                            mask_i = pred_i['mask']
                            mask_j = pred_j['mask']
                            inter = np.logical_and(mask_i, mask_j).sum()
                            total_linear = mask_i.sum()

                            if total_linear > 0 and (inter / total_linear) > 0.3:
                                final_keep_indices.remove(i)
                                break
                        else:
                            # 使用 IoU 近似
                            iou = self._calculate_iou(pred_i['bbox'], pred_j['bbox'])
                            if iou > 0.3:
                                final_keep_indices.remove(i)
                                break

        return [step1_predictions[i] for i in final_keep_indices]
    
    def _merge_detections(self, predictions: List[Dict]) -> List[Dict]:
        """合併相同類別的重疊檢測 (Simple Tool功能)"""
        if not predictions or not self.config.enable_detection_merge:
            return predictions

        # 按類別分組
        class_groups = defaultdict(list)
        for i, pred in enumerate(predictions):
            class_groups[pred['class_id']].append((i, pred))

        merged_predictions = []
        used_indices = set()

        # 對每個類別獨立處理
        for cls_id, group in class_groups.items():
            indices = [item[0] for item in group]
            preds = [item[1] for item in group]

            i = 0
            while i < len(indices):
                idx_i = indices[i]
                if idx_i in used_indices:
                    i += 1
                    continue

                pred_i = preds[i]
                merged_box = pred_i['bbox'].copy()
                merged_mask = pred_i.get('mask')
                best_conf = pred_i['confidence']

                used_indices.add(idx_i)

                # 找出與當前檢測重疊的其他檢測
                for j in range(i + 1, len(indices)):
                    idx_j = indices[j]
                    if idx_j in used_indices:
                        continue

                    pred_j = preds[j]
                    iou = self._calculate_iou(merged_box, pred_j['bbox'])

                    if iou > self.config.iou_merge_threshold:
                        used_indices.add(idx_j)

                        # 合併 bbox
                        merged_box[0] = min(merged_box[0], pred_j['bbox'][0])
                        merged_box[1] = min(merged_box[1], pred_j['bbox'][1])
                        merged_box[2] = max(merged_box[2], pred_j['bbox'][2])
                        merged_box[3] = max(merged_box[3], pred_j['bbox'][3])

                        # 合併置信度
                        best_conf = max(best_conf, pred_j['confidence'])

                        # 合併 mask
                        if merged_mask is not None and pred_j.get('mask') is not None:
                            merged_mask = np.logical_or(merged_mask, pred_j['mask'])
                        elif merged_mask is None and pred_j.get('mask') is not None:
                            merged_mask = pred_j['mask'].copy()

                # 添加合併後的檢測
                merged_pred = {
                    'class_id': cls_id,
                    'confidence': best_conf,
                    'bbox': merged_box,
                    'area': (merged_box[2] - merged_box[0]) * (merged_box[3] - merged_box[1]),
                }
                
                # 保留其他屬性
                for key in pred_i:
                    if key not in merged_pred:
                        merged_pred[key] = pred_i[key]
                
                if merged_mask is not None:
                    merged_pred['mask'] = merged_mask
                    
                merged_predictions.append(merged_pred)

                i += 1

        return merged_predictions
    
    def _filter_by_target_classes(self, predictions: List[Dict]) -> List[Dict]:
        """選擇性類別保存 (Simple Tool功能)"""
        if not self.config.target_classes:
            return predictions
        
        # 過濾指定類別
        filtered_predictions = []
        for pred in predictions:
            if pred['class_id'] in self.config.target_classes:
                filtered_predictions.append(pred)
        
        # 如果沒有找到目標類別且設置了save_all_when_target_found=False，返回空結果
        if not filtered_predictions and not self.config.save_all_when_target_found:
            return []
        
        # 如果找到目標類別且save_all_when_target_found=True，返回所有結果
        if filtered_predictions and self.config.save_all_when_target_found:
            return predictions
        
        # 如果沒有找到目標類別但save_all_when_target_found=True，根據skip_empty_results決定
        if not filtered_predictions and self.config.save_all_when_target_found:
            return [] if self.config.skip_empty_results else predictions
        
        return filtered_predictions
    
    def _apply_simple_tool_processing(self, predictions: List[Dict]) -> List[Dict]:
        """應用Simple Tool的所有處理步驟"""
        if not predictions:
            return predictions
        
        # 1. 應用類別特定閾值
        predictions = self._apply_class_thresholds(predictions)
        
        # 2. 應用智能檢測過濾
        predictions = self._apply_intelligent_filtering(predictions)
        
        # 3. 檢測合併
        predictions = self._merge_detections(predictions)
        
        # 4. 選擇性類別保存
        predictions = self._filter_by_target_classes(predictions)
        
        return predictions
    
    def _load_gt_annotations(self, annotation_path: str, image_shape: Tuple[int, int, int]) -> List[Dict]:
        """
        改進的GT標註載入方法，增強調試和錯誤處理
        
        Args:
            annotation_path: 標註文件路徑
            image_shape: 圖像形狀 (H, W, C)
        
        Returns:
            GT標註列表
        """
        # 基本驗證
        if not annotation_path:
            self.logger.warning("GT標註路徑為空")
            return []
        
        if not os.path.exists(annotation_path):
            self.logger.warning(f"GT標註文件不存在: {annotation_path}")
            return []
        
        # 圖像尺寸
        h, w = image_shape[:2]
        gt_annotations = []
        
        self.logger.info(f"🔍 開始載入GT標註: {annotation_path}")
        self.logger.info(f"📐 圖像尺寸: {w}x{h}")
        
        try:
            file_ext = Path(annotation_path).suffix.lower()
            self.logger.info(f"📄 文件類型: {file_ext}")
            
            if file_ext == '.json':  # LabelMe格式
                self.logger.info("🏷️  處理LabelMe JSON格式")
                
                with open(annotation_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 詳細日誌
                shapes = data.get('shapes', [])
                self.logger.info(f"📊 JSON包含 {len(shapes)} 個shapes")
                
                if 'imageWidth' in data and 'imageHeight' in data:
                    json_w, json_h = data['imageWidth'], data['imageHeight']
                    self.logger.info(f"📐 JSON中的圖像尺寸: {json_w}x{json_h}")
                    
                    if json_w != w or json_h != h:
                        self.logger.warning(f"⚠️  圖像尺寸不匹配! 實際: {w}x{h}, JSON: {json_w}x{json_h}")
                
                # 處理每個shape
                for i, shape in enumerate(shapes):
                    shape_type = shape.get('shape_type', '')
                    class_name = shape.get('label', '')
                    points = shape.get('points', [])
                    
                    self.logger.debug(f"   Shape {i+1}: {class_name} ({shape_type}) - {len(points)} 個點")
                    self.logger.debug(f"     原始點數據: {points}")
                    
                    bbox = None
                    
                    # 處理矩形
                    if shape_type == 'rectangle' and len(points) >= 2:
                        try:
                            x1, y1 = points[0]
                            x2, y2 = points[1]
                            
                            # 確保坐標正確
                            x1, x2 = min(x1, x2), max(x1, x2)
                            y1, y2 = min(y1, y2), max(y1, y2)
                            
                            # 邊界檢查
                            x1 = max(0, min(x1, w-1))
                            y1 = max(0, min(y1, h-1))
                            x2 = max(0, min(x2, w-1))
                            y2 = max(0, min(y2, h-1))
                            
                            bbox = [x1, y1, x2, y2]
                            self.logger.debug(f"     計算矩形邊界框: {bbox}")
                            
                        except (ValueError, IndexError, TypeError) as e:
                            self.logger.error(f"     ❌ 矩形點解析錯誤: {e}")
                            continue
                    
                    # 處理多邊形
                    elif shape_type == 'polygon' and len(points) >= 3:
                        try:
                            # 判斷點格式
                            if isinstance(points[0], list):
                                # 格式: [[x1,y1], [x2,y2], ...]
                                xs = [p[0] for p in points]
                                ys = [p[1] for p in points]
                            else:
                                # 格式: [x1, y1, x2, y2, ...]
                                xs = points[::2]
                                ys = points[1::2]
                            
                            if xs and ys:
                                x1, x2 = min(xs), max(xs)
                                y1, y2 = min(ys), max(ys)
                                
                                # 邊界檢查
                                x1 = max(0, min(x1, w-1))
                                y1 = max(0, min(y1, h-1))
                                x2 = max(0, min(x2, w-1))
                                y2 = max(0, min(y2, h-1))
                                
                                bbox = [x1, y1, x2, y2]
                                self.logger.debug(f"     計算多邊形邊界框: {bbox}")
                            
                        except (ValueError, IndexError, TypeError) as e:
                            self.logger.error(f"     ❌ 多邊形點解析錯誤: {e}")
                            continue
                    
                    else:
                        self.logger.warning(f"     ⚠️  不支持的形狀類型或點數不足: {shape_type}, {len(points)} 個點")
                        continue
                    
                    # 如果成功計算出邊界框
                    if bbox and bbox[2] > bbox[0] and bbox[3] > bbox[1]:  # 確保有效邊界框
                        # 尋找類別ID
                        class_id = self._find_class_id(class_name)
                        
                        if class_id >= 0:
                            annotation = {
                                'id': i,
                                'class_id': class_id,
                                'class_name': class_name,
                                'bbox': bbox,
                                'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                                'shape_type': shape_type,
                                'confidence': 1.0  # GT標註置信度為1
                            }
                            
                            gt_annotations.append(annotation)
                            self.logger.info(f"     ✅ 成功加入GT: {class_name} (ID: {class_id}) - {bbox}")
                        else:
                            self.logger.warning(f"     ⚠️  找不到類別: '{class_name}'")
                    else:
                        self.logger.warning(f"     ❌ 無效邊界框: {bbox}")
            
            elif file_ext == '.txt':  # YOLO格式
                self.logger.info("🎯 處理YOLO TXT格式")
                
                with open(annotation_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                self.logger.info(f"📊 TXT包含 {len(lines)} 行")
                
                for i, line in enumerate(lines):
                    line = line.strip()
                    if not line:
                        continue
                    
                    self.logger.debug(f"   行 {i+1}: {line}")
                    
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            class_id = int(parts[0])
                            cx, cy, bw, bh = map(float, parts[1:5])
                            
                            # 轉換為絕對坐標
                            x1 = (cx - bw/2) * w
                            y1 = (cy - bh/2) * h
                            x2 = (cx + bw/2) * w
                            y2 = (cy + bh/2) * h
                            
                            # 邊界檢查
                            x1 = max(0, min(x1, w-1))
                            y1 = max(0, min(y1, h-1))
                            x2 = max(0, min(x2, w-1))
                            y2 = max(0, min(y2, h-1))
                            
                            bbox = [x1, y1, x2, y2]
                            
                            # 獲取類別名稱
                            class_name = self._get_class_name(class_id)
                            
                            annotation = {
                                'id': i,
                                'class_id': class_id,
                                'class_name': class_name,
                                'bbox': bbox,
                                'area': (x2 - x1) * (y2 - y1),
                                'confidence': 1.0
                            }
                            
                            gt_annotations.append(annotation)
                            self.logger.info(f"     ✅ 成功加入YOLO GT: {class_name} (ID: {class_id}) - [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
                            
                        except (ValueError, IndexError) as e:
                            self.logger.error(f"     ❌ YOLO格式解析錯誤 行{i+1}: {e}")
                            continue
                    else:
                        self.logger.warning(f"     ⚠️  YOLO格式錯誤 行{i+1}: 需要至少5個值，得到 {len(parts)} 個")
            
            else:
                self.logger.error(f"❌ 不支持的標註格式: {file_ext}")
                return []
        
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON解析錯誤: {e}")
            return []
        except UnicodeDecodeError as e:
            self.logger.error(f"❌ 文件編碼錯誤: {e}")
            return []
        except Exception as e:
            self.logger.error(f"❌ GT載入失敗: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())
            return []
        
        # 最終結果
        self.logger.info(f"🎉 GT載入完成: 成功載入 {len(gt_annotations)} 個標註")
        
        if gt_annotations:
            self.logger.info("📋 GT標註摘要:")
            class_counts = {}
            for gt in gt_annotations:
                class_name = gt['class_name']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            for class_name, count in class_counts.items():
                self.logger.info(f"   - {class_name}: {count} 個")
        else:
            self.logger.warning("⚠️  未載入任何GT標註，請檢查:")
            self.logger.warning("   1. 標註文件格式是否正確")
            self.logger.warning("   2. 類別配置是否包含標註中的類別")
            self.logger.warning("   3. 標註坐標是否有效")
        
        return gt_annotations
    
    def _find_class_id(self, class_name: str) -> int:
        """尋找類別ID，包含精確匹配和模糊匹配"""
        if not class_name:
            return -1
        
        # 方法1: 精確匹配
        for cid, config in self.config.class_configs.items():
            if config.name == class_name:
                self.logger.debug(f"精確匹配: '{class_name}' -> ID: {cid}")
                return cid
        
        # 方法2: 模糊匹配 (忽略大小寫和空格)
        normalized_input = class_name.lower().replace(' ', '').replace('_', '').replace('-', '')
        for cid, config in self.config.class_configs.items():
            normalized_config = config.name.lower().replace(' ', '').replace('_', '').replace('-', '')
            if normalized_config == normalized_input:
                self.logger.debug(f"模糊匹配: '{class_name}' -> '{config.name}' (ID: {cid})")
                return cid
        
        # 方法3: 智能映射匹配
        LABEL_ALIASES = {
            'manhole': 'manhole_人孔蓋或排水溝',
            'potholes': 'potholes_坑洞', 
            'linear_crack': 'linear_crack_裂縫',
            'dirt': 'dirt_污垢',
            'expansion_joint': 'expansion_joint_伸縮縫',
            'joint': 'joint_路面接縫',
            'deformation': 'deformation_變形',
            'patch': 'patch_補綻',
            'Alligator_crack': 'Alligator_crack_龜裂',
            'lane_lline_linear': 'lane_line_linear_白綫裂縫',
            'lane_line_linear': 'lane_line_linear_白綫裂縫'
        }
        
        if class_name in LABEL_ALIASES:
            target_name = LABEL_ALIASES[class_name]
            for cid, config in self.config.class_configs.items():
                if config.name == target_name:
                    self.logger.info(f"🎯 智能映射匹配: '{class_name}' -> '{target_name}' (ID: {cid})")
                    return cid
        
        # 方法4: 包含匹配
        for cid, config in self.config.class_configs.items():
            if class_name.lower() in config.name.lower() or config.name.lower() in class_name.lower():
                self.logger.debug(f"包含匹配: '{class_name}' -> '{config.name}' (ID: {cid})")
                return cid
        
        # 方法5: 如果沒有配置類別，創建新類別
        if not self.config.class_configs:
            # 使用哈希創建穩定的ID
            class_id = hash(class_name) % 1000
            self.logger.info(f"創建新類別: '{class_name}' -> ID: {class_id}")
            return class_id
        
        # 記錄可用類別
        available_classes = [config.name for config in self.config.class_configs.values()]
        self.logger.warning(f"找不到類別匹配: '{class_name}', 可用類別: {available_classes}")
        return -1
    
    def _get_class_name(self, class_id: int) -> str:
        """根據類別ID獲取類別名稱"""
        if class_id in self.config.class_configs:
            return self.config.class_configs[class_id].name
        else:
            return f'class_{class_id}'
    
    def _calculate_metrics(self, predictions: List[Dict], gt_annotations: List[Dict]) -> Dict[str, Any]:
        """計算評估指標 (Simple Tool功能)"""
        if not predictions or not gt_annotations or not SKLEARN_AVAILABLE:
            return {}
        
        # 準備數據
        pred_boxes = [pred['bbox'] for pred in predictions]
        pred_classes = [pred['class_id'] for pred in predictions]
        pred_scores = [pred['confidence'] for pred in predictions]
        
        gt_boxes = [gt['bbox'] for gt in gt_annotations]
        gt_classes = [gt['class_id'] for gt in gt_annotations]
        
        # 計算IoU匹配
        matches = self._match_predictions_to_gt(pred_boxes, pred_classes, pred_scores, 
                                              gt_boxes, gt_classes, iou_threshold=0.5)
        
        # 提取匹配結果
        tp = sum(1 for match in matches if match['matched'])
        fp = len(predictions) - tp
        fn = len(gt_annotations) - tp
        
        # 計算基本指標
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        # 按類別計算指標 - 使用與圖像級別相同的方法
        class_metrics = {}
        all_classes = set(pred_classes + gt_classes)
        
        for class_id in all_classes:
            class_name = self.config.class_configs.get(class_id, ClassConfig(f'class_{class_id}')).name
            
            # 收集該類別的預測和GT
            class_predictions = [pred for pred in predictions if pred['class_id'] == class_id]
            class_gt = [gt for gt in gt_annotations if gt['class_id'] == class_id]
            
            # 使用與圖像級別相同的計算邏輯
            if not class_predictions and not class_gt:
                continue  # 跳過沒有預測也沒有GT的類別
                
            if not class_predictions:
                # 沒有預測，全部為FN
                class_tp, class_fp, class_fn = 0, 0, len(class_gt)
            elif not class_gt:
                # 沒有GT，全部為FP
                class_tp, class_fp, class_fn = 0, len(class_predictions), 0
            else:
                # 使用改進的匹配算法計算（與圖像級別相同）
                class_pred_boxes = [pred['bbox'] for pred in class_predictions]
                class_pred_classes = [pred['class_id'] for pred in class_predictions]
                class_pred_scores = [pred['confidence'] for pred in class_predictions]
                
                class_gt_boxes = [gt['bbox'] for gt in class_gt]
                class_gt_classes = [gt['class_id'] for gt in class_gt]
                
                # 使用基本匹配算法
                class_matches = self._match_predictions_to_gt(
                    class_pred_boxes, class_pred_classes, class_pred_scores,
                    class_gt_boxes, class_gt_classes, iou_threshold=0.5
                )
                
                # 計算TP, FP, FN
                class_tp = sum(1 for match in class_matches if match['matched'])
                class_fp = len(class_predictions) - class_tp
                class_fn = len(class_gt) - class_tp
            
            # 計算指標
            class_precision = class_tp / (class_tp + class_fp) if (class_tp + class_fp) > 0 else 0.0
            class_recall = class_tp / (class_tp + class_fn) if (class_tp + class_fn) > 0 else 0.0
            class_f1 = 2 * class_precision * class_recall / (class_precision + class_recall) if (class_precision + class_recall) > 0 else 0.0
            
            class_metrics[class_name] = {
                'precision': class_precision,
                'recall': class_recall,
                'f1_score': class_f1,
                'tp': class_tp,
                'fp': class_fp,
                'fn': class_fn,
                'gt_count': len(class_gt),
                'pred_count': len(class_predictions)
            }
        
        return {
            'overall': {
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'total_predictions': len(predictions),
                'total_ground_truth': len(gt_annotations)
            },
            'per_class': class_metrics,
            'matches': matches
        }
    
    def _match_predictions_to_gt(self, pred_boxes: List[List[float]], pred_classes: List[int], pred_scores: List[float],
                                gt_boxes: List[List[float]], gt_classes: List[int], iou_threshold: float = 0.5) -> List[Dict]:
        """匹配預測到GT標註"""
        matches = []
        used_gt = set()
        
        # 按置信度排序
        sorted_indices = sorted(range(len(pred_scores)), key=lambda i: pred_scores[i], reverse=True)
        
        for pred_idx in sorted_indices:
            pred_box = pred_boxes[pred_idx]
            pred_class = pred_classes[pred_idx]
            pred_score = pred_scores[pred_idx]
            
            best_iou = 0.0
            best_gt_idx = -1
            
            for gt_idx, (gt_box, gt_class) in enumerate(zip(gt_boxes, gt_classes)):
                if gt_idx in used_gt or gt_class != pred_class:
                    continue
                
                iou = self._calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            matched = best_iou >= iou_threshold and best_gt_idx >= 0
            if matched:
                used_gt.add(best_gt_idx)
            
            matches.append({
                'pred_idx': pred_idx,
                'gt_idx': best_gt_idx if matched else -1,
                'iou': best_iou,
                'matched': matched,
                'class_id': pred_class,
                'confidence': pred_score
            })
        
        return matches
    
    def _image_to_base64(self, image_path: str) -> Optional[str]:
        """圖像轉base64 (Simple Tool功能)"""
        try:
            import base64
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            self.logger.error(f"轉換base64失敗 {image_path}: {e}")
            return None
    
    def _mask_to_polygon(self, mask: np.ndarray) -> List[List[float]]:
        """將 mask 轉換為多邊形點列表 (Simple Tool功能)"""
        try:
            # 使用 cv2.findContours 找到輪廓
            if mask.dtype != np.uint8:
                mask_uint8 = (mask * 255).astype(np.uint8)
            else:
                mask_uint8 = mask.astype(np.uint8)

            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return []

            # 取最大的輪廓
            largest_contour = max(contours, key=cv2.contourArea)

            # 簡化輪廓，減少點數
            epsilon = 0.005 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

            # 轉換為點列表
            points = []
            for point in simplified_contour:
                x, y = point[0]
                points.append([float(x), float(y)])

            return points

        except Exception as e:
            self.logger.warning(f"Mask 轉換多邊形失敗: {e}, 使用 bbox 替代")
            return []
    
    def _generate_mask_from_bbox(self, bbox: List[float], img_width: int, img_height: int) -> List[List[float]]:
        """從邊界框生成mask多邊形點 (Simple Tool功能)"""
        x1, y1, x2, y2 = bbox

        # 應用膨脹
        expansion = 5  # 預設擴展像素
        x1 = max(0, x1 - expansion)
        y1 = max(0, y1 - expansion)
        x2 = min(img_width, x2 + expansion)
        y2 = min(img_height, y2 + expansion)

        # 生成矩形mask的四個角點
        points = [
            [float(x1), float(y1)],  # 左上
            [float(x2), float(y1)],  # 右上
            [float(x2), float(y2)],  # 右下
            [float(x1), float(y2)]   # 左下
        ]

        return points
    
    def _load_models(self):
        """載入YOLO模型"""
        
        # 載入檢測模型
        if self.config.detection_model_path and os.path.exists(self.config.detection_model_path):
            try:
                self.detection_model = YOLO(self.config.detection_model_path)
                self.logger.info(f"載入檢測模型: {self.config.detection_model_path}")
            except Exception as e:
                self.logger.error(f"載入檢測模型失敗: {e}")
        
        # 載入分割模型
        if self.config.segmentation_model_path and os.path.exists(self.config.segmentation_model_path):
            try:
                self.segmentation_model = YOLO(self.config.segmentation_model_path)
                self.logger.info(f"載入分割模型: {self.config.segmentation_model_path}")
            except Exception as e:
                self.logger.error(f"載入分割模型失敗: {e}")
        
        # 至少需要一個模型
        if not self.detection_model and not self.segmentation_model:
            raise ValueError("至少需要指定一個有效的模型路徑")
    
    def _setup_sahi(self):
        """設置SAHI"""
        if not SAHI_AVAILABLE:
            self.logger.warning("SAHI不可用，跳過SAHI設置")
            return
        
        # 使用檢測模型或分割模型設置SAHI
        model_path = self.config.detection_model_path or self.config.segmentation_model_path
        
        try:
            self.sahi_model = AutoDetectionModel.from_pretrained(
                model_type='ultralytics',
                model_path=model_path,
                confidence_threshold=self.config.global_conf,
                device=self.config.device
            )
            self.logger.info("SAHI模型初始化完成")
        except Exception as e:
            self.logger.error(f"SAHI設置失敗: {e}")
            self.config.enable_sahi = False
    
    def _setup_converter(self):
        """設置標註轉換器"""
        if not CONVERTER_AVAILABLE:
            self.logger.warning("標註轉換器不可用")
            return
        
        self.converter = AnnotationConverterV2(
            input_dir="",  # 動態設置
            output_dir="",  # 動態設置
            input_format="auto",
            output_format="yolo"
        )
        self.format_detector = FormatDetector()
    
    def predict_single_image(self, 
                           image_path: str,
                           annotation_path: str = None,
                           output_dir: str = None,
                           task_type: str = "both") -> Dict[str, Any]:
        """
        單張圖像推理
        
        Args:
            image_path: 圖像路徑
            annotation_path: 標註路徑（可選）
            output_dir: 輸出目錄
            task_type: 任務類型 ("detection", "segmentation", "both")
        
        Returns:
            推理結果字典
        """
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"圖像不存在: {image_path}")
        
        # 載入圖像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"無法載入圖像: {image_path}")
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        results = {}
        
        # 處理標註轉換和載入GT
        gt_annotations = []
        if annotation_path and os.path.exists(annotation_path):
            converted_annotations = self._convert_annotations(annotation_path, image_path)
            results['converted_annotations'] = converted_annotations
            
            # 載入GT標註用於Metrics計算
            gt_annotations = self._load_gt_annotations(annotation_path, image.shape)
            results['ground_truth'] = gt_annotations
        
        # 檢測任務
        if task_type in ["detection", "both"] and self.detection_model:
            detection_results = self._run_detection(image_path, image)
            results['detection'] = detection_results
        
        # 分割任務
        if task_type in ["segmentation", "both"] and self.segmentation_model:
            segmentation_results = self._run_segmentation(image_path, image)
            results['segmentation'] = segmentation_results
        
        # SAHI推理
        if self.config.enable_sahi:
            sahi_results = self._run_sahi(image_path)
            results['sahi'] = sahi_results
        
        # 計算Metrics(如果有GT)
        if gt_annotations:
            # 獲取預測結果用於Metrics計算
            all_predictions = []
            if 'detection' in results and results['detection'].get('detections'):
                all_predictions.extend(results['detection']['detections'])
            if 'segmentation' in results and results['segmentation'].get('segments'):
                all_predictions.extend(results['segmentation']['segments'])
            
            if all_predictions:
                metrics = self._calculate_metrics(all_predictions, gt_annotations)
                results['metrics'] = metrics
                self.logger.info(f"Metrics: P={metrics.get('overall', {}).get('precision', 0):.3f}, R={metrics.get('overall', {}).get('recall', 0):.3f}, F1={metrics.get('overall', {}).get('f1_score', 0):.3f}")
        
        # 保存結果
        if output_dir:
            self._save_results(results, image_path, output_dir, annotation_path)
        
        # 更新統計
        self._update_stats(results)
        
        return results
    
    def _run_detection(self, image_path: str, image: np.ndarray) -> Dict[str, Any]:
        """運行檢測"""
        
        results = self.detection_model(
            image_path,
            conf=self.config.global_conf,
            iou=self.config.iou_threshold,
            imgsz=self.config.img_size,
            max_det=self.config.max_det,
            device=self.config.device
        )[0]
        
        detections = []
        
        if results.boxes is not None:
            boxes = results.boxes.xyxy.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            
            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                if cls_id in self.config.class_configs:
                    class_config = self.config.class_configs[cls_id]
                    
                    # 檢查類別特定閾值
                    if conf >= class_config.conf_threshold and class_config.enabled:
                        detection = {
                            'id': i,
                            'class_id': int(cls_id),
                            'class_name': class_config.name,
                            'confidence': float(conf),
                            'bbox': box.tolist(),
                            'area': float((box[2] - box[0]) * (box[3] - box[1])),
                            'mask': None  # 檢測模式下沒有mask
                        }
                        detections.append(detection)
        
        # 應用Simple Tool的處理步驟
        detections = self._apply_simple_tool_processing(detections)
        
        return {
            'detections': detections,
            'image_shape': image.shape,
            'model_info': {
                'name': self.detection_model.model_name if hasattr(self.detection_model, 'model_name') else 'YOLO',
                'task': 'detection'
            }
        }
    
    def _run_segmentation(self, image_path: str, image: np.ndarray) -> Dict[str, Any]:
        """運行分割"""
        
        results = self.segmentation_model(
            image_path,
            conf=self.config.global_conf,
            iou=self.config.iou_threshold,
            imgsz=self.config.img_size,
            max_det=self.config.max_det,
            device=self.config.device
        )[0]
        
        segments = []
        
        if results.masks is not None:
            masks = results.masks.data.cpu().numpy()
            boxes = results.boxes.xyxy.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            
            for i, (mask, box, conf, cls_id) in enumerate(zip(masks, boxes, confidences, class_ids)):
                if cls_id in self.config.class_configs:
                    class_config = self.config.class_configs[cls_id]
                    
                    if conf >= class_config.conf_threshold and class_config.enabled:
                        # 計算遮罩面積
                        mask_area = np.sum(mask)
                        
                        segment = {
                            'id': i,
                            'class_id': int(cls_id),
                            'class_name': class_config.name,
                            'confidence': float(conf),
                            'bbox': box.tolist(),
                            'mask': mask.astype(np.uint8),
                            'mask_area': float(mask_area),
                            'area': float(mask_area),  # 統一area字段供Simple Tool使用
                            'bbox_area': float((box[2] - box[0]) * (box[3] - box[1]))
                        }
                        segments.append(segment)
        
        # 應用Simple Tool的處理步驟
        segments = self._apply_simple_tool_processing(segments)
        
        return {
            'segments': segments,
            'image_shape': image.shape,
            'model_info': {
                'name': self.segmentation_model.model_name if hasattr(self.segmentation_model, 'model_name') else 'YOLO',
                'task': 'segmentation'
            }
        }
    
    def _run_sahi(self, image_path: str) -> Dict[str, Any]:
        """運行SAHI切片推理"""
        
        if not self.sahi_model:
            return {}
        
        try:
            result = get_sliced_prediction(
                image=image_path,
                detection_model=self.sahi_model,
                slice_height=self.config.slice_height,
                slice_width=self.config.slice_width,
                overlap_height_ratio=self.config.overlap_height_ratio,
                overlap_width_ratio=self.config.overlap_width_ratio
            )
            
            sahi_detections = []
            for detection in result.object_prediction_list:
                sahi_detection = {
                    'class_id': detection.category.id,
                    'class_name': detection.category.name,
                    'confidence': detection.score.value,
                    'bbox': detection.bbox.to_xyxy(),
                    'area': detection.bbox.area
                }
                sahi_detections.append(sahi_detection)
            
            return {
                'sahi_detections': sahi_detections,
                'slice_info': {
                    'slice_height': self.config.slice_height,
                    'slice_width': self.config.slice_width,
                    'overlap_height_ratio': self.config.overlap_height_ratio,
                    'overlap_width_ratio': self.config.overlap_width_ratio
                }
            }
            
        except Exception as e:
            self.logger.error(f"SAHI推理失敗: {e}")
            return {}
    
    def _convert_annotations(self, annotation_path: str, image_path: str) -> Dict[str, Any]:
        """轉換標註格式"""
        
        if not self.converter:
            return {}
        
        try:
            # 檢測標註格式
            detected_format = self.format_detector.detect_format(str(Path(annotation_path).parent))
            
            # 創建臨時目錄進行轉換
            temp_dir = Path("./temp_conversion")
            temp_dir.mkdir(exist_ok=True)
            
            # 複製文件到臨時目錄
            temp_annotation = temp_dir / Path(annotation_path).name
            temp_image = temp_dir / Path(image_path).name
            
            shutil.copy2(annotation_path, temp_annotation)
            shutil.copy2(image_path, temp_image)
            
            # 設置轉換器
            self.converter.input_dir = temp_dir
            self.converter.output_dir = temp_dir / "converted"
            self.converter.input_format = detected_format
            
            # 執行轉換
            result = self.converter.run()
            
            # 讀取轉換結果
            converted_data = {}
            converted_dir = Path(self.converter.output_dir)
            
            if converted_dir.exists():
                for converted_file in converted_dir.glob("*"):
                    if converted_file.suffix in ['.txt', '.json', '.xml']:
                        with open(converted_file, 'r', encoding='utf-8') as f:
                            converted_data[converted_file.name] = f.read()
            
            # 清理臨時目錄
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            return {
                'original_format': detected_format,
                'converted_format': 'yolo',
                'conversion_result': result,
                'converted_data': converted_data
            }
            
        except Exception as e:
            self.logger.error(f"標註轉換失敗: {e}")
            return {}
    
    def _save_results(self, results: Dict[str, Any], image_path: str, output_dir: str, annotation_path: str = None):
        """保存推理結果
        
        Args:
            results: 推理結果字典
            image_path: 圖像路徑
            output_dir: 輸出目錄
            annotation_path: 標註文件路徑(可選)
        """
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        image_name = Path(image_path).stem
        
        # 保存JSON結果
        if self.config.save_predictions:
            json_path = output_path / f"{image_name}_results.json"
            
            # 處理numpy數組序列化
            serializable_results = self._make_serializable(results)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        # 保存可視化結果 (新的三圖格式: 原圖+GT+預測)
        if self.config.save_visualizations:
            self._save_three_panel_visualization(results, image_path, output_path, image_name, annotation_path)
    
    def _make_serializable(self, obj):
        """將對象轉換為JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(v) for v in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        else:
            return obj
    
    def _save_visualizations(self, results: Dict[str, Any], image_path: str, 
                           output_path: Path, image_name: str):
        """保存可視化結果"""
        
        # 載入原始圖像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 創建可視化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'YOLO推理結果: {image_name}', fontsize=16)
        
        # 原始圖像
        axes[0, 0].imshow(image_rgb)
        axes[0, 0].set_title('原始圖像')
        axes[0, 0].axis('off')
        
        # 檢測結果
        if 'detection' in results:
            det_img = image_rgb.copy()
            self._draw_detections(det_img, results['detection']['detections'])
            axes[0, 1].imshow(det_img)
            axes[0, 1].set_title(f"檢測結果 ({len(results['detection']['detections'])} 個物件)")
            axes[0, 1].axis('off')
        else:
            axes[0, 1].axis('off')
        
        # 分割結果
        if 'segmentation' in results:
            seg_img = image_rgb.copy()
            self._draw_segmentations(seg_img, results['segmentation']['segments'])
            axes[1, 0].imshow(seg_img)
            axes[1, 0].set_title(f"分割結果 ({len(results['segmentation']['segments'])} 個區域)")
            axes[1, 0].axis('off')
        else:
            axes[1, 0].axis('off')
        
        # 統計圖表
        self._draw_statistics(axes[1, 1], results)
        
        plt.tight_layout()
        viz_path = output_path / f"{image_name}_visualization.png"
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _draw_detections(self, image: np.ndarray, detections: List[Dict]):
        """繪製檢測結果 (備用方法，現在由_draw_predictions統一處理)"""
        self._draw_predictions(image, detections)
    
    def _draw_segmentations(self, image: np.ndarray, segments: List[Dict]):
        """繪製分割結果 (備用方法，現在由_draw_predictions統一處理)"""
        self._draw_predictions(image, segments)
    
    def _draw_ground_truth(self, image: np.ndarray, gt_annotations: List[Dict]):
        """繪製Ground Truth標註"""
        for gt in gt_annotations:
            bbox = gt['bbox']
            class_name = gt['class_name']
            class_id = gt['class_id']
            
            # 使用與預測相同的類別顏色，但稍作區分
            if class_id in self.config.class_configs:
                base_color = self.config.class_configs[class_id].color
                # GT使用稍暗的版本以區分預測
                color = tuple(max(0, min(255, int(c * 0.8))) for c in base_color)
            else:
                # 如果沒有配置，使用預設綠色
                color = (0, 255, 0)  # 綠色
            
            # 繪製邊界框 (更粗的線條)
            cv2.rectangle(image, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         color, 3)  # 更粗的線條用於GT
            
            # 繪製標籤
            label = f"GT: {class_name}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 標籤背景
            cv2.rectangle(image,
                         (int(bbox[0]), int(bbox[1]) - label_size[1] - 10),
                         (int(bbox[0]) + label_size[0] + 10, int(bbox[1])),
                         color, -1)
            
            # 標籤文字
            cv2.putText(image, label,
                       (int(bbox[0]) + 5, int(bbox[1]) - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    def _draw_predictions(self, image: np.ndarray, predictions: List[Dict]):
        """繪製預測結果 (統一處理檢測和分割)"""
        for pred in predictions:
            bbox = pred['bbox']
            class_id = pred['class_id']
            conf = pred['confidence']
            class_name = pred['class_name']
            
            # 獲取類別顏色
            if class_id in self.config.class_configs:
                color = self.config.class_configs[class_id].color
            else:
                color = (255, 0, 0)  # 預設紅色
            
            # 繪製邊界框
            cv2.rectangle(image, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         color, 2)
            
            # 繪製遮罩 (如果有)
            if pred.get('mask') is not None:
                mask = pred['mask']
                if mask.shape[:2] != image.shape[:2]:
                    mask = cv2.resize(mask.astype(np.uint8), (image.shape[1], image.shape[0]))
                
                # 應用半透明遮罩
                mask_colored = np.zeros_like(image)
                mask_colored[mask > 0] = color
                alpha = 0.3
                image[mask > 0] = image[mask > 0] * (1 - alpha) + mask_colored[mask > 0] * alpha
            
            # 繪製標籤
            label = f"{class_name}: {conf:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            # 標籤背景
            cv2.rectangle(image,
                         (int(bbox[0]), int(bbox[1]) - label_size[1] - 8),
                         (int(bbox[0]) + label_size[0] + 8, int(bbox[1])),
                         color, -1)
            
            # 標籤文字
            cv2.putText(image, label,
                       (int(bbox[0]) + 4, int(bbox[1]) - 4),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
    
    def _save_three_panel_visualization(self, results: Dict[str, Any], image_path: str, 
                                       output_path: Path, image_name: str, annotation_path: str = None):
        """保存三面板可視化: 原圖+GT+預測結果"""
        
        # 載入原始圖像
        image = cv2.imread(image_path)
        if image is None:
            self.logger.error(f"無法載入圖像: {image_path}")
            return
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        h, w = image.shape[:2]
        
        # 設置中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'DejaVu Sans', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 創建三面板圖形
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 面板1: 原始圖像
        axes[0].imshow(image_rgb)
        axes[0].set_title('原始圖像', fontsize=14, fontweight='bold')
        axes[0].axis('off')
        
        # 面板2: Ground Truth (如果有標註)
        gt_img = image_rgb.copy()
        if 'ground_truth' in results and results['ground_truth']:
            self._draw_ground_truth(gt_img, results['ground_truth'])
            gt_count = len(results['ground_truth'])
            axes[1].set_title(f'Ground Truth ({gt_count} 個標註)', fontsize=14, fontweight='bold')
        else:
            axes[1].set_title('Ground Truth (無標註)', fontsize=14, fontweight='bold')
        
        axes[1].imshow(gt_img)
        axes[1].axis('off')
        
        # 面板3: 預測結果
        pred_img = image_rgb.copy()
        total_predictions = 0
        
        # 繪製所有預測結果
        all_predictions = []
        if 'detection' in results and results['detection'].get('detections'):
            all_predictions.extend(results['detection']['detections'])
        if 'segmentation' in results and results['segmentation'].get('segments'):
            all_predictions.extend(results['segmentation']['segments'])
        
        if all_predictions:
            self._draw_predictions(pred_img, all_predictions)
            total_predictions = len(all_predictions)
        
        # 加入Metrics資訊(如果有)
        metrics_text = ""
        if 'metrics' in results:
            metrics = results['metrics']['overall']
            metrics_text = f" (P:{metrics['precision']:.2f}, R:{metrics['recall']:.2f}, F1:{metrics['f1_score']:.2f})"
        
        axes[2].set_title(f'預測結果 ({total_predictions} 個檢測){metrics_text}', fontsize=14, fontweight='bold')
        axes[2].imshow(pred_img)
        axes[2].axis('off')
        
        # 調整布局
        plt.tight_layout()
        
        # 使用原圖檔名保存(不加後綴)
        original_filename = Path(image_path).name  # 保留原始副檔名
        viz_path = output_path / original_filename
        
        plt.savefig(viz_path, dpi=200, bbox_inches='tight', facecolor='white')
        plt.close()
        
        self.logger.info(f"可視化結果已保存: {viz_path}")
    
    def _draw_statistics(self, ax, results: Dict[str, Any]):
        """繪製統計圖表"""
        
        class_counts = {}
        
        # 統計檢測結果
        if 'detection' in results:
            for det in results['detection']['detections']:
                class_name = det['class_name']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        # 統計分割結果
        if 'segmentation' in results:
            for seg in results['segmentation']['segments']:
                class_name = seg['class_name']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        if class_counts:
            classes = list(class_counts.keys())
            counts = list(class_counts.values())
            
            bars = ax.bar(classes, counts)
            ax.set_title('類別統計')
            ax.set_ylabel('數量')
            
            # 設置顏色
            for i, (cls, bar) in enumerate(zip(classes, bars)):
                for class_id, config in self.config.class_configs.items():
                    if config.name == cls:
                        bar.set_color([c/255.0 for c in config.color])
                        break
            
            ax.tick_params(axis='x', rotation=45)
        else:
            ax.text(0.5, 0.5, '無檢測結果', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('類別統計')
    
    def _update_stats(self, results: Dict[str, Any]):
        """更新統計資料"""
        
        self.inference_stats["total_images"] += 1
        
        # 統計檢測數量
        if 'detection' in results:
            detections = results['detection']['detections']
            self.inference_stats["total_detections"] += len(detections)
            
            for det in detections:
                class_id = det['class_id']
                if class_id in self.inference_stats["class_counts"]:
                    self.inference_stats["class_counts"][class_id] += 1
        
        # 統計分割數量
        if 'segmentation' in results:
            segments = results['segmentation']['segments']
            self.inference_stats["total_detections"] += len(segments)
            
            for seg in segments:
                class_id = seg['class_id']
                if class_id in self.inference_stats["class_counts"]:
                    self.inference_stats["class_counts"][class_id] += 1
    
    def batch_predict(self, 
                     input_dir: str,
                     output_dir: str,
                     image_extensions: List[str] = None,
                     task_type: str = "both") -> Dict[str, Any]:
        """批次推理"""
        
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 找到所有圖像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            raise ValueError(f"在 {input_dir} 中未找到圖像文件")
        
        batch_results = {}
        failed_files = []
        
        # 進度條
        with tqdm(total=len(image_files), desc="批次推理") as pbar:
            for image_file in image_files:
                try:
                    # 尋找對應的標註文件
                    annotation_file = None
                    for ext in ['.json', '.txt', '.xml']:
                        ann_path = input_path / f"{image_file.stem}{ext}"
                        if ann_path.exists():
                            annotation_file = str(ann_path)
                            break
                    
                    # 執行推理
                    result = self.predict_single_image(
                        image_path=str(image_file),
                        annotation_path=annotation_file,
                        output_dir=str(output_path),
                        task_type=task_type
                    )
                    
                    batch_results[image_file.name] = result
                    
                except Exception as e:
                    self.logger.error(f"處理 {image_file.name} 失敗: {e}")
                    failed_files.append(str(image_file))
                
                pbar.update(1)
        
        # 保存批次統計
        batch_stats = {
            'total_processed': len(image_files),
            'successful': len(batch_results),
            'failed': len(failed_files),
            'failed_files': failed_files,
            'inference_stats': self.inference_stats
        }
        
        stats_path = output_path / "batch_statistics.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(batch_stats, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"批次推理完成: {len(batch_results)}/{len(image_files)} 成功")
        
        return batch_results
    
    def get_model_info(self) -> Dict[str, Any]:
        """獲取模型資訊"""
        
        info = {
            'detection_model': None,
            'segmentation_model': None,
            'sahi_enabled': self.config.enable_sahi,
            'converter_enabled': self.config.auto_convert_annotations,
            'class_configs': {
                cls_id: {
                    'name': config.name,
                    'conf_threshold': config.conf_threshold,
                    'color': config.color,
                    'enabled': config.enabled
                }
                for cls_id, config in self.config.class_configs.items()
            }
        }
        
        if self.detection_model:
            info['detection_model'] = {
                'path': self.config.detection_model_path,
                'device': str(self.detection_model.device),
                'task': 'detection'
            }
        
        if self.segmentation_model:
            info['segmentation_model'] = {
                'path': self.config.segmentation_model_path,
                'device': str(self.segmentation_model.device),
                'task': 'segmentation'
            }
        
        return info


def create_enhanced_yolo_inference(config: Union[EnhancedYOLOConfig, str, Dict]) -> EnhancedYOLOInference:
    """創建增強YOLO推理器"""
    return EnhancedYOLOInference(config)


# 預設配置生成器
def create_default_config(detection_model: str = "", 
                         segmentation_model: str = "",
                         enable_sahi: bool = False) -> EnhancedYOLOConfig:
    """創建預設配置"""
    
    return EnhancedYOLOConfig(
        detection_model_path=detection_model,
        segmentation_model_path=segmentation_model,
        enable_sahi=enable_sahi,
        save_visualizations=True,
        save_predictions=True,
        auto_convert_annotations=True
    )


# 主函數接口 - 直接參數設定模式
def main():
    """
    主函數 - 直接在代碼中設定參數，不使用命令行參數
    修改以下參數以適應您的需求
    """
    
    # ==================== 參數設定區域 ====================
    # 模型路徑設定
    detection_model_path = ""      # 檢測模型路徑 (.pt文件)
    segmentation_model_path = ""   # 分割模型路徑 (.pt文件)
    
    # 輸入輸出設定
    input_path = ""               # 輸入圖像或目錄路徑
    output_path = ""              # 輸出目錄路徑
    
    # 任務設定
    task_type = "both"            # 任務類型: "detection", "segmentation", "both"
    enable_sahi = False           # 是否啟用SAHI切片推理
    batch_processing = True       # 是否批次處理（自動檢測輸入是文件還是目錄）
    
    # 進階配置（可選）
    config_file_path = ""         # 配置文件路徑（如果有的話）
    
    # 自動類別生成設定
    labelme_dir = ""              # LabelMe標註目錄（用於自動生成類別配置）
    auto_generate_classes = False # 是否從LabelMe自動生成類別
    
    # 推理參數設定
    img_size = 640               # 圖像大小
    global_conf = 0.25           # 全局置信度閾值
    iou_threshold = 0.45         # IoU閾值
    max_det = 1000               # 最大檢測數量
    
    # SAHI參數設定
    slice_height = 512           # 切片高度
    slice_width = 512            # 切片寬度
    overlap_height_ratio = 0.2   # 高度重疊比例
    overlap_width_ratio = 0.2    # 寬度重疊比例
    
    # 輸出設定
    save_visualizations = True   # 保存可視化結果
    save_predictions = True      # 保存預測結果JSON
    save_statistics = True       # 保存統計資料
    
    # ==================== 參數驗證 ====================
    # 檢查必要參數
    if not input_path:
        raise ValueError("請設定 input_path 參數")
    if not output_path:
        raise ValueError("請設定 output_path 參數")
    if not detection_model_path and not segmentation_model_path:
        raise ValueError("請至少設定一個模型路徑 (detection_model_path 或 segmentation_model_path)")
    
    # 檢查文件存在性
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
    
    if detection_model_path and not os.path.exists(detection_model_path):
        raise FileNotFoundError(f"檢測模型不存在: {detection_model_path}")
    
    if segmentation_model_path and not os.path.exists(segmentation_model_path):
        raise FileNotFoundError(f"分割模型不存在: {segmentation_model_path}")
    
    # ==================== 配置創建 ====================
    # 從配置文件載入或創建默認配置
    if config_file_path and os.path.exists(config_file_path):
        print(f"從配置文件載入設定: {config_file_path}")
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        config = EnhancedYOLOConfig(**config_dict)
    else:
        print("使用代碼中的參數設定創建配置")
        config = EnhancedYOLOConfig(
            # 模型配置
            detection_model_path=detection_model_path,
            segmentation_model_path=segmentation_model_path,
            
            # 推理配置
            img_size=img_size,
            global_conf=global_conf,
            iou_threshold=iou_threshold,
            max_det=max_det,
            
            # SAHI配置
            enable_sahi=enable_sahi,
            slice_height=slice_height,
            slice_width=slice_width,
            overlap_height_ratio=overlap_height_ratio,
            overlap_width_ratio=overlap_width_ratio,
            
            # 輸出配置
            save_visualizations=save_visualizations,
            save_predictions=save_predictions,
            save_statistics=save_statistics,
            
            # 類別配置
            labelme_dir=labelme_dir,
            auto_generate_classes=auto_generate_classes,
            
            # 其他配置
            auto_convert_annotations=True
        )
    
    # ==================== 執行推理 ====================
    try:
        # 創建推理器
        print("初始化增強YOLO推理器...")
        inference = create_enhanced_yolo_inference(config)
        
        # 顯示模型資訊
        model_info = inference.get_model_info()
        print("模型資訊:")
        if model_info['detection_model']:
            print(f"  檢測模型: {model_info['detection_model']['path']}")
        if model_info['segmentation_model']:
            print(f"  分割模型: {model_info['segmentation_model']['path']}")
        print(f"  SAHI啟用: {model_info['sahi_enabled']}")
        print(f"  類別數量: {len(model_info['class_configs'])}")
        
        # 執行推理
        print(f"開始推理...")
        print(f"  輸入: {input_path}")
        print(f"  輸出: {output_path}")
        print(f"  任務類型: {task_type}")
        
        if batch_processing or os.path.isdir(input_path):
            print("執行批次推理...")
            results = inference.batch_predict(input_path, output_path, task_type=task_type)
            print(f"批次推理完成: 處理了 {len(results)} 個圖像")
        else:
            print("執行單張圖像推理...")
            results = inference.predict_single_image(input_path, output_dir=output_path, task_type=task_type)
            print("單張圖像推理完成")
        
        # 顯示統計資訊
        stats = inference.inference_stats
        print("\n推理統計:")
        print(f"  總圖像數: {stats['total_images']}")
        print(f"  總檢測數: {stats['total_detections']}")
        print("  各類別統計:")
        for class_id, count in stats['class_counts'].items():
            if count > 0 and class_id in config.class_configs:
                class_name = config.class_configs[class_id].name
                print(f"    {class_name}: {count}")
        
        print(f"\n推理完成！結果保存在: {output_path}")
        
    except Exception as e:
        print(f"推理過程中發生錯誤: {e}")
        raise


if __name__ == "__main__":
    main()