#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圖像處理工具模組

基於 test.py 建構的圖像處理工具系統
支援圖像合併、時間戳匹配、CSV處理和置信度篩選
"""

import os
import shutil
import re
import logging
import pandas as pd
from PIL import Image
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
import cv2


class ImageProcessor:
    """
    圖像處理器
    
    提供圖像合併、匹配和後處理功能
    支援基於時間戳的圖像配對和CSV資料處理
    """
    
    def __init__(self, 
                 conf_threshold: float = 0.8,
                 default_resize_ratio: float = 0.5):
        """
        初始化圖像處理器
        
        參數:
            conf_threshold: 置信度閾值
            default_resize_ratio: 默認調整大小比例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.conf_threshold = conf_threshold
        self.default_resize_ratio = default_resize_ratio
        
        # 支援的圖像格式
        self.supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
    
    def extract_timestamp(self, filename: str) -> Optional[str]:
        """
        從檔案名稱中提取最後一個底線後的時間戳記
        
        參數:
            filename: 檔案名稱
            
        返回:
            時間戳字符串或None
        """
        match = re.search(r'_(\d+)\.jpg$', filename)
        if match:
            return match.group(1)
        return None
    
    def merge_images_side_by_side(self, 
                                 img1_path: str, 
                                 img2_path: str, 
                                 output_path: str,
                                 resize_ratio: Optional[float] = None) -> bool:
        """
        將兩張圖像側邊合併
        
        參數:
            img1_path: 第一張圖像路徑
            img2_path: 第二張圖像路徑
            output_path: 輸出路徑
            resize_ratio: 第二張圖像調整大小比例
            
        返回:
            是否成功
        """
        try:
            img1 = Image.open(img1_path)
            img2 = Image.open(img2_path)
            
            # 調整第二張圖像大小
            if resize_ratio is None:
                resize_ratio = self.default_resize_ratio
            
            img2 = img2.resize((int(img1.width * resize_ratio), img1.height))
            
            # 創建合併圖像
            merged_width = img1.width + img2.width
            merged_height = max(img1.height, img2.height)
            merged_img = Image.new('RGB', (merged_width, merged_height))
            
            # 將圖像並排貼上
            merged_img.paste(img1, (0, 0))
            merged_img.paste(img2, (img1.width, 0))
            
            # 確保輸出目錄存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 儲存合併後的圖像
            merged_img.save(output_path)
            
            self.logger.info(f"成功合併圖像: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"合併圖像失敗 {img1_path} + {img2_path}: {e}")
            return False
    
    def merge_matching_images_and_csv(self,
                                    folder1: str,
                                    folder2: str,
                                    output_folder: str,
                                    high_conf_folder: str,
                                    csv_path: str,
                                    conf_threshold: Optional[float] = None) -> Dict[str, int]:
        """
        基於時間戳記合併匹配圖像並處理CSV資料
        
        參數:
            folder1: 第一個圖像資料夾
            folder2: 第二個圖像資料夾
            output_folder: 輸出資料夾
            high_conf_folder: 高置信度資料夾
            csv_path: CSV檔案路徑
            conf_threshold: 置信度閾值
            
        返回:
            統計資訊字典
        """
        if conf_threshold is None:
            conf_threshold = self.conf_threshold
        
        # 創建輸出資料夾
        os.makedirs(output_folder, exist_ok=True)
        os.makedirs(high_conf_folder, exist_ok=True)
        
        # 讀取CSV檔案
        try:
            csv_data = pd.read_csv(csv_path)
        except Exception as e:
            self.logger.error(f"無法讀取CSV檔案 {csv_path}: {e}")
            return {'error': 1}
        
        # 從CSV中提取時間戳記並建立映射
        csv_timestamps = {}
        for _, row in csv_data.iterrows():
            filename = str(row['檔案名稱'])
            timestamp = self.extract_timestamp(filename)
            if timestamp:
                if timestamp not in csv_timestamps:
                    csv_timestamps[timestamp] = []
                csv_timestamps[timestamp].append(row)
        
        # 獲取兩個資料夾中的所有圖像檔案
        files1 = [f for f in os.listdir(folder1) 
                 if f.lower().endswith(self.supported_formats)]
        files2 = [f for f in os.listdir(folder2) 
                 if f.lower().endswith(self.supported_formats)]
        
        # 創建時間戳記到檔案名稱的映射字典
        timestamps1 = {self.extract_timestamp(f): f 
                      for f in files1 if self.extract_timestamp(f)}
        timestamps2 = {self.extract_timestamp(f): f 
                      for f in files2 if self.extract_timestamp(f)}
        
        # 找到共同的時間戳記
        common_timestamps = set(timestamps1.keys()) & set(timestamps2.keys())
        
        self.logger.info(f"找到 {len(common_timestamps)} 對匹配的圖像")
        
        # 創建新的CSV資料框
        output_csv = pd.DataFrame(columns=csv_data.columns)
        high_conf_csv = pd.DataFrame(columns=csv_data.columns)
        
        processed_count = 0
        high_conf_count = 0
        
        # 處理每一對匹配的圖像
        for timestamp in common_timestamps:
            file1 = os.path.join(folder1, timestamps1[timestamp])
            file2 = os.path.join(folder2, timestamps2[timestamp])
            
            # 檢查是否已存在合併後的圖像
            merged_filename = f"merged_{timestamp}.jpg"
            merged_path = os.path.join(output_folder, merged_filename)
            
            # 如果合併的圖像不存在，則創建它
            if not os.path.exists(merged_path):
                # 複製檔案到輸出資料夾並加上命名前綴
                output_file1 = os.path.join(output_folder, f"1_{timestamps1[timestamp]}")
                output_file2 = os.path.join(output_folder, f"2_{timestamps2[timestamp]}")
                shutil.copy2(file1, output_file1)
                shutil.copy2(file2, output_file2)
                
                # 創建並儲存側邊合併的圖像
                if self.merge_images_side_by_side(file1, file2, merged_path):
                    processed_count += 1
            else:
                self.logger.info(f"已存在合併圖像: {merged_filename}，跳過合併步驟")
            
            # 處理CSV資料
            if timestamp in csv_timestamps:
                timestamp_rows = csv_timestamps[timestamp]
                
                # 將匹配的行添加到輸出CSV
                for row in timestamp_rows:
                    row_df = pd.DataFrame([row])
                    output_csv = pd.concat([output_csv, row_df], ignore_index=True)
                    
                    # 檢查置信度並添加到高置信度CSV
                    if float(row['置信度']) >= conf_threshold:
                        high_conf_csv = pd.concat([high_conf_csv, row_df], ignore_index=True)
                        high_conf_count += 1
                        
                        # 複製高置信度圖像到高置信度資料夾
                        if os.path.exists(merged_path):
                            high_conf_image_path = os.path.join(high_conf_folder, merged_filename)
                            if not os.path.exists(high_conf_image_path):
                                shutil.copy2(merged_path, high_conf_image_path)
        
        # 儲存CSV檔案
        if not output_csv.empty:
            output_csv_path = os.path.join(output_folder, "merged_records.csv")
            output_csv.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"儲存合併的CSV到: {output_csv_path}")
        
        # 儲存高置信度CSV檔案
        if not high_conf_csv.empty:
            high_conf_csv_path = os.path.join(high_conf_folder, "high_confidence_records.csv")
            high_conf_csv.to_csv(high_conf_csv_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"儲存高置信度CSV到: {high_conf_csv_path}")
        
        return {
            'total_matched': len(common_timestamps),
            'processed_images': processed_count,
            'high_confidence_records': high_conf_count,
            'confidence_threshold': conf_threshold
        }
    
    def filter_by_confidence(self, 
                           csv_path: str, 
                           output_path: str, 
                           threshold: float) -> int:
        """
        按置信度篩選CSV記錄
        
        參數:
            csv_path: 輸入CSV路徑
            output_path: 輸出CSV路徑
            threshold: 置信度閾值
            
        返回:
            篩選後的記錄數量
        """
        try:
            df = pd.read_csv(csv_path)
            
            # 篩選高置信度記錄
            high_conf_df = df[df['置信度'] >= threshold]
            
            # 確保輸出目錄存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 儲存篩選後的資料
            high_conf_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"篩選完成: {len(high_conf_df)} 條記錄 (閾值: {threshold})")
            return len(high_conf_df)
            
        except Exception as e:
            self.logger.error(f"置信度篩選失敗: {e}")
            return 0
    
    def create_image_comparison(self, 
                              img1_path: str, 
                              img2_path: str, 
                              output_path: str,
                              title1: str = "Image 1",
                              title2: str = "Image 2") -> bool:
        """
        創建圖像對比視圖
        
        參數:
            img1_path: 第一張圖像路徑
            img2_path: 第二張圖像路徑
            output_path: 輸出路徑
            title1: 第一張圖像標題
            title2: 第二張圖像標題
            
        返回:
            是否成功
        """
        try:
            # 使用OpenCV讀取圖像
            img1 = cv2.imread(img1_path)
            img2 = cv2.imread(img2_path)
            
            if img1 is None or img2 is None:
                self.logger.error("無法讀取圖像檔案")
                return False
            
            # 調整圖像大小使其高度相同
            h1, w1 = img1.shape[:2]
            h2, w2 = img2.shape[:2]
            
            target_height = min(h1, h2)
            img1 = cv2.resize(img1, (int(w1 * target_height / h1), target_height))
            img2 = cv2.resize(img2, (int(w2 * target_height / h2), target_height))
            
            # 添加標題
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1
            color = (255, 255, 255)
            thickness = 2
            
            cv2.putText(img1, title1, (10, 30), font, font_scale, color, thickness)
            cv2.putText(img2, title2, (10, 30), font, font_scale, color, thickness)
            
            # 水平拼接
            comparison = np.hstack((img1, img2))
            
            # 確保輸出目錄存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 儲存對比圖像
            cv2.imwrite(output_path, comparison)
            
            self.logger.info(f"成功創建對比圖像: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"創建對比圖像失敗: {e}")
            return False


def create_image_processor(conf_threshold: float = 0.8, 
                          default_resize_ratio: float = 0.5) -> ImageProcessor:
    """
    創建圖像處理器實例（工廠函數）
    
    參數:
        conf_threshold: 置信度閾值
        default_resize_ratio: 默認調整大小比例
        
    返回:
        ImageProcessor實例
    """
    return ImageProcessor(
        conf_threshold=conf_threshold,
        default_resize_ratio=default_resize_ratio
    )


# 向後兼容函數
def merge_matching_images_and_csv(folder1: str, 
                                folder2: str, 
                                output_folder: str,
                                high_conf_folder: str, 
                                csv_path: str, 
                                conf_threshold: float = 0.8) -> None:
    """
    向後兼容的圖像合併函數
    
    這個函數保持與原始test.py相同的接口
    """
    processor = create_image_processor(conf_threshold=conf_threshold)
    result = processor.merge_matching_images_and_csv(
        folder1, folder2, output_folder, high_conf_folder, csv_path, conf_threshold
    )
    
    if 'error' not in result:
        print(f"找到 {result['total_matched']} 對匹配的圖像。")
        print(f"找到 {result['high_confidence_records']} 筆高置信度記錄（置信度 >= {conf_threshold}）")


if __name__ == "__main__":
    # 測試範例
    processor = create_image_processor()
    
    # 測試基本功能
    print("🧪 圖像處理工具模組測試")
    print(f"置信度閾值: {processor.conf_threshold}")
    print(f"調整大小比例: {processor.default_resize_ratio}")
    print("✅ 圖像處理器初始化成功")