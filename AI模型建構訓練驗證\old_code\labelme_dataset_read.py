import os
import json
import numpy as np
import logging
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import torch
from torch.utils.data import Dataset
import albumentations as A
from albumentations.pytorch import ToTensorV2

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('LabelmeDataset')

class LabelmeDataset(Dataset):
    def __init__(self, data_dir, size, split='train', transform=None, log_enabled=True):
        """
        初始化 LabelmeDataset
        
        參數:
            data_dir (str): 資料目錄路徑
            size (int): 輸出圖像大小
            split (str): 資料集分割 ('train', 'val', 'test')
            transform: albumentations 轉換
            log_enabled (bool): 是否啟用日誌
        """
        self.size = size
        self.split = split
        self.data_dir = data_dir
        self.transform = transform
        self.log_enabled = log_enabled
        self.use_tensor = False  # 檢查是否使用 ToTensorV2
        
        # 檢查轉換中是否含有 ToTensorV2
        if transform is not None:
            for t in transform:
                if isinstance(t, ToTensorV2):
                    self.use_tensor = True
                    if self.log_enabled:
                        logger.info("檢測到 ToTensorV2 轉換")
                    break
        
        self.img_files = []
        self.json_files = []
        
        # 記錄開始讀取資料
        if self.log_enabled:
            logger.info(f"開始初始化 {split} 資料集")
        
        # 檢查資料目錄結構
        if not os.path.exists(data_dir):
            raise FileNotFoundError(f"找不到資料目錄: {data_dir}")
        
        # 嘗試不同可能的目錄結構讀取分割檔案
        split_file = None
        possible_split_paths = [
            os.path.join(self.data_dir, f'images/{self.split}.txt'),
            os.path.join(self.data_dir, f'ImageSets/Segmentation/{self.split}.txt'),
            os.path.join(self.data_dir, f'{self.split}.txt')
        ]
        
        for path in possible_split_paths:
            if os.path.exists(path):
                split_file = path
                break
        
        if split_file:
            if self.log_enabled:
                logger.info(f"讀取分割檔案: {split_file}")
            
            # 讀取圖片文件列表
            with open(split_file, 'r') as f:
                lines = f.readlines()
                
            for line in lines:
                img_name = line.strip()
                img_file, json_file = self._find_image_and_json(img_name)
                
                if img_file and json_file:
                    self.img_files.append(img_file)
                    self.json_files.append(json_file)
        else:
            # 如果找不到分割文件，直接讀取所有 JSON 文件
            if self.log_enabled:
                logger.info(f"找不到分割檔案，將讀取所有 JSON 文件")
            
            # 尋找所有 JSON 文件
            json_dir = os.path.join(self.data_dir, 'labels', self.split)
            if not os.path.exists(json_dir):
                json_dir = os.path.join(self.data_dir, 'Annotations')
            if not os.path.exists(json_dir):
                json_dir = self.data_dir
            
            for filename in os.listdir(json_dir):
                if filename.endswith('.json'):
                    json_file = os.path.join(json_dir, filename)
                    # 讀取 JSON 以獲取對應的圖片路徑
                    try:
                        with open(json_file, 'r') as f:
                            json_data = json.load(f)
                            img_path = json_data.get('imagePath')
                            if img_path:
                                img_file = self._find_image_by_name(img_path)
                                if img_file:
                                    self.img_files.append(img_file)
                                    self.json_files.append(json_file)
                    except Exception as e:
                        if self.log_enabled:
                            logger.warning(f"無法讀取 JSON 文件 {filename}: {e}")
        
        if self.log_enabled:
            logger.info(f"找到 {len(self.img_files)} 筆資料")
    
    def _find_image_and_json(self, img_name):
        """尋找圖片和對應的 JSON 文件"""
        img_file = self._find_image_by_name(img_name)
        
        # 尋找對應的 JSON 文件
        json_file = None
        if img_file:
            base_name = os.path.splitext(os.path.basename(img_file))[0]
            possible_json_paths = [
                os.path.join(self.data_dir, 'labels', self.split, f"{base_name}.json"),
                os.path.join(self.data_dir, 'Annotations', f"{base_name}.json"),
                os.path.join(self.data_dir, f"{base_name}.json")
            ]
            
            for path in possible_json_paths:
                if os.path.exists(path):
                    json_file = path
                    break
        
        return img_file, json_file
    
    def _find_image_by_name(self, img_name):
        """尋找圖片文件"""
        # 如果 img_name 已經是完整路徑，直接檢查
        if os.path.exists(img_name):
            return img_name
        
        # 提取文件名（沒有副檔名）
        base_name = os.path.splitext(os.path.basename(img_name))[0]
        
        # 檢查不同可能的圖片路徑
        possible_paths = [
            os.path.join(self.data_dir, 'images', self.split, f"{base_name}.jpg"),
            os.path.join(self.data_dir, 'images', self.split, f"{base_name}.png"),
            os.path.join(self.data_dir, 'JPEGImages', f"{base_name}.jpg"),
            os.path.join(self.data_dir, 'JPEGImages', f"{base_name}.png"),
            os.path.join(self.data_dir, f"{base_name}.jpg"),
            os.path.join(self.data_dir, f"{base_name}.png")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        if self.log_enabled:
            logger.warning(f"找不到圖片: {img_name}")
        return None
    
    def __len__(self):
        """返回資料集大小"""
        return len(self.img_files)
        
    def __getitem__(self, idx):
        """獲取資料項"""
        if self.log_enabled:
            logger.info(f"讀取第 {idx} 筆資料")
            
        # 獲取檔案路徑
        img_file = self.img_files[idx]
        json_file = self.json_files[idx]
        
        # 讀取圖片
        image = np.array(Image.open(img_file).convert('RGB'))
        original_height, original_width = image.shape[:2]
        
        if self.log_enabled:
            logger.info(f"圖片尺寸: {original_width}x{original_height}")
        
        # 讀取 JSON 標籤
        with open(json_file, 'r') as f:
            label_data = json.load(f)
            
        # 創建掩碼
        mask = np.zeros((original_height, original_width), dtype=np.uint8)
        
        # 讀取所有形狀
        shapes = label_data.get('shapes', [])
        for i, shape in enumerate(shapes):
            label = shape.get('label')
            shape_type = shape.get('shape_type')
            points = shape.get('points')
            
            # 預設使用索引作為類別 ID（從 1 開始，0 保留給背景）
            class_id = i + 1
            
            if shape_type == 'polygon' and points:
                # 處理多邊形
                points_array = np.array(points, dtype=np.int32)
                cv2.fillPoly(mask, [points_array], class_id)
                
            elif shape_type == 'rectangle' and len(points) == 2:
                # 處理矩形 (左上和右下點)
                x1, y1 = points[0]
                x2, y2 = points[1]
                cv2.rectangle(mask, (int(x1), int(y1)), (int(x2), int(y2)), class_id, -1)
                
            elif shape_type == 'circle' and len(points) == 2:
                # 處理圓形 (中心點和半徑點)
                center_x, center_y = points[0]
                radius_point_x, radius_point_y = points[1]
                radius = int(np.sqrt((radius_point_x - center_x)**2 + (radius_point_y - center_y)**2))
                cv2.circle(mask, (int(center_x), int(center_y)), radius, class_id, -1)
                
            elif shape_type == 'line' and len(points) >= 2:
                # 處理線段 (可能有多個點)
                points_array = np.array(points, dtype=np.int32)
                cv2.polylines(mask, [points_array], False, class_id, 5)
                
            elif shape_type == 'point' and points:
                # 處理點
                for point in points:
                    cv2.circle(mask, (int(point[0]), int(point[1])), 5, class_id, -1)
        
        # 保存原始圖片和掩碼用於視覺化
        self.last_processed = {
            'image': image.copy(),
            'mask': mask.copy(),
            'original_image': image.copy()
        }
        
        # 應用轉換
        if self.transform:
            if self.log_enabled:
                logger.info("應用資料增強")
            
            transformed = self.transform(image=image, mask=mask)
            image = transformed["image"]
            mask = transformed["mask"]
            
            # 保存處理後的圖像和掩碼 (若是張量則先轉回 NumPy 以供視覺化)
            if self.use_tensor:
                if self.log_enabled:
                    logger.info("檢測到張量輸出，為視覺化轉換回 NumPy 格式")
                # 保存轉換後但尚未轉為張量的版本 (用於視覺化)
                if isinstance(image, torch.Tensor):
                    # 如果是張量，將其轉為 NumPy
                    self.last_processed['processed_image'] = image.permute(1, 2, 0).cpu().numpy()
                    
                if isinstance(mask, torch.Tensor):
                    self.last_processed['processed_mask'] = mask.cpu().numpy()
            else:
                self.last_processed['processed_image'] = image
                self.last_processed['processed_mask'] = mask
        else:
            # 如果沒有轉換，調整大小
            resized_image = cv2.resize(image, (self.size, self.size))
            resized_mask = cv2.resize(mask, (self.size, self.size), interpolation=cv2.INTER_NEAREST)
            
            # 將圖片轉換為 PyTorch tensor
            image_tensor = torch.from_numpy(resized_image.transpose(2, 0, 1)).float() / 255.0
            mask_tensor = torch.from_numpy(resized_mask).long()
            
            self.last_processed['processed_image'] = resized_image
            self.last_processed['processed_mask'] = resized_mask
            
            return image_tensor, mask_tensor
        
        if self.log_enabled:
            if isinstance(image, torch.Tensor):
                logger.info(f"處理完成，返回 shape: image={image.shape}, mask={mask.shape}")
            else:
                logger.info(f"處理完成，返回 shape: image={image.shape}, mask={mask.shape}")
        
        return image, mask
    
    def visualize(self, idx, show_n=3, figsize=(15, 5)):
        """
        視覺化資料
        
        參數:
            idx (int): 資料索引
            show_n (int): 顯示的圖片數量 (1-3)
            figsize (tuple): 圖形大小
        """
        # 檢查是否有處理過的資料
        if not hasattr(self, 'last_processed'):
            # 如果沒有，則處理資料
            self[idx]
            
        # 獲取圖片和掩碼
        original_image = self.last_processed['original_image']
        processed_image = self.last_processed.get('processed_image', self.last_processed['image'])
        mask = self.last_processed.get('processed_mask', self.last_processed['mask'])
        
        # 確保圖像和掩碼為 NumPy 陣列
        if isinstance(processed_image, torch.Tensor):
            processed_image = processed_image.permute(1, 2, 0).cpu().numpy()
        
        if isinstance(mask, torch.Tensor):
            mask = mask.cpu().numpy()
        
        # 限制顯示的圖片數量
        show_n = min(3, max(1, show_n))
        
        # 創建圖形
        fig, axes = plt.subplots(1, show_n, figsize=figsize)
        
        # 如果只顯示一張圖，則將 axes 轉換為列表
        if show_n == 1:
            axes = [axes]
            
        # 顯示原圖
        axes[0].imshow(original_image)
        axes[0].set_title('原始圖片')
        axes[0].axis('off')
        
        if show_n > 1:
            # 顯示掩碼
            axes[1].imshow(mask, cmap='tab20')
            axes[1].set_title('標籤掩碼')
            axes[1].axis('off')
            
        if show_n > 2:
            # 顯示疊加圖
            overlap = processed_image.copy()
            # 創建彩色掩碼
            colored_mask = np.zeros_like(overlap)
            for i in range(1, np.max(mask) + 1):
                color = np.random.randint(0, 255, 3)
                colored_mask[mask == i] = color
                
            # 疊加掩碼
            alpha = 0.5
            overlap = cv2.addWeighted(overlap, 1, colored_mask, alpha, 0)
            
            axes[2].imshow(overlap)
            axes[2].set_title('疊加')
            axes[2].axis('off')
            
        plt.tight_layout()
        plt.show()
        
        return fig


# 範例使用
if __name__ == "__main__":
    # 定義轉換
    transform = A.Compose([
        A.Resize(height=224, width=224),
        A.HorizontalFlip(p=0.5),
        A.RandomBrightnessContrast(p=0.2),
        ToTensorV2(),  # 轉換為 PyTorch 張量
    ])
    
    # 創建資料集
    dataset = LabelmeDataset(
        data_dir='./AI_train_dataset',
        size=224,
        split='train',
        transform=transform
    )
    
    # 視覺化示例
    print(f"資料集大小: {len(dataset)}")
    
    # 獲取並視覺化一個資料項
    if len(dataset) > 0:
        idx = 0
        image, mask = dataset[idx]
        print(f"圖片類型: {type(image)}")
        if isinstance(image, torch.Tensor):
            print(f"圖片 shape: {image.shape}")
        else:
            print(f"圖片 shape: {image.shape}")
        
        # 視覺化
        dataset.visualize(idx, show_n=3)

