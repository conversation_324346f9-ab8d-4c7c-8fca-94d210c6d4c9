import shutil
import random
import logging
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
import json
import os


# 新增 Pillow import 用於圖片處理
try:
    from PIL import Image
except ImportError:
    Image = None

# ---------------------------- 優化後的 YoloDatasetSplitter ----------------------------
class YoloDatasetSplitter:
    def __init__(self,
                 source_root,
                 target_root,
                 external_root=None,         # 外部資料集路徑
                 use_external_dataset=False, # 是否啟用外部資料集
                 simulate=False,             # 模擬模式（dry-run）
                 excluded_classes_input=None,
                 min_samples_per_class=0,
                 max_samples_per_class=450,
                 random_seed=42,
                 use_multi_label_samples=True,
                 # 外部資料集專用參數，若未指定則採用主資料集的設定
                 external_excluded_classes_input=None,
                 external_min_samples_per_class=None,
                 external_max_samples_per_class=None,
                 external_random_seed=None,
                 # 每個類別分別設定內部/外部抽取數量(共用預設)
                 internal_samples_per_class=None,  # e.g. {"joint":900, ...}
                 external_samples_per_class=None,
                 # 以下新加分任務的設定，若設定則優先使用
                 internal_samples_per_class_od=None,
                 internal_samples_per_class_seg=None,
                 external_samples_per_class_od=None,
                 external_samples_per_class_seg=None,
                 # 外部資料集啟用時，是否全部丟進 train (False 為依 0.7/0.15/0.15 切分)
                 external_train_only=False,
                 # 新增參數：是否調整圖像尺寸、尺寸大小及圖片儲存品質
                 resize_images=False,
                 resize_size=(1200, 900),
                 img_save_quality=75,
                 process_order="quality_then_resize",
                 apply_sharpen=False,
                 sharpen_amount=1.0,
                 # 新增 cutout 參數：
                 # 是否啟用 cutout、針對每張圖像執行的機率，以及網格大小與每格遮罩比率
                 apply_cutout=False,
                 cutout_image_prob=1.0,
                 cutout_grid_size=(50, 50),
                 cutout_cell_mask_ratio=0.3,
                 remove_excluded_labels=False):
    
        self.logger = logging.getLogger(__name__)
        self.source_root = Path(source_root)
        self.target_root = Path(target_root)
        self.external_root = Path(external_root) if external_root else None
        self.use_external_dataset = use_external_dataset
        self.simulate = simulate

        # 主資料集設定
        self.excluded_classes_input = excluded_classes_input or set()
        self.min_samples_per_class = min_samples_per_class
        self.max_samples_per_class = max_samples_per_class  
        self.random_seed = random_seed

        # 外部資料集設定
        
        self.external_excluded_classes_input = external_excluded_classes_input or self.excluded_classes_input
        
        self.external_min_samples_per_class = (external_min_samples_per_class 
                                               if external_min_samples_per_class is not None 
                                               else self.min_samples_per_class)
        self.external_max_samples_per_class = (external_max_samples_per_class 
                                               if external_max_samples_per_class is not None 
                                               else self.max_samples_per_class)
        self.external_random_seed = (external_random_seed 
                                     if external_random_seed is not None 
                                     else self.random_seed)

        # 共用抽取數量設定（當下方任務專用設定沒有提供時使用）
        self.internal_samples_per_class = internal_samples_per_class  
        self.external_samples_per_class = external_samples_per_class

        # 分任務抽取設定（優先使用）
        self.internal_samples_per_class_od = internal_samples_per_class_od
        self.internal_samples_per_class_seg = internal_samples_per_class_seg
        self.external_samples_per_class_od = external_samples_per_class_od
        self.external_samples_per_class_seg = external_samples_per_class_seg

        self.external_train_only = external_train_only
        self.use_multi_label_samples = use_multi_label_samples
        # 新增的參數：如果啟動，將移除掉排除類別的標籤而非直接捨棄整張圖
        self.remove_excluded_labels = remove_excluded_labels
        random.seed(self.random_seed)
        self.image_exts = [".png", ".jpg", ".jpeg"]

        # 新增圖片調整參數
        self.resize_images = resize_images
        self.resize_size = resize_size
        self.img_save_quality = img_save_quality
        self.process_order = process_order  # 新增處理順序參數
        # 新增銳化參數
        self.apply_sharpen = apply_sharpen
        self.sharpen_amount = sharpen_amount
        
        # 新增 cutout 參數
        self.apply_cutout = apply_cutout
        self.cutout_image_prob = cutout_image_prob
        self.cutout_grid_size = cutout_grid_size
        self.cutout_cell_mask_ratio = cutout_cell_mask_ratio


        # 定義類別對應：key 為英文，value 為中文描述
        self.class_mapping = {
            "expansion_joint": "伸縮縫",
            "joint": "路面接縫",
            "linear_crack": "裂縫",
            "Alligator_crack": "龜裂",
            "potholes": "坑洞",
            "patch": "補綻",
            "manhole_ditch": "人孔蓋或排水溝",
            "deformation": "變形",
            "dirt_spot": "污垢",
            "other": "其它"
        }
        self.excluded_class_keys = self.resolve_class_keys(self.excluded_classes_input)
        self.external_excluded_class_keys = self.resolve_class_keys(external_excluded_classes_input or excluded_classes_input)

        self.reverse_class_id_map = {i: k for i, k in enumerate(self.class_mapping)}

        # 預設切分比例（下游切分使用）
        self.train_ratio = 0.7
        self.val_ratio = 0.15
        self.test_ratio = 0.15

    def resolve_class_keys(self, input_set):
        resolved = set()
        for item in input_set:
            for key, zh in self.class_mapping.items():
                if item == key or item == zh:
                    resolved.add(key)
        return resolved

    def smart_ratio_split(self, samples):
        n = len(samples)
        if n == 0:
            return {"train": [], "val": [], "test": []}
        elif n == 1:
            return {"train": [], "val": [], "test": samples}
        elif n == 2:
            return {"train": [samples[0]], "val": [], "test": [samples[1]]}
        elif n == 3:
            return {"train": [samples[0]], "val": [samples[1]], "test": [samples[2]]}
        else:
            n_test = max(1, int(n * self.test_ratio))
            n_val = max(1, int(n * self.val_ratio))
            n_train = n - n_test - n_val
            if n_train < 1:
                n_train = 1
                if n_val + n_test > n - 1:
                    n_val = max(0, n - n_train - n_test)
                    n_test = n - n_train - n_val
            return {
                "train": samples[:n_train],
                "val": samples[n_train:n_train + n_val],
                "test": samples[n_train + n_val:]
            }

    # 依任務設置專屬目錄：影像與標籤皆獨立
    def ensure_dirs(self, task_type):
        images_dir = self.target_root / task_type / "images"
        labels_dir = self.target_root / f"{task_type}_task_labels"
        for split in ["train", "val", "test"]:
            if self.simulate:
                self.logger.info(f"[SIMULATE] Would create directory: {images_dir / split}")
                self.logger.info(f"[SIMULATE] Would create directory: {labels_dir / split}")
            else:
                (images_dir / split).mkdir(parents=True, exist_ok=True)
                (labels_dir / split).mkdir(parents=True, exist_ok=True)
    
    # 新增一個方法進行 cutout 操作，根據網格參數隨機黑色遮罩
    def apply_cutout_mask(self, img):
        import numpy as np
        h, w, _ = img.shape
        grid_w, grid_h = self.cutout_grid_size
        num_cells_x = w // grid_w
        num_cells_y = h // grid_h
        # 遍歷每個格子，按照給定比例決定是否遮罩該格子
        for i in range(num_cells_y):
            for j in range(num_cells_x):
                if np.random.rand() < self.cutout_cell_mask_ratio:
                    x1 = j * grid_w
                    y1 = i * grid_h
                    x2 = x1 + grid_w
                    y2 = y1 + grid_h
                    img[y1:y2, x1:x2] = 0  # 置為黑色
        return img


    def copy_file(self, src, dst):
        """
        複製檔案，同時可選擇：
          - 純 resize (process_order="resize_only")
          - quality_then_resize / resize_then_quality
        如果 resize_images=False，則直接用 shutil.copy。
        """
        if self.simulate:
            self.logger.info(f"[SIMULATE] Would copy file: {src} -> {dst}")
            return

        # 只有在需 resize_images 且是圖片時，才走 OpenCV 分支
        if self.resize_images and src.suffix.lower() in self.image_exts:
            try:
                import cv2
                import numpy as np
            except ImportError:
                self.logger.error("需要 opencv-python 才能使用圖像處理功能，請安裝後重試。")
                raise

            # 以 numpy.fromfile + imdecode 支援 Unicode 路徑
            data = np.fromfile(str(src), dtype=np.uint8)
            im = cv2.imdecode(data, cv2.IMREAD_COLOR)
            if im is None:
                raise ValueError(f"無法讀取影像檔: {src}")

            # 1) 純 resize 分支
            if self.process_order == "resize_only":
                im_resized = cv2.resize(im, self.resize_size, interpolation=cv2.INTER_AREA)
                # 確保目錄
                dst.parent.mkdir(parents=True, exist_ok=True)
                # 處理 Windows 長路徑
                path_to_open = ("\\\\?\\" + str(dst)) if os.name=='nt' and len(str(dst))>=260 else str(dst)
                # imencode 用原副檔名
                ext = dst.suffix.lower()
                ok, buf = cv2.imencode(ext, im_resized)
                if not ok:
                    raise ValueError(f"resize 寫出失敗: {src}")
                with open(path_to_open, 'wb') as f:
                    f.write(buf.tobytes())
                self.logger.info(f"Resized only: {src} -> {dst}")
                return

            # 2) quality_then_resize / resize_then_quality 分支
            encode_params = [int(cv2.IMWRITE_JPEG_QUALITY), self.img_save_quality]

            if self.process_order == "quality_then_resize":
                ok, encimg = cv2.imencode('.jpg', im, encode_params)
                if not ok:
                    raise ValueError(f"質量壓縮失敗: {src}")
                im_q = cv2.imdecode(encimg, cv2.IMREAD_COLOR)
                im_resized = cv2.resize(im_q, self.resize_size, interpolation=cv2.INTER_AREA) if self.resize_size else im_q

            elif self.process_order == "resize_then_quality":
                im_tmp = cv2.resize(im, self.resize_size, interpolation=cv2.INTER_AREA) if self.resize_size else im
                ok, encimg = cv2.imencode('.jpg', im_tmp, encode_params)
                if not ok:
                    raise ValueError(f"質量壓縮失敗: {src}")
                im_resized = cv2.imdecode(encimg, cv2.IMREAD_COLOR)

            else:
                raise ValueError(f"Unknown process_order: {self.process_order}")

            # 可選銳化
            if self.apply_sharpen:
                im_f = im_resized.astype('float32')
                lap = cv2.Laplacian(im_f, ddepth=cv2.CV_32F, ksize=3)
                im_resized = np.clip(im_f - self.sharpen_amount * lap, 0, 255).astype('uint8')

            # 可選 cutout
            if self.apply_cutout and np.random.rand() < self.cutout_image_prob:
                im_resized = self.apply_cutout_mask(im_resized)

            # 最後 encode 寫出 JPEG
            ok, encimg2 = cv2.imencode('.jpg', im_resized, encode_params)
            if not ok:
                raise ValueError(f"最終寫出壓縮影像失敗: {src}")

            dst.parent.mkdir(parents=True, exist_ok=True)
            path_to_open = ("\\\\?\\" + str(dst)) if os.name=='nt' and len(str(dst))>=260 else str(dst)
            with open(path_to_open, 'wb') as f:
                f.write(encimg2.tobytes())

            self.logger.info(f"Processed image (quality={self.img_save_quality}, "
                             f"order={self.process_order}): {src} -> {dst}")

        else:
            # 非圖片或不需 resize，就直接複製
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy(src, dst)




    # ----------------- 多標籤候選樣本讀取：分別處理 Object Detection 與 Segmentation -----------------
    def load_multi_label_od_samples_from(self, dataset_root, dataset_origin="main"):
        return self._load_multi_label_by_task(dataset_root, dataset_origin, task="object_detect")

    def load_multi_label_segment_samples_from(self, dataset_root, dataset_origin="main"):
        return self._load_multi_label_by_task(dataset_root, dataset_origin, task="segment")

    def _load_multi_label_by_task(self, dataset_root, dataset_origin="main", task="segment"):
        dataset_root = Path(dataset_root)
        ml_folder = dataset_root / "multi_label_多類別缺陷"
        stats = {
            "total_label_files": 0,
            "empty_files": 0,
            "filtered_excluded": 0,
            "no_distribution": 0,
            "no_image": 0,
            "accepted": 0,
        }
        if not ml_folder.exists():
            self.logger.warning(f"[{dataset_origin} {task}] 未找到 multi_label_多類別缺陷 目錄 in {dataset_root}")
            return []
        if task == "segment":
            label_dir = ml_folder / "segment_label" / "yolo_format"
        elif task == "object_detect":
            label_dir = ml_folder / "object_detect_label" / "yolo_format"
        else:
            self.logger.error(f"未知的 task: {task}")
            return []
        label_files = list(label_dir.glob("*.txt"))
        self.logger.info(f"[{dataset_origin} {task}] 檢測到 {len(label_files)} 個標籤檔於 {label_dir}")
        candidates = []
        # 判斷使用哪一組排除類別（內部或外部）
        if dataset_origin == "external":
            ext_excluded_keys = self.external_excluded_class_keys
        else:
            ext_excluded_keys = self.excluded_class_keys
        for txt_path in label_files:
            stats["total_label_files"] += 1
            content = txt_path.read_text(encoding="utf-8").strip()
            if not content:
                stats["empty_files"] += 1
                continue
            lines = content.splitlines()
            distribution = Counter()
            has_excluded = False
            for line in lines:
                parts = line.strip().split()
                if not parts:
                    continue
                try:
                    class_id = int(parts[0])
                except Exception:
                    continue
                class_key = self.reverse_class_id_map.get(class_id)
                if not class_key:
                    continue
                if class_key in ext_excluded_keys:
                    # 若未啟用刪除功能，則直接標記此候選需過濾
                    if not self.remove_excluded_labels:
                        has_excluded = True
                        break
                    else:
                        # 啟用後，僅跳過該標籤，不計入 distribution
                        continue
                distribution[class_key] += 1
            if has_excluded:
                stats["filtered_excluded"] += 1
                continue
            if not distribution:
                stats["no_distribution"] += 1
                continue
            img_path = ml_folder / "original" / f"{txt_path.stem}.jpg"
            if not img_path.exists():
                img_path = ml_folder / "original" / f"{txt_path.stem}.png"
                if not img_path.exists():
                    stats["no_image"] += 1
                    continue
            candidate = {
                "dataset_origin": dataset_origin,
                "source": "multi_label",
                "distribution": distribution,
                "img_path": img_path,
                "stem": txt_path.stem,
                "segment_label_file": txt_path if task == "segment" else None,
                "object_label_file": txt_path if task == "object_detect" else None,
            }
            stats["accepted"] += 1
            candidates.append(candidate)
        self.logger.info(
            f"[{dataset_origin} {task} multi_label] 統計資訊：總標籤檔={stats['total_label_files']}, 空檔={stats['empty_files']}, "
            f"因排除過濾={stats['filtered_excluded']}, 無標籤分布={stats['no_distribution']}, "
            f"找不到圖片={stats['no_image']}, 最終接受={stats['accepted']}"
        )
        self.logger.info(f"讀取 {dataset_origin} {task} multi_label 內候選樣本：{len(candidates)} 筆")
        return candidates


    # ----------------- 單一標籤候選樣本讀取 -----------------
    def load_single_label_samples_from(self, dataset_root, dataset_origin="main", task="object_detect"):
        dataset_root = Path(dataset_root)
        candidates = []
        if dataset_origin == "external":
            ext_excluded_keys = self.external_excluded_class_keys
        else:
            ext_excluded_keys = self.excluded_class_keys
        for label_folder in dataset_root.iterdir():
            if not label_folder.is_dir() or label_folder.name.startswith("multi_label"):
                continue
            label_name = label_folder.name
            class_key = next((k for k in self.class_mapping if label_name.startswith(k + "_")), None)
            if not class_key or class_key in ext_excluded_keys:
                self.logger.debug(f"[SKIP] 排除或無法解析單類別：{label_name}")
                continue
            original_dir = label_folder / "original"
            if not original_dir.exists():
                self.logger.warning(f"[WARN] 缺少 original 子目錄：{label_name} in {dataset_origin}")
                continue
            for ext in self.image_exts:
                for img_path in original_dir.glob(f"*{ext}"):
                    stem = img_path.stem
                    if task == "object_detect":
                        label_subdir = "object_detect_label"
                    elif task == "segment":
                        label_subdir = "segment_label"
                    else:
                        self.logger.error(f"未知的 task: {task}")
                        continue
                    label_format_dir = label_folder / f"{label_subdir}" / "yolo_format"
                    label_file = label_format_dir / f"{stem}.txt"
                    if not label_file.exists():
                        continue
                    content = label_file.read_text(encoding="utf-8").strip()
                    if not content:
                        continue
                    lines = content.splitlines()
                    count_value = 0
                    for line in lines:
                        parts = line.strip().split()
                        if not parts:
                            continue
                        try:
                            c = int(parts[0])
                        except Exception:
                            continue
                        if self.reverse_class_id_map.get(c) == class_key:
                            count_value += 1
                    if count_value == 0:
                        continue
                    candidate = {
                        "dataset_origin": dataset_origin,
                        "source": "single_label",
                        "class_key": class_key,
                        "distribution": Counter({class_key: count_value}),
                        "img_path": img_path,
                        "stem": stem,
                        "label_folder": label_folder.name,
                        "object_label_file": label_file if task == "object_detect" else None,
                        "segment_label_file": label_file if task == "segment" else None
                    }
                    candidates.append(candidate)
        self.logger.info(f"讀取 {dataset_origin} 單一類別候選樣本 [{task}]：{len(candidates)} 筆")
        return candidates

    # 主資料集讀取方法
    def load_multi_label_samples(self, task="segment"):
        return self._load_multi_label_by_task(self.source_root, "main", task=task)

    def load_single_label_samples(self, task="object_detect"):
        return self.load_single_label_samples_from(self.source_root, "main", task=task)

    # ---------- 候選樣本選取（根據各類別設定抽取數量） ----------
    def select_candidates(self, ml_candidates, single_candidates, internal_limit_override=None, external_limit_override=None):
        if internal_limit_override is None:
            internal_limit = self.internal_samples_per_class or {k: self.max_samples_per_class 
                                                            for k in self.class_mapping if k not in self.excluded_class_keys}
        else:
            internal_limit = internal_limit_override
        if external_limit_override is None:
            external_limit = self.external_samples_per_class or {k: self.external_max_samples_per_class 
                                                            for k in self.class_mapping if k not in self.resolve_class_keys(self.external_excluded_classes_input)}
        else:
            external_limit = external_limit_override

        main_global_count = Counter({k: 0 for k in internal_limit})
        ext_global_count = Counter({k: 0 for k in external_limit})
        accepted_samples = []
        if self.use_multi_label_samples:
            ml_pool = ml_candidates.copy()
            while True:
                candidate_found = False
                best_candidate = None
                best_index = None
                best_score = None
                for i, candidate in enumerate(ml_pool):
                    distribution = candidate["distribution"]
                    if any(label in self.excluded_class_keys for label in distribution):
                        continue
                    is_acceptable = True
                    ratios = []
                    for label, count in distribution.items():
                        if candidate["dataset_origin"] == "external":
                            gap = external_limit.get(label, 0) - ext_global_count[label]
                        else:
                            gap = internal_limit.get(label, 0) - main_global_count[label]
                        if count > gap:
                            is_acceptable = False
                            break
                        else:
                            ratios.append(gap / count)
                    if not is_acceptable or not ratios:
                        continue
                    candidate_score = min(ratios)
                    if best_score is None or candidate_score < best_score:
                        best_score = candidate_score
                        best_candidate = candidate
                        best_index = i
                if best_candidate is not None:
                    accepted_samples.append(best_candidate)
                    for label, count in best_candidate["distribution"].items():
                        if best_candidate["dataset_origin"] == "external":
                            ext_global_count[label] += count
                        else:
                            main_global_count[label] += count
                    del ml_pool[best_index]
                    candidate_found = True
                if not candidate_found:
                    break
            self.logger.info("multi_label 候選選取完成")
        else:
            self.logger.info("未啟用 multi_label 策略")
        single_by_class_main = defaultdict(list)
        single_by_class_ext = defaultdict(list)
        for candidate in single_candidates:
            if candidate["dataset_origin"] == "external":
                single_by_class_ext[candidate["class_key"]].append(candidate)
            else:
                single_by_class_main[candidate["class_key"]].append(candidate)
        for cls, clist in single_by_class_main.items():
            clist.sort(key=lambda x: x["distribution"][cls])
            for candidate in clist:
                if main_global_count[cls] >= internal_limit.get(cls, 0):
                    break
                count = candidate["distribution"][cls]
                if main_global_count[cls] + count <= internal_limit.get(cls, 0):
                    accepted_samples.append(candidate)
                    main_global_count[cls] += count
        for cls, clist in single_by_class_ext.items():
            clist.sort(key=lambda x: x["distribution"][cls])
            for candidate in clist:
                if ext_global_count[cls] >= external_limit.get(cls, 0):
                    break
                count = candidate["distribution"][cls]
                if ext_global_count[cls] + count <= external_limit.get(cls, 0):
                    accepted_samples.append(candidate)
                    ext_global_count[cls] += count
        self.logger.info("單一標籤候選補充完成")
        self.validate_accepted_samples(accepted_samples)
        combined_global_count = {k: main_global_count[k] + ext_global_count[k]
                                 for k in self.class_mapping if k not in self.excluded_class_keys}
        return accepted_samples, combined_global_count, main_global_count, ext_global_count

    def validate_accepted_samples(self, samples):
        for candidate in samples:
            if candidate["source"] == "multi_label":
                for label in candidate["distribution"]:
                    if label in self.excluded_class_keys:
                        self.logger.error(f"候選樣本 {candidate['stem']} 包含排除類別：{label}")
                        raise ValueError(f"候選樣本 {candidate['stem']} 包含排除類別：{label}")
            else:
                cls = candidate["class_key"]
                if cls in self.excluded_class_keys:
                    self.logger.error(f"候選樣本 {candidate['stem']} 屬於排除單一類別：{cls}")
                    raise ValueError(f"候選樣本 {candidate['stem']} 屬於排除單一類別：{cls}")

    def validate_summary(self, summary):
        for label in summary:
            if label in self.excluded_class_keys:
                self.logger.error(f"統計中出現排除類別：{label}")
                raise ValueError(f"統計中出現排除類別：{label}")

    from collections import Counter, defaultdict

    def copy_labels_and_images(self, task_type, splits):
        self.logger.info(f"開始處理任務：{task_type}")
        images_base = self.target_root / task_type / "images"
        labels_base = self.target_root / f"{task_type}_task_labels"
    
        # 预先保证所有 split 目录存在
        for base in (images_base, labels_base):
            for split in ("train","val","test"):
                (base/split).mkdir(parents=True, exist_ok=True)
    
        summary = defaultdict(lambda: {"total":0,"train":0,"val":0,"test":0,"main":0,"external":0})
    
        for split_name, sample_list in splits.items():
            for candidate in sample_list:
                stem = candidate["stem"]
                src_img = candidate["img_path"]
                dst_img = images_base / split_name / f"{stem}{src_img.suffix}"
                self.copy_file(src_img, dst_img)
    
                # pick correct label field
                src_label = (candidate.get("object_label_file") 
                             if task_type=="object_detect" 
                             else candidate.get("segment_label_file"))
                if not src_label or not src_label.exists():
                    self.logger.warning(f"找不到標籤檔：{stem}")
                    dst_img.unlink(missing_ok=True)
                    continue
    
                dst_label = labels_base / split_name / f"{stem}.txt"
                raw = src_label.read_text(encoding="utf-8").splitlines()
    
                if self.remove_excluded_labels:
                    excl = (self.external_excluded_class_keys 
                            if candidate["dataset_origin"]=="external" 
                            else self.excluded_class_keys)
                    out_lines = [l for l in raw 
                                 if (parts:=l.split()) 
                                 and (key:=self.reverse_class_id_map.get(int(parts[0]))) 
                                 and key not in excl]
                    if not out_lines:
                        self.logger.info(f"[過濾後無剩餘標籤，跳過] {stem}")
                        dst_img.unlink(missing_ok=True)
                        continue
                    dst_label.write_text("\n".join(out_lines), encoding="utf-8")
                else:
                    self.copy_file(src_label, dst_label)
                    out_lines = raw
    
                # 重算 summary
                cnt = Counter(self.reverse_class_id_map[int(l.split()[0])] for l in out_lines)
                for label, c in cnt.items():
                    stats = summary[label]
                    stats["total"]     += c
                    stats[split_name]  += c
                    stats["external" if candidate["dataset_origin"]=="external" else "main"] += c
    
        self.validate_summary(summary)
        self.logger.info("統計結果驗證完畢，未發現排除類別。")
        self.logger.info(f"--- {task_type.upper()} 任務統計報告 ---")
        for label, stats in summary.items():
            cname = self.class_mapping[label]
            self.logger.info(
                f"[✓] {label:<20} ({cname}) → Total={stats['total']} "
                f"(main={stats['main']}, external={stats['external']}) | "
                f"Train={stats['train']} | Val={stats['val']} | Test={stats['test']}"
            )
        self.logger.info(f"任務 {task_type} 完成，資料輸出至：{self.target_root}")


    def verify_labels_consistency(self, task):
        self.logger.info(f"開始驗證 {task} 標籤與 images 的一致性")
        splits = ["train", "val", "test"]
        images_base = self.target_root / task / "images"
        labels_base = self.target_root / f"{task}_task_labels"
        for split in splits:
            image_files = {p.stem for p in (images_base / split).glob("*") if p.is_file()}
            label_files = {p.stem for p in (labels_base / split).glob("*") if p.is_file()}
            missing_labels = image_files - label_files
            extra_labels = label_files - image_files
            if missing_labels:
                self.logger.error(f"{task} 任務在 {split} 分割中缺少標籤檔：{missing_labels}")
            if extra_labels:
                self.logger.warning(f"{task} 任務在 {split} 分割中發現多餘標籤檔：{extra_labels}")
            self.logger.info(f"{task} 任務在 {split} 分割中：images={len(image_files)}，labels={len(label_files)}")

    def deduplicate_candidates(self, candidates):
        from collections import defaultdict
        grouped = defaultdict(list)
        for candidate in candidates:
            grouped[candidate["stem"]].append(candidate)
        deduped = []
        for stem, group in grouped.items():
            main_candidates = [cand for cand in group if cand["dataset_origin"] == "main"]
            if main_candidates:
                deduped.append(main_candidates[0])
            else:
                deduped.append(group[0])
        return deduped

    def report_common_images(self):
        # 統計 object_detect 與 segment 任務下，每個分割中圖片檔名完全相同的數量
        splits = ["train", "val", "test"]
        for split in splits:
            od_dir = self.target_root / "object_detect" / "images" / split
            seg_dir = self.target_root / "segment" / "images" / split
            od_files = {p.stem for p in od_dir.glob("*") if p.is_file()}
            seg_files = {p.stem for p in seg_dir.glob("*") if p.is_file()}
            common = od_files & seg_files
            self.logger.info(f"在 {split} 分割中，object_detect 與 segment 共有 {len(common)} 張相同的圖像")
    
    def save_config(self):
        """
        将当前参数配置存储到 target_root 下的 config.json 文件中，
        包含所有影响数据分割和过滤的关键参数。
        """
        config = {
            # 基本路径
            "source_root": str(self.source_root),
            "target_root": str(self.target_root),
            "external_root": str(self.external_root) if self.external_root else None,
    
            # 开关
            "use_external_dataset": self.use_external_dataset,
            "external_train_only": self.external_train_only,
            "simulate": self.simulate,
            "use_multi_label_samples": self.use_multi_label_samples,
            "remove_excluded_labels": self.remove_excluded_labels,
    
            # 排除类别
            "excluded_classes_input": list(self.excluded_classes_input),
            "excluded_class_keys": list(self.excluded_class_keys),
            "external_excluded_classes_input": list(self.external_excluded_classes_input),
            "external_excluded_class_keys": list(self.external_excluded_class_keys),
    
            # 抽样数设置
            "min_samples_per_class": self.min_samples_per_class,
            "max_samples_per_class": self.max_samples_per_class,
            "external_min_samples_per_class": self.external_min_samples_per_class,
            "external_max_samples_per_class": self.external_max_samples_per_class,
            "internal_samples_per_class_od": self.internal_samples_per_class_od,
            "internal_samples_per_class_seg": self.internal_samples_per_class_seg,
            "external_samples_per_class_od": self.external_samples_per_class_od,
            "external_samples_per_class_seg": self.external_samples_per_class_seg,
    
            # 随机种子
            "random_seed": self.random_seed,
            "external_random_seed": self.external_random_seed,
    
            # 分割比例
            "train_ratio": self.train_ratio,
            "val_ratio": self.val_ratio,
            "test_ratio": self.test_ratio,
    
            # 图像处理参数
            "process_order": self.process_order,
            "resize_images": self.resize_images,
            "resize_size": list(self.resize_size) if self.resize_size is not None else None,
            "img_save_quality": self.img_save_quality,
            "apply_sharpen": self.apply_sharpen,
            "sharpen_amount": self.sharpen_amount,
            "apply_cutout": self.apply_cutout,
            "cutout_image_prob": self.cutout_image_prob,
            "cutout_grid_size": list(self.cutout_grid_size),
            "cutout_cell_mask_ratio": self.cutout_cell_mask_ratio,
        }
    
        config_path = self.target_root / "config.json"
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        self.logger.info(f"已保存参数配置到: {config_path}")



    def run(self):
        # ----- 處理 Object Detection 任務 -----
        self.logger.info("開始處理 object_detect 任務")
        ml_candidates_main_od = self.load_multi_label_od_samples_from(self.source_root, "main")
        single_candidates_main_od = self.load_single_label_samples_from(self.source_root, "main", task="object_detect")
        ml_candidates_od = ml_candidates_main_od.copy()
        single_candidates_od = single_candidates_main_od.copy()
        if self.use_external_dataset and self.external_root:
            ml_candidates_ext_od = self.load_multi_label_od_samples_from(self.external_root, "external")
            single_candidates_ext_od = self.load_single_label_samples_from(self.external_root, "external", task="object_detect")
            ml_candidates_od.extend(ml_candidates_ext_od)
            single_candidates_od.extend(single_candidates_ext_od)
        # 使用分任務上限設定 (如果有提供則使用)
        accepted_od, combined_count_od, main_count_od, ext_count_od = self.select_candidates(
            ml_candidates_od, single_candidates_od,
            internal_limit_override=self.internal_samples_per_class_od or self.internal_samples_per_class,
            external_limit_override=self.external_samples_per_class_od or self.external_samples_per_class
        )
        accepted_od = self.deduplicate_candidates(accepted_od)
        self.logger.info("object_detect 各類別統計：")
        for label in sorted(combined_count_od):
            cname = self.class_mapping.get(label, label)
            self.logger.info(f"  {label} ({cname})：main={main_count_od[label]}, external={ext_count_od[label]}, total={combined_count_od[label]} "
                             f"(limit: main={self.max_samples_per_class}, external={self.external_max_samples_per_class})")
        self.logger.info(f"object_detect 任務總選取 {len(accepted_od)} 筆樣本")
        random.shuffle(accepted_od)
        if self.external_train_only:
            main_samples_od = [s for s in accepted_od if s["dataset_origin"] != "external"]
            ext_samples_od = [s for s in accepted_od if s["dataset_origin"] == "external"]
            splits_od = self.smart_ratio_split(main_samples_od)
            splits_od["train"].extend(ext_samples_od)
            self.logger.info("object_detect 外部資料已全部分配至 train")
        else:
            splits_od = self.smart_ratio_split(accepted_od)
        ext_train_od = len([s for s in splits_od["train"] if s["dataset_origin"] == "external"])
        ext_val_od = len([s for s in splits_od["val"] if s["dataset_origin"] == "external"])
        ext_test_od = len([s for s in splits_od["test"] if s["dataset_origin"] == "external"])
        self.logger.info(f"object_detect 外部資料分布：train={ext_train_od}, val={ext_val_od}, test={ext_test_od}")
        self.ensure_dirs("object_detect")
        self.copy_labels_and_images("object_detect", splits_od)

        # ----- 處理 Segmentation 任務 -----
        self.logger.info("開始處理 segment 任務")
        ml_candidates_main_seg = self.load_multi_label_segment_samples_from(self.source_root, "main")
        single_candidates_main_seg = self.load_single_label_samples_from(self.source_root, "main", task="segment")
        ml_candidates_seg = ml_candidates_main_seg.copy()
        single_candidates_seg = single_candidates_main_seg.copy()
        if self.use_external_dataset and self.external_root:
            ml_candidates_ext_seg = self.load_multi_label_segment_samples_from(self.external_root, "external")
            single_candidates_ext_seg = self.load_single_label_samples_from(self.external_root, "external", task="segment")
            ml_candidates_seg.extend(ml_candidates_ext_seg)
            single_candidates_seg.extend(single_candidates_ext_seg)
        accepted_seg, combined_count_seg, main_count_seg, ext_count_seg = self.select_candidates(
            ml_candidates_seg, single_candidates_seg,
            internal_limit_override=self.internal_samples_per_class_seg or self.internal_samples_per_class,
            external_limit_override=self.external_samples_per_class_seg or self.external_samples_per_class
        )
        accepted_seg = self.deduplicate_candidates(accepted_seg)
        self.logger.info("segment 各類別統計：")
        for label in sorted(combined_count_seg):
            cname = self.class_mapping.get(label, label)
            self.logger.info(f"  {label} ({cname})：main={main_count_seg[label]}, external={ext_count_seg[label]}, total={combined_count_seg[label]} "
                             f"(limit: main={self.max_samples_per_class}, external={self.external_max_samples_per_class})")
        self.logger.info(f"segment 任務總選取 {len(accepted_seg)} 筆樣本")
        random.shuffle(accepted_seg)
        if self.external_train_only:
            main_samples_seg = [s for s in accepted_seg if s["dataset_origin"] != "external"]
            ext_samples_seg = [s for s in accepted_seg if s["dataset_origin"] == "external"]
            splits_seg = self.smart_ratio_split(main_samples_seg)
            splits_seg["train"].extend(ext_samples_seg)
            self.logger.info("segment 外部資料已全部分配至 train")
        else:
            splits_seg = self.smart_ratio_split(accepted_seg)
        ext_train_seg = len([s for s in splits_seg["train"] if s["dataset_origin"] == "external"])
        ext_val_seg = len([s for s in splits_seg["val"] if s["dataset_origin"] == "external"])
        ext_test_seg = len([s for s in splits_seg["test"] if s["dataset_origin"] == "external"])
        self.logger.info(f"segment 外部資料分布：train={ext_train_seg}, val={ext_val_seg}, test={ext_test_seg}")
        self.ensure_dirs("segment")
        self.copy_labels_and_images("segment", splits_seg)

        # 統計兩個任務下，每個分割中相同影像的數量
        self.report_common_images()

        # 最終驗證：檢查任務專屬 images 與標籤檔是否一致
        self.verify_labels_consistency("object_detect")
        self.verify_labels_consistency("segment")

        mode_str = "(模擬模式)" if self.simulate else ""
        self.logger.info(f"所有任務完成 {mode_str}，資料儲存至：{self.target_root}")
        # 保存參數以便追蹤
        self.save_config()

if __name__ == '__main__':
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_target = Path(r"D:\路面缺陷資料集\全部資料集\yolo_訓練資料集")
    target_root = base_target / f"road_yolo_dataset_各類all筆_内_Q75_R_1200X900_{timestamp}"
    target_root.mkdir(parents=True, exist_ok=True)
    log_path = target_root / "dataset_splitter.log"
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s %(levelname)s: %(message)s",
        filename=str(log_path),
        filemode="w",
    )
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s %(levelname)s: %(message)s")
    console_handler.setFormatter(formatter)
    logging.getLogger('').addHandler(console_handler)
    
    print(f"Log 保存位置：{log_path.resolve()}")
    splitter = YoloDatasetSplitter(
        source_root=r"D:\路面缺陷資料集\全部資料集\資料集_已處理\資料集_已處理_多類別_完整V3_公司1&2",
        target_root=str(target_root),
        external_root=r"D:\路面缺陷資料集\全部資料集\資料集_已處理\資料集_已處理_多類別_完整V4_Others",
        use_external_dataset=True,
        simulate=False,
        excluded_classes_input={"deformation", "manhole_ditch", "dirt_spot", "other"},
        min_samples_per_class=0,
        max_samples_per_class=450000,
        random_seed=42,
        use_multi_label_samples=True,
        external_excluded_classes_input={"deformation", "manhole_ditch", "dirt_spot", "other"},
        remove_excluded_labels=True,  
        external_min_samples_per_class=0,
        external_max_samples_per_class=450000,
        external_random_seed=42,
        # 此處提供各任務獨立設定 (若為 None 則使用共用設定)
        internal_samples_per_class_od={
            "joint": 5000, "other": 0, "linear_crack": 5000,
            "Alligator_crack": 5000, "potholes": 5000, "patch": 5000,
            "expansion_joint": 5000, "manhole_ditch": 0, "deformation": 0,
            "dirt_spot": 0
        },
        internal_samples_per_class_seg={
            "joint": 5000, "other": 0, "linear_crack": 5000,
            "Alligator_crack": 5000, "potholes": 5000, "patch": 5000,
            "expansion_joint": 5000, "manhole_ditch": 0, "deformation": 0,
            "dirt_spot": 0
        },
        external_samples_per_class_od={
            "joint": 5000, "other": 0, "linear_crack": 5000,
            "Alligator_crack": 5000, "potholes": 5000, "patch": 5000,
            "expansion_joint": 5000, "manhole_ditch": 0, "deformation": 0,
            "dirt_spot": 0
        },
        external_samples_per_class_seg={
            "joint": 5000, "other": 0, "linear_crack": 5000,
            "Alligator_crack": 5000, "potholes": 5000, "patch": 5000,
            "expansion_joint": 5000, "manhole_ditch": 0, "deformation": 0,
            "dirt_spot": 0
        },
        external_train_only=True,
        # 若要啟用圖像調整功能，請設定 resize_images 為 True，
        # 並依需求調整 resize_size 及 img_save_quality
        resize_images=True,
        resize_size=(1200, 900),
        img_save_quality=75,
        # "quality_then_resize"：先降低品質再調整尺寸（預設）
        # "resize_then_quality"：先調整尺寸再降低品質
        #"resize_only"只做解析度
        process_order="quality_then_resize",
        apply_sharpen=False,      # 啟用銳化
        sharpen_amount=0.50,       # 銳化強度（依需求調整）
        apply_cutout=False,
        cutout_image_prob=1.0,     # 例如 100% 的圖像都執行 cutout
        cutout_grid_size=(25, 25),   # 每個格子 50x50 像素
        cutout_cell_mask_ratio=0.1   # 約 30% 的格子被遮罩
    )
    splitter.run()

    '''
    internal_samples_per_class_od={
        "joint": 5000, "other": 0, "linear_crack": 5000,
        "Alligator_crack": 5000, "potholes": 2400, "patch": 5000,
        "expansion_joint": 5000, "manhole_ditch": 0, "deformation": 0,
        "dirt_spot": 0
    },
    internal_samples_per_class_seg={
        "joint": 3600, "other": 50, "linear_crack": 3600,
        "Alligator_crack": 3600, "potholes": 2400, "patch": 3600,
        "expansion_joint": 3600, "manhole_ditch": 0, "deformation": 0,
        "dirt_spot": 0
    },
    external_samples_per_class_od={
        "joint": 0, "other": 0, "linear_crack": 0,
        "Alligator_crack": 0, "potholes": 1300, "patch": 0,
        "expansion_joint": 0, "manhole_ditch": 0, "deformation": 0,
        "dirt_spot": 0
    },
    external_samples_per_class_seg={
        "joint": 0, "other": 0, "linear_crack": 0,
        "Alligator_crack": 0, "potholes": 1300, "patch": 0,
        "expansion_joint": 0, "manhole_ditch": 0, "deformation": 0,
        "dirt_spot": 0
    },
    '''
