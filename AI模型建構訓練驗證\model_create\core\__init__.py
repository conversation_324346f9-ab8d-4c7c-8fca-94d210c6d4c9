"""
核心模組：提供統一的模型架構基礎
包含基礎類別、註冊系統和工廠模式
"""

from .base_encoder import BaseEncoder
from .base_decoder import BaseDecoder
from .registry import ENCODER_REGISTRY, DECODER_REGISTRY, MODEL_REGISTRY
from .model_factory import ModelFactory
from .config_manager import ConfigManager

__all__ = [
    'BaseEncoder',
    'BaseDecoder', 
    'ENCODER_REGISTRY',
    'DECODER_REGISTRY',
    'MODEL_REGISTRY',
    'ModelFactory',
    'ConfigManager'
]